// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronHarmonyEngineBridge.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_AuracronHarmonyEngineBridge_generated_h
#error "AuracronHarmonyEngineBridge.generated.h already included, missing '#pragma once' in AuracronHarmonyEngineBridge.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_AuracronHarmonyEngineBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FHarmonyInterventionData;
struct FKindnessReward;
struct FPlayerBehaviorSnapshot;

// ********** Begin ScriptStruct FPlayerBehaviorSnapshot *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerBehaviorSnapshot;
// ********** End ScriptStruct FPlayerBehaviorSnapshot *********************************************

// ********** Begin ScriptStruct FHarmonyInterventionData ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_165_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHarmonyInterventionData;
// ********** End ScriptStruct FHarmonyInterventionData ********************************************

// ********** Begin ScriptStruct FKindnessReward ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_198_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FKindnessReward_Statics; \
	static class UScriptStruct* StaticStruct();


struct FKindnessReward;
// ********** End ScriptStruct FKindnessReward *****************************************************

// ********** Begin Delegate FOnBehaviorDetected ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_227_DELEGATE \
AURACRONHARMONYENGINEBRIDGE_API void FOnBehaviorDetected_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorDetected, const FString& PlayerID, FPlayerBehaviorSnapshot const& BehaviorData);


// ********** End Delegate FOnBehaviorDetected *****************************************************

// ********** Begin Delegate FOnInterventionTriggered **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_228_DELEGATE \
AURACRONHARMONYENGINEBRIDGE_API void FOnInterventionTriggered_DelegateWrapper(const FMulticastScriptDelegate& OnInterventionTriggered, const FString& PlayerID, FHarmonyInterventionData const& InterventionData);


// ********** End Delegate FOnInterventionTriggered ************************************************

// ********** Begin Delegate FOnCommunityHealing ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_229_DELEGATE \
AURACRONHARMONYENGINEBRIDGE_API void FOnCommunityHealing_DelegateWrapper(const FMulticastScriptDelegate& OnCommunityHealing, const FString& HealerID, const FString& VictimID);


// ********** End Delegate FOnCommunityHealing *****************************************************

// ********** Begin Delegate FOnKindnessReward *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_230_DELEGATE \
AURACRONHARMONYENGINEBRIDGE_API void FOnKindnessReward_DelegateWrapper(const FMulticastScriptDelegate& OnKindnessReward, const FString& PlayerID, FKindnessReward const& Reward);


// ********** End Delegate FOnKindnessReward *******************************************************

// ********** Begin Delegate FOnHarmonyLevelChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h_231_DELEGATE \
AURACRONHARMONYENGINEBRIDGE_API void FOnHarmonyLevelChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHarmonyLevelChanged, int32 NewHarmonyLevel);


// ********** End Delegate FOnHarmonyLevelChanged **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h

// ********** Begin Enum EHarmonyBehaviorType ******************************************************
#define FOREACH_ENUM_EHARMONYBEHAVIORTYPE(op) \
	op(EHarmonyBehaviorType::Positive) \
	op(EHarmonyBehaviorType::Neutral) \
	op(EHarmonyBehaviorType::Warning) \
	op(EHarmonyBehaviorType::Toxic) \
	op(EHarmonyBehaviorType::Healing) 

enum class EHarmonyBehaviorType : uint8;
template<> struct TIsUEnumClass<EHarmonyBehaviorType> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHarmonyBehaviorType>();
// ********** End Enum EHarmonyBehaviorType ********************************************************

// ********** Begin Enum EEmotionalState ***********************************************************
#define FOREACH_ENUM_EEMOTIONALSTATE(op) \
	op(EEmotionalState::Happy) \
	op(EEmotionalState::Excited) \
	op(EEmotionalState::Calm) \
	op(EEmotionalState::Neutral) \
	op(EEmotionalState::Frustrated) \
	op(EEmotionalState::Angry) \
	op(EEmotionalState::Sad) \
	op(EEmotionalState::Anxious) 

enum class EEmotionalState : uint8;
template<> struct TIsUEnumClass<EEmotionalState> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EEmotionalState>();
// ********** End Enum EEmotionalState *************************************************************

// ********** Begin Enum EInterventionType *********************************************************
#define FOREACH_ENUM_EINTERVENTIONTYPE(op) \
	op(EInterventionType::None) \
	op(EInterventionType::Gentle) \
	op(EInterventionType::Moderate) \
	op(EInterventionType::Strong) \
	op(EInterventionType::Emergency) 

enum class EInterventionType : uint8;
template<> struct TIsUEnumClass<EInterventionType> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionType>();
// ********** End Enum EInterventionType ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
