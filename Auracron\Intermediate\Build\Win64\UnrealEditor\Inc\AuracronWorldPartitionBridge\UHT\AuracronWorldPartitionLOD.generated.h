// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionLOD.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLOD_generated_h
#error "AuracronWorldPartitionLOD.generated.h already included, missing '#pragma once' in AuracronWorldPartitionLOD.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionLOD_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionLODManager;
class UWorld;
enum class EAuracronLODGenerationState : uint8;
enum class EAuracronLODType : uint8;
struct FAuracronHLODGenerationParameters;
struct FAuracronLODConfiguration;
struct FAuracronLODDescriptor;
struct FAuracronLODStatistics;
struct FAuracronWorldPartitionLODConfiguration;

// ********** Begin ScriptStruct FAuracronLODConfiguration *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_102_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLODConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLODConfiguration;
// ********** End ScriptStruct FAuracronLODConfiguration *******************************************

// ********** Begin ScriptStruct FAuracronWorldPartitionLODConfiguration ***************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_188_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWorldPartitionLODConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWorldPartitionLODConfiguration;
// ********** End ScriptStruct FAuracronWorldPartitionLODConfiguration *****************************

// ********** Begin ScriptStruct FAuracronLODDescriptor ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_221_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLODDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLODDescriptor;
// ********** End ScriptStruct FAuracronLODDescriptor **********************************************

// ********** Begin ScriptStruct FAuracronHLODGenerationParameters *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_306_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronHLODGenerationParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronHLODGenerationParameters;
// ********** End ScriptStruct FAuracronHLODGenerationParameters ***********************************

// ********** Begin ScriptStruct FAuracronLODStatistics ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_359_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLODStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLODStatistics;
// ********** End ScriptStruct FAuracronLODStatistics **********************************************

// ********** Begin Delegate FOnLODGenerated *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_559_DELEGATE \
static void FOnLODGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnLODGenerated, const FString& LODId, EAuracronLODType LODType);


// ********** End Delegate FOnLODGenerated *********************************************************

// ********** Begin Delegate FOnLODRemoved *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_560_DELEGATE \
static void FOnLODRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnLODRemoved, const FString& LODId);


// ********** End Delegate FOnLODRemoved ***********************************************************

// ********** Begin Delegate FOnLODTransition ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_561_DELEGATE \
static void FOnLODTransition_DelegateWrapper(const FMulticastScriptDelegate& OnLODTransition, const FString& ActorId, int32 FromLOD, int32 ToLOD);


// ********** End Delegate FOnLODTransition ********************************************************

// ********** Begin Delegate FOnHLODGenerationCompleted ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_562_DELEGATE \
static void FOnHLODGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnHLODGenerationCompleted, const FString& LODId, bool bSuccess);


// ********** End Delegate FOnHLODGenerationCompleted **********************************************

// ********** Begin Class UAuracronWorldPartitionLODManager ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugLODInfo); \
	DECLARE_FUNCTION(execLogLODState); \
	DECLARE_FUNCTION(execIsLODDebugEnabled); \
	DECLARE_FUNCTION(execEnableLODDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetGeneratedLODCount); \
	DECLARE_FUNCTION(execGetTotalLODCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetLODStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetActiveLODs); \
	DECLARE_FUNCTION(execGetLODsByType); \
	DECLARE_FUNCTION(execGetLODsInCell); \
	DECLARE_FUNCTION(execGetLODsForActor); \
	DECLARE_FUNCTION(execGenerateImpostorLOD); \
	DECLARE_FUNCTION(execSimplifyMeshToTriangleCount); \
	DECLARE_FUNCTION(execSimplifyMesh); \
	DECLARE_FUNCTION(execGetLODDistanceForLevel); \
	DECLARE_FUNCTION(execCalculateLODLevelForDistance); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLODs); \
	DECLARE_FUNCTION(execUpdateLODTransitions); \
	DECLARE_FUNCTION(execTransitionToLOD); \
	DECLARE_FUNCTION(execGetCurrentLODLevel); \
	DECLARE_FUNCTION(execSetLODLevel); \
	DECLARE_FUNCTION(execGetHLODGenerationState); \
	DECLARE_FUNCTION(execGenerateHLODForActors); \
	DECLARE_FUNCTION(execGenerateHLODForCell); \
	DECLARE_FUNCTION(execGenerateHLOD); \
	DECLARE_FUNCTION(execDoesLODExist); \
	DECLARE_FUNCTION(execGetLODIds); \
	DECLARE_FUNCTION(execGetAllLODs); \
	DECLARE_FUNCTION(execGetLODDescriptor); \
	DECLARE_FUNCTION(execRemoveLOD); \
	DECLARE_FUNCTION(execCreateLOD); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionLODManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionLODManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionLODManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionLODManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionLODManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionLODManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionLODManager(UAuracronWorldPartitionLODManager&&) = delete; \
	UAuracronWorldPartitionLODManager(const UAuracronWorldPartitionLODManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionLODManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionLODManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionLODManager) \
	NO_API virtual ~UAuracronWorldPartitionLODManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_421_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h_424_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionLODManager;

// ********** End Class UAuracronWorldPartitionLODManager ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionLOD_h

// ********** Begin Enum EAuracronLODGenerationState ***********************************************
#define FOREACH_ENUM_EAURACRONLODGENERATIONSTATE(op) \
	op(EAuracronLODGenerationState::NotGenerated) \
	op(EAuracronLODGenerationState::Generating) \
	op(EAuracronLODGenerationState::Generated) \
	op(EAuracronLODGenerationState::Failed) \
	op(EAuracronLODGenerationState::Outdated) 

enum class EAuracronLODGenerationState : uint8;
template<> struct TIsUEnumClass<EAuracronLODGenerationState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODGenerationState>();
// ********** End Enum EAuracronLODGenerationState *************************************************

// ********** Begin Enum EAuracronLODType **********************************************************
#define FOREACH_ENUM_EAURACRONLODTYPE(op) \
	op(EAuracronLODType::StaticMesh) \
	op(EAuracronLODType::HLOD) \
	op(EAuracronLODType::Nanite) \
	op(EAuracronLODType::Impostor) \
	op(EAuracronLODType::Billboard) 

enum class EAuracronLODType : uint8;
template<> struct TIsUEnumClass<EAuracronLODType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODType>();
// ********** End Enum EAuracronLODType ************************************************************

// ********** Begin Enum EAuracronLODQuality *******************************************************
#define FOREACH_ENUM_EAURACRONLODQUALITY(op) \
	op(EAuracronLODQuality::Lowest) \
	op(EAuracronLODQuality::Low) \
	op(EAuracronLODQuality::Medium) \
	op(EAuracronLODQuality::High) \
	op(EAuracronLODQuality::Highest) \
	op(EAuracronLODQuality::Lossless) 

enum class EAuracronLODQuality : uint8;
template<> struct TIsUEnumClass<EAuracronLODQuality> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronLODQuality>();
// ********** End Enum EAuracronLODQuality *********************************************************

// ********** Begin Enum EAuracronWorldPartitionLODTransitionType **********************************
#define FOREACH_ENUM_EAURACRONWORLDPARTITIONLODTRANSITIONTYPE(op) \
	op(EAuracronWorldPartitionLODTransitionType::Instant) \
	op(EAuracronWorldPartitionLODTransitionType::Fade) \
	op(EAuracronWorldPartitionLODTransitionType::Dither) \
	op(EAuracronWorldPartitionLODTransitionType::Smooth) \
	op(EAuracronWorldPartitionLODTransitionType::Custom) 

enum class EAuracronWorldPartitionLODTransitionType : uint8;
template<> struct TIsUEnumClass<EAuracronWorldPartitionLODTransitionType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionLODTransitionType>();
// ********** End Enum EAuracronWorldPartitionLODTransitionType ************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
