/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/GeometryCore.natvis"
/NATVIS:"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/PCG.natvis"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/AuracronEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.NonOptimized.RTTI.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.1.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.2.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.3.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.4.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.5.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.6.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.7.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.8.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Module.AuracronFoliageBridge.gen.9.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliage.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageBiome.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageBridge.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageCollision.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageInstanced.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageInteraction.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageLOD.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageMaterialVariation.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliagePerformanceOptimization.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageProcedural.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageSeasonal.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageStreaming.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/AuracronFoliageWind.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/PCGBiomeCache.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/PerModuleInline.gen.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronFoliageBridge/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EngineSettings/UnrealEditor-EngineSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTasks/UnrealEditor-GameplayTasks.lib"
"../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayAbilities/UnrealEditor-GameplayAbilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NavigationSystem/UnrealEditor-NavigationSystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshUtilitiesCommon/UnrealEditor-MeshUtilitiesCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RawMesh/UnrealEditor-RawMesh.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SkeletalMeshUtilitiesCommon/UnrealEditor-SkeletalMeshUtilitiesCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothSysRuntimeIntrfc/UnrealEditor-ClothingSystemRuntimeInterface.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothingSystemRuntimeCommon/UnrealEditor-ClothingSystemRuntimeCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCore/UnrealEditor-ChaosCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AssetTools/UnrealEditor-AssetTools.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ComponentVisualizers/UnrealEditor-ComponentVisualizers.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ContentBrowser/UnrealEditor-ContentBrowser.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DetailCustomizations/UnrealEditor-DetailCustomizations.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorStyle/UnrealEditor-EditorStyle.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorSubsystem/UnrealEditor-EditorSubsystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorWidgets/UnrealEditor-EditorWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LevelEditor/UnrealEditor-LevelEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshBuilder/UnrealEditor-MeshBuilder.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PropertyEditor/UnrealEditor-PropertyEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SourceControl/UnrealEditor-SourceControl.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LandscapeEditor/UnrealEditor-LandscapeEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorInteractiveToolsFramework/UnrealEditor-EditorInteractiveToolsFramework.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MaterialUtilities/UnrealEditor-MaterialUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MainFrame/UnrealEditor-MainFrame.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SourceControlWindows/UnrealEditor-SourceControlWindows.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ContentBrowserData/UnrealEditor-ContentBrowserData.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Foliage/UnrealEditor-Foliage.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Landscape/UnrealEditor-Landscape.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/StaticMeshDescription/UnrealEditor-StaticMeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshDescription/UnrealEditor-MeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/UnrealEditor-GeometryCore.lib"
"../Plugins/Runtime/GeometryProcessing/Intermediate/Build/Win64/x64/UnrealEditor/Development/DynamicMesh/UnrealEditor-DynamicMesh.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFramework/UnrealEditor-GeometryFramework.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InteractiveToolsFramework/UnrealEditor-InteractiveToolsFramework.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingComponents/UnrealEditor-ModelingComponents.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingOperators/UnrealEditor-ModelingOperators.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshConversion/UnrealEditor-MeshConversion.lib"
"../Plugins/PCG/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCG/UnrealEditor-PCG.lib"
"../Plugins/PCGInterops/PCGGeometryScriptInterop/Intermediate/Build/Win64/x64/UnrealEditor/Development/PCGGeometryScriptInterop/UnrealEditor-PCGGeometryScriptInterop.lib"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronPCGBridge/UnrealEditor-AuracronPCGBridge.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AudioMixer/UnrealEditor-AudioMixer.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageWrapper/UnrealEditor-ImageWrapper.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageCore/UnrealEditor-ImageCore.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"legacy_stdio_definitions.lib"
/OUT:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronFoliageBridge.dll"
/PDB:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronFoliageBridge.pdb"
/ignore:4078