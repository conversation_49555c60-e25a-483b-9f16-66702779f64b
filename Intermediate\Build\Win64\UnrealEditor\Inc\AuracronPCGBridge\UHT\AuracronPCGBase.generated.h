// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGBase.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGBase_generated_h
#error "AuracronPCGBase.generated.h already included, missing '#pragma once' in AuracronPCGBase.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGBase_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();

#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSettingsBase(); \
	friend struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSettingsBase, UPCGSettings, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSettingsBase)


#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSettingsBase(UAuracronPCGSettingsBase&&) = delete; \
	UAuracronPCGSettingsBase(const UAuracronPCGSettingsBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSettingsBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSettingsBase); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGSettingsBase) \
	NO_API virtual ~UAuracronPCGSettingsBase();


#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_26_PROLOG
#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSettingsBase;

// ********** End Class UAuracronPCGSettingsBase ***************************************************

// ********** Begin Class UAuracronPCGExampleSettings **********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleSettings_NoRegister();

#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_73_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGExampleSettings(); \
	friend struct Z_Construct_UClass_UAuracronPCGExampleSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGExampleSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGExampleSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGExampleSettings)


#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_73_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGExampleSettings(UAuracronPCGExampleSettings&&) = delete; \
	UAuracronPCGExampleSettings(const UAuracronPCGExampleSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGExampleSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGExampleSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGExampleSettings) \
	NO_API virtual ~UAuracronPCGExampleSettings();


#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_70_PROLOG
#define FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_73_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_73_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_73_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGExampleSettings;

// ********** End Class UAuracronPCGExampleSettings ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
