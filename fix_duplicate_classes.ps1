# Script para corrigir classes duplicadas criadas pela expansao da macro
# Remove as definicoes duplicadas de UCLASS que foram criadas incorretamente

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao de classes duplicadas..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Encontra todos os arquivos .h
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalFixes = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileFixes = 0
    
    # Procura por padroes de classes duplicadas
    # Padrao: UCLASS dentro de uma classe (identacao indica que esta dentro de outra classe)
    $lines = $content -split "`n"
    $newLines = @()
    $insideClass = $false
    $braceCount = 0
    $skipUntilCloseBrace = $false
    
    for ($i = 0; $i -lt $lines.Length; $i++) {
        $line = $lines[$i]
        
        # Conta chaves para rastrear escopo
        $openBraces = ($line -split '\{').Length - 1
        $closeBraces = ($line -split '\}').Length - 1
        $braceCount += $openBraces - $closeBraces
        
        # Detecta se estamos dentro de uma classe
        if ($line -match '^\s*class\s+\w+.*\{?\s*$') {
            $insideClass = $true
        }
        
        # Se encontramos UCLASS dentro de uma classe (com identacao), pula
        if ($insideClass -and $braceCount -gt 0 -and $line -match '^\s+UCLASS\s*\(') {
            Write-Host "    Removendo UCLASS duplicada dentro de classe" -ForegroundColor Yellow
            $skipUntilCloseBrace = $true
            $fileFixes++
            continue
        }
        
        # Se estamos pulando e encontramos uma declaracao de classe duplicada, pula tambem
        if ($skipUntilCloseBrace -and $line -match '^\s*class\s+AURACRONPCGBRIDGE_API\s+\w+.*:\s*public\s+\w+') {
            Write-Host "    Removendo declaracao de classe duplicada" -ForegroundColor Yellow
            continue
        }
        
        # Se estamos pulando e encontramos GENERATED_BODY, pula tambem
        if ($skipUntilCloseBrace -and $line -match '^\s*GENERATED_BODY\s*\(\s*\)') {
            Write-Host "    Removendo GENERATED_BODY duplicado" -ForegroundColor Yellow
            continue
        }
        
        # Para de pular quando saimos do escopo da classe
        if ($skipUntilCloseBrace -and $braceCount -eq 0) {
            $skipUntilCloseBrace = $false
            $insideClass = $false
        }
        
        # Adiciona a linha se nao estamos pulando
        if (-not $skipUntilCloseBrace) {
            $newLines += $line
        }
    }
    
    # Reconstroi o conteudo
    if ($fileFixes -gt 0) {
        $content = $newLines -join "`n"
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalFixes += $fileFixes
        $filesModified++
        Write-Host "  OK $fileFixes duplicacoes corrigidas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma duplicacao encontrada" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de duplicacoes corrigidas: $totalFixes" -ForegroundColor Green

if ($totalFixes -gt 0) {
    Write-Host "`nCorrecao de duplicacoes concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma duplicacao encontrada." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
