// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronFoliageStreaming.h"

#ifdef AURACRONFOLIAGEBRIDGE_AuracronFoliageStreaming_generated_h
#error "AuracronFoliageStreaming.generated.h already included, missing '#pragma once' in AuracronFoliageStreaming.h"
#endif
#define AURACRONFOLIAGEBRIDGE_AuracronFoliageStreaming_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronDataLayerManager;
class UAuracronFoliageStreamingManager;
class UAuracronWorldPartitionStreamingManager;
class UWorld;
enum class EAuracronFoliageLoadingPriority : uint8;
enum class EAuracronFoliageStreamingState : uint8;
struct FAuracronFoliageChunkData;
struct FAuracronFoliageMemoryPool;
struct FAuracronFoliageStreamingConfiguration;
struct FAuracronFoliageStreamingPerformanceData;

// ********** Begin ScriptStruct FAuracronFoliageStreamingConfiguration ****************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_113_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingConfiguration_Statics; \
	AURACRONFOLIAGEBRIDGE_API static class UScriptStruct* StaticStruct();


struct FAuracronFoliageStreamingConfiguration;
// ********** End ScriptStruct FAuracronFoliageStreamingConfiguration ******************************

// ********** Begin ScriptStruct FAuracronFoliageChunkData *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_251_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageChunkData_Statics; \
	AURACRONFOLIAGEBRIDGE_API static class UScriptStruct* StaticStruct();


struct FAuracronFoliageChunkData;
// ********** End ScriptStruct FAuracronFoliageChunkData *******************************************

// ********** Begin ScriptStruct FAuracronFoliageStreamingRequest **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_312_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingRequest_Statics; \
	AURACRONFOLIAGEBRIDGE_API static class UScriptStruct* StaticStruct();


struct FAuracronFoliageStreamingRequest;
// ********** End ScriptStruct FAuracronFoliageStreamingRequest ************************************

// ********** Begin ScriptStruct FAuracronFoliageMemoryPool ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_360_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageMemoryPool_Statics; \
	AURACRONFOLIAGEBRIDGE_API static class UScriptStruct* StaticStruct();


struct FAuracronFoliageMemoryPool;
// ********** End ScriptStruct FAuracronFoliageMemoryPool ******************************************

// ********** Begin ScriptStruct FAuracronFoliageStreamingPerformanceData **************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_405_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFoliageStreamingPerformanceData_Statics; \
	AURACRONFOLIAGEBRIDGE_API static class UScriptStruct* StaticStruct();


struct FAuracronFoliageStreamingPerformanceData;
// ********** End ScriptStruct FAuracronFoliageStreamingPerformanceData ****************************

// ********** Begin Delegate FOnFoliageChunkLoaded *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_615_DELEGATE \
static AURACRONFOLIAGEBRIDGE_API void FOnFoliageChunkLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageChunkLoaded, const FString& ChunkId, int32 InstanceCount);


// ********** End Delegate FOnFoliageChunkLoaded ***************************************************

// ********** Begin Delegate FOnFoliageChunkUnloaded ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_616_DELEGATE \
static AURACRONFOLIAGEBRIDGE_API void FOnFoliageChunkUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageChunkUnloaded, const FString& ChunkId);


// ********** End Delegate FOnFoliageChunkUnloaded *************************************************

// ********** Begin Delegate FOnFoliageStreamingStateChanged ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_617_DELEGATE \
static AURACRONFOLIAGEBRIDGE_API void FOnFoliageStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageStreamingStateChanged, const FString& ChunkId, EAuracronFoliageStreamingState NewState);


// ********** End Delegate FOnFoliageStreamingStateChanged *****************************************

// ********** Begin Delegate FOnFoliageMemoryPressure **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_618_DELEGATE \
static AURACRONFOLIAGEBRIDGE_API void FOnFoliageMemoryPressure_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageMemoryPressure, float MemoryUsagePercent);


// ********** End Delegate FOnFoliageMemoryPressure ************************************************

// ********** Begin Class UAuracronFoliageStreamingManager *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogStreamingStatistics); \
	DECLARE_FUNCTION(execDrawDebugStreamingInfo); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetFoliageMemoryUsage); \
	DECLARE_FUNCTION(execGetLoadedChunkCount); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceData); \
	DECLARE_FUNCTION(execGetFoliageChunksInDataLayer); \
	DECLARE_FUNCTION(execUpdateDataLayerBasedStreaming); \
	DECLARE_FUNCTION(execOnDataLayerStateChanged); \
	DECLARE_FUNCTION(execCleanupUnusedMemory); \
	DECLARE_FUNCTION(execIsMemoryPressureHigh); \
	DECLARE_FUNCTION(execUpdateMemoryManagement); \
	DECLARE_FUNCTION(execUpdateMemoryPool); \
	DECLARE_FUNCTION(execCreateMemoryPool); \
	DECLARE_FUNCTION(execInitializeMemoryPools); \
	DECLARE_FUNCTION(execGetActiveAsyncLoadingCount); \
	DECLARE_FUNCTION(execUpdateAsyncLoadingOperations); \
	DECLARE_FUNCTION(execIsAsyncFoliageLoadingEnabled); \
	DECLARE_FUNCTION(execEnableAsyncFoliageLoading); \
	DECLARE_FUNCTION(execUpdateDistanceBasedStreaming); \
	DECLARE_FUNCTION(execProcessStreamingRequests); \
	DECLARE_FUNCTION(execCancelFoliageStreamingRequest); \
	DECLARE_FUNCTION(execRequestFoliageChunkUnloading); \
	DECLARE_FUNCTION(execRequestFoliageChunkLoading); \
	DECLARE_FUNCTION(execGetChunksInRange); \
	DECLARE_FUNCTION(execGetAllFoliageChunks); \
	DECLARE_FUNCTION(execGetFoliageChunk); \
	DECLARE_FUNCTION(execRemoveFoliageChunk); \
	DECLARE_FUNCTION(execUpdateFoliageChunk); \
	DECLARE_FUNCTION(execCreateFoliageChunk); \
	DECLARE_FUNCTION(execUnregisterFoliageFromCell); \
	DECLARE_FUNCTION(execRegisterFoliageWithCell); \
	DECLARE_FUNCTION(execIntegrateWithDataLayers); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartition); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronFoliageStreamingManager(); \
	friend struct Z_Construct_UClass_UAuracronFoliageStreamingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronFoliageStreamingManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UAuracronFoliageStreamingManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronFoliageStreamingManager)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronFoliageStreamingManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronFoliageStreamingManager(UAuracronFoliageStreamingManager&&) = delete; \
	UAuracronFoliageStreamingManager(const UAuracronFoliageStreamingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronFoliageStreamingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronFoliageStreamingManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronFoliageStreamingManager) \
	NO_API virtual ~UAuracronFoliageStreamingManager();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_468_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h_471_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronFoliageStreamingManager;

// ********** End Class UAuracronFoliageStreamingManager *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageStreaming_h

// ********** Begin Enum EAuracronFoliageStreamingStrategy *****************************************
#define FOREACH_ENUM_EAURACRONFOLIAGESTREAMINGSTRATEGY(op) \
	op(EAuracronFoliageStreamingStrategy::None) \
	op(EAuracronFoliageStreamingStrategy::DistanceBased) \
	op(EAuracronFoliageStreamingStrategy::CellBased) \
	op(EAuracronFoliageStreamingStrategy::DataLayerBased) \
	op(EAuracronFoliageStreamingStrategy::Hybrid) \
	op(EAuracronFoliageStreamingStrategy::Custom) 

enum class EAuracronFoliageStreamingStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageStreamingStrategy> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageStreamingStrategy>();
// ********** End Enum EAuracronFoliageStreamingStrategy *******************************************

// ********** Begin Enum EAuracronFoliageMemoryStrategy ********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGEMEMORYSTRATEGY(op) \
	op(EAuracronFoliageMemoryStrategy::Conservative) \
	op(EAuracronFoliageMemoryStrategy::Balanced) \
	op(EAuracronFoliageMemoryStrategy::Aggressive) \
	op(EAuracronFoliageMemoryStrategy::Adaptive) \
	op(EAuracronFoliageMemoryStrategy::Custom) 

enum class EAuracronFoliageMemoryStrategy : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageMemoryStrategy> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageMemoryStrategy>();
// ********** End Enum EAuracronFoliageMemoryStrategy **********************************************

// ********** Begin Enum EAuracronFoliageLoadingPriority *******************************************
#define FOREACH_ENUM_EAURACRONFOLIAGELOADINGPRIORITY(op) \
	op(EAuracronFoliageLoadingPriority::VeryLow) \
	op(EAuracronFoliageLoadingPriority::Low) \
	op(EAuracronFoliageLoadingPriority::Normal) \
	op(EAuracronFoliageLoadingPriority::High) \
	op(EAuracronFoliageLoadingPriority::VeryHigh) \
	op(EAuracronFoliageLoadingPriority::Critical) 

enum class EAuracronFoliageLoadingPriority : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageLoadingPriority> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLoadingPriority>();
// ********** End Enum EAuracronFoliageLoadingPriority *********************************************

// ********** Begin Enum EAuracronFoliageStreamingState ********************************************
#define FOREACH_ENUM_EAURACRONFOLIAGESTREAMINGSTATE(op) \
	op(EAuracronFoliageStreamingState::Unloaded) \
	op(EAuracronFoliageStreamingState::Loading) \
	op(EAuracronFoliageStreamingState::Loaded) \
	op(EAuracronFoliageStreamingState::Unloading) \
	op(EAuracronFoliageStreamingState::Error) \
	op(EAuracronFoliageStreamingState::Cached) 

enum class EAuracronFoliageStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronFoliageStreamingState> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageStreamingState>();
// ********** End Enum EAuracronFoliageStreamingState **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
