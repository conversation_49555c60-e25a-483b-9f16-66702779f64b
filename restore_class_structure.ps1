# Script para restaurar a estrutura das classes que foi danificada
# Reconstroi as definicoes de classe que foram removidas incorretamente

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando restauracao da estrutura de classes..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Lista de classes que precisam ser restauradas com suas definicoes corretas
$classesToRestore = @{
    "UAuracronAdvancedPCGGenerator" = @{
        File = "AuracronAdvancedPCGGenerator.h"
        BaseClass = "UEngineSubsystem"
        StartLine = 267
    }
    "UAuracronPCGSettingsBase" = @{
        File = "AuracronPCGElementBase.h"
        BaseClass = "UPCGSettings"
        StartLine = 144
    }
    "UAuracronPCGGraphWrapper" = @{
        File = "AuracronPCGGraphSystem.h"
        BaseClass = "UObject"
        StartLine = 209
    }
}

# Encontra todos os arquivos .h
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalFixes = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileFixes = 0
    
    # Verifica se este arquivo precisa de restauracao
    $needsRestore = $false
    $className = ""
    $baseClass = ""
    
    foreach ($class in $classesToRestore.Keys) {
        $classInfo = $classesToRestore[$class]
        if ($file.Name -eq $classInfo.File) {
            $needsRestore = $true
            $className = $class
            $baseClass = $classInfo.BaseClass
            break
        }
    }
    
    if ($needsRestore) {
        Write-Host "    Restaurando classe: $className" -ForegroundColor Yellow
        
        # Procura por "public:" solto no escopo global (indica classe danificada)
        if ($content -match '^\s*public:\s*$') {
            # Adiciona a definicao da classe antes do primeiro "public:"
            $classDefinition = @"
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API $className : public $baseClass
{
    GENERATED_BODY()

"@
            
            # Substitui o primeiro "public:" pela definicao da classe + public:
            $content = $content -replace '^\s*public:\s*$', "$classDefinition`npublic:", 1
            $fileFixes++
            
            # Adiciona a chave de fechamento no final do arquivo (antes da ultima linha vazia)
            if (-not ($content -match '\};\s*$')) {
                $content = $content.TrimEnd() + "`n};"
                $fileFixes++
            }
        }
    }
    
    # Salva o arquivo se houve mudancas
    if ($fileFixes -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalFixes += $fileFixes
        $filesModified++
        Write-Host "  OK $fileFixes restauracoes aplicadas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma restauracao necessaria" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de restauracoes: $totalFixes" -ForegroundColor Green

if ($totalFixes -gt 0) {
    Write-Host "`nRestauracao da estrutura de classes concluida!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma restauracao necessaria." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
