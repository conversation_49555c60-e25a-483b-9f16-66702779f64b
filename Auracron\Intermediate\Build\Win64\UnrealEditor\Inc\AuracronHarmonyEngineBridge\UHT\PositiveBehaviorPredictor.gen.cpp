// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PositiveBehaviorPredictor.h"
#include "AuracronHarmonyEngineBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePositiveBehaviorPredictor() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UPositiveBehaviorPredictor();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UPositiveBehaviorPredictor_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UPositiveBehaviorPredictor Function AddTrainingData **********************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics
{
	struct PositiveBehaviorPredictor_eventAddTrainingData_Parms
	{
		FPlayerBehaviorSnapshot BehaviorData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Machine Learning Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Machine Learning Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::NewProp_BehaviorData = { "BehaviorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventAddTrainingData_Parms, BehaviorData), Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorData_MetaData), NewProp_BehaviorData_MetaData) }; // 3446946508
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::NewProp_BehaviorData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "AddTrainingData", Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PositiveBehaviorPredictor_eventAddTrainingData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::PositiveBehaviorPredictor_eventAddTrainingData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execAddTrainingData)
{
	P_GET_STRUCT_REF(FPlayerBehaviorSnapshot,Z_Param_Out_BehaviorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddTrainingData(Z_Param_Out_BehaviorData);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function AddTrainingData ************************

// ********** Begin Class UPositiveBehaviorPredictor Function AnalyzePositiveBehaviorPatterns ******
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics
{
	struct PositiveBehaviorPredictor_eventAnalyzePositiveBehaviorPatterns_Parms
	{
		FString PlayerID;
		TArray<FGameplayTag> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Analysis\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventAnalyzePositiveBehaviorPatterns_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventAnalyzePositiveBehaviorPatterns_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 133831994
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "AnalyzePositiveBehaviorPatterns", Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PositiveBehaviorPredictor_eventAnalyzePositiveBehaviorPatterns_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::PositiveBehaviorPredictor_eventAnalyzePositiveBehaviorPatterns_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execAnalyzePositiveBehaviorPatterns)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGameplayTag>*)Z_Param__Result=P_THIS->AnalyzePositiveBehaviorPatterns(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function AnalyzePositiveBehaviorPatterns ********

// ********** Begin Class UPositiveBehaviorPredictor Function CalculateBonusKindnessPoints *********
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics
{
	struct PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms
	{
		FString PlayerID;
		int32 BasePoints;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward Amplification" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BasePoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_BasePoints = { "BasePoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms, BasePoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_BasePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "CalculateBonusKindnessPoints", Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::PositiveBehaviorPredictor_eventCalculateBonusKindnessPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execCalculateBonusKindnessPoints)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_BasePoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->CalculateBonusKindnessPoints(Z_Param_PlayerID,Z_Param_BasePoints);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function CalculateBonusKindnessPoints ***********

// ********** Begin Class UPositiveBehaviorPredictor Function CalculateConsistencyScore ************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics
{
	struct PositiveBehaviorPredictor_eventCalculateConsistencyScore_Parms
	{
		FString PlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior Analysis" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateConsistencyScore_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateConsistencyScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "CalculateConsistencyScore", Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PositiveBehaviorPredictor_eventCalculateConsistencyScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::PositiveBehaviorPredictor_eventCalculateConsistencyScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execCalculateConsistencyScore)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateConsistencyScore(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function CalculateConsistencyScore **************

// ********** Begin Class UPositiveBehaviorPredictor Function CalculateLeadershipPotential *********
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics
{
	struct PositiveBehaviorPredictor_eventCalculateLeadershipPotential_Parms
	{
		FString PlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Positive Behavior Prediction" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateLeadershipPotential_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateLeadershipPotential_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "CalculateLeadershipPotential", Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PositiveBehaviorPredictor_eventCalculateLeadershipPotential_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::PositiveBehaviorPredictor_eventCalculateLeadershipPotential_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execCalculateLeadershipPotential)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateLeadershipPotential(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function CalculateLeadershipPotential ***********

// ********** Begin Class UPositiveBehaviorPredictor Function CalculateRewardMultiplier ************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics
{
	struct PositiveBehaviorPredictor_eventCalculateRewardMultiplier_Parms
	{
		FString PlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward Amplification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reward Amplification\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reward Amplification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateRewardMultiplier_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventCalculateRewardMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "CalculateRewardMultiplier", Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PositiveBehaviorPredictor_eventCalculateRewardMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::PositiveBehaviorPredictor_eventCalculateRewardMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execCalculateRewardMultiplier)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateRewardMultiplier(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function CalculateRewardMultiplier **************

// ********** Begin Class UPositiveBehaviorPredictor Function GetModelAccuracy *********************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics
{
	struct PositiveBehaviorPredictor_eventGetModelAccuracy_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventGetModelAccuracy_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "GetModelAccuracy", Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PositiveBehaviorPredictor_eventGetModelAccuracy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::PositiveBehaviorPredictor_eventGetModelAccuracy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execGetModelAccuracy)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetModelAccuracy();
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function GetModelAccuracy ***********************

// ********** Begin Class UPositiveBehaviorPredictor Function HasPositiveTrend *********************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics
{
	struct PositiveBehaviorPredictor_eventHasPositiveTrend_Parms
	{
		FString PlayerID;
		float TimeWindow;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior Analysis" },
		{ "CPP_Default_TimeWindow", "300.000000" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeWindow;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventHasPositiveTrend_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_TimeWindow = { "TimeWindow", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventHasPositiveTrend_Parms, TimeWindow), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PositiveBehaviorPredictor_eventHasPositiveTrend_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PositiveBehaviorPredictor_eventHasPositiveTrend_Parms), &Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_TimeWindow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "HasPositiveTrend", Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PositiveBehaviorPredictor_eventHasPositiveTrend_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::PositiveBehaviorPredictor_eventHasPositiveTrend_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execHasPositiveTrend)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeWindow);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasPositiveTrend(Z_Param_PlayerID,Z_Param_TimeWindow);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function HasPositiveTrend ***********************

// ********** Begin Class UPositiveBehaviorPredictor Function IdentifyPotentialCommunityLeaders ****
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics
{
	struct PositiveBehaviorPredictor_eventIdentifyPotentialCommunityLeaders_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Positive Behavior Prediction" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventIdentifyPotentialCommunityLeaders_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "IdentifyPotentialCommunityLeaders", Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PositiveBehaviorPredictor_eventIdentifyPotentialCommunityLeaders_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::PositiveBehaviorPredictor_eventIdentifyPotentialCommunityLeaders_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execIdentifyPotentialCommunityLeaders)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->IdentifyPotentialCommunityLeaders();
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function IdentifyPotentialCommunityLeaders ******

// ********** Begin Class UPositiveBehaviorPredictor Function IdentifyPotentialMentors *************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics
{
	struct PositiveBehaviorPredictor_eventIdentifyPotentialMentors_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Positive Behavior Prediction" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventIdentifyPotentialMentors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "IdentifyPotentialMentors", Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PositiveBehaviorPredictor_eventIdentifyPotentialMentors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::PositiveBehaviorPredictor_eventIdentifyPotentialMentors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execIdentifyPotentialMentors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->IdentifyPotentialMentors();
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function IdentifyPotentialMentors ***************

// ********** Begin Class UPositiveBehaviorPredictor Function IsPlayerLikelyToBePositive ***********
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics
{
	struct PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms
	{
		FString PlayerID;
		float Threshold;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Positive Behavior Prediction" },
		{ "CPP_Default_Threshold", "0.700000" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms), &Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_Threshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "IsPlayerLikelyToBePositive", Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::PositiveBehaviorPredictor_eventIsPlayerLikelyToBePositive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execIsPlayerLikelyToBePositive)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Threshold);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerLikelyToBePositive(Z_Param_PlayerID,Z_Param_Threshold);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function IsPlayerLikelyToBePositive *************

// ********** Begin Class UPositiveBehaviorPredictor Function PredictPositiveBehaviorProbability ***
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics
{
	struct PositiveBehaviorPredictor_eventPredictPositiveBehaviorProbability_Parms
	{
		FString PlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Positive Behavior Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Prediction Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Prediction Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventPredictPositiveBehaviorProbability_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventPredictPositiveBehaviorProbability_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "PredictPositiveBehaviorProbability", Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PositiveBehaviorPredictor_eventPredictPositiveBehaviorProbability_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::PositiveBehaviorPredictor_eventPredictPositiveBehaviorProbability_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execPredictPositiveBehaviorProbability)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->PredictPositiveBehaviorProbability(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function PredictPositiveBehaviorProbability *****

// ********** Begin Class UPositiveBehaviorPredictor Function ResetTrainingData ********************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "ResetTrainingData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execResetTrainingData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetTrainingData();
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function ResetTrainingData **********************

// ********** Begin Class UPositiveBehaviorPredictor Function ShouldAmplifyRewards *****************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics
{
	struct PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Reward Amplification" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms), &Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "ShouldAmplifyRewards", Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::PositiveBehaviorPredictor_eventShouldAmplifyRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execShouldAmplifyRewards)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldAmplifyRewards(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function ShouldAmplifyRewards *******************

// ********** Begin Class UPositiveBehaviorPredictor Function TrainPredictionModel *****************
struct Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPositiveBehaviorPredictor, nullptr, "TrainPredictionModel", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPositiveBehaviorPredictor::execTrainPredictionModel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrainPredictionModel();
	P_NATIVE_END;
}
// ********** End Class UPositiveBehaviorPredictor Function TrainPredictionModel *******************

// ********** Begin Class UPositiveBehaviorPredictor ***********************************************
void UPositiveBehaviorPredictor::StaticRegisterNativesUPositiveBehaviorPredictor()
{
	UClass* Class = UPositiveBehaviorPredictor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddTrainingData", &UPositiveBehaviorPredictor::execAddTrainingData },
		{ "AnalyzePositiveBehaviorPatterns", &UPositiveBehaviorPredictor::execAnalyzePositiveBehaviorPatterns },
		{ "CalculateBonusKindnessPoints", &UPositiveBehaviorPredictor::execCalculateBonusKindnessPoints },
		{ "CalculateConsistencyScore", &UPositiveBehaviorPredictor::execCalculateConsistencyScore },
		{ "CalculateLeadershipPotential", &UPositiveBehaviorPredictor::execCalculateLeadershipPotential },
		{ "CalculateRewardMultiplier", &UPositiveBehaviorPredictor::execCalculateRewardMultiplier },
		{ "GetModelAccuracy", &UPositiveBehaviorPredictor::execGetModelAccuracy },
		{ "HasPositiveTrend", &UPositiveBehaviorPredictor::execHasPositiveTrend },
		{ "IdentifyPotentialCommunityLeaders", &UPositiveBehaviorPredictor::execIdentifyPotentialCommunityLeaders },
		{ "IdentifyPotentialMentors", &UPositiveBehaviorPredictor::execIdentifyPotentialMentors },
		{ "IsPlayerLikelyToBePositive", &UPositiveBehaviorPredictor::execIsPlayerLikelyToBePositive },
		{ "PredictPositiveBehaviorProbability", &UPositiveBehaviorPredictor::execPredictPositiveBehaviorProbability },
		{ "ResetTrainingData", &UPositiveBehaviorPredictor::execResetTrainingData },
		{ "ShouldAmplifyRewards", &UPositiveBehaviorPredictor::execShouldAmplifyRewards },
		{ "TrainPredictionModel", &UPositiveBehaviorPredictor::execTrainPredictionModel },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPositiveBehaviorPredictor;
UClass* UPositiveBehaviorPredictor::GetPrivateStaticClass()
{
	using TClass = UPositiveBehaviorPredictor;
	if (!Z_Registration_Info_UClass_UPositiveBehaviorPredictor.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PositiveBehaviorPredictor"),
			Z_Registration_Info_UClass_UPositiveBehaviorPredictor.InnerSingleton,
			StaticRegisterNativesUPositiveBehaviorPredictor,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPositiveBehaviorPredictor.InnerSingleton;
}
UClass* Z_Construct_UClass_UPositiveBehaviorPredictor_NoRegister()
{
	return UPositiveBehaviorPredictor::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPositiveBehaviorPredictor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Positive Behavior Predictor\n * AI system that identifies players likely to exhibit positive behavior and amplifies rewards\n */" },
#endif
		{ "IncludePath", "PositiveBehaviorPredictor.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Positive Behavior Predictor\nAI system that identifies players likely to exhibit positive behavior and amplifies rewards" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositiveBehaviorThreshold_MetaData[] = {
		{ "Category", "Prediction Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeadershipThreshold_MetaData[] = {
		{ "Category", "Prediction Config" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConsistencyRequirement_MetaData[] = {
		{ "Category", "Prediction Config" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumDataPointsForPrediction_MetaData[] = {
		{ "Category", "Prediction Config" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardAmplificationFactor_MetaData[] = {
		{ "Category", "Prediction Config" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRewardAmplification_MetaData[] = {
		{ "Category", "Prediction Config" },
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrainingDataset_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ML Model Data\n" },
#endif
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ML Model Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerPositivityScores_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerConsistencyScores_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLeadershipScores_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentModelAccuracy_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrainingIterations_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bModelTrained_MetaData[] = {
		{ "ModuleRelativePath", "Public/PositiveBehaviorPredictor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PositiveBehaviorThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LeadershipThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConsistencyRequirement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinimumDataPointsForPrediction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardAmplificationFactor;
	static void NewProp_bEnableRewardAmplification_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRewardAmplification;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TrainingDataset_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TrainingDataset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerPositivityScores_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerPositivityScores_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerPositivityScores;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerConsistencyScores_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerConsistencyScores_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerConsistencyScores;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerLeadershipScores_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerLeadershipScores_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerLeadershipScores;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentModelAccuracy;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TrainingIterations;
	static void NewProp_bModelTrained_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bModelTrained;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_AddTrainingData, "AddTrainingData" }, // 3323356319
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_AnalyzePositiveBehaviorPatterns, "AnalyzePositiveBehaviorPatterns" }, // 1373290197
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateBonusKindnessPoints, "CalculateBonusKindnessPoints" }, // 810840750
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateConsistencyScore, "CalculateConsistencyScore" }, // 3498807124
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateLeadershipPotential, "CalculateLeadershipPotential" }, // 221290829
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_CalculateRewardMultiplier, "CalculateRewardMultiplier" }, // 856713353
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_GetModelAccuracy, "GetModelAccuracy" }, // 456374726
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_HasPositiveTrend, "HasPositiveTrend" }, // 958468618
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialCommunityLeaders, "IdentifyPotentialCommunityLeaders" }, // 1958935178
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_IdentifyPotentialMentors, "IdentifyPotentialMentors" }, // 2828162718
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_IsPlayerLikelyToBePositive, "IsPlayerLikelyToBePositive" }, // 2511366891
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_PredictPositiveBehaviorProbability, "PredictPositiveBehaviorProbability" }, // 2316499024
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_ResetTrainingData, "ResetTrainingData" }, // 1353893591
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_ShouldAmplifyRewards, "ShouldAmplifyRewards" }, // 2477718151
		{ &Z_Construct_UFunction_UPositiveBehaviorPredictor_TrainPredictionModel, "TrainPredictionModel" }, // 3089431875
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPositiveBehaviorPredictor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PositiveBehaviorThreshold = { "PositiveBehaviorThreshold", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, PositiveBehaviorThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositiveBehaviorThreshold_MetaData), NewProp_PositiveBehaviorThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_LeadershipThreshold = { "LeadershipThreshold", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, LeadershipThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeadershipThreshold_MetaData), NewProp_LeadershipThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_ConsistencyRequirement = { "ConsistencyRequirement", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, ConsistencyRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConsistencyRequirement_MetaData), NewProp_ConsistencyRequirement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_MinimumDataPointsForPrediction = { "MinimumDataPointsForPrediction", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, MinimumDataPointsForPrediction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumDataPointsForPrediction_MetaData), NewProp_MinimumDataPointsForPrediction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_RewardAmplificationFactor = { "RewardAmplificationFactor", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, RewardAmplificationFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardAmplificationFactor_MetaData), NewProp_RewardAmplificationFactor_MetaData) };
void Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bEnableRewardAmplification_SetBit(void* Obj)
{
	((UPositiveBehaviorPredictor*)Obj)->bEnableRewardAmplification = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bEnableRewardAmplification = { "bEnableRewardAmplification", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPositiveBehaviorPredictor), &Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bEnableRewardAmplification_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRewardAmplification_MetaData), NewProp_bEnableRewardAmplification_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingDataset_Inner = { "TrainingDataset", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, METADATA_PARAMS(0, nullptr) }; // 3446946508
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingDataset = { "TrainingDataset", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, TrainingDataset), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrainingDataset_MetaData), NewProp_TrainingDataset_MetaData) }; // 3446946508
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores_ValueProp = { "PlayerPositivityScores", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores_Key_KeyProp = { "PlayerPositivityScores_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores = { "PlayerPositivityScores", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, PlayerPositivityScores), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerPositivityScores_MetaData), NewProp_PlayerPositivityScores_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores_ValueProp = { "PlayerConsistencyScores", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores_Key_KeyProp = { "PlayerConsistencyScores_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores = { "PlayerConsistencyScores", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, PlayerConsistencyScores), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerConsistencyScores_MetaData), NewProp_PlayerConsistencyScores_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores_ValueProp = { "PlayerLeadershipScores", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores_Key_KeyProp = { "PlayerLeadershipScores_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores = { "PlayerLeadershipScores", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, PlayerLeadershipScores), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLeadershipScores_MetaData), NewProp_PlayerLeadershipScores_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_CurrentModelAccuracy = { "CurrentModelAccuracy", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, CurrentModelAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentModelAccuracy_MetaData), NewProp_CurrentModelAccuracy_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingIterations = { "TrainingIterations", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPositiveBehaviorPredictor, TrainingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrainingIterations_MetaData), NewProp_TrainingIterations_MetaData) };
void Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bModelTrained_SetBit(void* Obj)
{
	((UPositiveBehaviorPredictor*)Obj)->bModelTrained = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bModelTrained = { "bModelTrained", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPositiveBehaviorPredictor), &Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bModelTrained_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bModelTrained_MetaData), NewProp_bModelTrained_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PositiveBehaviorThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_LeadershipThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_ConsistencyRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_MinimumDataPointsForPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_RewardAmplificationFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bEnableRewardAmplification,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingDataset_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingDataset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerPositivityScores,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerConsistencyScores,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_PlayerLeadershipScores,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_CurrentModelAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_TrainingIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::NewProp_bModelTrained,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::ClassParams = {
	&UPositiveBehaviorPredictor::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::Class_MetaDataParams), Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPositiveBehaviorPredictor()
{
	if (!Z_Registration_Info_UClass_UPositiveBehaviorPredictor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPositiveBehaviorPredictor.OuterSingleton, Z_Construct_UClass_UPositiveBehaviorPredictor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPositiveBehaviorPredictor.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPositiveBehaviorPredictor);
UPositiveBehaviorPredictor::~UPositiveBehaviorPredictor() {}
// ********** End Class UPositiveBehaviorPredictor *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPositiveBehaviorPredictor, UPositiveBehaviorPredictor::StaticClass, TEXT("UPositiveBehaviorPredictor"), &Z_Registration_Info_UClass_UPositiveBehaviorPredictor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPositiveBehaviorPredictor), 3521782838U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h__Script_AuracronHarmonyEngineBridge_2784691887(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
