﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Python Bindings Header
// Bridge 2.15: PCG Framework - Python Integration

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGBasePointData.h"
#include "Data/PCGSpatialData.h"
#include "Metadata/PCGMetadata.h"
#include "PCGGraph.h"
#include "PCGComponent.h"
#include "PCGPin.h"

// Python integration includes
#include "Engine/Engine.h"
#include "Modules/ModuleManager.h"
#include "UObject/PropertyPortFlags.h"

// pybind11 includes
#ifdef WITH_PYTHON
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <pybind11/functional.h>
#include <pybind11/chrono.h>
#include <pybind11/complex.h>
#include <pybind11/cast.h>
#include <pybind11/embed.h>
#endif

#include "AuracronPCGPythonBindings.generated.h"

// Forward declarations
class FProperty;
class UAuracronPCGNodeSystem;
class UAuracronPCGElementBase;
class UAuracronPCGCustomNodeSystem;
class UAuracronPCGDebugSystem;

// =============================================================================
// PYTHON BINDING TYPES
// =============================================================================

// Python binding categories
UENUM(BlueprintType)
enum class EAuracronPCGPythonBindingCategory : uint8
{
    Core                    UMETA(DisplayName = "Core"),
    Nodes                   UMETA(DisplayName = "Nodes"),
    Data                    UMETA(DisplayName = "Data"),
    Graph                   UMETA(DisplayName = "Graph"),
    Execution               UMETA(DisplayName = "Execution"),
    Debug                   UMETA(DisplayName = "Debug"),
    Custom                  UMETA(DisplayName = "Custom"),
    Utilities               UMETA(DisplayName = "Utilities"),
    All                     UMETA(DisplayName = "All")
};

// Python binding modes
UENUM(BlueprintType)
enum class EAuracronPCGPythonBindingMode : uint8
{
    ReadOnly                UMETA(DisplayName = "Read Only"),
    ReadWrite               UMETA(DisplayName = "Read Write"),
    ExecuteOnly             UMETA(DisplayName = "Execute Only"),
    Full                    UMETA(DisplayName = "Full Access")
};

// Python execution contexts
UENUM(BlueprintType)
enum class EAuracronPCGPythonExecutionContext : uint8
{
    Editor                  UMETA(DisplayName = "Editor"),
    Runtime                 UMETA(DisplayName = "Runtime"),
    Commandlet              UMETA(DisplayName = "Commandlet"),
    Server                  UMETA(DisplayName = "Server"),
    Client                  UMETA(DisplayName = "Client"),
    Any                     UMETA(DisplayName = "Any")
};

// =============================================================================
// PYTHON BINDING DESCRIPTOR
// =============================================================================

/**
 * Python Binding Descriptor
 * Describes configuration for Python bindings
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGPythonBindingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Binding")
    EAuracronPCGPythonBindingCategory Category = EAuracronPCGPythonBindingCategory::Core;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Binding")
    EAuracronPCGPythonBindingMode Mode = EAuracronPCGPythonBindingMode::Full;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Binding")
    EAuracronPCGPythonExecutionContext Context = EAuracronPCGPythonExecutionContext::Any;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Module")
    FString ModuleName = TEXT("auracron_pcg");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Module")
    FString ModuleDescription = TEXT("Auracron PCG Framework Python Bindings");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Module")
    FString ModuleVersion = TEXT("1.0.0");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Classes")
    TArray<FString> ClassesToBind;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Classes")
    TArray<FString> ClassesToExclude;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Functions")
    TArray<FString> FunctionsToBind;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Functions")
    TArray<FString> FunctionsToExclude;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
    bool bBindProperties = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
    bool bBindReadOnlyProperties = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
    bool bBindWriteOnlyProperties = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Events")
    bool bBindEvents = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Events")
    bool bBindDelegates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableAutoCompletion = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableDocstrings = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableTypeHints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableErrorHandling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLazyLoading = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxCacheSize = 1000;

    FAuracronPCGPythonBindingDescriptor()
    {
        Category = EAuracronPCGPythonBindingCategory::Core;
        Mode = EAuracronPCGPythonBindingMode::Full;
        Context = EAuracronPCGPythonExecutionContext::Any;
        ModuleName = TEXT("auracron_pcg");
        ModuleDescription = TEXT("Auracron PCG Framework Python Bindings");
        ModuleVersion = TEXT("1.0.0");
        bBindProperties = true;
        bBindReadOnlyProperties = true;
        bBindWriteOnlyProperties = false;
        bBindEvents = true;
        bBindDelegates = true;
        bEnableAutoCompletion = true;
        bEnableDocstrings = true;
        bEnableTypeHints = true;
        bEnableErrorHandling = true;
        bEnableCaching = true;
        bEnableLazyLoading = false;
        MaxCacheSize = 1000;
    }
};

// =============================================================================
// PYTHON EXECUTION RESULT
// =============================================================================

/**
 * Python Execution Result
 * Result of Python code execution
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGPythonExecutionResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString Output;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    float ExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    int32 LinesExecuted = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TMap<FString, FString> Variables;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Result")
    TArray<FString> Warnings;

    FAuracronPCGPythonExecutionResult()
    {
        bSuccess = false;
        ExecutionTime = 0.0f;
        LinesExecuted = 0;
    }
};

// =============================================================================
// PYTHON BINDING MANAGER
// =============================================================================

/**
 * Python Binding Manager
 * Manages Python bindings for PCG Framework
 */

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    static UAuracronPCGPythonBindingManager* GetInstance();

    // Initialization and shutdown
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool InitializePythonBindings(const FAuracronPCGPythonBindingDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    void ShutdownPythonBindings();

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool IsInitialized() const;

    // Module management
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool RegisterModule(const FString& ModuleName, const FAuracronPCGPythonBindingDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool UnregisterModule(const FString& ModuleName);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    TArray<FString> GetRegisteredModules() const;

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool IsModuleRegistered(const FString& ModuleName) const;

    // Binding operations
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool BindClass(const FString& ClassName, UClass* Class);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool BindFunction(const FString& FunctionName, UFunction* Function);

    // C++ only function (FProperty cannot be exposed to Blueprint)
    bool BindProperty(const FString& PropertyName, FProperty* Property);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool BindEnum(const FString& EnumName, UEnum* Enum);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool BindStruct(const FString& StructName, UScriptStruct* Struct);

    // Execution functions
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    FAuracronPCGPythonExecutionResult ExecutePythonCode(const FString& Code);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    FAuracronPCGPythonExecutionResult ExecutePythonFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    FAuracronPCGPythonExecutionResult ExecutePythonFunction(const FString& ModuleName, const FString& FunctionName, const TArray<FString>& Arguments);

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool ImportPythonModule(const FString& ModuleName);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool ReloadPythonModule(const FString& ModuleName);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    TArray<FString> GetPythonPath() const;

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool AddToPythonPath(const FString& Path);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    FString GetPythonVersion() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    void SetBindingDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor);

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    FAuracronPCGPythonBindingDescriptor GetBindingDescriptor() const;

    // Error handling
    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    TArray<FString> GetLastErrors() const;

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    void ClearErrors();

    UFUNCTION(BlueprintCallable, Category = "Python Binding Manager")
    bool HasErrors() const;

private:
    UPROPERTY()
    FAuracronPCGPythonBindingDescriptor CurrentDescriptor;

    UPROPERTY()
    TMap<FString, FAuracronPCGPythonBindingDescriptor> RegisteredModules;

    UPROPERTY()
    TArray<FString> LastErrors;

    static UAuracronPCGPythonBindingManager* Instance;
    bool bIsInitialized = false;

#ifdef WITH_PYTHON
    // pybind11 module instance
    pybind11::module_ PCGModule;
#endif

    // Internal binding functions
    void BindCoreClasses();
    void BindNodeClasses();
    void BindDataClasses();
    void BindGraphClasses();
    void BindExecutionClasses();
    void BindDebugClasses();
    void BindCustomClasses();
    void BindUtilityClasses();

    // Helper functions
    void AddError(const FString& Error);
    bool ValidateDescriptor(const FAuracronPCGPythonBindingDescriptor& Descriptor);
};

// =============================================================================
// PYTHON SCRIPT EXECUTOR
// =============================================================================

/**
 * Python Script Executor
 * Executes Python scripts with PCG context
 */

public:
    // Script execution
    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static FAuracronPCGPythonExecutionResult ExecuteScript(const FString& ScriptContent, const TMap<FString, FString>& Variables);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor", meta = (CallInEditor = "true"))
    static FAuracronPCGPythonExecutionResult ExecuteScriptSimple(const FString& ScriptContent);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static FAuracronPCGPythonExecutionResult ExecuteScriptFile(const FString& FilePath, const TMap<FString, FString>& Variables);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor", meta = (CallInEditor = "true"))
    static FAuracronPCGPythonExecutionResult ExecuteScriptFileSimple(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static FAuracronPCGPythonExecutionResult ExecuteScriptWithGraph(const FString& ScriptContent, UPCGGraph* Graph, const TMap<FString, FString>& Variables);

    // Async execution
    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static void ExecuteScriptAsync(const FString& ScriptContent, const FString& CallbackFunction = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static void ExecuteScriptFileAsync(const FString& FilePath, const FString& CallbackFunction = TEXT(""));

    // Script validation
    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static bool ValidateScript(const FString& ScriptContent, TArray<FString>& OutErrors);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static bool ValidateScriptFile(const FString& FilePath, TArray<FString>& OutErrors);

    // Script utilities
    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static FString FormatScript(const FString& ScriptContent);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static TArray<FString> GetScriptDependencies(const FString& ScriptContent);

    UFUNCTION(BlueprintCallable, Category = "Python Script Executor")
    static bool HasScriptFunction(const FString& ScriptContent, const FString& FunctionName);

private:
    static TMap<FString, FAuracronPCGPythonExecutionResult> ExecutionCache;
    static bool bCacheEnabled;
};

// =============================================================================
// PYTHON INTEGRATION UTILITIES
// =============================================================================

/**
 * Python Integration Utilities
 * Utility functions for Python integration
 */

public:
    // Type conversion utilities
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString ConvertUObjectToPython(UObject* Object);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static UObject* ConvertPythonToUObject(const FString& PythonObject, UClass* TargetClass);

    // C++ only functions (void* cannot be exposed to Blueprints)
    static FString ConvertStructToPython(const UScriptStruct* Struct, const void* StructPtr);
    static bool ConvertPythonToStruct(const FString& PythonStruct, const UScriptStruct* Struct, void* StructPtr);

    // Array conversion utilities
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString ConvertArrayToPython(const TArray<FString>& Array);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static TArray<FString> ConvertPythonToArray(const FString& PythonArray);

    // Map conversion utilities
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString ConvertMapToPython(const TMap<FString, FString>& Map);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static TMap<FString, FString> ConvertPythonToMap(const FString& PythonMap);

    // Documentation generation
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GenerateClassDocumentation(UClass* Class);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GenerateFunctionDocumentation(UFunction* Function);

    // C++ only function (FProperty cannot be exposed to Blueprint)
    static FString GeneratePropertyDocumentation(FProperty* Property);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GenerateModuleDocumentation(const FString& ModuleName);

    // Code generation utilities
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GeneratePythonStub(UClass* Class);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GeneratePythonWrapper(UClass* Class);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static bool SavePythonStubs(const FString& OutputDirectory);

    // Debugging utilities
    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static void EnablePythonDebugging(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static bool IsPythonDebuggingEnabled();

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static TArray<FString> GetPythonCallStack();

    UFUNCTION(BlueprintCallable, Category = "Python Integration Utils")
    static FString GetPythonMemoryUsage();
};
