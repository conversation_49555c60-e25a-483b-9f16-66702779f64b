// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronAudioBridge_init() {}
	AURACRONAUDIOBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature();
	AURACRONAUDIOBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronAudioBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronAudioBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronAudioBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronAudioBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x050A36EF,
				0x98023D53,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronAudioBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronAudioBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronAudioBridge(Z_Construct_UPackage__Script_AuracronAudioBridge, TEXT("/Script/AuracronAudioBridge"), Z_Registration_Info_UPackage__Script_AuracronAudioBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x050A36EF, 0x98023D53));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
