﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Caching System Header
// Bridge 2.17: PCG Framework - Caching System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
// UE 5.6 Compatible - Core PCG includes
#include "PCGPoint.h"
#include "PCGPointArray.h"
#include "PCGData.h"
#include "Metadata/PCGMetadata.h"
#include "PCGGraph.h"
#include "PCGComponent.h"
#include "PCGContext.h"

// Engine includes for caching
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "Misc/SecureHash.h"
#include "Serialization/Archive.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Templates/SharedPointer.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"

#include "AuracronPCGCachingSystem.generated.h"

// Forward declarations
class UAuracronPCGCacheManager;
class UAuracronPCGDependencyTracker;
class UAuracronPCGCacheInvalidator;

// =============================================================================
// CACHE TYPES AND ENUMS
// =============================================================================

// Cache entry types
UENUM(BlueprintType)
enum class EAuracronPCGCacheEntryType : uint8
{
    NodeResult              UMETA(DisplayName = "Node Result"),
    GraphResult             UMETA(DisplayName = "Graph Result"),
    PointData               UMETA(DisplayName = "Point Data"),
    SpatialData             UMETA(DisplayName = "Spatial Data"),
    Metadata                UMETA(DisplayName = "Metadata"),
    Settings                UMETA(DisplayName = "Settings"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Cache invalidation strategies
UENUM(BlueprintType)
enum class EAuracronPCGCacheInvalidationStrategy : uint8
{
    Immediate               UMETA(DisplayName = "Immediate"),
    Deferred                UMETA(DisplayName = "Deferred"),
    OnAccess                UMETA(DisplayName = "On Access"),
    TimeBasedLRU            UMETA(DisplayName = "Time Based LRU"),
    SizeBasedLRU            UMETA(DisplayName = "Size Based LRU"),
    DependencyBased         UMETA(DisplayName = "Dependency Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Cache storage types
UENUM(BlueprintType)
enum class EAuracronPCGCacheStorageType : uint8
{
    Memory                  UMETA(DisplayName = "Memory Only"),
    Disk                    UMETA(DisplayName = "Disk Only"),
    Hybrid                  UMETA(DisplayName = "Memory + Disk"),
    Distributed             UMETA(DisplayName = "Distributed"),
    Compressed              UMETA(DisplayName = "Compressed"),
    Encrypted               UMETA(DisplayName = "Encrypted")
};

// Cache access patterns
UENUM(BlueprintType)
enum class EAuracronPCGCacheAccessPattern : uint8
{
    ReadOnly                UMETA(DisplayName = "Read Only"),
    WriteOnly               UMETA(DisplayName = "Write Only"),
    ReadWrite               UMETA(DisplayName = "Read Write"),
    WriteThrough            UMETA(DisplayName = "Write Through"),
    WriteBack               UMETA(DisplayName = "Write Back"),
    ReadThrough             UMETA(DisplayName = "Read Through")
};

// =============================================================================
// CACHE CONFIGURATION
// =============================================================================

/**
 * Cache Configuration
 * Configuration settings for the caching system
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCacheConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache")
    bool bEnableCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache")
    EAuracronPCGCacheStorageType StorageType = EAuracronPCGCacheStorageType::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache")
    EAuracronPCGCacheInvalidationStrategy InvalidationStrategy = EAuracronPCGCacheInvalidationStrategy::DependencyBased;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache")
    EAuracronPCGCacheAccessPattern AccessPattern = EAuracronPCGCacheAccessPattern::ReadWrite;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxMemoryCacheSizeMB = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxCacheEntries = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MemoryPressureThreshold = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    FString DiskCacheDirectory = TEXT("Saved/PCGCache");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    int32 MaxDiskCacheSizeMB = 5120;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    bool bCompressDiskCache = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Disk")
    bool bEncryptDiskCache = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Expiration")
    float DefaultExpirationTimeSeconds = 3600.0f; // 1 hour

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Expiration")
    float MaxExpirationTimeSeconds = 86400.0f; // 24 hours

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Expiration")
    bool bUseTimeBasedExpiration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Expiration")
    bool bUseAccessBasedExpiration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CacheWorkerThreads = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCachePreloading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableCachePrefetching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bEnableCacheDebugging = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bLogCacheOperations = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    bool bTrackCacheStatistics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debugging")
    float StatisticsUpdateInterval = 1.0f;

    FAuracronPCGCacheConfiguration()
    {
        bEnableCaching = true;
        StorageType = EAuracronPCGCacheStorageType::Hybrid;
        InvalidationStrategy = EAuracronPCGCacheInvalidationStrategy::DependencyBased;
        AccessPattern = EAuracronPCGCacheAccessPattern::ReadWrite;
        MaxMemoryCacheSizeMB = 1024;
        MaxCacheEntries = 10000;
        MemoryPressureThreshold = 0.8f;
        DiskCacheDirectory = TEXT("Saved/PCGCache");
        MaxDiskCacheSizeMB = 5120;
        bCompressDiskCache = true;
        bEncryptDiskCache = false;
        DefaultExpirationTimeSeconds = 3600.0f;
        MaxExpirationTimeSeconds = 86400.0f;
        bUseTimeBasedExpiration = true;
        bUseAccessBasedExpiration = true;
        bEnableAsyncCaching = true;
        CacheWorkerThreads = 2;
        bEnableCachePreloading = true;
        bEnableCachePrefetching = true;
        bEnableCacheDebugging = false;
        bLogCacheOperations = false;
        bTrackCacheStatistics = true;
        StatisticsUpdateInterval = 1.0f;
    }
};

// =============================================================================
// CACHE KEY
// =============================================================================

/**
 * Cache Key
 * Unique identifier for cache entries
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCacheKey
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    FString NodeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    FString GraphId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    FString ParametersHash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    FString InputDataHash;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    EAuracronPCGCacheEntryType EntryType = EAuracronPCGCacheEntryType::NodeResult;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Key")
    int32 Version = 1;

    FAuracronPCGCacheKey()
    {
        EntryType = EAuracronPCGCacheEntryType::NodeResult;
        Version = 1;
    }

    // Generate unique hash for this cache key
    FString GenerateHash() const;

    // Comparison operators
    bool operator==(const FAuracronPCGCacheKey& Other) const;
    bool operator!=(const FAuracronPCGCacheKey& Other) const;
    bool operator<(const FAuracronPCGCacheKey& Other) const;

    // Hash function for use in TMap
    friend uint32 GetTypeHash(const FAuracronPCGCacheKey& Key);
};

// =============================================================================
// CACHE ENTRY
// =============================================================================

/**
 * Cache Entry
 * Individual cache entry with data and metadata
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCacheEntry
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FAuracronPCGCacheKey Key;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    TArray<uint8> SerializedData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FDateTime LastAccessTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    FDateTime ExpirationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 AccessCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    int32 DataSizeBytes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    bool bIsCompressed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    bool bIsEncrypted = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    bool bIsPersistent = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    float ComputationTimeSeconds = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    TArray<FString> Dependencies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cache Entry")
    TMap<FString, FString> Metadata;

    FAuracronPCGCacheEntry()
    {
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
        ExpirationTime = CreationTime + FTimespan::FromSeconds(3600.0); // 1 hour default
        AccessCount = 0;
        DataSizeBytes = 0;
        bIsCompressed = false;
        bIsEncrypted = false;
        bIsPersistent = false;
        ComputationTimeSeconds = 0.0f;
    }

    // Check if entry is expired
    bool IsExpired() const;

    // Update access information
    void UpdateAccess();

    // Serialize/Deserialize
    void Serialize(FArchive& Ar);
};

// =============================================================================
// CACHE STATISTICS
// =============================================================================

/**
 * Cache Statistics
 * Performance and usage statistics for the cache
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCacheStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalEntries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 MemoryEntries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 DiskEntries = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalHits = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalMisses = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalEvictions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalInvalidations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float HitRatio = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float MissRatio = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 MemoryUsageBytes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 DiskUsageBytes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageAccessTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageComputationTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TimeSavedSeconds = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronPCGCacheStatistics()
    {
        TotalEntries = 0;
        MemoryEntries = 0;
        DiskEntries = 0;
        TotalHits = 0;
        TotalMisses = 0;
        TotalEvictions = 0;
        TotalInvalidations = 0;
        HitRatio = 0.0f;
        MissRatio = 0.0f;
        MemoryUsageBytes = 0;
        DiskUsageBytes = 0;
        AverageAccessTime = 0.0f;
        AverageComputationTime = 0.0f;
        TimeSavedSeconds = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// DEPENDENCY TRACKER
// =============================================================================

/**
 * Dependency Tracker
 * Tracks dependencies between cache entries and nodes
 */

public:
    // Dependency management
    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void AddDependency(const FString& DependentId, const FString& DependencyId);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void RemoveDependency(const FString& DependentId, const FString& DependencyId);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void RemoveAllDependencies(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> GetDependencies(const FString& NodeId) const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> GetDependents(const FString& NodeId) const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> GetAllDependencies(const FString& NodeId, bool bRecursive = true) const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> GetAllDependents(const FString& NodeId, bool bRecursive = true) const;

    // Dependency analysis
    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    bool HasDependency(const FString& DependentId, const FString& DependencyId) const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    bool HasCircularDependency(const FString& NodeId) const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> DetectCircularDependencies() const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    TArray<FString> GetTopologicalOrder() const;

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    int32 GetDependencyDepth(const FString& NodeId) const;

    // Graph operations
    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void BuildDependencyGraph(UPCGGraph* Graph);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void UpdateDependencyGraph(UPCGGraph* Graph);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void ClearDependencyGraph();

    // Serialization
    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void SaveDependencyGraph(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Dependency Tracker")
    void LoadDependencyGraph(const FString& FilePath);

private:
    // Dependency maps
    TMap<FString, TSet<FString>> Dependencies; // NodeId -> Set of dependencies
    TMap<FString, TSet<FString>> Dependents;   // NodeId -> Set of dependents

    mutable FCriticalSection DependencyLock;

    // Helper functions
    void GetAllDependenciesRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TArray<FString>& Result) const;
    void GetAllDependentsRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TArray<FString>& Result) const;
    bool HasCircularDependencyRecursive(const FString& NodeId, TSet<FString>& VisitedNodes, TSet<FString>& RecursionStack) const;
};

// =============================================================================
// CACHE INVALIDATOR
// =============================================================================

/**
 * Cache Invalidator
 * Handles intelligent cache invalidation based on dependencies and changes
 */

public:
    // Invalidation operations
    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateEntry(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateNode(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateGraph(const FString& GraphId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateDependents(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateExpiredEntries();

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateByPattern(const FString& Pattern);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void InvalidateAll();

    // Invalidation strategies
    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void SetInvalidationStrategy(EAuracronPCGCacheInvalidationStrategy Strategy);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    EAuracronPCGCacheInvalidationStrategy GetInvalidationStrategy() const;

    // Invalidation scheduling
    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void ScheduleInvalidation(const FAuracronPCGCacheKey& Key, float DelaySeconds);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void CancelScheduledInvalidation(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void ProcessScheduledInvalidations();

    // Change detection
    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void RegisterChangeListener(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void UnregisterChangeListener(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void NotifyNodeChanged(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void NotifyGraphChanged(const FString& GraphId);

    // Statistics
    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    int32 GetInvalidationCount() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    TMap<FString, int32> GetInvalidationStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Invalidator")
    void ResetInvalidationStatistics();

private:
    UPROPERTY()
    EAuracronPCGCacheInvalidationStrategy InvalidationStrategy = EAuracronPCGCacheInvalidationStrategy::DependencyBased;

    UPROPERTY()
    UAuracronPCGDependencyTracker* DependencyTracker;

    // Scheduled invalidations
    TMap<FAuracronPCGCacheKey, FDateTime> ScheduledInvalidations;
    TSet<FString> ChangeListeners;
    TMap<FString, int32> InvalidationStatistics;

    mutable FCriticalSection InvalidationLock;

    // Helper functions
    void InvalidateByStrategy(const FAuracronPCGCacheKey& Key);
    void InvalidateImmediate(const FAuracronPCGCacheKey& Key);
    void InvalidateDeferred(const FAuracronPCGCacheKey& Key);
    void InvalidateLRU();
};

// =============================================================================
// PERSISTENT STORAGE
// =============================================================================

/**
 * Persistent Storage
 * Handles persistent storage of cache entries to disk
 */

public:
    // Storage operations
    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool SaveEntry(const FAuracronPCGCacheEntry& Entry);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool LoadEntry(const FAuracronPCGCacheKey& Key, FAuracronPCGCacheEntry& OutEntry);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool DeleteEntry(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool EntryExists(const FAuracronPCGCacheKey& Key);

    // Batch operations
    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    int32 SaveEntries(const TArray<FAuracronPCGCacheEntry>& Entries);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    int32 LoadEntries(const TArray<FAuracronPCGCacheKey>& Keys, TArray<FAuracronPCGCacheEntry>& OutEntries);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    int32 DeleteEntries(const TArray<FAuracronPCGCacheKey>& Keys);

    // Storage management
    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    void SetStorageDirectory(const FString& Directory);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    FString GetStorageDirectory() const;

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    int32 GetStorageSize() const;

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    int32 GetEntryCount() const;

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    void CleanupStorage();

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    void CompactStorage();

    // Compression and encryption
    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    void SetCompressionEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool IsCompressionEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    void SetEncryptionEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Persistent Storage")
    bool IsEncryptionEnabled() const;

private:
    UPROPERTY()
    FString StorageDirectory = TEXT("Saved/PCGCache");

    UPROPERTY()
    bool bCompressionEnabled = true;

    UPROPERTY()
    bool bEncryptionEnabled = false;

    mutable FCriticalSection StorageLock;

    // Helper functions
    FString GetEntryFilePath(const FAuracronPCGCacheKey& Key) const;
    bool CompressData(const TArray<uint8>& InputData, TArray<uint8>& OutputData) const;
    bool DecompressData(const TArray<uint8>& InputData, TArray<uint8>& OutputData) const;
    bool EncryptData(const TArray<uint8>& InputData, TArray<uint8>& OutputData) const;
    bool DecryptData(const TArray<uint8>& InputData, TArray<uint8>& OutputData) const;
};

// =============================================================================
// CACHE MANAGER
// =============================================================================

/**
 * Cache Manager
 * Central manager for the PCG caching system
 */

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    static UAuracronPCGCacheManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void Initialize(const FAuracronPCGCacheConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool IsInitialized() const;

    // Cache operations
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool GetCachedResult(const FAuracronPCGCacheKey& Key, TArray<uint8>& OutData);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool SetCachedResult(const FAuracronPCGCacheKey& Key, const TArray<uint8>& Data, float ComputationTime = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool HasCachedResult(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool RemoveCachedResult(const FAuracronPCGCacheKey& Key);

    // PCG-specific cache operations
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool GetCachedPointData(const FAuracronPCGCacheKey& Key, UPCGPointData*& OutPointData);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool SetCachedPointData(const FAuracronPCGCacheKey& Key, UPCGPointData* PointData, float ComputationTime = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool GetCachedSpatialData(const FAuracronPCGCacheKey& Key, UPCGSpatialData*& OutSpatialData);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    bool SetCachedSpatialData(const FAuracronPCGCacheKey& Key, UPCGSpatialData* SpatialData, float ComputationTime = 0.0f);

    // Cache management
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void InvalidateCache(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void InvalidateNodeCache(const FString& NodeId);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void InvalidateGraphCache(const FString& GraphId);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void ClearCache();

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void CleanupExpiredEntries();

    // Cache statistics
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    FAuracronPCGCacheStatistics GetCacheStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void ResetCacheStatistics();

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    float GetHitRatio() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    int32 GetCacheSize() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    int32 GetMemoryUsage() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void SetConfiguration(const FAuracronPCGCacheConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    FAuracronPCGCacheConfiguration GetConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void SetMaxMemorySize(int32 SizeMB);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void SetMaxEntries(int32 MaxEntries);

    // Preloading and prefetching
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void PreloadCache(const TArray<FAuracronPCGCacheKey>& Keys);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void PrefetchCache(const TArray<FAuracronPCGCacheKey>& Keys);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void SetPrefetchingEnabled(bool bEnabled);

    // Dependency management
    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void AddDependency(const FString& DependentId, const FString& DependencyId);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    void RemoveDependency(const FString& DependentId, const FString& DependencyId);

    UFUNCTION(BlueprintCallable, Category = "Cache Manager")
    TArray<FString> GetDependencies(const FString& NodeId) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheHit, FAuracronPCGCacheKey, Key, float, AccessTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheMiss, FAuracronPCGCacheKey, Key, float, ComputationTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCacheEviction, FAuracronPCGCacheKey, Key, FString, Reason);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCacheHit OnCacheHit;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCacheMiss OnCacheMiss;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCacheEviction OnCacheEviction;

private:
    static UAuracronPCGCacheManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronPCGCacheConfiguration Configuration;

    UPROPERTY()
    UAuracronPCGDependencyTracker* DependencyTracker;

    UPROPERTY()
    UAuracronPCGCacheInvalidator* CacheInvalidator;

    UPROPERTY()
    UAuracronPCGPersistentStorage* PersistentStorage;

    // Cache storage
    TMap<FAuracronPCGCacheKey, FAuracronPCGCacheEntry> MemoryCache;
    TMap<FAuracronPCGCacheKey, FDateTime> AccessTimes;

    // Statistics
    FAuracronPCGCacheStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection CacheLock;

    // Internal functions
    bool GetFromMemoryCache(const FAuracronPCGCacheKey& Key, FAuracronPCGCacheEntry& OutEntry);
    bool GetFromDiskCache(const FAuracronPCGCacheKey& Key, FAuracronPCGCacheEntry& OutEntry);
    void SetToMemoryCache(const FAuracronPCGCacheKey& Key, const FAuracronPCGCacheEntry& Entry);
    void SetToDiskCache(const FAuracronPCGCacheKey& Key, const FAuracronPCGCacheEntry& Entry);
    void EvictLRUEntries();
    void EvictExpiredEntries();
    void UpdateStatistics(bool bHit, float AccessTime, float ComputationTime = 0.0f);
    TArray<uint8> SerializeData(UObject* Object) const;
    UObject* DeserializeData(const TArray<uint8>& Data, UClass* ObjectClass) const;
    FAuracronPCGCacheKey GenerateKeyForNode(const FString& NodeId, UPCGSettings* Settings, const TArray<UPCGData*>& InputData) const;
    FString GenerateParametersHash(UPCGSettings* Settings) const;
    FString GenerateInputDataHash(const TArray<UPCGData*>& InputData) const;
};

// =============================================================================
// CACHE UTILITIES
// =============================================================================

/**
 * Cache Utilities
 * Utility functions for the caching system
 */

public:
    // Key generation
    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FAuracronPCGCacheKey GenerateKeyForNode(const FString& NodeId, UPCGSettings* Settings);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FAuracronPCGCacheKey GenerateKeyForGraph(const FString& GraphId, const TMap<FString, FString>& Parameters);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FString GenerateHashForData(UPCGData* Data);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FString GenerateHashForSettings(UPCGSettings* Settings);

    // Data serialization
    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static TArray<uint8> SerializePointData(UPCGPointData* PointData);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static UPCGPointData* DeserializePointData(const TArray<uint8>& Data);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static TArray<uint8> SerializeSpatialData(UPCGSpatialData* SpatialData);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static UPCGSpatialData* DeserializeSpatialData(const TArray<uint8>& Data);

    // Cache validation
    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static bool ValidateCacheEntry(const FAuracronPCGCacheEntry& Entry);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static bool ValidateCacheKey(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static bool IsDataCompatible(UPCGData* Data1, UPCGData* Data2);

    // Performance utilities
    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static float EstimateCacheValue(float ComputationTime, int32 DataSize, int32 AccessFrequency);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static int32 EstimateMemoryUsage(const FAuracronPCGCacheEntry& Entry);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static bool ShouldCacheResult(float ComputationTime, int32 DataSize);

    // Debugging utilities
    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FString CacheKeyToString(const FAuracronPCGCacheKey& Key);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FString CacheEntryToString(const FAuracronPCGCacheEntry& Entry);

    UFUNCTION(BlueprintCallable, Category = "Cache Utils")
    static FString CacheStatisticsToString(const FAuracronPCGCacheStatistics& Statistics);
};
