// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for Auracron
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef AURACRON_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Auracron
#define UE_TARGET_NAME AuracronEditor
#define UE_MODULE_NAME "Auracron"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define AURACRON_FOLIAGE_BRIDGE_PLATFORM_WINDOWS 1
#define WITH_FOLIAGE 1
#define AURACRON_FOLIAGE_BRIDGE_VERSION_MAJOR 1
#define AURACRON_FOLIAGE_BRIDGE_VERSION_MINOR 0
#define AURACRON_FOLIAGE_BRIDGE_VERSION_PATCH 0
#define AURACRONFOLIAGEBRIDGE_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define DYNAMICMESH_API DLLIMPORT
#define GEOMETRYALGORITHMS_API DLLIMPORT
#define GEOMETRYFRAMEWORK_API DLLIMPORT
#define MESHCONVERSION_API DLLIMPORT
#define MODELINGCOMPONENTS_API DLLIMPORT
#define MODELINGOPERATORS_API DLLIMPORT
#define TEXTUREUTILITIESCOMMON_API DLLIMPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define PCGGEOMETRYSCRIPTINTEROP_API DLLIMPORT
#define GEOMETRYSCRIPTINGCORE_API DLLIMPORT
#define WITH_PCG_ADVANCED_FEATURES 1
#define WITH_PCG_ASYNC_PROCESSING 1
#define WITH_PCG_PERFORMANCE_MONITORING 1
#define WITH_PCG_MEMORY_OPTIMIZATION 1
#define WITH_PCG_CUSTOM_ELEMENTS 1
#define AURACRON_PCG_BRIDGE_PLATFORM_WINDOWS 1
#define WITH_PCG_MULTITHREADING 1
#define WITH_PCG_SIMD_OPTIMIZATIONS 1
#define WITH_PCG 1
#define AURACRON_PCG_BRIDGE_VERSION_MAJOR 1
#define AURACRON_PCG_BRIDGE_VERSION_MINOR 0
#define AURACRON_PCG_BRIDGE_VERSION_PATCH 0
#define AURACRONPCGBRIDGE_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
#define AURACRON_LUMEN_BRIDGE_PLATFORM_WINDOWS 1
#define WITH_LUMEN 1
#define AURACRON_LUMEN_BRIDGE_VERSION_MAJOR 1
#define AURACRON_LUMEN_BRIDGE_VERSION_MINOR 0
#define AURACRON_LUMEN_BRIDGE_VERSION_PATCH 0
#define AURACRONLUMENBRIDGE_API DLLIMPORT
#define WITH_WORLD_PARTITION_BRIDGE_WIN64 1
#define WITH_WORLD_PARTITION_BRIDGE 1
#define WITH_WORLD_PARTITION_STREAMING 1
#define WITH_WORLD_PARTITION_HLOD 1
#define WITH_WORLD_PARTITION_DATA_LAYERS 1
#define WITH_WORLD_PARTITION_RUNTIME_HASH 1
#define WITH_WORLD_PARTITION_EDITOR_HASH 1
#define WITH_WORLD_PARTITION_MINIMAP 1
#define WITH_WORLD_PARTITION_LOCATION_VOLUMES 1
#define WITH_WORLD_PARTITION_STREAMING_SOURCE 1
#define WITH_WORLD_PARTITION_GRID_PREVIEW 1
#define WITH_WORLD_PARTITION_DEBUG_VISUALIZATION 1
#define WITH_WORLD_PARTITION_COMMANDLETS 1
#define WITH_WORLD_PARTITION_BUILDER 1
#define WITH_WORLD_PARTITION_COOK_SUPPORT 1
#define WITH_WORLD_PARTITION_VALIDATION 1
#define WITH_PYTHON_BINDINGS 1
#define WITH_AURACRON_WORLD_PARTITION_EXTENSIONS 1
#define WITH_WORLD_PARTITION_BRIDGE_OPTIMIZED 1
#define WITH_WORLD_PARTITION_BRIDGE_EDITOR 1
#define AURACRONWORLDPARTITIONBRIDGE_API DLLIMPORT
#define NAVMESH_API DLLIMPORT
#define AURACRONABISMOUMBRIOBRIDGE_API DLLIMPORT
#define AURACRONADAPTIVECREATURESBRIDGE_API DLLIMPORT
#define MASSENTITY_API DLLIMPORT
#define MASSENTITYTESTSUITE_API DLLIMPORT
#define WITH_AITESTSUITE 1 1
#define AITESTSUITE_API DLLIMPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define MASSMOVEMENT_API DLLIMPORT
#define MASSCOMMON_API DLLIMPORT
#define MASSLOD_API DLLIMPORT
#define MASSSIMULATION_API DLLIMPORT
#define MASSSPAWNER_API DLLIMPORT
#define ZONEGRAPH_API DLLIMPORT
#define MASSENTITYEDITOR_API DLLIMPORT
#define COMPONENTVISUALIZERS_API DLLIMPORT
#define MASSSIGNALS_API DLLIMPORT
#define ZONEGRAPHANNOTATIONS_API DLLIMPORT
#define ZONEGRAPHDEBUG_API DLLIMPORT
#define MASSNAVIGATION_API DLLIMPORT
#define MASSREPLICATION_API DLLIMPORT
#define WITH_STATETREE_TRACE 1
#define WITH_STATETREE_TRACE_DEBUGGER 1
#define STATETREEMODULE_API DLLIMPORT
#define PROPERTYBINDINGUTILS_API DLLIMPORT
#define TRACESERVICES_API DLLIMPORT
#define CBOR_API DLLIMPORT
#define TRACEANALYSIS_API DLLIMPORT
#define GAMEPLAYSTATETREEMODULE_API DLLIMPORT
#define STRUCTUTILS_API DLLIMPORT
#define SMARTOBJECTSMODULE_API DLLIMPORT
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define WORLDCONDITIONS_API DLLIMPORT
#define TARGETINGSYSTEM_API DLLIMPORT
#define AURACRONVERTICALTRANSITIONSBRIDGE_API DLLIMPORT
#define CINEMATICCAMERA_API DLLIMPORT
#define AURACRONQABRIDGE_API DLLIMPORT
#define AUTOMATIONMESSAGES_API DLLIMPORT
#define AUTOMATIONWORKER_API DLLIMPORT
#define SCREENSHOTCOMPARISONTOOLS_API DLLIMPORT
#define AURACRON_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define WITH_METAHUMAN_DNA_CALIBRATION 1
#define AURACRONMETAHUMANBRIDGE_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define SCENEOUTLINER_API DLLIMPORT
#define WIDGETCAROUSEL_API DLLIMPORT
#define LOGVISUALIZER_API DLLIMPORT
#define WORKSPACEMENUSTRUCTURE_API DLLIMPORT
#define OUTPUTLOG_API DLLIMPORT
#define ANIMATIONBLUEPRINTLIBRARY_API DLLIMPORT
#define ANIMGRAPH_API DLLIMPORT
#define ANIMGRAPHRUNTIME_API DLLIMPORT
#define KISMETWIDGETS_API DLLIMPORT
#define SKELETALMESHEDITOR_API DLLIMPORT
#define SEQUENCERCORE_API DLLIMPORT
#define CURVEEDITOR_API DLLIMPORT
#define SEQUENCERWIDGETS_API DLLIMPORT
#define SEQUENCER_API DLLIMPORT
#define MOVIESCENETOOLS_API DLLIMPORT
#define LEVELSEQUENCEEDITOR_API DLLIMPORT
#define SEQUENCERSCRIPTING_API DLLIMPORT
#define SEQUENCERSCRIPTINGEDITOR_API DLLIMPORT
#define MOVIESCENECAPTUREDIALOG_API DLLIMPORT
#define CLOTHINGSYSTEMEDITOR_API DLLIMPORT
#define CLOTHINGSYSTEMRUNTIMECOMMON_API DLLIMPORT
#define CLOTHINGSYSTEMRUNTIMENV_API DLLIMPORT
#define CLOTHINGSYSTEMEDITORINTERFACE_API DLLIMPORT
#define CHAOSCLOTHEDITOR_API DLLIMPORT
#define HAIRSTRANDSEDITOR_API DLLIMPORT
#define TEXTUREEDITOR_API DLLIMPORT
#define LIVELINKEDITOR_API DLLIMPORT
#define LIVELINKANIMATIONCORE_API DLLIMPORT
#define LIVELINKINTERFACE_API DLLIMPORT
#define LIVELINK_API DLLIMPORT
#define LIVELINKMESSAGEBUSFRAMEWORK_API DLLIMPORT
#define SERIALIZATION_API DLLIMPORT
#define AURACRON_UE56_FEATURES 1
#define WITH_ANALYTICS 1
#define WITH_TELEMETRY 1
#define WITH_FIREBASE_ANALYTICS 1
#define WITH_EOS_ANALYTICS 1
#define WITH_UNREAL_INSIGHTS 1
#define WITH_TRACE_LOG 1
#define WITH_A_B_TESTING 1
#define WITH_BEHAVIORAL_ANALYTICS 1
#define WITH_PERFORMANCE_METRICS 1
#define WITH_BALANCE_ANALYTICS 1
#define AURACRON_ANALYTICS_DEBUG 1
#define AURACRON_DETAILED_LOGGING 1
#define AURACRON_MOBILE_ANALYTICS 0
#define WITH_MOBILE_TELEMETRY 0
#define AURACRON_GDPR_COMPLIANT 1
#define AURACRON_PRIVACY_CONTROLS 1
#define AURACRON_DATA_ANONYMIZATION 1
#define AURACRON_CONSENT_MANAGEMENT 1
#define AURACRON_SECURE_ANALYTICS 1
#define AURACRON_ENCRYPTED_TELEMETRY 1
#define AURACRON_TAMPER_DETECTION 1
#define AURACRONANALYTICSBRIDGE_API DLLIMPORT
#define ANALYTICSMULTICAST_API DLLIMPORT
#define MODULARGAMEPLAY_API DLLIMPORT
#define REPLICATIONGRAPH_API DLLIMPORT
#define ONLINESUBSYSTEMUTILS_PACKAGE 1
#define ONLINESUBSYSTEMUTILS_API DLLIMPORT
#define ONLINESUBSYSTEM_PACKAGE 1
#define DEBUG_LAN_BEACON 0
#define ONLINESUBSYSTEM_API DLLIMPORT
#define ONLINEBASE_API DLLIMPORT
#define UE_WITH_EOS_SDK_APIVERSION_WARNINGS 1
#define EOSSHARED_API DLLIMPORT
#define WITH_EOS_SDK 1
#define EOSSDK_RUNTIME_LOAD_REQUIRED 1
#define EOSSDK_RUNTIME_LIBRARY_NAME "EOSSDK-Win64-Shipping.dll"
#define WITH_METASOUNDS 1
#define WITH_AUDIO_MIXER 1
#define WITH_SYNTHESIS 1
#define WITH_SIGNAL_PROCESSING 1
#define WITH_SOUND_FIELD_RENDERING 1
#define WITH_SPATIAL_AUDIO 1
#define WITH_VOICE_CHAT 1
#define WITH_AUDIO_STREAMING 1
#define WITH_PROCEDURAL_AUDIO 1
#define AURACRON_3D_AUDIO 1
#define AURACRON_DYNAMIC_MUSIC 1
#define AURACRON_ADAPTIVE_AUDIO 1
#define AURACRON_REALM_AUDIO 1
#define AURACRON_CHAMPION_VOICES 1
#define AURACRON_ABILITY_SOUNDS 1
#define AURACRON_AMBIENT_AUDIO 1
#define AURACRON_VOICE_PROCESSING 1
#define AURACRON_MOBILE_AUDIO 0
#define AURACRON_COMPRESSED_AUDIO 0
#define AURACRON_AUDIO_DEBUG 1
#define AURACRON_AUDIO_PROFILING 1
#define AURACRON_OPTIMIZE_AUDIO 0
#define AURACRON_AUDIO_STREAMING 0
#define AURACRONAUDIOBRIDGE_API DLLIMPORT
#define SYNTHESIS_API DLLIMPORT
#define SOUNDFIELDRENDERING_API DLLIMPORT
#define WITH_METASOUND_FRONTEND 1
#define UE_METASOUND_DISABLE_5_6_NODE_REGISTRATION_DEPRECATION_WARNINGS 0
#define METASOUNDFRONTEND_API DLLIMPORT
#define METASOUNDGRAPHCORE_API DLLIMPORT
#define METASOUNDSTANDARDNODES_API DLLIMPORT
#define METASOUNDGENERATOR_API DLLIMPORT
#define WAVETABLE_API DLLIMPORT
#define WITH_NIAGARA 1
#define WITH_CASCADE 1
#define WITH_CHAOS_PHYSICS 1
#define WITH_FIELD_SYSTEM 1
#define WITH_GEOMETRY_COLLECTION 1
#define WITH_PROCEDURAL_GENERATION 1
#define WITH_DYNAMIC_MATERIALS 1
#define WITH_SHADER_COMPILATION 1
#define AURACRON_NIAGARA_VFX 1
#define AURACRON_DYNAMIC_PARTICLES 1
#define AURACRON_REALM_VFX 1
#define AURACRON_ABILITY_VFX 1
#define AURACRON_CHAMPION_VFX 1
#define AURACRON_COMBAT_VFX 1
#define AURACRON_ENVIRONMENTAL_VFX 1
#define AURACRON_WEATHER_VFX 1
#define AURACRON_DESTRUCTION_VFX 1
#define AURACRON_MOBILE_VFX 0
#define AURACRON_OPTIMIZED_PARTICLES 0
#define AURACRON_REDUCED_COMPLEXITY 0
#define AURACRON_VFX_DEBUG 1
#define AURACRON_VFX_PROFILING 1
#define AURACRON_PARTICLE_DEBUG 1
#define AURACRON_OPTIMIZE_VFX 0
#define AURACRON_VFX_CULLING 0
#define AURACRON_PARTICLE_POOLING 0
#define AURACRON_VFX_SCALABILITY 1
#define AURACRON_ADAPTIVE_QUALITY 1
#define AURACRON_LOD_PARTICLES 1
#define AURACRON_DISTANCE_CULLING 1
#define AURACRONVFXBRIDGE_API DLLIMPORT
#define NIAGARAANIMNOTIFIES_API DLLIMPORT
#define WITH_CHAOS 1
#define WITH_DESTRUCTION 1
#define WITH_CLOTH_SIMULATION 1
#define WITH_FLUID_SIMULATION 1
#define WITH_SOFT_BODY 1
#define WITH_RIGID_BODY 1
#define WITH_ADVANCED_CONSTRAINTS 1
#define WITH_VEHICLE_PHYSICS 1
#define WITH_NETWORK_PHYSICS 1
#define WITH_MATERIAL_PHYSICS 1
#define WITH_PHYSICS_ANALYTICS 1
#define WITH_MACHINE_LEARNING_CLOTH 1
#define AURACRON_CHAOS_DESTRUCTION 1
#define AURACRON_FIELD_SYSTEM 1
#define AURACRON_GEOMETRY_COLLECTION 1
#define AURACRON_DYNAMIC_PHYSICS 1
#define AURACRON_REALM_PHYSICS 1
#define AURACRON_ABILITY_PHYSICS 1
#define AURACRON_ENVIRONMENTAL_PHYSICS 1
#define AURACRON_INTERACTIVE_DESTRUCTION 1
#define AURACRON_MOBILE_PHYSICS 0
#define AURACRON_SIMPLIFIED_PHYSICS 0
#define AURACRON_PHYSICS_DEBUG 1
#define AURACRON_PHYSICS_PROFILING 1
#define AURACRON_CHAOS_DEBUG 1
#define AURACRON_OPTIMIZE_PHYSICS 0
#define AURACRON_PHYSICS_CULLING 0
#define AURACRON_ASYNC_PHYSICS 0
#define AURACRONPHYSICSBRIDGE_API DLLIMPORT
#define CHAOSVEHICLES_API DLLIMPORT
#define CHAOSVEHICLESCORE_API DLLIMPORT
#define CHAOSVEHICLESENGINE_API DLLIMPORT
#define CHAOSCLOTH_API DLLIMPORT
#define CHAOSCACHING_API DLLIMPORT
#define LEVELSEQUENCE_API DLLIMPORT
#define WITH_NANITE 1
#define WITH_VIRTUALIZED_GEOMETRY 1
#define WITH_MESH_STREAMING 1
#define WITH_LOD_STREAMING 1
#define WITH_DYNAMIC_MESH 1
#define WITH_PROCEDURAL_MESH 1
#define WITH_MESH_OPTIMIZATION 1
#define WITH_MESH_SIMPLIFICATION 1
#define AURACRON_NANITE_MESHES 1
#define AURACRON_VIRTUALIZED_GEOMETRY 1
#define AURACRON_DYNAMIC_LOD 1
#define AURACRON_MESH_STREAMING 1
#define AURACRON_PROCEDURAL_GENERATION 1
#define AURACRON_REALM_GEOMETRY 1
#define AURACRON_DESTRUCTIBLE_NANITE 1
#define AURACRON_ADAPTIVE_DETAIL 1
#define AURACRON_MOBILE_NANITE 0
#define AURACRON_FALLBACK_GEOMETRY 0
#define AURACRON_NANITE_DEBUG 1
#define AURACRON_GEOMETRY_DEBUG 1
#define AURACRON_LOD_DEBUG 1
#define AURACRON_OPTIMIZE_NANITE 0
#define AURACRON_AGGRESSIVE_CULLING 0
#define AURACRON_MESH_COMPRESSION 0
#define AURACRONNANITEBRIDGE_API DLLIMPORT
#define WITH_VOICE 1
#define WITH_WEBRTC 1
#define WITH_OPUS 1
#define WITH_AUDIO_CAPTURE 1
#define WITH_SPATIAL_VOICE 1
#define WITH_VOICE_PROCESSING 1
#define WITH_NOISE_SUPPRESSION 1
#define WITH_ECHO_CANCELLATION 1
#define AURACRON_TEAM_VOICE 1
#define AURACRON_PROXIMITY_VOICE 1
#define AURACRON_3D_VOICE 1
#define AURACRON_VOICE_EFFECTS 1
#define AURACRON_VOICE_MODULATION 1
#define AURACRON_VOICE_RECORDING 1
#define AURACRON_VOICE_PLAYBACK 1
#define AURACRON_VOICE_FILTERING 1
#define AURACRON_MOBILE_VOICE 0
#define AURACRON_COMPRESSED_VOICE 0
#define AURACRON_VOICE_DEBUG 1
#define AURACRON_VOICE_PROFILING 1
#define AURACRON_VOICE_ENCRYPTION 1
#define AURACRON_VOICE_PRIVACY 1
#define AURACRON_VOICE_MODERATION 1
#define AURACRON_VOICE_RECORDING_CONSENT 1
#define AURACRONVOICEBRIDGE_API DLLIMPORT
#define WITH_ANTI_CHEAT 1
#define WITH_SERVER_VALIDATION 1
#define WITH_CLIENT_PREDICTION 1
#define WITH_NETWORK_SECURITY 1
#define WITH_ENCRYPTION 1
#define WITH_INTEGRITY_CHECKS 1
#define WITH_BEHAVIOR_ANALYSIS 1
#define WITH_STATISTICAL_ANALYSIS 1
#define WITH_MACHINE_LEARNING 1
#define AURACRON_MOVEMENT_VALIDATION 1
#define AURACRON_ABILITY_VALIDATION 1
#define AURACRON_DAMAGE_VALIDATION 1
#define AURACRON_POSITION_VALIDATION 1
#define AURACRON_TIMING_VALIDATION 1
#define AURACRON_INPUT_VALIDATION 1
#define AURACRON_MEMORY_PROTECTION 1
#define AURACRON_PROCESS_MONITORING 1
#define AURACRON_NETWORK_MONITORING 1
#define AURACRON_SECURITY_LEVEL 2
#define AURACRON_OBFUSCATION 0
#define AURACRON_TAMPER_DETECTION 0
#define AURACRON_MOBILE_ANTICHEAT 0
#define AURACRON_DEVICE_VALIDATION 0
#define AURACRON_ANTICHEAT_DEBUG 1
#define AURACRON_SECURITY_LOGGING 1
#define AURACRONANTICHEATBRIDGE_API DLLIMPORT
#define WITH_COMMON_UI 1
#define WITH_ENHANCED_INPUT 1
#define WITH_AI_MODULE 1
#define WITH_NAVIGATION_SYSTEM 1
#define WITH_LEVEL_SEQUENCE 1
#define WITH_CINEMATIC_CAMERA 1
#define WITH_LOCALIZATION 1
#define WITH_INTERNATIONALIZATION 1
#define AURACRON_INTERACTIVE_TUTORIAL 1
#define AURACRON_AI_MENTOR 1
#define AURACRON_PROGRESSIVE_TUTORIAL 1
#define AURACRON_ADAPTIVE_TUTORIAL 1
#define AURACRON_TUTORIAL_ANALYTICS 1
#define AURACRON_TUTORIAL_LOCALIZATION 1
#define AURACRON_TUTORIAL_ACCESSIBILITY 1
#define AURACRON_TUTORIAL_CUSTOMIZATION 1
#define AURACRON_MOBILE_TUTORIAL 0
#define AURACRON_TOUCH_TUTORIAL 0
#define AURACRON_TUTORIAL_DEBUG 1
#define AURACRON_TUTORIAL_EDITOR 1
#define AURACRONTUTORIALBRIDGE_API DLLIMPORT
#define UE_COMMONINPUT_PLATFORM_TYPE PC
#define COMMONINPUT_API DLLIMPORT
#define WITH_FIREBASE 1
#define WITH_CLOUD_STORAGE 1
#define WITH_DYNAMIC_CONTENT 1
#define AURACRON_DYNAMIC_LORE 1
#define AURACRON_INTERACTIVE_LORE 1
#define AURACRON_LORE_DISCOVERY 1
#define AURACRON_LORE_PROGRESSION 1
#define AURACRON_LORE_ANALYTICS 1
#define AURACRON_LORE_LOCALIZATION 1
#define AURACRON_LORE_CINEMATICS 1
#define AURACRON_LORE_AUDIO 1
#define AURACRON_LORE_CODEX 1
#define AURACRON_MOBILE_LORE 0
#define AURACRON_COMPRESSED_LORE 0
#define AURACRON_LORE_DEBUG 1
#define AURACRON_LORE_EDITOR 1
#define AURACRONLOREBRIDGE_API DLLIMPORT
#define COMMONUI_API DLLIMPORT
#define MEDIAASSETS_API DLLIMPORT
#define MEDIA_API DLLIMPORT
#define MEDIAUTILS_API DLLIMPORT
#define WITH_ONLINE_SUBSYSTEM 1
#define WITH_PURCHASE_FLOW 1
#define WITH_STORE_INTERFACE 1
#define WITH_CROSS_PLATFORM_PURCHASES 1
#define WITH_BATTLE_PASS 1
#define WITH_COSMETICS 1
#define WITH_PREMIUM_CURRENCY 1
#define AURACRON_BATTLE_PASS 1
#define AURACRON_CHAMPION_PURCHASES 1
#define AURACRON_COSMETICS_STORE 1
#define AURACRON_PREMIUM_CURRENCY 1
#define AURACRON_SUBSCRIPTION_MODEL 1
#define AURACRON_SEASONAL_CONTENT 1
#define AURACRON_GIFTING_SYSTEM 1
#define AURACRON_REFERRAL_SYSTEM 1
#define AURACRON_STEAM_STORE 1
#define AURACRON_EPIC_GAMES_STORE 1
#define AURACRON_MONETIZATION_DEBUG 1
#define AURACRON_PURCHASE_TESTING 1
#define AURACRON_PURCHASE_VALIDATION 1
#define AURACRON_RECEIPT_VERIFICATION 1
#define AURACRON_FRAUD_DETECTION 1
#define AURACRON_GDPR_COMPLIANCE 1
#define AURACRON_COPPA_COMPLIANCE 1
#define AURACRONMONETIZATIONBRIDGE_API DLLIMPORT
#define WITH_EOS 1
#define WITH_ONLINE_SUBSYSTEM_EOS 1
#define WITH_EOS_VOICE 1
#define WITH_EOS_SESSIONS 1
#define WITH_EOS_ACHIEVEMENTS 1
#define WITH_EOS_LEADERBOARDS 1
#define WITH_EOS_STATS 1
#define WITH_EOS_FRIENDS 1
#define WITH_EOS_PRESENCE 1
#define AURACRON_EOS_AUTHENTICATION 1
#define AURACRON_EOS_MATCHMAKING 1
#define AURACRON_EOS_LOBBIES 1
#define AURACRON_EOS_SESSIONS 1
#define AURACRON_EOS_VOICE_CHAT 1
#define AURACRON_EOS_ACHIEVEMENTS 1
#define AURACRON_EOS_LEADERBOARDS 1
#define AURACRON_EOS_PLAYER_DATA 1
#define AURACRON_EOS_FRIENDS 1
#define AURACRON_EOS_PRESENCE 1
#define AURACRON_EOS_METRICS 1
#define AURACRON_EOS_ANTI_CHEAT 1
#define AURACRON_EOS_SANCTIONS 1
#define AURACRON_EOS_REPORTS 1
#define AURACRON_CROSS_PLATFORM_PLAY 1
#define AURACRON_CROSS_PLATFORM_PROGRESSION 1
#define AURACRON_CROSS_PLATFORM_FRIENDS 1
#define AURACRON_CROSS_PLATFORM_VOICE 1
#define AURACRON_MOBILE_EOS 0
#define AURACRON_EOS_DEBUG 1
#define AURACRON_EOS_LOGGING 1
#define AURACRON_EOS_ENCRYPTION 1
#define AURACRON_EOS_GDPR 1
#define AURACRON_EOS_COPPA 1
#define AURACRONEOSBRIDGE_API DLLIMPORT
#define AURACRON_COMBAT_DEBUG 1
#define AURACRON_COMBAT_PROFILING 1
#define AURACRON_MOBILE_PLATFORM 0
#define WITH_GAMEPLAY_ABILITY_SYSTEM 1
#define WITH_BEHAVIOR_TREE 1
#define WITH_STATE_TREE 1
#define WITH_MASS_ENTITY 1
#define WITH_ADVANCED_DESTRUCTION 1
#define WITH_ELEMENTAL_SYSTEM 1
#define WITH_COMBO_SYSTEM 1
#define AURACRONCOMBATBRIDGE_API DLLIMPORT
#define AURACRON_CHAMPIONS_DEBUG 1
#define AURACRON_CHAMPIONS_PROFILING 1
#define WITH_METAHUMAN_SDK 1
#define WITH_CONTROL_RIG 1
#define WITH_IK_RIG 1
#define AURACRONCHAMPIONSBRIDGE_API DLLIMPORT
#define CONTROLRIG_API DLLIMPORT
#define RIGVM_API DLLIMPORT
#define WITH_RIGVMLEGACYEDITOR 1
#define RIGVMDEVELOPER_API DLLIMPORT
#define VISUALGRAPHUTILS_API DLLIMPORT
#define IKRIG_API DLLIMPORT
#define PBIK_API DLLIMPORT
#define CONTROLRIGDEVELOPER_API DLLIMPORT
#define ANIMATIONEDITORWIDGETS_API DLLIMPORT
#define CONTROLRIGEDITOR_API DLLIMPORT
#define APPFRAMEWORK_API DLLIMPORT
#define RIGVMEDITOR_API DLLIMPORT
#define MESSAGELOG_API DLLIMPORT
#define STRUCTUTILSEDITOR_API DLLIMPORT
#define TWEENINGUTILSEDITOR_API DLLIMPORT
#define TWEENINGUTILS_API DLLIMPORT
#define AURACRON_NETWORKING_DEBUG 1
#define AURACRON_NETWORKING_PROFILING 1
#define AURACRON_CHEAT_DETECTION 1
#define WITH_REPLICATION_GRAPH 1
#define WITH_IRIS_NETWORKING 1
#define WITH_NETWORK_PREDICTION 1
#define WITH_REPLAY_SYSTEM 1
#define AURACRON_SERVER_AUTHORITATIVE 1
#define AURACRON_CLIENT_PREDICTION 1
#define AURACRON_SECURE_NETWORKING 1
#define AURACRON_OPTIMIZE_NETWORKING 0
#define AURACRON_COMPRESS_PACKETS 0
#define AURACRON_CROSS_PLATFORM 1
#define AURACRON_CROSS_PROGRESSION 1
#define AURACRON_CROSS_PLAY 1
#define AURACRONNETWORKINGBRIDGE_API DLLIMPORT
#define ONLINESUBSYSTEMEOS_API DLLIMPORT
#define NETWORKPREDICTION_API DLLIMPORT
#define AURACRON_PROGRESSION_DEBUG 1
#define AURACRON_PROGRESSION_PROFILING 1
#define AURACRON_ACCOUNT_PROGRESSION 1
#define AURACRON_CHAMPION_MASTERY 1
#define AURACRON_REALM_MASTERY 1
#define AURACRON_SEASONAL_PROGRESSION 1
#define AURACRON_ACHIEVEMENTS 1
#define AURACRON_LEADERBOARDS 1
#define AURACRON_SECURE_PROGRESSION 1
#define AURACRON_ENCRYPTED_SAVES 1
#define AURACRON_CLOUD_SYNC 1
#define AURACRON_BACKUP_SYSTEM 1
#define AURACRON_CLOUD_SAVE 1
#define AURACRON_OPTIMIZE_PROGRESSION 0
#define AURACRON_CACHE_PROGRESSION 0
#define AURACRON_MONETIZATION_INTEGRATION 1
#define AURACRON_PREMIUM_REWARDS 1
#define AURACRON_SUBSCRIPTION_SYSTEM 1
#define AURACRONPROGRESSIONBRIDGE_API DLLIMPORT
#define AURACRON_REALMS_DEBUG 1
#define AURACRON_REALMS_PROFILING 1
#define WITH_WORLD_PARTITION 1
#define AURACRONREALMSBRIDGE_API DLLIMPORT
#define AURACRON_SIGILOS_DEBUG 1
#define AURACRON_SIGILOS_PROFILING 1
#define AURACRONSIGILOSBRIDGE_API DLLIMPORT
#define AURACRON_UI_DEBUG 1
#define AURACRON_UI_PROFILING 1
#define AURACRON_TOUCH_UI 0
#define WITH_COMMON_INPUT 1
#define WITH_SLATE_DEBUGGING 1
#define WITH_UMG_DEBUGGING 1
#define AURACRON_3D_MINIMAP 1
#define AURACRON_ADAPTIVE_UI 1
#define AURACRON_CROSS_PLATFORM_UI 1
#define AURACRON_ACCESSIBILITY_SUPPORT 1
#define AURACRON_DYNAMIC_SCALING 1
#define AURACRON_TOUCH_GESTURES 1
#define AURACRON_OPTIMIZE_UI 0
#define AURACRON_CACHE_UI_ELEMENTS 0
#define AURACRON_SCREEN_READER_SUPPORT 1
#define AURACRON_COLOR_BLIND_SUPPORT 1
#define AURACRON_HIGH_CONTRAST_MODE 1
#define AURACRON_MULTI_LANGUAGE 1
#define AURACRONUIBRIDGE_API DLLIMPORT
#define AURACRON_METAHUMAN_OPTIMIZED 1
#define AURACRON_METAHUMAN_THREADING 1
#define AURACRON_METAHUMAN_GPU_ACCELERATION 1
#define AURACRONMETAHUMANFRAMEWORK_API DLLIMPORT
#define RIGLOGICMODULE_API DLLIMPORT
#define RL_SHARED 1
#define RIGLOGICLIB_API DLLIMPORT
#define SKELETALMESHUTILITIESCOMMON_API DLLIMPORT
#define EDITORINTERACTIVETOOLSFRAMEWORK_API DLLIMPORT
