// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronSigilosBridge.h"

#ifdef AURACRONSIGILOSBRIDGE_AuracronSigilosBridge_generated_h
#error "AuracronSigilosBridge.generated.h already included, missing '#pragma once' in AuracronSigilosBridge.h"
#endif
#define AURACRONSIGILOSBRIDGE_AuracronSigilosBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UGameplayAbility;
enum class EAuracronAegisSigilType : uint8;
enum class EAuracronRuinSigilType : uint8;
enum class EAuracronSigiloFusionState : uint8;
enum class EAuracronSigiloType : uint8;
enum class EAuracronVesperSigilType : uint8;
enum class ESigilSystemErrorSeverity : uint8;
struct FAuracronSigilArchetype;
struct FAuracronSigiloConfiguration;
struct FSigilDynamicBalanceModifiers;
struct FSigilSystemError;
struct FSigilSystemPerformanceMetrics;
struct FSigilSystemTelemetryData;
struct FSigilUsageAnalytics;

// ********** Begin ScriptStruct FAuracronEquippedSigil ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_140_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronEquippedSigil_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronEquippedSigil;
// ********** End ScriptStruct FAuracronEquippedSigil **********************************************

// ********** Begin ScriptStruct FAuracronSigilArchetype *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_181_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigilArchetype_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigilArchetype;
// ********** End ScriptStruct FAuracronSigilArchetype *********************************************

// ********** Begin ScriptStruct FAuracronSigiloProperties *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_256_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloProperties_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloProperties;
// ********** End ScriptStruct FAuracronSigiloProperties *******************************************

// ********** Begin ScriptStruct FAuracronSigiloPassiveBonuses *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_309_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloPassiveBonuses_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloPassiveBonuses;
// ********** End ScriptStruct FAuracronSigiloPassiveBonuses ***************************************

// ********** Begin ScriptStruct FAuracronSigiloExclusiveAbility ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_350_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloExclusiveAbility_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloExclusiveAbility;
// ********** End ScriptStruct FAuracronSigiloExclusiveAbility *************************************

// ********** Begin ScriptStruct FAuracronSigiloVisualEffects **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_399_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloVisualEffects_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloVisualEffects;
// ********** End ScriptStruct FAuracronSigiloVisualEffects ****************************************

// ********** Begin ScriptStruct FAuracronSigiloAudioEffects ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_440_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloAudioEffects_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloAudioEffects;
// ********** End ScriptStruct FAuracronSigiloAudioEffects *****************************************

// ********** Begin ScriptStruct FAuracronSigiloConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_469_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloConfiguration;
// ********** End ScriptStruct FAuracronSigiloConfiguration ****************************************

// ********** Begin ScriptStruct FSigilProgressionSaveData *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_510_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilProgressionSaveData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilProgressionSaveData;
// ********** End ScriptStruct FSigilProgressionSaveData *******************************************

// ********** Begin ScriptStruct FSigilSystemPerformanceMetrics ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_548_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSystemPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSystemPerformanceMetrics;
// ********** End ScriptStruct FSigilSystemPerformanceMetrics **************************************

// ********** Begin ScriptStruct FSigilUsageAnalytics **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_597_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilUsageAnalytics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilUsageAnalytics;
// ********** End ScriptStruct FSigilUsageAnalytics ************************************************

// ********** Begin ScriptStruct FSigilDynamicBalanceModifiers *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_638_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilDynamicBalanceModifiers_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilDynamicBalanceModifiers;
// ********** End ScriptStruct FSigilDynamicBalanceModifiers ***************************************

// ********** Begin ScriptStruct FCombatPerformanceData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_694_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCombatPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FCombatPerformanceData;
// ********** End ScriptStruct FCombatPerformanceData **********************************************

// ********** Begin ScriptStruct FSigilSystemError *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_732_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSystemError_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSystemError;
// ********** End ScriptStruct FSigilSystemError ***************************************************

// ********** Begin ScriptStruct FSigilTelemetrySnapshot *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_758_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilTelemetrySnapshot_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilTelemetrySnapshot;
// ********** End ScriptStruct FSigilTelemetrySnapshot *********************************************

// ********** Begin ScriptStruct FSigilSystemAdvancedTelemetryData *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_844_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSystemAdvancedTelemetryData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSystemAdvancedTelemetryData;
// ********** End ScriptStruct FSigilSystemAdvancedTelemetryData ***********************************

// ********** Begin ScriptStruct FSigilSystemTelemetryData *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_881_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilSystemTelemetryData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilSystemTelemetryData;
// ********** End ScriptStruct FSigilSystemTelemetryData *******************************************

// ********** Begin ScriptStruct FAuracronSigiloConfigurationEntry *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_918_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSigiloConfigurationEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSigiloConfigurationEntry;
// ********** End ScriptStruct FAuracronSigiloConfigurationEntry ***********************************

// ********** Begin Delegate FOnSigiloSelected *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1767_DELEGATE \
static void FOnSigiloSelected_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloSelected, EAuracronSigiloType SigiloType);


// ********** End Delegate FOnSigiloSelected *******************************************************

// ********** Begin Delegate FOnFusionStarted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1772_DELEGATE \
static void FOnFusionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionStarted);


// ********** End Delegate FOnFusionStarted ********************************************************

// ********** Begin Delegate FOnFusionCompleted ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1777_DELEGATE \
static void FOnFusionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnFusionCompleted, EAuracronSigiloType SigiloType);


// ********** End Delegate FOnFusionCompleted ******************************************************

// ********** Begin Delegate FOnSigiloReforged *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1782_DELEGATE \
static void FOnSigiloReforged_DelegateWrapper(const FMulticastScriptDelegate& OnSigiloReforged, EAuracronSigiloType OldSigilo, EAuracronSigiloType NewSigilo);


// ********** End Delegate FOnSigiloReforged *******************************************************

// ********** Begin Delegate FOnSigilEquipped ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1789_DELEGATE \
static void FOnSigilEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnSigilEquipped, EAuracronSigiloType SigiloType, int32 SubtypeIndex, int32 Level);


// ********** End Delegate FOnSigilEquipped ********************************************************

// ********** Begin Delegate FOnSigilActivated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1794_DELEGATE \
static void FOnSigilActivated_DelegateWrapper(const FMulticastScriptDelegate& OnSigilActivated, EAuracronSigiloType SigiloType, float Duration);


// ********** End Delegate FOnSigilActivated *******************************************************

// ********** Begin Delegate FOnArchetypeFormed ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1799_DELEGATE \
static void FOnArchetypeFormed_DelegateWrapper(const FMulticastScriptDelegate& OnArchetypeFormed, FAuracronSigilArchetype const& Archetype);


// ********** End Delegate FOnArchetypeFormed ******************************************************

// ********** Begin Delegate FOnFusion20Activated **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1804_DELEGATE \
static void FOnFusion20Activated_DelegateWrapper(const FMulticastScriptDelegate& OnFusion20Activated, FAuracronSigilArchetype const& Archetype, float Duration);


// ********** End Delegate FOnFusion20Activated ****************************************************

// ********** Begin Delegate FOnFusion20Ended ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1809_DELEGATE \
static void FOnFusion20Ended_DelegateWrapper(const FMulticastScriptDelegate& OnFusion20Ended, FAuracronSigilArchetype const& Archetype);


// ********** End Delegate FOnFusion20Ended ********************************************************

// ********** Begin Delegate FOnSigilExperienceGained **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1814_DELEGATE \
static void FOnSigilExperienceGained_DelegateWrapper(const FMulticastScriptDelegate& OnSigilExperienceGained, EAuracronSigiloType SigiloType, int32 ExperienceGained, int32 NewLevel);


// ********** End Delegate FOnSigilExperienceGained ************************************************

// ********** Begin Delegate FOnSigilLevelUp *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1819_DELEGATE \
static void FOnSigilLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnSigilLevelUp, EAuracronSigiloType SigiloType, int32 NewLevel, int32 TotalExperience);


// ********** End Delegate FOnSigilLevelUp *********************************************************

// ********** Begin Delegate FOnAegisSigilCooldownComplete *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1824_DELEGATE \
static void FOnAegisSigilCooldownComplete_DelegateWrapper(const FMulticastScriptDelegate& OnAegisSigilCooldownComplete);


// ********** End Delegate FOnAegisSigilCooldownComplete *******************************************

// ********** Begin Delegate FOnRuinSigilCooldownComplete ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1828_DELEGATE \
static void FOnRuinSigilCooldownComplete_DelegateWrapper(const FMulticastScriptDelegate& OnRuinSigilCooldownComplete);


// ********** End Delegate FOnRuinSigilCooldownComplete ********************************************

// ********** Begin Delegate FOnVesperSigilCooldownComplete ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1832_DELEGATE \
static void FOnVesperSigilCooldownComplete_DelegateWrapper(const FMulticastScriptDelegate& OnVesperSigilCooldownComplete);


// ********** End Delegate FOnVesperSigilCooldownComplete ******************************************

// ********** Begin Delegate FOnFusion20CooldownComplete *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1836_DELEGATE \
static void FOnFusion20CooldownComplete_DelegateWrapper(const FMulticastScriptDelegate& OnFusion20CooldownComplete);


// ********** End Delegate FOnFusion20CooldownComplete *********************************************

// ********** Begin Delegate FOnCriticalAssetsLoadComplete *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1841_DELEGATE \
static void FOnCriticalAssetsLoadComplete_DelegateWrapper(const FMulticastScriptDelegate& OnCriticalAssetsLoadComplete);


// ********** End Delegate FOnCriticalAssetsLoadComplete *******************************************

// ********** Begin Delegate FOnSystemFullyInitialized *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_1846_DELEGATE \
static void FOnSystemFullyInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnSystemFullyInitialized);


// ********** End Delegate FOnSystemFullyInitialized ***********************************************

// ********** Begin Class UAuracronSigilosBridge ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_Fusion20Active); \
	DECLARE_FUNCTION(execOnRep_CurrentArchetype); \
	DECLARE_FUNCTION(execOnRep_EquippedSigils); \
	DECLARE_FUNCTION(execOnRep_FusionState); \
	DECLARE_FUNCTION(execOnRep_SelectedSigiloType); \
	DECLARE_FUNCTION(execIsSystemFullyInitialized); \
	DECLARE_FUNCTION(execFinalizeSystemInitialization); \
	DECLARE_FUNCTION(execIntegrateWithGameMode); \
	DECLARE_FUNCTION(execGetTelemetryData); \
	DECLARE_FUNCTION(execSetTelemetryEndpoint); \
	DECLARE_FUNCTION(execEnableTelemetry); \
	DECLARE_FUNCTION(execInitializeTelemetrySystem); \
	DECLARE_FUNCTION(execAreCriticalAssetsLoaded); \
	DECLARE_FUNCTION(execUnloadUnusedAssets); \
	DECLARE_FUNCTION(execPreloadCriticalAssets); \
	DECLARE_FUNCTION(execGetSystemErrorCount); \
	DECLARE_FUNCTION(execGetSystemErrorHistory); \
	DECLARE_FUNCTION(execHandleSystemError); \
	DECLARE_FUNCTION(execOptimizeNetworkReplication); \
	DECLARE_FUNCTION(execIsInCombat); \
	DECLARE_FUNCTION(execOnCombatEnd); \
	DECLARE_FUNCTION(execOnCombatStart); \
	DECLARE_FUNCTION(execRunDiagnostics); \
	DECLARE_FUNCTION(execValidateSystemIntegrity); \
	DECLARE_FUNCTION(execGetOverallMastery); \
	DECLARE_FUNCTION(execGetSigilMastery); \
	DECLARE_FUNCTION(execUpdateSigilMastery); \
	DECLARE_FUNCTION(execResetDynamicBalance); \
	DECLARE_FUNCTION(execGetDynamicBalanceModifiers); \
	DECLARE_FUNCTION(execGetCurrentUsageAnalytics); \
	DECLARE_FUNCTION(execAnalyzeUsagePatterns); \
	DECLARE_FUNCTION(execGetPerformanceMetrics); \
	DECLARE_FUNCTION(execStopPerformanceMonitoring); \
	DECLARE_FUNCTION(execStartPerformanceMonitoring); \
	DECLARE_FUNCTION(execLoadSigilProgression); \
	DECLARE_FUNCTION(execSaveSigilProgression); \
	DECLARE_FUNCTION(execIsLegendaryArchetypeCombination); \
	DECLARE_FUNCTION(execCalculateArchetypeRequiredLevel); \
	DECLARE_FUNCTION(execGenerateArchetype); \
	DECLARE_FUNCTION(execAddSigilExperience); \
	DECLARE_FUNCTION(execGetExperienceRequiredForLevel); \
	DECLARE_FUNCTION(execCalculateSigilLevel); \
	DECLARE_FUNCTION(execLoadDefaultSigiloConfigurations); \
	DECLARE_FUNCTION(execSetSigiloConfiguration); \
	DECLARE_FUNCTION(execGetSigiloConfiguration); \
	DECLARE_FUNCTION(execCanReforge); \
	DECLARE_FUNCTION(execGetReforgeCooldownRemaining); \
	DECLARE_FUNCTION(execGetTimeToFusion); \
	DECLARE_FUNCTION(execGetSelectedSigilo); \
	DECLARE_FUNCTION(execGetFusionState); \
	DECLARE_FUNCTION(execRemovePassiveBonuses); \
	DECLARE_FUNCTION(execApplyPassiveBonuses); \
	DECLARE_FUNCTION(execGetAlternativeAbilityTree); \
	DECLARE_FUNCTION(execActivateExclusiveAbility); \
	DECLARE_FUNCTION(execGenerateArchetypeName); \
	DECLARE_FUNCTION(execGetAvailableArchetypes); \
	DECLARE_FUNCTION(execCanActivateFusion20); \
	DECLARE_FUNCTION(execGetCurrentArchetype); \
	DECLARE_FUNCTION(execActivateFusion20); \
	DECLARE_FUNCTION(execActivateVesperSigil); \
	DECLARE_FUNCTION(execActivateRuinSigil); \
	DECLARE_FUNCTION(execActivateAegisSigil); \
	DECLARE_FUNCTION(execEquipVesperSigil); \
	DECLARE_FUNCTION(execEquipRuinSigil); \
	DECLARE_FUNCTION(execEquipAegisSigil); \
	DECLARE_FUNCTION(execCancelSigiloFusion); \
	DECLARE_FUNCTION(execReforgeSigilo); \
	DECLARE_FUNCTION(execCompleteSigiloFusion); \
	DECLARE_FUNCTION(execStartSigiloFusion); \
	DECLARE_FUNCTION(execSelectSigilo);


AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronSigilosBridge(); \
	friend struct Z_Construct_UClass_UAuracronSigilosBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilosBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronSigilosBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronSigilosBridge"), Z_Construct_UClass_UAuracronSigilosBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronSigilosBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SigiloConfigurations=NETFIELD_REP_START, \
		SelectedSigiloType, \
		CurrentFusionState, \
		FusionStartTime, \
		LastReforgeTime, \
		EquippedAegisSigil, \
		EquippedRuinSigil, \
		EquippedVesperSigil, \
		CurrentArchetype, \
		bFusion20Active, \
		Fusion20StartTime, \
		LastFusion20Time, \
		CompressedSigilState, \
		CompressedAegisCooldown, \
		CompressedRuinCooldown, \
		CompressedVesperCooldown, \
		CompressedFusion20Cooldown, \
		NETFIELD_REP_END=CompressedFusion20Cooldown	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronSigilosBridge(UAuracronSigilosBridge&&) = delete; \
	UAuracronSigilosBridge(const UAuracronSigilosBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronSigilosBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronSigilosBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronSigilosBridge) \
	NO_API virtual ~UAuracronSigilosBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_969_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h_972_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronSigilosBridge;

// ********** End Class UAuracronSigilosBridge *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilosBridge_h

// ********** Begin Enum EAuracronSigiloType *******************************************************
#define FOREACH_ENUM_EAURACRONSIGILOTYPE(op) \
	op(EAuracronSigiloType::None) \
	op(EAuracronSigiloType::Aegis) \
	op(EAuracronSigiloType::Ruin) \
	op(EAuracronSigiloType::Vesper) 

enum class EAuracronSigiloType : uint8;
template<> struct TIsUEnumClass<EAuracronSigiloType> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloType>();
// ********** End Enum EAuracronSigiloType *********************************************************

// ********** Begin Enum EAuracronAegisSigilType ***************************************************
#define FOREACH_ENUM_EAURACRONAEGISSIGILTYPE(op) \
	op(EAuracronAegisSigilType::None) \
	op(EAuracronAegisSigilType::Primordial) \
	op(EAuracronAegisSigilType::Cristalino) \
	op(EAuracronAegisSigilType::Temporal) \
	op(EAuracronAegisSigilType::Espectral) \
	op(EAuracronAegisSigilType::Absoluto) 

enum class EAuracronAegisSigilType : uint8;
template<> struct TIsUEnumClass<EAuracronAegisSigilType> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronAegisSigilType>();
// ********** End Enum EAuracronAegisSigilType *****************************************************

// ********** Begin Enum EAuracronRuinSigilType ****************************************************
#define FOREACH_ENUM_EAURACRONRUINSIGILTYPE(op) \
	op(EAuracronRuinSigilType::None) \
	op(EAuracronRuinSigilType::Flamejante) \
	op(EAuracronRuinSigilType::Gelido) \
	op(EAuracronRuinSigilType::Sombrio) \
	op(EAuracronRuinSigilType::Corrosivo) \
	op(EAuracronRuinSigilType::Aniquilador) 

enum class EAuracronRuinSigilType : uint8;
template<> struct TIsUEnumClass<EAuracronRuinSigilType> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronRuinSigilType>();
// ********** End Enum EAuracronRuinSigilType ******************************************************

// ********** Begin Enum EAuracronVesperSigilType **************************************************
#define FOREACH_ENUM_EAURACRONVESPERSIGILTYPE(op) \
	op(EAuracronVesperSigilType::None) \
	op(EAuracronVesperSigilType::Curativo) \
	op(EAuracronVesperSigilType::Energetico) \
	op(EAuracronVesperSigilType::Velocidade) \
	op(EAuracronVesperSigilType::Visao) \
	op(EAuracronVesperSigilType::Teleporte) \
	op(EAuracronVesperSigilType::Temporal) 

enum class EAuracronVesperSigilType : uint8;
template<> struct TIsUEnumClass<EAuracronVesperSigilType> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronVesperSigilType>();
// ********** End Enum EAuracronVesperSigilType ****************************************************

// ********** Begin Enum EAuracronSigiloFusionState ************************************************
#define FOREACH_ENUM_EAURACRONSIGILOFUSIONSTATE(op) \
	op(EAuracronSigiloFusionState::Inactive) \
	op(EAuracronSigiloFusionState::Charging) \
	op(EAuracronSigiloFusionState::Active) \
	op(EAuracronSigiloFusionState::Cooldown) \
	op(EAuracronSigiloFusionState::Reforging) 

enum class EAuracronSigiloFusionState : uint8;
template<> struct TIsUEnumClass<EAuracronSigiloFusionState> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<EAuracronSigiloFusionState>();
// ********** End Enum EAuracronSigiloFusionState **************************************************

// ********** Begin Enum ESigilSystemErrorSeverity *************************************************
#define FOREACH_ENUM_ESIGILSYSTEMERRORSEVERITY(op) \
	op(ESigilSystemErrorSeverity::Warning) \
	op(ESigilSystemErrorSeverity::Error) \
	op(ESigilSystemErrorSeverity::Critical) 

enum class ESigilSystemErrorSeverity : uint8;
template<> struct TIsUEnumClass<ESigilSystemErrorSeverity> { enum { Value = true }; };
template<> AURACRONSIGILOSBRIDGE_API UEnum* StaticEnum<ESigilSystemErrorSeverity>();
// ********** End Enum ESigilSystemErrorSeverity ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
