// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronCombatBridgeSettings.h"
#include "AuracronCombatBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronCombatBridgeSettings() {}

// ********** Begin Cross Module References ********************************************************
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAICombatConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronElementalDamageConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig();
DEVELOPERSETTINGS_API UClass* Z_Construct_UClass_UDeveloperSettings();
UPackage* Z_Construct_UPackage__Script_AuracronCombatBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronCombatBridgeSettings Function AreAdvancedCombatFeaturesEnabled **
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics
{
	struct AuracronCombatBridgeSettings_eventAreAdvancedCombatFeaturesEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if advanced combat features are enabled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if advanced combat features are enabled" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridgeSettings_eventAreAdvancedCombatFeaturesEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridgeSettings_eventAreAdvancedCombatFeaturesEnabled_Parms), &Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "AreAdvancedCombatFeaturesEnabled", Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::AuracronCombatBridgeSettings_eventAreAdvancedCombatFeaturesEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::AuracronCombatBridgeSettings_eventAreAdvancedCombatFeaturesEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execAreAdvancedCombatFeaturesEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UAuracronCombatBridgeSettings::AreAdvancedCombatFeaturesEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function AreAdvancedCombatFeaturesEnabled ****

// ********** Begin Class UAuracronCombatBridgeSettings Function GetCombatBridgeSettings ***********
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics
{
	struct AuracronCombatBridgeSettings_eventGetCombatBridgeSettings_Parms
	{
		const UAuracronCombatBridgeSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the combat bridge settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the combat bridge settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000582, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetCombatBridgeSettings_Parms, ReturnValue), Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetCombatBridgeSettings", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::AuracronCombatBridgeSettings_eventGetCombatBridgeSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::AuracronCombatBridgeSettings_eventGetCombatBridgeSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetCombatBridgeSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(const UAuracronCombatBridgeSettings**)Z_Param__Result=UAuracronCombatBridgeSettings::GetCombatBridgeSettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetCombatBridgeSettings *************

// ********** Begin Class UAuracronCombatBridgeSettings Function GetDefaultAdvancedDestructionConfig 
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics
{
	struct AuracronCombatBridgeSettings_eventGetDefaultAdvancedDestructionConfig_Parms
	{
		FAuracronAdvancedDestructionConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get default advanced destruction configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get default advanced destruction configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetDefaultAdvancedDestructionConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, METADATA_PARAMS(0, nullptr) }; // 886306391
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetDefaultAdvancedDestructionConfig", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultAdvancedDestructionConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultAdvancedDestructionConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetDefaultAdvancedDestructionConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAdvancedDestructionConfig*)Z_Param__Result=UAuracronCombatBridgeSettings::GetDefaultAdvancedDestructionConfig();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetDefaultAdvancedDestructionConfig *

// ********** Begin Class UAuracronCombatBridgeSettings Function GetDefaultAICombatConfig **********
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics
{
	struct AuracronCombatBridgeSettings_eventGetDefaultAICombatConfig_Parms
	{
		FAuracronAICombatConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get default AI combat configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get default AI combat configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetDefaultAICombatConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAICombatConfig, METADATA_PARAMS(0, nullptr) }; // 517528943
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetDefaultAICombatConfig", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultAICombatConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultAICombatConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetDefaultAICombatConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAICombatConfig*)Z_Param__Result=UAuracronCombatBridgeSettings::GetDefaultAICombatConfig();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetDefaultAICombatConfig ************

// ********** Begin Class UAuracronCombatBridgeSettings Function GetDefaultElementalDamageConfig ***
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics
{
	struct AuracronCombatBridgeSettings_eventGetDefaultElementalDamageConfig_Parms
	{
		FAuracronElementalDamageConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get default elemental damage configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get default elemental damage configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetDefaultElementalDamageConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, METADATA_PARAMS(0, nullptr) }; // 1909653023
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetDefaultElementalDamageConfig", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultElementalDamageConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultElementalDamageConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetDefaultElementalDamageConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronElementalDamageConfig*)Z_Param__Result=UAuracronCombatBridgeSettings::GetDefaultElementalDamageConfig();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetDefaultElementalDamageConfig *****

// ********** Begin Class UAuracronCombatBridgeSettings Function GetDefaultEnhancedInputConfig *****
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics
{
	struct AuracronCombatBridgeSettings_eventGetDefaultEnhancedInputConfig_Parms
	{
		FAuracronEnhancedInputConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get default enhanced input configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get default enhanced input configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetDefaultEnhancedInputConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, METADATA_PARAMS(0, nullptr) }; // 2243282029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetDefaultEnhancedInputConfig", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultEnhancedInputConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::AuracronCombatBridgeSettings_eventGetDefaultEnhancedInputConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetDefaultEnhancedInputConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronEnhancedInputConfig*)Z_Param__Result=UAuracronCombatBridgeSettings::GetDefaultEnhancedInputConfig();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetDefaultEnhancedInputConfig *******

// ********** Begin Class UAuracronCombatBridgeSettings Function GetGlobalCombatTickInterval *******
struct Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics
{
	struct AuracronCombatBridgeSettings_eventGetGlobalCombatTickInterval_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get global combat tick interval */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get global combat tick interval" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridgeSettings_eventGetGlobalCombatTickInterval_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridgeSettings, nullptr, "GetGlobalCombatTickInterval", Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::AuracronCombatBridgeSettings_eventGetGlobalCombatTickInterval_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::AuracronCombatBridgeSettings_eventGetGlobalCombatTickInterval_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridgeSettings::execGetGlobalCombatTickInterval)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UAuracronCombatBridgeSettings::GetGlobalCombatTickInterval();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridgeSettings Function GetGlobalCombatTickInterval *********

// ********** Begin Class UAuracronCombatBridgeSettings ********************************************
void UAuracronCombatBridgeSettings::StaticRegisterNativesUAuracronCombatBridgeSettings()
{
	UClass* Class = UAuracronCombatBridgeSettings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AreAdvancedCombatFeaturesEnabled", &UAuracronCombatBridgeSettings::execAreAdvancedCombatFeaturesEnabled },
		{ "GetCombatBridgeSettings", &UAuracronCombatBridgeSettings::execGetCombatBridgeSettings },
		{ "GetDefaultAdvancedDestructionConfig", &UAuracronCombatBridgeSettings::execGetDefaultAdvancedDestructionConfig },
		{ "GetDefaultAICombatConfig", &UAuracronCombatBridgeSettings::execGetDefaultAICombatConfig },
		{ "GetDefaultElementalDamageConfig", &UAuracronCombatBridgeSettings::execGetDefaultElementalDamageConfig },
		{ "GetDefaultEnhancedInputConfig", &UAuracronCombatBridgeSettings::execGetDefaultEnhancedInputConfig },
		{ "GetGlobalCombatTickInterval", &UAuracronCombatBridgeSettings::execGetGlobalCombatTickInterval },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronCombatBridgeSettings;
UClass* UAuracronCombatBridgeSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronCombatBridgeSettings;
	if (!Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronCombatBridgeSettings"),
			Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.InnerSingleton,
			StaticRegisterNativesUAuracronCombatBridgeSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister()
{
	return UAuracronCombatBridgeSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Project settings for AURACRON Combat Bridge\n * Allows configuration of default values and global settings\n */" },
#endif
		{ "DisplayName", "AURACRON Combat Bridge" },
		{ "IncludePath", "AuracronCombatBridgeSettings.h" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Project settings for AURACRON Combat Bridge\nAllows configuration of default values and global settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdvancedCombatFeatures_MetaData[] = {
		{ "Category", "Global Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable advanced combat features globally */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable advanced combat features globally" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Global Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable debug logging for combat systems */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable debug logging for combat systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceProfiling_MetaData[] = {
		{ "Category", "Global Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance profiling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance profiling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalCombatTickInterval_MetaData[] = {
		{ "Category", "Global Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.01" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global combat tick interval */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global combat tick interval" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultEnhancedInputConfig_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default Enhanced Input configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default Enhanced Input configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEnhancedInputByDefault_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Enhanced Input system by default */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Enhanced Input system by default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalComboWindowMultiplier_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global combo window multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global combo window multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAICombatConfig_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default AI Combat configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default AI Combat configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAILearningByDefault_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable AI learning by default */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable AI learning by default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalAIReactionTimeMultiplier_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global AI reaction time multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global AI reaction time multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAILearningDataEntries_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum AI learning data entries */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum AI learning data entries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultElementalDamageConfig_MetaData[] = {
		{ "Category", "Elemental System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default Elemental Damage configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default Elemental Damage configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableElementalInteractionsByDefault_MetaData[] = {
		{ "Category", "Elemental System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable elemental interactions by default */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable elemental interactions by default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalElementalDamageMultiplier_MetaData[] = {
		{ "Category", "Elemental System" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global elemental damage multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global elemental damage multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveElementalEffectsPerActor_MetaData[] = {
		{ "Category", "Elemental System" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum active elemental effects per actor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum active elemental effects per actor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCombatAnalyticsByDefault_MetaData[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable combat analytics by default */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable combat analytics by default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCombatEventLogEntries_MetaData[] = {
		{ "Category", "Analytics" },
		{ "ClampMax", "10000" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum combat event log entries */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum combat event log entries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalyticsUpdateFrequency_MetaData[] = {
		{ "Category", "Analytics" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analytics update frequency (seconds) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics update frequency (seconds)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoExportAnalyticsOnCombatEnd_MetaData[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Auto-export analytics on combat end */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-export analytics on combat end" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalyticsExportDirectory_MetaData[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analytics export directory */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics export directory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultAdvancedDestructionConfig_MetaData[] = {
		{ "Category", "Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Default Advanced Destruction configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Default Advanced Destruction configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableChaosDestructionByDefault_MetaData[] = {
		{ "Category", "Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Chaos destruction by default */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Chaos destruction by default" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalDestructionForceMultiplier_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global destruction force multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global destruction force multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDebrisObjectsPerEvent_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum debris objects per destruction event */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum debris objects per destruction event" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCombatLODSystem_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable LOD system for combat features */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable LOD system for combat features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance1_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distance threshold for LOD level 1 */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance threshold for LOD level 1" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance2_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distance threshold for LOD level 2 */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance threshold for LOD level 2" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentCombatEffects_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "500" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum concurrent combat effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum concurrent combat effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncAssetLoading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable async asset loading */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable async asset loading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMobileOptimizations_MetaData[] = {
		{ "Category", "Platform Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mobile platform optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mobile platform optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableConsoleOptimizations_MetaData[] = {
		{ "Category", "Platform Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Console platform optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Console platform optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVROptimizations_MetaData[] = {
		{ "Category", "Platform Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VR platform optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VR platform optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNetworkReplication_MetaData[] = {
		{ "Category", "Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable network replication for combat data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable network replication for combat data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkUpdateFrequency_MetaData[] = {
		{ "Category", "Networking" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "5.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Network update frequency for combat data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network update frequency for combat data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableClientSidePrediction_MetaData[] = {
		{ "Category", "Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable client-side prediction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable client-side prediction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDeveloperDebugFeatures_MetaData[] = {
		{ "Category", "Developer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable developer debug features */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable developer debug features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDebugInfoOnScreen_MetaData[] = {
		{ "Category", "Developer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Show debug information on screen */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Show debug information on screen" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCombatSystemUnitTests_MetaData[] = {
		{ "Category", "Developer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable combat system unit tests */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridgeSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable combat system unit tests" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAdvancedCombatFeatures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedCombatFeatures;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static void NewProp_bEnablePerformanceProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceProfiling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalCombatTickInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultEnhancedInputConfig;
	static void NewProp_bEnableEnhancedInputByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEnhancedInputByDefault;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalComboWindowMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultAICombatConfig;
	static void NewProp_bEnableAILearningByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAILearningByDefault;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalAIReactionTimeMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAILearningDataEntries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultElementalDamageConfig;
	static void NewProp_bEnableElementalInteractionsByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableElementalInteractionsByDefault;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalElementalDamageMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveElementalEffectsPerActor;
	static void NewProp_bEnableCombatAnalyticsByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCombatAnalyticsByDefault;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCombatEventLogEntries;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnalyticsUpdateFrequency;
	static void NewProp_bAutoExportAnalyticsOnCombatEnd_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoExportAnalyticsOnCombatEnd;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnalyticsExportDirectory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultAdvancedDestructionConfig;
	static void NewProp_bEnableChaosDestructionByDefault_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableChaosDestructionByDefault;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalDestructionForceMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxDebrisObjectsPerEvent;
	static void NewProp_bEnableCombatLODSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCombatLODSystem;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance1;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance2;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentCombatEffects;
	static void NewProp_bEnableAsyncAssetLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncAssetLoading;
	static void NewProp_bEnableMobileOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMobileOptimizations;
	static void NewProp_bEnableConsoleOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableConsoleOptimizations;
	static void NewProp_bEnableVROptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVROptimizations;
	static void NewProp_bEnableNetworkReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNetworkReplication;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkUpdateFrequency;
	static void NewProp_bEnableClientSidePrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableClientSidePrediction;
	static void NewProp_bEnableDeveloperDebugFeatures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDeveloperDebugFeatures;
	static void NewProp_bShowDebugInfoOnScreen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDebugInfoOnScreen;
	static void NewProp_bEnableCombatSystemUnitTests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCombatSystemUnitTests;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_AreAdvancedCombatFeaturesEnabled, "AreAdvancedCombatFeaturesEnabled" }, // 3817426829
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetCombatBridgeSettings, "GetCombatBridgeSettings" }, // 1306883028
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAdvancedDestructionConfig, "GetDefaultAdvancedDestructionConfig" }, // 2880225616
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultAICombatConfig, "GetDefaultAICombatConfig" }, // 62406720
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultElementalDamageConfig, "GetDefaultElementalDamageConfig" }, // 1076818571
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetDefaultEnhancedInputConfig, "GetDefaultEnhancedInputConfig" }, // 3097611229
		{ &Z_Construct_UFunction_UAuracronCombatBridgeSettings_GetGlobalCombatTickInterval, "GetGlobalCombatTickInterval" }, // 2771107283
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronCombatBridgeSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAdvancedCombatFeatures_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableAdvancedCombatFeatures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAdvancedCombatFeatures = { "bEnableAdvancedCombatFeatures", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAdvancedCombatFeatures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdvancedCombatFeatures_MetaData), NewProp_bEnableAdvancedCombatFeatures_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnablePerformanceProfiling_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnablePerformanceProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnablePerformanceProfiling = { "bEnablePerformanceProfiling", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnablePerformanceProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceProfiling_MetaData), NewProp_bEnablePerformanceProfiling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalCombatTickInterval = { "GlobalCombatTickInterval", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, GlobalCombatTickInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalCombatTickInterval_MetaData), NewProp_GlobalCombatTickInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultEnhancedInputConfig = { "DefaultEnhancedInputConfig", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, DefaultEnhancedInputConfig), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultEnhancedInputConfig_MetaData), NewProp_DefaultEnhancedInputConfig_MetaData) }; // 2243282029
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableEnhancedInputByDefault_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableEnhancedInputByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableEnhancedInputByDefault = { "bEnableEnhancedInputByDefault", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableEnhancedInputByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEnhancedInputByDefault_MetaData), NewProp_bEnableEnhancedInputByDefault_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalComboWindowMultiplier = { "GlobalComboWindowMultiplier", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, GlobalComboWindowMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalComboWindowMultiplier_MetaData), NewProp_GlobalComboWindowMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultAICombatConfig = { "DefaultAICombatConfig", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, DefaultAICombatConfig), Z_Construct_UScriptStruct_FAuracronAICombatConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAICombatConfig_MetaData), NewProp_DefaultAICombatConfig_MetaData) }; // 517528943
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAILearningByDefault_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableAILearningByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAILearningByDefault = { "bEnableAILearningByDefault", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAILearningByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAILearningByDefault_MetaData), NewProp_bEnableAILearningByDefault_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalAIReactionTimeMultiplier = { "GlobalAIReactionTimeMultiplier", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, GlobalAIReactionTimeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalAIReactionTimeMultiplier_MetaData), NewProp_GlobalAIReactionTimeMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxAILearningDataEntries = { "MaxAILearningDataEntries", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, MaxAILearningDataEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAILearningDataEntries_MetaData), NewProp_MaxAILearningDataEntries_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultElementalDamageConfig = { "DefaultElementalDamageConfig", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, DefaultElementalDamageConfig), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultElementalDamageConfig_MetaData), NewProp_DefaultElementalDamageConfig_MetaData) }; // 1909653023
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableElementalInteractionsByDefault_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableElementalInteractionsByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableElementalInteractionsByDefault = { "bEnableElementalInteractionsByDefault", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableElementalInteractionsByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableElementalInteractionsByDefault_MetaData), NewProp_bEnableElementalInteractionsByDefault_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalElementalDamageMultiplier = { "GlobalElementalDamageMultiplier", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, GlobalElementalDamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalElementalDamageMultiplier_MetaData), NewProp_GlobalElementalDamageMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxActiveElementalEffectsPerActor = { "MaxActiveElementalEffectsPerActor", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, MaxActiveElementalEffectsPerActor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveElementalEffectsPerActor_MetaData), NewProp_MaxActiveElementalEffectsPerActor_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatAnalyticsByDefault_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableCombatAnalyticsByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatAnalyticsByDefault = { "bEnableCombatAnalyticsByDefault", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatAnalyticsByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCombatAnalyticsByDefault_MetaData), NewProp_bEnableCombatAnalyticsByDefault_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxCombatEventLogEntries = { "MaxCombatEventLogEntries", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, MaxCombatEventLogEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCombatEventLogEntries_MetaData), NewProp_MaxCombatEventLogEntries_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_AnalyticsUpdateFrequency = { "AnalyticsUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, AnalyticsUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalyticsUpdateFrequency_MetaData), NewProp_AnalyticsUpdateFrequency_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bAutoExportAnalyticsOnCombatEnd_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bAutoExportAnalyticsOnCombatEnd = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bAutoExportAnalyticsOnCombatEnd = { "bAutoExportAnalyticsOnCombatEnd", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bAutoExportAnalyticsOnCombatEnd_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoExportAnalyticsOnCombatEnd_MetaData), NewProp_bAutoExportAnalyticsOnCombatEnd_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_AnalyticsExportDirectory = { "AnalyticsExportDirectory", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, AnalyticsExportDirectory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalyticsExportDirectory_MetaData), NewProp_AnalyticsExportDirectory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultAdvancedDestructionConfig = { "DefaultAdvancedDestructionConfig", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, DefaultAdvancedDestructionConfig), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultAdvancedDestructionConfig_MetaData), NewProp_DefaultAdvancedDestructionConfig_MetaData) }; // 886306391
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableChaosDestructionByDefault_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableChaosDestructionByDefault = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableChaosDestructionByDefault = { "bEnableChaosDestructionByDefault", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableChaosDestructionByDefault_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableChaosDestructionByDefault_MetaData), NewProp_bEnableChaosDestructionByDefault_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalDestructionForceMultiplier = { "GlobalDestructionForceMultiplier", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, GlobalDestructionForceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalDestructionForceMultiplier_MetaData), NewProp_GlobalDestructionForceMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxDebrisObjectsPerEvent = { "MaxDebrisObjectsPerEvent", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, MaxDebrisObjectsPerEvent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDebrisObjectsPerEvent_MetaData), NewProp_MaxDebrisObjectsPerEvent_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatLODSystem_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableCombatLODSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatLODSystem = { "bEnableCombatLODSystem", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatLODSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCombatLODSystem_MetaData), NewProp_bEnableCombatLODSystem_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_LODDistance1 = { "LODDistance1", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, LODDistance1), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance1_MetaData), NewProp_LODDistance1_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_LODDistance2 = { "LODDistance2", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, LODDistance2), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance2_MetaData), NewProp_LODDistance2_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxConcurrentCombatEffects = { "MaxConcurrentCombatEffects", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, MaxConcurrentCombatEffects), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentCombatEffects_MetaData), NewProp_MaxConcurrentCombatEffects_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAsyncAssetLoading_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableAsyncAssetLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAsyncAssetLoading = { "bEnableAsyncAssetLoading", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAsyncAssetLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncAssetLoading_MetaData), NewProp_bEnableAsyncAssetLoading_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableMobileOptimizations_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableMobileOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableMobileOptimizations = { "bEnableMobileOptimizations", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableMobileOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMobileOptimizations_MetaData), NewProp_bEnableMobileOptimizations_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableConsoleOptimizations_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableConsoleOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableConsoleOptimizations = { "bEnableConsoleOptimizations", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableConsoleOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableConsoleOptimizations_MetaData), NewProp_bEnableConsoleOptimizations_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableVROptimizations_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableVROptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableVROptimizations = { "bEnableVROptimizations", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableVROptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVROptimizations_MetaData), NewProp_bEnableVROptimizations_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableNetworkReplication_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableNetworkReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableNetworkReplication = { "bEnableNetworkReplication", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableNetworkReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNetworkReplication_MetaData), NewProp_bEnableNetworkReplication_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_NetworkUpdateFrequency = { "NetworkUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridgeSettings, NetworkUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkUpdateFrequency_MetaData), NewProp_NetworkUpdateFrequency_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableClientSidePrediction_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableClientSidePrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableClientSidePrediction = { "bEnableClientSidePrediction", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableClientSidePrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableClientSidePrediction_MetaData), NewProp_bEnableClientSidePrediction_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDeveloperDebugFeatures_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableDeveloperDebugFeatures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDeveloperDebugFeatures = { "bEnableDeveloperDebugFeatures", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDeveloperDebugFeatures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDeveloperDebugFeatures_MetaData), NewProp_bEnableDeveloperDebugFeatures_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bShowDebugInfoOnScreen_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bShowDebugInfoOnScreen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bShowDebugInfoOnScreen = { "bShowDebugInfoOnScreen", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bShowDebugInfoOnScreen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDebugInfoOnScreen_MetaData), NewProp_bShowDebugInfoOnScreen_MetaData) };
void Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatSystemUnitTests_SetBit(void* Obj)
{
	((UAuracronCombatBridgeSettings*)Obj)->bEnableCombatSystemUnitTests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatSystemUnitTests = { "bEnableCombatSystemUnitTests", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronCombatBridgeSettings), &Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatSystemUnitTests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCombatSystemUnitTests_MetaData), NewProp_bEnableCombatSystemUnitTests_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAdvancedCombatFeatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnablePerformanceProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalCombatTickInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultEnhancedInputConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableEnhancedInputByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalComboWindowMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultAICombatConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAILearningByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalAIReactionTimeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxAILearningDataEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultElementalDamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableElementalInteractionsByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalElementalDamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxActiveElementalEffectsPerActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatAnalyticsByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxCombatEventLogEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_AnalyticsUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bAutoExportAnalyticsOnCombatEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_AnalyticsExportDirectory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_DefaultAdvancedDestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableChaosDestructionByDefault,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_GlobalDestructionForceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxDebrisObjectsPerEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatLODSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_LODDistance1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_LODDistance2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_MaxConcurrentCombatEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableAsyncAssetLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableMobileOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableConsoleOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableVROptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableNetworkReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_NetworkUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableClientSidePrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableDeveloperDebugFeatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bShowDebugInfoOnScreen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::NewProp_bEnableCombatSystemUnitTests,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDeveloperSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::ClassParams = {
	&UAuracronCombatBridgeSettings::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.OuterSingleton, Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridgeSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronCombatBridgeSettings);
UAuracronCombatBridgeSettings::~UAuracronCombatBridgeSettings() {}
// ********** End Class UAuracronCombatBridgeSettings **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h__Script_AuracronCombatBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronCombatBridgeSettings, UAuracronCombatBridgeSettings::StaticClass, TEXT("UAuracronCombatBridgeSettings"), &Z_Registration_Info_UClass_UAuracronCombatBridgeSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronCombatBridgeSettings), 91514652U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h__Script_AuracronCombatBridge_2209423382(TEXT("/Script/AuracronCombatBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h__Script_AuracronCombatBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h__Script_AuracronCombatBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
