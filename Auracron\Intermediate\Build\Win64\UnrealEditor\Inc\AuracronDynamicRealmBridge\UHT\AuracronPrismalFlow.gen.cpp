// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronPrismalFlow.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPrismalFlow() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalIsland_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalFlowSegment;
class UScriptStruct* FPrismalFlowSegment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalFlowSegment, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("PrismalFlowSegment"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Prismal Flow segment data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prismal Flow segment data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segment start location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segment start location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segment end location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segment end location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segment width */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segment width" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Flow speed in this segment */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow speed in this segment" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowDirection_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Flow direction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow direction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Controlling team (0 = neutral, 1 = team A, 2 = team B) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Controlling team (0 = neutral, 1 = team A, 2 = team B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SegmentColor_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segment color based on control */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segment color based on control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Islands_MetaData[] = {
		{ "Category", "Flow Segment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Islands in this segment */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Islands in this segment" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowDirection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SegmentColor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Islands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Islands;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalFlowSegment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection = { "FlowDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowDirection_MetaData), NewProp_FlowDirection_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_SegmentColor = { "SegmentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, SegmentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SegmentColor_MetaData), NewProp_SegmentColor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Islands_Inner = { "Islands", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronPrismalIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Islands = { "Islands", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Islands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Islands_MetaData), NewProp_Islands_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_SegmentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Islands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Islands,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"PrismalFlowSegment",
	Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers),
	sizeof(FPrismalFlowSegment),
	alignof(FPrismalFlowSegment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton;
}
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin ScriptStruct FPrismalFlowConfig ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalFlowConfig;
class UScriptStruct* FPrismalFlowConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalFlowConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("PrismalFlowConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Prismal Flow configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prismal Flow configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLength_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total flow length */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total flow length" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurveCount_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of serpentine curves */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of serpentine curves" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurveAmplitude_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Curve amplitude */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curve amplitude" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatternChangeInterval_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Flow pattern change interval */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow pattern change interval" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseFlowSpeed_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base flow speed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base flow speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeedVariation_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Flow speed variation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow speed variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyIntensity_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Energy intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Energy intensity" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalLength;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurveCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurveAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatternChangeInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseFlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeedVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalFlowConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_TotalLength = { "TotalLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, TotalLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLength_MetaData), NewProp_TotalLength_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_CurveCount = { "CurveCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, CurveCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurveCount_MetaData), NewProp_CurveCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_CurveAmplitude = { "CurveAmplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, CurveAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurveAmplitude_MetaData), NewProp_CurveAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_PatternChangeInterval = { "PatternChangeInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, PatternChangeInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatternChangeInterval_MetaData), NewProp_PatternChangeInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_BaseFlowSpeed = { "BaseFlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, BaseFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseFlowSpeed_MetaData), NewProp_BaseFlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_FlowSpeedVariation = { "FlowSpeedVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, FlowSpeedVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeedVariation_MetaData), NewProp_FlowSpeedVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_EnergyIntensity = { "EnergyIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowConfig, EnergyIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyIntensity_MetaData), NewProp_EnergyIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_TotalLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_CurveCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_CurveAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_PatternChangeInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_BaseFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_FlowSpeedVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewProp_EnergyIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"PrismalFlowConfig",
	Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::PropPointers),
	sizeof(FPrismalFlowConfig),
	alignof(FPrismalFlowConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.InnerSingleton, Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowConfig.InnerSingleton;
}
// ********** End ScriptStruct FPrismalFlowConfig **************************************************

// ********** Begin Class AAuracronPrismalFlow Function ApplyFlowEffectToActor *********************
struct Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics
{
	struct AuracronPrismalFlow_eventApplyFlowEffectToActor_Parms
	{
		AActor* Actor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flow Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventApplyFlowEffectToActor_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::NewProp_Actor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "ApplyFlowEffectToActor", Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::AuracronPrismalFlow_eventApplyFlowEffectToActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::AuracronPrismalFlow_eventApplyFlowEffectToActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execApplyFlowEffectToActor)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyFlowEffectToActor(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function ApplyFlowEffectToActor ***********************

// ********** Begin Class AAuracronPrismalFlow Function DebugRegenerateFlow ************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "DebugRegenerateFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execDebugRegenerateFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugRegenerateFlow();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function DebugRegenerateFlow **************************

// ********** Begin Class AAuracronPrismalFlow Function DebugShowFlowPath **************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "DebugShowFlowPath", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execDebugShowFlowPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugShowFlowPath();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function DebugShowFlowPath ****************************

// ********** Begin Class AAuracronPrismalFlow Function DebugShowIslandLocations *******************
struct Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "DebugShowIslandLocations", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execDebugShowIslandLocations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugShowIslandLocations();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function DebugShowIslandLocations *********************

// ********** Begin Class AAuracronPrismalFlow Function GetFlowIntensityAtLocation *****************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics
{
	struct AuracronPrismalFlow_eventGetFlowIntensityAtLocation_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flow Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetFlowIntensityAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetFlowIntensityAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetFlowIntensityAtLocation", Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::AuracronPrismalFlow_eventGetFlowIntensityAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::AuracronPrismalFlow_eventGetFlowIntensityAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetFlowIntensityAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFlowIntensityAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetFlowIntensityAtLocation *******************

// ********** Begin Class AAuracronPrismalFlow Function GetFlowSpeed *******************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics
{
	struct AuracronPrismalFlow_eventGetFlowSpeed_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetFlowSpeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetFlowSpeed", Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::AuracronPrismalFlow_eventGetFlowSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::AuracronPrismalFlow_eventGetFlowSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetFlowSpeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFlowSpeed();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetFlowSpeed *********************************

// ********** Begin Class AAuracronPrismalFlow Function GetFlowVelocityAtLocation ******************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics
{
	struct AuracronPrismalFlow_eventGetFlowVelocityAtLocation_Parms
	{
		FVector Location;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flow Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetFlowVelocityAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetFlowVelocityAtLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetFlowVelocityAtLocation", Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::AuracronPrismalFlow_eventGetFlowVelocityAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::AuracronPrismalFlow_eventGetFlowVelocityAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetFlowVelocityAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetFlowVelocityAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetFlowVelocityAtLocation ********************

// ********** Begin Class AAuracronPrismalFlow Function GetIslandsByType ***************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics
{
	struct AuracronPrismalFlow_eventGetIslandsByType_Parms
	{
		EPrismalIslandType IslandType;
		TArray<AAuracronPrismalIsland*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetIslandsByType_Parms, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronPrismalIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetIslandsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetIslandsByType", Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::AuracronPrismalFlow_eventGetIslandsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::AuracronPrismalFlow_eventGetIslandsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetIslandsByType)
{
	P_GET_ENUM(EPrismalIslandType,Z_Param_IslandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAuracronPrismalIsland*>*)Z_Param__Result=P_THIS->GetIslandsByType(EPrismalIslandType(Z_Param_IslandType));
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetIslandsByType *****************************

// ********** Begin Class AAuracronPrismalFlow Function GetNearestIsland ***************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics
{
	struct AuracronPrismalFlow_eventGetNearestIsland_Parms
	{
		FVector Location;
		EPrismalIslandType IslandType;
		AAuracronPrismalIsland* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "CPP_Default_IslandType", "Nexus" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetNearestIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetNearestIsland_Parms, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetNearestIsland_Parms, ReturnValue), Z_Construct_UClass_AAuracronPrismalIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetNearestIsland", Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::AuracronPrismalFlow_eventGetNearestIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::AuracronPrismalFlow_eventGetNearestIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetNearestIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_ENUM(EPrismalIslandType,Z_Param_IslandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AAuracronPrismalIsland**)Z_Param__Result=P_THIS->GetNearestIsland(Z_Param_Out_Location,EPrismalIslandType(Z_Param_IslandType));
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetNearestIsland *****************************

// ********** Begin Class AAuracronPrismalFlow Function GetSegmentControl **************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics
{
	struct AuracronPrismalFlow_eventGetSegmentControl_Parms
	{
		int32 SegmentIndex;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Team Control" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SegmentIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::NewProp_SegmentIndex = { "SegmentIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetSegmentControl_Parms, SegmentIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventGetSegmentControl_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::NewProp_SegmentIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "GetSegmentControl", Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::AuracronPrismalFlow_eventGetSegmentControl_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::AuracronPrismalFlow_eventGetSegmentControl_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execGetSegmentControl)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SegmentIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetSegmentControl(Z_Param_SegmentIndex);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function GetSegmentControl ****************************

// ********** Begin Class AAuracronPrismalFlow Function InitializeFlow *****************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Flow management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "InitializeFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execInitializeFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeFlow();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function InitializeFlow *******************************

// ********** Begin Class AAuracronPrismalFlow Function IsLocationInFlow ***************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics
{
	struct AuracronPrismalFlow_eventIsLocationInFlow_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Flow Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Flow interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventIsLocationInFlow_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPrismalFlow_eventIsLocationInFlow_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPrismalFlow_eventIsLocationInFlow_Parms), &Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "IsLocationInFlow", Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::AuracronPrismalFlow_eventIsLocationInFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::AuracronPrismalFlow_eventIsLocationInFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execIsLocationInFlow)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLocationInFlow(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function IsLocationInFlow *****************************

// ********** Begin Class AAuracronPrismalFlow Function PlayFlowTransitionEffect *******************
struct Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "PlayFlowTransitionEffect", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execPlayFlowTransitionEffect)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayFlowTransitionEffect();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function PlayFlowTransitionEffect *********************

// ********** Begin Class AAuracronPrismalFlow Function RegenerateFlowPattern **********************
struct Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "RegenerateFlowPattern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execRegenerateFlowPattern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegenerateFlowPattern();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function RegenerateFlowPattern ************************

// ********** Begin Class AAuracronPrismalFlow Function SetFlowIntensity ***************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics
{
	struct AuracronPrismalFlow_eventSetFlowIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventSetFlowIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "SetFlowIntensity", Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::AuracronPrismalFlow_eventSetFlowIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::AuracronPrismalFlow_eventSetFlowIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execSetFlowIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlowIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function SetFlowIntensity *****************************

// ********** Begin Class AAuracronPrismalFlow Function SetFlowSpeed *******************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics
{
	struct AuracronPrismalFlow_eventSetFlowSpeed_Parms
	{
		float NewSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::NewProp_NewSpeed = { "NewSpeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventSetFlowSpeed_Parms, NewSpeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::NewProp_NewSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "SetFlowSpeed", Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::AuracronPrismalFlow_eventSetFlowSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::AuracronPrismalFlow_eventSetFlowSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execSetFlowSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlowSpeed(Z_Param_NewSpeed);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function SetFlowSpeed *********************************

// ********** Begin Class AAuracronPrismalFlow Function SetSegmentControl **************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics
{
	struct AuracronPrismalFlow_eventSetSegmentControl_Parms
	{
		int32 SegmentIndex;
		int32 TeamID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Team Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Team control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SegmentIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::NewProp_SegmentIndex = { "SegmentIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventSetSegmentControl_Parms, SegmentIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventSetSegmentControl_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::NewProp_SegmentIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::NewProp_TeamID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "SetSegmentControl", Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::AuracronPrismalFlow_eventSetSegmentControl_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::AuracronPrismalFlow_eventSetSegmentControl_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execSetSegmentControl)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SegmentIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSegmentControl(Z_Param_SegmentIndex,Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function SetSegmentControl ****************************

// ********** Begin Class AAuracronPrismalFlow Function SpawnPrismalIslands ************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "SpawnPrismalIslands", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execSpawnPrismalIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnPrismalIslands();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function SpawnPrismalIslands **************************

// ********** Begin Class AAuracronPrismalFlow Function UpdateFlowColors ***************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Team Control" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "UpdateFlowColors", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execUpdateFlowColors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFlowColors();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function UpdateFlowColors *****************************

// ********** Begin Class AAuracronPrismalFlow Function UpdateFlowDynamics *************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics
{
	struct AuracronPrismalFlow_eventUpdateFlowDynamics_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prismal Flow" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventUpdateFlowDynamics_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "UpdateFlowDynamics", Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::AuracronPrismalFlow_eventUpdateFlowDynamics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::AuracronPrismalFlow_eventUpdateFlowDynamics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execUpdateFlowDynamics)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFlowDynamics(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function UpdateFlowDynamics ***************************

// ********** Begin Class AAuracronPrismalFlow Function UpdateFlowVisuals **************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics
{
	struct AuracronPrismalFlow_eventUpdateFlowVisuals_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual effects\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventUpdateFlowVisuals_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "UpdateFlowVisuals", Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::AuracronPrismalFlow_eventUpdateFlowVisuals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::AuracronPrismalFlow_eventUpdateFlowVisuals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execUpdateFlowVisuals)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateFlowVisuals(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function UpdateFlowVisuals ****************************

// ********** Begin Class AAuracronPrismalFlow Function UpdateIslandStates *************************
struct Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics
{
	struct AuracronPrismalFlow_eventUpdateIslandStates_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalFlow_eventUpdateIslandStates_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalFlow, nullptr, "UpdateIslandStates", Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::AuracronPrismalFlow_eventUpdateIslandStates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::AuracronPrismalFlow_eventUpdateIslandStates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalFlow::execUpdateIslandStates)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateIslandStates(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalFlow Function UpdateIslandStates ***************************

// ********** Begin Class AAuracronPrismalFlow *****************************************************
void AAuracronPrismalFlow::StaticRegisterNativesAAuracronPrismalFlow()
{
	UClass* Class = AAuracronPrismalFlow::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyFlowEffectToActor", &AAuracronPrismalFlow::execApplyFlowEffectToActor },
		{ "DebugRegenerateFlow", &AAuracronPrismalFlow::execDebugRegenerateFlow },
		{ "DebugShowFlowPath", &AAuracronPrismalFlow::execDebugShowFlowPath },
		{ "DebugShowIslandLocations", &AAuracronPrismalFlow::execDebugShowIslandLocations },
		{ "GetFlowIntensityAtLocation", &AAuracronPrismalFlow::execGetFlowIntensityAtLocation },
		{ "GetFlowSpeed", &AAuracronPrismalFlow::execGetFlowSpeed },
		{ "GetFlowVelocityAtLocation", &AAuracronPrismalFlow::execGetFlowVelocityAtLocation },
		{ "GetIslandsByType", &AAuracronPrismalFlow::execGetIslandsByType },
		{ "GetNearestIsland", &AAuracronPrismalFlow::execGetNearestIsland },
		{ "GetSegmentControl", &AAuracronPrismalFlow::execGetSegmentControl },
		{ "InitializeFlow", &AAuracronPrismalFlow::execInitializeFlow },
		{ "IsLocationInFlow", &AAuracronPrismalFlow::execIsLocationInFlow },
		{ "PlayFlowTransitionEffect", &AAuracronPrismalFlow::execPlayFlowTransitionEffect },
		{ "RegenerateFlowPattern", &AAuracronPrismalFlow::execRegenerateFlowPattern },
		{ "SetFlowIntensity", &AAuracronPrismalFlow::execSetFlowIntensity },
		{ "SetFlowSpeed", &AAuracronPrismalFlow::execSetFlowSpeed },
		{ "SetSegmentControl", &AAuracronPrismalFlow::execSetSegmentControl },
		{ "SpawnPrismalIslands", &AAuracronPrismalFlow::execSpawnPrismalIslands },
		{ "UpdateFlowColors", &AAuracronPrismalFlow::execUpdateFlowColors },
		{ "UpdateFlowDynamics", &AAuracronPrismalFlow::execUpdateFlowDynamics },
		{ "UpdateFlowVisuals", &AAuracronPrismalFlow::execUpdateFlowVisuals },
		{ "UpdateIslandStates", &AAuracronPrismalFlow::execUpdateIslandStates },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronPrismalFlow;
UClass* AAuracronPrismalFlow::GetPrivateStaticClass()
{
	using TClass = AAuracronPrismalFlow;
	if (!Z_Registration_Info_UClass_AAuracronPrismalFlow.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPrismalFlow"),
			Z_Registration_Info_UClass_AAuracronPrismalFlow.InnerSingleton,
			StaticRegisterNativesAAuracronPrismalFlow,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronPrismalFlow.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister()
{
	return AAuracronPrismalFlow::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Prismal Flow\n * \n * The central energy river that connects all three layers:\n * - Serpentine pattern that changes every 10 minutes\n * - Contains 23 strategic islands (5 Nexus, 8 Santu\xc3\xa1rio, 6 Arsenal, 4 Caos)\n * - Variable flow speed affects movement and abilities\n * - Color changes based on controlling team\n * - Connects all three vertical layers\n */" },
#endif
		{ "IncludePath", "AuracronPrismalFlow.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Prismal Flow\n\nThe central energy river that connects all three layers:\n- Serpentine pattern that changes every 10 minutes\n- Contains 23 strategic islands (5 Nexus, 8 Santu\xc3\xa1rio, 6 Arsenal, 4 Caos)\n- Variable flow speed affects movement and abilities\n- Color changes based on controlling team\n- Connects all three vertical layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMeshComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowEffectComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowAudioComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowPCGComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowConfig_MetaData[] = {
		{ "Category", "Flow Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Flow configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMesh_MetaData[] = {
		{ "Category", "Flow Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMaterial_MetaData[] = {
		{ "Category", "Flow Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowEffect_MetaData[] = {
		{ "Category", "Flow Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowAudio_MetaData[] = {
		{ "Category", "Flow Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSegments_MetaData[] = {
		{ "Category", "Flow State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Flow state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flow state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFlowSpeed_MetaData[] = {
		{ "Category", "Flow State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastPatternChange_MetaData[] = {
		{ "Category", "Flow State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPatternSeed_MetaData[] = {
		{ "Category", "Flow State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedIslands_MetaData[] = {
		{ "Category", "Islands" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandCounts_MetaData[] = {
		{ "Category", "Islands" },
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dynamic materials\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic materials" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowMeshComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowEffectComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowEffectComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowAudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowPCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowPCGComponents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowAudio;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowSegments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowSegments;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastPatternChange;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPatternSeed;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedIslands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedIslands;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IslandCounts_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandCounts_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandCounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_IslandCounts;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DynamicMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DynamicMaterials;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_ApplyFlowEffectToActor, "ApplyFlowEffectToActor" }, // 98569462
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_DebugRegenerateFlow, "DebugRegenerateFlow" }, // 1535657317
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowFlowPath, "DebugShowFlowPath" }, // 3084703889
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_DebugShowIslandLocations, "DebugShowIslandLocations" }, // 718810207
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowIntensityAtLocation, "GetFlowIntensityAtLocation" }, // 461955345
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowSpeed, "GetFlowSpeed" }, // 1046546272
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetFlowVelocityAtLocation, "GetFlowVelocityAtLocation" }, // 239238264
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetIslandsByType, "GetIslandsByType" }, // 269335679
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetNearestIsland, "GetNearestIsland" }, // 1455966171
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_GetSegmentControl, "GetSegmentControl" }, // 2888917631
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_InitializeFlow, "InitializeFlow" }, // 4074060766
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_IsLocationInFlow, "IsLocationInFlow" }, // 2305248931
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_PlayFlowTransitionEffect, "PlayFlowTransitionEffect" }, // 3879203539
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_RegenerateFlowPattern, "RegenerateFlowPattern" }, // 2162752422
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowIntensity, "SetFlowIntensity" }, // 3135420709
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_SetFlowSpeed, "SetFlowSpeed" }, // 2396762841
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_SetSegmentControl, "SetSegmentControl" }, // 1684968385
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_SpawnPrismalIslands, "SpawnPrismalIslands" }, // 3730509645
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowColors, "UpdateFlowColors" }, // 4186847332
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowDynamics, "UpdateFlowDynamics" }, // 1362664565
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_UpdateFlowVisuals, "UpdateFlowVisuals" }, // 3632703304
		{ &Z_Construct_UFunction_AAuracronPrismalFlow_UpdateIslandStates, "UpdateIslandStates" }, // 1099372428
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronPrismalFlow>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSpline = { "FlowSpline", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpline_MetaData), NewProp_FlowSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMeshComponents_Inner = { "FlowMeshComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USplineMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMeshComponents = { "FlowMeshComponents", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowMeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMeshComponents_MetaData), NewProp_FlowMeshComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffectComponents_Inner = { "FlowEffectComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffectComponents = { "FlowEffectComponents", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowEffectComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowEffectComponents_MetaData), NewProp_FlowEffectComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowAudioComponent = { "FlowAudioComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowAudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowAudioComponent_MetaData), NewProp_FlowAudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowPCGComponents_Inner = { "FlowPCGComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowPCGComponents = { "FlowPCGComponents", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowPCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowPCGComponents_MetaData), NewProp_FlowPCGComponents_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowConfig = { "FlowConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowConfig), Z_Construct_UScriptStruct_FPrismalFlowConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowConfig_MetaData), NewProp_FlowConfig_MetaData) }; // 516576427
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMesh = { "FlowMesh", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMesh_MetaData), NewProp_FlowMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMaterial = { "FlowMaterial", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMaterial_MetaData), NewProp_FlowMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffect = { "FlowEffect", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowEffect_MetaData), NewProp_FlowEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowAudio = { "FlowAudio", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowAudio), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowAudio_MetaData), NewProp_FlowAudio_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSegments_Inner = { "FlowSegments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrismalFlowSegment, METADATA_PARAMS(0, nullptr) }; // 955822961
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSegments = { "FlowSegments", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, FlowSegments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSegments_MetaData), NewProp_FlowSegments_MetaData) }; // 955822961
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_CurrentFlowSpeed = { "CurrentFlowSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, CurrentFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFlowSpeed_MetaData), NewProp_CurrentFlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_LastPatternChange = { "LastPatternChange", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, LastPatternChange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastPatternChange_MetaData), NewProp_LastPatternChange_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_CurrentPatternSeed = { "CurrentPatternSeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, CurrentPatternSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPatternSeed_MetaData), NewProp_CurrentPatternSeed_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_SpawnedIslands_Inner = { "SpawnedIslands", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronPrismalIsland_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_SpawnedIslands = { "SpawnedIslands", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, SpawnedIslands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedIslands_MetaData), NewProp_SpawnedIslands_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_ValueProp = { "IslandCounts", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_Key_KeyProp = { "IslandCounts_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts = { "IslandCounts", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, IslandCounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandCounts_MetaData), NewProp_IslandCounts_MetaData) }; // 1145382866
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_DynamicMaterials_Inner = { "DynamicMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_DynamicMaterials = { "DynamicMaterials", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalFlow, DynamicMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicMaterials_MetaData), NewProp_DynamicMaterials_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMeshComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffectComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffectComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowAudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowPCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowPCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSegments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_FlowSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_CurrentFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_LastPatternChange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_CurrentPatternSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_SpawnedIslands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_SpawnedIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_IslandCounts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_DynamicMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalFlow_Statics::NewProp_DynamicMaterials,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalFlow_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronPrismalFlow_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalFlow_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronPrismalFlow_Statics::ClassParams = {
	&AAuracronPrismalFlow::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronPrismalFlow_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalFlow_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalFlow_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronPrismalFlow_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronPrismalFlow()
{
	if (!Z_Registration_Info_UClass_AAuracronPrismalFlow.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronPrismalFlow.OuterSingleton, Z_Construct_UClass_AAuracronPrismalFlow_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronPrismalFlow.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronPrismalFlow);
AAuracronPrismalFlow::~AAuracronPrismalFlow() {}
// ********** End Class AAuracronPrismalFlow *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPrismalFlowSegment::StaticStruct, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewStructOps, TEXT("PrismalFlowSegment"), &Z_Registration_Info_UScriptStruct_FPrismalFlowSegment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalFlowSegment), 955822961U) },
		{ FPrismalFlowConfig::StaticStruct, Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics::NewStructOps, TEXT("PrismalFlowConfig"), &Z_Registration_Info_UScriptStruct_FPrismalFlowConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalFlowConfig), 516576427U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronPrismalFlow, AAuracronPrismalFlow::StaticClass, TEXT("AAuracronPrismalFlow"), &Z_Registration_Info_UClass_AAuracronPrismalFlow, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronPrismalFlow), 3561111856U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_3007886405(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
