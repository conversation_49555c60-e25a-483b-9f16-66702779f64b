// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdvancedCombatExample.h"

#ifdef AURACRONCOMBATBRIDGE_AuracronAdvancedCombatExample_generated_h
#error "AuracronAdvancedCombatExample.generated.h already included, missing '#pragma once' in AuracronAdvancedCombatExample.h"
#endif
#define AURACRONCOMBATBRIDGE_AuracronAdvancedCombatExample_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAuracronAICombatBehavior : uint8;
enum class EAuracronComboType : uint8;
enum class EAuracronElementalType : uint8;
struct FAuracronCombatAnalytics;

// ********** Begin Class AAuracronAdvancedCombatExample *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnCombatAnalyticsUpdatedExample); \
	DECLARE_FUNCTION(execOnAdvancedDestructionExample); \
	DECLARE_FUNCTION(execOnAICombatDecisionExample); \
	DECLARE_FUNCTION(execOnElementalDamageAppliedExample); \
	DECLARE_FUNCTION(execOnComboExecutedExample); \
	DECLARE_FUNCTION(execRunAdvancedCombatScenario); \
	DECLARE_FUNCTION(execDemonstrateProceduralDamage); \
	DECLARE_FUNCTION(execDemonstrateAdvancedDestruction); \
	DECLARE_FUNCTION(execInitializeAdvancedDestructionExample); \
	DECLARE_FUNCTION(execExportExampleAnalytics); \
	DECLARE_FUNCTION(execDemonstrateCombatAnalytics); \
	DECLARE_FUNCTION(execDemonstrateElementalInteractions); \
	DECLARE_FUNCTION(execDemonstrateElementalDamage); \
	DECLARE_FUNCTION(execInitializeElementalSystemExample); \
	DECLARE_FUNCTION(execUpdateAICombatExample); \
	DECLARE_FUNCTION(execDemonstrateAIAdaptation); \
	DECLARE_FUNCTION(execInitializeAICombatExample); \
	DECLARE_FUNCTION(execExecuteExampleCombo); \
	DECLARE_FUNCTION(execSetupExampleCombos); \
	DECLARE_FUNCTION(execInitializeEnhancedInputExample);


AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronAdvancedCombatExample(); \
	friend struct Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronAdvancedCombatExample, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronCombatBridge"), Z_Construct_UClass_AAuracronAdvancedCombatExample_NoRegister) \
	DECLARE_SERIALIZER(AAuracronAdvancedCombatExample)


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronAdvancedCombatExample(AAuracronAdvancedCombatExample&&) = delete; \
	AAuracronAdvancedCombatExample(const AAuracronAdvancedCombatExample&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronAdvancedCombatExample); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronAdvancedCombatExample); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronAdvancedCombatExample) \
	NO_API virtual ~AAuracronAdvancedCombatExample();


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_15_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronAdvancedCombatExample;

// ********** End Class AAuracronAdvancedCombatExample *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
