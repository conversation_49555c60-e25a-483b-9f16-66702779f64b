// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronCombatBridge.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronCombatBridge() {}

// ********** Begin Cross Module References ********************************************************
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTree_NoRegister();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature();
AURACRONCOMBATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAICombatConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatAnalytics();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronComboConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDamageConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronElementalDamageConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingConfiguration();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingResult();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundCue_NoRegister();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_ECollisionChannel();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UEnhancedInputComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
FIELDSYSTEMENGINE_API UClass* Z_Construct_UClass_UFieldSystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
STATETREEMODULE_API UClass* Z_Construct_UClass_UStateTree_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronCombatBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronDamageType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDamageType;
static UEnum* EAuracronDamageType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronDamageType"));
	}
	return Z_Registration_Info_UEnum_EAuracronDamageType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronDamageType>()
{
	return EAuracronDamageType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Area.DisplayName", "Area of Effect" },
		{ "Area.Name", "EAuracronDamageType::Area" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de dano\n */" },
#endif
		{ "Healing.DisplayName", "Healing" },
		{ "Healing.Name", "EAuracronDamageType::Healing" },
		{ "Magical.DisplayName", "Magical" },
		{ "Magical.Name", "EAuracronDamageType::Magical" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronDamageType::None" },
		{ "OverTime.DisplayName", "Over Time" },
		{ "OverTime.Name", "EAuracronDamageType::OverTime" },
		{ "Percentage.DisplayName", "Percentage" },
		{ "Percentage.Name", "EAuracronDamageType::Percentage" },
		{ "Physical.DisplayName", "Physical" },
		{ "Physical.Name", "EAuracronDamageType::Physical" },
		{ "Shield.DisplayName", "Shield" },
		{ "Shield.Name", "EAuracronDamageType::Shield" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de dano" },
#endif
		{ "TrueDamage.DisplayName", "True Damage" },
		{ "TrueDamage.Name", "EAuracronDamageType::TrueDamage" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDamageType::None", (int64)EAuracronDamageType::None },
		{ "EAuracronDamageType::Physical", (int64)EAuracronDamageType::Physical },
		{ "EAuracronDamageType::Magical", (int64)EAuracronDamageType::Magical },
		{ "EAuracronDamageType::TrueDamage", (int64)EAuracronDamageType::TrueDamage },
		{ "EAuracronDamageType::Healing", (int64)EAuracronDamageType::Healing },
		{ "EAuracronDamageType::Shield", (int64)EAuracronDamageType::Shield },
		{ "EAuracronDamageType::Percentage", (int64)EAuracronDamageType::Percentage },
		{ "EAuracronDamageType::OverTime", (int64)EAuracronDamageType::OverTime },
		{ "EAuracronDamageType::Area", (int64)EAuracronDamageType::Area },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronDamageType",
	"EAuracronDamageType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType()
{
	if (!Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDamageType.InnerSingleton;
}
// ********** End Enum EAuracronDamageType *********************************************************

// ********** Begin Enum EAuracronCombatLayer ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCombatLayer;
static UEnum* EAuracronCombatLayer_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronCombatLayer"));
	}
	return Z_Registration_Info_UEnum_EAuracronCombatLayer.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronCombatLayer>()
{
	return EAuracronCombatLayer_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All Layers" },
		{ "All.Name", "EAuracronCombatLayer::All" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de combate 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "Sky.DisplayName", "Sky Layer" },
		{ "Sky.Name", "EAuracronCombatLayer::Sky" },
		{ "Surface.DisplayName", "Surface Layer" },
		{ "Surface.Name", "EAuracronCombatLayer::Surface" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de combate 3D" },
#endif
		{ "Underground.DisplayName", "Underground Layer" },
		{ "Underground.Name", "EAuracronCombatLayer::Underground" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCombatLayer::Surface", (int64)EAuracronCombatLayer::Surface },
		{ "EAuracronCombatLayer::Sky", (int64)EAuracronCombatLayer::Sky },
		{ "EAuracronCombatLayer::Underground", (int64)EAuracronCombatLayer::Underground },
		{ "EAuracronCombatLayer::All", (int64)EAuracronCombatLayer::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronCombatLayer",
	"EAuracronCombatLayer",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer()
{
	if (!Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCombatLayer.InnerSingleton;
}
// ********** End Enum EAuracronCombatLayer ********************************************************

// ********** Begin Enum EAuracronTargetingType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronTargetingType;
static UEnum* EAuracronTargetingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronTargetingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronTargetingType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronTargetingType>()
{
	return EAuracronTargetingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AreaOfEffect.DisplayName", "Area of Effect" },
		{ "AreaOfEffect.Name", "EAuracronTargetingType::AreaOfEffect" },
		{ "BlueprintType", "true" },
		{ "Box.DisplayName", "Box" },
		{ "Box.Name", "EAuracronTargetingType::Box" },
		{ "ChainTarget.DisplayName", "Chain Target" },
		{ "ChainTarget.Name", "EAuracronTargetingType::ChainTarget" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de targeting\n */" },
#endif
		{ "Cone.DisplayName", "Cone" },
		{ "Cone.Name", "EAuracronTargetingType::Cone" },
		{ "CrossLayer.DisplayName", "Cross Layer" },
		{ "CrossLayer.Name", "EAuracronTargetingType::CrossLayer" },
		{ "Cylinder.DisplayName", "Cylinder" },
		{ "Cylinder.Name", "EAuracronTargetingType::Cylinder" },
		{ "GroundTarget.DisplayName", "Ground Target" },
		{ "GroundTarget.Name", "EAuracronTargetingType::GroundTarget" },
		{ "LineTrace.DisplayName", "Line Trace" },
		{ "LineTrace.Name", "EAuracronTargetingType::LineTrace" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronTargetingType::None" },
		{ "PredictiveTarget.DisplayName", "Predictive Target" },
		{ "PredictiveTarget.Name", "EAuracronTargetingType::PredictiveTarget" },
		{ "SingleTarget.DisplayName", "Single Target" },
		{ "SingleTarget.Name", "EAuracronTargetingType::SingleTarget" },
		{ "SkyTarget.DisplayName", "Sky Target" },
		{ "SkyTarget.Name", "EAuracronTargetingType::SkyTarget" },
		{ "SmartTarget.DisplayName", "Smart Target" },
		{ "SmartTarget.Name", "EAuracronTargetingType::SmartTarget" },
		{ "Sphere.DisplayName", "Sphere" },
		{ "Sphere.Name", "EAuracronTargetingType::Sphere" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de targeting" },
#endif
		{ "UndergroundTarget.DisplayName", "Underground Target" },
		{ "UndergroundTarget.Name", "EAuracronTargetingType::UndergroundTarget" },
		{ "VerticalColumn.DisplayName", "Vertical Column" },
		{ "VerticalColumn.Name", "EAuracronTargetingType::VerticalColumn" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronTargetingType::None", (int64)EAuracronTargetingType::None },
		{ "EAuracronTargetingType::SingleTarget", (int64)EAuracronTargetingType::SingleTarget },
		{ "EAuracronTargetingType::LineTrace", (int64)EAuracronTargetingType::LineTrace },
		{ "EAuracronTargetingType::AreaOfEffect", (int64)EAuracronTargetingType::AreaOfEffect },
		{ "EAuracronTargetingType::Cone", (int64)EAuracronTargetingType::Cone },
		{ "EAuracronTargetingType::Sphere", (int64)EAuracronTargetingType::Sphere },
		{ "EAuracronTargetingType::Box", (int64)EAuracronTargetingType::Box },
		{ "EAuracronTargetingType::Cylinder", (int64)EAuracronTargetingType::Cylinder },
		{ "EAuracronTargetingType::VerticalColumn", (int64)EAuracronTargetingType::VerticalColumn },
		{ "EAuracronTargetingType::CrossLayer", (int64)EAuracronTargetingType::CrossLayer },
		{ "EAuracronTargetingType::GroundTarget", (int64)EAuracronTargetingType::GroundTarget },
		{ "EAuracronTargetingType::SkyTarget", (int64)EAuracronTargetingType::SkyTarget },
		{ "EAuracronTargetingType::UndergroundTarget", (int64)EAuracronTargetingType::UndergroundTarget },
		{ "EAuracronTargetingType::SmartTarget", (int64)EAuracronTargetingType::SmartTarget },
		{ "EAuracronTargetingType::PredictiveTarget", (int64)EAuracronTargetingType::PredictiveTarget },
		{ "EAuracronTargetingType::ChainTarget", (int64)EAuracronTargetingType::ChainTarget },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronTargetingType",
	"EAuracronTargetingType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronTargetingType.InnerSingleton;
}
// ********** End Enum EAuracronTargetingType ******************************************************

// ********** Begin Enum EAuracronElementalType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronElementalType;
static UEnum* EAuracronElementalType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronElementalType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronElementalType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronElementalType"));
	}
	return Z_Registration_Info_UEnum_EAuracronElementalType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronElementalType>()
{
	return EAuracronElementalType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Air.DisplayName", "Air" },
		{ "Air.Name", "EAuracronElementalType::Air" },
		{ "BlueprintType", "true" },
		{ "Chaos.DisplayName", "Chaos" },
		{ "Chaos.Name", "EAuracronElementalType::Chaos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Elemental damage types for advanced combat\n */" },
#endif
		{ "Earth.DisplayName", "Earth" },
		{ "Earth.Name", "EAuracronElementalType::Earth" },
		{ "Fire.DisplayName", "Fire" },
		{ "Fire.Name", "EAuracronElementalType::Fire" },
		{ "Ice.DisplayName", "Ice" },
		{ "Ice.Name", "EAuracronElementalType::Ice" },
		{ "Light.DisplayName", "Light" },
		{ "Light.Name", "EAuracronElementalType::Light" },
		{ "Lightning.DisplayName", "Lightning" },
		{ "Lightning.Name", "EAuracronElementalType::Lightning" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronElementalType::None" },
		{ "Order.DisplayName", "Order" },
		{ "Order.Name", "EAuracronElementalType::Order" },
		{ "Poison.DisplayName", "Poison" },
		{ "Poison.Name", "EAuracronElementalType::Poison" },
		{ "Shadow.DisplayName", "Shadow" },
		{ "Shadow.Name", "EAuracronElementalType::Shadow" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental damage types for advanced combat" },
#endif
		{ "Void.DisplayName", "Void" },
		{ "Void.Name", "EAuracronElementalType::Void" },
		{ "Water.DisplayName", "Water" },
		{ "Water.Name", "EAuracronElementalType::Water" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronElementalType::None", (int64)EAuracronElementalType::None },
		{ "EAuracronElementalType::Fire", (int64)EAuracronElementalType::Fire },
		{ "EAuracronElementalType::Water", (int64)EAuracronElementalType::Water },
		{ "EAuracronElementalType::Earth", (int64)EAuracronElementalType::Earth },
		{ "EAuracronElementalType::Air", (int64)EAuracronElementalType::Air },
		{ "EAuracronElementalType::Lightning", (int64)EAuracronElementalType::Lightning },
		{ "EAuracronElementalType::Ice", (int64)EAuracronElementalType::Ice },
		{ "EAuracronElementalType::Poison", (int64)EAuracronElementalType::Poison },
		{ "EAuracronElementalType::Shadow", (int64)EAuracronElementalType::Shadow },
		{ "EAuracronElementalType::Light", (int64)EAuracronElementalType::Light },
		{ "EAuracronElementalType::Chaos", (int64)EAuracronElementalType::Chaos },
		{ "EAuracronElementalType::Order", (int64)EAuracronElementalType::Order },
		{ "EAuracronElementalType::Void", (int64)EAuracronElementalType::Void },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronElementalType",
	"EAuracronElementalType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType()
{
	if (!Z_Registration_Info_UEnum_EAuracronElementalType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronElementalType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronElementalType.InnerSingleton;
}
// ********** End Enum EAuracronElementalType ******************************************************

// ********** Begin Enum EAuracronAICombatBehavior *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAICombatBehavior;
static UEnum* EAuracronAICombatBehavior_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAICombatBehavior.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAICombatBehavior.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronAICombatBehavior"));
	}
	return Z_Registration_Info_UEnum_EAuracronAICombatBehavior.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronAICombatBehavior>()
{
	return EAuracronAICombatBehavior_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronAICombatBehavior::Adaptive" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAuracronAICombatBehavior::Aggressive" },
		{ "Assassin.DisplayName", "Assassin" },
		{ "Assassin.Name", "EAuracronAICombatBehavior::Assassin" },
		{ "Berserker.DisplayName", "Berserker" },
		{ "Berserker.Name", "EAuracronAICombatBehavior::Berserker" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AI Combat behavior types\n */" },
#endif
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "EAuracronAICombatBehavior::Defensive" },
		{ "Learning.DisplayName", "Learning" },
		{ "Learning.Name", "EAuracronAICombatBehavior::Learning" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "Passive.DisplayName", "Passive" },
		{ "Passive.Name", "EAuracronAICombatBehavior::Passive" },
		{ "Support.DisplayName", "Support" },
		{ "Support.Name", "EAuracronAICombatBehavior::Support" },
		{ "Tactical.DisplayName", "Tactical" },
		{ "Tactical.Name", "EAuracronAICombatBehavior::Tactical" },
		{ "Tank.DisplayName", "Tank" },
		{ "Tank.Name", "EAuracronAICombatBehavior::Tank" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Combat behavior types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAICombatBehavior::Passive", (int64)EAuracronAICombatBehavior::Passive },
		{ "EAuracronAICombatBehavior::Defensive", (int64)EAuracronAICombatBehavior::Defensive },
		{ "EAuracronAICombatBehavior::Aggressive", (int64)EAuracronAICombatBehavior::Aggressive },
		{ "EAuracronAICombatBehavior::Tactical", (int64)EAuracronAICombatBehavior::Tactical },
		{ "EAuracronAICombatBehavior::Berserker", (int64)EAuracronAICombatBehavior::Berserker },
		{ "EAuracronAICombatBehavior::Support", (int64)EAuracronAICombatBehavior::Support },
		{ "EAuracronAICombatBehavior::Assassin", (int64)EAuracronAICombatBehavior::Assassin },
		{ "EAuracronAICombatBehavior::Tank", (int64)EAuracronAICombatBehavior::Tank },
		{ "EAuracronAICombatBehavior::Adaptive", (int64)EAuracronAICombatBehavior::Adaptive },
		{ "EAuracronAICombatBehavior::Learning", (int64)EAuracronAICombatBehavior::Learning },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronAICombatBehavior",
	"EAuracronAICombatBehavior",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior()
{
	if (!Z_Registration_Info_UEnum_EAuracronAICombatBehavior.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAICombatBehavior.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAICombatBehavior.InnerSingleton;
}
// ********** End Enum EAuracronAICombatBehavior ***************************************************

// ********** Begin Enum EAuracronComboType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronComboType;
static UEnum* EAuracronComboType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronComboType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronComboType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("EAuracronComboType"));
	}
	return Z_Registration_Info_UEnum_EAuracronComboType.OuterSingleton;
}
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronComboType>()
{
	return EAuracronComboType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chain.DisplayName", "Chain Combo" },
		{ "Chain.Name", "EAuracronComboType::Chain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Combat combo types\n */" },
#endif
		{ "Elemental.DisplayName", "Elemental Combo" },
		{ "Elemental.Name", "EAuracronComboType::Elemental" },
		{ "Finisher.DisplayName", "Finisher Combo" },
		{ "Finisher.Name", "EAuracronComboType::Finisher" },
		{ "Heavy.DisplayName", "Heavy Combo" },
		{ "Heavy.Name", "EAuracronComboType::Heavy" },
		{ "Light.DisplayName", "Light Combo" },
		{ "Light.Name", "EAuracronComboType::Light" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronComboType::None" },
		{ "Special.DisplayName", "Special Combo" },
		{ "Special.Name", "EAuracronComboType::Special" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat combo types" },
#endif
		{ "Ultimate.DisplayName", "Ultimate Combo" },
		{ "Ultimate.Name", "EAuracronComboType::Ultimate" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronComboType::None", (int64)EAuracronComboType::None },
		{ "EAuracronComboType::Light", (int64)EAuracronComboType::Light },
		{ "EAuracronComboType::Heavy", (int64)EAuracronComboType::Heavy },
		{ "EAuracronComboType::Special", (int64)EAuracronComboType::Special },
		{ "EAuracronComboType::Ultimate", (int64)EAuracronComboType::Ultimate },
		{ "EAuracronComboType::Elemental", (int64)EAuracronComboType::Elemental },
		{ "EAuracronComboType::Chain", (int64)EAuracronComboType::Chain },
		{ "EAuracronComboType::Finisher", (int64)EAuracronComboType::Finisher },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	"EAuracronComboType",
	"EAuracronComboType",
	Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType()
{
	if (!Z_Registration_Info_UEnum_EAuracronComboType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronComboType.InnerSingleton, Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronComboType.InnerSingleton;
}
// ********** End Enum EAuracronComboType **********************************************************

// ********** Begin ScriptStruct FAuracronDamageConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration;
class UScriptStruct* FAuracronDamageConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDamageConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronDamageConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageType_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDamage_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor base do dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor base do dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamageScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com Attack Damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com Attack Damage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityPowerScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com Ability Power */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com Ability Power" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealthScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com HP m\xc3\x83\xc2\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com HP m\xc3\x83\xc2\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealthScaling_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escalonamento com HP atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escalonamento com HP atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanCrit_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode causar cr\xc3\x83\xc2\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode causar cr\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresArmor_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora armadura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora armadura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresMagicResistance_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora resist\xc3\x83\xc2\xaancia m\xc3\x83\xc2\xa1gica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora resist\xc3\x83\xc2\xaancia m\xc3\x83\xc2\xa1gica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPenetratesShields_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Penetra shields */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Penetra shields" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDamageOverTime_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano ao longo do tempo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano ao longo do tempo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DotDuration_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do DoT (se aplic\xc3\x83\xc2\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do DoT (se aplic\xc3\x83\xc2\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DotInterval_MetaData[] = {
		{ "Category", "Damage Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo do DoT (se aplic\xc3\x83\xc2\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo do DoT (se aplic\xc3\x83\xc2\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedLayers_MetaData[] = {
		{ "Category", "Damage Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camadas afetadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camadas afetadas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DamageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DamageType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamageScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityPowerScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealthScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealthScaling;
	static void NewProp_bCanCrit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanCrit;
	static void NewProp_bIgnoresArmor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresArmor;
	static void NewProp_bIgnoresMagicResistance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresMagicResistance;
	static void NewProp_bPenetratesShields_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPenetratesShields;
	static void NewProp_bDamageOverTime_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDamageOverTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DotDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DotInterval;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectedLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectedLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedLayers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDamageConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType = { "DamageType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DamageType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageType_MetaData), NewProp_DamageType_MetaData) }; // 2470854689
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_BaseDamage = { "BaseDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, BaseDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDamage_MetaData), NewProp_BaseDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AttackDamageScaling = { "AttackDamageScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AttackDamageScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamageScaling_MetaData), NewProp_AttackDamageScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AbilityPowerScaling = { "AbilityPowerScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AbilityPowerScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityPowerScaling_MetaData), NewProp_AbilityPowerScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_MaxHealthScaling = { "MaxHealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, MaxHealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealthScaling_MetaData), NewProp_MaxHealthScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_CurrentHealthScaling = { "CurrentHealthScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, CurrentHealthScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealthScaling_MetaData), NewProp_CurrentHealthScaling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bCanCrit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit = { "bCanCrit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanCrit_MetaData), NewProp_bCanCrit_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bIgnoresArmor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor = { "bIgnoresArmor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresArmor_MetaData), NewProp_bIgnoresArmor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bIgnoresMagicResistance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance = { "bIgnoresMagicResistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresMagicResistance_MetaData), NewProp_bIgnoresMagicResistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bPenetratesShields = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields = { "bPenetratesShields", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPenetratesShields_MetaData), NewProp_bPenetratesShields_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime_SetBit(void* Obj)
{
	((FAuracronDamageConfiguration*)Obj)->bDamageOverTime = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime = { "bDamageOverTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDamageConfiguration), &Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDamageOverTime_MetaData), NewProp_bDamageOverTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotDuration = { "DotDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DotDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DotDuration_MetaData), NewProp_DotDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotInterval = { "DotInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, DotInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DotInterval_MetaData), NewProp_DotInterval_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner = { "AffectedLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers = { "AffectedLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDamageConfiguration, AffectedLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedLayers_MetaData), NewProp_AffectedLayers_MetaData) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DamageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_BaseDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AttackDamageScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AbilityPowerScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_MaxHealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_CurrentHealthScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bCanCrit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresArmor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bIgnoresMagicResistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bPenetratesShields,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_bDamageOverTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_DotInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewProp_AffectedLayers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronDamageConfiguration",
	Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::PropPointers),
	sizeof(FAuracronDamageConfiguration),
	alignof(FAuracronDamageConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDamageConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDamageConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronTargetingConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration;
class UScriptStruct* FAuracronTargetingConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronTargetingConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de targeting 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de targeting 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingType_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRange_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance m\xc3\x83\xc2\xa1ximo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance m\xc3\x83\xc2\xa1ximo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinRange_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance m\xc3\x83\xc2\xadnimo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance m\xc3\x83\xc2\xadnimo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffectRadius_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da \xc3\x83\xc2\xa1rea de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da \xc3\x83\xc2\xa1rea de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaOfEffectHeight_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura da \xc3\x83\xc2\xa1rea de efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura da \xc3\x83\xc2\xa1rea de efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConeAngle_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\x9angulo do cone (para targeting c\xc3\x83\xc2\xb4nico) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\x9angulo do cone (para targeting c\xc3\x83\xc2\xb4nico)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowCrossLayerTargeting_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite targeting atrav\xc3\x83\xc2\xa9s de camadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite targeting atrav\xc3\x83\xc2\xa9s de camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresLineOfSight_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Requer line of sight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requer line of sight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIgnoresObstacles_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ignora obst\xc3\x83\xc2\xa1""culos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ignora obst\xc3\x83\xc2\xa1""culos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTargetAlliesOnly_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting apenas aliados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting apenas aliados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTargetEnemiesOnly_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting apenas inimigos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting apenas inimigos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeSelf_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inclui o pr\xc3\x83\xc2\xb3prio caster */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inclui o pr\xc3\x83\xc2\xb3prio caster" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedTargetingLayers_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camadas de targeting permitidas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camadas de targeting permitidas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LineOfSightChannels_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais de colis\xc3\x83\xc2\xa3o para line of sight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais de colis\xc3\x83\xc2\xa3o para line of sight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingChannels_MetaData[] = {
		{ "Category", "Targeting Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Canais de colis\xc3\x83\xc2\xa3o para targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Canais de colis\xc3\x83\xc2\xa3o para targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalOffset_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "-1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Offset vertical para targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset vertical para targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingAccuracy_MetaData[] = {
		{ "Category", "Targeting Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Precis\xc3\x83\xc2\xa3o do targeting (0.0 = perfeito, 1.0 = muito impreciso) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Precis\xc3\x83\xc2\xa3o do targeting (0.0 = perfeito, 1.0 = muito impreciso)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetingType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffectRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AreaOfEffectHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConeAngle;
	static void NewProp_bAllowCrossLayerTargeting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowCrossLayerTargeting;
	static void NewProp_bRequiresLineOfSight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresLineOfSight;
	static void NewProp_bIgnoresObstacles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIgnoresObstacles;
	static void NewProp_bTargetAlliesOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTargetAlliesOnly;
	static void NewProp_bTargetEnemiesOnly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTargetEnemiesOnly;
	static void NewProp_bIncludeSelf_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeSelf;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AllowedTargetingLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AllowedTargetingLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedTargetingLayers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LineOfSightChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LineOfSightChannels;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetingChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetingChannels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VerticalOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetingAccuracy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTargetingConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType = { "TargetingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronTargetingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingType_MetaData), NewProp_TargetingType_MetaData) }; // 3374031875
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MaxRange = { "MaxRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, MaxRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRange_MetaData), NewProp_MaxRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MinRange = { "MinRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, MinRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinRange_MetaData), NewProp_MinRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectRadius = { "AreaOfEffectRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AreaOfEffectRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffectRadius_MetaData), NewProp_AreaOfEffectRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectHeight = { "AreaOfEffectHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AreaOfEffectHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaOfEffectHeight_MetaData), NewProp_AreaOfEffectHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_ConeAngle = { "ConeAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, ConeAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConeAngle_MetaData), NewProp_ConeAngle_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bAllowCrossLayerTargeting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting = { "bAllowCrossLayerTargeting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowCrossLayerTargeting_MetaData), NewProp_bAllowCrossLayerTargeting_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bRequiresLineOfSight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight = { "bRequiresLineOfSight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresLineOfSight_MetaData), NewProp_bRequiresLineOfSight_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bIgnoresObstacles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles = { "bIgnoresObstacles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIgnoresObstacles_MetaData), NewProp_bIgnoresObstacles_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bTargetAlliesOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly = { "bTargetAlliesOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTargetAlliesOnly_MetaData), NewProp_bTargetAlliesOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bTargetEnemiesOnly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly = { "bTargetEnemiesOnly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTargetEnemiesOnly_MetaData), NewProp_bTargetEnemiesOnly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf_SetBit(void* Obj)
{
	((FAuracronTargetingConfiguration*)Obj)->bIncludeSelf = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf = { "bIncludeSelf", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingConfiguration), &Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeSelf_MetaData), NewProp_bIncludeSelf_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner = { "AllowedTargetingLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers = { "AllowedTargetingLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, AllowedTargetingLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedTargetingLayers_MetaData), NewProp_AllowedTargetingLayers_MetaData) }; // 3955355270
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels_Inner = { "LineOfSightChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels = { "LineOfSightChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, LineOfSightChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LineOfSightChannels_MetaData), NewProp_LineOfSightChannels_MetaData) }; // 756624936
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels_Inner = { "TargetingChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels = { "TargetingChannels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingChannels_MetaData), NewProp_TargetingChannels_MetaData) }; // 756624936
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_VerticalOffset = { "VerticalOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, VerticalOffset), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalOffset_MetaData), NewProp_VerticalOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingAccuracy = { "TargetingAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingConfiguration, TargetingAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingAccuracy_MetaData), NewProp_TargetingAccuracy_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MaxRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_MinRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AreaOfEffectHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_ConeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bAllowCrossLayerTargeting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bRequiresLineOfSight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIgnoresObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetAlliesOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bTargetEnemiesOnly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_bIncludeSelf,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_AllowedTargetingLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_LineOfSightChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_VerticalOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewProp_TargetingAccuracy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronTargetingConfiguration",
	Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::PropPointers),
	sizeof(FAuracronTargetingConfiguration),
	alignof(FAuracronTargetingConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTargetingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronTargetingResult ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTargetingResult;
class UScriptStruct* FAuracronTargetingResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTargetingResult, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronTargetingResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para resultado de targeting\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para resultado de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccessful_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Targeting foi bem-sucedido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Targeting foi bem-sucedido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetActors_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atores alvo encontrados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atores alvo encontrados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocations_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es alvo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es alvo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitResults_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de hit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de hit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLayer_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camada onde o targeting ocorreu */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camada onde o targeting ocorreu" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToTarget_MetaData[] = {
		{ "Category", "Targeting Result" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia at\xc3\x83\xc2\xa9 o alvo principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia at\xc3\x83\xc2\xa9 o alvo principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AngleToTarget_MetaData[] = {
		{ "Category", "Targeting Result" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xe2\x80\x9angulo at\xc3\x83\xc2\xa9 o alvo principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xe2\x80\x9angulo at\xc3\x83\xc2\xa9 o alvo principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasLineOfSight_MetaData[] = {
		{ "Category", "Targeting Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tem line of sight para o alvo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tem line of sight para o alvo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccessful_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccessful;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TargetLocations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HitResults_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HitResults;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToTarget;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AngleToTarget;
	static void NewProp_bHasLineOfSight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasLineOfSight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTargetingResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful_SetBit(void* Obj)
{
	((FAuracronTargetingResult*)Obj)->bSuccessful = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful = { "bSuccessful", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingResult), &Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccessful_MetaData), NewProp_bSuccessful_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors_Inner = { "TargetActors", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors = { "TargetActors", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetActors_MetaData), NewProp_TargetActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations_Inner = { "TargetLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations = { "TargetLocations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocations_MetaData), NewProp_TargetLocations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults_Inner = { "HitResults", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(0, nullptr) }; // 267591329
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults = { "HitResults", nullptr, (EPropertyFlags)0x0010008000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, HitResults), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitResults_MetaData), NewProp_HitResults_MetaData) }; // 267591329
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, TargetLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLayer_MetaData), NewProp_TargetLayer_MetaData) }; // 3955355270
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_DistanceToTarget = { "DistanceToTarget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, DistanceToTarget), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToTarget_MetaData), NewProp_DistanceToTarget_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_AngleToTarget = { "AngleToTarget", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTargetingResult, AngleToTarget), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AngleToTarget_MetaData), NewProp_AngleToTarget_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight_SetBit(void* Obj)
{
	((FAuracronTargetingResult*)Obj)->bHasLineOfSight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight = { "bHasLineOfSight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTargetingResult), &Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasLineOfSight_MetaData), NewProp_bHasLineOfSight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bSuccessful,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_HitResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_DistanceToTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_AngleToTarget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewProp_bHasLineOfSight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronTargetingResult",
	Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::PropPointers),
	sizeof(FAuracronTargetingResult),
	alignof(FAuracronTargetingResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTargetingResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTargetingResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTargetingResult ********************************************

// ********** Begin ScriptStruct FAuracronCombatEffectsConfiguration *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration;
class UScriptStruct* FAuracronCombatEffectsConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronCombatEffectsConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de combate\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de impacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectileEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de proj\xc3\x83\xc2\xa9til */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de proj\xc3\x83\xc2\xa9til" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AreaEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de \xc3\x83\xc2\xa1rea */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de \xc3\x83\xc2\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de cura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de cura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldEffect_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de shield */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de shield" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactSound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de impacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectileSound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de proj\xc3\x83\xc2\xa9til */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de proj\xc3\x83\xc2\xa9til" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySound_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectDuration_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectScale_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectColor_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFieldSystemDestruction_MetaData[] = {
		{ "Category", "Combat Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemForce_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\x83\xc2\xa7""a do Field System */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\x83\xc2\xa7""a do Field System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemRadius_MetaData[] = {
		{ "Category", "Combat Effects" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do Field System */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do Field System" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProjectileEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AreaEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_HealingEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ShieldEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ProjectileSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilitySound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectColor;
	static void NewProp_bUseFieldSystemDestruction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFieldSystemDestruction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldSystemForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FieldSystemRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCombatEffectsConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactEffect = { "ImpactEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ImpactEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactEffect_MetaData), NewProp_ImpactEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileEffect = { "ProjectileEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ProjectileEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectileEffect_MetaData), NewProp_ProjectileEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AreaEffect = { "AreaEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, AreaEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AreaEffect_MetaData), NewProp_AreaEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_HealingEffect = { "HealingEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, HealingEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingEffect_MetaData), NewProp_HealingEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ShieldEffect = { "ShieldEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ShieldEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldEffect_MetaData), NewProp_ShieldEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactSound = { "ImpactSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ImpactSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactSound_MetaData), NewProp_ImpactSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileSound = { "ProjectileSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, ProjectileSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectileSound_MetaData), NewProp_ProjectileSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AbilitySound = { "AbilitySound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, AbilitySound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySound_MetaData), NewProp_AbilitySound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectDuration = { "EffectDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectDuration_MetaData), NewProp_EffectDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectScale = { "EffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectScale_MetaData), NewProp_EffectScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectColor = { "EffectColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, EffectColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectColor_MetaData), NewProp_EffectColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction_SetBit(void* Obj)
{
	((FAuracronCombatEffectsConfiguration*)Obj)->bUseFieldSystemDestruction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction = { "bUseFieldSystemDestruction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCombatEffectsConfiguration), &Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFieldSystemDestruction_MetaData), NewProp_bUseFieldSystemDestruction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemForce = { "FieldSystemForce", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, FieldSystemForce), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemForce_MetaData), NewProp_FieldSystemForce_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemRadius = { "FieldSystemRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatEffectsConfiguration, FieldSystemRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemRadius_MetaData), NewProp_FieldSystemRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AreaEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_HealingEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ShieldEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ImpactSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_ProjectileSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_AbilitySound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_EffectColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_bUseFieldSystemDestruction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewProp_FieldSystemRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronCombatEffectsConfiguration",
	Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::PropPointers),
	sizeof(FAuracronCombatEffectsConfiguration),
	alignof(FAuracronCombatEffectsConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCombatEffectsConfiguration *********************************

// ********** Begin ScriptStruct FAuracronEnhancedInputConfig **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig;
class UScriptStruct* FAuracronEnhancedInputConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronEnhancedInputConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced Input combat action configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input combat action configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BasicAttackAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Action for basic attack */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Action for basic attack" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeavyAttackAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Action for heavy attack */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Action for heavy attack" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialAbilityAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Action for special ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Action for special ability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DodgeAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Action for dodge/dash */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Action for dodge/dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlockAction_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Action for block/parry */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Action for block/parry" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatInputContext_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input Mapping Context */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Mapping Context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboWindowTime_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combo window timing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combo window timing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxComboChain_MetaData[] = {
		{ "Category", "Enhanced Input" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum combo chain length */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum combo chain length" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BasicAttackAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_HeavyAttackAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SpecialAbilityAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DodgeAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BlockAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CombatInputContext;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ComboWindowTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxComboChain;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronEnhancedInputConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_BasicAttackAction = { "BasicAttackAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, BasicAttackAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BasicAttackAction_MetaData), NewProp_BasicAttackAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_HeavyAttackAction = { "HeavyAttackAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, HeavyAttackAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeavyAttackAction_MetaData), NewProp_HeavyAttackAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_SpecialAbilityAction = { "SpecialAbilityAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, SpecialAbilityAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialAbilityAction_MetaData), NewProp_SpecialAbilityAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_DodgeAction = { "DodgeAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, DodgeAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DodgeAction_MetaData), NewProp_DodgeAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_BlockAction = { "BlockAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, BlockAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlockAction_MetaData), NewProp_BlockAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_CombatInputContext = { "CombatInputContext", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, CombatInputContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatInputContext_MetaData), NewProp_CombatInputContext_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_ComboWindowTime = { "ComboWindowTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, ComboWindowTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboWindowTime_MetaData), NewProp_ComboWindowTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_MaxComboChain = { "MaxComboChain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronEnhancedInputConfig, MaxComboChain), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxComboChain_MetaData), NewProp_MaxComboChain_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_BasicAttackAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_HeavyAttackAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_SpecialAbilityAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_DodgeAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_BlockAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_CombatInputContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_ComboWindowTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewProp_MaxComboChain,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronEnhancedInputConfig",
	Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::PropPointers),
	sizeof(FAuracronEnhancedInputConfig),
	alignof(FAuracronEnhancedInputConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronEnhancedInputConfig ****************************************

// ********** Begin ScriptStruct FAuracronAICombatConfig *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig;
class UScriptStruct* FAuracronAICombatConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAICombatConfig, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronAICombatConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AI Combat behavior configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Combat behavior configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorType_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** AI Behavior type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Behavior type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTree_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Behavior Tree asset */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Tree asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StateTree_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** State Tree asset */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State Tree asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AggressionLevel_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aggression level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aggression level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReactionTime_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reaction time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reaction time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredCombatRange_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combat range preference */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat range preference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLearning_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable learning behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable learning behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LearningRate_MetaData[] = {
		{ "Category", "AI Combat" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.01" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Learning adaptation rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Learning adaptation rate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BehaviorTree;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StateTree;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AggressionLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReactionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PreferredCombatRange;
	static void NewProp_bEnableLearning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLearning;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LearningRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAICombatConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, BehaviorType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorType_MetaData), NewProp_BehaviorType_MetaData) }; // 2928806894
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorTree = { "BehaviorTree", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, BehaviorTree), Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTree_MetaData), NewProp_BehaviorTree_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_StateTree = { "StateTree", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, StateTree), Z_Construct_UClass_UStateTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StateTree_MetaData), NewProp_StateTree_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_AggressionLevel = { "AggressionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, AggressionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AggressionLevel_MetaData), NewProp_AggressionLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_ReactionTime = { "ReactionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, ReactionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReactionTime_MetaData), NewProp_ReactionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_PreferredCombatRange = { "PreferredCombatRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, PreferredCombatRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredCombatRange_MetaData), NewProp_PreferredCombatRange_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_bEnableLearning_SetBit(void* Obj)
{
	((FAuracronAICombatConfig*)Obj)->bEnableLearning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_bEnableLearning = { "bEnableLearning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAICombatConfig), &Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_bEnableLearning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLearning_MetaData), NewProp_bEnableLearning_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_LearningRate = { "LearningRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAICombatConfig, LearningRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LearningRate_MetaData), NewProp_LearningRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_BehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_StateTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_AggressionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_ReactionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_PreferredCombatRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_bEnableLearning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewProp_LearningRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronAICombatConfig",
	Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::PropPointers),
	sizeof(FAuracronAICombatConfig),
	alignof(FAuracronAICombatConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAICombatConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAICombatConfig *********************************************

// ********** Begin ScriptStruct FAuracronElementalDamageConfig ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig;
class UScriptStruct* FAuracronElementalDamageConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronElementalDamageConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Elemental damage configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental damage configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryElement_MetaData[] = {
		{ "Category", "Elemental Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Primary elemental type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Primary elemental type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryElement_MetaData[] = {
		{ "Category", "Elemental Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Secondary elemental type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Secondary elemental type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalMultiplier_MetaData[] = {
		{ "Category", "Elemental Damage" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemental damage multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental damage multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalResistances_MetaData[] = {
		{ "Category", "Elemental Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemental resistances */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental resistances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalWeaknesses_MetaData[] = {
		{ "Category", "Elemental Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemental weaknesses */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental weaknesses" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatusEffectChance_MetaData[] = {
		{ "Category", "Elemental Damage" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status effect chance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status effect chance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatusEffectDuration_MetaData[] = {
		{ "Category", "Elemental Damage" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Status effect duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status effect duration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PrimaryElement_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PrimaryElement;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SecondaryElement_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SecondaryElement;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElementalMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElementalResistances_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementalResistances_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementalResistances_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ElementalResistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElementalWeaknesses_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementalWeaknesses_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementalWeaknesses_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ElementalWeaknesses;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StatusEffectChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StatusEffectDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronElementalDamageConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_PrimaryElement_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_PrimaryElement = { "PrimaryElement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, PrimaryElement), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryElement_MetaData), NewProp_PrimaryElement_MetaData) }; // 2822706140
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_SecondaryElement_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_SecondaryElement = { "SecondaryElement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, SecondaryElement), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryElement_MetaData), NewProp_SecondaryElement_MetaData) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalMultiplier = { "ElementalMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, ElementalMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalMultiplier_MetaData), NewProp_ElementalMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_ValueProp = { "ElementalResistances", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_Key_KeyProp = { "ElementalResistances_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances = { "ElementalResistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, ElementalResistances), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalResistances_MetaData), NewProp_ElementalResistances_MetaData) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_ValueProp = { "ElementalWeaknesses", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_Key_KeyProp = { "ElementalWeaknesses_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses = { "ElementalWeaknesses", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, ElementalWeaknesses), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalWeaknesses_MetaData), NewProp_ElementalWeaknesses_MetaData) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_StatusEffectChance = { "StatusEffectChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, StatusEffectChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatusEffectChance_MetaData), NewProp_StatusEffectChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_StatusEffectDuration = { "StatusEffectDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronElementalDamageConfig, StatusEffectDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatusEffectDuration_MetaData), NewProp_StatusEffectDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_PrimaryElement_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_PrimaryElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_SecondaryElement_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_SecondaryElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalResistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_ElementalWeaknesses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_StatusEffectChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewProp_StatusEffectDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronElementalDamageConfig",
	Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::PropPointers),
	sizeof(FAuracronElementalDamageConfig),
	alignof(FAuracronElementalDamageConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronElementalDamageConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronElementalDamageConfig **************************************

// ********** Begin ScriptStruct FAuracronCombatAnalytics ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics;
class UScriptStruct* FAuracronCombatAnalytics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCombatAnalytics, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronCombatAnalytics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Combat analytics data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat analytics data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalDamageDealt_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total damage dealt */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total damage dealt" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalDamageReceived_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total damage received */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total damage received" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitsLanded_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of hits landed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of hits landed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HitsMissed_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of hits missed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of hits missed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalHits_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of critical hits */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of critical hits" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlocksParries_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of blocks/parries */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of blocks/parries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Dodges_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of dodges */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of dodges" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LongestCombo_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Longest combo achieved */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Longest combo achieved" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageReactionTime_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average reaction time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average reaction time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EfficiencyScore_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combat efficiency score */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat efficiency score" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Combat Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp of last update */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp of last update" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalDamageDealt;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalDamageReceived;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HitsLanded;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HitsMissed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CriticalHits;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BlocksParries;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Dodges;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LongestCombo;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageReactionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EfficiencyScore;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCombatAnalytics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_TotalDamageDealt = { "TotalDamageDealt", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, TotalDamageDealt), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalDamageDealt_MetaData), NewProp_TotalDamageDealt_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_TotalDamageReceived = { "TotalDamageReceived", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, TotalDamageReceived), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalDamageReceived_MetaData), NewProp_TotalDamageReceived_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_HitsLanded = { "HitsLanded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, HitsLanded), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitsLanded_MetaData), NewProp_HitsLanded_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_HitsMissed = { "HitsMissed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, HitsMissed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HitsMissed_MetaData), NewProp_HitsMissed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_CriticalHits = { "CriticalHits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, CriticalHits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalHits_MetaData), NewProp_CriticalHits_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_BlocksParries = { "BlocksParries", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, BlocksParries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlocksParries_MetaData), NewProp_BlocksParries_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_Dodges = { "Dodges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, Dodges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Dodges_MetaData), NewProp_Dodges_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_LongestCombo = { "LongestCombo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, LongestCombo), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LongestCombo_MetaData), NewProp_LongestCombo_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_AverageReactionTime = { "AverageReactionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, AverageReactionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageReactionTime_MetaData), NewProp_AverageReactionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_EfficiencyScore = { "EfficiencyScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, EfficiencyScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EfficiencyScore_MetaData), NewProp_EfficiencyScore_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCombatAnalytics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_TotalDamageDealt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_TotalDamageReceived,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_HitsLanded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_HitsMissed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_CriticalHits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_BlocksParries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_Dodges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_LongestCombo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_AverageReactionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_EfficiencyScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronCombatAnalytics",
	Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::PropPointers),
	sizeof(FAuracronCombatAnalytics),
	alignof(FAuracronCombatAnalytics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatAnalytics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCombatAnalytics ********************************************

// ********** Begin ScriptStruct FAuracronComboConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronComboConfig;
class UScriptStruct* FAuracronComboConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronComboConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronComboConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronComboConfig, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronComboConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronComboConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronComboConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Combat combo configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat combo configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboType_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combo type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combo type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSequence_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input sequence for combo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input sequence for combo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimingWindows_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timing windows for each input */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timing windows for each input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultipliers_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Damage multipliers for each hit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Damage multipliers for each hit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboEffects_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Effects for each hit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effects for each hit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboSounds_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sounds for each hit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sounds for each hit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredComboPoints_MetaData[] = {
		{ "Category", "Combo System" },
		{ "ClampMax", "100" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required combo points */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required combo points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboCooldown_MetaData[] = {
		{ "Category", "Combo System" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combo cooldown */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combo cooldown" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ComboType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ComboType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InputSequence_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSequence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimingWindows_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TimingWindows;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultipliers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DamageMultipliers;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ComboEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ComboEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ComboSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ComboSounds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredComboPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ComboCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronComboConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboType = { "ComboType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, ComboType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboType_MetaData), NewProp_ComboType_MetaData) }; // 916783320
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_InputSequence_Inner = { "InputSequence", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_InputSequence = { "InputSequence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, InputSequence), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSequence_MetaData), NewProp_InputSequence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_TimingWindows_Inner = { "TimingWindows", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_TimingWindows = { "TimingWindows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, TimingWindows), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimingWindows_MetaData), NewProp_TimingWindows_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_DamageMultipliers_Inner = { "DamageMultipliers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_DamageMultipliers = { "DamageMultipliers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, DamageMultipliers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultipliers_MetaData), NewProp_DamageMultipliers_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboEffects_Inner = { "ComboEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboEffects = { "ComboEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, ComboEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboEffects_MetaData), NewProp_ComboEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboSounds_Inner = { "ComboSounds", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboSounds = { "ComboSounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, ComboSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboSounds_MetaData), NewProp_ComboSounds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_RequiredComboPoints = { "RequiredComboPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, RequiredComboPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredComboPoints_MetaData), NewProp_RequiredComboPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboCooldown = { "ComboCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronComboConfig, ComboCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboCooldown_MetaData), NewProp_ComboCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_InputSequence_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_InputSequence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_TimingWindows_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_TimingWindows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_DamageMultipliers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_DamageMultipliers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_RequiredComboPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewProp_ComboCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronComboConfig",
	Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::PropPointers),
	sizeof(FAuracronComboConfig),
	alignof(FAuracronComboConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronComboConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronComboConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronComboConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronComboConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronComboConfig ************************************************

// ********** Begin ScriptStruct FAuracronAdvancedDestructionConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig;
class UScriptStruct* FAuracronAdvancedDestructionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, (UObject*)Z_Construct_UPackage__Script_AuracronCombatBridge(), TEXT("AuracronAdvancedDestructionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Advanced physics destruction configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced physics destruction configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableChaosDestruction_MetaData[] = {
		{ "Category", "Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Chaos destruction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Chaos destruction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionThreshold_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Destruction threshold */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destruction threshold" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FractureImpulse_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fracture impulse */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fracture impulse" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebrisLifetime_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Debris lifetime */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debris lifetime" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProceduralDamage_MetaData[] = {
		{ "Category", "Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable procedural damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable procedural damage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamagePropagationRadius_MetaData[] = {
		{ "Category", "Advanced Destruction" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Damage propagation radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Damage propagation radius" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableChaosDestruction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableChaosDestruction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FractureImpulse;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DebrisLifetime;
	static void NewProp_bEnableProceduralDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProceduralDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamagePropagationRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAdvancedDestructionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableChaosDestruction_SetBit(void* Obj)
{
	((FAuracronAdvancedDestructionConfig*)Obj)->bEnableChaosDestruction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableChaosDestruction = { "bEnableChaosDestruction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedDestructionConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableChaosDestruction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableChaosDestruction_MetaData), NewProp_bEnableChaosDestruction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DestructionThreshold = { "DestructionThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedDestructionConfig, DestructionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionThreshold_MetaData), NewProp_DestructionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_FractureImpulse = { "FractureImpulse", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedDestructionConfig, FractureImpulse), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FractureImpulse_MetaData), NewProp_FractureImpulse_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DebrisLifetime = { "DebrisLifetime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedDestructionConfig, DebrisLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebrisLifetime_MetaData), NewProp_DebrisLifetime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableProceduralDamage_SetBit(void* Obj)
{
	((FAuracronAdvancedDestructionConfig*)Obj)->bEnableProceduralDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableProceduralDamage = { "bEnableProceduralDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedDestructionConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableProceduralDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProceduralDamage_MetaData), NewProp_bEnableProceduralDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DamagePropagationRadius = { "DamagePropagationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedDestructionConfig, DamagePropagationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamagePropagationRadius_MetaData), NewProp_DamagePropagationRadius_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableChaosDestruction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DestructionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_FractureImpulse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DebrisLifetime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_bEnableProceduralDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewProp_DamagePropagationRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
	nullptr,
	&NewStructOps,
	"AuracronAdvancedDestructionConfig",
	Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::PropPointers),
	sizeof(FAuracronAdvancedDestructionConfig),
	alignof(FAuracronAdvancedDestructionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAdvancedDestructionConfig **********************************

// ********** Begin Delegate FOnTargetingExecuted **************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnTargetingExecuted_Parms
	{
		FAuracronTargetingResult Result;
		FAuracronTargetingConfiguration Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando targeting \xc3\x83\xc2\xa9 executado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando targeting \xc3\x83\xc2\xa9 executado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010008000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnTargetingExecuted_Parms, Result), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(0, nullptr) }; // 282061631
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnTargetingExecuted_Parms, Config), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(0, nullptr) }; // 2989207853
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnTargetingExecuted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnTargetingExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnTargetingExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnTargetingExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnTargetingExecuted, FAuracronTargetingResult Result, FAuracronTargetingConfiguration Config)
{
	struct AuracronCombatBridge_eventOnTargetingExecuted_Parms
	{
		FAuracronTargetingResult Result;
		FAuracronTargetingConfiguration Config;
	};
	AuracronCombatBridge_eventOnTargetingExecuted_Parms Parms;
	Parms.Result=Result;
	Parms.Config=Config;
	OnTargetingExecuted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnTargetingExecuted ****************************************************

// ********** Begin Delegate FOnDamageApplied ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnDamageApplied_Parms
	{
		AActor* TargetActor;
		float DamageAmount;
		EAuracronDamageType DamageType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando dano \xc3\x83\xc2\xa9 aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando dano \xc3\x83\xc2\xa9 aplicado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DamageType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DamageType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType = { "DamageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnDamageApplied_Parms, DamageType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronDamageType, METADATA_PARAMS(0, nullptr) }; // 2470854689
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::NewProp_DamageType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnDamageApplied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnDamageApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnDamageApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnDamageApplied, AActor* TargetActor, float DamageAmount, EAuracronDamageType DamageType)
{
	struct AuracronCombatBridge_eventOnDamageApplied_Parms
	{
		AActor* TargetActor;
		float DamageAmount;
		EAuracronDamageType DamageType;
	};
	AuracronCombatBridge_eventOnDamageApplied_Parms Parms;
	Parms.TargetActor=TargetActor;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageType=DamageType;
	OnDamageApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDamageApplied ********************************************************

// ********** Begin Delegate FOnCombatLayerChanged *************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnCombatLayerChanged_Parms
	{
		EAuracronCombatLayer OldLayer;
		EAuracronCombatLayer NewLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando camada de combate muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando camada de combate muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer = { "OldLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatLayerChanged_Parms, OldLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatLayerChanged_Parms, NewLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_OldLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnCombatLayerChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatLayerChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatLayerChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnCombatLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCombatLayerChanged, EAuracronCombatLayer OldLayer, EAuracronCombatLayer NewLayer)
{
	struct AuracronCombatBridge_eventOnCombatLayerChanged_Parms
	{
		EAuracronCombatLayer OldLayer;
		EAuracronCombatLayer NewLayer;
	};
	AuracronCombatBridge_eventOnCombatLayerChanged_Parms Parms;
	Parms.OldLayer=OldLayer;
	Parms.NewLayer=NewLayer;
	OnCombatLayerChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatLayerChanged ***************************************************

// ********** Begin Delegate FOnCombatEffectsSpawned ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando efeitos s\xc3\x83\xc2\xa3o spawnados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando efeitos s\xc3\x83\xc2\xa3o spawnados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectsConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_EffectsConfig = { "EffectsConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms, EffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(0, nullptr) }; // 3448964008
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::NewProp_EffectsConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnCombatEffectsSpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnCombatEffectsSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnCombatEffectsSpawned, FVector Location, FAuracronCombatEffectsConfiguration EffectsConfig)
{
	struct AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
	};
	AuracronCombatBridge_eventOnCombatEffectsSpawned_Parms Parms;
	Parms.Location=Location;
	Parms.EffectsConfig=EffectsConfig;
	OnCombatEffectsSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatEffectsSpawned *************************************************

// ********** Begin Delegate FOnComboExecuted ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnComboExecuted_Parms
	{
		EAuracronComboType ComboType;
		int32 ComboStep;
		float DamageMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando combo \xc3\x83\xc2\xa9 executado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando combo \xc3\x83\xc2\xa9 executado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ComboType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ComboType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComboStep;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboType = { "ComboType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnComboExecuted_Parms, ComboType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType, METADATA_PARAMS(0, nullptr) }; // 916783320
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboStep = { "ComboStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnComboExecuted_Parms, ComboStep), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnComboExecuted_Parms, DamageMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_ComboStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::NewProp_DamageMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnComboExecuted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnComboExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::AuracronCombatBridge_eventOnComboExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnComboExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnComboExecuted, EAuracronComboType ComboType, int32 ComboStep, float DamageMultiplier)
{
	struct AuracronCombatBridge_eventOnComboExecuted_Parms
	{
		EAuracronComboType ComboType;
		int32 ComboStep;
		float DamageMultiplier;
	};
	AuracronCombatBridge_eventOnComboExecuted_Parms Parms;
	Parms.ComboType=ComboType;
	Parms.ComboStep=ComboStep;
	Parms.DamageMultiplier=DamageMultiplier;
	OnComboExecuted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnComboExecuted ********************************************************

// ********** Begin Delegate FOnElementalDamageApplied *********************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnElementalDamageApplied_Parms
	{
		AActor* TargetActor;
		EAuracronElementalType ElementType;
		float Damage;
		bool bStatusEffectApplied;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando dano elemental \xc3\x83\xc2\xa9 aplicado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando dano elemental \xc3\x83\xc2\xa9 aplicado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static void NewProp_bStatusEffectApplied_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStatusEffectApplied;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnElementalDamageApplied_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_ElementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_ElementType = { "ElementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnElementalDamageApplied_Parms, ElementType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnElementalDamageApplied_Parms, Damage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_bStatusEffectApplied_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventOnElementalDamageApplied_Parms*)Obj)->bStatusEffectApplied = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_bStatusEffectApplied = { "bStatusEffectApplied", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventOnElementalDamageApplied_Parms), &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_bStatusEffectApplied_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_ElementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_ElementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::NewProp_bStatusEffectApplied,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnElementalDamageApplied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnElementalDamageApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::AuracronCombatBridge_eventOnElementalDamageApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnElementalDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnElementalDamageApplied, AActor* TargetActor, EAuracronElementalType ElementType, float Damage, bool bStatusEffectApplied)
{
	struct AuracronCombatBridge_eventOnElementalDamageApplied_Parms
	{
		AActor* TargetActor;
		EAuracronElementalType ElementType;
		float Damage;
		bool bStatusEffectApplied;
	};
	AuracronCombatBridge_eventOnElementalDamageApplied_Parms Parms;
	Parms.TargetActor=TargetActor;
	Parms.ElementType=ElementType;
	Parms.Damage=Damage;
	Parms.bStatusEffectApplied=bStatusEffectApplied ? true : false;
	OnElementalDamageApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnElementalDamageApplied ***********************************************

// ********** Begin Delegate FOnAICombatDecision ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnAICombatDecision_Parms
	{
		EAuracronAICombatBehavior BehaviorType;
		FString DecisionType;
		float ConfidenceLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando AI toma decis\xc3\x83\xc2\xa3o de combate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando AI toma decis\xc3\x83\xc2\xa3o de combate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DecisionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConfidenceLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAICombatDecision_Parms, BehaviorType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior, METADATA_PARAMS(0, nullptr) }; // 2928806894
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_DecisionType = { "DecisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAICombatDecision_Parms, DecisionType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_ConfidenceLevel = { "ConfidenceLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAICombatDecision_Parms, ConfidenceLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_DecisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::NewProp_ConfidenceLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnAICombatDecision__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::AuracronCombatBridge_eventOnAICombatDecision_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::AuracronCombatBridge_eventOnAICombatDecision_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnAICombatDecision_DelegateWrapper(const FMulticastScriptDelegate& OnAICombatDecision, EAuracronAICombatBehavior BehaviorType, const FString& DecisionType, float ConfidenceLevel)
{
	struct AuracronCombatBridge_eventOnAICombatDecision_Parms
	{
		EAuracronAICombatBehavior BehaviorType;
		FString DecisionType;
		float ConfidenceLevel;
	};
	AuracronCombatBridge_eventOnAICombatDecision_Parms Parms;
	Parms.BehaviorType=BehaviorType;
	Parms.DecisionType=DecisionType;
	Parms.ConfidenceLevel=ConfidenceLevel;
	OnAICombatDecision.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAICombatDecision *****************************************************

// ********** Begin Delegate FOnAdvancedDestruction ************************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnAdvancedDestruction_Parms
	{
		FVector Location;
		float DestructionForce;
		int32 AffectedObjects;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o avan\xc3\x83\xc2\xa7""ada ocorre */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o avan\xc3\x83\xc2\xa7""ada ocorre" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionForce;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AffectedObjects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAdvancedDestruction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_DestructionForce = { "DestructionForce", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAdvancedDestruction_Parms, DestructionForce), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_AffectedObjects = { "AffectedObjects", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnAdvancedDestruction_Parms, AffectedObjects), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_DestructionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::NewProp_AffectedObjects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnAdvancedDestruction__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::AuracronCombatBridge_eventOnAdvancedDestruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00930000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::AuracronCombatBridge_eventOnAdvancedDestruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnAdvancedDestruction_DelegateWrapper(const FMulticastScriptDelegate& OnAdvancedDestruction, FVector Location, float DestructionForce, int32 AffectedObjects)
{
	struct AuracronCombatBridge_eventOnAdvancedDestruction_Parms
	{
		FVector Location;
		float DestructionForce;
		int32 AffectedObjects;
	};
	AuracronCombatBridge_eventOnAdvancedDestruction_Parms Parms;
	Parms.Location=Location;
	Parms.DestructionForce=DestructionForce;
	Parms.AffectedObjects=AffectedObjects;
	OnAdvancedDestruction.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAdvancedDestruction **************************************************

// ********** Begin Delegate FOnCombatAnalyticsUpdated *********************************************
struct Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics
{
	struct AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms
	{
		FAuracronCombatAnalytics Analytics;
		float EfficiencyScore;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando analytics s\xc3\x83\xc2\xa3o atualizados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando analytics s\xc3\x83\xc2\xa3o atualizados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Analytics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EfficiencyScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::NewProp_Analytics = { "Analytics", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms, Analytics), Z_Construct_UScriptStruct_FAuracronCombatAnalytics, METADATA_PARAMS(0, nullptr) }; // 2913893938
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::NewProp_EfficiencyScore = { "EfficiencyScore", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms, EfficiencyScore), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::NewProp_Analytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::NewProp_EfficiencyScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnCombatAnalyticsUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronCombatBridge::FOnCombatAnalyticsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnCombatAnalyticsUpdated, FAuracronCombatAnalytics Analytics, float EfficiencyScore)
{
	struct AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms
	{
		FAuracronCombatAnalytics Analytics;
		float EfficiencyScore;
	};
	AuracronCombatBridge_eventOnCombatAnalyticsUpdated_Parms Parms;
	Parms.Analytics=Analytics;
	Parms.EfficiencyScore=EfficiencyScore;
	OnCombatAnalyticsUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCombatAnalyticsUpdated ***********************************************

// ********** Begin Class UAuracronCombatBridge Function AdaptAIBehavior ***************************
struct Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics
{
	struct AuracronCombatBridge_eventAdaptAIBehavior_Parms
	{
		FAuracronCombatAnalytics CombatData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adapt AI behavior based on combat data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adapt AI behavior based on combat data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CombatData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::NewProp_CombatData = { "CombatData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventAdaptAIBehavior_Parms, CombatData), Z_Construct_UScriptStruct_FAuracronCombatAnalytics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatData_MetaData), NewProp_CombatData_MetaData) }; // 2913893938
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::NewProp_CombatData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "AdaptAIBehavior", Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::AuracronCombatBridge_eventAdaptAIBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::AuracronCombatBridge_eventAdaptAIBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execAdaptAIBehavior)
{
	P_GET_STRUCT_REF(FAuracronCombatAnalytics,Z_Param_Out_CombatData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdaptAIBehavior(Z_Param_Out_CombatData);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function AdaptAIBehavior *****************************

// ********** Begin Class UAuracronCombatBridge Function ApplyAreaDamage ***************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics
{
	struct AuracronCombatBridge_eventApplyAreaDamage_Parms
	{
		FVector Location;
		FAuracronDamageConfiguration DamageConfig;
		FAuracronTargetingConfiguration TargetingConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar dano em \xc3\x83\xc2\xa1rea\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar dano em \xc3\x83\xc2\xa1rea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyAreaDamage_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyAreaDamage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyAreaDamage_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyAreaDamage", Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::AuracronCombatBridge_eventApplyAreaDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::AuracronCombatBridge_eventApplyAreaDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyAreaDamage)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyAreaDamage(Z_Param_Out_Location,Z_Param_Out_DamageConfig,Z_Param_Out_TargetingConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyAreaDamage *****************************

// ********** Begin Class UAuracronCombatBridge Function ApplyDamageToTarget ***********************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics
{
	struct AuracronCombatBridge_eventApplyDamageToTarget_Parms
	{
		AActor* TargetActor;
		FAuracronDamageConfiguration DamageConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar dano a um alvo\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar dano a um alvo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyDamageToTarget_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyDamageToTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyDamageToTarget_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyDamageToTarget", Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::AuracronCombatBridge_eventApplyDamageToTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::AuracronCombatBridge_eventApplyDamageToTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyDamageToTarget)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyDamageToTarget(Z_Param_TargetActor,Z_Param_Out_DamageConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyDamageToTarget *************************

// ********** Begin Class UAuracronCombatBridge Function ApplyElementalDamage **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics
{
	struct AuracronCombatBridge_eventApplyElementalDamage_Parms
	{
		AActor* TargetActor;
		FAuracronElementalDamageConfig ElementalConfig;
		float BaseDamage;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply elemental damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply elemental damage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementalConfig;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_ElementalConfig = { "ElementalConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalDamage_Parms, ElementalConfig), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalConfig_MetaData), NewProp_ElementalConfig_MetaData) }; // 1909653023
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_BaseDamage = { "BaseDamage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalDamage_Parms, BaseDamage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_ElementalConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_BaseDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyElementalDamage", Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::AuracronCombatBridge_eventApplyElementalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::AuracronCombatBridge_eventApplyElementalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyElementalDamage)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronElementalDamageConfig,Z_Param_Out_ElementalConfig);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BaseDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->ApplyElementalDamage(Z_Param_TargetActor,Z_Param_Out_ElementalConfig,Z_Param_BaseDamage);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyElementalDamage ************************

// ********** Begin Class UAuracronCombatBridge Function ApplyElementalStatusEffect ****************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics
{
	struct AuracronCombatBridge_eventApplyElementalStatusEffect_Parms
	{
		AActor* TargetActor;
		EAuracronElementalType ElementType;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply elemental status effect */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply elemental status effect" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalStatusEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ElementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ElementType = { "ElementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalStatusEffect_Parms, ElementType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyElementalStatusEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyElementalStatusEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyElementalStatusEffect_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ElementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ElementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyElementalStatusEffect", Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::AuracronCombatBridge_eventApplyElementalStatusEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::AuracronCombatBridge_eventApplyElementalStatusEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyElementalStatusEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_ENUM(EAuracronElementalType,Z_Param_ElementType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyElementalStatusEffect(Z_Param_TargetActor,EAuracronElementalType(Z_Param_ElementType),Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyElementalStatusEffect ******************

// ********** Begin Class UAuracronCombatBridge Function ApplyFieldSystemDestruction ***************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics
{
	struct AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms
	{
		FVector Location;
		float Force;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar Field System para destrui\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyFieldSystemDestruction", Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::AuracronCombatBridge_eventApplyFieldSystemDestruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyFieldSystemDestruction)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyFieldSystemDestruction(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyFieldSystemDestruction *****************

// ********** Begin Class UAuracronCombatBridge Function ApplyProceduralDamage *********************
struct Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics
{
	struct AuracronCombatBridge_eventApplyProceduralDamage_Parms
	{
		AActor* TargetActor;
		FVector ImpactLocation;
		float Damage;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply procedural damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply procedural damage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpactLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyProceduralDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ImpactLocation = { "ImpactLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyProceduralDamage_Parms, ImpactLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactLocation_MetaData), NewProp_ImpactLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventApplyProceduralDamage_Parms, Damage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventApplyProceduralDamage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventApplyProceduralDamage_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ImpactLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ApplyProceduralDamage", Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::AuracronCombatBridge_eventApplyProceduralDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::AuracronCombatBridge_eventApplyProceduralDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execApplyProceduralDamage)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ImpactLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyProceduralDamage(Z_Param_TargetActor,Z_Param_Out_ImpactLocation,Z_Param_Damage);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ApplyProceduralDamage ***********************

// ********** Begin Class UAuracronCombatBridge Function CalculateCombatEfficiency *****************
struct Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics
{
	struct AuracronCombatBridge_eventCalculateCombatEfficiency_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calculate combat efficiency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculate combat efficiency" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateCombatEfficiency_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CalculateCombatEfficiency", Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::AuracronCombatBridge_eventCalculateCombatEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::AuracronCombatBridge_eventCalculateCombatEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCalculateCombatEfficiency)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateCombatEfficiency();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CalculateCombatEfficiency *******************

// ********** Begin Class UAuracronCombatBridge Function CalculateElementalEffectiveness ***********
struct Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics
{
	struct AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms
	{
		EAuracronElementalType AttackElement;
		EAuracronElementalType DefenseElement;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calculate elemental effectiveness */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculate elemental effectiveness" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AttackElement_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AttackElement;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefenseElement_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefenseElement;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_AttackElement_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_AttackElement = { "AttackElement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms, AttackElement), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_DefenseElement_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_DefenseElement = { "DefenseElement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms, DefenseElement), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_AttackElement_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_AttackElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_DefenseElement_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_DefenseElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CalculateElementalEffectiveness", Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::AuracronCombatBridge_eventCalculateElementalEffectiveness_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCalculateElementalEffectiveness)
{
	P_GET_ENUM(EAuracronElementalType,Z_Param_AttackElement);
	P_GET_ENUM(EAuracronElementalType,Z_Param_DefenseElement);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateElementalEffectiveness(EAuracronElementalType(Z_Param_AttackElement),EAuracronElementalType(Z_Param_DefenseElement));
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CalculateElementalEffectiveness *************

// ********** Begin Class UAuracronCombatBridge Function CalculateFinalDamage **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics
{
	struct AuracronCombatBridge_eventCalculateFinalDamage_Parms
	{
		FAuracronDamageConfiguration DamageConfig;
		AActor* SourceActor;
		AActor* TargetActor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Damage" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcular dano final\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular dano final" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_DamageConfig = { "DamageConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, DamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageConfig_MetaData), NewProp_DamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCalculateFinalDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_DamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CalculateFinalDamage", Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::AuracronCombatBridge_eventCalculateFinalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::AuracronCombatBridge_eventCalculateFinalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCalculateFinalDamage)
{
	P_GET_STRUCT_REF(FAuracronDamageConfiguration,Z_Param_Out_DamageConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateFinalDamage(Z_Param_Out_DamageConfig,Z_Param_SourceActor,Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CalculateFinalDamage ************************

// ********** Begin Class UAuracronCombatBridge Function CheckLineOfSight **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics
{
	struct AuracronCombatBridge_eventCheckLineOfSight_Parms
	{
		FVector StartLocation;
		FVector EndLocation;
		TArray<TEnumAsByte<ECollisionChannel>> CollisionChannels;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar line of sight entre dois pontos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar line of sight entre dois pontos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionChannels_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CollisionChannels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CollisionChannels;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels_Inner = { "CollisionChannels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_ECollisionChannel, METADATA_PARAMS(0, nullptr) }; // 756624936
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels = { "CollisionChannels", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCheckLineOfSight_Parms, CollisionChannels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionChannels_MetaData), NewProp_CollisionChannels_MetaData) }; // 756624936
void Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCheckLineOfSight_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCheckLineOfSight_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_CollisionChannels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CheckLineOfSight", Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::AuracronCombatBridge_eventCheckLineOfSight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::AuracronCombatBridge_eventCheckLineOfSight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCheckLineOfSight)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndLocation);
	P_GET_TARRAY_REF(TEnumAsByte<ECollisionChannel>,Z_Param_Out_CollisionChannels);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckLineOfSight(Z_Param_Out_StartLocation,Z_Param_Out_EndLocation,Z_Param_Out_CollisionChannels);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CheckLineOfSight ****************************

// ********** Begin Class UAuracronCombatBridge Function CreateAdvancedDestruction *****************
struct Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics
{
	struct AuracronCombatBridge_eventCreateAdvancedDestruction_Parms
	{
		FVector Location;
		FAuracronAdvancedDestructionConfig DestructionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create advanced destruction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create advanced destruction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestructionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestructionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateAdvancedDestruction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_DestructionConfig = { "DestructionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateAdvancedDestruction_Parms, DestructionConfig), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestructionConfig_MetaData), NewProp_DestructionConfig_MetaData) }; // 886306391
void Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCreateAdvancedDestruction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCreateAdvancedDestruction_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_DestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CreateAdvancedDestruction", Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::AuracronCombatBridge_eventCreateAdvancedDestruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::AuracronCombatBridge_eventCreateAdvancedDestruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCreateAdvancedDestruction)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronAdvancedDestructionConfig,Z_Param_Out_DestructionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateAdvancedDestruction(Z_Param_Out_Location,Z_Param_Out_DestructionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CreateAdvancedDestruction *******************

// ********** Begin Class UAuracronCombatBridge Function CreateChaosExplosion **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics
{
	struct AuracronCombatBridge_eventCreateChaosExplosion_Parms
	{
		FVector Location;
		float Force;
		float Radius;
		bool bAffectAllLayers;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar explos\xc3\x83\xc2\xa3o com Chaos Physics\n     */" },
#endif
		{ "CPP_Default_bAffectAllLayers", "false" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar explos\xc3\x83\xc2\xa3o com Chaos Physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bAffectAllLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectAllLayers;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventCreateChaosExplosion_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCreateChaosExplosion_Parms*)Obj)->bAffectAllLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers = { "bAffectAllLayers", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCreateChaosExplosion_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventCreateChaosExplosion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventCreateChaosExplosion_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_bAffectAllLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "CreateChaosExplosion", Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::AuracronCombatBridge_eventCreateChaosExplosion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::AuracronCombatBridge_eventCreateChaosExplosion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execCreateChaosExplosion)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_UBOOL(Z_Param_bAffectAllLayers);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateChaosExplosion(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius,Z_Param_bAffectAllLayers);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function CreateChaosExplosion ************************

// ********** Begin Class UAuracronCombatBridge Function ExecuteAICombatDecision *******************
struct Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics
{
	struct AuracronCombatBridge_eventExecuteAICombatDecision_Parms
	{
		FString DecisionType;
		TMap<FString,FString> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Execute AI combat decision */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute AI combat decision" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DecisionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DecisionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_DecisionType = { "DecisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteAICombatDecision_Parms, DecisionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DecisionType_MetaData), NewProp_DecisionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteAICombatDecision_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventExecuteAICombatDecision_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventExecuteAICombatDecision_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_DecisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ExecuteAICombatDecision", Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::AuracronCombatBridge_eventExecuteAICombatDecision_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::AuracronCombatBridge_eventExecuteAICombatDecision_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execExecuteAICombatDecision)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DecisionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteAICombatDecision(Z_Param_DecisionType,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ExecuteAICombatDecision *********************

// ********** Begin Class UAuracronCombatBridge Function ExecuteComboSequence **********************
struct Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics
{
	struct AuracronCombatBridge_eventExecuteComboSequence_Parms
	{
		FAuracronComboConfig ComboConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Execute combo sequence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute combo sequence" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComboConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ComboConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ComboConfig = { "ComboConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteComboSequence_Parms, ComboConfig), Z_Construct_UScriptStruct_FAuracronComboConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComboConfig_MetaData), NewProp_ComboConfig_MetaData) }; // 2639411205
void Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventExecuteComboSequence_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventExecuteComboSequence_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ComboConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ExecuteComboSequence", Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::AuracronCombatBridge_eventExecuteComboSequence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::AuracronCombatBridge_eventExecuteComboSequence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execExecuteComboSequence)
{
	P_GET_STRUCT_REF(FAuracronComboConfig,Z_Param_Out_ComboConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecuteComboSequence(Z_Param_Out_ComboConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ExecuteComboSequence ************************

// ********** Begin Class UAuracronCombatBridge Function ExecuteTargeting **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics
{
	struct AuracronCombatBridge_eventExecuteTargeting_Parms
	{
		FAuracronTargetingConfiguration TargetingConfig;
		FVector SourceLocation;
		FVector TargetDirection;
		FAuracronTargetingResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar targeting 3D\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar targeting 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetDirection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetDirection = { "TargetDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, TargetDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetDirection_MetaData), NewProp_TargetDirection_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExecuteTargeting_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(0, nullptr) }; // 282061631
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_TargetDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ExecuteTargeting", Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::AuracronCombatBridge_eventExecuteTargeting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::AuracronCombatBridge_eventExecuteTargeting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execExecuteTargeting)
{
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SourceLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTargetingResult*)Z_Param__Result=P_THIS->ExecuteTargeting(Z_Param_Out_TargetingConfig,Z_Param_Out_SourceLocation,Z_Param_Out_TargetDirection);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ExecuteTargeting ****************************

// ********** Begin Class UAuracronCombatBridge Function ExportCombatData **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics
{
	struct AuracronCombatBridge_eventExportCombatData_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Export combat data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Export combat data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventExportCombatData_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventExportCombatData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventExportCombatData_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ExportCombatData", Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::AuracronCombatBridge_eventExportCombatData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::AuracronCombatBridge_eventExportCombatData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execExportCombatData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportCombatData(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ExportCombatData ****************************

// ********** Begin Class UAuracronCombatBridge Function GetActorCombatLayer ***********************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics
{
	struct AuracronCombatBridge_eventGetActorCombatLayer_Parms
	{
		AActor* Actor;
		EAuracronCombatLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Layers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter camada de combate de um ator\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camada de combate de um ator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetActorCombatLayer_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetActorCombatLayer_Parms, ReturnValue), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(0, nullptr) }; // 3955355270
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetActorCombatLayer", Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::AuracronCombatBridge_eventGetActorCombatLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::AuracronCombatBridge_eventGetActorCombatLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetActorCombatLayer)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronCombatLayer*)Z_Param__Result=P_THIS->GetActorCombatLayer(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetActorCombatLayer *************************

// ********** Begin Class UAuracronCombatBridge Function GetCombatAnalytics ************************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics
{
	struct AuracronCombatBridge_eventGetCombatAnalytics_Parms
	{
		FAuracronCombatAnalytics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get combat analytics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get combat analytics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetCombatAnalytics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCombatAnalytics, METADATA_PARAMS(0, nullptr) }; // 2913893938
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetCombatAnalytics", Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::AuracronCombatBridge_eventGetCombatAnalytics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::AuracronCombatBridge_eventGetCombatAnalytics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetCombatAnalytics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCombatAnalytics*)Z_Param__Result=P_THIS->GetCombatAnalytics();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetCombatAnalytics **************************

// ********** Begin Class UAuracronCombatBridge Function GetElementalResistances *******************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics
{
	struct AuracronCombatBridge_eventGetElementalResistances_Parms
	{
		AActor* Actor;
		TMap<EAuracronElementalType,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get elemental resistances */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get elemental resistances" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetElementalResistances_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetElementalResistances_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetElementalResistances", Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::AuracronCombatBridge_eventGetElementalResistances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::AuracronCombatBridge_eventGetElementalResistances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetElementalResistances)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<EAuracronElementalType,float>*)Z_Param__Result=P_THIS->GetElementalResistances(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetElementalResistances *********************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInCone **************************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInCone_Parms
	{
		FVector SourceLocation;
		FVector Direction;
		float Range;
		float ConeAngle;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em cone\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em cone" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Direction_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SourceLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Direction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConeAngle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_SourceLocation = { "SourceLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, SourceLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLocation_MetaData), NewProp_SourceLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Direction = { "Direction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, Direction), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Direction_MetaData), NewProp_Direction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, Range), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ConeAngle = { "ConeAngle", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, ConeAngle), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInCone_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_SourceLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Direction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ConeAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInCone", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::AuracronCombatBridge_eventGetTargetsInCone_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::AuracronCombatBridge_eventGetTargetsInCone_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInCone)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SourceLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Direction);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Range);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ConeAngle);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInCone(Z_Param_Out_SourceLocation,Z_Param_Out_Direction,Z_Param_Range,Z_Param_ConeAngle,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInCone ****************************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInRadius ************************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInRadius_Parms
	{
		FVector Location;
		float Radius;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em raio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em raio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInRadius", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::AuracronCombatBridge_eventGetTargetsInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::AuracronCombatBridge_eventGetTargetsInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInRadius(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInRadius **************************

// ********** Begin Class UAuracronCombatBridge Function GetTargetsInVerticalColumn ****************
struct Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics
{
	struct AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms
	{
		FVector Location;
		float Radius;
		float Height;
		FAuracronTargetingConfiguration TargetingConfig;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter alvos em coluna vertical\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter alvos em coluna vertical" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "GetTargetsInVerticalColumn", Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::AuracronCombatBridge_eventGetTargetsInVerticalColumn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execGetTargetsInVerticalColumn)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Height);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetTargetsInVerticalColumn(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Height,Z_Param_Out_TargetingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function GetTargetsInVerticalColumn ******************

// ********** Begin Class UAuracronCombatBridge Function InitializeAICombatBehavior ****************
struct Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics
{
	struct AuracronCombatBridge_eventInitializeAICombatBehavior_Parms
	{
		FAuracronAICombatConfig AIConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize AI combat behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize AI combat behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AIConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_AIConfig = { "AIConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventInitializeAICombatBehavior_Parms, AIConfig), Z_Construct_UScriptStruct_FAuracronAICombatConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIConfig_MetaData), NewProp_AIConfig_MetaData) }; // 517528943
void Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventInitializeAICombatBehavior_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventInitializeAICombatBehavior_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_AIConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "InitializeAICombatBehavior", Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::AuracronCombatBridge_eventInitializeAICombatBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::AuracronCombatBridge_eventInitializeAICombatBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execInitializeAICombatBehavior)
{
	P_GET_STRUCT_REF(FAuracronAICombatConfig,Z_Param_Out_AIConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeAICombatBehavior(Z_Param_Out_AIConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function InitializeAICombatBehavior ******************

// ********** Begin Class UAuracronCombatBridge Function InitializeEnhancedInputSystem *************
struct Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics
{
	struct AuracronCombatBridge_eventInitializeEnhancedInputSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize Enhanced Input system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Enhanced Input system" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventInitializeEnhancedInputSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventInitializeEnhancedInputSystem_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "InitializeEnhancedInputSystem", Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::AuracronCombatBridge_eventInitializeEnhancedInputSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::AuracronCombatBridge_eventInitializeEnhancedInputSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execInitializeEnhancedInputSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeEnhancedInputSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function InitializeEnhancedInputSystem ***************

// ********** Begin Class UAuracronCombatBridge Function IsValidTarget *****************************
struct Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics
{
	struct AuracronCombatBridge_eventIsValidTarget_Parms
	{
		AActor* TargetActor;
		FAuracronTargetingConfiguration TargetingConfig;
		AActor* SourceActor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Targeting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se alvo \xc3\x83\xc2\xa9 v\xc3\x83\xc2\xa1lido\n     */" },
#endif
		{ "CPP_Default_SourceActor", "None" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se alvo \xc3\x83\xc2\xa9 v\xc3\x83\xc2\xa1lido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetingConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceActor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetingConfig = { "TargetingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, TargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetingConfig_MetaData), NewProp_TargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_SourceActor = { "SourceActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventIsValidTarget_Parms, SourceActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventIsValidTarget_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventIsValidTarget_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_TargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_SourceActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "IsValidTarget", Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::AuracronCombatBridge_eventIsValidTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::AuracronCombatBridge_eventIsValidTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execIsValidTarget)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT_REF(FAuracronTargetingConfiguration,Z_Param_Out_TargetingConfig);
	P_GET_OBJECT(AActor,Z_Param_SourceActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidTarget(Z_Param_TargetActor,Z_Param_Out_TargetingConfig,Z_Param_SourceActor);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function IsValidTarget *******************************

// ********** Begin Class UAuracronCombatBridge Function OnRep_CurrentCombatLayer ******************
struct Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "OnRep_CurrentCombatLayer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execOnRep_CurrentCombatLayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentCombatLayer();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function OnRep_CurrentCombatLayer ********************

// ********** Begin Class UAuracronCombatBridge Function ProcessComboInput *************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics
{
	struct AuracronCombatBridge_eventProcessComboInput_Parms
	{
		FString InputName;
		float InputTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Process combo input */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Process combo input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InputName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InputTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_InputName = { "InputName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventProcessComboInput_Parms, InputName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputName_MetaData), NewProp_InputName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_InputTime = { "InputTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventProcessComboInput_Parms, InputTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventProcessComboInput_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventProcessComboInput_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_InputName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_InputTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ProcessComboInput", Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::AuracronCombatBridge_eventProcessComboInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::AuracronCombatBridge_eventProcessComboInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execProcessComboInput)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InputName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_InputTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ProcessComboInput(Z_Param_InputName,Z_Param_InputTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ProcessComboInput ***************************

// ********** Begin Class UAuracronCombatBridge Function ResetComboChain ***************************
struct Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reset combo chain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset combo chain" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "ResetComboChain", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execResetComboChain)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetComboChain();
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function ResetComboChain *****************************

// ********** Begin Class UAuracronCombatBridge Function SetupCombatInputActions *******************
struct Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics
{
	struct AuracronCombatBridge_eventSetupCombatInputActions_Parms
	{
		FAuracronEnhancedInputConfig InputConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Setup combat input actions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup combat input actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InputConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::NewProp_InputConfig = { "InputConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSetupCombatInputActions_Parms, InputConfig), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputConfig_MetaData), NewProp_InputConfig_MetaData) }; // 2243282029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::NewProp_InputConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "SetupCombatInputActions", Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::AuracronCombatBridge_eventSetupCombatInputActions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::AuracronCombatBridge_eventSetupCombatInputActions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execSetupCombatInputActions)
{
	P_GET_STRUCT_REF(FAuracronEnhancedInputConfig,Z_Param_Out_InputConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupCombatInputActions(Z_Param_Out_InputConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function SetupCombatInputActions *********************

// ********** Begin Class UAuracronCombatBridge Function SimulateDestructionPhysics ****************
struct Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics
{
	struct AuracronCombatBridge_eventSimulateDestructionPhysics_Parms
	{
		FVector Location;
		float Force;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Simulate destruction physics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simulate destruction physics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Force;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSimulateDestructionPhysics_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Force = { "Force", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSimulateDestructionPhysics_Parms, Force), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSimulateDestructionPhysics_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Force,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "SimulateDestructionPhysics", Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::AuracronCombatBridge_eventSimulateDestructionPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::AuracronCombatBridge_eventSimulateDestructionPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execSimulateDestructionPhysics)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Force);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SimulateDestructionPhysics(Z_Param_Out_Location,Z_Param_Force,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function SimulateDestructionPhysics ******************

// ********** Begin Class UAuracronCombatBridge Function SpawnCombatEffects ************************
struct Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics
{
	struct AuracronCombatBridge_eventSpawnCombatEffects_Parms
	{
		FVector Location;
		FAuracronCombatEffectsConfiguration EffectsConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Combat|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar efeitos de combate\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar efeitos de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectsConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectsConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSpawnCombatEffects_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_EffectsConfig = { "EffectsConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventSpawnCombatEffects_Parms, EffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectsConfig_MetaData), NewProp_EffectsConfig_MetaData) }; // 3448964008
void Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronCombatBridge_eventSpawnCombatEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronCombatBridge_eventSpawnCombatEffects_Parms), &Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_EffectsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "SpawnCombatEffects", Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::AuracronCombatBridge_eventSpawnCombatEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::AuracronCombatBridge_eventSpawnCombatEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execSpawnCombatEffects)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronCombatEffectsConfiguration,Z_Param_Out_EffectsConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpawnCombatEffects(Z_Param_Out_Location,Z_Param_Out_EffectsConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function SpawnCombatEffects **************************

// ********** Begin Class UAuracronCombatBridge Function UpdateAICombatState ***********************
struct Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics
{
	struct AuracronCombatBridge_eventUpdateAICombatState_Parms
	{
		AActor* TargetActor;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update AI combat state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update AI combat state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventUpdateAICombatState_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventUpdateAICombatState_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "UpdateAICombatState", Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::AuracronCombatBridge_eventUpdateAICombatState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::AuracronCombatBridge_eventUpdateAICombatState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execUpdateAICombatState)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAICombatState(Z_Param_TargetActor,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function UpdateAICombatState *************************

// ********** Begin Class UAuracronCombatBridge Function UpdateCombatAnalytics *********************
struct Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics
{
	struct AuracronCombatBridge_eventUpdateCombatAnalytics_Parms
	{
		FString EventType;
		TMap<FString,FString> EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Combat|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update combat analytics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update combat analytics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventUpdateCombatAnalytics_Parms, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData_ValueProp = { "EventData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData_Key_KeyProp = { "EventData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronCombatBridge_eventUpdateCombatAnalytics_Parms, EventData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronCombatBridge, nullptr, "UpdateCombatAnalytics", Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::AuracronCombatBridge_eventUpdateCombatAnalytics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::AuracronCombatBridge_eventUpdateCombatAnalytics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronCombatBridge::execUpdateCombatAnalytics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_EventData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCombatAnalytics(Z_Param_EventType,Z_Param_Out_EventData);
	P_NATIVE_END;
}
// ********** End Class UAuracronCombatBridge Function UpdateCombatAnalytics ***********************

// ********** Begin Class UAuracronCombatBridge ****************************************************
void UAuracronCombatBridge::StaticRegisterNativesUAuracronCombatBridge()
{
	UClass* Class = UAuracronCombatBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdaptAIBehavior", &UAuracronCombatBridge::execAdaptAIBehavior },
		{ "ApplyAreaDamage", &UAuracronCombatBridge::execApplyAreaDamage },
		{ "ApplyDamageToTarget", &UAuracronCombatBridge::execApplyDamageToTarget },
		{ "ApplyElementalDamage", &UAuracronCombatBridge::execApplyElementalDamage },
		{ "ApplyElementalStatusEffect", &UAuracronCombatBridge::execApplyElementalStatusEffect },
		{ "ApplyFieldSystemDestruction", &UAuracronCombatBridge::execApplyFieldSystemDestruction },
		{ "ApplyProceduralDamage", &UAuracronCombatBridge::execApplyProceduralDamage },
		{ "CalculateCombatEfficiency", &UAuracronCombatBridge::execCalculateCombatEfficiency },
		{ "CalculateElementalEffectiveness", &UAuracronCombatBridge::execCalculateElementalEffectiveness },
		{ "CalculateFinalDamage", &UAuracronCombatBridge::execCalculateFinalDamage },
		{ "CheckLineOfSight", &UAuracronCombatBridge::execCheckLineOfSight },
		{ "CreateAdvancedDestruction", &UAuracronCombatBridge::execCreateAdvancedDestruction },
		{ "CreateChaosExplosion", &UAuracronCombatBridge::execCreateChaosExplosion },
		{ "ExecuteAICombatDecision", &UAuracronCombatBridge::execExecuteAICombatDecision },
		{ "ExecuteComboSequence", &UAuracronCombatBridge::execExecuteComboSequence },
		{ "ExecuteTargeting", &UAuracronCombatBridge::execExecuteTargeting },
		{ "ExportCombatData", &UAuracronCombatBridge::execExportCombatData },
		{ "GetActorCombatLayer", &UAuracronCombatBridge::execGetActorCombatLayer },
		{ "GetCombatAnalytics", &UAuracronCombatBridge::execGetCombatAnalytics },
		{ "GetElementalResistances", &UAuracronCombatBridge::execGetElementalResistances },
		{ "GetTargetsInCone", &UAuracronCombatBridge::execGetTargetsInCone },
		{ "GetTargetsInRadius", &UAuracronCombatBridge::execGetTargetsInRadius },
		{ "GetTargetsInVerticalColumn", &UAuracronCombatBridge::execGetTargetsInVerticalColumn },
		{ "InitializeAICombatBehavior", &UAuracronCombatBridge::execInitializeAICombatBehavior },
		{ "InitializeEnhancedInputSystem", &UAuracronCombatBridge::execInitializeEnhancedInputSystem },
		{ "IsValidTarget", &UAuracronCombatBridge::execIsValidTarget },
		{ "OnRep_CurrentCombatLayer", &UAuracronCombatBridge::execOnRep_CurrentCombatLayer },
		{ "ProcessComboInput", &UAuracronCombatBridge::execProcessComboInput },
		{ "ResetComboChain", &UAuracronCombatBridge::execResetComboChain },
		{ "SetupCombatInputActions", &UAuracronCombatBridge::execSetupCombatInputActions },
		{ "SimulateDestructionPhysics", &UAuracronCombatBridge::execSimulateDestructionPhysics },
		{ "SpawnCombatEffects", &UAuracronCombatBridge::execSpawnCombatEffects },
		{ "UpdateAICombatState", &UAuracronCombatBridge::execUpdateAICombatState },
		{ "UpdateCombatAnalytics", &UAuracronCombatBridge::execUpdateCombatAnalytics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronCombatBridge;
UClass* UAuracronCombatBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronCombatBridge;
	if (!Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronCombatBridge"),
			Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton,
			StaticRegisterNativesUAuracronCombatBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister()
{
	return UAuracronCombatBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronCombatBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Combate 3D Vertical\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de combate com targeting vertical\n */" },
#endif
		{ "DisplayName", "AURACRON Combat Bridge" },
		{ "IncludePath", "AuracronCombatBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Combate 3D Vertical\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de combate com targeting vertical" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTargetingConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de targeting padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de targeting padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDamageConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de dano padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de dano padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultEffectsConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de efeitos padr\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de efeitos padr\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnhancedInputConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AICombatConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** AI Combat configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Combat configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalDamageConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemental damage configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemental damage configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdvancedDestructionConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Advanced destruction configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced destruction configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableCombos_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Available combo configurations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Available combo configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentCombatLayer_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camada de combate atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camada de combate atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTargetingResult_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltimo resultado de targeting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltimo resultado de targeting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FieldSystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao FieldSystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao FieldSystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnhancedInputComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Input Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Input Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentCombatAnalytics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current combat analytics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current combat analytics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentComboChain_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current combo chain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current combo chain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentComboPoints_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current combo points */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current combo points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveElementalEffects_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active elemental effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active elemental effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTargetingExecuted_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDamageApplied_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatLayerChanged_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatEffectsSpawned_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnComboExecuted_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnElementalDamageApplied_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAICombatDecision_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAdvancedDestruction_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCombatAnalyticsUpdated_MetaData[] = {
		{ "Category", "AURACRON Combat|Events" },
		{ "ModuleRelativePath", "Public/AuracronCombatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultTargetingConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultDamageConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultEffectsConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnhancedInputConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AICombatConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementalDamageConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdvancedDestructionConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AvailableCombos_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableCombos;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentCombatLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentCombatLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastTargetingResult;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FieldSystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnhancedInputComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentCombatAnalytics;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentComboChain_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CurrentComboChain;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentComboPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActiveElementalEffects_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActiveElementalEffects_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActiveElementalEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveElementalEffects;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTargetingExecuted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDamageApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatLayerChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatEffectsSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnComboExecuted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnElementalDamageApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAICombatDecision;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAdvancedDestruction;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCombatAnalyticsUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronCombatBridge_AdaptAIBehavior, "AdaptAIBehavior" }, // 2682012898
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyAreaDamage, "ApplyAreaDamage" }, // 1247714456
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyDamageToTarget, "ApplyDamageToTarget" }, // 4218748587
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalDamage, "ApplyElementalDamage" }, // 3014085314
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyElementalStatusEffect, "ApplyElementalStatusEffect" }, // 1524327847
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyFieldSystemDestruction, "ApplyFieldSystemDestruction" }, // 3689865967
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ApplyProceduralDamage, "ApplyProceduralDamage" }, // 306519808
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CalculateCombatEfficiency, "CalculateCombatEfficiency" }, // 3073477203
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CalculateElementalEffectiveness, "CalculateElementalEffectiveness" }, // 2488342251
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CalculateFinalDamage, "CalculateFinalDamage" }, // 351128093
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CheckLineOfSight, "CheckLineOfSight" }, // 3745041030
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CreateAdvancedDestruction, "CreateAdvancedDestruction" }, // 538684949
		{ &Z_Construct_UFunction_UAuracronCombatBridge_CreateChaosExplosion, "CreateChaosExplosion" }, // 3680153202
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteAICombatDecision, "ExecuteAICombatDecision" }, // 1021759159
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteComboSequence, "ExecuteComboSequence" }, // 1343214725
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ExecuteTargeting, "ExecuteTargeting" }, // 3821346754
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ExportCombatData, "ExportCombatData" }, // 2624634029
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetActorCombatLayer, "GetActorCombatLayer" }, // 3560224605
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetCombatAnalytics, "GetCombatAnalytics" }, // 3037049821
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetElementalResistances, "GetElementalResistances" }, // 1194661568
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInCone, "GetTargetsInCone" }, // 308879522
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInRadius, "GetTargetsInRadius" }, // 2879593072
		{ &Z_Construct_UFunction_UAuracronCombatBridge_GetTargetsInVerticalColumn, "GetTargetsInVerticalColumn" }, // 732781749
		{ &Z_Construct_UFunction_UAuracronCombatBridge_InitializeAICombatBehavior, "InitializeAICombatBehavior" }, // 2477864459
		{ &Z_Construct_UFunction_UAuracronCombatBridge_InitializeEnhancedInputSystem, "InitializeEnhancedInputSystem" }, // 1417625468
		{ &Z_Construct_UFunction_UAuracronCombatBridge_IsValidTarget, "IsValidTarget" }, // 1896498501
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature, "OnAdvancedDestruction__DelegateSignature" }, // 2060265046
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature, "OnAICombatDecision__DelegateSignature" }, // 166989688
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature, "OnCombatAnalyticsUpdated__DelegateSignature" }, // 1816421240
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature, "OnCombatEffectsSpawned__DelegateSignature" }, // 1950413761
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature, "OnCombatLayerChanged__DelegateSignature" }, // 2955618232
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature, "OnComboExecuted__DelegateSignature" }, // 1664768751
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature, "OnDamageApplied__DelegateSignature" }, // 1820049788
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature, "OnElementalDamageApplied__DelegateSignature" }, // 4225334408
		{ &Z_Construct_UFunction_UAuracronCombatBridge_OnRep_CurrentCombatLayer, "OnRep_CurrentCombatLayer" }, // 1472461616
		{ &Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature, "OnTargetingExecuted__DelegateSignature" }, // 4125075543
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ProcessComboInput, "ProcessComboInput" }, // 462678893
		{ &Z_Construct_UFunction_UAuracronCombatBridge_ResetComboChain, "ResetComboChain" }, // 1313230356
		{ &Z_Construct_UFunction_UAuracronCombatBridge_SetupCombatInputActions, "SetupCombatInputActions" }, // 2066375527
		{ &Z_Construct_UFunction_UAuracronCombatBridge_SimulateDestructionPhysics, "SimulateDestructionPhysics" }, // 2222578393
		{ &Z_Construct_UFunction_UAuracronCombatBridge_SpawnCombatEffects, "SpawnCombatEffects" }, // 2773563558
		{ &Z_Construct_UFunction_UAuracronCombatBridge_UpdateAICombatState, "UpdateAICombatState" }, // 2194187127
		{ &Z_Construct_UFunction_UAuracronCombatBridge_UpdateCombatAnalytics, "UpdateCombatAnalytics" }, // 3780280448
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronCombatBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultTargetingConfig = { "DefaultTargetingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultTargetingConfig), Z_Construct_UScriptStruct_FAuracronTargetingConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTargetingConfig_MetaData), NewProp_DefaultTargetingConfig_MetaData) }; // 2989207853
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultDamageConfig = { "DefaultDamageConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultDamageConfig), Z_Construct_UScriptStruct_FAuracronDamageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDamageConfig_MetaData), NewProp_DefaultDamageConfig_MetaData) }; // 2783236807
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultEffectsConfig = { "DefaultEffectsConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, DefaultEffectsConfig), Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultEffectsConfig_MetaData), NewProp_DefaultEffectsConfig_MetaData) }; // 3448964008
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_EnhancedInputConfig = { "EnhancedInputConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, EnhancedInputConfig), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnhancedInputConfig_MetaData), NewProp_EnhancedInputConfig_MetaData) }; // 2243282029
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AICombatConfig = { "AICombatConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, AICombatConfig), Z_Construct_UScriptStruct_FAuracronAICombatConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AICombatConfig_MetaData), NewProp_AICombatConfig_MetaData) }; // 517528943
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ElementalDamageConfig = { "ElementalDamageConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, ElementalDamageConfig), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalDamageConfig_MetaData), NewProp_ElementalDamageConfig_MetaData) }; // 1909653023
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AdvancedDestructionConfig = { "AdvancedDestructionConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, AdvancedDestructionConfig), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdvancedDestructionConfig_MetaData), NewProp_AdvancedDestructionConfig_MetaData) }; // 886306391
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AvailableCombos_Inner = { "AvailableCombos", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronComboConfig, METADATA_PARAMS(0, nullptr) }; // 2639411205
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AvailableCombos = { "AvailableCombos", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, AvailableCombos), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableCombos_MetaData), NewProp_AvailableCombos_MetaData) }; // 2639411205
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer = { "CurrentCombatLayer", "OnRep_CurrentCombatLayer", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, CurrentCombatLayer), Z_Construct_UEnum_AuracronCombatBridge_EAuracronCombatLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentCombatLayer_MetaData), NewProp_CurrentCombatLayer_MetaData) }; // 3955355270
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_LastTargetingResult = { "LastTargetingResult", nullptr, (EPropertyFlags)0x0010008000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, LastTargetingResult), Z_Construct_UScriptStruct_FAuracronTargetingResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTargetingResult_MetaData), NewProp_LastTargetingResult_MetaData) }; // 282061631
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_FieldSystemComponent = { "FieldSystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, FieldSystemComponent), Z_Construct_UClass_UFieldSystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FieldSystemComponent_MetaData), NewProp_FieldSystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_EnhancedInputComponent = { "EnhancedInputComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, EnhancedInputComponent), Z_Construct_UClass_UEnhancedInputComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnhancedInputComponent_MetaData), NewProp_EnhancedInputComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatAnalytics = { "CurrentCombatAnalytics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, CurrentCombatAnalytics), Z_Construct_UScriptStruct_FAuracronCombatAnalytics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentCombatAnalytics_MetaData), NewProp_CurrentCombatAnalytics_MetaData) }; // 2913893938
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboChain_Inner = { "CurrentComboChain", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboChain = { "CurrentComboChain", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, CurrentComboChain), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentComboChain_MetaData), NewProp_CurrentComboChain_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboPoints = { "CurrentComboPoints", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, CurrentComboPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentComboPoints_MetaData), NewProp_CurrentComboPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_ValueProp = { "ActiveElementalEffects", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_Key_KeyProp = { "ActiveElementalEffects_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects = { "ActiveElementalEffects", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, ActiveElementalEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveElementalEffects_MetaData), NewProp_ActiveElementalEffects_MetaData) }; // 2822706140
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnTargetingExecuted = { "OnTargetingExecuted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnTargetingExecuted), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnTargetingExecuted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTargetingExecuted_MetaData), NewProp_OnTargetingExecuted_MetaData) }; // 4125075543
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnDamageApplied = { "OnDamageApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnDamageApplied), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnDamageApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDamageApplied_MetaData), NewProp_OnDamageApplied_MetaData) }; // 1820049788
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatLayerChanged = { "OnCombatLayerChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnCombatLayerChanged), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatLayerChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatLayerChanged_MetaData), NewProp_OnCombatLayerChanged_MetaData) }; // 2955618232
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatEffectsSpawned = { "OnCombatEffectsSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnCombatEffectsSpawned), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatEffectsSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatEffectsSpawned_MetaData), NewProp_OnCombatEffectsSpawned_MetaData) }; // 1950413761
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnComboExecuted = { "OnComboExecuted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnComboExecuted), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnComboExecuted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnComboExecuted_MetaData), NewProp_OnComboExecuted_MetaData) }; // 1664768751
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnElementalDamageApplied = { "OnElementalDamageApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnElementalDamageApplied), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnElementalDamageApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnElementalDamageApplied_MetaData), NewProp_OnElementalDamageApplied_MetaData) }; // 4225334408
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnAICombatDecision = { "OnAICombatDecision", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnAICombatDecision), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAICombatDecision__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAICombatDecision_MetaData), NewProp_OnAICombatDecision_MetaData) }; // 166989688
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnAdvancedDestruction = { "OnAdvancedDestruction", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnAdvancedDestruction), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnAdvancedDestruction__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAdvancedDestruction_MetaData), NewProp_OnAdvancedDestruction_MetaData) }; // 2060265046
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatAnalyticsUpdated = { "OnCombatAnalyticsUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronCombatBridge, OnCombatAnalyticsUpdated), Z_Construct_UDelegateFunction_UAuracronCombatBridge_OnCombatAnalyticsUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCombatAnalyticsUpdated_MetaData), NewProp_OnCombatAnalyticsUpdated_MetaData) }; // 1816421240
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultTargetingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultDamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_DefaultEffectsConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_EnhancedInputConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AICombatConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ElementalDamageConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AdvancedDestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AvailableCombos_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AvailableCombos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_LastTargetingResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_FieldSystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_EnhancedInputComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentCombatAnalytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboChain_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboChain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_CurrentComboPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_ActiveElementalEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnTargetingExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnDamageApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatLayerChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatEffectsSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnComboExecuted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnElementalDamageApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnAICombatDecision,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnAdvancedDestruction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronCombatBridge_Statics::NewProp_OnCombatAnalyticsUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronCombatBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronCombatBridge_Statics::ClassParams = {
	&UAuracronCombatBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronCombatBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronCombatBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronCombatBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton, Z_Construct_UClass_UAuracronCombatBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronCombatBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronCombatBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentCombatLayer(TEXT("CurrentCombatLayer"));
	const bool bIsValid = true
		&& Name_CurrentCombatLayer == ClassReps[(int32)ENetFields_Private::CurrentCombatLayer].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronCombatBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronCombatBridge);
UAuracronCombatBridge::~UAuracronCombatBridge() {}
// ********** End Class UAuracronCombatBridge ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronDamageType_StaticEnum, TEXT("EAuracronDamageType"), &Z_Registration_Info_UEnum_EAuracronDamageType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2470854689U) },
		{ EAuracronCombatLayer_StaticEnum, TEXT("EAuracronCombatLayer"), &Z_Registration_Info_UEnum_EAuracronCombatLayer, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3955355270U) },
		{ EAuracronTargetingType_StaticEnum, TEXT("EAuracronTargetingType"), &Z_Registration_Info_UEnum_EAuracronTargetingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3374031875U) },
		{ EAuracronElementalType_StaticEnum, TEXT("EAuracronElementalType"), &Z_Registration_Info_UEnum_EAuracronElementalType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2822706140U) },
		{ EAuracronAICombatBehavior_StaticEnum, TEXT("EAuracronAICombatBehavior"), &Z_Registration_Info_UEnum_EAuracronAICombatBehavior, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2928806894U) },
		{ EAuracronComboType_StaticEnum, TEXT("EAuracronComboType"), &Z_Registration_Info_UEnum_EAuracronComboType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 916783320U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDamageConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics::NewStructOps, TEXT("AuracronDamageConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDamageConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDamageConfiguration), 2783236807U) },
		{ FAuracronTargetingConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics::NewStructOps, TEXT("AuracronTargetingConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronTargetingConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTargetingConfiguration), 2989207853U) },
		{ FAuracronTargetingResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics::NewStructOps, TEXT("AuracronTargetingResult"), &Z_Registration_Info_UScriptStruct_FAuracronTargetingResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTargetingResult), 282061631U) },
		{ FAuracronCombatEffectsConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics::NewStructOps, TEXT("AuracronCombatEffectsConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronCombatEffectsConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCombatEffectsConfiguration), 3448964008U) },
		{ FAuracronEnhancedInputConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics::NewStructOps, TEXT("AuracronEnhancedInputConfig"), &Z_Registration_Info_UScriptStruct_FAuracronEnhancedInputConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronEnhancedInputConfig), 2243282029U) },
		{ FAuracronAICombatConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics::NewStructOps, TEXT("AuracronAICombatConfig"), &Z_Registration_Info_UScriptStruct_FAuracronAICombatConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAICombatConfig), 517528943U) },
		{ FAuracronElementalDamageConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics::NewStructOps, TEXT("AuracronElementalDamageConfig"), &Z_Registration_Info_UScriptStruct_FAuracronElementalDamageConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronElementalDamageConfig), 1909653023U) },
		{ FAuracronCombatAnalytics::StaticStruct, Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics::NewStructOps, TEXT("AuracronCombatAnalytics"), &Z_Registration_Info_UScriptStruct_FAuracronCombatAnalytics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCombatAnalytics), 2913893938U) },
		{ FAuracronComboConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronComboConfig_Statics::NewStructOps, TEXT("AuracronComboConfig"), &Z_Registration_Info_UScriptStruct_FAuracronComboConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronComboConfig), 2639411205U) },
		{ FAuracronAdvancedDestructionConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics::NewStructOps, TEXT("AuracronAdvancedDestructionConfig"), &Z_Registration_Info_UScriptStruct_FAuracronAdvancedDestructionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAdvancedDestructionConfig), 886306391U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronCombatBridge, UAuracronCombatBridge::StaticClass, TEXT("UAuracronCombatBridge"), &Z_Registration_Info_UClass_UAuracronCombatBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronCombatBridge), 2603379393U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_1770749327(TEXT("/Script/AuracronCombatBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h__Script_AuracronCombatBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
