// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "FoliageEditModule.h"
#include "AuracronFoliage.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeFoliageEditModule() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UFoliageEditUtility();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UFoliageEditUtility_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageInstanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FFoliageEditSettings();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
FOLIAGE_API UClass* Z_Construct_UClass_UFoliageType_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EFoliageEditMode **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EFoliageEditMode;
static UEnum* EFoliageEditMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EFoliageEditMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EFoliageEditMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EFoliageEditMode"));
	}
	return Z_Registration_Info_UEnum_EFoliageEditMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EFoliageEditMode>()
{
	return EFoliageEditMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum for foliage edit modes\n */" },
#endif
		{ "Erase.DisplayName", "Erase" },
		{ "Erase.Name", "EFoliageEditMode::Erase" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
		{ "Paint.DisplayName", "Paint" },
		{ "Paint.Name", "EFoliageEditMode::Paint" },
		{ "Reapply.DisplayName", "Reapply" },
		{ "Reapply.Name", "EFoliageEditMode::Reapply" },
		{ "Select.DisplayName", "Select" },
		{ "Select.Name", "EFoliageEditMode::Select" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for foliage edit modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EFoliageEditMode::Paint", (int64)EFoliageEditMode::Paint },
		{ "EFoliageEditMode::Erase", (int64)EFoliageEditMode::Erase },
		{ "EFoliageEditMode::Select", (int64)EFoliageEditMode::Select },
		{ "EFoliageEditMode::Reapply", (int64)EFoliageEditMode::Reapply },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EFoliageEditMode",
	"EFoliageEditMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode()
{
	if (!Z_Registration_Info_UEnum_EFoliageEditMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EFoliageEditMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EFoliageEditMode.InnerSingleton;
}
// ********** End Enum EFoliageEditMode ************************************************************

// ********** Begin Enum EFoliageEditToolType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EFoliageEditToolType;
static UEnum* EFoliageEditToolType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EFoliageEditToolType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EFoliageEditToolType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EFoliageEditToolType"));
	}
	return Z_Registration_Info_UEnum_EFoliageEditToolType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EFoliageEditToolType>()
{
	return EFoliageEditToolType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Brush.DisplayName", "Brush" },
		{ "Brush.Name", "EFoliageEditToolType::Brush" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum for foliage edit tool types\n */" },
#endif
		{ "Fill.DisplayName", "Fill" },
		{ "Fill.Name", "EFoliageEditToolType::Fill" },
		{ "Lasso.DisplayName", "Lasso" },
		{ "Lasso.Name", "EFoliageEditToolType::Lasso" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for foliage edit tool types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EFoliageEditToolType::Brush", (int64)EFoliageEditToolType::Brush },
		{ "EFoliageEditToolType::Lasso", (int64)EFoliageEditToolType::Lasso },
		{ "EFoliageEditToolType::Fill", (int64)EFoliageEditToolType::Fill },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EFoliageEditToolType",
	"EFoliageEditToolType",
	Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType()
{
	if (!Z_Registration_Info_UEnum_EFoliageEditToolType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EFoliageEditToolType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EFoliageEditToolType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EFoliageEditToolType.InnerSingleton;
}
// ********** End Enum EFoliageEditToolType ********************************************************

// ********** Begin ScriptStruct FFoliageEditSettings **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FFoliageEditSettings;
class UScriptStruct* FFoliageEditSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FFoliageEditSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FFoliageEditSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FFoliageEditSettings, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("FoliageEditSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FFoliageEditSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FFoliageEditSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Struct for foliage edit settings\n */" },
#endif
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct for foliage edit settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrushSize_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaintDensity_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EraseDensity_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToNormal_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomYaw_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleRange_MetaData[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrushSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PaintDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EraseDensity;
	static void NewProp_bAlignToNormal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToNormal;
	static void NewProp_bRandomYaw_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomYaw;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FFoliageEditSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_BrushSize = { "BrushSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFoliageEditSettings, BrushSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrushSize_MetaData), NewProp_BrushSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_PaintDensity = { "PaintDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFoliageEditSettings, PaintDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaintDensity_MetaData), NewProp_PaintDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_EraseDensity = { "EraseDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFoliageEditSettings, EraseDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EraseDensity_MetaData), NewProp_EraseDensity_MetaData) };
void Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bAlignToNormal_SetBit(void* Obj)
{
	((FFoliageEditSettings*)Obj)->bAlignToNormal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bAlignToNormal = { "bAlignToNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFoliageEditSettings), &Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bAlignToNormal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToNormal_MetaData), NewProp_bAlignToNormal_MetaData) };
void Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bRandomYaw_SetBit(void* Obj)
{
	((FFoliageEditSettings*)Obj)->bRandomYaw = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bRandomYaw = { "bRandomYaw", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FFoliageEditSettings), &Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bRandomYaw_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomYaw_MetaData), NewProp_bRandomYaw_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_ScaleRange = { "ScaleRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FFoliageEditSettings, ScaleRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleRange_MetaData), NewProp_ScaleRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_BrushSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_PaintDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_EraseDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bAlignToNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_bRandomYaw,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewProp_ScaleRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"FoliageEditSettings",
	Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::PropPointers),
	sizeof(FFoliageEditSettings),
	alignof(FFoliageEditSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FFoliageEditSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FFoliageEditSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FFoliageEditSettings.InnerSingleton, Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FFoliageEditSettings.InnerSingleton;
}
// ********** End ScriptStruct FFoliageEditSettings ************************************************

// ********** Begin Class UFoliageEditUtility Function AddFoliageType ******************************
struct Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics
{
	struct FoliageEditUtility_eventAddFoliageType_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventAddFoliageType_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventAddFoliageType_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventAddFoliageType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventAddFoliageType_Parms), &Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "AddFoliageType", Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::FoliageEditUtility_eventAddFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::FoliageEditUtility_eventAddFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execAddFoliageType)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::AddFoliageType(Z_Param_World,Z_Param_FoliageType);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function AddFoliageType ********************************

// ********** Begin Class UFoliageEditUtility Function CreateFoliageType ***************************
struct Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics
{
	struct FoliageEditUtility_eventCreateFoliageType_Parms
	{
		UStaticMesh* StaticMesh;
		UFoliageType* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage Type Management\n" },
#endif
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Type Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventCreateFoliageType_Parms, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventCreateFoliageType_Parms, ReturnValue), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "CreateFoliageType", Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::FoliageEditUtility_eventCreateFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::FoliageEditUtility_eventCreateFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execCreateFoliageType)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_StaticMesh);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UFoliageType**)Z_Param__Result=UFoliageEditUtility::CreateFoliageType(Z_Param_StaticMesh);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function CreateFoliageType *****************************

// ********** Begin Class UFoliageEditUtility Function EraseFoliage ********************************
struct Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics
{
	struct FoliageEditUtility_eventEraseFoliage_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		FVector Location;
		FFoliageEditSettings Settings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventEraseFoliage_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventEraseFoliage_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventEraseFoliage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventEraseFoliage_Parms, Settings), Z_Construct_UScriptStruct_FFoliageEditSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 789320608
void Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventEraseFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventEraseFoliage_Parms), &Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_Settings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "EraseFoliage", Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::FoliageEditUtility_eventEraseFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::FoliageEditUtility_eventEraseFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execEraseFoliage)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FFoliageEditSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::EraseFoliage(Z_Param_World,Z_Param_FoliageType,Z_Param_Out_Location,Z_Param_Out_Settings);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function EraseFoliage **********************************

// ********** Begin Class UFoliageEditUtility Function GetFoliageInstanceCount *********************
struct Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics
{
	struct FoliageEditUtility_eventGetFoliageInstanceCount_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage Instance Management\n" },
#endif
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Instance Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstanceCount_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstanceCount_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "GetFoliageInstanceCount", Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::FoliageEditUtility_eventGetFoliageInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::FoliageEditUtility_eventGetFoliageInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execGetFoliageInstanceCount)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=UFoliageEditUtility::GetFoliageInstanceCount(Z_Param_World,Z_Param_FoliageType);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function GetFoliageInstanceCount ***********************

// ********** Begin Class UFoliageEditUtility Function GetFoliageInstances *************************
struct Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics
{
	struct FoliageEditUtility_eventGetFoliageInstances_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		TArray<FAuracronFoliageInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstances_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstances_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageInstances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "GetFoliageInstances", Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::FoliageEditUtility_eventGetFoliageInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::FoliageEditUtility_eventGetFoliageInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execGetFoliageInstances)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageInstanceData>*)Z_Param__Result=UFoliageEditUtility::GetFoliageInstances(Z_Param_World,Z_Param_FoliageType);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function GetFoliageInstances ***************************

// ********** Begin Class UFoliageEditUtility Function GetFoliageTypes *****************************
struct Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics
{
	struct FoliageEditUtility_eventGetFoliageTypes_Parms
	{
		UWorld* World;
		TArray<UFoliageType*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageTypes_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetFoliageTypes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "GetFoliageTypes", Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::FoliageEditUtility_eventGetFoliageTypes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::FoliageEditUtility_eventGetFoliageTypes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execGetFoliageTypes)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<UFoliageType*>*)Z_Param__Result=UFoliageEditUtility::GetFoliageTypes(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function GetFoliageTypes *******************************

// ********** Begin Class UFoliageEditUtility Function GetSurfaceHeight ****************************
struct Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics
{
	struct FoliageEditUtility_eventGetSurfaceHeight_Parms
	{
		UWorld* World;
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceHeight_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceHeight_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceHeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "GetSurfaceHeight", Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::FoliageEditUtility_eventGetSurfaceHeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::FoliageEditUtility_eventGetSurfaceHeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execGetSurfaceHeight)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=UFoliageEditUtility::GetSurfaceHeight(Z_Param_World,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function GetSurfaceHeight ******************************

// ********** Begin Class UFoliageEditUtility Function GetSurfaceNormal ****************************
struct Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics
{
	struct FoliageEditUtility_eventGetSurfaceNormal_Parms
	{
		UWorld* World;
		FVector Location;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceNormal_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceNormal_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventGetSurfaceNormal_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "GetSurfaceNormal", Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::FoliageEditUtility_eventGetSurfaceNormal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::FoliageEditUtility_eventGetSurfaceNormal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execGetSurfaceNormal)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=UFoliageEditUtility::GetSurfaceNormal(Z_Param_World,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function GetSurfaceNormal ******************************

// ********** Begin Class UFoliageEditUtility Function IsValidFoliageLocation **********************
struct Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics
{
	struct FoliageEditUtility_eventIsValidFoliageLocation_Parms
	{
		UWorld* World;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventIsValidFoliageLocation_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventIsValidFoliageLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventIsValidFoliageLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventIsValidFoliageLocation_Parms), &Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "IsValidFoliageLocation", Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::FoliageEditUtility_eventIsValidFoliageLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::FoliageEditUtility_eventIsValidFoliageLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execIsValidFoliageLocation)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::IsValidFoliageLocation(Z_Param_World,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function IsValidFoliageLocation ************************

// ********** Begin Class UFoliageEditUtility Function PaintFoliage ********************************
struct Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics
{
	struct FoliageEditUtility_eventPaintFoliage_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		FVector Location;
		FFoliageEditSettings Settings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage Editing Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Editing Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventPaintFoliage_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventPaintFoliage_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventPaintFoliage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventPaintFoliage_Parms, Settings), Z_Construct_UScriptStruct_FFoliageEditSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 789320608
void Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventPaintFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventPaintFoliage_Parms), &Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_Settings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "PaintFoliage", Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::FoliageEditUtility_eventPaintFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::FoliageEditUtility_eventPaintFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execPaintFoliage)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FFoliageEditSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::PaintFoliage(Z_Param_World,Z_Param_FoliageType,Z_Param_Out_Location,Z_Param_Out_Settings);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function PaintFoliage **********************************

// ********** Begin Class UFoliageEditUtility Function ReapplyFoliage ******************************
struct Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics
{
	struct FoliageEditUtility_eventReapplyFoliage_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		TArray<FAuracronFoliageInstanceData> Instances;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Instances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Instances;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventReapplyFoliage_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventReapplyFoliage_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_Instances_Inner = { "Instances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_Instances = { "Instances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventReapplyFoliage_Parms, Instances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instances_MetaData), NewProp_Instances_MetaData) }; // 1168665060
void Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventReapplyFoliage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventReapplyFoliage_Parms), &Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_Instances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "ReapplyFoliage", Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::FoliageEditUtility_eventReapplyFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::FoliageEditUtility_eventReapplyFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execReapplyFoliage)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_GET_TARRAY_REF(FAuracronFoliageInstanceData,Z_Param_Out_Instances);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::ReapplyFoliage(Z_Param_World,Z_Param_FoliageType,Z_Param_Out_Instances);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function ReapplyFoliage ********************************

// ********** Begin Class UFoliageEditUtility Function RemoveFoliageType ***************************
struct Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics
{
	struct FoliageEditUtility_eventRemoveFoliageType_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventRemoveFoliageType_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventRemoveFoliageType_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventRemoveFoliageType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventRemoveFoliageType_Parms), &Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "RemoveFoliageType", Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::FoliageEditUtility_eventRemoveFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::FoliageEditUtility_eventRemoveFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execRemoveFoliageType)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::RemoveFoliageType(Z_Param_World,Z_Param_FoliageType);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function RemoveFoliageType *****************************

// ********** Begin Class UFoliageEditUtility Function SelectFoliage *******************************
struct Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics
{
	struct FoliageEditUtility_eventSelectFoliage_Parms
	{
		UWorld* World;
		FVector Location;
		float Radius;
		TArray<FAuracronFoliageInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventSelectFoliage_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventSelectFoliage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventSelectFoliage_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventSelectFoliage_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "SelectFoliage", Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::FoliageEditUtility_eventSelectFoliage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::FoliageEditUtility_eventSelectFoliage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execSelectFoliage)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageInstanceData>*)Z_Param__Result=UFoliageEditUtility::SelectFoliage(Z_Param_World,Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function SelectFoliage *********************************

// ********** Begin Class UFoliageEditUtility Function UpdateFoliageInstances **********************
struct Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics
{
	struct FoliageEditUtility_eventUpdateFoliageInstances_Parms
	{
		UWorld* World;
		UFoliageType* FoliageType;
		TArray<FAuracronFoliageInstanceData> Instances;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Edit" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instances_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Instances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Instances;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventUpdateFoliageInstances_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventUpdateFoliageInstances_Parms, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_Instances_Inner = { "Instances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_Instances = { "Instances", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FoliageEditUtility_eventUpdateFoliageInstances_Parms, Instances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instances_MetaData), NewProp_Instances_MetaData) }; // 1168665060
void Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((FoliageEditUtility_eventUpdateFoliageInstances_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FoliageEditUtility_eventUpdateFoliageInstances_Parms), &Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_Instances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UFoliageEditUtility, nullptr, "UpdateFoliageInstances", Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::FoliageEditUtility_eventUpdateFoliageInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::FoliageEditUtility_eventUpdateFoliageInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UFoliageEditUtility::execUpdateFoliageInstances)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_OBJECT(UFoliageType,Z_Param_FoliageType);
	P_GET_TARRAY_REF(FAuracronFoliageInstanceData,Z_Param_Out_Instances);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=UFoliageEditUtility::UpdateFoliageInstances(Z_Param_World,Z_Param_FoliageType,Z_Param_Out_Instances);
	P_NATIVE_END;
}
// ********** End Class UFoliageEditUtility Function UpdateFoliageInstances ************************

// ********** Begin Class UFoliageEditUtility ******************************************************
void UFoliageEditUtility::StaticRegisterNativesUFoliageEditUtility()
{
	UClass* Class = UFoliageEditUtility::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddFoliageType", &UFoliageEditUtility::execAddFoliageType },
		{ "CreateFoliageType", &UFoliageEditUtility::execCreateFoliageType },
		{ "EraseFoliage", &UFoliageEditUtility::execEraseFoliage },
		{ "GetFoliageInstanceCount", &UFoliageEditUtility::execGetFoliageInstanceCount },
		{ "GetFoliageInstances", &UFoliageEditUtility::execGetFoliageInstances },
		{ "GetFoliageTypes", &UFoliageEditUtility::execGetFoliageTypes },
		{ "GetSurfaceHeight", &UFoliageEditUtility::execGetSurfaceHeight },
		{ "GetSurfaceNormal", &UFoliageEditUtility::execGetSurfaceNormal },
		{ "IsValidFoliageLocation", &UFoliageEditUtility::execIsValidFoliageLocation },
		{ "PaintFoliage", &UFoliageEditUtility::execPaintFoliage },
		{ "ReapplyFoliage", &UFoliageEditUtility::execReapplyFoliage },
		{ "RemoveFoliageType", &UFoliageEditUtility::execRemoveFoliageType },
		{ "SelectFoliage", &UFoliageEditUtility::execSelectFoliage },
		{ "UpdateFoliageInstances", &UFoliageEditUtility::execUpdateFoliageInstances },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UFoliageEditUtility;
UClass* UFoliageEditUtility::GetPrivateStaticClass()
{
	using TClass = UFoliageEditUtility;
	if (!Z_Registration_Info_UClass_UFoliageEditUtility.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("FoliageEditUtility"),
			Z_Registration_Info_UClass_UFoliageEditUtility.InnerSingleton,
			StaticRegisterNativesUFoliageEditUtility,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UFoliageEditUtility.InnerSingleton;
}
UClass* Z_Construct_UClass_UFoliageEditUtility_NoRegister()
{
	return UFoliageEditUtility::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UFoliageEditUtility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Edit Utility Class\n */" },
#endif
		{ "IncludePath", "FoliageEditModule.h" },
		{ "ModuleRelativePath", "Public/FoliageEditModule.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Edit Utility Class" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UFoliageEditUtility_AddFoliageType, "AddFoliageType" }, // 3141024579
		{ &Z_Construct_UFunction_UFoliageEditUtility_CreateFoliageType, "CreateFoliageType" }, // 4052141868
		{ &Z_Construct_UFunction_UFoliageEditUtility_EraseFoliage, "EraseFoliage" }, // 257077457
		{ &Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstanceCount, "GetFoliageInstanceCount" }, // 1243528729
		{ &Z_Construct_UFunction_UFoliageEditUtility_GetFoliageInstances, "GetFoliageInstances" }, // 3734697345
		{ &Z_Construct_UFunction_UFoliageEditUtility_GetFoliageTypes, "GetFoliageTypes" }, // 2739912076
		{ &Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceHeight, "GetSurfaceHeight" }, // 1647656082
		{ &Z_Construct_UFunction_UFoliageEditUtility_GetSurfaceNormal, "GetSurfaceNormal" }, // 1817741061
		{ &Z_Construct_UFunction_UFoliageEditUtility_IsValidFoliageLocation, "IsValidFoliageLocation" }, // 3467845581
		{ &Z_Construct_UFunction_UFoliageEditUtility_PaintFoliage, "PaintFoliage" }, // 1026130452
		{ &Z_Construct_UFunction_UFoliageEditUtility_ReapplyFoliage, "ReapplyFoliage" }, // 1535092486
		{ &Z_Construct_UFunction_UFoliageEditUtility_RemoveFoliageType, "RemoveFoliageType" }, // 2456821427
		{ &Z_Construct_UFunction_UFoliageEditUtility_SelectFoliage, "SelectFoliage" }, // 111206640
		{ &Z_Construct_UFunction_UFoliageEditUtility_UpdateFoliageInstances, "UpdateFoliageInstances" }, // 1278185320
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UFoliageEditUtility>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UFoliageEditUtility_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UFoliageEditUtility_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UFoliageEditUtility_Statics::ClassParams = {
	&UFoliageEditUtility::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UFoliageEditUtility_Statics::Class_MetaDataParams), Z_Construct_UClass_UFoliageEditUtility_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UFoliageEditUtility()
{
	if (!Z_Registration_Info_UClass_UFoliageEditUtility.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UFoliageEditUtility.OuterSingleton, Z_Construct_UClass_UFoliageEditUtility_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UFoliageEditUtility.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UFoliageEditUtility);
UFoliageEditUtility::~UFoliageEditUtility() {}
// ********** End Class UFoliageEditUtility ********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EFoliageEditMode_StaticEnum, TEXT("EFoliageEditMode"), &Z_Registration_Info_UEnum_EFoliageEditMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4092600887U) },
		{ EFoliageEditToolType_StaticEnum, TEXT("EFoliageEditToolType"), &Z_Registration_Info_UEnum_EFoliageEditToolType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2490687217U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FFoliageEditSettings::StaticStruct, Z_Construct_UScriptStruct_FFoliageEditSettings_Statics::NewStructOps, TEXT("FoliageEditSettings"), &Z_Registration_Info_UScriptStruct_FFoliageEditSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FFoliageEditSettings), 789320608U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UFoliageEditUtility, UFoliageEditUtility::StaticClass, TEXT("UFoliageEditUtility"), &Z_Registration_Info_UClass_UFoliageEditUtility, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UFoliageEditUtility), 1900527053U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_2522581631(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
