# Script para corrigir todas as definicoes duplicadas de classes
# Remove as definicoes UCLASS duplicadas que foram criadas pela expansao da macro

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao de todas as duplicacoes de classes..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Lista de padroes de duplicacao para corrigir
$duplicatePatterns = @(
    # Padrao: UCLASS seguido de class com mesmo nome
    @{
        Pattern = 'UCLASS\s*\([^)]*\)\s*\n\s*class\s+AURACRONPCGBRIDGE_API\s+(\w+)\s*:\s*public\s+\w+\s*\{\s*\n\s*GENERATED_BODY\s*\(\s*\)'
        Description = "UCLASS duplicada com class e GENERATED_BODY"
    }
)

# Encontra todos os arquivos .h
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalFixes = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileFixes = 0
    
    # Aplica cada padrao de correcao
    foreach ($patternInfo in $duplicatePatterns) {
        $pattern = $patternInfo.Pattern
        $description = $patternInfo.Description
        
        # Encontra todas as ocorrencias do padrao
        $matches = [regex]::Matches($content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
        
        foreach ($match in $matches) {
            $fullMatch = $match.Value
            $className = $match.Groups[1].Value
            
            Write-Host "    Removendo: $description para $className" -ForegroundColor Yellow
            
            # Remove a duplicacao
            $content = $content -replace [regex]::Escape($fullMatch), ""
            $fileFixes++
        }
    }
    
    # Correcoes especificas por regex mais simples
    $simplePatterns = @(
        # Remove UCLASS duplicadas dentro de classes (com identacao)
        '^\s{4,}UCLASS\s*\([^)]*\)\s*$',
        # Remove declaracoes de classe duplicadas dentro de outras classes
        '^\s{4,}class\s+AURACRONPCGBRIDGE_API\s+\w+\s*:\s*public\s+\w+\s*\{\s*$',
        # Remove GENERATED_BODY duplicados dentro de classes
        '^\s{4,}GENERATED_BODY\s*\(\s*\)\s*$'
    )
    
    foreach ($pattern in $simplePatterns) {
        $matches = [regex]::Matches($content, $pattern, [System.Text.RegularExpressions.RegexOptions]::Multiline)
        if ($matches.Count -gt 0) {
            Write-Host "    Removendo $($matches.Count) ocorrencias do padrao: $pattern" -ForegroundColor Yellow
            $content = $content -replace $pattern, ""
            $fileFixes += $matches.Count
        }
    }
    
    # Remove linhas vazias excessivas
    $content = $content -replace '\n\s*\n\s*\n', "`n`n"
    
    # Salva o arquivo se houve mudancas
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalFixes += $fileFixes
        $filesModified++
        Write-Host "  OK $fileFixes duplicacoes corrigidas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma duplicacao encontrada" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de duplicacoes corrigidas: $totalFixes" -ForegroundColor Green

if ($totalFixes -gt 0) {
    Write-Host "`nCorrecao de duplicacoes concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma duplicacao encontrada." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
