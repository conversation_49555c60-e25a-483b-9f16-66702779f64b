// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGAdvanced.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGAdvanced_generated_h
#error "AuracronPCGAdvanced.generated.h already included, missing '#pragma once' in AuracronPCGAdvanced.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGAdvanced_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronBiomeConfig **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_36_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBiomeConfig;
// ********** End ScriptStruct FAuracronBiomeConfig ************************************************

// ********** Begin ScriptStruct FAuracronTerrainParams ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_95_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTerrainParams;
// ********** End ScriptStruct FAuracronTerrainParams **********************************************

// ********** Begin ScriptStruct FAuracronStructurePlacementRule ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_136_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStructurePlacementRule;
// ********** End ScriptStruct FAuracronStructurePlacementRule *************************************

// ********** Begin ScriptStruct FAuracronResourceDistribution *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_181_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronResourceDistribution;
// ********** End ScriptStruct FAuracronResourceDistribution ***************************************

// ********** Begin Class UAuracronMasterPCGSettings ***********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronMasterPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_234_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMasterPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronMasterPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronMasterPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMasterPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronMasterPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMasterPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_234_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMasterPCGSettings(UAuracronMasterPCGSettings&&) = delete; \
	UAuracronMasterPCGSettings(const UAuracronMasterPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMasterPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMasterPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronMasterPCGSettings) \
	NO_API virtual ~UAuracronMasterPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_231_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_234_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_234_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_234_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMasterPCGSettings;

// ********** End Class UAuracronMasterPCGSettings *************************************************

// ********** Begin Class UAuracronBiomeTransitionPCGSettings **************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_294_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronBiomeTransitionPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronBiomeTransitionPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronBiomeTransitionPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_294_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronBiomeTransitionPCGSettings(UAuracronBiomeTransitionPCGSettings&&) = delete; \
	UAuracronBiomeTransitionPCGSettings(const UAuracronBiomeTransitionPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronBiomeTransitionPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronBiomeTransitionPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronBiomeTransitionPCGSettings) \
	NO_API virtual ~UAuracronBiomeTransitionPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_291_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_294_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_294_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_294_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronBiomeTransitionPCGSettings;

// ********** End Class UAuracronBiomeTransitionPCGSettings ****************************************

// ********** Begin Class UAuracronQuestPCGSettings ************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronQuestPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_338_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronQuestPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronQuestPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronQuestPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronQuestPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronQuestPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronQuestPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_338_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronQuestPCGSettings(UAuracronQuestPCGSettings&&) = delete; \
	UAuracronQuestPCGSettings(const UAuracronQuestPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronQuestPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronQuestPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronQuestPCGSettings) \
	NO_API virtual ~UAuracronQuestPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_335_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_338_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_338_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_338_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronQuestPCGSettings;

// ********** End Class UAuracronQuestPCGSettings **************************************************

// ********** Begin Class UAuracronPerformancePCGSettings ******************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_386_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPerformancePCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPerformancePCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPerformancePCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPerformancePCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_386_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPerformancePCGSettings(UAuracronPerformancePCGSettings&&) = delete; \
	UAuracronPerformancePCGSettings(const UAuracronPerformancePCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPerformancePCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPerformancePCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPerformancePCGSettings) \
	NO_API virtual ~UAuracronPerformancePCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_383_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_386_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_386_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h_386_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPerformancePCGSettings;

// ********** End Class UAuracronPerformancePCGSettings ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
