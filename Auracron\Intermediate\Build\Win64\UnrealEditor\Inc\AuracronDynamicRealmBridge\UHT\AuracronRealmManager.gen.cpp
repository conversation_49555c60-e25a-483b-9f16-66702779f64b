// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronRealmManager.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronRealmManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronRealmManager();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronRealmManager_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmContentConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmEnvironment();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
FOLIAGE_API UClass* Z_Construct_UClass_UFoliageType_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronRealmContentConfig ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig;
class UScriptStruct* FAuracronRealmContentConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmContentConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronRealmContentConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Realm content configuration data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm content configuration data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmMeshes_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static meshes for this realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static meshes for this realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmMaterials_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Materials for this realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materials for this realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmEffects_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Niagara effects for this realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Niagara effects for this realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraphs_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** PCG graphs for procedural generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG graphs for procedural generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypes_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Foliage types for this realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage types for this realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmAudio_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio assets for this realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio assets for this realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RealmMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RealmMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmMaterials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RealmEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmEffects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGGraphs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGGraphs;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FoliageTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FoliageTypes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RealmAudio_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmAudio;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmContentConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMeshes_Inner = { "RealmMeshes", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMeshes = { "RealmMeshes", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, RealmMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmMeshes_MetaData), NewProp_RealmMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMaterials_Inner = { "RealmMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMaterials = { "RealmMaterials", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, RealmMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmMaterials_MetaData), NewProp_RealmMaterials_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmEffects_Inner = { "RealmEffects", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmEffects = { "RealmEffects", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, RealmEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmEffects_MetaData), NewProp_RealmEffects_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_PCGGraphs_Inner = { "PCGGraphs", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_PCGGraphs = { "PCGGraphs", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, PCGGraphs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraphs_MetaData), NewProp_PCGGraphs_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_FoliageTypes_Inner = { "FoliageTypes", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_FoliageTypes = { "FoliageTypes", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, FoliageTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypes_MetaData), NewProp_FoliageTypes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmAudio_Inner = { "RealmAudio", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmAudio = { "RealmAudio", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmContentConfig, RealmAudio), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmAudio_MetaData), NewProp_RealmAudio_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_PCGGraphs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_PCGGraphs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_FoliageTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_FoliageTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmAudio_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewProp_RealmAudio,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmContentConfig",
	Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::PropPointers),
	sizeof(FAuracronRealmContentConfig),
	alignof(FAuracronRealmContentConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmContentConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmContentConfig *****************************************

// ********** Begin ScriptStruct FAuracronRealmEnvironment *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment;
class UScriptStruct* FAuracronRealmEnvironment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmEnvironment, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronRealmEnvironment"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Realm environmental settings\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm environmental settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lighting settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightIntensity_MetaData[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogColor_MetaData[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atmospheric settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atmospheric settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogDensity_MetaData[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogStartDistance_MetaData[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasWeatherEffects_MetaData[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherIntensity_MetaData[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityScale_MetaData[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gravity settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gravity settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindDirection_MetaData[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Wind effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WindStrength_MetaData[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientLightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FogColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogStartDistance;
	static void NewProp_bHasWeatherEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasWeatherEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WindDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WindStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmEnvironment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_AmbientLightIntensity = { "AmbientLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, AmbientLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightIntensity_MetaData), NewProp_AmbientLightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogColor = { "FogColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, FogColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogColor_MetaData), NewProp_FogColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogDensity = { "FogDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, FogDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogDensity_MetaData), NewProp_FogDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogStartDistance = { "FogStartDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, FogStartDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogStartDistance_MetaData), NewProp_FogStartDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_bHasWeatherEffects_SetBit(void* Obj)
{
	((FAuracronRealmEnvironment*)Obj)->bHasWeatherEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_bHasWeatherEffects = { "bHasWeatherEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmEnvironment), &Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_bHasWeatherEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasWeatherEffects_MetaData), NewProp_bHasWeatherEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WeatherIntensity = { "WeatherIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, WeatherIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherIntensity_MetaData), NewProp_WeatherIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_GravityScale = { "GravityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, GravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityScale_MetaData), NewProp_GravityScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WindDirection = { "WindDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, WindDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindDirection_MetaData), NewProp_WindDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WindStrength = { "WindStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmEnvironment, WindStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WindStrength_MetaData), NewProp_WindStrength_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_AmbientLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_FogStartDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_bHasWeatherEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WeatherIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_GravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WindDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewProp_WindStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmEnvironment",
	Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::PropPointers),
	sizeof(FAuracronRealmEnvironment),
	alignof(FAuracronRealmEnvironment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmEnvironment()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmEnvironment *******************************************

// ********** Begin Class AAuracronRealmManager Function ApplyEnvironmentalEffects *****************
struct Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Environment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Environmental effects\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Environmental effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "ApplyEnvironmentalEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execApplyEnvironmentalEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyEnvironmentalEffects();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function ApplyEnvironmentalEffects *******************

// ********** Begin Class AAuracronRealmManager Function CullDistantContent ************************
struct Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics
{
	struct AuracronRealmManager_eventCullDistantContent_Parms
	{
		FVector ViewerLocation;
		float CullDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventCullDistantContent_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::NewProp_CullDistance = { "CullDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventCullDistantContent_Parms, CullDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::NewProp_CullDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "CullDistantContent", Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::AuracronRealmManager_eventCullDistantContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::AuracronRealmManager_eventCullDistantContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execCullDistantContent)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CullDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CullDistantContent(Z_Param_Out_ViewerLocation,Z_Param_CullDistance);
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function CullDistantContent **************************

// ********** Begin Class AAuracronRealmManager Function DebugRegenerateContent ********************
struct Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "DebugRegenerateContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execDebugRegenerateContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugRegenerateContent();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function DebugRegenerateContent **********************

// ********** Begin Class AAuracronRealmManager Function DebugShowLayerBounds **********************
struct Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "DebugShowLayerBounds", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execDebugShowLayerBounds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugShowLayerBounds();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function DebugShowLayerBounds ************************

// ********** Begin Class AAuracronRealmManager Function DebugShowPerformanceStats *****************
struct Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "DebugShowPerformanceStats", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execDebugShowPerformanceStats)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugShowPerformanceStats();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function DebugShowPerformanceStats *******************

// ********** Begin Class AAuracronRealmManager Function DespawnRealmContent ***********************
struct Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "DespawnRealmContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execDespawnRealmContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DespawnRealmContent();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function DespawnRealmContent *************************

// ********** Begin Class AAuracronRealmManager Function GenerateLayerContent **********************
struct Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "GenerateLayerContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execGenerateLayerContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateLayerContent();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function GenerateLayerContent ************************

// ********** Begin Class AAuracronRealmManager Function GetCurrentPerformanceMetric ***************
struct Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics
{
	struct AuracronRealmManager_eventGetCurrentPerformanceMetric_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventGetCurrentPerformanceMetric_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "GetCurrentPerformanceMetric", Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::AuracronRealmManager_eventGetCurrentPerformanceMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::AuracronRealmManager_eventGetCurrentPerformanceMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execGetCurrentPerformanceMetric)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentPerformanceMetric();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function GetCurrentPerformanceMetric *****************

// ********** Begin Class AAuracronRealmManager Function InitializeLayer ***************************
struct Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer management functions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer management functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "InitializeLayer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execInitializeLayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeLayer();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function InitializeLayer *****************************

// ********** Begin Class AAuracronRealmManager Function OptimizeLayerPerformance ******************
struct Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "OptimizeLayerPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execOptimizeLayerPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeLayerPerformance();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function OptimizeLayerPerformance ********************

// ********** Begin Class AAuracronRealmManager Function SetEnvironmentSettings ********************
struct Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics
{
	struct AuracronRealmManager_eventSetEnvironmentSettings_Parms
	{
		FAuracronRealmEnvironment NewSettings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::NewProp_NewSettings = { "NewSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventSetEnvironmentSettings_Parms, NewSettings), Z_Construct_UScriptStruct_FAuracronRealmEnvironment, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSettings_MetaData), NewProp_NewSettings_MetaData) }; // 2640511407
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::NewProp_NewSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "SetEnvironmentSettings", Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::AuracronRealmManager_eventSetEnvironmentSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::AuracronRealmManager_eventSetEnvironmentSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execSetEnvironmentSettings)
{
	P_GET_STRUCT_REF(FAuracronRealmEnvironment,Z_Param_Out_NewSettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentSettings(Z_Param_Out_NewSettings);
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function SetEnvironmentSettings **********************

// ********** Begin Class AAuracronRealmManager Function SetLayerLODLevel **************************
struct Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics
{
	struct AuracronRealmManager_eventSetLayerLODLevel_Parms
	{
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventSetLayerLODLevel_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "SetLayerLODLevel", Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::AuracronRealmManager_eventSetLayerLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::AuracronRealmManager_eventSetLayerLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execSetLayerLODLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerLODLevel(Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function SetLayerLODLevel ****************************

// ********** Begin Class AAuracronRealmManager Function SetLayerVisibility ************************
struct Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics
{
	struct AuracronRealmManager_eventSetLayerVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AuracronRealmManager_eventSetLayerVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmManager_eventSetLayerVisibility_Parms), &Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "SetLayerVisibility", Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::AuracronRealmManager_eventSetLayerVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::AuracronRealmManager_eventSetLayerVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execSetLayerVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function SetLayerVisibility **************************

// ********** Begin Class AAuracronRealmManager Function SpawnRealmContent *************************
struct Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Content management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Content management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "SpawnRealmContent", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execSpawnRealmContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnRealmContent();
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function SpawnRealmContent ***************************

// ********** Begin Class AAuracronRealmManager Function UpdateContentBasedOnPhase *****************
struct Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics
{
	struct AuracronRealmManager_eventUpdateContentBasedOnPhase_Parms
	{
		ERealmEvolutionPhase Phase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventUpdateContentBasedOnPhase_Parms, Phase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(0, nullptr) }; // 560471848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::NewProp_Phase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "UpdateContentBasedOnPhase", Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::AuracronRealmManager_eventUpdateContentBasedOnPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::AuracronRealmManager_eventUpdateContentBasedOnPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execUpdateContentBasedOnPhase)
{
	P_GET_ENUM(ERealmEvolutionPhase,Z_Param_Phase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateContentBasedOnPhase(ERealmEvolutionPhase(Z_Param_Phase));
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function UpdateContentBasedOnPhase *******************

// ********** Begin Class AAuracronRealmManager Function UpdateLayerEvolution **********************
struct Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics
{
	struct AuracronRealmManager_eventUpdateLayerEvolution_Parms
	{
		ERealmEvolutionPhase Phase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Management" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Phase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Phase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::NewProp_Phase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::NewProp_Phase = { "Phase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventUpdateLayerEvolution_Parms, Phase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(0, nullptr) }; // 560471848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::NewProp_Phase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::NewProp_Phase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "UpdateLayerEvolution", Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::AuracronRealmManager_eventUpdateLayerEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::AuracronRealmManager_eventUpdateLayerEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execUpdateLayerEvolution)
{
	P_GET_ENUM(ERealmEvolutionPhase,Z_Param_Phase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLayerEvolution(ERealmEvolutionPhase(Z_Param_Phase));
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function UpdateLayerEvolution ************************

// ********** Begin Class AAuracronRealmManager Function UpdateWeatherEffects **********************
struct Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics
{
	struct AuracronRealmManager_eventUpdateWeatherEffects_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Environment" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmManager_eventUpdateWeatherEffects_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronRealmManager, nullptr, "UpdateWeatherEffects", Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::AuracronRealmManager_eventUpdateWeatherEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::AuracronRealmManager_eventUpdateWeatherEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronRealmManager::execUpdateWeatherEffects)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateWeatherEffects(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronRealmManager Function UpdateWeatherEffects ************************

// ********** Begin Class AAuracronRealmManager ****************************************************
void AAuracronRealmManager::StaticRegisterNativesAAuracronRealmManager()
{
	UClass* Class = AAuracronRealmManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyEnvironmentalEffects", &AAuracronRealmManager::execApplyEnvironmentalEffects },
		{ "CullDistantContent", &AAuracronRealmManager::execCullDistantContent },
		{ "DebugRegenerateContent", &AAuracronRealmManager::execDebugRegenerateContent },
		{ "DebugShowLayerBounds", &AAuracronRealmManager::execDebugShowLayerBounds },
		{ "DebugShowPerformanceStats", &AAuracronRealmManager::execDebugShowPerformanceStats },
		{ "DespawnRealmContent", &AAuracronRealmManager::execDespawnRealmContent },
		{ "GenerateLayerContent", &AAuracronRealmManager::execGenerateLayerContent },
		{ "GetCurrentPerformanceMetric", &AAuracronRealmManager::execGetCurrentPerformanceMetric },
		{ "InitializeLayer", &AAuracronRealmManager::execInitializeLayer },
		{ "OptimizeLayerPerformance", &AAuracronRealmManager::execOptimizeLayerPerformance },
		{ "SetEnvironmentSettings", &AAuracronRealmManager::execSetEnvironmentSettings },
		{ "SetLayerLODLevel", &AAuracronRealmManager::execSetLayerLODLevel },
		{ "SetLayerVisibility", &AAuracronRealmManager::execSetLayerVisibility },
		{ "SpawnRealmContent", &AAuracronRealmManager::execSpawnRealmContent },
		{ "UpdateContentBasedOnPhase", &AAuracronRealmManager::execUpdateContentBasedOnPhase },
		{ "UpdateLayerEvolution", &AAuracronRealmManager::execUpdateLayerEvolution },
		{ "UpdateWeatherEffects", &AAuracronRealmManager::execUpdateWeatherEffects },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronRealmManager;
UClass* AAuracronRealmManager::GetPrivateStaticClass()
{
	using TClass = AAuracronRealmManager;
	if (!Z_Registration_Info_UClass_AAuracronRealmManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronRealmManager"),
			Z_Registration_Info_UClass_AAuracronRealmManager.InnerSingleton,
			StaticRegisterNativesAAuracronRealmManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronRealmManager.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronRealmManager_NoRegister()
{
	return AAuracronRealmManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronRealmManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Realm Manager\n * \n * Manages a specific realm layer (Terrestrial, Celestial, or Abyssal):\n * - Handles layer-specific content generation\n * - Manages environmental effects\n * - Controls layer visibility and LOD\n * - Integrates with PCG for procedural content\n * - Optimizes performance for the layer\n */" },
#endif
		{ "IncludePath", "AuracronRealmManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Realm Manager\n\nManages a specific realm layer (Terrestrial, Celestial, or Abyssal):\n- Handles layer-specific content generation\n- Manages environmental effects\n- Controls layer visibility and LOD\n- Integrates with PCG for procedural content\n- Optimizes performance for the layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedLayer_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContentConfig_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentSettings_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateContent_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEvolution_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedContent_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentComponents_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance metrics\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsage_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ManagedLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ManagedLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ContentConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnvironmentSettings;
	static void NewProp_bAutoGenerateContent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateContent;
	static void NewProp_bEnableEvolution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEvolution;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LayerComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EffectComponents;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedContent_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedContent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnvironmentComponents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronRealmManager_ApplyEnvironmentalEffects, "ApplyEnvironmentalEffects" }, // 390082447
		{ &Z_Construct_UFunction_AAuracronRealmManager_CullDistantContent, "CullDistantContent" }, // 607063143
		{ &Z_Construct_UFunction_AAuracronRealmManager_DebugRegenerateContent, "DebugRegenerateContent" }, // 4088717762
		{ &Z_Construct_UFunction_AAuracronRealmManager_DebugShowLayerBounds, "DebugShowLayerBounds" }, // 1880027231
		{ &Z_Construct_UFunction_AAuracronRealmManager_DebugShowPerformanceStats, "DebugShowPerformanceStats" }, // 2377364824
		{ &Z_Construct_UFunction_AAuracronRealmManager_DespawnRealmContent, "DespawnRealmContent" }, // 2962263369
		{ &Z_Construct_UFunction_AAuracronRealmManager_GenerateLayerContent, "GenerateLayerContent" }, // 2712746920
		{ &Z_Construct_UFunction_AAuracronRealmManager_GetCurrentPerformanceMetric, "GetCurrentPerformanceMetric" }, // 2057253470
		{ &Z_Construct_UFunction_AAuracronRealmManager_InitializeLayer, "InitializeLayer" }, // 3292206609
		{ &Z_Construct_UFunction_AAuracronRealmManager_OptimizeLayerPerformance, "OptimizeLayerPerformance" }, // 3593886737
		{ &Z_Construct_UFunction_AAuracronRealmManager_SetEnvironmentSettings, "SetEnvironmentSettings" }, // 533298547
		{ &Z_Construct_UFunction_AAuracronRealmManager_SetLayerLODLevel, "SetLayerLODLevel" }, // 1305688179
		{ &Z_Construct_UFunction_AAuracronRealmManager_SetLayerVisibility, "SetLayerVisibility" }, // 1450293233
		{ &Z_Construct_UFunction_AAuracronRealmManager_SpawnRealmContent, "SpawnRealmContent" }, // 3892870373
		{ &Z_Construct_UFunction_AAuracronRealmManager_UpdateContentBasedOnPhase, "UpdateContentBasedOnPhase" }, // 1058779813
		{ &Z_Construct_UFunction_AAuracronRealmManager_UpdateLayerEvolution, "UpdateLayerEvolution" }, // 1629622112
		{ &Z_Construct_UFunction_AAuracronRealmManager_UpdateWeatherEffects, "UpdateWeatherEffects" }, // 2570865527
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronRealmManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ManagedLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ManagedLayer = { "ManagedLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, ManagedLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedLayer_MetaData), NewProp_ManagedLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ContentConfig = { "ContentConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, ContentConfig), Z_Construct_UScriptStruct_FAuracronRealmContentConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContentConfig_MetaData), NewProp_ContentConfig_MetaData) }; // 3608437816
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentSettings = { "EnvironmentSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, EnvironmentSettings), Z_Construct_UScriptStruct_FAuracronRealmEnvironment, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentSettings_MetaData), NewProp_EnvironmentSettings_MetaData) }; // 2640511407
void Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bAutoGenerateContent_SetBit(void* Obj)
{
	((AAuracronRealmManager*)Obj)->bAutoGenerateContent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bAutoGenerateContent = { "bAutoGenerateContent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronRealmManager), &Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bAutoGenerateContent_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateContent_MetaData), NewProp_bAutoGenerateContent_MetaData) };
void Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bEnableEvolution_SetBit(void* Obj)
{
	((AAuracronRealmManager*)Obj)->bEnableEvolution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bEnableEvolution = { "bEnableEvolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronRealmManager), &Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bEnableEvolution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEvolution_MetaData), NewProp_bEnableEvolution_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_LayerComponent = { "LayerComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, LayerComponent), Z_Construct_UClass_UAuracronLayerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerComponent_MetaData), NewProp_LayerComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_PCGComponents_Inner = { "PCGComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_PCGComponents = { "PCGComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, PCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponents_MetaData), NewProp_PCGComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EffectComponents_Inner = { "EffectComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EffectComponents = { "EffectComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, EffectComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectComponents_MetaData), NewProp_EffectComponents_MetaData) };
void Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((AAuracronRealmManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronRealmManager), &Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
void Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((AAuracronRealmManager*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronRealmManager), &Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, CurrentLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_SpawnedContent_Inner = { "SpawnedContent", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_SpawnedContent = { "SpawnedContent", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, SpawnedContent), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedContent_MetaData), NewProp_SpawnedContent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentComponents_Inner = { "EnvironmentComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentComponents = { "EnvironmentComponents", nullptr, (EPropertyFlags)0x0124088000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, EnvironmentComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentComponents_MetaData), NewProp_EnvironmentComponents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_MemoryUsage = { "MemoryUsage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronRealmManager, MemoryUsage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsage_MetaData), NewProp_MemoryUsage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronRealmManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ManagedLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ManagedLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_ContentConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bAutoGenerateContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bEnableEvolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_LayerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_PCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_PCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EffectComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EffectComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_SpawnedContent_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_SpawnedContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_EnvironmentComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronRealmManager_Statics::NewProp_MemoryUsage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronRealmManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronRealmManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronRealmManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronRealmManager_Statics::ClassParams = {
	&AAuracronRealmManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronRealmManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronRealmManager_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronRealmManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronRealmManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronRealmManager()
{
	if (!Z_Registration_Info_UClass_AAuracronRealmManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronRealmManager.OuterSingleton, Z_Construct_UClass_AAuracronRealmManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronRealmManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronRealmManager);
AAuracronRealmManager::~AAuracronRealmManager() {}
// ********** End Class AAuracronRealmManager ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronRealmContentConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics::NewStructOps, TEXT("AuracronRealmContentConfig"), &Z_Registration_Info_UScriptStruct_FAuracronRealmContentConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmContentConfig), 3608437816U) },
		{ FAuracronRealmEnvironment::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics::NewStructOps, TEXT("AuracronRealmEnvironment"), &Z_Registration_Info_UScriptStruct_FAuracronRealmEnvironment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmEnvironment), 2640511407U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronRealmManager, AAuracronRealmManager::StaticClass, TEXT("AAuracronRealmManager"), &Z_Registration_Info_UClass_AAuracronRealmManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronRealmManager), 282321187U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_1929458229(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
