// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "HarmonyEnginePlayerComponent.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_HarmonyEnginePlayerComponent_generated_h
#error "HarmonyEnginePlayerComponent.generated.h already included, missing '#pragma once' in HarmonyEnginePlayerComponent.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_HarmonyEnginePlayerComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EEmotionalState : uint8;
enum class EHealingSessionType : uint8;
enum class EInterventionType : uint8;
struct FHarmonyInterventionData;
struct FHarmonyReward;
struct FKindnessReward;
struct FPlayerBehaviorSnapshot;

// ********** Begin Class UHarmonyEnginePlayerComponent ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void ClientShowRewardNotification_Implementation(FKindnessReward const& Reward); \
	virtual void ClientShowIntervention_Implementation(const FString& InterventionMessage, EInterventionType InterventionType); \
	virtual void ClientUpdateHarmonyData_Implementation(float ToxicityScore, float PositivityScore, EEmotionalState EmotionalState, int32 KindnessPoints); \
	virtual void ServerReportEmotionalState_Implementation(EEmotionalState NewEmotionalState); \
	virtual void ServerReportChatMessage_Implementation(const FString& Message); \
	virtual void ServerReportPlayerAction_Implementation(const FString& ActionType, bool bIsPositive, float Intensity); \
	DECLARE_FUNCTION(execClientShowRewardNotification); \
	DECLARE_FUNCTION(execClientShowIntervention); \
	DECLARE_FUNCTION(execClientUpdateHarmonyData); \
	DECLARE_FUNCTION(execServerReportEmotionalState); \
	DECLARE_FUNCTION(execServerReportChatMessage); \
	DECLARE_FUNCTION(execServerReportPlayerAction); \
	DECLARE_FUNCTION(execOnKindnessRewardInternal); \
	DECLARE_FUNCTION(execOnInterventionTriggeredInternal); \
	DECLARE_FUNCTION(execOnBehaviorDetectedInternal); \
	DECLARE_FUNCTION(execGetProgressToNextTier); \
	DECLARE_FUNCTION(execClaimReward); \
	DECLARE_FUNCTION(execGetAvailableRewards); \
	DECLARE_FUNCTION(execVolunteerAsHealer); \
	DECLARE_FUNCTION(execRequestCommunitySupport); \
	DECLARE_FUNCTION(execRespondToIntervention); \
	DECLARE_FUNCTION(execIsInHealingSession); \
	DECLARE_FUNCTION(execIsUnderIntervention); \
	DECLARE_FUNCTION(execGetKindnessPoints); \
	DECLARE_FUNCTION(execGetCurrentEmotionalState); \
	DECLARE_FUNCTION(execGetCurrentPositivityScore); \
	DECLARE_FUNCTION(execGetCurrentToxicityScore); \
	DECLARE_FUNCTION(execReportTeamworkAction); \
	DECLARE_FUNCTION(execReportEmotionalState); \
	DECLARE_FUNCTION(execReportChatMessage); \
	DECLARE_FUNCTION(execReportPlayerAction);


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_CALLBACK_WRAPPERS
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUHarmonyEnginePlayerComponent(); \
	friend struct Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UHarmonyEnginePlayerComponent, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UHarmonyEnginePlayerComponent_NoRegister) \
	DECLARE_SERIALIZER(UHarmonyEnginePlayerComponent) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentToxicityScore=NETFIELD_REP_START, \
		CurrentPositivityScore, \
		CurrentEmotionalState, \
		TotalKindnessPoints, \
		bIsUnderIntervention, \
		bIsInHealingSession, \
		NETFIELD_REP_END=bIsInHealingSession	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UHarmonyEnginePlayerComponent(UHarmonyEnginePlayerComponent&&) = delete; \
	UHarmonyEnginePlayerComponent(const UHarmonyEnginePlayerComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UHarmonyEnginePlayerComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UHarmonyEnginePlayerComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UHarmonyEnginePlayerComponent) \
	NO_API virtual ~UHarmonyEnginePlayerComponent();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_19_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h_22_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UHarmonyEnginePlayerComponent;

// ********** End Class UHarmonyEnginePlayerComponent **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
