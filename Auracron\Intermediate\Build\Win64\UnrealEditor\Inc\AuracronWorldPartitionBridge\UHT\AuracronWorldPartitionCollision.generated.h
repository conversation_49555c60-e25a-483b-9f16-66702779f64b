// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionCollision.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionCollision_generated_h
#error "AuracronWorldPartitionCollision.generated.h already included, missing '#pragma once' in AuracronWorldPartitionCollision.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionCollision_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionCollisionManager;
class UWorld;
enum class EAuracronCollisionLODLevel : uint8;
enum class EAuracronCollisionStreamingState : uint8;
enum class EAuracronCollisionType : uint8;
struct FAuracronCollisionConfiguration;
struct FAuracronCollisionDescriptor;
struct FAuracronCollisionQueryParameters;
struct FAuracronCollisionStatistics;

// ********** Begin ScriptStruct FAuracronCollisionConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_99_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionConfiguration;
// ********** End ScriptStruct FAuracronCollisionConfiguration *************************************

// ********** Begin ScriptStruct FAuracronCollisionDescriptor **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_185_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionDescriptor;
// ********** End ScriptStruct FAuracronCollisionDescriptor ****************************************

// ********** Begin ScriptStruct FAuracronCollisionQueryParameters *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_274_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionQueryParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionQueryParameters;
// ********** End ScriptStruct FAuracronCollisionQueryParameters ***********************************

// ********** Begin ScriptStruct FAuracronCollisionStatistics **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_319_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionStatistics;
// ********** End ScriptStruct FAuracronCollisionStatistics ****************************************

// ********** Begin Delegate FOnCollisionLoaded ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_525_DELEGATE \
static void FOnCollisionLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionLoaded, const FString& CollisionId, bool bSuccess);


// ********** End Delegate FOnCollisionLoaded ******************************************************

// ********** Begin Delegate FOnCollisionUnloaded **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_526_DELEGATE \
static void FOnCollisionUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionUnloaded, const FString& CollisionId);


// ********** End Delegate FOnCollisionUnloaded ****************************************************

// ********** Begin Delegate FOnCollisionLODChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_527_DELEGATE \
static void FOnCollisionLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionLODChanged, const FString& CollisionId, EAuracronCollisionLODLevel NewLOD);


// ********** End Delegate FOnCollisionLODChanged **************************************************

// ********** Begin Delegate FOnCollisionStateChanged **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_528_DELEGATE \
static void FOnCollisionStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCollisionStateChanged, const FString& CollisionId, bool bEnabled);


// ********** End Delegate FOnCollisionStateChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionCollisionManager **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugCollisionInfo); \
	DECLARE_FUNCTION(execLogCollisionState); \
	DECLARE_FUNCTION(execIsCollisionDebugEnabled); \
	DECLARE_FUNCTION(execEnableCollisionDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetLoadedCollisionObjectCount); \
	DECLARE_FUNCTION(execGetTotalCollisionObjectCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetCollisionStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execMoveCollisionObjectToCell); \
	DECLARE_FUNCTION(execGetCollisionObjectCell); \
	DECLARE_FUNCTION(execGetCollisionObjectsInCell); \
	DECLARE_FUNCTION(execIsCollisionObjectVisible); \
	DECLARE_FUNCTION(execSetCollisionObjectVisibility); \
	DECLARE_FUNCTION(execIsCollisionObjectEnabled); \
	DECLARE_FUNCTION(execEnableCollisionObject); \
	DECLARE_FUNCTION(execGetCollisionObjectsByType); \
	DECLARE_FUNCTION(execGetCollisionObjectsInBox); \
	DECLARE_FUNCTION(execGetCollisionObjectsInRadius); \
	DECLARE_FUNCTION(execExecuteCollisionQuery); \
	DECLARE_FUNCTION(execCalculateLODForDistance); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLODs); \
	DECLARE_FUNCTION(execGetCollisionLOD); \
	DECLARE_FUNCTION(execSetCollisionLOD); \
	DECLARE_FUNCTION(execGetStreamingCollisionObjects); \
	DECLARE_FUNCTION(execGetLoadedCollisionObjects); \
	DECLARE_FUNCTION(execGetCollisionStreamingState); \
	DECLARE_FUNCTION(execUnloadCollisionObject); \
	DECLARE_FUNCTION(execLoadCollisionObject); \
	DECLARE_FUNCTION(execDoesCollisionObjectExist); \
	DECLARE_FUNCTION(execGetCollisionIds); \
	DECLARE_FUNCTION(execGetAllCollisionObjects); \
	DECLARE_FUNCTION(execGetCollisionDescriptor); \
	DECLARE_FUNCTION(execRemoveCollisionObject); \
	DECLARE_FUNCTION(execCreateCollisionObject); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionCollisionManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionCollisionManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionCollisionManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionCollisionManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionCollisionManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionCollisionManager(UAuracronWorldPartitionCollisionManager&&) = delete; \
	UAuracronWorldPartitionCollisionManager(const UAuracronWorldPartitionCollisionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionCollisionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionCollisionManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionCollisionManager) \
	NO_API virtual ~UAuracronWorldPartitionCollisionManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_381_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h_384_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionCollisionManager;

// ********** End Class UAuracronWorldPartitionCollisionManager ************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionCollision_h

// ********** Begin Enum EAuracronCollisionStreamingState ******************************************
#define FOREACH_ENUM_EAURACRONCOLLISIONSTREAMINGSTATE(op) \
	op(EAuracronCollisionStreamingState::Unloaded) \
	op(EAuracronCollisionStreamingState::Loading) \
	op(EAuracronCollisionStreamingState::Loaded) \
	op(EAuracronCollisionStreamingState::Unloading) \
	op(EAuracronCollisionStreamingState::Failed) 

enum class EAuracronCollisionStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronCollisionStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionStreamingState>();
// ********** End Enum EAuracronCollisionStreamingState ********************************************

// ********** Begin Enum EAuracronCollisionLODLevel ************************************************
#define FOREACH_ENUM_EAURACRONCOLLISIONLODLEVEL(op) \
	op(EAuracronCollisionLODLevel::LOD0) \
	op(EAuracronCollisionLODLevel::LOD1) \
	op(EAuracronCollisionLODLevel::LOD2) \
	op(EAuracronCollisionLODLevel::LOD3) \
	op(EAuracronCollisionLODLevel::LOD4) 

enum class EAuracronCollisionLODLevel : uint8;
template<> struct TIsUEnumClass<EAuracronCollisionLODLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionLODLevel>();
// ********** End Enum EAuracronCollisionLODLevel **************************************************

// ********** Begin Enum EAuracronCollisionType ****************************************************
#define FOREACH_ENUM_EAURACRONCOLLISIONTYPE(op) \
	op(EAuracronCollisionType::Static) \
	op(EAuracronCollisionType::Dynamic) \
	op(EAuracronCollisionType::Kinematic) \
	op(EAuracronCollisionType::Trigger) \
	op(EAuracronCollisionType::Query) 

enum class EAuracronCollisionType : uint8;
template<> struct TIsUEnumClass<EAuracronCollisionType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionType>();
// ********** End Enum EAuracronCollisionType ******************************************************

// ********** Begin Enum EAuracronCollisionComplexity **********************************************
#define FOREACH_ENUM_EAURACRONCOLLISIONCOMPLEXITY(op) \
	op(EAuracronCollisionComplexity::Simple) \
	op(EAuracronCollisionComplexity::Complex) \
	op(EAuracronCollisionComplexity::UseDefault) 

enum class EAuracronCollisionComplexity : uint8;
template<> struct TIsUEnumClass<EAuracronCollisionComplexity> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCollisionComplexity>();
// ********** End Enum EAuracronCollisionComplexity ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
