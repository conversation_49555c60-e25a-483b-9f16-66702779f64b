// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionAudio.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionAudio_generated_h
#error "AuracronWorldPartitionAudio.generated.h already included, missing '#pragma once' in AuracronWorldPartitionAudio.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionAudio_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionAudioManager;
class UWorld;
enum class EAuracronAudioLODLevel : uint8;
enum class EAuracronAudioStreamingState : uint8;
enum class EAuracronWorldPartitionAudioType : uint8;
struct FAuracronAudioDescriptor;
struct FAuracronAudioStatistics;
struct FAuracronWorldPartitionAudioConfiguration;

// ********** Begin ScriptStruct FAuracronWorldPartitionAudioConfiguration *************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_105_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWorldPartitionAudioConfiguration;
// ********** End ScriptStruct FAuracronWorldPartitionAudioConfiguration ***************************

// ********** Begin ScriptStruct FAuracronAudioDescriptor ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_211_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAudioDescriptor;
// ********** End ScriptStruct FAuracronAudioDescriptor ********************************************

// ********** Begin ScriptStruct FAuracronAudioStatistics ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_309_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAudioStatistics;
// ********** End ScriptStruct FAuracronAudioStatistics ********************************************

// ********** Begin Delegate FOnAudioLoaded ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_527_DELEGATE \
static void FOnAudioLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLoaded, const FString& AudioId, bool bSuccess);


// ********** End Delegate FOnAudioLoaded **********************************************************

// ********** Begin Delegate FOnAudioUnloaded ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_528_DELEGATE \
static void FOnAudioUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnAudioUnloaded, const FString& AudioId);


// ********** End Delegate FOnAudioUnloaded ********************************************************

// ********** Begin Delegate FOnAudioStarted *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_529_DELEGATE \
static void FOnAudioStarted_DelegateWrapper(const FMulticastScriptDelegate& OnAudioStarted, const FString& AudioId);


// ********** End Delegate FOnAudioStarted *********************************************************

// ********** Begin Delegate FOnAudioStopped *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_530_DELEGATE \
static void FOnAudioStopped_DelegateWrapper(const FMulticastScriptDelegate& OnAudioStopped, const FString& AudioId);


// ********** End Delegate FOnAudioStopped *********************************************************

// ********** Begin Delegate FOnAudioLODChanged ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_531_DELEGATE \
static void FOnAudioLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLODChanged, const FString& AudioId, EAuracronAudioLODLevel NewLOD);


// ********** End Delegate FOnAudioLODChanged ******************************************************

// ********** Begin Class UAuracronWorldPartitionAudioManager **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugAudioInfo); \
	DECLARE_FUNCTION(execLogAudioState); \
	DECLARE_FUNCTION(execIsAudioDebugEnabled); \
	DECLARE_FUNCTION(execEnableAudioDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetPlayingAudioSourceCount); \
	DECLARE_FUNCTION(execGetLoadedAudioSourceCount); \
	DECLARE_FUNCTION(execGetTotalAudioSourceCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetAudioStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execMoveAudioSourceToCell); \
	DECLARE_FUNCTION(execGetAudioSourceCell); \
	DECLARE_FUNCTION(execGetAudioSourcesInCell); \
	DECLARE_FUNCTION(execGetAudibleAudioSources); \
	DECLARE_FUNCTION(execGetAudioSourcesByType); \
	DECLARE_FUNCTION(execGetAudioSourcesInRadius); \
	DECLARE_FUNCTION(execCalculateLODForDistance); \
	DECLARE_FUNCTION(execUpdateDistanceBasedLODs); \
	DECLARE_FUNCTION(execGetAudioLOD); \
	DECLARE_FUNCTION(execSetAudioLOD); \
	DECLARE_FUNCTION(execSetAudioRotation); \
	DECLARE_FUNCTION(execSetAudioLocation); \
	DECLARE_FUNCTION(execSetAudioPitch); \
	DECLARE_FUNCTION(execSetAudioVolume); \
	DECLARE_FUNCTION(execGetPlayingAudioSources); \
	DECLARE_FUNCTION(execResumeAudioSource); \
	DECLARE_FUNCTION(execPauseAudioSource); \
	DECLARE_FUNCTION(execStopAudioSource); \
	DECLARE_FUNCTION(execPlayAudioSource); \
	DECLARE_FUNCTION(execGetStreamingAudioSources); \
	DECLARE_FUNCTION(execGetLoadedAudioSources); \
	DECLARE_FUNCTION(execGetAudioStreamingState); \
	DECLARE_FUNCTION(execUnloadAudioSource); \
	DECLARE_FUNCTION(execLoadAudioSource); \
	DECLARE_FUNCTION(execDoesAudioSourceExist); \
	DECLARE_FUNCTION(execGetAudioIds); \
	DECLARE_FUNCTION(execGetAllAudioSources); \
	DECLARE_FUNCTION(execGetAudioDescriptor); \
	DECLARE_FUNCTION(execRemoveAudioSource); \
	DECLARE_FUNCTION(execCreateAudioSource); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionAudioManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionAudioManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionAudioManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionAudioManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionAudioManager(UAuracronWorldPartitionAudioManager&&) = delete; \
	UAuracronWorldPartitionAudioManager(const UAuracronWorldPartitionAudioManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionAudioManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionAudioManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionAudioManager) \
	NO_API virtual ~UAuracronWorldPartitionAudioManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_367_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h_370_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionAudioManager;

// ********** End Class UAuracronWorldPartitionAudioManager ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h

// ********** Begin Enum EAuracronAudioStreamingState **********************************************
#define FOREACH_ENUM_EAURACRONAUDIOSTREAMINGSTATE(op) \
	op(EAuracronAudioStreamingState::Unloaded) \
	op(EAuracronAudioStreamingState::Loading) \
	op(EAuracronAudioStreamingState::Loaded) \
	op(EAuracronAudioStreamingState::Playing) \
	op(EAuracronAudioStreamingState::Paused) \
	op(EAuracronAudioStreamingState::Unloading) \
	op(EAuracronAudioStreamingState::Failed) 

enum class EAuracronAudioStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronAudioStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioStreamingState>();
// ********** End Enum EAuracronAudioStreamingState ************************************************

// ********** Begin Enum EAuracronAudioLODLevel ****************************************************
#define FOREACH_ENUM_EAURACRONAUDIOLODLEVEL(op) \
	op(EAuracronAudioLODLevel::LOD0) \
	op(EAuracronAudioLODLevel::LOD1) \
	op(EAuracronAudioLODLevel::LOD2) \
	op(EAuracronAudioLODLevel::LOD3) \
	op(EAuracronAudioLODLevel::LOD4) 

enum class EAuracronAudioLODLevel : uint8;
template<> struct TIsUEnumClass<EAuracronAudioLODLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioLODLevel>();
// ********** End Enum EAuracronAudioLODLevel ******************************************************

// ********** Begin Enum EAuracronWorldPartitionAudioType ******************************************
#define FOREACH_ENUM_EAURACRONWORLDPARTITIONAUDIOTYPE(op) \
	op(EAuracronWorldPartitionAudioType::Ambient) \
	op(EAuracronWorldPartitionAudioType::Music) \
	op(EAuracronWorldPartitionAudioType::SFX) \
	op(EAuracronWorldPartitionAudioType::Voice) \
	op(EAuracronWorldPartitionAudioType::UI) \
	op(EAuracronWorldPartitionAudioType::Foley) 

enum class EAuracronWorldPartitionAudioType : uint8;
template<> struct TIsUEnumClass<EAuracronWorldPartitionAudioType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionAudioType>();
// ********** End Enum EAuracronWorldPartitionAudioType ********************************************

// ********** Begin Enum EAuracronAudioSpatialization **********************************************
#define FOREACH_ENUM_EAURACRONAUDIOSPATIALIZATION(op) \
	op(EAuracronAudioSpatialization::None) \
	op(EAuracronAudioSpatialization::Simple) \
	op(EAuracronAudioSpatialization::Binaural) \
	op(EAuracronAudioSpatialization::Surround) \
	op(EAuracronAudioSpatialization::Atmos) 

enum class EAuracronAudioSpatialization : uint8;
template<> struct TIsUEnumClass<EAuracronAudioSpatialization> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioSpatialization>();
// ********** End Enum EAuracronAudioSpatialization ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
