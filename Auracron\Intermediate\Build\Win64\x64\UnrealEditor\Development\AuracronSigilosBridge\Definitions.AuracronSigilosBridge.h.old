// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for AuracronSigilosBridge
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef AURACRONSIGILOSBRIDGE_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Auracron
#define UE_TARGET_NAME AuracronEditor
#define AURACRON_SIGILOS_DEBUG 1
#define AURACRON_SIGILOS_PROFILING 1
#define AURACRON_MOBILE_PLATFORM 0
#define AURACRON_UE56_FEATURES 1
#define WITH_GAMEPLAY_ABILITY_SYSTEM 1
#define WITH_ENHANCED_INPUT 1
#define WITH_COMMON_UI 1
#define UE_MODULE_NAME "AuracronSigilosBridge"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
#define WITH_METASOUND_FRONTEND 1
#define UE_METASOUND_DISABLE_5_6_NODE_REGISTRATION_DEPRECATION_WARNINGS 0
#define METASOUNDFRONTEND_API DLLIMPORT
#define SERIALIZATION_API DLLIMPORT
#define CBOR_API DLLIMPORT
#define METASOUNDGRAPHCORE_API DLLIMPORT
#define METASOUNDENGINE_API DLLIMPORT
#define METASOUNDSTANDARDNODES_API DLLIMPORT
#define WAVETABLE_API DLLIMPORT
#define ONLINESUBSYSTEMUTILS_PACKAGE 1
#define ONLINESUBSYSTEMUTILS_API DLLIMPORT
#define ONLINESUBSYSTEM_PACKAGE 1
#define DEBUG_LAN_BEACON 0
#define ONLINESUBSYSTEM_API DLLIMPORT
#define ONLINEBASE_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define AURACRONSIGILOSBRIDGE_API DLLEXPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define MODULARGAMEPLAY_API DLLIMPORT
#define REPLICATIONGRAPH_API DLLIMPORT
#define ENHANCEDINPUT_API DLLIMPORT
