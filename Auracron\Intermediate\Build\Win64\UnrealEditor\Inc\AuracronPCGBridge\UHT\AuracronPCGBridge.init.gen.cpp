// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronPCGBridge_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronPCGBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronPCGBridge",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x2582F8CE,
				0x999C8033,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronPCGBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronPCGBridge(Z_Construct_UPackage__Script_AuracronPCGBridge, TEXT("/Script/AuracronPCGBridge"), Z_Registration_Info_UPackage__Script_AuracronPCGBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x2582F8CE, 0x999C8033));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
