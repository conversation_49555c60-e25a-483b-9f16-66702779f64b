﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Library Header
// Bridge 2.3: PCG Framework - Element Library

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGBasePointData.h"
#include "Data/PCGSpatialData.h"
#include "Data/PCGVolumeData.h"
#include "Data/PCGSurfaceData.h"
#include "Data/PCGLandscapeData.h"
#include "Data/PCGSplineData.h"
#include "Metadata/PCGMetadata.h"
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGVolumeSampler.h"
#include "Elements/PCGStaticMeshSpawner.h"
#include "Elements/PCGFilterByAttribute.h"
#include "Elements/PCGTransformPoints.h"

// Engine includes
#include "Engine/StaticMesh.h"
// #include "Components/StaticMeshComponent.h" // Removido - nÃ£o necessÃ¡rio
#include "Materials/MaterialInterface.h"
#include "Landscape.h"
#include "LandscapeProxy.h"

#include "AuracronPCGElementLibrary.generated.h"

// Forward declarations
class UStaticMesh;
class UMaterialInterface;
class ALandscape;
class USplineComponent;

// Element library categories
UENUM(BlueprintType)
enum class EAuracronPCGElementCategory : uint8
{
    Generators      UMETA(DisplayName = "Generators"),
    Samplers        UMETA(DisplayName = "Samplers"),
    Filters         UMETA(DisplayName = "Filters"),
    Transformers    UMETA(DisplayName = "Transformers"),
    Spawners        UMETA(DisplayName = "Spawners"),
    Utilities       UMETA(DisplayName = "Utilities"),
    Noise           UMETA(DisplayName = "Noise"),
    Spatial         UMETA(DisplayName = "Spatial"),
    Landscape       UMETA(DisplayName = "Landscape"),
    Spline          UMETA(DisplayName = "Spline"),
    Custom          UMETA(DisplayName = "Custom")
};

// Noise types for procedural generation
UENUM(BlueprintType)
enum class EAuracronPCGNoiseType : uint8
{
    Perlin          UMETA(DisplayName = "Perlin"),
    Simplex         UMETA(DisplayName = "Simplex"),
    Worley          UMETA(DisplayName = "Worley"),
    Ridge           UMETA(DisplayName = "Ridge"),
    Fractal         UMETA(DisplayName = "Fractal"),
    Turbulence      UMETA(DisplayName = "Turbulence")
};

// Distribution patterns for point generation
UENUM(BlueprintType)
enum class EAuracronPCGDistributionPattern : uint8
{
    Random          UMETA(DisplayName = "Random"),
    Grid            UMETA(DisplayName = "Grid"),
    Hexagonal       UMETA(DisplayName = "Hexagonal"),
    Poisson         UMETA(DisplayName = "Poisson Disk"),
    Spiral          UMETA(DisplayName = "Spiral"),
    Radial          UMETA(DisplayName = "Radial"),
    Custom          UMETA(DisplayName = "Custom")
};

// Biome types for ecosystem generation
UENUM(BlueprintType)
enum class EAuracronPCGBiomeType : uint8
{
    Forest          UMETA(DisplayName = "Forest"),
    Desert          UMETA(DisplayName = "Desert"),
    Grassland       UMETA(DisplayName = "Grassland"),
    Mountain        UMETA(DisplayName = "Mountain"),
    Swamp           UMETA(DisplayName = "Swamp"),
    Tundra          UMETA(DisplayName = "Tundra"),
    Ocean           UMETA(DisplayName = "Ocean"),
    Urban           UMETA(DisplayName = "Urban"),
    Volcanic        UMETA(DisplayName = "Volcanic"),
    Crystal         UMETA(DisplayName = "Crystal")
};

// =============================================================================
// GENERATOR ELEMENTS
// =============================================================================

/**
 * Advanced Point Grid Generator
 * Generates points in various grid patterns with noise and variation
 */

public:
    UAuracronPCGPointGridSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGPointGridSettings();

    // Grid configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings")
    EAuracronPCGDistributionPattern DistributionPattern = EAuracronPCGDistributionPattern::Grid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings", meta = (ClampMin = "0.1", ClampMax = "1000.0"))
    float GridSpacing = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Jitter = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings")
    FVector2D GridSize = FVector2D(1000.0f, 1000.0f);

    // Noise configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Settings")
    bool bUseNoise = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Settings")
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Settings", meta = (ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Settings", meta = (ClampMin = "1", ClampMax = "8"))
    int32 NoiseOctaves = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseThreshold = 0.5f;

    // Density configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float BaseDensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DensityVariation = 0.0f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGPointGridElement, UAuracronPCGPointGridSettings)

/**
 * Biome Generator
 * Generates biome-specific content with ecosystem rules
 */

public:
    UAuracronPCGBiomeGeneratorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGBiomeGeneratorSettings();

    // Biome configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome Settings")
    EAuracronPCGBiomeType BiomeType = EAuracronPCGBiomeType::Forest;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float BiomeIntensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome Settings", meta = (ClampMin = "0.0", ClampMax = "1000.0"))
    float TransitionDistance = 100.0f;

    // Vegetation settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation Settings")
    TArray<TSoftObjectPtr<UStaticMesh>> VegetationMeshes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation Settings")
    TArray<float> VegetationWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation Settings", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float VegetationDensity = 1.0f;

    // Environmental settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment Settings", meta = (ClampMin = "0.0", ClampMax = "90.0"))
    float MaxSlope = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment Settings")
    FVector2D HeightRange = FVector2D(-1000.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MoistureLevel = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TemperatureLevel = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGBiomeGeneratorElement, UAuracronPCGBiomeGeneratorSettings)

// =============================================================================
// SAMPLER ELEMENTS
// =============================================================================

/**
 * Advanced Surface Sampler
 * Enhanced surface sampling with multiple sampling strategies
 */

public:
    UAuracronPCGAdvancedSurfaceSamplerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGAdvancedSurfaceSamplerSettings();

    // Sampling configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling Settings", meta = (ClampMin = "0.001", ClampMax = "100.0"))
    float PointsPerSquareMeter = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling Settings")
    EAuracronPCGDistributionPattern SamplingPattern = EAuracronPCGDistributionPattern::Random;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sampling Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SamplingJitter = 0.5f;

    // Surface constraints
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface Constraints", meta = (ClampMin = "0.0", ClampMax = "90.0"))
    float MaxSurfaceAngle = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface Constraints")
    bool bProjectToSurface = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Surface Constraints")
    float ProjectionDistance = 1000.0f;

    // Quality settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bUseHighQualitySampling = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings", meta = (ClampMin = "1", ClampMax = "16"))
    int32 SamplingIterations = 1;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedSurfaceSamplerElement, UAuracronPCGAdvancedSurfaceSamplerSettings)

/**
 * Landscape Sampler
 * Specialized sampler for landscape data with height and material sampling
 */

public:
    UAuracronPCGLandscapeSamplerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGLandscapeSamplerSettings();

    // Landscape sampling
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Settings")
    bool bSampleHeight = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Settings")
    bool bSampleSlope = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Settings")
    bool bSampleMaterials = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Settings")
    TArray<FName> MaterialLayerNames;

    // Height constraints
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Constraints")
    FVector2D HeightRange = FVector2D(-10000.0f, 10000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Constraints", meta = (ClampMin = "0.0", ClampMax = "90.0"))
    float MaxSlope = 45.0f;

    // Sampling quality
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float SamplingResolution = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Settings")
    bool bInterpolateSamples = true;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGLandscapeSamplerElement, UAuracronPCGLandscapeSamplerSettings)

// =============================================================================
// FILTER ELEMENTS
// =============================================================================

/**
 * Advanced Point Filter
 * Multi-criteria point filtering with complex conditions
 */

public:
    UAuracronPCGAdvancedFilterSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGAdvancedFilterSettings();

    // Filter criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterByDensity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    FVector2D DensityRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterByHeight = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    FVector2D HeightRange = FVector2D(-1000.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings")
    bool bFilterBySlope = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter Settings", meta = (ClampMin = "0.0", ClampMax = "90.0"))
    float MaxSlope = 45.0f;

    // Advanced filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter")
    bool bUseCustomAttribute = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter")
    FString CustomAttributeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Filter")
    FVector2D AttributeRange = FVector2D(0.0f, 1.0f);

    // Noise-based filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Filter")
    bool bUseNoiseFilter = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Filter")
    EAuracronPCGNoiseType NoiseType = EAuracronPCGNoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Filter", meta = (ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise Filter", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float NoiseThreshold = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedFilterElement, UAuracronPCGAdvancedFilterSettings)

// Element library manager

public:
    UAuracronPCGElementLibrary();

    // Library management
    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static void RegisterAllElements();

    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static TArray<TSubclassOf<UAuracronPCGNodeSettings>> GetElementsByCategory(EAuracronPCGElementCategory Category);

    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static TArray<FString> GetAvailableCategories();

    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static int32 GetElementCount();

    // Element creation helpers
    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static UAuracronPCGNodeSettings* CreateElementInstance(TSubclassOf<UAuracronPCGNodeSettings> ElementClass);

    UFUNCTION(BlueprintCallable, Category = "Element Library")
    static bool ValidateElementConfiguration(UAuracronPCGNodeSettings* ElementSettings, TArray<FString>& ValidationErrors);

private:
    static bool bElementsRegistered;
    static void RegisterGeneratorElements();
    static void RegisterSamplerElements();
    static void RegisterFilterElements();
    static void RegisterTransformerElements();
    static void RegisterSpawnerElements();
    static void RegisterUtilityElements();
};

// Utility functions for element library
namespace AuracronPCGElementUtils
{
    AURACRONPCGBRIDGE_API float GenerateNoise(EAuracronPCGNoiseType NoiseType, const FVector& Position, float Scale, int32 Octaves = 1);
    AURACRONPCGBRIDGE_API TArray<FVector> GenerateDistributionPattern(EAuracronPCGDistributionPattern Pattern, const FBox& Bounds, float Spacing, float Jitter = 0.0f);
    AURACRONPCGBRIDGE_API bool IsPointValidForBiome(const FVector& Position, EAuracronPCGBiomeType BiomeType, float Intensity);
    AURACRONPCGBRIDGE_API FVector CalculateSurfaceNormal(const FVector& Position, float SampleDistance = 10.0f);
    AURACRONPCGBRIDGE_API float CalculateSlope(const FVector& Normal);
    AURACRONPCGBRIDGE_API bool SampleLandscapeHeight(const FVector& Position, float& OutHeight);
    AURACRONPCGBRIDGE_API bool SampleLandscapeMaterial(const FVector& Position, const FName& LayerName, float& OutWeight);
}
