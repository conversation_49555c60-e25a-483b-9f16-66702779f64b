// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "FoliageEditModule.h"

#ifdef AURACRONFOLIAGEBRIDGE_FoliageEditModule_generated_h
#error "FoliageEditModule.generated.h already included, missing '#pragma once' in FoliageEditModule.h"
#endif
#define AURACRONFOLIAGEBRIDGE_FoliageEditModule_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UFoliageType;
class UStaticMesh;
class UWorld;
struct FAuracronFoliageInstanceData;
struct FFoliageEditSettings;

// ********** Begin ScriptStruct FFoliageEditSettings **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_45_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FFoliageEditSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FFoliageEditSettings;
// ********** End ScriptStruct FFoliageEditSettings ************************************************

// ********** Begin Class UFoliageEditUtility ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSurfaceHeight); \
	DECLARE_FUNCTION(execGetSurfaceNormal); \
	DECLARE_FUNCTION(execIsValidFoliageLocation); \
	DECLARE_FUNCTION(execUpdateFoliageInstances); \
	DECLARE_FUNCTION(execGetFoliageInstances); \
	DECLARE_FUNCTION(execGetFoliageInstanceCount); \
	DECLARE_FUNCTION(execGetFoliageTypes); \
	DECLARE_FUNCTION(execRemoveFoliageType); \
	DECLARE_FUNCTION(execAddFoliageType); \
	DECLARE_FUNCTION(execCreateFoliageType); \
	DECLARE_FUNCTION(execReapplyFoliage); \
	DECLARE_FUNCTION(execSelectFoliage); \
	DECLARE_FUNCTION(execEraseFoliage); \
	DECLARE_FUNCTION(execPaintFoliage);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UFoliageEditUtility_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUFoliageEditUtility(); \
	friend struct Z_Construct_UClass_UFoliageEditUtility_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UFoliageEditUtility_NoRegister(); \
public: \
	DECLARE_CLASS2(UFoliageEditUtility, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UFoliageEditUtility_NoRegister) \
	DECLARE_SERIALIZER(UFoliageEditUtility)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UFoliageEditUtility(UFoliageEditUtility&&) = delete; \
	UFoliageEditUtility(const UFoliageEditUtility&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UFoliageEditUtility); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UFoliageEditUtility); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UFoliageEditUtility) \
	NO_API virtual ~UFoliageEditUtility();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_79_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h_82_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UFoliageEditUtility;

// ********** End Class UFoliageEditUtility ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_FoliageEditModule_h

// ********** Begin Enum EFoliageEditMode **********************************************************
#define FOREACH_ENUM_EFOLIAGEEDITMODE(op) \
	op(EFoliageEditMode::Paint) \
	op(EFoliageEditMode::Erase) \
	op(EFoliageEditMode::Select) \
	op(EFoliageEditMode::Reapply) 

enum class EFoliageEditMode : uint8;
template<> struct TIsUEnumClass<EFoliageEditMode> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EFoliageEditMode>();
// ********** End Enum EFoliageEditMode ************************************************************

// ********** Begin Enum EFoliageEditToolType ******************************************************
#define FOREACH_ENUM_EFOLIAGEEDITTOOLTYPE(op) \
	op(EFoliageEditToolType::Brush) \
	op(EFoliageEditToolType::Lasso) \
	op(EFoliageEditToolType::Fill) 

enum class EFoliageEditToolType : uint8;
template<> struct TIsUEnumClass<EFoliageEditToolType> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EFoliageEditToolType>();
// ********** End Enum EFoliageEditToolType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
