// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronVFXBridge.h"

#ifdef AURACRONVFXBRIDGE_AuracronVFXBridge_generated_h
#error "AuracronVFXBridge.generated.h already included, missing '#pragma once' in AuracronVFXBridge.h"
#endif
#define AURACRONVFXBRIDGE_AuracronVFXBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class AActor;
class UMaterialInstanceDynamic;
class UMaterialInterface;
class UNiagaraComponent;
enum class EAuracronVFXQuality : uint8;
struct FAuracronVFXConfiguration;

// ********** Begin ScriptStruct FAuracronVFXConfiguration *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_77_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVFXConfiguration;
// ********** End ScriptStruct FAuracronVFXConfiguration *******************************************

// ********** Begin ScriptStruct FAuracronRealmVFXConfiguration ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_174_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmVFXConfiguration;
// ********** End ScriptStruct FAuracronRealmVFXConfiguration **************************************

// ********** Begin Delegate FOnVFXSpawned *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_453_DELEGATE \
static void FOnVFXSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnVFXSpawned, UNiagaraComponent* EffectComponent, FAuracronVFXConfiguration VFXConfig);


// ********** End Delegate FOnVFXSpawned ***********************************************************

// ********** Begin Delegate FOnVFXQualityChanged **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_458_DELEGATE \
static void FOnVFXQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVFXQualityChanged, EAuracronVFXQuality OldQuality, EAuracronVFXQuality NewQuality);


// ********** End Delegate FOnVFXQualityChanged ****************************************************

// ********** Begin Class UAuracronVFXBridge *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_VFXQuality); \
	DECLARE_FUNCTION(execCleanupInactiveVFX); \
	DECLARE_FUNCTION(execOptimizeVFXByDistance); \
	DECLARE_FUNCTION(execSetVFXQuality); \
	DECLARE_FUNCTION(execApplyDissolveEffect); \
	DECLARE_FUNCTION(execUpdateMaterialParameters); \
	DECLARE_FUNCTION(execCreateDynamicMaterial); \
	DECLARE_FUNCTION(execSpawnPortalVFX); \
	DECLARE_FUNCTION(execUpdateAmbientVFX); \
	DECLARE_FUNCTION(execPlayRealmTransitionVFX); \
	DECLARE_FUNCTION(execActivateRealmVFX); \
	DECLARE_FUNCTION(execPlayStatusEffectVFX); \
	DECLARE_FUNCTION(execPlayHealingVFX); \
	DECLARE_FUNCTION(execPlayImpactVFX); \
	DECLARE_FUNCTION(execPlayAbilityVFX); \
	DECLARE_FUNCTION(execResumeAllVFXEffects); \
	DECLARE_FUNCTION(execPauseAllVFXEffects); \
	DECLARE_FUNCTION(execStopVFXEffect); \
	DECLARE_FUNCTION(execSpawnAttachedNiagaraEffect); \
	DECLARE_FUNCTION(execSpawnNiagaraEffect);


AURACRONVFXBRIDGE_API UClass* Z_Construct_UClass_UAuracronVFXBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronVFXBridge(); \
	friend struct Z_Construct_UClass_UAuracronVFXBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONVFXBRIDGE_API UClass* Z_Construct_UClass_UAuracronVFXBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronVFXBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronVFXBridge"), Z_Construct_UClass_UAuracronVFXBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronVFXBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentVFXQuality=NETFIELD_REP_START, \
		NETFIELD_REP_END=CurrentVFXQuality	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronVFXBridge(UAuracronVFXBridge&&) = delete; \
	UAuracronVFXBridge(const UAuracronVFXBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronVFXBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronVFXBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronVFXBridge) \
	NO_API virtual ~UAuracronVFXBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_229_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h_232_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronVFXBridge;

// ********** End Class UAuracronVFXBridge *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h

// ********** Begin Enum EAuracronVFXType **********************************************************
#define FOREACH_ENUM_EAURACRONVFXTYPE(op) \
	op(EAuracronVFXType::None) \
	op(EAuracronVFXType::Ability) \
	op(EAuracronVFXType::Impact) \
	op(EAuracronVFXType::Projectile) \
	op(EAuracronVFXType::Aura) \
	op(EAuracronVFXType::Buff) \
	op(EAuracronVFXType::Debuff) \
	op(EAuracronVFXType::Healing) \
	op(EAuracronVFXType::Death) \
	op(EAuracronVFXType::Respawn) \
	op(EAuracronVFXType::Teleport) \
	op(EAuracronVFXType::Environmental) \
	op(EAuracronVFXType::Weather) \
	op(EAuracronVFXType::Destruction) \
	op(EAuracronVFXType::UI) \
	op(EAuracronVFXType::Transition) 

enum class EAuracronVFXType : uint8;
template<> struct TIsUEnumClass<EAuracronVFXType> { enum { Value = true }; };
template<> AURACRONVFXBRIDGE_API UEnum* StaticEnum<EAuracronVFXType>();
// ********** End Enum EAuracronVFXType ************************************************************

// ********** Begin Enum EAuracronVFXQuality *******************************************************
#define FOREACH_ENUM_EAURACRONVFXQUALITY(op) \
	op(EAuracronVFXQuality::Low) \
	op(EAuracronVFXQuality::Medium) \
	op(EAuracronVFXQuality::High) \
	op(EAuracronVFXQuality::Ultra) \
	op(EAuracronVFXQuality::Cinematic) 

enum class EAuracronVFXQuality : uint8;
template<> struct TIsUEnumClass<EAuracronVFXQuality> { enum { Value = true }; };
template<> AURACRONVFXBRIDGE_API UEnum* StaticEnum<EAuracronVFXQuality>();
// ********** End Enum EAuracronVFXQuality *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
