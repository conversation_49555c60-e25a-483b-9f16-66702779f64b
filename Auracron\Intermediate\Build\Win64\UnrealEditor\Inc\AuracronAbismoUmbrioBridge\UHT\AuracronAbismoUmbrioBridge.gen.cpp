// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAbismoUmbrioBridge/Public/AuracronAbismoUmbrioBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAbismoUmbrioBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONABISMOUMBRIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge();
AURACRONABISMOUMBRIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge_NoRegister();
AURACRONABISMOUMBRIOBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation();
AURACRONABISMOUMBRIOBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveData();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveProperties();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig();
AURACRONABISMOUMBRIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronUndergroundLighting();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UParticleSystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundCue_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronUndergroundBiome *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronUndergroundBiome;
static UEnum* EAuracronUndergroundBiome_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronUndergroundBiome.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronUndergroundBiome.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("EAuracronUndergroundBiome"));
	}
	return Z_Registration_Info_UEnum_EAuracronUndergroundBiome.OuterSingleton;
}
template<> AURACRONABISMOUMBRIOBRIDGE_API UEnum* StaticEnum<EAuracronUndergroundBiome>()
{
	return EAuracronUndergroundBiome_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum para tipos de biomas subterr\xc3\xa2neos\n */" },
#endif
		{ "CrystalCaverns.DisplayName", "Crystal Caverns" },
		{ "CrystalCaverns.Name", "EAuracronUndergroundBiome::CrystalCaverns" },
		{ "DeepAbyss.DisplayName", "Deep Abyss" },
		{ "DeepAbyss.Name", "EAuracronUndergroundBiome::DeepAbyss" },
		{ "IceCaves.DisplayName", "Ice Caves" },
		{ "IceCaves.Name", "EAuracronUndergroundBiome::IceCaves" },
		{ "LavaTubes.DisplayName", "Lava Tubes" },
		{ "LavaTubes.Name", "EAuracronUndergroundBiome::LavaTubes" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
		{ "MushroomForests.DisplayName", "Mushroom Forests" },
		{ "MushroomForests.Name", "EAuracronUndergroundBiome::MushroomForests" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum para tipos de biomas subterr\xc3\xa2neos" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronUndergroundBiome::CrystalCaverns", (int64)EAuracronUndergroundBiome::CrystalCaverns },
		{ "EAuracronUndergroundBiome::LavaTubes", (int64)EAuracronUndergroundBiome::LavaTubes },
		{ "EAuracronUndergroundBiome::MushroomForests", (int64)EAuracronUndergroundBiome::MushroomForests },
		{ "EAuracronUndergroundBiome::IceCaves", (int64)EAuracronUndergroundBiome::IceCaves },
		{ "EAuracronUndergroundBiome::DeepAbyss", (int64)EAuracronUndergroundBiome::DeepAbyss },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	"EAuracronUndergroundBiome",
	"EAuracronUndergroundBiome",
	Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome()
{
	if (!Z_Registration_Info_UEnum_EAuracronUndergroundBiome.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronUndergroundBiome.InnerSingleton, Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronUndergroundBiome.InnerSingleton;
}
// ********** End Enum EAuracronUndergroundBiome ***************************************************

// ********** Begin ScriptStruct FAuracronCaveGenerationProperties *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties;
class UScriptStruct* FAuracronCaveGenerationProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronCaveGenerationProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para propriedades de gera\xc3\xa7\xc3\xa3o de cavernas\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para propriedades de gera\xc3\xa7\xc3\xa3o de cavernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveSize_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Complexity_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Complexidade da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Complexidade da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChamberCount_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de c\xc3\xa2maras */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de c\xc3\xa2maras" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCaves_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de cavernas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de cavernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTunnels_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de t\xc3\xbaneis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de t\xc3\xbaneis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTunnelLength_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Comprimento m\xc3\xa1ximo de t\xc3\xbanel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Comprimento m\xc3\xa1ximo de t\xc3\xbanel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CaveSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Complexity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ChamberCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCaves;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTunnels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTunnelLength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCaveGenerationProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_CaveSize = { "CaveSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, CaveSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveSize_MetaData), NewProp_CaveSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_Complexity = { "Complexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, Complexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Complexity_MetaData), NewProp_Complexity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_ChamberCount = { "ChamberCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, ChamberCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChamberCount_MetaData), NewProp_ChamberCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxCaves = { "MaxCaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, MaxCaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCaves_MetaData), NewProp_MaxCaves_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxTunnels = { "MaxTunnels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, MaxTunnels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTunnels_MetaData), NewProp_MaxTunnels_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxTunnelLength = { "MaxTunnelLength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveGenerationProperties, MaxTunnelLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTunnelLength_MetaData), NewProp_MaxTunnelLength_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_CaveSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_Complexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_ChamberCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxCaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxTunnels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewProp_MaxTunnelLength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronCaveGenerationProperties",
	Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::PropPointers),
	sizeof(FAuracronCaveGenerationProperties),
	alignof(FAuracronCaveGenerationProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCaveGenerationProperties ***********************************

// ********** Begin ScriptStruct FAuracronCaveData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCaveData;
class UScriptStruct* FAuracronCaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCaveData, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronCaveData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para dados de caverna\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveId_MetaData[] = {
		{ "Category", "Cave" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Cave" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\xa7\xc3\xa3o da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\xa7\xc3\xa3o da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "Category", "Cave" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de bioma */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de bioma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "Category", "Cave" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Propriedades de gera\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades de gera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTimestamp_MetaData[] = {
		{ "Category", "Cave" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp de gera\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp de gera\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CaveId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTimestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_CaveId = { "CaveId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveData, CaveId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveId_MetaData), NewProp_CaveId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveData, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveData, BiomeType), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) }; // 3074728667
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveData, Properties), Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 1784099713
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_GenerationTimestamp = { "GenerationTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveData, GenerationTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTimestamp_MetaData), NewProp_GenerationTimestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_CaveId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewProp_GenerationTimestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronCaveData",
	Z_Construct_UScriptStruct_FAuracronCaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveData_Statics::PropPointers),
	sizeof(FAuracronCaveData),
	alignof(FAuracronCaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCaveData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCaveData ***************************************************

// ********** Begin ScriptStruct FAuracronCaveProperties *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCaveProperties;
class UScriptStruct* FAuracronCaveProperties::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCaveProperties, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronCaveProperties"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para propriedades de cavernas subterr\xc3\x83\xc2\xa2neas\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para propriedades de cavernas subterr\xc3\x83\xc2\xa2neas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Depth_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade da caverna em metros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade da caverna em metros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "5.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura m\xc3\x83\xc2\xa9""dia da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura m\xc3\x83\xc2\xa9""dia da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "3.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Altura m\xc3\x83\xc2\xa9""dia da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Altura m\xc3\x83\xc2\xa9""dia da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TunnelComplexity_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Complexidade do sistema de t\xc3\x83\xc2\xbaneis (0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Complexidade do sistema de t\xc3\x83\xc2\xbaneis (0-1)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationDensity_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de stalactites/stalagmites */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de stalactites/stalagmites" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HumidityLevel_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de umidade (afeta crescimento de fungos/musgos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de umidade (afeta crescimento de fungos/musgos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Temperature_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "40.0" },
		{ "ClampMin", "-10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temperatura da caverna em Celsius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temperatura da caverna em Celsius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasUndergroundWater_MetaData[] = {
		{ "Category", "Cave Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Presen\xc3\x83\xc2\xa7""a de \xc3\x83\xc2\xa1gua subterr\xc3\x83\xc2\xa2nea */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Presen\xc3\x83\xc2\xa7""a de \xc3\x83\xc2\xa1gua subterr\xc3\x83\xc2\xa2nea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasLuminousCrystals_MetaData[] = {
		{ "Category", "Cave Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Presen\xc3\x83\xc2\xa7""a de cristais luminosos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Presen\xc3\x83\xc2\xa7""a de cristais luminosos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructuralStability_MetaData[] = {
		{ "Category", "Cave Properties" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estabilidade estrutural (0-1, 0 = inst\xc3\x83\xc2\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estabilidade estrutural (0-1, 0 = inst\xc3\x83\xc2\xa1vel)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Depth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Height;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TunnelComplexity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FormationDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HumidityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Temperature;
	static void NewProp_bHasUndergroundWater_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasUndergroundWater;
	static void NewProp_bHasLuminousCrystals_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasLuminousCrystals;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StructuralStability;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCaveProperties>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Depth = { "Depth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, Depth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Depth_MetaData), NewProp_Depth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_TunnelComplexity = { "TunnelComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, TunnelComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TunnelComplexity_MetaData), NewProp_TunnelComplexity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_FormationDensity = { "FormationDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, FormationDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationDensity_MetaData), NewProp_FormationDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_HumidityLevel = { "HumidityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, HumidityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HumidityLevel_MetaData), NewProp_HumidityLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Temperature = { "Temperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, Temperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Temperature_MetaData), NewProp_Temperature_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasUndergroundWater_SetBit(void* Obj)
{
	((FAuracronCaveProperties*)Obj)->bHasUndergroundWater = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasUndergroundWater = { "bHasUndergroundWater", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCaveProperties), &Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasUndergroundWater_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasUndergroundWater_MetaData), NewProp_bHasUndergroundWater_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasLuminousCrystals_SetBit(void* Obj)
{
	((FAuracronCaveProperties*)Obj)->bHasLuminousCrystals = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasLuminousCrystals = { "bHasLuminousCrystals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCaveProperties), &Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasLuminousCrystals_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasLuminousCrystals_MetaData), NewProp_bHasLuminousCrystals_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_StructuralStability = { "StructuralStability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCaveProperties, StructuralStability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructuralStability_MetaData), NewProp_StructuralStability_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Depth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_TunnelComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_FormationDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_HumidityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_Temperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasUndergroundWater,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_bHasLuminousCrystals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewProp_StructuralStability,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronCaveProperties",
	Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::PropPointers),
	sizeof(FAuracronCaveProperties),
	alignof(FAuracronCaveProperties),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCaveProperties()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCaveProperties.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCaveProperties *********************************************

// ********** Begin ScriptStruct FAuracronUndergroundLighting **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting;
class UScriptStruct* FAuracronUndergroundLighting::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronUndergroundLighting, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronUndergroundLighting"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o subterr\xc3\x83\xc2\xa2nea\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o subterr\xc3\x83\xc2\xa2nea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightIntensity_MetaData[] = {
		{ "Category", "Underground Lighting" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da luz ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da luz ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "Underground Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da luz ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da luz ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumetricFogDensity_MetaData[] = {
		{ "Category", "Underground Lighting" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de fog volum\xc3\x83\xc2\xa9trico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de fog volum\xc3\x83\xc2\xa9trico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumetricFogColor_MetaData[] = {
		{ "Category", "Underground Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do fog volum\xc3\x83\xc2\xa9trico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do fog volum\xc3\x83\xc2\xa9trico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrystalLuminosity_MetaData[] = {
		{ "Category", "Underground Lighting" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade de cristais luminosos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade de cristais luminosos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrystalColor_MetaData[] = {
		{ "Category", "Underground Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos cristais luminosos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos cristais luminosos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLumenGI_MetaData[] = {
		{ "Category", "Underground Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Lumen para ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o global */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Lumen para ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o global" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRayTracedReflections_MetaData[] = {
		{ "Category", "Underground Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar ray tracing para reflex\xc3\x83\xc2\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar ray tracing para reflex\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientLightIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VolumetricFogDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VolumetricFogColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CrystalLuminosity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CrystalColor;
	static void NewProp_bUseLumenGI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLumenGI;
	static void NewProp_bUseRayTracedReflections_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRayTracedReflections;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronUndergroundLighting>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_AmbientLightIntensity = { "AmbientLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, AmbientLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightIntensity_MetaData), NewProp_AmbientLightIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_VolumetricFogDensity = { "VolumetricFogDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, VolumetricFogDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumetricFogDensity_MetaData), NewProp_VolumetricFogDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_VolumetricFogColor = { "VolumetricFogColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, VolumetricFogColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumetricFogColor_MetaData), NewProp_VolumetricFogColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_CrystalLuminosity = { "CrystalLuminosity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, CrystalLuminosity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrystalLuminosity_MetaData), NewProp_CrystalLuminosity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_CrystalColor = { "CrystalColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundLighting, CrystalColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrystalColor_MetaData), NewProp_CrystalColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseLumenGI_SetBit(void* Obj)
{
	((FAuracronUndergroundLighting*)Obj)->bUseLumenGI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseLumenGI = { "bUseLumenGI", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUndergroundLighting), &Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseLumenGI_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLumenGI_MetaData), NewProp_bUseLumenGI_MetaData) };
void Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseRayTracedReflections_SetBit(void* Obj)
{
	((FAuracronUndergroundLighting*)Obj)->bUseRayTracedReflections = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseRayTracedReflections = { "bUseRayTracedReflections", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronUndergroundLighting), &Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseRayTracedReflections_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRayTracedReflections_MetaData), NewProp_bUseRayTracedReflections_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_AmbientLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_VolumetricFogDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_VolumetricFogColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_CrystalLuminosity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_CrystalColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseLumenGI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewProp_bUseRayTracedReflections,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronUndergroundLighting",
	Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::PropPointers),
	sizeof(FAuracronUndergroundLighting),
	alignof(FAuracronUndergroundLighting),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronUndergroundLighting()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.InnerSingleton, Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting.InnerSingleton;
}
// ********** End ScriptStruct FAuracronUndergroundLighting ****************************************

// ********** Begin Enum EAuracronGeologicalFormation **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGeologicalFormation;
static UEnum* EAuracronGeologicalFormation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGeologicalFormation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGeologicalFormation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("EAuracronGeologicalFormation"));
	}
	return Z_Registration_Info_UEnum_EAuracronGeologicalFormation.OuterSingleton;
}
template<> AURACRONABISMOUMBRIOBRIDGE_API UEnum* StaticEnum<EAuracronGeologicalFormation>()
{
	return EAuracronGeologicalFormation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chasm.DisplayName", "Chasm" },
		{ "Chasm.Name", "EAuracronGeologicalFormation::Chasm" },
		{ "Column.DisplayName", "Column" },
		{ "Column.Name", "EAuracronGeologicalFormation::Column" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas\n */" },
#endif
		{ "Crystal.DisplayName", "Crystal" },
		{ "Crystal.Name", "EAuracronGeologicalFormation::Crystal" },
		{ "Flowstone.DisplayName", "Flowstone" },
		{ "Flowstone.Name", "EAuracronGeologicalFormation::Flowstone" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
		{ "RockFormation.DisplayName", "Rock Formation" },
		{ "RockFormation.Name", "EAuracronGeologicalFormation::RockFormation" },
		{ "Stalactite.DisplayName", "Stalactite" },
		{ "Stalactite.Name", "EAuracronGeologicalFormation::Stalactite" },
		{ "Stalagmite.DisplayName", "Stalagmite" },
		{ "Stalagmite.Name", "EAuracronGeologicalFormation::Stalagmite" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas" },
#endif
		{ "UndergroundLake.DisplayName", "Underground Lake" },
		{ "UndergroundLake.Name", "EAuracronGeologicalFormation::UndergroundLake" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGeologicalFormation::Stalactite", (int64)EAuracronGeologicalFormation::Stalactite },
		{ "EAuracronGeologicalFormation::Stalagmite", (int64)EAuracronGeologicalFormation::Stalagmite },
		{ "EAuracronGeologicalFormation::Column", (int64)EAuracronGeologicalFormation::Column },
		{ "EAuracronGeologicalFormation::Flowstone", (int64)EAuracronGeologicalFormation::Flowstone },
		{ "EAuracronGeologicalFormation::Crystal", (int64)EAuracronGeologicalFormation::Crystal },
		{ "EAuracronGeologicalFormation::UndergroundLake", (int64)EAuracronGeologicalFormation::UndergroundLake },
		{ "EAuracronGeologicalFormation::Chasm", (int64)EAuracronGeologicalFormation::Chasm },
		{ "EAuracronGeologicalFormation::RockFormation", (int64)EAuracronGeologicalFormation::RockFormation },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	"EAuracronGeologicalFormation",
	"EAuracronGeologicalFormation",
	Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation()
{
	if (!Z_Registration_Info_UEnum_EAuracronGeologicalFormation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGeologicalFormation.InnerSingleton, Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGeologicalFormation.InnerSingleton;
}
// ********** End Enum EAuracronGeologicalFormation ************************************************

// ********** Begin ScriptStruct FAuracronGeologicalFormationConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig;
class UScriptStruct* FAuracronGeologicalFormationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronGeologicalFormationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationType_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationMesh_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh est\xc3\x83\xc2\xa1tico para a forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh est\xc3\x83\xc2\xa1tico para a forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FormationMaterial_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material para a forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material para a forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMin_MetaData[] = {
		{ "Category", "Formation" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala m\xc3\x83\xc2\xadnima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala m\xc3\x83\xc2\xadnima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleMax_MetaData[] = {
		{ "Category", "Formation" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala m\xc3\x83\xc2\xa1xima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala m\xc3\x83\xc2\xa1xima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnDensity_MetaData[] = {
		{ "Category", "Formation" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de spawn (0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de spawn (0-1)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomRotation_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o aleat\xc3\x83\xc2\xb3ria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o aleat\xc3\x83\xc2\xb3ria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSurfaceNormal_MetaData[] = {
		{ "Category", "Formation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alinhar com normal da superf\xc3\x83\xc2\xad""cie */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alinhar com normal da superf\xc3\x83\xc2\xad""cie" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_FormationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FormationType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FormationMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FormationMaterial;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleMax;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnDensity;
	static void NewProp_bRandomRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomRotation;
	static void NewProp_bAlignToSurfaceNormal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSurfaceNormal;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGeologicalFormationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationType = { "FormationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, FormationType), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronGeologicalFormation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationType_MetaData), NewProp_FormationType_MetaData) }; // 2900979120
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationMesh = { "FormationMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, FormationMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationMesh_MetaData), NewProp_FormationMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationMaterial = { "FormationMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, FormationMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FormationMaterial_MetaData), NewProp_FormationMaterial_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_ScaleMin = { "ScaleMin", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, ScaleMin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMin_MetaData), NewProp_ScaleMin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_ScaleMax = { "ScaleMax", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, ScaleMax), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleMax_MetaData), NewProp_ScaleMax_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_SpawnDensity = { "SpawnDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGeologicalFormationConfig, SpawnDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnDensity_MetaData), NewProp_SpawnDensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bRandomRotation_SetBit(void* Obj)
{
	((FAuracronGeologicalFormationConfig*)Obj)->bRandomRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bRandomRotation = { "bRandomRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGeologicalFormationConfig), &Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bRandomRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomRotation_MetaData), NewProp_bRandomRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bAlignToSurfaceNormal_SetBit(void* Obj)
{
	((FAuracronGeologicalFormationConfig*)Obj)->bAlignToSurfaceNormal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bAlignToSurfaceNormal = { "bAlignToSurfaceNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGeologicalFormationConfig), &Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bAlignToSurfaceNormal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSurfaceNormal_MetaData), NewProp_bAlignToSurfaceNormal_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_FormationMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_ScaleMin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_ScaleMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_SpawnDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bRandomRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewProp_bAlignToSurfaceNormal,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronGeologicalFormationConfig",
	Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::PropPointers),
	sizeof(FAuracronGeologicalFormationConfig),
	alignof(FAuracronGeologicalFormationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGeologicalFormationConfig **********************************

// ********** Begin ScriptStruct FAuracronUndergroundBiomeConfig ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig;
class UScriptStruct* FAuracronUndergroundBiomeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, (UObject*)Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge(), TEXT("AuracronUndergroundBiomeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de biomas subterr\xc3\x83\xc2\xa2neos\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de biomas subterr\xc3\x83\xc2\xa2neos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de bioma */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de bioma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeName_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do bioma */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do bioma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnProbability_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Probabilidade de spawn */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Probabilidade de spawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDepth_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade m\xc3\xadnima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade m\xc3\xadnima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDepth_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade m\xc3\xa1xima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade m\xc3\xa1xima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da luz ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da luz ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightIntensity_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da luz ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da luz ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParticleSystem_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\xad""culas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeologicalFormations_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingConfig_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveProperties_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Propriedades da caverna */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedades da caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientParticleSystem_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientSound_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeTemperature_MetaData[] = {
		{ "Category", "Biome" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "-50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temperatura do bioma */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temperatura do bioma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DangerLevel_MetaData[] = {
		{ "Category", "Biome" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de perigo (0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de perigo (0-1)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnProbability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDepth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDepth;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientLightIntensity;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ParticleSystem;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GeologicalFormations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeologicalFormations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightingConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveProperties;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AmbientParticleSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AmbientSound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BiomeTemperature;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DangerLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronUndergroundBiomeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, BiomeType), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) }; // 3074728667
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeName = { "BiomeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, BiomeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeName_MetaData), NewProp_BiomeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_SpawnProbability = { "SpawnProbability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, SpawnProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnProbability_MetaData), NewProp_SpawnProbability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_MinDepth = { "MinDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, MinDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDepth_MetaData), NewProp_MinDepth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_MaxDepth = { "MaxDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, MaxDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDepth_MetaData), NewProp_MaxDepth_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientLightIntensity = { "AmbientLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, AmbientLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightIntensity_MetaData), NewProp_AmbientLightIntensity_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_ParticleSystem = { "ParticleSystem", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, ParticleSystem), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParticleSystem_MetaData), NewProp_ParticleSystem_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_GeologicalFormations_Inner = { "GeologicalFormations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig, METADATA_PARAMS(0, nullptr) }; // 2405762441
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_GeologicalFormations = { "GeologicalFormations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, GeologicalFormations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeologicalFormations_MetaData), NewProp_GeologicalFormations_MetaData) }; // 2405762441
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_LightingConfig = { "LightingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, LightingConfig), Z_Construct_UScriptStruct_FAuracronUndergroundLighting, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingConfig_MetaData), NewProp_LightingConfig_MetaData) }; // 4010356376
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_CaveProperties = { "CaveProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, CaveProperties), Z_Construct_UScriptStruct_FAuracronCaveProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveProperties_MetaData), NewProp_CaveProperties_MetaData) }; // 3857113942
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientParticleSystem = { "AmbientParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, AmbientParticleSystem), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientParticleSystem_MetaData), NewProp_AmbientParticleSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientSound = { "AmbientSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, AmbientSound), Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientSound_MetaData), NewProp_AmbientSound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeTemperature = { "BiomeTemperature", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, BiomeTemperature), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeTemperature_MetaData), NewProp_BiomeTemperature_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_DangerLevel = { "DangerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronUndergroundBiomeConfig, DangerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DangerLevel_MetaData), NewProp_DangerLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_SpawnProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_MinDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_MaxDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_ParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_GeologicalFormations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_GeologicalFormations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_LightingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_CaveProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_AmbientSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_BiomeTemperature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewProp_DangerLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
	nullptr,
	&NewStructOps,
	"AuracronUndergroundBiomeConfig",
	Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::PropPointers),
	sizeof(FAuracronUndergroundBiomeConfig),
	alignof(FAuracronUndergroundBiomeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronUndergroundBiomeConfig *************************************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function AddLuminousCrystals *****************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics
{
	struct AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms
	{
		FVector CaveLocation;
		int32 CrystalCount;
		float LuminosityRange;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Adicionar cristais luminosos\n     */" },
#endif
		{ "CPP_Default_CrystalCount", "10" },
		{ "CPP_Default_LuminosityRange", "500.000000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar cristais luminosos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CrystalCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LuminosityRange;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_CrystalCount = { "CrystalCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms, CrystalCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_LuminosityRange = { "LuminosityRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms, LuminosityRange), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_CrystalCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_LuminosityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "AddLuminousCrystals", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::AuracronAbismoUmbrioBridge_eventAddLuminousCrystals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execAddLuminousCrystals)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_CrystalCount);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LuminosityRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddLuminousCrystals(Z_Param_Out_CaveLocation,Z_Param_CrystalCount,Z_Param_LuminosityRange);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function AddLuminousCrystals *******************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ApplyBiomeToCave ********************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics
{
	struct AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms
	{
		FVector CaveLocation;
		FAuracronUndergroundBiomeConfig BiomeConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar bioma a uma caverna\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar bioma a uma caverna" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_BiomeConfig = { "BiomeConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms, BiomeConfig), Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeConfig_MetaData), NewProp_BiomeConfig_MetaData) }; // 1652372378
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_BiomeConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ApplyBiomeToCave", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::AuracronAbismoUmbrioBridge_eventApplyBiomeToCave_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execApplyBiomeToCave)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_STRUCT_REF(FAuracronUndergroundBiomeConfig,Z_Param_Out_BiomeConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyBiomeToCave(Z_Param_Out_CaveLocation,Z_Param_Out_BiomeConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ApplyBiomeToCave **********************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ClearGeneratedSystem ****************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar sistema gerado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar sistema gerado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ClearGeneratedSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execClearGeneratedSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGeneratedSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ClearGeneratedSystem ******************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ConfigureAdvancedAtmosphericEffects *
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics
{
	struct AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms
	{
		FVector CaveLocation;
		float AtmosphericDensity;
		float ParticleIntensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configure advanced atmospheric effects using UE 5.6 volumetric systems\n     * @param CaveLocation Location of the cave\n     * @param AtmosphericDensity Density of atmospheric effects (0-1)\n     * @param ParticleIntensity Intensity of particle effects (0-1)\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_AtmosphericDensity", "0.500000" },
		{ "CPP_Default_ParticleIntensity", "0.700000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure advanced atmospheric effects using UE 5.6 volumetric systems\n@param CaveLocation Location of the cave\n@param AtmosphericDensity Density of atmospheric effects (0-1)\n@param ParticleIntensity Intensity of particle effects (0-1)\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AtmosphericDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParticleIntensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_AtmosphericDensity = { "AtmosphericDensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms, AtmosphericDensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ParticleIntensity = { "ParticleIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms, ParticleIntensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_AtmosphericDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ParticleIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ConfigureAdvancedAtmosphericEffects", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::AuracronAbismoUmbrioBridge_eventConfigureAdvancedAtmosphericEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execConfigureAdvancedAtmosphericEffects)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AtmosphericDensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ParticleIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConfigureAdvancedAtmosphericEffects(Z_Param_Out_CaveLocation,Z_Param_AtmosphericDensity,Z_Param_ParticleIntensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ConfigureAdvancedAtmosphericEffects ***

// ********** Begin Class UAuracronAbismoUmbrioBridge Function Create3DNavigationPoints ************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics
{
	struct AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms
	{
		TArray<FVector> CaveLocations;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar pontos de navega\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pontos de navega\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CaveLocations;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_CaveLocations_Inner = { "CaveLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_CaveLocations = { "CaveLocations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms, CaveLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocations_MetaData), NewProp_CaveLocations_MetaData) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_CaveLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_CaveLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "Create3DNavigationPoints", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::AuracronAbismoUmbrioBridge_eventCreate3DNavigationPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execCreate3DNavigationPoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_CaveLocations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Create3DNavigationPoints(Z_Param_Out_CaveLocations);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function Create3DNavigationPoints **************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function CreateAdvancedCaveNavigation ********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics
{
	struct AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms
	{
		FVector CaveLocation;
		int32 NavigationComplexity;
		bool bEnable3DPathfinding;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create advanced cave navigation with 3D pathfinding\n     * @param CaveLocation Location of the cave\n     * @param NavigationComplexity Navigation complexity (1-10)\n     * @param bEnable3DPathfinding Enable 3D pathfinding\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnable3DPathfinding", "true" },
		{ "CPP_Default_NavigationComplexity", "6" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create advanced cave navigation with 3D pathfinding\n@param CaveLocation Location of the cave\n@param NavigationComplexity Navigation complexity (1-10)\n@param bEnable3DPathfinding Enable 3D pathfinding\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NavigationComplexity;
	static void NewProp_bEnable3DPathfinding_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable3DPathfinding;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_NavigationComplexity = { "NavigationComplexity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms, NavigationComplexity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_bEnable3DPathfinding_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms*)Obj)->bEnable3DPathfinding = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_bEnable3DPathfinding = { "bEnable3DPathfinding", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_bEnable3DPathfinding_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_NavigationComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_bEnable3DPathfinding,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "CreateAdvancedCaveNavigation", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::AuracronAbismoUmbrioBridge_eventCreateAdvancedCaveNavigation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execCreateAdvancedCaveNavigation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_NavigationComplexity);
	P_GET_UBOOL(Z_Param_bEnable3DPathfinding);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateAdvancedCaveNavigation(Z_Param_Out_CaveLocation,Z_Param_NavigationComplexity,Z_Param_bEnable3DPathfinding);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function CreateAdvancedCaveNavigation **********

// ********** Begin Class UAuracronAbismoUmbrioBridge Function CreateAdvancedGeologicalFormations **
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics
{
	struct AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms
	{
		FVector CaveLocation;
		int32 FormationComplexity;
		bool bUseNaniteGeometry;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create advanced geological formations using Nanite virtualized geometry\n     * @param CaveLocation Location of the cave\n     * @param FormationComplexity Complexity level (1-10)\n     * @param bUseNaniteGeometry Use Nanite for high-detail geometry\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bUseNaniteGeometry", "true" },
		{ "CPP_Default_FormationComplexity", "5" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create advanced geological formations using Nanite virtualized geometry\n@param CaveLocation Location of the cave\n@param FormationComplexity Complexity level (1-10)\n@param bUseNaniteGeometry Use Nanite for high-detail geometry\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FormationComplexity;
	static void NewProp_bUseNaniteGeometry_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNaniteGeometry;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_FormationComplexity = { "FormationComplexity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms, FormationComplexity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_bUseNaniteGeometry_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms*)Obj)->bUseNaniteGeometry = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_bUseNaniteGeometry = { "bUseNaniteGeometry", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_bUseNaniteGeometry_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_FormationComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_bUseNaniteGeometry,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "CreateAdvancedGeologicalFormations", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::AuracronAbismoUmbrioBridge_eventCreateAdvancedGeologicalFormations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execCreateAdvancedGeologicalFormations)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_FormationComplexity);
	P_GET_UBOOL(Z_Param_bUseNaniteGeometry);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateAdvancedGeologicalFormations(Z_Param_Out_CaveLocation,Z_Param_FormationComplexity,Z_Param_bUseNaniteGeometry);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function CreateAdvancedGeologicalFormations ****

// ********** Begin Class UAuracronAbismoUmbrioBridge Function CreateProceduralCaveEcosystems ******
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics
{
	struct AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms
	{
		FVector CaveLocation;
		int32 BiodiversityLevel;
		bool bEnableAdvancedAI;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create procedural cave ecosystems with advanced AI\n     * @param CaveLocation Location of the cave\n     * @param BiodiversityLevel Biodiversity level (1-10)\n     * @param bEnableAdvancedAI Enable advanced AI behaviors\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnableAdvancedAI", "true" },
		{ "CPP_Default_BiodiversityLevel", "5" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create procedural cave ecosystems with advanced AI\n@param CaveLocation Location of the cave\n@param BiodiversityLevel Biodiversity level (1-10)\n@param bEnableAdvancedAI Enable advanced AI behaviors\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BiodiversityLevel;
	static void NewProp_bEnableAdvancedAI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdvancedAI;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_BiodiversityLevel = { "BiodiversityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms, BiodiversityLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_bEnableAdvancedAI_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms*)Obj)->bEnableAdvancedAI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_bEnableAdvancedAI = { "bEnableAdvancedAI", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_bEnableAdvancedAI_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_BiodiversityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_bEnableAdvancedAI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "CreateProceduralCaveEcosystems", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::AuracronAbismoUmbrioBridge_eventCreateProceduralCaveEcosystems_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execCreateProceduralCaveEcosystems)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_BiodiversityLevel);
	P_GET_UBOOL(Z_Param_bEnableAdvancedAI);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateProceduralCaveEcosystems(Z_Param_Out_CaveLocation,Z_Param_BiodiversityLevel,Z_Param_bEnableAdvancedAI);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function CreateProceduralCaveEcosystems ********

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ExecutePythonScript *****************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics
{
	struct AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms
	{
		FString ScriptPath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Executar script Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executar script Python" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptPath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ScriptPath = { "ScriptPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms, ScriptPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptPath_MetaData), NewProp_ScriptPath_MetaData) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ScriptPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ExecutePythonScript", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::AuracronAbismoUmbrioBridge_eventExecutePythonScript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execExecutePythonScript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExecutePythonScript(Z_Param_ScriptPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ExecutePythonScript *******************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateAdvancedCaveMaterials *******
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms
	{
		FVector CaveLocation;
		int32 MaterialComplexity;
		bool bUseProceduralTextures;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate advanced cave materials with procedural texturing\n     * @param CaveLocation Location of the cave\n     * @param MaterialComplexity Material complexity level (1-10)\n     * @param bUseProceduralTextures Use procedural texture generation\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bUseProceduralTextures", "true" },
		{ "CPP_Default_MaterialComplexity", "7" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate advanced cave materials with procedural texturing\n@param CaveLocation Location of the cave\n@param MaterialComplexity Material complexity level (1-10)\n@param bUseProceduralTextures Use procedural texture generation\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaterialComplexity;
	static void NewProp_bUseProceduralTextures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseProceduralTextures;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_MaterialComplexity = { "MaterialComplexity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms, MaterialComplexity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_bUseProceduralTextures_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms*)Obj)->bUseProceduralTextures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_bUseProceduralTextures = { "bUseProceduralTextures", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_bUseProceduralTextures_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_MaterialComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_bUseProceduralTextures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateAdvancedCaveMaterials", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::AuracronAbismoUmbrioBridge_eventGenerateAdvancedCaveMaterials_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateAdvancedCaveMaterials)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaterialComplexity);
	P_GET_UBOOL(Z_Param_bUseProceduralTextures);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateAdvancedCaveMaterials(Z_Param_Out_CaveLocation,Z_Param_MaterialComplexity,Z_Param_bUseProceduralTextures);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateAdvancedCaveMaterials *********

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateAdvancedWaterSystems ********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms
	{
		FVector CaveLocation;
		float WaterLevel;
		bool bEnableFluidSimulation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate advanced cave water systems with fluid simulation\n     * @param CaveLocation Location of the cave\n     * @param WaterLevel Water level (0-1)\n     * @param bEnableFluidSimulation Enable advanced fluid simulation\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnableFluidSimulation", "true" },
		{ "CPP_Default_WaterLevel", "0.300000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate advanced cave water systems with fluid simulation\n@param CaveLocation Location of the cave\n@param WaterLevel Water level (0-1)\n@param bEnableFluidSimulation Enable advanced fluid simulation\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WaterLevel;
	static void NewProp_bEnableFluidSimulation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFluidSimulation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_WaterLevel = { "WaterLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms, WaterLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_bEnableFluidSimulation_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms*)Obj)->bEnableFluidSimulation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_bEnableFluidSimulation = { "bEnableFluidSimulation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_bEnableFluidSimulation_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_WaterLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_bEnableFluidSimulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateAdvancedWaterSystems", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::AuracronAbismoUmbrioBridge_eventGenerateAdvancedWaterSystems_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateAdvancedWaterSystems)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WaterLevel);
	P_GET_UBOOL(Z_Param_bEnableFluidSimulation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateAdvancedWaterSystems(Z_Param_Out_CaveLocation,Z_Param_WaterLevel,Z_Param_bEnableFluidSimulation);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateAdvancedWaterSystems **********

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateCave ************************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateCave_Parms
	{
		FVector Location;
		FAuracronUndergroundBiomeConfig BiomeConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar caverna individual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar caverna individual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateCave_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_BiomeConfig = { "BiomeConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateCave_Parms, BiomeConfig), Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeConfig_MetaData), NewProp_BiomeConfig_MetaData) }; // 1652372378
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateCave_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateCave_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_BiomeConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateCave", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::AuracronAbismoUmbrioBridge_eventGenerateCave_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::AuracronAbismoUmbrioBridge_eventGenerateCave_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateCave)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FAuracronUndergroundBiomeConfig,Z_Param_Out_BiomeConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateCave(Z_Param_Out_Location,Z_Param_Out_BiomeConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateCave **************************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateCaveNavMesh *****************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms
	{
		FVector CaveLocation;
		float CaveRadius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar navmesh para cavernas\n     */" },
#endif
		{ "CPP_Default_CaveRadius", "50.000000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar navmesh para cavernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CaveRadius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_CaveRadius = { "CaveRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms, CaveRadius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_CaveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateCaveNavMesh", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::AuracronAbismoUmbrioBridge_eventGenerateCaveNavMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateCaveNavMesh)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CaveRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateCaveNavMesh(Z_Param_Out_CaveLocation,Z_Param_CaveRadius);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateCaveNavMesh *******************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateGeologicalFormations ********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms
	{
		FVector CaveCenter;
		TArray<FAuracronGeologicalFormationConfig> Formations;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar forma\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es geol\xc3\x83\xc2\xb3gicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveCenter_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Formations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveCenter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Formations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Formations;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_CaveCenter = { "CaveCenter", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms, CaveCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveCenter_MetaData), NewProp_CaveCenter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_Formations_Inner = { "Formations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig, METADATA_PARAMS(0, nullptr) }; // 2405762441
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_Formations = { "Formations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms, Formations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Formations_MetaData), NewProp_Formations_MetaData) }; // 2405762441
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_CaveCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_Formations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_Formations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateGeologicalFormations", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::AuracronAbismoUmbrioBridge_eventGenerateGeologicalFormations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateGeologicalFormations)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveCenter);
	P_GET_TARRAY_REF(FAuracronGeologicalFormationConfig,Z_Param_Out_Formations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateGeologicalFormations(Z_Param_Out_CaveCenter,Z_Param_Out_Formations);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateGeologicalFormations **********

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateProceduralCaveAcoustics *****
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms
	{
		FVector CaveLocation;
		float ReverbIntensity;
		float EchoDelay;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Generate procedural cave acoustics using UE 5.6 audio systems\n     * @param CaveLocation Location of the cave\n     * @param ReverbIntensity Reverb intensity (0-1)\n     * @param EchoDelay Echo delay in seconds\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_EchoDelay", "0.500000" },
		{ "CPP_Default_ReverbIntensity", "0.800000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate procedural cave acoustics using UE 5.6 audio systems\n@param CaveLocation Location of the cave\n@param ReverbIntensity Reverb intensity (0-1)\n@param EchoDelay Echo delay in seconds\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReverbIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EchoDelay;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReverbIntensity = { "ReverbIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms, ReverbIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_EchoDelay = { "EchoDelay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms, EchoDelay), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReverbIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_EchoDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateProceduralCaveAcoustics", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::AuracronAbismoUmbrioBridge_eventGenerateProceduralCaveAcoustics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateProceduralCaveAcoustics)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ReverbIntensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EchoDelay);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateProceduralCaveAcoustics(Z_Param_Out_CaveLocation,Z_Param_ReverbIntensity,Z_Param_EchoDelay);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateProceduralCaveAcoustics *******

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateTunnelNetwork ***************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms
	{
		TArray<FVector> CaveLocations;
		float TunnelWidth;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar rede de t\xc3\x83\xc2\xbaneis conectando cavernas\n     */" },
#endif
		{ "CPP_Default_TunnelWidth", "5.000000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar rede de t\xc3\x83\xc2\xbaneis conectando cavernas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CaveLocations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TunnelWidth;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_CaveLocations_Inner = { "CaveLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_CaveLocations = { "CaveLocations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms, CaveLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocations_MetaData), NewProp_CaveLocations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_TunnelWidth = { "TunnelWidth", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms, TunnelWidth), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_CaveLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_CaveLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_TunnelWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateTunnelNetwork", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::AuracronAbismoUmbrioBridge_eventGenerateTunnelNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateTunnelNetwork)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_CaveLocations);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TunnelWidth);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateTunnelNetwork(Z_Param_Out_CaveLocations,Z_Param_TunnelWidth);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateTunnelNetwork *****************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateUndergroundCave *************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms
	{
		FVector CaveLocation;
		FAuracronCaveGenerationProperties Properties;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar caverna subterr\xc3\xa2nea com propriedades espec\xc3\xad""ficas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar caverna subterr\xc3\xa2nea com propriedades espec\xc3\xad""ficas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms, Properties), Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 1784099713
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateUndergroundCave", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::AuracronAbismoUmbrioBridge_eventGenerateUndergroundCave_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateUndergroundCave)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_STRUCT_REF(FAuracronCaveGenerationProperties,Z_Param_Out_Properties);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateUndergroundCave(Z_Param_Out_CaveLocation,Z_Param_Out_Properties);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateUndergroundCave ***************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GenerateUndergroundSystem ***********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms
	{
		FVector Origin;
		FAuracronCaveProperties Properties;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar sistema de cavernas procedural\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar sistema de cavernas procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Origin_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Properties_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Origin;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Properties;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_Origin = { "Origin", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms, Origin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Origin_MetaData), NewProp_Origin_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_Properties = { "Properties", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms, Properties), Z_Construct_UScriptStruct_FAuracronCaveProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Properties_MetaData), NewProp_Properties_MetaData) }; // 3857113942
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_Origin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_Properties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GenerateUndergroundSystem", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::AuracronAbismoUmbrioBridge_eventGenerateUndergroundSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGenerateUndergroundSystem)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Origin);
	P_GET_STRUCT_REF(FAuracronCaveProperties,Z_Param_Out_Properties);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateUndergroundSystem(Z_Param_Out_Origin,Z_Param_Out_Properties);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GenerateUndergroundSystem *************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GetBiomeConfiguration ***************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGetBiomeConfiguration_Parms
	{
		EAuracronUndergroundBiome BiomeType;
		FAuracronUndergroundBiomeConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de bioma por tipo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de bioma por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGetBiomeConfiguration_Parms, BiomeType), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, METADATA_PARAMS(0, nullptr) }; // 3074728667
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGetBiomeConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, METADATA_PARAMS(0, nullptr) }; // 1652372378
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GetBiomeConfiguration", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::AuracronAbismoUmbrioBridge_eventGetBiomeConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::AuracronAbismoUmbrioBridge_eventGetBiomeConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGetBiomeConfiguration)
{
	P_GET_ENUM(EAuracronUndergroundBiome,Z_Param_BiomeType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronUndergroundBiomeConfig*)Z_Param__Result=P_THIS->GetBiomeConfiguration(EAuracronUndergroundBiome(Z_Param_BiomeType));
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GetBiomeConfiguration *****************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GetSystemDataForPython **************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGetSystemDataForPython_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter dados para Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter dados para Python" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGetSystemDataForPython_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GetSystemDataForPython", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::AuracronAbismoUmbrioBridge_eventGetSystemDataForPython_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::AuracronAbismoUmbrioBridge_eventGetSystemDataForPython_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGetSystemDataForPython)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSystemDataForPython();
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GetSystemDataForPython ****************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function GetSystemStatistics *****************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics
{
	struct AuracronAbismoUmbrioBridge_eventGetSystemStatistics_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\x83\xc2\xadsticas do sistema\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\x83\xc2\xadsticas do sistema" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventGetSystemStatistics_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "GetSystemStatistics", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::AuracronAbismoUmbrioBridge_eventGetSystemStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::AuracronAbismoUmbrioBridge_eventGetSystemStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execGetSystemStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSystemStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function GetSystemStatistics *******************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function InitializePythonBindings ************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics
{
	struct AuracronAbismoUmbrioBridge_eventInitializePythonBindings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Python" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Inicializar bindings Python\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar bindings Python" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventInitializePythonBindings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventInitializePythonBindings_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "InitializePythonBindings", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::AuracronAbismoUmbrioBridge_eventInitializePythonBindings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::AuracronAbismoUmbrioBridge_eventInitializePythonBindings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execInitializePythonBindings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePythonBindings();
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function InitializePythonBindings **************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetBiomeConfiguration ***************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetBiomeConfiguration_Parms
	{
		EAuracronUndergroundBiome BiomeType;
		FAuracronUndergroundBiomeConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de bioma\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de bioma" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_BiomeType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetBiomeConfiguration_Parms, BiomeType), Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, METADATA_PARAMS(0, nullptr) }; // 3074728667
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetBiomeConfiguration_Parms, Config), Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1652372378
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_BiomeType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetBiomeConfiguration", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::AuracronAbismoUmbrioBridge_eventSetBiomeConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::AuracronAbismoUmbrioBridge_eventSetBiomeConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetBiomeConfiguration)
{
	P_GET_ENUM(EAuracronUndergroundBiome,Z_Param_BiomeType);
	P_GET_STRUCT_REF(FAuracronUndergroundBiomeConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBiomeConfiguration(EAuracronUndergroundBiome(Z_Param_BiomeType),Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetBiomeConfiguration *****************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetupAdvancedCavePhysics ************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms
	{
		FVector CaveLocation;
		float StructuralIntegrity;
		bool bEnableDestruction;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Setup advanced cave physics with destruction systems\n     * @param CaveLocation Location of the cave\n     * @param StructuralIntegrity Structural integrity (0-1)\n     * @param bEnableDestruction Enable destruction physics\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnableDestruction", "true" },
		{ "CPP_Default_StructuralIntegrity", "0.800000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup advanced cave physics with destruction systems\n@param CaveLocation Location of the cave\n@param StructuralIntegrity Structural integrity (0-1)\n@param bEnableDestruction Enable destruction physics\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StructuralIntegrity;
	static void NewProp_bEnableDestruction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDestruction;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_StructuralIntegrity = { "StructuralIntegrity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms, StructuralIntegrity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_bEnableDestruction_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms*)Obj)->bEnableDestruction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_bEnableDestruction = { "bEnableDestruction", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_bEnableDestruction_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_StructuralIntegrity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_bEnableDestruction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetupAdvancedCavePhysics", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::AuracronAbismoUmbrioBridge_eventSetupAdvancedCavePhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetupAdvancedCavePhysics)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StructuralIntegrity);
	P_GET_UBOOL(Z_Param_bEnableDestruction);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupAdvancedCavePhysics(Z_Param_Out_CaveLocation,Z_Param_StructuralIntegrity,Z_Param_bEnableDestruction);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetupAdvancedCavePhysics **************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetupDynamicCaveLighting ************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms
	{
		FVector CaveLocation;
		float LightingIntensity;
		bool bEnableRayTracing;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Setup dynamic cave lighting with Lumen integration\n     * @param CaveLocation Location of the cave\n     * @param LightingIntensity Overall lighting intensity\n     * @param bEnableRayTracing Enable hardware ray tracing if available\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnableRayTracing", "true" },
		{ "CPP_Default_LightingIntensity", "0.300000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup dynamic cave lighting with Lumen integration\n@param CaveLocation Location of the cave\n@param LightingIntensity Overall lighting intensity\n@param bEnableRayTracing Enable hardware ray tracing if available\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightingIntensity;
	static void NewProp_bEnableRayTracing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRayTracing;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_LightingIntensity = { "LightingIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms, LightingIntensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_bEnableRayTracing_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms*)Obj)->bEnableRayTracing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_bEnableRayTracing = { "bEnableRayTracing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_bEnableRayTracing_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_LightingIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_bEnableRayTracing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetupDynamicCaveLighting", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::AuracronAbismoUmbrioBridge_eventSetupDynamicCaveLighting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetupDynamicCaveLighting)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LightingIntensity);
	P_GET_UBOOL(Z_Param_bEnableRayTracing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupDynamicCaveLighting(Z_Param_Out_CaveLocation,Z_Param_LightingIntensity,Z_Param_bEnableRayTracing);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetupDynamicCaveLighting **************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetupDynamicWeatherEffects **********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms
	{
		FVector CaveLocation;
		float WeatherIntensity;
		bool bEnableWindEffects;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Setup dynamic weather effects for cave entrances\n     * @param CaveLocation Location of the cave\n     * @param WeatherIntensity Weather effect intensity (0-1)\n     * @param bEnableWindEffects Enable wind particle effects\n     * @return True if successful\n     */" },
#endif
		{ "CPP_Default_bEnableWindEffects", "true" },
		{ "CPP_Default_WeatherIntensity", "0.600000" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup dynamic weather effects for cave entrances\n@param CaveLocation Location of the cave\n@param WeatherIntensity Weather effect intensity (0-1)\n@param bEnableWindEffects Enable wind particle effects\n@return True if successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherIntensity;
	static void NewProp_bEnableWindEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWindEffects;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_WeatherIntensity = { "WeatherIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms, WeatherIntensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_bEnableWindEffects_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms*)Obj)->bEnableWindEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_bEnableWindEffects = { "bEnableWindEffects", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_bEnableWindEffects_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_WeatherIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_bEnableWindEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetupDynamicWeatherEffects", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::AuracronAbismoUmbrioBridge_eventSetupDynamicWeatherEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetupDynamicWeatherEffects)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_WeatherIntensity);
	P_GET_UBOOL(Z_Param_bEnableWindEffects);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupDynamicWeatherEffects(Z_Param_Out_CaveLocation,Z_Param_WeatherIntensity,Z_Param_bEnableWindEffects);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetupDynamicWeatherEffects ************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetupUndergroundLighting ************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms
	{
		FVector CaveLocation;
		FAuracronUndergroundLighting LightingConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o subterr\xc3\x83\xc2\xa2nea\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar ilumina\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o subterr\xc3\x83\xc2\xa2nea" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightingConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_LightingConfig = { "LightingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms, LightingConfig), Z_Construct_UScriptStruct_FAuracronUndergroundLighting, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingConfig_MetaData), NewProp_LightingConfig_MetaData) }; // 4010356376
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_LightingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetupUndergroundLighting", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::AuracronAbismoUmbrioBridge_eventSetupUndergroundLighting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetupUndergroundLighting)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_STRUCT_REF(FAuracronUndergroundLighting,Z_Param_Out_LightingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupUndergroundLighting(Z_Param_Out_CaveLocation,Z_Param_Out_LightingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetupUndergroundLighting **************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function SetupVolumetricFog ******************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics
{
	struct AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms
	{
		FVector CaveLocation;
		FAuracronUndergroundLighting LightingConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar fog volum\xc3\x83\xc2\xa9trico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar fog volum\xc3\x83\xc2\xa9trico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightingConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightingConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_CaveLocation = { "CaveLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms, CaveLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocation_MetaData), NewProp_CaveLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_LightingConfig = { "LightingConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms, LightingConfig), Z_Construct_UScriptStruct_FAuracronUndergroundLighting, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightingConfig_MetaData), NewProp_LightingConfig_MetaData) }; // 4010356376
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_CaveLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_LightingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "SetupVolumetricFog", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::AuracronAbismoUmbrioBridge_eventSetupVolumetricFog_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execSetupVolumetricFog)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CaveLocation);
	P_GET_STRUCT_REF(FAuracronUndergroundLighting,Z_Param_Out_LightingConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupVolumetricFog(Z_Param_Out_CaveLocation,Z_Param_Out_LightingConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function SetupVolumetricFog ********************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ValidateSystemIntegrity *************
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics
{
	struct AuracronAbismoUmbrioBridge_eventValidateSystemIntegrity_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar integridade do sistema\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar integridade do sistema" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventValidateSystemIntegrity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventValidateSystemIntegrity_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ValidateSystemIntegrity", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::AuracronAbismoUmbrioBridge_eventValidateSystemIntegrity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::AuracronAbismoUmbrioBridge_eventValidateSystemIntegrity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execValidateSystemIntegrity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSystemIntegrity();
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ValidateSystemIntegrity ***************

// ********** Begin Class UAuracronAbismoUmbrioBridge Function ValidateTunnelConnectivity **********
struct Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics
{
	struct AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms
	{
		TArray<FVector> CaveLocations;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Abismo Umbrio|Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar conectividade de t\xc3\x83\xc2\xbaneis\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar conectividade de t\xc3\x83\xc2\xbaneis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaveLocations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CaveLocations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CaveLocations;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_CaveLocations_Inner = { "CaveLocations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_CaveLocations = { "CaveLocations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms, CaveLocations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaveLocations_MetaData), NewProp_CaveLocations_MetaData) };
void Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms), &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_CaveLocations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_CaveLocations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAbismoUmbrioBridge, nullptr, "ValidateTunnelConnectivity", Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::AuracronAbismoUmbrioBridge_eventValidateTunnelConnectivity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAbismoUmbrioBridge::execValidateTunnelConnectivity)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_CaveLocations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateTunnelConnectivity(Z_Param_Out_CaveLocations);
	P_NATIVE_END;
}
// ********** End Class UAuracronAbismoUmbrioBridge Function ValidateTunnelConnectivity ************

// ********** Begin Class UAuracronAbismoUmbrioBridge **********************************************
void UAuracronAbismoUmbrioBridge::StaticRegisterNativesUAuracronAbismoUmbrioBridge()
{
	UClass* Class = UAuracronAbismoUmbrioBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddLuminousCrystals", &UAuracronAbismoUmbrioBridge::execAddLuminousCrystals },
		{ "ApplyBiomeToCave", &UAuracronAbismoUmbrioBridge::execApplyBiomeToCave },
		{ "ClearGeneratedSystem", &UAuracronAbismoUmbrioBridge::execClearGeneratedSystem },
		{ "ConfigureAdvancedAtmosphericEffects", &UAuracronAbismoUmbrioBridge::execConfigureAdvancedAtmosphericEffects },
		{ "Create3DNavigationPoints", &UAuracronAbismoUmbrioBridge::execCreate3DNavigationPoints },
		{ "CreateAdvancedCaveNavigation", &UAuracronAbismoUmbrioBridge::execCreateAdvancedCaveNavigation },
		{ "CreateAdvancedGeologicalFormations", &UAuracronAbismoUmbrioBridge::execCreateAdvancedGeologicalFormations },
		{ "CreateProceduralCaveEcosystems", &UAuracronAbismoUmbrioBridge::execCreateProceduralCaveEcosystems },
		{ "ExecutePythonScript", &UAuracronAbismoUmbrioBridge::execExecutePythonScript },
		{ "GenerateAdvancedCaveMaterials", &UAuracronAbismoUmbrioBridge::execGenerateAdvancedCaveMaterials },
		{ "GenerateAdvancedWaterSystems", &UAuracronAbismoUmbrioBridge::execGenerateAdvancedWaterSystems },
		{ "GenerateCave", &UAuracronAbismoUmbrioBridge::execGenerateCave },
		{ "GenerateCaveNavMesh", &UAuracronAbismoUmbrioBridge::execGenerateCaveNavMesh },
		{ "GenerateGeologicalFormations", &UAuracronAbismoUmbrioBridge::execGenerateGeologicalFormations },
		{ "GenerateProceduralCaveAcoustics", &UAuracronAbismoUmbrioBridge::execGenerateProceduralCaveAcoustics },
		{ "GenerateTunnelNetwork", &UAuracronAbismoUmbrioBridge::execGenerateTunnelNetwork },
		{ "GenerateUndergroundCave", &UAuracronAbismoUmbrioBridge::execGenerateUndergroundCave },
		{ "GenerateUndergroundSystem", &UAuracronAbismoUmbrioBridge::execGenerateUndergroundSystem },
		{ "GetBiomeConfiguration", &UAuracronAbismoUmbrioBridge::execGetBiomeConfiguration },
		{ "GetSystemDataForPython", &UAuracronAbismoUmbrioBridge::execGetSystemDataForPython },
		{ "GetSystemStatistics", &UAuracronAbismoUmbrioBridge::execGetSystemStatistics },
		{ "InitializePythonBindings", &UAuracronAbismoUmbrioBridge::execInitializePythonBindings },
		{ "SetBiomeConfiguration", &UAuracronAbismoUmbrioBridge::execSetBiomeConfiguration },
		{ "SetupAdvancedCavePhysics", &UAuracronAbismoUmbrioBridge::execSetupAdvancedCavePhysics },
		{ "SetupDynamicCaveLighting", &UAuracronAbismoUmbrioBridge::execSetupDynamicCaveLighting },
		{ "SetupDynamicWeatherEffects", &UAuracronAbismoUmbrioBridge::execSetupDynamicWeatherEffects },
		{ "SetupUndergroundLighting", &UAuracronAbismoUmbrioBridge::execSetupUndergroundLighting },
		{ "SetupVolumetricFog", &UAuracronAbismoUmbrioBridge::execSetupVolumetricFog },
		{ "ValidateSystemIntegrity", &UAuracronAbismoUmbrioBridge::execValidateSystemIntegrity },
		{ "ValidateTunnelConnectivity", &UAuracronAbismoUmbrioBridge::execValidateTunnelConnectivity },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge;
UClass* UAuracronAbismoUmbrioBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronAbismoUmbrioBridge;
	if (!Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAbismoUmbrioBridge"),
			Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.InnerSingleton,
			StaticRegisterNativesUAuracronAbismoUmbrioBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge_NoRegister()
{
	return UAuracronAbismoUmbrioBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Abismo Umbrio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema Abismo Umbrio\n * Respons\xc3\x83\xc2\xa1vel pela gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural de sistemas subterr\xc3\x83\xc2\xa2neos complexos\n */" },
#endif
		{ "DisplayName", "AURACRON Abismo Umbrio Bridge" },
		{ "IncludePath", "AuracronAbismoUmbrioBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema Abismo Umbrio\nRespons\xc3\x83\xc2\xa1vel pela gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural de sistemas subterr\xc3\x83\xc2\xa2neos complexos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de biomas dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de biomas dispon\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationParameters_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\xa2metros de gera\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\xa2metros de gera\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveCaves_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cavernas ativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cavernas ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente PCG para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationSeed_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "999999" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seed para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultiThreading_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar multi-threading para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar multi-threading para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetailLevel_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "5" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de detalhe para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de detalhe para gera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSystemRadius_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio m\xc3\x83\xc2\xa1ximo do sistema subterr\xc3\x83\xc2\xa2neo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio m\xc3\x83\xc2\xa1ximo do sistema subterr\xc3\x83\xc2\xa2neo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSystemDepth_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade m\xc3\x83\xc2\xa1xima do sistema */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAbismoUmbrioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade m\xc3\x83\xc2\xa1xima do sistema" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeConfigurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BiomeConfigurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BiomeConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BiomeConfigurations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveCaves_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveCaves_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveCaves;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GenerationSeed;
	static void NewProp_bUseMultiThreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultiThreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DetailLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSystemRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSystemDepth;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_AddLuminousCrystals, "AddLuminousCrystals" }, // 3769498684
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ApplyBiomeToCave, "ApplyBiomeToCave" }, // 2085635466
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ClearGeneratedSystem, "ClearGeneratedSystem" }, // 1292944573
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ConfigureAdvancedAtmosphericEffects, "ConfigureAdvancedAtmosphericEffects" }, // 2658660586
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_Create3DNavigationPoints, "Create3DNavigationPoints" }, // 3776598371
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedCaveNavigation, "CreateAdvancedCaveNavigation" }, // 670164863
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateAdvancedGeologicalFormations, "CreateAdvancedGeologicalFormations" }, // 3886361388
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_CreateProceduralCaveEcosystems, "CreateProceduralCaveEcosystems" }, // 279939756
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ExecutePythonScript, "ExecutePythonScript" }, // 2619474153
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedCaveMaterials, "GenerateAdvancedCaveMaterials" }, // 3783468344
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateAdvancedWaterSystems, "GenerateAdvancedWaterSystems" }, // 184222413
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCave, "GenerateCave" }, // 3083254680
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateCaveNavMesh, "GenerateCaveNavMesh" }, // 444280344
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateGeologicalFormations, "GenerateGeologicalFormations" }, // 2602793738
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateProceduralCaveAcoustics, "GenerateProceduralCaveAcoustics" }, // 4028360292
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateTunnelNetwork, "GenerateTunnelNetwork" }, // 2956960850
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundCave, "GenerateUndergroundCave" }, // 2752787815
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GenerateUndergroundSystem, "GenerateUndergroundSystem" }, // 2655999329
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetBiomeConfiguration, "GetBiomeConfiguration" }, // 754027823
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemDataForPython, "GetSystemDataForPython" }, // 716152532
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_GetSystemStatistics, "GetSystemStatistics" }, // 2382767575
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_InitializePythonBindings, "InitializePythonBindings" }, // 1776119772
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetBiomeConfiguration, "SetBiomeConfiguration" }, // 1583357687
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupAdvancedCavePhysics, "SetupAdvancedCavePhysics" }, // 2747909913
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicCaveLighting, "SetupDynamicCaveLighting" }, // 3968519580
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupDynamicWeatherEffects, "SetupDynamicWeatherEffects" }, // 3353308380
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupUndergroundLighting, "SetupUndergroundLighting" }, // 3798464802
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_SetupVolumetricFog, "SetupVolumetricFog" }, // 622917541
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateSystemIntegrity, "ValidateSystemIntegrity" }, // 299177974
		{ &Z_Construct_UFunction_UAuracronAbismoUmbrioBridge_ValidateTunnelConnectivity, "ValidateTunnelConnectivity" }, // 4096198561
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAbismoUmbrioBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_ValueProp = { "BiomeConfigurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig, METADATA_PARAMS(0, nullptr) }; // 1652372378
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_Key_KeyProp = { "BiomeConfigurations_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronAbismoUmbrioBridge_EAuracronUndergroundBiome, METADATA_PARAMS(0, nullptr) }; // 3074728667
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations = { "BiomeConfigurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, BiomeConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeConfigurations_MetaData), NewProp_BiomeConfigurations_MetaData) }; // 3074728667 1652372378
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_GenerationParameters = { "GenerationParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, GenerationParameters), Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationParameters_MetaData), NewProp_GenerationParameters_MetaData) }; // 1784099713
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves_ValueProp = { "ActiveCaves", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronCaveData, METADATA_PARAMS(0, nullptr) }; // 1902154694
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves_Key_KeyProp = { "ActiveCaves_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves = { "ActiveCaves", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, ActiveCaves), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveCaves_MetaData), NewProp_ActiveCaves_MetaData) }; // 1902154694
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_GenerationSeed = { "GenerationSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, GenerationSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationSeed_MetaData), NewProp_GenerationSeed_MetaData) };
void Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_bUseMultiThreading_SetBit(void* Obj)
{
	((UAuracronAbismoUmbrioBridge*)Obj)->bUseMultiThreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_bUseMultiThreading = { "bUseMultiThreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAbismoUmbrioBridge), &Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_bUseMultiThreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultiThreading_MetaData), NewProp_bUseMultiThreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_DetailLevel = { "DetailLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, DetailLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetailLevel_MetaData), NewProp_DetailLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_MaxSystemRadius = { "MaxSystemRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, MaxSystemRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSystemRadius_MetaData), NewProp_MaxSystemRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_MaxSystemDepth = { "MaxSystemDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAbismoUmbrioBridge, MaxSystemDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSystemDepth_MetaData), NewProp_MaxSystemDepth_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_BiomeConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_GenerationParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_ActiveCaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_GenerationSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_bUseMultiThreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_DetailLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_MaxSystemRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::NewProp_MaxSystemDepth,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAbismoUmbrioBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::ClassParams = {
	&UAuracronAbismoUmbrioBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAbismoUmbrioBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.OuterSingleton, Z_Construct_UClass_UAuracronAbismoUmbrioBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAbismoUmbrioBridge);
UAuracronAbismoUmbrioBridge::~UAuracronAbismoUmbrioBridge() {}
// ********** End Class UAuracronAbismoUmbrioBridge ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronUndergroundBiome_StaticEnum, TEXT("EAuracronUndergroundBiome"), &Z_Registration_Info_UEnum_EAuracronUndergroundBiome, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3074728667U) },
		{ EAuracronGeologicalFormation_StaticEnum, TEXT("EAuracronGeologicalFormation"), &Z_Registration_Info_UEnum_EAuracronGeologicalFormation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2900979120U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronCaveGenerationProperties::StaticStruct, Z_Construct_UScriptStruct_FAuracronCaveGenerationProperties_Statics::NewStructOps, TEXT("AuracronCaveGenerationProperties"), &Z_Registration_Info_UScriptStruct_FAuracronCaveGenerationProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCaveGenerationProperties), 1784099713U) },
		{ FAuracronCaveData::StaticStruct, Z_Construct_UScriptStruct_FAuracronCaveData_Statics::NewStructOps, TEXT("AuracronCaveData"), &Z_Registration_Info_UScriptStruct_FAuracronCaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCaveData), 1902154694U) },
		{ FAuracronCaveProperties::StaticStruct, Z_Construct_UScriptStruct_FAuracronCaveProperties_Statics::NewStructOps, TEXT("AuracronCaveProperties"), &Z_Registration_Info_UScriptStruct_FAuracronCaveProperties, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCaveProperties), 3857113942U) },
		{ FAuracronUndergroundLighting::StaticStruct, Z_Construct_UScriptStruct_FAuracronUndergroundLighting_Statics::NewStructOps, TEXT("AuracronUndergroundLighting"), &Z_Registration_Info_UScriptStruct_FAuracronUndergroundLighting, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronUndergroundLighting), 4010356376U) },
		{ FAuracronGeologicalFormationConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronGeologicalFormationConfig_Statics::NewStructOps, TEXT("AuracronGeologicalFormationConfig"), &Z_Registration_Info_UScriptStruct_FAuracronGeologicalFormationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGeologicalFormationConfig), 2405762441U) },
		{ FAuracronUndergroundBiomeConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronUndergroundBiomeConfig_Statics::NewStructOps, TEXT("AuracronUndergroundBiomeConfig"), &Z_Registration_Info_UScriptStruct_FAuracronUndergroundBiomeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronUndergroundBiomeConfig), 1652372378U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAbismoUmbrioBridge, UAuracronAbismoUmbrioBridge::StaticClass, TEXT("UAuracronAbismoUmbrioBridge"), &Z_Registration_Info_UClass_UAuracronAbismoUmbrioBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAbismoUmbrioBridge), 705177985U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_4208487948(TEXT("/Script/AuracronAbismoUmbrioBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAbismoUmbrioBridge_Public_AuracronAbismoUmbrioBridge_h__Script_AuracronAbismoUmbrioBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
