// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliage.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliage() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageInstanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageTypeData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
FOLIAGE_API UClass* Z_Construct_UClass_UFoliageType_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronFoliagePlacementMode *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode;
static UEnum* EAuracronFoliagePlacementMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliagePlacementMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliagePlacementMode>()
{
	return EAuracronFoliagePlacementMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage placement modes\n" },
#endif
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronFoliagePlacementMode::Hybrid" },
		{ "Manual.DisplayName", "Manual" },
		{ "Manual.Name", "EAuracronFoliagePlacementMode::Manual" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
		{ "PCGDriven.DisplayName", "PCG Driven" },
		{ "PCGDriven.Name", "EAuracronFoliagePlacementMode::PCGDriven" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EAuracronFoliagePlacementMode::Procedural" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage placement modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliagePlacementMode::Manual", (int64)EAuracronFoliagePlacementMode::Manual },
		{ "EAuracronFoliagePlacementMode::Procedural", (int64)EAuracronFoliagePlacementMode::Procedural },
		{ "EAuracronFoliagePlacementMode::Hybrid", (int64)EAuracronFoliagePlacementMode::Hybrid },
		{ "EAuracronFoliagePlacementMode::PCGDriven", (int64)EAuracronFoliagePlacementMode::PCGDriven },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliagePlacementMode",
	"EAuracronFoliagePlacementMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode.InnerSingleton;
}
// ********** End Enum EAuracronFoliagePlacementMode ***********************************************

// ********** Begin Enum EAuracronFoliageDensityMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageDensityMode;
static UEnum* EAuracronFoliageDensityMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageDensityMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageDensityMode>()
{
	return EAuracronFoliageDensityMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Biome.DisplayName", "Biome Based" },
		{ "Biome.Name", "EAuracronFoliageDensityMode::Biome" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage density modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageDensityMode::Custom" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
		{ "Noise.DisplayName", "Noise Based" },
		{ "Noise.Name", "EAuracronFoliageDensityMode::Noise" },
		{ "Texture.DisplayName", "Texture Based" },
		{ "Texture.Name", "EAuracronFoliageDensityMode::Texture" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage density modes" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronFoliageDensityMode::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageDensityMode::Uniform", (int64)EAuracronFoliageDensityMode::Uniform },
		{ "EAuracronFoliageDensityMode::Noise", (int64)EAuracronFoliageDensityMode::Noise },
		{ "EAuracronFoliageDensityMode::Texture", (int64)EAuracronFoliageDensityMode::Texture },
		{ "EAuracronFoliageDensityMode::Biome", (int64)EAuracronFoliageDensityMode::Biome },
		{ "EAuracronFoliageDensityMode::Custom", (int64)EAuracronFoliageDensityMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageDensityMode",
	"EAuracronFoliageDensityMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageDensityMode.InnerSingleton;
}
// ********** End Enum EAuracronFoliageDensityMode *************************************************

// ********** Begin Enum EAuracronFoliageScalingMode ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageScalingMode;
static UEnum* EAuracronFoliageScalingMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageScalingMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageScalingMode>()
{
	return EAuracronFoliageScalingMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage scaling modes\n" },
#endif
		{ "Free.DisplayName", "Free" },
		{ "Free.Name", "EAuracronFoliageScalingMode::Free" },
		{ "LockXY.DisplayName", "Lock XY" },
		{ "LockXY.Name", "EAuracronFoliageScalingMode::LockXY" },
		{ "LockXZ.DisplayName", "Lock XZ" },
		{ "LockXZ.Name", "EAuracronFoliageScalingMode::LockXZ" },
		{ "LockYZ.DisplayName", "Lock YZ" },
		{ "LockYZ.Name", "EAuracronFoliageScalingMode::LockYZ" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage scaling modes" },
#endif
		{ "Uniform.DisplayName", "Uniform" },
		{ "Uniform.Name", "EAuracronFoliageScalingMode::Uniform" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageScalingMode::Uniform", (int64)EAuracronFoliageScalingMode::Uniform },
		{ "EAuracronFoliageScalingMode::Free", (int64)EAuracronFoliageScalingMode::Free },
		{ "EAuracronFoliageScalingMode::LockXY", (int64)EAuracronFoliageScalingMode::LockXY },
		{ "EAuracronFoliageScalingMode::LockXZ", (int64)EAuracronFoliageScalingMode::LockXZ },
		{ "EAuracronFoliageScalingMode::LockYZ", (int64)EAuracronFoliageScalingMode::LockYZ },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageScalingMode",
	"EAuracronFoliageScalingMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageScalingMode.InnerSingleton;
}
// ********** End Enum EAuracronFoliageScalingMode *************************************************

// ********** Begin Enum EAuracronFoliageInstanceState *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageInstanceState;
static UEnum* EAuracronFoliageInstanceState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageInstanceState"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageInstanceState>()
{
	return EAuracronFoliageInstanceState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronFoliageInstanceState::Active" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage instance states\n" },
#endif
		{ "Culled.DisplayName", "Culled" },
		{ "Culled.Name", "EAuracronFoliageInstanceState::Culled" },
		{ "Destroyed.DisplayName", "Destroyed" },
		{ "Destroyed.Name", "EAuracronFoliageInstanceState::Destroyed" },
		{ "Hidden.DisplayName", "Hidden" },
		{ "Hidden.Name", "EAuracronFoliageInstanceState::Hidden" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EAuracronFoliageInstanceState::Pending" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage instance states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageInstanceState::Active", (int64)EAuracronFoliageInstanceState::Active },
		{ "EAuracronFoliageInstanceState::Hidden", (int64)EAuracronFoliageInstanceState::Hidden },
		{ "EAuracronFoliageInstanceState::Culled", (int64)EAuracronFoliageInstanceState::Culled },
		{ "EAuracronFoliageInstanceState::Destroyed", (int64)EAuracronFoliageInstanceState::Destroyed },
		{ "EAuracronFoliageInstanceState::Pending", (int64)EAuracronFoliageInstanceState::Pending },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageInstanceState",
	"EAuracronFoliageInstanceState",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageInstanceState.InnerSingleton;
}
// ********** End Enum EAuracronFoliageInstanceState ***********************************************

// ********** Begin Enum EAuracronFoliageLODMode ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageLODMode;
static UEnum* EAuracronFoliageLODMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLODMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageLODMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageLODMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLODMode.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLODMode>()
{
	return EAuracronFoliageLODMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage LOD modes\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageLODMode::Custom" },
		{ "Distance.DisplayName", "Distance Based" },
		{ "Distance.Name", "EAuracronFoliageLODMode::Distance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
		{ "Performance.DisplayName", "Performance Based" },
		{ "Performance.Name", "EAuracronFoliageLODMode::Performance" },
		{ "Quality.DisplayName", "Quality Based" },
		{ "Quality.Name", "EAuracronFoliageLODMode::Quality" },
		{ "ScreenSize.DisplayName", "Screen Size Based" },
		{ "ScreenSize.Name", "EAuracronFoliageLODMode::ScreenSize" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage LOD modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageLODMode::Distance", (int64)EAuracronFoliageLODMode::Distance },
		{ "EAuracronFoliageLODMode::ScreenSize", (int64)EAuracronFoliageLODMode::ScreenSize },
		{ "EAuracronFoliageLODMode::Performance", (int64)EAuracronFoliageLODMode::Performance },
		{ "EAuracronFoliageLODMode::Quality", (int64)EAuracronFoliageLODMode::Quality },
		{ "EAuracronFoliageLODMode::Custom", (int64)EAuracronFoliageLODMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageLODMode",
	"EAuracronFoliageLODMode",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLODMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageLODMode.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLODMode.InnerSingleton;
}
// ********** End Enum EAuracronFoliageLODMode *****************************************************

// ********** Begin ScriptStruct FAuracronFoliageConfiguration *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration;
class UScriptStruct* FAuracronFoliageConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Configuration\n * Configuration settings for the Auracron Foliage System\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Configuration\nConfiguration settings for the Auracron Foliage System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFoliageSystem_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProceduralGeneration_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODSystem_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCulling_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPlacementMode_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDensityMode_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultScalingMode_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLODMode_MetaData[] = {
		{ "Category", "Foliage" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerType_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentSpawns_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance0_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance1_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistance2_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultDensity_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSpacing_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSpacing_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinScale_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxScale_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRotationAngle_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToNormal_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlignToNormalStrength_MetaData[] = {
		{ "Category", "Generation" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInstancing_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBatching_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOcclusion_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceBatchSize_MetaData[] = {
		{ "Category", "Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInstanceBounds_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowDensityMap_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableFoliageSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFoliageSystem;
	static void NewProp_bEnableProceduralGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProceduralGeneration;
	static void NewProp_bEnableLODSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODSystem;
	static void NewProp_bEnableCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCulling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultPlacementMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultPlacementMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultDensityMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultDensityMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultScalingMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultScalingMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultLODMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultLODMode;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentSpawns;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance0;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance1;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistance2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSpacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MinScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MaxScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRotationAngle;
	static void NewProp_bAlignToNormal_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToNormal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AlignToNormalStrength;
	static void NewProp_bEnableInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInstancing;
	static void NewProp_bEnableBatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBatching;
	static void NewProp_bEnableOcclusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOcclusion;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InstanceBatchSize;
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bShowInstanceBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInstanceBounds;
	static void NewProp_bShowDensityMap_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowDensityMap;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableFoliageSystem_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableFoliageSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableFoliageSystem = { "bEnableFoliageSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableFoliageSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFoliageSystem_MetaData), NewProp_bEnableFoliageSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableProceduralGeneration_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableProceduralGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableProceduralGeneration = { "bEnableProceduralGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableProceduralGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProceduralGeneration_MetaData), NewProp_bEnableProceduralGeneration_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableLODSystem_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableLODSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableLODSystem = { "bEnableLODSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableLODSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODSystem_MetaData), NewProp_bEnableLODSystem_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableCulling_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableCulling = { "bEnableCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCulling_MetaData), NewProp_bEnableCulling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultPlacementMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultPlacementMode = { "DefaultPlacementMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, DefaultPlacementMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPlacementMode_MetaData), NewProp_DefaultPlacementMode_MetaData) }; // 155988664
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensityMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensityMode = { "DefaultDensityMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, DefaultDensityMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDensityMode_MetaData), NewProp_DefaultDensityMode_MetaData) }; // 488399728
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultScalingMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultScalingMode = { "DefaultScalingMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, DefaultScalingMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageScalingMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultScalingMode_MetaData), NewProp_DefaultScalingMode_MetaData) }; // 4126854976
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultLODMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultLODMode = { "DefaultLODMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, DefaultLODMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLODMode_MetaData), NewProp_DefaultLODMode_MetaData) }; // 1211770979
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxInstancesPerType = { "MaxInstancesPerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MaxInstancesPerType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerType_MetaData), NewProp_MaxInstancesPerType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxConcurrentSpawns = { "MaxConcurrentSpawns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MaxConcurrentSpawns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentSpawns_MetaData), NewProp_MaxConcurrentSpawns_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance0 = { "LODDistance0", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, LODDistance0), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance0_MetaData), NewProp_LODDistance0_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance1 = { "LODDistance1", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, LODDistance1), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance1_MetaData), NewProp_LODDistance1_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance2 = { "LODDistance2", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, LODDistance2), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistance2_MetaData), NewProp_LODDistance2_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensity = { "DefaultDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, DefaultDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultDensity_MetaData), NewProp_DefaultDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MinSpacing = { "MinSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MinSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSpacing_MetaData), NewProp_MinSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxSpacing = { "MaxSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MaxSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSpacing_MetaData), NewProp_MaxSpacing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MinScale = { "MinScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MinScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinScale_MetaData), NewProp_MinScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxScale = { "MaxScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MaxScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxScale_MetaData), NewProp_MaxScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxRotationAngle = { "MaxRotationAngle", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, MaxRotationAngle), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRotationAngle_MetaData), NewProp_MaxRotationAngle_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bAlignToNormal_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bAlignToNormal = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bAlignToNormal = { "bAlignToNormal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bAlignToNormal_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToNormal_MetaData), NewProp_bAlignToNormal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_AlignToNormalStrength = { "AlignToNormalStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, AlignToNormalStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlignToNormalStrength_MetaData), NewProp_AlignToNormalStrength_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableInstancing_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableInstancing = { "bEnableInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInstancing_MetaData), NewProp_bEnableInstancing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableBatching_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableBatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableBatching = { "bEnableBatching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableBatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBatching_MetaData), NewProp_bEnableBatching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableOcclusion_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableOcclusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableOcclusion = { "bEnableOcclusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableOcclusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOcclusion_MetaData), NewProp_bEnableOcclusion_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_InstanceBatchSize = { "InstanceBatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageConfiguration, InstanceBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceBatchSize_MetaData), NewProp_InstanceBatchSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowInstanceBounds_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bShowInstanceBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowInstanceBounds = { "bShowInstanceBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowInstanceBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInstanceBounds_MetaData), NewProp_bShowInstanceBounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowDensityMap_SetBit(void* Obj)
{
	((FAuracronFoliageConfiguration*)Obj)->bShowDensityMap = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowDensityMap = { "bShowDensityMap", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowDensityMap_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowDensityMap_MetaData), NewProp_bShowDensityMap_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableFoliageSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableProceduralGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableLODSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultPlacementMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultPlacementMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensityMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensityMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultScalingMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultScalingMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultLODMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultLODMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxInstancesPerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxConcurrentSpawns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance0,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_LODDistance2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_DefaultDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MinSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MinScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_MaxRotationAngle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bAlignToNormal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_AlignToNormalStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableBatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableOcclusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_InstanceBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowInstanceBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewProp_bShowDensityMap,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageConfiguration),
	alignof(FAuracronFoliageConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageConfiguration ***************************************

// ********** Begin ScriptStruct FAuracronFoliageInstanceData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData;
class UScriptStruct* FAuracronFoliageInstanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageInstanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Instance Data\n * Data structure for individual foliage instances\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Instance Data\nData structure for individual foliage instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODLevel_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToViewer_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCulled_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToViewer;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_bIsCulled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCulled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageInstanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, State), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageInstanceState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 2715996281
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, LODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODLevel_MetaData), NewProp_LODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_DistanceToViewer = { "DistanceToViewer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, DistanceToViewer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToViewer_MetaData), NewProp_DistanceToViewer_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuracronFoliageInstanceData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInstanceData), &Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsCulled_SetBit(void* Obj)
{
	((FAuracronFoliageInstanceData*)Obj)->bIsCulled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsCulled = { "bIsCulled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageInstanceData), &Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsCulled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCulled_MetaData), NewProp_bIsCulled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageInstanceData, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_DistanceToViewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_bIsCulled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageInstanceData",
	Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::PropPointers),
	sizeof(FAuracronFoliageInstanceData),
	alignof(FAuracronFoliageInstanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageInstanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageInstanceData ****************************************

// ********** Begin ScriptStruct FAuracronFoliageTypeData ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData;
class UScriptStruct* FAuracronFoliageTypeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageTypeData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageTypeData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage Type Data\n * Extended data structure for foliage types\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage Type Data\nExtended data structure for foliage types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeName_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StaticMesh_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageType_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementMode_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DensityMode_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Density_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeMinSpacing_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeMaxSpacing_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeMinScale_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeMaxScale_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnabled_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstances_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instances_MetaData[] = {
		{ "Category", "Foliage Type" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_StaticMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FoliageType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlacementMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlacementMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DensityMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DensityMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TypeMinSpacing;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TypeMaxSpacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TypeMinScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TypeMaxScale;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstances;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Instances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Instances;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageTypeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeName = { "TypeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeName_MetaData), NewProp_TypeName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_StaticMesh = { "StaticMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, StaticMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StaticMesh_MetaData), NewProp_StaticMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_FoliageType = { "FoliageType", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, FoliageType), Z_Construct_UClass_UFoliageType_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageType_MetaData), NewProp_FoliageType_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_PlacementMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_PlacementMode = { "PlacementMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, PlacementMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliagePlacementMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementMode_MetaData), NewProp_PlacementMode_MetaData) }; // 155988664
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_DensityMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_DensityMode = { "DensityMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, DensityMode), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageDensityMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DensityMode_MetaData), NewProp_DensityMode_MetaData) }; // 488399728
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, Density), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Density_MetaData), NewProp_Density_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMinSpacing = { "TypeMinSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeMinSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeMinSpacing_MetaData), NewProp_TypeMinSpacing_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMaxSpacing = { "TypeMaxSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeMaxSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeMaxSpacing_MetaData), NewProp_TypeMaxSpacing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMinScale = { "TypeMinScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeMinScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeMinScale_MetaData), NewProp_TypeMinScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMaxScale = { "TypeMaxScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, TypeMaxScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeMaxScale_MetaData), NewProp_TypeMaxScale_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((FAuracronFoliageTypeData*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageTypeData), &Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnabled_MetaData), NewProp_bEnabled_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_MaxInstances = { "MaxInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, MaxInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstances_MetaData), NewProp_MaxInstances_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Instances_Inner = { "Instances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Instances = { "Instances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageTypeData, Instances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instances_MetaData), NewProp_Instances_MetaData) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_StaticMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_FoliageType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_PlacementMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_PlacementMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_DensityMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_DensityMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMinSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMaxSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMinScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_TypeMaxScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_bEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_MaxInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Instances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewProp_Instances,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageTypeData",
	Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::PropPointers),
	sizeof(FAuracronFoliageTypeData),
	alignof(FAuracronFoliageTypeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageTypeData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageTypeData ********************************************

// ********** Begin Delegate FOnFoliageInstanceSpawned *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics
{
	struct AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms
	{
		FString TypeId;
		FString InstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms, TypeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::NewProp_InstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "OnFoliageInstanceSpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageManager::FOnFoliageInstanceSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageInstanceSpawned, const FString& TypeId, const FString& InstanceId)
{
	struct AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms
	{
		FString TypeId;
		FString InstanceId;
	};
	AuracronFoliageManager_eventOnFoliageInstanceSpawned_Parms Parms;
	Parms.TypeId=TypeId;
	Parms.InstanceId=InstanceId;
	OnFoliageInstanceSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageInstanceSpawned ***********************************************

// ********** Begin Delegate FOnFoliageInstanceRemoved *********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics
{
	struct AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms
	{
		FString TypeId;
		FString InstanceId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms, TypeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::NewProp_InstanceId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "OnFoliageInstanceRemoved__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageManager::FOnFoliageInstanceRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageInstanceRemoved, const FString& TypeId, const FString& InstanceId)
{
	struct AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms
	{
		FString TypeId;
		FString InstanceId;
	};
	AuracronFoliageManager_eventOnFoliageInstanceRemoved_Parms Parms;
	Parms.TypeId=TypeId;
	Parms.InstanceId=InstanceId;
	OnFoliageInstanceRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageInstanceRemoved ***********************************************

// ********** Begin Delegate FOnFoliageTypeRegistered **********************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics
{
	struct AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms
	{
		FString TypeId;
		FAuracronFoliageTypeData TypeData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TypeData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms, TypeId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::NewProp_TypeData = { "TypeData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms, TypeData), Z_Construct_UScriptStruct_FAuracronFoliageTypeData, METADATA_PARAMS(0, nullptr) }; // 3005380608
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::NewProp_TypeData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "OnFoliageTypeRegistered__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageManager::FOnFoliageTypeRegistered_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageTypeRegistered, const FString& TypeId, FAuracronFoliageTypeData TypeData)
{
	struct AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms
	{
		FString TypeId;
		FAuracronFoliageTypeData TypeData;
	};
	AuracronFoliageManager_eventOnFoliageTypeRegistered_Parms Parms;
	Parms.TypeId=TypeId;
	Parms.TypeData=TypeData;
	OnFoliageTypeRegistered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageTypeRegistered ************************************************

// ********** Begin Delegate FOnFoliageOptimized ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics
{
	struct AuracronFoliageManager_eventOnFoliageOptimized_Parms
	{
		int32 OptimizedInstances;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_OptimizedInstances;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::NewProp_OptimizedInstances = { "OptimizedInstances", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventOnFoliageOptimized_Parms, OptimizedInstances), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::NewProp_OptimizedInstances,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "OnFoliageOptimized__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::AuracronFoliageManager_eventOnFoliageOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageManager::FOnFoliageOptimized_DelegateWrapper(const FMulticastScriptDelegate& OnFoliageOptimized, int32 OptimizedInstances)
{
	struct AuracronFoliageManager_eventOnFoliageOptimized_Parms
	{
		int32 OptimizedInstances;
	};
	AuracronFoliageManager_eventOnFoliageOptimized_Parms Parms;
	Parms.OptimizedInstances=OptimizedInstances;
	OnFoliageOptimized.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnFoliageOptimized *****************************************************

// ********** Begin Class UAuracronFoliageManager Function BatchInstances **************************
struct Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "BatchInstances", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execBatchInstances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BatchInstances();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function BatchInstances ****************************

// ********** Begin Class UAuracronFoliageManager Function ClearAllFoliage *************************
struct Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "ClearAllFoliage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execClearAllFoliage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllFoliage();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function ClearAllFoliage ***************************

// ********** Begin Class UAuracronFoliageManager Function ClearFoliageInArea **********************
struct Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics
{
	struct AuracronFoliageManager_eventClearFoliageInArea_Parms
	{
		FBox Area;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventClearFoliageInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::NewProp_Area,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "ClearFoliageInArea", Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::AuracronFoliageManager_eventClearFoliageInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::AuracronFoliageManager_eventClearFoliageInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execClearFoliageInArea)
{
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearFoliageInArea(Z_Param_Out_Area);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function ClearFoliageInArea ************************

// ********** Begin Class UAuracronFoliageManager Function DrawDebugVisualization ******************
struct Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics
{
	struct AuracronFoliageManager_eventDrawDebugVisualization_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventDrawDebugVisualization_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "DrawDebugVisualization", Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::AuracronFoliageManager_eventDrawDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::AuracronFoliageManager_eventDrawDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execDrawDebugVisualization)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugVisualization(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function DrawDebugVisualization ********************

// ********** Begin Class UAuracronFoliageManager Function EnableDebugVisualization ****************
struct Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::AuracronFoliageManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::AuracronFoliageManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function EnableDebugVisualization ******************

// ********** Begin Class UAuracronFoliageManager Function GenerateFoliageInArea *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics
{
	struct AuracronFoliageManager_eventGenerateFoliageInArea_Parms
	{
		FString TypeId;
		FBox Area;
		float Density;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Procedural generation\n" },
#endif
		{ "CPP_Default_Density", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Area_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Area;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageInArea_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_Area = { "Area", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageInArea_Parms, Area), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Area_MetaData), NewProp_Area_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageInArea_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageInArea_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_Area,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GenerateFoliageInArea", Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::AuracronFoliageManager_eventGenerateFoliageInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::AuracronFoliageManager_eventGenerateFoliageInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGenerateFoliageInArea)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_GET_STRUCT_REF(FBox,Z_Param_Out_Area);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GenerateFoliageInArea(Z_Param_TypeId,Z_Param_Out_Area,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GenerateFoliageInArea *********************

// ********** Begin Class UAuracronFoliageManager Function GenerateFoliageOnSurface ****************
struct Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics
{
	struct AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms
	{
		FString TypeId;
		UPrimitiveComponent* Surface;
		float Density;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "CPP_Default_Density", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Surface_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Surface;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_Surface = { "Surface", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms, Surface), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Surface_MetaData), NewProp_Surface_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_Surface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GenerateFoliageOnSurface", Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::AuracronFoliageManager_eventGenerateFoliageOnSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGenerateFoliageOnSurface)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Surface);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GenerateFoliageOnSurface(Z_Param_TypeId,Z_Param_Surface,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GenerateFoliageOnSurface ******************

// ********** Begin Class UAuracronFoliageManager Function GetAllFoliageTypes **********************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics
{
	struct AuracronFoliageManager_eventGetAllFoliageTypes_Parms
	{
		TArray<FAuracronFoliageTypeData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageTypeData, METADATA_PARAMS(0, nullptr) }; // 3005380608
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetAllFoliageTypes_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3005380608
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetAllFoliageTypes", Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::AuracronFoliageManager_eventGetAllFoliageTypes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::AuracronFoliageManager_eventGetAllFoliageTypes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetAllFoliageTypes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageTypeData>*)Z_Param__Result=P_THIS->GetAllFoliageTypes();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetAllFoliageTypes ************************

// ********** Begin Class UAuracronFoliageManager Function GetConfiguration ************************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics
{
	struct AuracronFoliageManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageConfiguration, METADATA_PARAMS(0, nullptr) }; // 4148579787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::AuracronFoliageManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::AuracronFoliageManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetConfiguration **************************

// ********** Begin Class UAuracronFoliageManager Function GetFoliageInstance **********************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics
{
	struct AuracronFoliageManager_eventGetFoliageInstance_Parms
	{
		FString InstanceId;
		FAuracronFoliageInstanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetFoliageInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetFoliageInstance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetFoliageInstance", Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::AuracronFoliageManager_eventGetFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::AuracronFoliageManager_eventGetFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetFoliageInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageInstanceData*)Z_Param__Result=P_THIS->GetFoliageInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetFoliageInstance ************************

// ********** Begin Class UAuracronFoliageManager Function GetFoliageType **************************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics
{
	struct AuracronFoliageManager_eventGetFoliageType_Parms
	{
		FString TypeId;
		FAuracronFoliageTypeData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetFoliageType_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetFoliageType_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageTypeData, METADATA_PARAMS(0, nullptr) }; // 3005380608
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetFoliageType", Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::AuracronFoliageManager_eventGetFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::AuracronFoliageManager_eventGetFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetFoliageType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageTypeData*)Z_Param__Result=P_THIS->GetFoliageType(Z_Param_TypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetFoliageType ****************************

// ********** Begin Class UAuracronFoliageManager Function GetInstance *****************************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics
{
	struct AuracronFoliageManager_eventGetInstance_Parms
	{
		UAuracronFoliageManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::AuracronFoliageManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::AuracronFoliageManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageManager**)Z_Param__Result=UAuracronFoliageManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetInstance *******************************

// ********** Begin Class UAuracronFoliageManager Function GetInstanceCountForType *****************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics
{
	struct AuracronFoliageManager_eventGetInstanceCountForType_Parms
	{
		FString TypeId;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstanceCountForType_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstanceCountForType_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetInstanceCountForType", Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::AuracronFoliageManager_eventGetInstanceCountForType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::AuracronFoliageManager_eventGetInstanceCountForType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetInstanceCountForType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetInstanceCountForType(Z_Param_TypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetInstanceCountForType *******************

// ********** Begin Class UAuracronFoliageManager Function GetInstancesInRadius ********************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics
{
	struct AuracronFoliageManager_eventGetInstancesInRadius_Parms
	{
		FVector Center;
		float Radius;
		TArray<FAuracronFoliageInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstancesInRadius_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstancesInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstancesInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetInstancesInRadius", Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::AuracronFoliageManager_eventGetInstancesInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::AuracronFoliageManager_eventGetInstancesInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetInstancesInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageInstanceData>*)Z_Param__Result=P_THIS->GetInstancesInRadius(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetInstancesInRadius **********************

// ********** Begin Class UAuracronFoliageManager Function GetInstancesOfType **********************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics
{
	struct AuracronFoliageManager_eventGetInstancesOfType_Parms
	{
		FString TypeId;
		TArray<FAuracronFoliageInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstancesOfType_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstancesOfType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1168665060
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetInstancesOfType", Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::AuracronFoliageManager_eventGetInstancesOfType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::AuracronFoliageManager_eventGetInstancesOfType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetInstancesOfType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronFoliageInstanceData>*)Z_Param__Result=P_THIS->GetInstancesOfType(Z_Param_TypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetInstancesOfType ************************

// ********** Begin Class UAuracronFoliageManager Function GetInstanceStatistics *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics
{
	struct AuracronFoliageManager_eventGetInstanceStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetInstanceStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetInstanceStatistics", Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::AuracronFoliageManager_eventGetInstanceStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::AuracronFoliageManager_eventGetInstanceStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetInstanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetInstanceStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetInstanceStatistics *********************

// ********** Begin Class UAuracronFoliageManager Function GetMemoryUsageMB ************************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics
{
	struct AuracronFoliageManager_eventGetMemoryUsageMB_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetMemoryUsageMB", Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::AuracronFoliageManager_eventGetMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::AuracronFoliageManager_eventGetMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMemoryUsageMB();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetMemoryUsageMB **************************

// ********** Begin Class UAuracronFoliageManager Function GetTotalInstanceCount *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics
{
	struct AuracronFoliageManager_eventGetTotalInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and debugging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and debugging" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetTotalInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetTotalInstanceCount", Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::AuracronFoliageManager_eventGetTotalInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::AuracronFoliageManager_eventGetTotalInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetTotalInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetTotalInstanceCount *********************

// ********** Begin Class UAuracronFoliageManager Function GetVisibleInstanceCount *****************
struct Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics
{
	struct AuracronFoliageManager_eventGetVisibleInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventGetVisibleInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "GetVisibleInstanceCount", Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::AuracronFoliageManager_eventGetVisibleInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::AuracronFoliageManager_eventGetVisibleInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execGetVisibleInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetVisibleInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function GetVisibleInstanceCount *******************

// ********** Begin Class UAuracronFoliageManager Function Initialize ******************************
struct Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics
{
	struct AuracronFoliageManager_eventInitialize_Parms
	{
		FAuracronFoliageConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4148579787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::AuracronFoliageManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::AuracronFoliageManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function Initialize ********************************

// ********** Begin Class UAuracronFoliageManager Function IsDebugVisualizationEnabled *************
struct Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function IsDebugVisualizationEnabled ***************

// ********** Begin Class UAuracronFoliageManager Function IsInitialized ***************************
struct Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics
{
	struct AuracronFoliageManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::AuracronFoliageManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::AuracronFoliageManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function IsInitialized *****************************

// ********** Begin Class UAuracronFoliageManager Function OptimizeInstances ***********************
struct Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "OptimizeInstances", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execOptimizeInstances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeInstances();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function OptimizeInstances *************************

// ********** Begin Class UAuracronFoliageManager Function RebuildInstancedComponents **************
struct Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "RebuildInstancedComponents", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execRebuildInstancedComponents)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RebuildInstancedComponents();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function RebuildInstancedComponents ****************

// ********** Begin Class UAuracronFoliageManager Function RegisterFoliageType *********************
struct Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics
{
	struct AuracronFoliageManager_eventRegisterFoliageType_Parms
	{
		FAuracronFoliageTypeData FoliageTypeData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Foliage type management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage type management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FoliageTypeData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_FoliageTypeData = { "FoliageTypeData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventRegisterFoliageType_Parms, FoliageTypeData), Z_Construct_UScriptStruct_FAuracronFoliageTypeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeData_MetaData), NewProp_FoliageTypeData_MetaData) }; // 3005380608
void Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventRegisterFoliageType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventRegisterFoliageType_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_FoliageTypeData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "RegisterFoliageType", Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::AuracronFoliageManager_eventRegisterFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::AuracronFoliageManager_eventRegisterFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execRegisterFoliageType)
{
	P_GET_STRUCT_REF(FAuracronFoliageTypeData,Z_Param_Out_FoliageTypeData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterFoliageType(Z_Param_Out_FoliageTypeData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function RegisterFoliageType ***********************

// ********** Begin Class UAuracronFoliageManager Function RemoveFoliageInstance *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics
{
	struct AuracronFoliageManager_eventRemoveFoliageInstance_Parms
	{
		FString InstanceId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventRemoveFoliageInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventRemoveFoliageInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventRemoveFoliageInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "RemoveFoliageInstance", Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::AuracronFoliageManager_eventRemoveFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::AuracronFoliageManager_eventRemoveFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execRemoveFoliageInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveFoliageInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function RemoveFoliageInstance *********************

// ********** Begin Class UAuracronFoliageManager Function SetConfiguration ************************
struct Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics
{
	struct AuracronFoliageManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4148579787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::AuracronFoliageManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::AuracronFoliageManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function SetConfiguration **************************

// ********** Begin Class UAuracronFoliageManager Function SetInstanceLOD **************************
struct Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics
{
	struct AuracronFoliageManager_eventSetInstanceLOD_Parms
	{
		FString InstanceId;
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSetInstanceLOD_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSetInstanceLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "SetInstanceLOD", Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::AuracronFoliageManager_eventSetInstanceLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::AuracronFoliageManager_eventSetInstanceLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execSetInstanceLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetInstanceLOD(Z_Param_InstanceId,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function SetInstanceLOD ****************************

// ********** Begin Class UAuracronFoliageManager Function SetInstanceVisibility *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics
{
	struct AuracronFoliageManager_eventSetInstanceVisibility_Parms
	{
		FString InstanceId;
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSetInstanceVisibility_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventSetInstanceVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventSetInstanceVisibility_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "SetInstanceVisibility", Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::AuracronFoliageManager_eventSetInstanceVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::AuracronFoliageManager_eventSetInstanceVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execSetInstanceVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetInstanceVisibility(Z_Param_InstanceId,Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function SetInstanceVisibility *********************

// ********** Begin Class UAuracronFoliageManager Function Shutdown ********************************
struct Z_Construct_UFunction_UAuracronFoliageManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function Shutdown **********************************

// ********** Begin Class UAuracronFoliageManager Function SpawnFoliageInstance ********************
struct Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics
{
	struct AuracronFoliageManager_eventSpawnFoliageInstance_Parms
	{
		FString TypeId;
		FTransform Transform;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transform_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Transform;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSpawnFoliageInstance_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_Transform = { "Transform", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSpawnFoliageInstance_Parms, Transform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transform_MetaData), NewProp_Transform_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventSpawnFoliageInstance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_Transform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "SpawnFoliageInstance", Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::AuracronFoliageManager_eventSpawnFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::AuracronFoliageManager_eventSpawnFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execSpawnFoliageInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_GET_STRUCT_REF(FTransform,Z_Param_Out_Transform);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->SpawnFoliageInstance(Z_Param_TypeId,Z_Param_Out_Transform);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function SpawnFoliageInstance **********************

// ********** Begin Class UAuracronFoliageManager Function Tick ************************************
struct Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics
{
	struct AuracronFoliageManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::AuracronFoliageManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::AuracronFoliageManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function Tick **************************************

// ********** Begin Class UAuracronFoliageManager Function UnregisterFoliageType *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics
{
	struct AuracronFoliageManager_eventUnregisterFoliageType_Parms
	{
		FString TypeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TypeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_TypeId = { "TypeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventUnregisterFoliageType_Parms, TypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeId_MetaData), NewProp_TypeId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventUnregisterFoliageType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventUnregisterFoliageType_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_TypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "UnregisterFoliageType", Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::AuracronFoliageManager_eventUnregisterFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::AuracronFoliageManager_eventUnregisterFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execUnregisterFoliageType)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TypeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterFoliageType(Z_Param_TypeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function UnregisterFoliageType *********************

// ********** Begin Class UAuracronFoliageManager Function UpdateCulling ***************************
struct Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics
{
	struct AuracronFoliageManager_eventUpdateCulling_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventUpdateCulling_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "UpdateCulling", Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::AuracronFoliageManager_eventUpdateCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::AuracronFoliageManager_eventUpdateCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execUpdateCulling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCulling(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function UpdateCulling *****************************

// ********** Begin Class UAuracronFoliageManager Function UpdateFoliageInstance *******************
struct Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics
{
	struct AuracronFoliageManager_eventUpdateFoliageInstance_Parms
	{
		FAuracronFoliageInstanceData InstanceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_InstanceData = { "InstanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventUpdateFoliageInstance_Parms, InstanceData), Z_Construct_UScriptStruct_FAuracronFoliageInstanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceData_MetaData), NewProp_InstanceData_MetaData) }; // 1168665060
void Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventUpdateFoliageInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventUpdateFoliageInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_InstanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "UpdateFoliageInstance", Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::AuracronFoliageManager_eventUpdateFoliageInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::AuracronFoliageManager_eventUpdateFoliageInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execUpdateFoliageInstance)
{
	P_GET_STRUCT_REF(FAuracronFoliageInstanceData,Z_Param_Out_InstanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateFoliageInstance(Z_Param_Out_InstanceData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function UpdateFoliageInstance *********************

// ********** Begin Class UAuracronFoliageManager Function UpdateFoliageType ***********************
struct Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics
{
	struct AuracronFoliageManager_eventUpdateFoliageType_Parms
	{
		FAuracronFoliageTypeData FoliageTypeData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FoliageTypeData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_FoliageTypeData = { "FoliageTypeData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventUpdateFoliageType_Parms, FoliageTypeData), Z_Construct_UScriptStruct_FAuracronFoliageTypeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeData_MetaData), NewProp_FoliageTypeData_MetaData) }; // 3005380608
void Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageManager_eventUpdateFoliageType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageManager_eventUpdateFoliageType_Parms), &Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_FoliageTypeData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "UpdateFoliageType", Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::AuracronFoliageManager_eventUpdateFoliageType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::AuracronFoliageManager_eventUpdateFoliageType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execUpdateFoliageType)
{
	P_GET_STRUCT_REF(FAuracronFoliageTypeData,Z_Param_Out_FoliageTypeData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateFoliageType(Z_Param_Out_FoliageTypeData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function UpdateFoliageType *************************

// ********** Begin Class UAuracronFoliageManager Function UpdateLOD *******************************
struct Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics
{
	struct AuracronFoliageManager_eventUpdateLOD_Parms
	{
		FVector ViewerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Foliage Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD and culling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD and culling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageManager_eventUpdateLOD_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::NewProp_ViewerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageManager, nullptr, "UpdateLOD", Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::AuracronFoliageManager_eventUpdateLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::AuracronFoliageManager_eventUpdateLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageManager::execUpdateLOD)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLOD(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageManager Function UpdateLOD *********************************

// ********** Begin Class UAuracronFoliageManager **************************************************
void UAuracronFoliageManager::StaticRegisterNativesUAuracronFoliageManager()
{
	UClass* Class = UAuracronFoliageManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BatchInstances", &UAuracronFoliageManager::execBatchInstances },
		{ "ClearAllFoliage", &UAuracronFoliageManager::execClearAllFoliage },
		{ "ClearFoliageInArea", &UAuracronFoliageManager::execClearFoliageInArea },
		{ "DrawDebugVisualization", &UAuracronFoliageManager::execDrawDebugVisualization },
		{ "EnableDebugVisualization", &UAuracronFoliageManager::execEnableDebugVisualization },
		{ "GenerateFoliageInArea", &UAuracronFoliageManager::execGenerateFoliageInArea },
		{ "GenerateFoliageOnSurface", &UAuracronFoliageManager::execGenerateFoliageOnSurface },
		{ "GetAllFoliageTypes", &UAuracronFoliageManager::execGetAllFoliageTypes },
		{ "GetConfiguration", &UAuracronFoliageManager::execGetConfiguration },
		{ "GetFoliageInstance", &UAuracronFoliageManager::execGetFoliageInstance },
		{ "GetFoliageType", &UAuracronFoliageManager::execGetFoliageType },
		{ "GetInstance", &UAuracronFoliageManager::execGetInstance },
		{ "GetInstanceCountForType", &UAuracronFoliageManager::execGetInstanceCountForType },
		{ "GetInstancesInRadius", &UAuracronFoliageManager::execGetInstancesInRadius },
		{ "GetInstancesOfType", &UAuracronFoliageManager::execGetInstancesOfType },
		{ "GetInstanceStatistics", &UAuracronFoliageManager::execGetInstanceStatistics },
		{ "GetMemoryUsageMB", &UAuracronFoliageManager::execGetMemoryUsageMB },
		{ "GetTotalInstanceCount", &UAuracronFoliageManager::execGetTotalInstanceCount },
		{ "GetVisibleInstanceCount", &UAuracronFoliageManager::execGetVisibleInstanceCount },
		{ "Initialize", &UAuracronFoliageManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageManager::execIsInitialized },
		{ "OptimizeInstances", &UAuracronFoliageManager::execOptimizeInstances },
		{ "RebuildInstancedComponents", &UAuracronFoliageManager::execRebuildInstancedComponents },
		{ "RegisterFoliageType", &UAuracronFoliageManager::execRegisterFoliageType },
		{ "RemoveFoliageInstance", &UAuracronFoliageManager::execRemoveFoliageInstance },
		{ "SetConfiguration", &UAuracronFoliageManager::execSetConfiguration },
		{ "SetInstanceLOD", &UAuracronFoliageManager::execSetInstanceLOD },
		{ "SetInstanceVisibility", &UAuracronFoliageManager::execSetInstanceVisibility },
		{ "Shutdown", &UAuracronFoliageManager::execShutdown },
		{ "SpawnFoliageInstance", &UAuracronFoliageManager::execSpawnFoliageInstance },
		{ "Tick", &UAuracronFoliageManager::execTick },
		{ "UnregisterFoliageType", &UAuracronFoliageManager::execUnregisterFoliageType },
		{ "UpdateCulling", &UAuracronFoliageManager::execUpdateCulling },
		{ "UpdateFoliageInstance", &UAuracronFoliageManager::execUpdateFoliageInstance },
		{ "UpdateFoliageType", &UAuracronFoliageManager::execUpdateFoliageType },
		{ "UpdateLOD", &UAuracronFoliageManager::execUpdateLOD },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageManager;
UClass* UAuracronFoliageManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageManager"),
			Z_Registration_Info_UClass_UAuracronFoliageManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageManager_NoRegister()
{
	return UAuracronFoliageManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Foliage Manager\n * Central manager for the Auracron Foliage System\n */" },
#endif
		{ "IncludePath", "AuracronFoliage.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Foliage Manager\nCentral manager for the Auracron Foliage System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageInstanceSpawned_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageInstanceRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageTypeRegistered_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnFoliageOptimized_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliage.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageInstanceSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageInstanceRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageTypeRegistered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnFoliageOptimized;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageManager_BatchInstances, "BatchInstances" }, // 324319450
		{ &Z_Construct_UFunction_UAuracronFoliageManager_ClearAllFoliage, "ClearAllFoliage" }, // 1821069727
		{ &Z_Construct_UFunction_UAuracronFoliageManager_ClearFoliageInArea, "ClearFoliageInArea" }, // 3788467411
		{ &Z_Construct_UFunction_UAuracronFoliageManager_DrawDebugVisualization, "DrawDebugVisualization" }, // 2580287918
		{ &Z_Construct_UFunction_UAuracronFoliageManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 4211931355
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageInArea, "GenerateFoliageInArea" }, // 2525876790
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GenerateFoliageOnSurface, "GenerateFoliageOnSurface" }, // 2428842521
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetAllFoliageTypes, "GetAllFoliageTypes" }, // 3332264491
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetConfiguration, "GetConfiguration" }, // 636967082
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageInstance, "GetFoliageInstance" }, // 1641214075
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetFoliageType, "GetFoliageType" }, // 2088186910
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetInstance, "GetInstance" }, // 2613739974
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceCountForType, "GetInstanceCountForType" }, // 2643850667
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesInRadius, "GetInstancesInRadius" }, // 3439312780
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetInstancesOfType, "GetInstancesOfType" }, // 2503246439
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetInstanceStatistics, "GetInstanceStatistics" }, // 1857253023
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetMemoryUsageMB, "GetMemoryUsageMB" }, // 1673801922
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetTotalInstanceCount, "GetTotalInstanceCount" }, // 3121739138
		{ &Z_Construct_UFunction_UAuracronFoliageManager_GetVisibleInstanceCount, "GetVisibleInstanceCount" }, // 3408159596
		{ &Z_Construct_UFunction_UAuracronFoliageManager_Initialize, "Initialize" }, // 1088648247
		{ &Z_Construct_UFunction_UAuracronFoliageManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 2489444048
		{ &Z_Construct_UFunction_UAuracronFoliageManager_IsInitialized, "IsInitialized" }, // 3764208320
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature, "OnFoliageInstanceRemoved__DelegateSignature" }, // 1229830832
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature, "OnFoliageInstanceSpawned__DelegateSignature" }, // 1176902968
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature, "OnFoliageOptimized__DelegateSignature" }, // 1024282473
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature, "OnFoliageTypeRegistered__DelegateSignature" }, // 339142022
		{ &Z_Construct_UFunction_UAuracronFoliageManager_OptimizeInstances, "OptimizeInstances" }, // 2735005385
		{ &Z_Construct_UFunction_UAuracronFoliageManager_RebuildInstancedComponents, "RebuildInstancedComponents" }, // 3114802900
		{ &Z_Construct_UFunction_UAuracronFoliageManager_RegisterFoliageType, "RegisterFoliageType" }, // 530898032
		{ &Z_Construct_UFunction_UAuracronFoliageManager_RemoveFoliageInstance, "RemoveFoliageInstance" }, // 3287971414
		{ &Z_Construct_UFunction_UAuracronFoliageManager_SetConfiguration, "SetConfiguration" }, // 3927056858
		{ &Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceLOD, "SetInstanceLOD" }, // 2465916137
		{ &Z_Construct_UFunction_UAuracronFoliageManager_SetInstanceVisibility, "SetInstanceVisibility" }, // 3921472088
		{ &Z_Construct_UFunction_UAuracronFoliageManager_Shutdown, "Shutdown" }, // 1654002302
		{ &Z_Construct_UFunction_UAuracronFoliageManager_SpawnFoliageInstance, "SpawnFoliageInstance" }, // 3940962284
		{ &Z_Construct_UFunction_UAuracronFoliageManager_Tick, "Tick" }, // 1345123902
		{ &Z_Construct_UFunction_UAuracronFoliageManager_UnregisterFoliageType, "UnregisterFoliageType" }, // 1724760896
		{ &Z_Construct_UFunction_UAuracronFoliageManager_UpdateCulling, "UpdateCulling" }, // 69359478
		{ &Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageInstance, "UpdateFoliageInstance" }, // 1520613865
		{ &Z_Construct_UFunction_UAuracronFoliageManager_UpdateFoliageType, "UpdateFoliageType" }, // 860981129
		{ &Z_Construct_UFunction_UAuracronFoliageManager_UpdateLOD, "UpdateLOD" }, // 983616679
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageInstanceSpawned = { "OnFoliageInstanceSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, OnFoliageInstanceSpawned), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageInstanceSpawned_MetaData), NewProp_OnFoliageInstanceSpawned_MetaData) }; // 1176902968
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageInstanceRemoved = { "OnFoliageInstanceRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, OnFoliageInstanceRemoved), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageInstanceRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageInstanceRemoved_MetaData), NewProp_OnFoliageInstanceRemoved_MetaData) }; // 1229830832
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageTypeRegistered = { "OnFoliageTypeRegistered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, OnFoliageTypeRegistered), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageTypeRegistered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageTypeRegistered_MetaData), NewProp_OnFoliageTypeRegistered_MetaData) }; // 339142022
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageOptimized = { "OnFoliageOptimized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, OnFoliageOptimized), Z_Construct_UDelegateFunction_UAuracronFoliageManager_OnFoliageOptimized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnFoliageOptimized_MetaData), NewProp_OnFoliageOptimized_MetaData) }; // 1024282473
void Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageManager), &Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4148579787
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageInstanceSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageInstanceRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageTypeRegistered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_OnFoliageOptimized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageManager_Statics::ClassParams = {
	&UAuracronFoliageManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageManager.OuterSingleton;
}
UAuracronFoliageManager::UAuracronFoliageManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageManager);
UAuracronFoliageManager::~UAuracronFoliageManager() {}
// ********** End Class UAuracronFoliageManager ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronFoliagePlacementMode_StaticEnum, TEXT("EAuracronFoliagePlacementMode"), &Z_Registration_Info_UEnum_EAuracronFoliagePlacementMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 155988664U) },
		{ EAuracronFoliageDensityMode_StaticEnum, TEXT("EAuracronFoliageDensityMode"), &Z_Registration_Info_UEnum_EAuracronFoliageDensityMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 488399728U) },
		{ EAuracronFoliageScalingMode_StaticEnum, TEXT("EAuracronFoliageScalingMode"), &Z_Registration_Info_UEnum_EAuracronFoliageScalingMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4126854976U) },
		{ EAuracronFoliageInstanceState_StaticEnum, TEXT("EAuracronFoliageInstanceState"), &Z_Registration_Info_UEnum_EAuracronFoliageInstanceState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2715996281U) },
		{ EAuracronFoliageLODMode_StaticEnum, TEXT("EAuracronFoliageLODMode"), &Z_Registration_Info_UEnum_EAuracronFoliageLODMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1211770979U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageConfiguration), 4148579787U) },
		{ FAuracronFoliageInstanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageInstanceData_Statics::NewStructOps, TEXT("AuracronFoliageInstanceData"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageInstanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageInstanceData), 1168665060U) },
		{ FAuracronFoliageTypeData::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageTypeData_Statics::NewStructOps, TEXT("AuracronFoliageTypeData"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageTypeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageTypeData), 3005380608U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageManager, UAuracronFoliageManager::StaticClass, TEXT("UAuracronFoliageManager"), &Z_Registration_Info_UClass_UAuracronFoliageManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageManager), 3357236771U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_301843452(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliage_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
