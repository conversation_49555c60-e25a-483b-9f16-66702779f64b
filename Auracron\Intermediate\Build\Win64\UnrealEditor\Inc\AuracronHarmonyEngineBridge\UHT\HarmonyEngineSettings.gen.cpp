// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "HarmonyEngineSettings.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeHarmonyEngineSettings() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSettings();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSettings_NoRegister();
DEVELOPERSETTINGS_API UClass* Z_Construct_UClass_UDeveloperSettings();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UHarmonyEngineSettings Function ApplySettings ****************************
struct Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEngineSettings, nullptr, "ApplySettings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEngineSettings::execApplySettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySettings();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEngineSettings Function ApplySettings ******************************

// ********** Begin Class UHarmonyEngineSettings Function GetHarmonyEngineSettings *****************
struct Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics
{
	struct HarmonyEngineSettings_eventGetHarmonyEngineSettings_Parms
	{
		UHarmonyEngineSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Static access\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineSettings_eventGetHarmonyEngineSettings_Parms, ReturnValue), Z_Construct_UClass_UHarmonyEngineSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEngineSettings, nullptr, "GetHarmonyEngineSettings", Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::HarmonyEngineSettings_eventGetHarmonyEngineSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x14022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::HarmonyEngineSettings_eventGetHarmonyEngineSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEngineSettings::execGetHarmonyEngineSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UHarmonyEngineSettings**)Z_Param__Result=UHarmonyEngineSettings::GetHarmonyEngineSettings();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEngineSettings Function GetHarmonyEngineSettings *******************

// ********** Begin Class UHarmonyEngineSettings Function ResetToDefaults **************************
struct Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEngineSettings, nullptr, "ResetToDefaults", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEngineSettings::execResetToDefaults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetToDefaults();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEngineSettings Function ResetToDefaults ****************************

// ********** Begin Class UHarmonyEngineSettings Function ValidateSettings *************************
struct Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics
{
	struct HarmonyEngineSettings_eventValidateSettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation and utility functions\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation and utility functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyEngineSettings_eventValidateSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEngineSettings_eventValidateSettings_Parms), &Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEngineSettings, nullptr, "ValidateSettings", Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::HarmonyEngineSettings_eventValidateSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::HarmonyEngineSettings_eventValidateSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEngineSettings::execValidateSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSettings();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEngineSettings Function ValidateSettings ***************************

// ********** Begin Class UHarmonyEngineSettings ***************************************************
void UHarmonyEngineSettings::StaticRegisterNativesUHarmonyEngineSettings()
{
	UClass* Class = UHarmonyEngineSettings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplySettings", &UHarmonyEngineSettings::execApplySettings },
		{ "GetHarmonyEngineSettings", &UHarmonyEngineSettings::execGetHarmonyEngineSettings },
		{ "ResetToDefaults", &UHarmonyEngineSettings::execResetToDefaults },
		{ "ValidateSettings", &UHarmonyEngineSettings::execValidateSettings },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UHarmonyEngineSettings;
UClass* UHarmonyEngineSettings::GetPrivateStaticClass()
{
	using TClass = UHarmonyEngineSettings;
	if (!Z_Registration_Info_UClass_UHarmonyEngineSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("HarmonyEngineSettings"),
			Z_Registration_Info_UClass_UHarmonyEngineSettings.InnerSingleton,
			StaticRegisterNativesUHarmonyEngineSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UHarmonyEngineSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UHarmonyEngineSettings_NoRegister()
{
	return UHarmonyEngineSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UHarmonyEngineSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Developer Settings for Harmony Engine\n * Configure all aspects of the anti-toxicity and community healing system\n */" },
#endif
		{ "DisplayName", "Harmony Engine Settings" },
		{ "IncludePath", "HarmonyEngineSettings.h" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Developer Settings for Harmony Engine\nConfigure all aspects of the anti-toxicity and community healing system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHarmonyEngine_MetaData[] = {
		{ "Category", "Core Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core System Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core System Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRealTimeMonitoring_MetaData[] = {
		{ "Category", "Core Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePredictiveIntervention_MetaData[] = {
		{ "Category", "Core Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCommunityHealing_MetaData[] = {
		{ "Category", "Core Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMachineLearning_MetaData[] = {
		{ "Category", "Core Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToxicityDetectionThreshold_MetaData[] = {
		{ "Category", "Behavior Detection" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Detection Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Detection Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositivityDetectionThreshold_MetaData[] = {
		{ "Category", "Behavior Detection" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustrationDetectionThreshold_MetaData[] = {
		{ "Category", "Behavior Detection" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorAnalysisInterval_MetaData[] = {
		{ "Category", "Behavior Detection" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticBehaviorDetection_MetaData[] = {
		{ "Category", "Behavior Detection" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentInterventions_MetaData[] = {
		{ "Category", "Intervention Settings" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervention Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervention Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionCooldownTime_MetaData[] = {
		{ "Category", "Intervention Settings" },
		{ "ClampMin", "30.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionTimeout_MetaData[] = {
		{ "Category", "Intervention Settings" },
		{ "ClampMin", "60.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticEscalation_MetaData[] = {
		{ "Category", "Intervention Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EscalationThreshold_MetaData[] = {
		{ "Category", "Intervention Settings" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentHealingSessions_MetaData[] = {
		{ "Category", "Community Healing" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Community Healing Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Community Healing Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealingSessionDuration_MetaData[] = {
		{ "Category", "Community Healing" },
		{ "ClampMin", "300.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHealerSkillLevel_MetaData[] = {
		{ "Category", "Community Healing" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealersPerSession_MetaData[] = {
		{ "Category", "Community Healing" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticHealerMatching_MetaData[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRewardsPerSession_MetaData[] = {
		{ "Category", "Rewards System" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rewards System Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rewards System Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardCooldownTime_MetaData[] = {
		{ "Category", "Rewards System" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProgressiveRewards_MetaData[] = {
		{ "Category", "Rewards System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSpecialEvents_MetaData[] = {
		{ "Category", "Rewards System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardAmplificationFactor_MetaData[] = {
		{ "Category", "Rewards System" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionalMonitoringInterval_MetaData[] = {
		{ "Category", "Emotional Intelligence" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Emotional Intelligence Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Emotional Intelligence Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EscalationDetectionWindow_MetaData[] = {
		{ "Category", "Emotional Intelligence" },
		{ "ClampMin", "60.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSupportMessagesPerSession_MetaData[] = {
		{ "Category", "Emotional Intelligence" },
		{ "ClampMin", "0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePredictiveEmotionalAnalysis_MetaData[] = {
		{ "Category", "Emotional Intelligence" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTrainingDataPoints_MetaData[] = {
		{ "Category", "Machine Learning" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Machine Learning Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Machine Learning Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MLModelUpdateInterval_MetaData[] = {
		{ "Category", "Machine Learning" },
		{ "ClampMin", "60.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MLModelAccuracyThreshold_MetaData[] = {
		{ "Category", "Machine Learning" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMLModelTraining_MetaData[] = {
		{ "Category", "Machine Learning" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTrainingDatasetSize_MetaData[] = {
		{ "Category", "Machine Learning" },
		{ "ClampMin", "100" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BronzeToSilverRequirement_MetaData[] = {
		{ "Category", "Tier System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tier Requirements\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tier Requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SilverToGoldRequirement_MetaData[] = {
		{ "Category", "Tier System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldToPlatinumRequirement_MetaData[] = {
		{ "Category", "Tier System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlatinumToDiamondRequirement_MetaData[] = {
		{ "Category", "Tier System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DiamondToLegendaryRequirement_MetaData[] = {
		{ "Category", "Tier System" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVerboseLogging_MetaData[] = {
		{ "Category", "Debug Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Logging and Debug Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Logging and Debug Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBehaviorDataLogging_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInterventionLogging_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSaveDebugDataToDisk_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugDataSavePath_MetaData[] = {
		{ "Category", "Debug Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TickInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayersPerAnalysisBatch_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceOptimizations_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBehaviorHistoryEntries_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMin", "100" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateBehaviorData_MetaData[] = {
		{ "Category", "Network Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateInterventions_MetaData[] = {
		{ "Category", "Network Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bReplicateRewards_MetaData[] = {
		{ "Category", "Network Settings" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkUpdateFrequency_MetaData[] = {
		{ "Category", "Network Settings" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithGameplayAbilitySystem_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration Settings\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithChatSystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithUISystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateWithAudioSystem_MetaData[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Public/HarmonyEngineSettings.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableHarmonyEngine_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHarmonyEngine;
	static void NewProp_bEnableRealTimeMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRealTimeMonitoring;
	static void NewProp_bEnablePredictiveIntervention_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePredictiveIntervention;
	static void NewProp_bEnableCommunityHealing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCommunityHealing;
	static void NewProp_bEnableMachineLearning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMachineLearning;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ToxicityDetectionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PositivityDetectionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrustrationDetectionThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BehaviorAnalysisInterval;
	static void NewProp_bEnableAutomaticBehaviorDetection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticBehaviorDetection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentInterventions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InterventionCooldownTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InterventionTimeout;
	static void NewProp_bEnableAutomaticEscalation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticEscalation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EscalationThreshold;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentHealingSessions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealingSessionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHealerSkillLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxHealersPerSession;
	static void NewProp_bEnableAutomaticHealerMatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticHealerMatching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRewardsPerSession;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardCooldownTime;
	static void NewProp_bEnableProgressiveRewards_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProgressiveRewards;
	static void NewProp_bEnableSpecialEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSpecialEvents;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardAmplificationFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmotionalMonitoringInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EscalationDetectionWindow;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSupportMessagesPerSession;
	static void NewProp_bEnablePredictiveEmotionalAnalysis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePredictiveEmotionalAnalysis;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinTrainingDataPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MLModelUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MLModelAccuracyThreshold;
	static void NewProp_bEnableMLModelTraining_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMLModelTraining;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTrainingDatasetSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BronzeToSilverRequirement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SilverToGoldRequirement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GoldToPlatinumRequirement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlatinumToDiamondRequirement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DiamondToLegendaryRequirement;
	static void NewProp_bEnableVerboseLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVerboseLogging;
	static void NewProp_bEnableBehaviorDataLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBehaviorDataLogging;
	static void NewProp_bEnableInterventionLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInterventionLogging;
	static void NewProp_bSaveDebugDataToDisk_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSaveDebugDataToDisk;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DebugDataSavePath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TickInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayersPerAnalysisBatch;
	static void NewProp_bEnablePerformanceOptimizations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceOptimizations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxBehaviorHistoryEntries;
	static void NewProp_bReplicateBehaviorData_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateBehaviorData;
	static void NewProp_bReplicateInterventions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateInterventions;
	static void NewProp_bReplicateRewards_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bReplicateRewards;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkUpdateFrequency;
	static void NewProp_bIntegrateWithGameplayAbilitySystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithGameplayAbilitySystem;
	static void NewProp_bIntegrateWithChatSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithChatSystem;
	static void NewProp_bIntegrateWithUISystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithUISystem;
	static void NewProp_bIntegrateWithAudioSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateWithAudioSystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UHarmonyEngineSettings_ApplySettings, "ApplySettings" }, // 2879572848
		{ &Z_Construct_UFunction_UHarmonyEngineSettings_GetHarmonyEngineSettings, "GetHarmonyEngineSettings" }, // 3026217887
		{ &Z_Construct_UFunction_UHarmonyEngineSettings_ResetToDefaults, "ResetToDefaults" }, // 784984796
		{ &Z_Construct_UFunction_UHarmonyEngineSettings_ValidateSettings, "ValidateSettings" }, // 2461608706
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UHarmonyEngineSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableHarmonyEngine_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableHarmonyEngine = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableHarmonyEngine = { "bEnableHarmonyEngine", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableHarmonyEngine_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHarmonyEngine_MetaData), NewProp_bEnableHarmonyEngine_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableRealTimeMonitoring_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableRealTimeMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableRealTimeMonitoring = { "bEnableRealTimeMonitoring", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableRealTimeMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRealTimeMonitoring_MetaData), NewProp_bEnableRealTimeMonitoring_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveIntervention_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnablePredictiveIntervention = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveIntervention = { "bEnablePredictiveIntervention", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveIntervention_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePredictiveIntervention_MetaData), NewProp_bEnablePredictiveIntervention_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableCommunityHealing_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableCommunityHealing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableCommunityHealing = { "bEnableCommunityHealing", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableCommunityHealing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCommunityHealing_MetaData), NewProp_bEnableCommunityHealing_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMachineLearning_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableMachineLearning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMachineLearning = { "bEnableMachineLearning", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMachineLearning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMachineLearning_MetaData), NewProp_bEnableMachineLearning_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_ToxicityDetectionThreshold = { "ToxicityDetectionThreshold", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, ToxicityDetectionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToxicityDetectionThreshold_MetaData), NewProp_ToxicityDetectionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_PositivityDetectionThreshold = { "PositivityDetectionThreshold", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, PositivityDetectionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositivityDetectionThreshold_MetaData), NewProp_PositivityDetectionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_FrustrationDetectionThreshold = { "FrustrationDetectionThreshold", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, FrustrationDetectionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustrationDetectionThreshold_MetaData), NewProp_FrustrationDetectionThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_BehaviorAnalysisInterval = { "BehaviorAnalysisInterval", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, BehaviorAnalysisInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorAnalysisInterval_MetaData), NewProp_BehaviorAnalysisInterval_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticBehaviorDetection_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableAutomaticBehaviorDetection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticBehaviorDetection = { "bEnableAutomaticBehaviorDetection", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticBehaviorDetection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticBehaviorDetection_MetaData), NewProp_bEnableAutomaticBehaviorDetection_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxConcurrentInterventions = { "MaxConcurrentInterventions", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxConcurrentInterventions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentInterventions_MetaData), NewProp_MaxConcurrentInterventions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_InterventionCooldownTime = { "InterventionCooldownTime", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, InterventionCooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionCooldownTime_MetaData), NewProp_InterventionCooldownTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_InterventionTimeout = { "InterventionTimeout", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, InterventionTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionTimeout_MetaData), NewProp_InterventionTimeout_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticEscalation_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableAutomaticEscalation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticEscalation = { "bEnableAutomaticEscalation", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticEscalation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticEscalation_MetaData), NewProp_bEnableAutomaticEscalation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EscalationThreshold = { "EscalationThreshold", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, EscalationThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EscalationThreshold_MetaData), NewProp_EscalationThreshold_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxConcurrentHealingSessions = { "MaxConcurrentHealingSessions", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxConcurrentHealingSessions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentHealingSessions_MetaData), NewProp_MaxConcurrentHealingSessions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxHealingSessionDuration = { "MaxHealingSessionDuration", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxHealingSessionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealingSessionDuration_MetaData), NewProp_MaxHealingSessionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MinHealerSkillLevel = { "MinHealerSkillLevel", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MinHealerSkillLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHealerSkillLevel_MetaData), NewProp_MinHealerSkillLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxHealersPerSession = { "MaxHealersPerSession", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxHealersPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealersPerSession_MetaData), NewProp_MaxHealersPerSession_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticHealerMatching_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableAutomaticHealerMatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticHealerMatching = { "bEnableAutomaticHealerMatching", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticHealerMatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticHealerMatching_MetaData), NewProp_bEnableAutomaticHealerMatching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxRewardsPerSession = { "MaxRewardsPerSession", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxRewardsPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRewardsPerSession_MetaData), NewProp_MaxRewardsPerSession_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_RewardCooldownTime = { "RewardCooldownTime", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, RewardCooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardCooldownTime_MetaData), NewProp_RewardCooldownTime_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableProgressiveRewards_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableProgressiveRewards = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableProgressiveRewards = { "bEnableProgressiveRewards", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableProgressiveRewards_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProgressiveRewards_MetaData), NewProp_bEnableProgressiveRewards_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableSpecialEvents_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableSpecialEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableSpecialEvents = { "bEnableSpecialEvents", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableSpecialEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSpecialEvents_MetaData), NewProp_bEnableSpecialEvents_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_RewardAmplificationFactor = { "RewardAmplificationFactor", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, RewardAmplificationFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardAmplificationFactor_MetaData), NewProp_RewardAmplificationFactor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EmotionalMonitoringInterval = { "EmotionalMonitoringInterval", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, EmotionalMonitoringInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionalMonitoringInterval_MetaData), NewProp_EmotionalMonitoringInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EscalationDetectionWindow = { "EscalationDetectionWindow", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, EscalationDetectionWindow), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EscalationDetectionWindow_MetaData), NewProp_EscalationDetectionWindow_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxSupportMessagesPerSession = { "MaxSupportMessagesPerSession", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxSupportMessagesPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSupportMessagesPerSession_MetaData), NewProp_MaxSupportMessagesPerSession_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveEmotionalAnalysis_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnablePredictiveEmotionalAnalysis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveEmotionalAnalysis = { "bEnablePredictiveEmotionalAnalysis", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveEmotionalAnalysis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePredictiveEmotionalAnalysis_MetaData), NewProp_bEnablePredictiveEmotionalAnalysis_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MinTrainingDataPoints = { "MinTrainingDataPoints", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MinTrainingDataPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTrainingDataPoints_MetaData), NewProp_MinTrainingDataPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MLModelUpdateInterval = { "MLModelUpdateInterval", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MLModelUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MLModelUpdateInterval_MetaData), NewProp_MLModelUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MLModelAccuracyThreshold = { "MLModelAccuracyThreshold", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MLModelAccuracyThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MLModelAccuracyThreshold_MetaData), NewProp_MLModelAccuracyThreshold_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMLModelTraining_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableMLModelTraining = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMLModelTraining = { "bEnableMLModelTraining", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMLModelTraining_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMLModelTraining_MetaData), NewProp_bEnableMLModelTraining_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxTrainingDatasetSize = { "MaxTrainingDatasetSize", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxTrainingDatasetSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTrainingDatasetSize_MetaData), NewProp_MaxTrainingDatasetSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_BronzeToSilverRequirement = { "BronzeToSilverRequirement", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, BronzeToSilverRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BronzeToSilverRequirement_MetaData), NewProp_BronzeToSilverRequirement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_SilverToGoldRequirement = { "SilverToGoldRequirement", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, SilverToGoldRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SilverToGoldRequirement_MetaData), NewProp_SilverToGoldRequirement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_GoldToPlatinumRequirement = { "GoldToPlatinumRequirement", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, GoldToPlatinumRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldToPlatinumRequirement_MetaData), NewProp_GoldToPlatinumRequirement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_PlatinumToDiamondRequirement = { "PlatinumToDiamondRequirement", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, PlatinumToDiamondRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlatinumToDiamondRequirement_MetaData), NewProp_PlatinumToDiamondRequirement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_DiamondToLegendaryRequirement = { "DiamondToLegendaryRequirement", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, DiamondToLegendaryRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DiamondToLegendaryRequirement_MetaData), NewProp_DiamondToLegendaryRequirement_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableVerboseLogging_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableVerboseLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableVerboseLogging = { "bEnableVerboseLogging", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableVerboseLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVerboseLogging_MetaData), NewProp_bEnableVerboseLogging_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableBehaviorDataLogging_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableBehaviorDataLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableBehaviorDataLogging = { "bEnableBehaviorDataLogging", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableBehaviorDataLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBehaviorDataLogging_MetaData), NewProp_bEnableBehaviorDataLogging_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableInterventionLogging_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnableInterventionLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableInterventionLogging = { "bEnableInterventionLogging", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableInterventionLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInterventionLogging_MetaData), NewProp_bEnableInterventionLogging_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bSaveDebugDataToDisk_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bSaveDebugDataToDisk = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bSaveDebugDataToDisk = { "bSaveDebugDataToDisk", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bSaveDebugDataToDisk_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSaveDebugDataToDisk_MetaData), NewProp_bSaveDebugDataToDisk_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_DebugDataSavePath = { "DebugDataSavePath", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, DebugDataSavePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugDataSavePath_MetaData), NewProp_DebugDataSavePath_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_TickInterval = { "TickInterval", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, TickInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TickInterval_MetaData), NewProp_TickInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxPlayersPerAnalysisBatch = { "MaxPlayersPerAnalysisBatch", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxPlayersPerAnalysisBatch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayersPerAnalysisBatch_MetaData), NewProp_MaxPlayersPerAnalysisBatch_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePerformanceOptimizations_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bEnablePerformanceOptimizations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePerformanceOptimizations = { "bEnablePerformanceOptimizations", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePerformanceOptimizations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceOptimizations_MetaData), NewProp_bEnablePerformanceOptimizations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxBehaviorHistoryEntries = { "MaxBehaviorHistoryEntries", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, MaxBehaviorHistoryEntries), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBehaviorHistoryEntries_MetaData), NewProp_MaxBehaviorHistoryEntries_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateBehaviorData_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bReplicateBehaviorData = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateBehaviorData = { "bReplicateBehaviorData", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateBehaviorData_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateBehaviorData_MetaData), NewProp_bReplicateBehaviorData_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateInterventions_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bReplicateInterventions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateInterventions = { "bReplicateInterventions", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateInterventions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateInterventions_MetaData), NewProp_bReplicateInterventions_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateRewards_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bReplicateRewards = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateRewards = { "bReplicateRewards", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateRewards_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bReplicateRewards_MetaData), NewProp_bReplicateRewards_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_NetworkUpdateFrequency = { "NetworkUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEngineSettings, NetworkUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkUpdateFrequency_MetaData), NewProp_NetworkUpdateFrequency_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithGameplayAbilitySystem_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bIntegrateWithGameplayAbilitySystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithGameplayAbilitySystem = { "bIntegrateWithGameplayAbilitySystem", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithGameplayAbilitySystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithGameplayAbilitySystem_MetaData), NewProp_bIntegrateWithGameplayAbilitySystem_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithChatSystem_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bIntegrateWithChatSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithChatSystem = { "bIntegrateWithChatSystem", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithChatSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithChatSystem_MetaData), NewProp_bIntegrateWithChatSystem_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithUISystem_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bIntegrateWithUISystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithUISystem = { "bIntegrateWithUISystem", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithUISystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithUISystem_MetaData), NewProp_bIntegrateWithUISystem_MetaData) };
void Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithAudioSystem_SetBit(void* Obj)
{
	((UHarmonyEngineSettings*)Obj)->bIntegrateWithAudioSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithAudioSystem = { "bIntegrateWithAudioSystem", nullptr, (EPropertyFlags)0x0010000000004015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEngineSettings), &Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithAudioSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateWithAudioSystem_MetaData), NewProp_bIntegrateWithAudioSystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UHarmonyEngineSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableHarmonyEngine,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableRealTimeMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveIntervention,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableCommunityHealing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMachineLearning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_ToxicityDetectionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_PositivityDetectionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_FrustrationDetectionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_BehaviorAnalysisInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticBehaviorDetection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxConcurrentInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_InterventionCooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_InterventionTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticEscalation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EscalationThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxConcurrentHealingSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxHealingSessionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MinHealerSkillLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxHealersPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableAutomaticHealerMatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxRewardsPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_RewardCooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableProgressiveRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableSpecialEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_RewardAmplificationFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EmotionalMonitoringInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_EscalationDetectionWindow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxSupportMessagesPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePredictiveEmotionalAnalysis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MinTrainingDataPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MLModelUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MLModelAccuracyThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableMLModelTraining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxTrainingDatasetSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_BronzeToSilverRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_SilverToGoldRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_GoldToPlatinumRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_PlatinumToDiamondRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_DiamondToLegendaryRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableVerboseLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableBehaviorDataLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnableInterventionLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bSaveDebugDataToDisk,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_DebugDataSavePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_TickInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxPlayersPerAnalysisBatch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bEnablePerformanceOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_MaxBehaviorHistoryEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateBehaviorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bReplicateRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_NetworkUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithGameplayAbilitySystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithChatSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithUISystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEngineSettings_Statics::NewProp_bIntegrateWithAudioSystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEngineSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UHarmonyEngineSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDeveloperSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEngineSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UHarmonyEngineSettings_Statics::ClassParams = {
	&UHarmonyEngineSettings::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UHarmonyEngineSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEngineSettings_Statics::PropPointers),
	0,
	0x001000A6u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEngineSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UHarmonyEngineSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UHarmonyEngineSettings()
{
	if (!Z_Registration_Info_UClass_UHarmonyEngineSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UHarmonyEngineSettings.OuterSingleton, Z_Construct_UClass_UHarmonyEngineSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UHarmonyEngineSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UHarmonyEngineSettings);
UHarmonyEngineSettings::~UHarmonyEngineSettings() {}
// ********** End Class UHarmonyEngineSettings *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UHarmonyEngineSettings, UHarmonyEngineSettings::StaticClass, TEXT("UHarmonyEngineSettings"), &Z_Registration_Info_UClass_UHarmonyEngineSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UHarmonyEngineSettings), 1188070253U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h__Script_AuracronHarmonyEngineBridge_1487851171(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
