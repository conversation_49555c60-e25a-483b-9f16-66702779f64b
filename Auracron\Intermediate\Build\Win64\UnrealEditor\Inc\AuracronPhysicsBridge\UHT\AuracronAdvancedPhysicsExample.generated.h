// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdvancedPhysicsExample.h"

#ifdef AURACRONPHYSICSBRIDGE_AuracronAdvancedPhysicsExample_generated_h
#error "AuracronAdvancedPhysicsExample.generated.h already included, missing '#pragma once' in AuracronAdvancedPhysicsExample.h"
#endif
#define AURACRONPHYSICSBRIDGE_AuracronAdvancedPhysicsExample_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAuracronFluidType : uint8;
enum class EAuracronPhysicsQuality : uint8;

// ********** Begin Class AAuracronAdvancedPhysicsExample ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPhysicsPerformanceUpdatedExample); \
	DECLARE_FUNCTION(execOnVehicleCreatedExample); \
	DECLARE_FUNCTION(execOnConstraintBrokenExample); \
	DECLARE_FUNCTION(execOnClothTornExample); \
	DECLARE_FUNCTION(execOnSoftBodyDeformedExample); \
	DECLARE_FUNCTION(execOnFluidSimulationCreatedExample); \
	DECLARE_FUNCTION(execRunAdvancedPhysicsScenario); \
	DECLARE_FUNCTION(execSetPhysicsQualityExample); \
	DECLARE_FUNCTION(execExportPhysicsAnalyticsExample); \
	DECLARE_FUNCTION(execDemonstratePerformanceMonitoring); \
	DECLARE_FUNCTION(execDemonstrateVehicleSuspension); \
	DECLARE_FUNCTION(execCreateAdvancedVehicleExample); \
	DECLARE_FUNCTION(execInitializeVehiclePhysicsExample); \
	DECLARE_FUNCTION(execDemonstrateConstraintBreaking); \
	DECLARE_FUNCTION(execCreateMotorConstraintExample); \
	DECLARE_FUNCTION(execCreateSpringConstraintExample); \
	DECLARE_FUNCTION(execDemonstrateClothTearing); \
	DECLARE_FUNCTION(execDemonstrateClothWindEffects); \
	DECLARE_FUNCTION(execCreateClothSimulationExample); \
	DECLARE_FUNCTION(execInitializeClothExample); \
	DECLARE_FUNCTION(execDemonstrateSoftBodyDeformation); \
	DECLARE_FUNCTION(execConvertToJellySoftBody); \
	DECLARE_FUNCTION(execConvertToRubberSoftBody); \
	DECLARE_FUNCTION(execInitializeSoftBodyExample); \
	DECLARE_FUNCTION(execDemonstrateFluidInteractions); \
	DECLARE_FUNCTION(execCreateLavaSimulationExample); \
	DECLARE_FUNCTION(execCreateWaterSimulationExample); \
	DECLARE_FUNCTION(execInitializeFluidSimulationExample);


AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronAdvancedPhysicsExample(); \
	friend struct Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronAdvancedPhysicsExample, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronPhysicsBridge"), Z_Construct_UClass_AAuracronAdvancedPhysicsExample_NoRegister) \
	DECLARE_SERIALIZER(AAuracronAdvancedPhysicsExample)


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronAdvancedPhysicsExample(AAuracronAdvancedPhysicsExample&&) = delete; \
	AAuracronAdvancedPhysicsExample(const AAuracronAdvancedPhysicsExample&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronAdvancedPhysicsExample); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronAdvancedPhysicsExample); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronAdvancedPhysicsExample) \
	NO_API virtual ~AAuracronAdvancedPhysicsExample();


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_15_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronAdvancedPhysicsExample;

// ********** End Class AAuracronAdvancedPhysicsExample ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
