// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronSigilosBridge_init() {}
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnAegisSigilCooldownComplete__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnArchetypeFormed__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnCriticalAssetsLoadComplete__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20Activated__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20CooldownComplete__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20Ended__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnRuinSigilCooldownComplete__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilActivated__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilEquipped__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilExperienceGained__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilLevelUp__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSystemFullyInitialized__DelegateSignature();
	AURACRONSIGILOSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnVesperSigilCooldownComplete__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronSigilosBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronSigilosBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnAegisSigilCooldownComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnArchetypeFormed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnCriticalAssetsLoadComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20Activated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20CooldownComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusion20Ended__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnFusionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnRuinSigilCooldownComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilEquipped__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilExperienceGained__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigilLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloReforged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSigiloSelected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnSystemFullyInitialized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronSigilosBridge_OnVesperSigilCooldownComplete__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronSigilosBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xD5B4BBB7,
				0xB289FE35,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronSigilosBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronSigilosBridge(Z_Construct_UPackage__Script_AuracronSigilosBridge, TEXT("/Script/AuracronSigilosBridge"), Z_Registration_Info_UPackage__Script_AuracronSigilosBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xD5B4BBB7, 0xB289FE35));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
