// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAntiCheatBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAntiCheatBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONANTICHEATBRIDGE_API UClass* Z_Construct_UClass_UAuracronAntiCheatBridge();
AURACRONANTICHEATBRIDGE_API UClass* Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister();
AURACRONANTICHEATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction();
AURACRONANTICHEATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity();
AURACRONANTICHEATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType();
AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature();
AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature();
AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature();
AURACRONANTICHEATBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature();
AURACRONANTICHEATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration();
AURACRONANTICHEATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBanInfo();
AURACRONANTICHEATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCheatDetection();
AURACRONANTICHEATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronKickInfo();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_AuracronAntiCheatBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronCheatType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCheatType;
static UEnum* EAuracronCheatType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCheatType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCheatType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("EAuracronCheatType"));
	}
	return Z_Registration_Info_UEnum_EAuracronCheatType.OuterSingleton;
}
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronCheatType>()
{
	return EAuracronCheatType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbilityHack.DisplayName", "Ability Hack" },
		{ "AbilityHack.Name", "EAuracronCheatType::AbilityHack" },
		{ "AimbotHack.DisplayName", "Aimbot Hack" },
		{ "AimbotHack.Name", "EAuracronCheatType::AimbotHack" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de cheat detectados\n */" },
#endif
		{ "CooldownHack.DisplayName", "Cooldown Hack" },
		{ "CooldownHack.Name", "EAuracronCheatType::CooldownHack" },
		{ "DamageHack.DisplayName", "Damage Hack" },
		{ "DamageHack.Name", "EAuracronCheatType::DamageHack" },
		{ "ExperienceHack.DisplayName", "Experience Hack" },
		{ "ExperienceHack.Name", "EAuracronCheatType::ExperienceHack" },
		{ "HealthHack.DisplayName", "Health Hack" },
		{ "HealthHack.Name", "EAuracronCheatType::HealthHack" },
		{ "InputHack.DisplayName", "Input Hack" },
		{ "InputHack.Name", "EAuracronCheatType::InputHack" },
		{ "ItemHack.DisplayName", "Item Hack" },
		{ "ItemHack.Name", "EAuracronCheatType::ItemHack" },
		{ "MemoryHack.DisplayName", "Memory Hack" },
		{ "MemoryHack.Name", "EAuracronCheatType::MemoryHack" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
		{ "NetworkHack.DisplayName", "Network Hack" },
		{ "NetworkHack.Name", "EAuracronCheatType::NetworkHack" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronCheatType::None" },
		{ "PositionHack.DisplayName", "Position Hack" },
		{ "PositionHack.Name", "EAuracronCheatType::PositionHack" },
		{ "ResourceHack.DisplayName", "Resource Hack" },
		{ "ResourceHack.Name", "EAuracronCheatType::ResourceHack" },
		{ "SpeedHack.DisplayName", "Speed Hack" },
		{ "SpeedHack.Name", "EAuracronCheatType::SpeedHack" },
		{ "StatHack.DisplayName", "Stat Hack" },
		{ "StatHack.Name", "EAuracronCheatType::StatHack" },
		{ "TeleportHack.DisplayName", "Teleport Hack" },
		{ "TeleportHack.Name", "EAuracronCheatType::TeleportHack" },
		{ "TimingHack.DisplayName", "Timing Hack" },
		{ "TimingHack.Name", "EAuracronCheatType::TimingHack" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de cheat detectados" },
#endif
		{ "UnknownHack.DisplayName", "Unknown Hack" },
		{ "UnknownHack.Name", "EAuracronCheatType::UnknownHack" },
		{ "WallHack.DisplayName", "Wall Hack" },
		{ "WallHack.Name", "EAuracronCheatType::WallHack" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCheatType::None", (int64)EAuracronCheatType::None },
		{ "EAuracronCheatType::SpeedHack", (int64)EAuracronCheatType::SpeedHack },
		{ "EAuracronCheatType::TeleportHack", (int64)EAuracronCheatType::TeleportHack },
		{ "EAuracronCheatType::WallHack", (int64)EAuracronCheatType::WallHack },
		{ "EAuracronCheatType::AimbotHack", (int64)EAuracronCheatType::AimbotHack },
		{ "EAuracronCheatType::DamageHack", (int64)EAuracronCheatType::DamageHack },
		{ "EAuracronCheatType::HealthHack", (int64)EAuracronCheatType::HealthHack },
		{ "EAuracronCheatType::ResourceHack", (int64)EAuracronCheatType::ResourceHack },
		{ "EAuracronCheatType::CooldownHack", (int64)EAuracronCheatType::CooldownHack },
		{ "EAuracronCheatType::PositionHack", (int64)EAuracronCheatType::PositionHack },
		{ "EAuracronCheatType::InputHack", (int64)EAuracronCheatType::InputHack },
		{ "EAuracronCheatType::MemoryHack", (int64)EAuracronCheatType::MemoryHack },
		{ "EAuracronCheatType::NetworkHack", (int64)EAuracronCheatType::NetworkHack },
		{ "EAuracronCheatType::TimingHack", (int64)EAuracronCheatType::TimingHack },
		{ "EAuracronCheatType::StatHack", (int64)EAuracronCheatType::StatHack },
		{ "EAuracronCheatType::AbilityHack", (int64)EAuracronCheatType::AbilityHack },
		{ "EAuracronCheatType::ItemHack", (int64)EAuracronCheatType::ItemHack },
		{ "EAuracronCheatType::ExperienceHack", (int64)EAuracronCheatType::ExperienceHack },
		{ "EAuracronCheatType::UnknownHack", (int64)EAuracronCheatType::UnknownHack },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	"EAuracronCheatType",
	"EAuracronCheatType",
	Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType()
{
	if (!Z_Registration_Info_UEnum_EAuracronCheatType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCheatType.InnerSingleton, Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCheatType.InnerSingleton;
}
// ********** End Enum EAuracronCheatType **********************************************************

// ********** Begin Enum EAuracronCheatSeverity ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCheatSeverity;
static UEnum* EAuracronCheatSeverity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCheatSeverity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCheatSeverity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("EAuracronCheatSeverity"));
	}
	return Z_Registration_Info_UEnum_EAuracronCheatSeverity.OuterSingleton;
}
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronCheatSeverity>()
{
	return EAuracronCheatSeverity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para severidade de cheat\n */" },
#endif
		{ "Critical.DisplayName", "Critical Severity" },
		{ "Critical.Name", "EAuracronCheatSeverity::Critical" },
		{ "High.DisplayName", "High Severity" },
		{ "High.Name", "EAuracronCheatSeverity::High" },
		{ "Immediate.DisplayName", "Immediate Action Required" },
		{ "Immediate.Name", "EAuracronCheatSeverity::Immediate" },
		{ "Low.DisplayName", "Low Severity" },
		{ "Low.Name", "EAuracronCheatSeverity::Low" },
		{ "Medium.DisplayName", "Medium Severity" },
		{ "Medium.Name", "EAuracronCheatSeverity::Medium" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para severidade de cheat" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCheatSeverity::Low", (int64)EAuracronCheatSeverity::Low },
		{ "EAuracronCheatSeverity::Medium", (int64)EAuracronCheatSeverity::Medium },
		{ "EAuracronCheatSeverity::High", (int64)EAuracronCheatSeverity::High },
		{ "EAuracronCheatSeverity::Critical", (int64)EAuracronCheatSeverity::Critical },
		{ "EAuracronCheatSeverity::Immediate", (int64)EAuracronCheatSeverity::Immediate },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	"EAuracronCheatSeverity",
	"EAuracronCheatSeverity",
	Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity()
{
	if (!Z_Registration_Info_UEnum_EAuracronCheatSeverity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCheatSeverity.InnerSingleton, Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCheatSeverity.InnerSingleton;
}
// ********** End Enum EAuracronCheatSeverity ******************************************************

// ********** Begin Enum EAuracronAntiCheatAction **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAntiCheatAction;
static UEnum* EAuracronAntiCheatAction_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAntiCheatAction.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAntiCheatAction.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("EAuracronAntiCheatAction"));
	}
	return Z_Registration_Info_UEnum_EAuracronAntiCheatAction.OuterSingleton;
}
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronAntiCheatAction>()
{
	return EAuracronAntiCheatAction_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para a\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es anti-cheat\n */" },
#endif
		{ "Correction.DisplayName", "Correction" },
		{ "Correction.Name", "EAuracronAntiCheatAction::Correction" },
		{ "Investigation.DisplayName", "Investigation" },
		{ "Investigation.Name", "EAuracronAntiCheatAction::Investigation" },
		{ "Kick.DisplayName", "Kick" },
		{ "Kick.Name", "EAuracronAntiCheatAction::Kick" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
		{ "Monitoring.DisplayName", "Enhanced Monitoring" },
		{ "Monitoring.Name", "EAuracronAntiCheatAction::Monitoring" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronAntiCheatAction::None" },
		{ "PermBan.DisplayName", "Permanent Ban" },
		{ "PermBan.Name", "EAuracronAntiCheatAction::PermBan" },
		{ "TempBan.DisplayName", "Temporary Ban" },
		{ "TempBan.Name", "EAuracronAntiCheatAction::TempBan" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para a\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es anti-cheat" },
#endif
		{ "Warning.DisplayName", "Warning" },
		{ "Warning.Name", "EAuracronAntiCheatAction::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAntiCheatAction::None", (int64)EAuracronAntiCheatAction::None },
		{ "EAuracronAntiCheatAction::Warning", (int64)EAuracronAntiCheatAction::Warning },
		{ "EAuracronAntiCheatAction::Correction", (int64)EAuracronAntiCheatAction::Correction },
		{ "EAuracronAntiCheatAction::Kick", (int64)EAuracronAntiCheatAction::Kick },
		{ "EAuracronAntiCheatAction::TempBan", (int64)EAuracronAntiCheatAction::TempBan },
		{ "EAuracronAntiCheatAction::PermBan", (int64)EAuracronAntiCheatAction::PermBan },
		{ "EAuracronAntiCheatAction::Investigation", (int64)EAuracronAntiCheatAction::Investigation },
		{ "EAuracronAntiCheatAction::Monitoring", (int64)EAuracronAntiCheatAction::Monitoring },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	"EAuracronAntiCheatAction",
	"EAuracronAntiCheatAction",
	Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction()
{
	if (!Z_Registration_Info_UEnum_EAuracronAntiCheatAction.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAntiCheatAction.InnerSingleton, Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAntiCheatAction.InnerSingleton;
}
// ********** End Enum EAuracronAntiCheatAction ****************************************************

// ********** Begin ScriptStruct FAuracronBanInfo **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBanInfo;
class UScriptStruct* FAuracronBanInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBanInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBanInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBanInfo, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("AuracronBanInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBanInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBanInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para informa\xc3\xa7\xc3\xb5""es de ban\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para informa\xc3\xa7\xc3\xb5""es de ban" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BanStartTime_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BanDurationHours_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPermanent_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BanEndTime_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdminID_MetaData[] = {
		{ "Category", "Ban Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BanStartTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BanDurationHours;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static void NewProp_bIsPermanent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPermanent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BanEndTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdminID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBanInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanStartTime = { "BanStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, BanStartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BanStartTime_MetaData), NewProp_BanStartTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanDurationHours = { "BanDurationHours", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, BanDurationHours), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BanDurationHours_MetaData), NewProp_BanDurationHours_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_bIsPermanent_SetBit(void* Obj)
{
	((FAuracronBanInfo*)Obj)->bIsPermanent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_bIsPermanent = { "bIsPermanent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBanInfo), &Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_bIsPermanent_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPermanent_MetaData), NewProp_bIsPermanent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanEndTime = { "BanEndTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, BanEndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BanEndTime_MetaData), NewProp_BanEndTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_AdminID = { "AdminID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBanInfo, AdminID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdminID_MetaData), NewProp_AdminID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanDurationHours,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_bIsPermanent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_BanEndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewProp_AdminID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	&NewStructOps,
	"AuracronBanInfo",
	Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::PropPointers),
	sizeof(FAuracronBanInfo),
	alignof(FAuracronBanInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBanInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBanInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBanInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBanInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBanInfo ****************************************************

// ********** Begin ScriptStruct FAuracronKickInfo *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronKickInfo;
class UScriptStruct* FAuracronKickInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronKickInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronKickInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronKickInfo, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("AuracronKickInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronKickInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronKickInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para informa\xc3\xa7\xc3\xb5""es de kick\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para informa\xc3\xa7\xc3\xb5""es de kick" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Kick Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KickTime_MetaData[] = {
		{ "Category", "Kick Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "Category", "Kick Info" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_KickTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronKickInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronKickInfo, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_KickTime = { "KickTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronKickInfo, KickTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KickTime_MetaData), NewProp_KickTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronKickInfo, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_KickTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	&NewStructOps,
	"AuracronKickInfo",
	Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::PropPointers),
	sizeof(FAuracronKickInfo),
	alignof(FAuracronKickInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronKickInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronKickInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronKickInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronKickInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronKickInfo ***************************************************

// ********** Begin ScriptStruct FAuracronCheatDetection *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCheatDetection;
class UScriptStruct* FAuracronCheatDetection::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCheatDetection, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("AuracronCheatDetection"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para detec\xc3\xa7\xc3\xa3o de cheat\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para detec\xc3\xa7\xc3\xa3o de cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionID_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheatType_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de cheat detectado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de cheat detectado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Severidade do cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Severidade do cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Confidence_MetaData[] = {
		{ "Category", "Cheat Detection" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Confian\xc3\x83\xc2\xa7""a da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Confian\xc3\x83\xc2\xa7""a da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionTime_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Evidence_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evid\xc3\x83\xc2\xaancias coletadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evid\xc3\x83\xc2\xaancias coletadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuspiciousValues_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valores suspeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valores suspeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionLocation_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionContext_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contexto da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contexto da detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTaken_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o tomada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o tomada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bConfirmed_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi confirmada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o foi confirmada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFalsePositive_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Falso positivo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Falso positivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntegrityHash_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Hash de integridade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Hash de integridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EncryptedData_MetaData[] = {
		{ "Category", "Cheat Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dados criptografados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados criptografados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DetectionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CheatType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CheatType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Severity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Confidence;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DetectionTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Evidence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuspiciousValues_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuspiciousValues_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SuspiciousValues;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DetectionLocation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DetectionContext;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActionTaken_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActionTaken;
	static void NewProp_bConfirmed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bConfirmed;
	static void NewProp_bFalsePositive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFalsePositive;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IntegrityHash;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EncryptedData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCheatDetection>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionID = { "DetectionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, DetectionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionID_MetaData), NewProp_DetectionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_CheatType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_CheatType = { "CheatType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, CheatType), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheatType_MetaData), NewProp_CheatType_MetaData) }; // 2592168363
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Severity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, Severity), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronCheatSeverity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) }; // 1869528791
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Confidence = { "Confidence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, Confidence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Confidence_MetaData), NewProp_Confidence_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionTime = { "DetectionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, DetectionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionTime_MetaData), NewProp_DetectionTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence_ValueProp = { "Evidence", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence_Key_KeyProp = { "Evidence_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence = { "Evidence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, Evidence), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Evidence_MetaData), NewProp_Evidence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues_ValueProp = { "SuspiciousValues", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues_Key_KeyProp = { "SuspiciousValues_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues = { "SuspiciousValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, SuspiciousValues), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuspiciousValues_MetaData), NewProp_SuspiciousValues_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionLocation = { "DetectionLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, DetectionLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionLocation_MetaData), NewProp_DetectionLocation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionContext = { "DetectionContext", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, DetectionContext), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionContext_MetaData), NewProp_DetectionContext_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_ActionTaken_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_ActionTaken = { "ActionTaken", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, ActionTaken), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTaken_MetaData), NewProp_ActionTaken_MetaData) }; // 4279590589
void Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bConfirmed_SetBit(void* Obj)
{
	((FAuracronCheatDetection*)Obj)->bConfirmed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bConfirmed = { "bConfirmed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCheatDetection), &Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bConfirmed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bConfirmed_MetaData), NewProp_bConfirmed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bFalsePositive_SetBit(void* Obj)
{
	((FAuracronCheatDetection*)Obj)->bFalsePositive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bFalsePositive = { "bFalsePositive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCheatDetection), &Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bFalsePositive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFalsePositive_MetaData), NewProp_bFalsePositive_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_IntegrityHash = { "IntegrityHash", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, IntegrityHash), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntegrityHash_MetaData), NewProp_IntegrityHash_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_EncryptedData = { "EncryptedData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCheatDetection, EncryptedData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EncryptedData_MetaData), NewProp_EncryptedData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_CheatType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_CheatType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Severity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Confidence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_Evidence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_SuspiciousValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_DetectionContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_ActionTaken_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_ActionTaken,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bConfirmed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_bFalsePositive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_IntegrityHash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewProp_EncryptedData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	&NewStructOps,
	"AuracronCheatDetection",
	Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::PropPointers),
	sizeof(FAuracronCheatDetection),
	alignof(FAuracronCheatDetection),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCheatDetection()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCheatDetection.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCheatDetection *********************************************

// ********** Begin ScriptStruct FAuracronAntiCheatConfiguration ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration;
class UScriptStruct* FAuracronAntiCheatConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronAntiCheatBridge(), TEXT("AuracronAntiCheatConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAntiCheatEnabled_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema anti-cheat habilitado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema anti-cheat habilitado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseServerValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o server-side */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o server-side" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMovementValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAllowedSpeed_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade m\xc3\x83\xc2\xa1xima permitida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade m\xc3\x83\xc2\xa1xima permitida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePositionValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTeleportDistance_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de teleporte */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de teleporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAbilityValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCooldownValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de cooldowns */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de cooldowns" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimingTolerance_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.01" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Toler\xc3\x83\xc2\xa2ncia de timing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Toler\xc3\x83\xc2\xa2ncia de timing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDamageValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDamageMultiplier_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador m\xc3\x83\xc2\xa1ximo de dano */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador m\xc3\x83\xc2\xa1ximo de dano" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseResourceValidation_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de recursos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar valida\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de recursos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBehavioralDetection_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o comportamental */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o comportamental" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuspiciousBehaviorThreshold_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold para comportamento suspeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold para comportamento suspeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseStatisticalAnalysis_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar an\xc3\x83\xc2\xa1lise estat\xc3\x83\xc2\xadstica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar an\xc3\x83\xc2\xa1lise estat\xc3\x83\xc2\xadstica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalysisWindowSize_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho da janela de an\xc3\x83\xc2\xa1lise */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho da janela de an\xc3\x83\xc2\xa1lise" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMachineLearning_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar machine learning */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar machine learning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MLConfidenceThreshold_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "0.99" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Threshold de confian\xc3\x83\xc2\xa7""a para ML */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Threshold de confian\xc3\x83\xc2\xa7""a para ML" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseEncryption_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar criptografia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar criptografia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EncryptionKey_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chave de criptografia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chave de criptografia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseIntegrityChecks_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar checksums de integridade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar checksums de integridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckInterval_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de verifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de verifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSecurityLogging_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar logging de seguran\xc3\x83\xc2\xa7""a */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar logging de seguran\xc3\x83\xc2\xa7""a" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAutomaticReporting_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar relat\xc3\x83\xc2\xb3rios autom\xc3\x83\xc2\xa1ticos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar relat\xc3\x83\xc2\xb3rios autom\xc3\x83\xc2\xa1ticos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bAntiCheatEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAntiCheatEnabled;
	static void NewProp_bUseServerValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseServerValidation;
	static void NewProp_bUseMovementValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMovementValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAllowedSpeed;
	static void NewProp_bUsePositionValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePositionValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxTeleportDistance;
	static void NewProp_bUseAbilityValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAbilityValidation;
	static void NewProp_bUseCooldownValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCooldownValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimingTolerance;
	static void NewProp_bUseDamageValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDamageValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDamageMultiplier;
	static void NewProp_bUseResourceValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseResourceValidation;
	static void NewProp_bUseBehavioralDetection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBehavioralDetection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuspiciousBehaviorThreshold;
	static void NewProp_bUseStatisticalAnalysis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseStatisticalAnalysis;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AnalysisWindowSize;
	static void NewProp_bUseMachineLearning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMachineLearning;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MLConfidenceThreshold;
	static void NewProp_bUseEncryption_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseEncryption;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EncryptionKey;
	static void NewProp_bUseIntegrityChecks_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseIntegrityChecks;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CheckInterval;
	static void NewProp_bUseSecurityLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSecurityLogging;
	static void NewProp_bUseAutomaticReporting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAutomaticReporting;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAntiCheatConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bAntiCheatEnabled_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bAntiCheatEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bAntiCheatEnabled = { "bAntiCheatEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bAntiCheatEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAntiCheatEnabled_MetaData), NewProp_bAntiCheatEnabled_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseServerValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseServerValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseServerValidation = { "bUseServerValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseServerValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseServerValidation_MetaData), NewProp_bUseServerValidation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMovementValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseMovementValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMovementValidation = { "bUseMovementValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMovementValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMovementValidation_MetaData), NewProp_bUseMovementValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxAllowedSpeed = { "MaxAllowedSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, MaxAllowedSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAllowedSpeed_MetaData), NewProp_MaxAllowedSpeed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUsePositionValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUsePositionValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUsePositionValidation = { "bUsePositionValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUsePositionValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePositionValidation_MetaData), NewProp_bUsePositionValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxTeleportDistance = { "MaxTeleportDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, MaxTeleportDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTeleportDistance_MetaData), NewProp_MaxTeleportDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAbilityValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseAbilityValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAbilityValidation = { "bUseAbilityValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAbilityValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAbilityValidation_MetaData), NewProp_bUseAbilityValidation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseCooldownValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseCooldownValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseCooldownValidation = { "bUseCooldownValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseCooldownValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCooldownValidation_MetaData), NewProp_bUseCooldownValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_TimingTolerance = { "TimingTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, TimingTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimingTolerance_MetaData), NewProp_TimingTolerance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseDamageValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseDamageValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseDamageValidation = { "bUseDamageValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseDamageValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDamageValidation_MetaData), NewProp_bUseDamageValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxDamageMultiplier = { "MaxDamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, MaxDamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDamageMultiplier_MetaData), NewProp_MaxDamageMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseResourceValidation_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseResourceValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseResourceValidation = { "bUseResourceValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseResourceValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseResourceValidation_MetaData), NewProp_bUseResourceValidation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseBehavioralDetection_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseBehavioralDetection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseBehavioralDetection = { "bUseBehavioralDetection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseBehavioralDetection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBehavioralDetection_MetaData), NewProp_bUseBehavioralDetection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_SuspiciousBehaviorThreshold = { "SuspiciousBehaviorThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, SuspiciousBehaviorThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuspiciousBehaviorThreshold_MetaData), NewProp_SuspiciousBehaviorThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseStatisticalAnalysis_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseStatisticalAnalysis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseStatisticalAnalysis = { "bUseStatisticalAnalysis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseStatisticalAnalysis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseStatisticalAnalysis_MetaData), NewProp_bUseStatisticalAnalysis_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_AnalysisWindowSize = { "AnalysisWindowSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, AnalysisWindowSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalysisWindowSize_MetaData), NewProp_AnalysisWindowSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMachineLearning_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseMachineLearning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMachineLearning = { "bUseMachineLearning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMachineLearning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMachineLearning_MetaData), NewProp_bUseMachineLearning_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MLConfidenceThreshold = { "MLConfidenceThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, MLConfidenceThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MLConfidenceThreshold_MetaData), NewProp_MLConfidenceThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseEncryption_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseEncryption = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseEncryption = { "bUseEncryption", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseEncryption_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseEncryption_MetaData), NewProp_bUseEncryption_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_EncryptionKey = { "EncryptionKey", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, EncryptionKey), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EncryptionKey_MetaData), NewProp_EncryptionKey_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseIntegrityChecks_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseIntegrityChecks = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseIntegrityChecks = { "bUseIntegrityChecks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseIntegrityChecks_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseIntegrityChecks_MetaData), NewProp_bUseIntegrityChecks_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_CheckInterval = { "CheckInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatConfiguration, CheckInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckInterval_MetaData), NewProp_CheckInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseSecurityLogging_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseSecurityLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseSecurityLogging = { "bUseSecurityLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseSecurityLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSecurityLogging_MetaData), NewProp_bUseSecurityLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAutomaticReporting_SetBit(void* Obj)
{
	((FAuracronAntiCheatConfiguration*)Obj)->bUseAutomaticReporting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAutomaticReporting = { "bUseAutomaticReporting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAutomaticReporting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAutomaticReporting_MetaData), NewProp_bUseAutomaticReporting_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bAntiCheatEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseServerValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMovementValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxAllowedSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUsePositionValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxTeleportDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAbilityValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseCooldownValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_TimingTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseDamageValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MaxDamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseResourceValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseBehavioralDetection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_SuspiciousBehaviorThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseStatisticalAnalysis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_AnalysisWindowSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseMachineLearning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_MLConfidenceThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseEncryption,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_EncryptionKey,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseIntegrityChecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_CheckInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseSecurityLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewProp_bUseAutomaticReporting,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
	nullptr,
	&NewStructOps,
	"AuracronAntiCheatConfiguration",
	Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::PropPointers),
	sizeof(FAuracronAntiCheatConfiguration),
	alignof(FAuracronAntiCheatConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAntiCheatConfiguration *************************************

// ********** Begin Delegate FOnCheatDetected ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics
{
	struct AuracronAntiCheatBridge_eventOnCheatDetected_Parms
	{
		FAuracronCheatDetection Detection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando cheat \xc3\x83\xc2\xa9 detectado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando cheat \xc3\x83\xc2\xa9 detectado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Detection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::NewProp_Detection = { "Detection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnCheatDetected_Parms, Detection), Z_Construct_UScriptStruct_FAuracronCheatDetection, METADATA_PARAMS(0, nullptr) }; // 578795733
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::NewProp_Detection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "OnCheatDetected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnCheatDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnCheatDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAntiCheatBridge::FOnCheatDetected_DelegateWrapper(const FMulticastScriptDelegate& OnCheatDetected, FAuracronCheatDetection Detection)
{
	struct AuracronAntiCheatBridge_eventOnCheatDetected_Parms
	{
		FAuracronCheatDetection Detection;
	};
	AuracronAntiCheatBridge_eventOnCheatDetected_Parms Parms;
	Parms.Detection=Detection;
	OnCheatDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCheatDetected ********************************************************

// ********** Begin Delegate FOnAntiCheatActionTaken ***********************************************
struct Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics
{
	struct AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms
	{
		FString PlayerID;
		EAuracronAntiCheatAction Action;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 tomada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 tomada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Action_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Action;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Action_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Action = { "Action", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms, Action), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction, METADATA_PARAMS(0, nullptr) }; // 4279590589
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms, Reason), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Action_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Action,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "OnAntiCheatActionTaken__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAntiCheatBridge::FOnAntiCheatActionTaken_DelegateWrapper(const FMulticastScriptDelegate& OnAntiCheatActionTaken, const FString& PlayerID, EAuracronAntiCheatAction Action, const FString& Reason)
{
	struct AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms
	{
		FString PlayerID;
		EAuracronAntiCheatAction Action;
		FString Reason;
	};
	AuracronAntiCheatBridge_eventOnAntiCheatActionTaken_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.Action=Action;
	Parms.Reason=Reason;
	OnAntiCheatActionTaken.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAntiCheatActionTaken *************************************************

// ********** Begin Delegate FOnPlayerBanned *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics
{
	struct AuracronAntiCheatBridge_eventOnPlayerBanned_Parms
	{
		FString PlayerID;
		int32 BanDurationHours;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegates para ban e kick\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates para ban e kick" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BanDurationHours;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnPlayerBanned_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_BanDurationHours = { "BanDurationHours", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnPlayerBanned_Parms, BanDurationHours), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnPlayerBanned_Parms, Reason), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_BanDurationHours,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "OnPlayerBanned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnPlayerBanned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnPlayerBanned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAntiCheatBridge::FOnPlayerBanned_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerBanned, const FString& PlayerID, int32 BanDurationHours, const FString& Reason)
{
	struct AuracronAntiCheatBridge_eventOnPlayerBanned_Parms
	{
		FString PlayerID;
		int32 BanDurationHours;
		FString Reason;
	};
	AuracronAntiCheatBridge_eventOnPlayerBanned_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.BanDurationHours=BanDurationHours;
	Parms.Reason=Reason;
	OnPlayerBanned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerBanned *********************************************************

// ********** Begin Delegate FOnPlayerKicked *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics
{
	struct AuracronAntiCheatBridge_eventOnPlayerKicked_Parms
	{
		FString PlayerID;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnPlayerKicked_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventOnPlayerKicked_Parms, Reason), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "OnPlayerKicked__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnPlayerKicked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::AuracronAntiCheatBridge_eventOnPlayerKicked_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAntiCheatBridge::FOnPlayerKicked_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerKicked, const FString& PlayerID, const FString& Reason)
{
	struct AuracronAntiCheatBridge_eventOnPlayerKicked_Parms
	{
		FString PlayerID;
		FString Reason;
	};
	AuracronAntiCheatBridge_eventOnPlayerKicked_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.Reason=Reason;
	OnPlayerKicked.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerKicked *********************************************************

// ********** Begin Class UAuracronAntiCheatBridge Function AnalyzeInputPatterns *******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics
{
	struct AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms
	{
		FString PlayerID;
		TArray<FString> InputSequence;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Analisar padr\xc3\x83\xc2\xb5""es de input\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analisar padr\xc3\x83\xc2\xb5""es de input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputSequence_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InputSequence_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InputSequence;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_InputSequence_Inner = { "InputSequence", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_InputSequence = { "InputSequence", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms, InputSequence), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputSequence_MetaData), NewProp_InputSequence_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_InputSequence_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_InputSequence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "AnalyzeInputPatterns", Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::AuracronAntiCheatBridge_eventAnalyzeInputPatterns_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execAnalyzeInputPatterns)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_TARRAY_REF(FString,Z_Param_Out_InputSequence);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AnalyzeInputPatterns(Z_Param_PlayerID,Z_Param_Out_InputSequence);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function AnalyzeInputPatterns *********************

// ********** Begin Class UAuracronAntiCheatBridge Function BanPlayer ******************************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics
{
	struct AuracronAntiCheatBridge_eventBanPlayer_Parms
	{
		FString PlayerID;
		int32 BanDurationHours;
		FString Reason;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Banir jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Banir jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BanDurationHours;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventBanPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_BanDurationHours = { "BanDurationHours", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventBanPlayer_Parms, BanDurationHours), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventBanPlayer_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventBanPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventBanPlayer_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_BanDurationHours,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "BanPlayer", Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::AuracronAntiCheatBridge_eventBanPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::AuracronAntiCheatBridge_eventBanPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execBanPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_BanDurationHours);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BanPlayer(Z_Param_PlayerID,Z_Param_BanDurationHours,Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function BanPlayer ********************************

// ********** Begin Class UAuracronAntiCheatBridge Function DecryptData ****************************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics
{
	struct AuracronAntiCheatBridge_eventDecryptData_Parms
	{
		FString EncryptedData;
		FString Key;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Security" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Descriptografar dados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descriptografar dados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EncryptedData_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EncryptedData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_EncryptedData = { "EncryptedData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventDecryptData_Parms, EncryptedData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EncryptedData_MetaData), NewProp_EncryptedData_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventDecryptData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventDecryptData_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_EncryptedData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "DecryptData", Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::AuracronAntiCheatBridge_eventDecryptData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::AuracronAntiCheatBridge_eventDecryptData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execDecryptData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EncryptedData);
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->DecryptData(Z_Param_EncryptedData,Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function DecryptData ******************************

// ********** Begin Class UAuracronAntiCheatBridge Function DetectSuspiciousBehavior ***************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics
{
	struct AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms
	{
		FString PlayerID;
		TMap<FString,float> BehaviorMetrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Detectar comportamento suspeito\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detectar comportamento suspeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorMetrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BehaviorMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BehaviorMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BehaviorMetrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics_ValueProp = { "BehaviorMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics_Key_KeyProp = { "BehaviorMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics = { "BehaviorMetrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms, BehaviorMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorMetrics_MetaData), NewProp_BehaviorMetrics_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_BehaviorMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "DetectSuspiciousBehavior", Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::AuracronAntiCheatBridge_eventDetectSuspiciousBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execDetectSuspiciousBehavior)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_BehaviorMetrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DetectSuspiciousBehavior(Z_Param_PlayerID,Z_Param_Out_BehaviorMetrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function DetectSuspiciousBehavior *****************

// ********** Begin Class UAuracronAntiCheatBridge Function EncryptData ****************************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics
{
	struct AuracronAntiCheatBridge_eventEncryptData_Parms
	{
		FString Data;
		FString Key;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Security" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criptografar dados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criptografar dados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Data;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventEncryptData_Parms, Data), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventEncryptData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventEncryptData_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "EncryptData", Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::AuracronAntiCheatBridge_eventEncryptData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::AuracronAntiCheatBridge_eventEncryptData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execEncryptData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Data);
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->EncryptData(Z_Param_Data,Z_Param_Key);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function EncryptData ******************************

// ********** Begin Class UAuracronAntiCheatBridge Function GenerateIntegrityHash ******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics
{
	struct AuracronAntiCheatBridge_eventGenerateIntegrityHash_Parms
	{
		FString Data;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Security" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Gerar hash de integridade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar hash de integridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Data;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventGenerateIntegrityHash_Parms, Data), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventGenerateIntegrityHash_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "GenerateIntegrityHash", Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::AuracronAntiCheatBridge_eventGenerateIntegrityHash_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::AuracronAntiCheatBridge_eventGenerateIntegrityHash_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execGenerateIntegrityHash)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Data);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GenerateIntegrityHash(Z_Param_Data);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function GenerateIntegrityHash ********************

// ********** Begin Class UAuracronAntiCheatBridge Function GetSecurityStatistics ******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics
{
	struct AuracronAntiCheatBridge_eventGetSecurityStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\x83\xc2\xadsticas de seguran\xc3\x83\xc2\xa7""a\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\x83\xc2\xadsticas de seguran\xc3\x83\xc2\xa7""a" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventGetSecurityStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "GetSecurityStatistics", Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::AuracronAntiCheatBridge_eventGetSecurityStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::AuracronAntiCheatBridge_eventGetSecurityStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execGetSecurityStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetSecurityStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function GetSecurityStatistics ********************

// ********** Begin Class UAuracronAntiCheatBridge Function KickPlayer *****************************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics
{
	struct AuracronAntiCheatBridge_eventKickPlayer_Parms
	{
		FString PlayerID;
		FString Reason;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Kickar jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Kickar jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventKickPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventKickPlayer_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventKickPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventKickPlayer_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "KickPlayer", Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::AuracronAntiCheatBridge_eventKickPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::AuracronAntiCheatBridge_eventKickPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execKickPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->KickPlayer(Z_Param_PlayerID,Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function KickPlayer *******************************

// ********** Begin Class UAuracronAntiCheatBridge Function MonitorPlayer **************************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics
{
	struct AuracronAntiCheatBridge_eventMonitorPlayer_Parms
	{
		FString PlayerID;
		bool bEnhancedMonitoring;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar jogador\n     */" },
#endif
		{ "CPP_Default_bEnhancedMonitoring", "false" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_bEnhancedMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnhancedMonitoring;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventMonitorPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_bEnhancedMonitoring_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventMonitorPlayer_Parms*)Obj)->bEnhancedMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_bEnhancedMonitoring = { "bEnhancedMonitoring", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventMonitorPlayer_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_bEnhancedMonitoring_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventMonitorPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventMonitorPlayer_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_bEnhancedMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "MonitorPlayer", Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::AuracronAntiCheatBridge_eventMonitorPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::AuracronAntiCheatBridge_eventMonitorPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execMonitorPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_UBOOL(Z_Param_bEnhancedMonitoring);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorPlayer(Z_Param_PlayerID,Z_Param_bEnhancedMonitoring);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function MonitorPlayer ****************************

// ********** Begin Class UAuracronAntiCheatBridge Function MonitorSuspiciousPerformance ***********
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics
{
	struct AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms
	{
		FString PlayerID;
		TMap<FString,float> PerformanceMetrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar performance suspeita\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar performance suspeita" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_PerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "MonitorSuspiciousPerformance", Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::AuracronAntiCheatBridge_eventMonitorSuspiciousPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execMonitorSuspiciousPerformance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_PerformanceMetrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorSuspiciousPerformance(Z_Param_PlayerID,Z_Param_Out_PerformanceMetrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function MonitorSuspiciousPerformance *************

// ********** Begin Class UAuracronAntiCheatBridge Function ReportCheatDetection *******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics
{
	struct AuracronAntiCheatBridge_eventReportCheatDetection_Parms
	{
		FAuracronCheatDetection Detection;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Reporting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reportar cheat detectado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reportar cheat detectado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Detection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Detection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_Detection = { "Detection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventReportCheatDetection_Parms, Detection), Z_Construct_UScriptStruct_FAuracronCheatDetection, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Detection_MetaData), NewProp_Detection_MetaData) }; // 578795733
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventReportCheatDetection_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventReportCheatDetection_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_Detection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ReportCheatDetection", Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::AuracronAntiCheatBridge_eventReportCheatDetection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::AuracronAntiCheatBridge_eventReportCheatDetection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execReportCheatDetection)
{
	P_GET_STRUCT_REF(FAuracronCheatDetection,Z_Param_Out_Detection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReportCheatDetection(Z_Param_Out_Detection);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ReportCheatDetection *********************

// ********** Begin Class UAuracronAntiCheatBridge Function StopMonitoringPlayer *******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics
{
	struct AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar monitoramento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar monitoramento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "StopMonitoringPlayer", Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::AuracronAntiCheatBridge_eventStopMonitoringPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execStopMonitoringPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopMonitoringPlayer(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function StopMonitoringPlayer *********************

// ********** Begin Class UAuracronAntiCheatBridge Function TakeAntiCheatAction ********************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics
{
	struct AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms
	{
		FString PlayerID;
		EAuracronAntiCheatAction Action;
		FString Reason;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Tomar a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tomar a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Action_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Action;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Action_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Action = { "Action", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms, Action), Z_Construct_UEnum_AuracronAntiCheatBridge_EAuracronAntiCheatAction, METADATA_PARAMS(0, nullptr) }; // 4279590589
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Action_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Action,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "TakeAntiCheatAction", Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::AuracronAntiCheatBridge_eventTakeAntiCheatAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execTakeAntiCheatAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_ENUM(EAuracronAntiCheatAction,Z_Param_Action);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TakeAntiCheatAction(Z_Param_PlayerID,EAuracronAntiCheatAction(Z_Param_Action),Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function TakeAntiCheatAction **********************

// ********** Begin Class UAuracronAntiCheatBridge Function ValidateAbilityUsage *******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics
{
	struct AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms
	{
		FString PlayerID;
		FString AbilityID;
		float Timestamp;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar uso de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar uso de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_AbilityID = { "AbilityID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms, AbilityID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityID_MetaData), NewProp_AbilityID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_AbilityID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ValidateAbilityUsage", Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::AuracronAntiCheatBridge_eventValidateAbilityUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execValidateAbilityUsage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilityID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAbilityUsage(Z_Param_PlayerID,Z_Param_AbilityID,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ValidateAbilityUsage *********************

// ********** Begin Class UAuracronAntiCheatBridge Function ValidateActionTiming *******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics
{
	struct AuracronAntiCheatBridge_eventValidateActionTiming_Parms
	{
		FString PlayerID;
		FString ActionType;
		float Timestamp;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar timing de a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar timing de a\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateActionTiming_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateActionTiming_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateActionTiming_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventValidateActionTiming_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventValidateActionTiming_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ValidateActionTiming", Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::AuracronAntiCheatBridge_eventValidateActionTiming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::AuracronAntiCheatBridge_eventValidateActionTiming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execValidateActionTiming)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateActionTiming(Z_Param_PlayerID,Z_Param_ActionType,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ValidateActionTiming *********************

// ********** Begin Class UAuracronAntiCheatBridge Function ValidateDamageDealt ********************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics
{
	struct AuracronAntiCheatBridge_eventValidateDamageDealt_Parms
	{
		FString PlayerID;
		FString TargetID;
		float DamageAmount;
		FString DamageSource;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar dano causado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar dano causado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageSource_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DamageSource;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateDamageDealt_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_TargetID = { "TargetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateDamageDealt_Parms, TargetID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetID_MetaData), NewProp_TargetID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateDamageDealt_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidateDamageDealt_Parms, DamageSource), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageSource_MetaData), NewProp_DamageSource_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventValidateDamageDealt_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventValidateDamageDealt_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_TargetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_DamageSource,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ValidateDamageDealt", Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::AuracronAntiCheatBridge_eventValidateDamageDealt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::AuracronAntiCheatBridge_eventValidateDamageDealt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execValidateDamageDealt)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_TargetID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_PROPERTY(FStrProperty,Z_Param_DamageSource);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateDamageDealt(Z_Param_PlayerID,Z_Param_TargetID,Z_Param_DamageAmount,Z_Param_DamageSource);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ValidateDamageDealt **********************

// ********** Begin Class UAuracronAntiCheatBridge Function ValidatePlayerMovement *****************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics
{
	struct AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms
	{
		FString PlayerID;
		FVector OldPosition;
		FVector NewPosition;
		float DeltaTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar movimento do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar movimento do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldPosition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_OldPosition = { "OldPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms, OldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldPosition_MetaData), NewProp_OldPosition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_NewPosition = { "NewPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms, NewPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewPosition_MetaData), NewProp_NewPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_OldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_NewPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_DeltaTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ValidatePlayerMovement", Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::AuracronAntiCheatBridge_eventValidatePlayerMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execValidatePlayerMovement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_OldPosition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_NewPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePlayerMovement(Z_Param_PlayerID,Z_Param_Out_OldPosition,Z_Param_Out_NewPosition,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ValidatePlayerMovement *******************

// ********** Begin Class UAuracronAntiCheatBridge Function ValidatePlayerPosition *****************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics
{
	struct AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms
	{
		FString PlayerID;
		FVector Position;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "ValidatePlayerPosition", Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::AuracronAntiCheatBridge_eventValidatePlayerPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execValidatePlayerPosition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePlayerPosition(Z_Param_PlayerID,Z_Param_Out_Position);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function ValidatePlayerPosition *******************

// ********** Begin Class UAuracronAntiCheatBridge Function VerifyClientIntegrity ******************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics
{
	struct AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms
	{
		FString PlayerID;
		FString IntegrityHash;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar integridade do cliente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar integridade do cliente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntegrityHash_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IntegrityHash;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_IntegrityHash = { "IntegrityHash", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms, IntegrityHash), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntegrityHash_MetaData), NewProp_IntegrityHash_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_IntegrityHash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "VerifyClientIntegrity", Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::AuracronAntiCheatBridge_eventVerifyClientIntegrity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execVerifyClientIntegrity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_IntegrityHash);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->VerifyClientIntegrity(Z_Param_PlayerID,Z_Param_IntegrityHash);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function VerifyClientIntegrity ********************

// ********** Begin Class UAuracronAntiCheatBridge Function VerifyIntegrityHash ********************
struct Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics
{
	struct AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms
	{
		FString Data;
		FString Hash;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON AntiCheat|Security" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar hash de integridade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar hash de integridade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Data_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Hash_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Data;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Hash;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_Data = { "Data", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms, Data), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Data_MetaData), NewProp_Data_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_Hash = { "Hash", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms, Hash), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Hash_MetaData), NewProp_Hash_MetaData) };
void Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms), &Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_Data,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_Hash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAntiCheatBridge, nullptr, "VerifyIntegrityHash", Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::AuracronAntiCheatBridge_eventVerifyIntegrityHash_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAntiCheatBridge::execVerifyIntegrityHash)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Data);
	P_GET_PROPERTY(FStrProperty,Z_Param_Hash);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->VerifyIntegrityHash(Z_Param_Data,Z_Param_Hash);
	P_NATIVE_END;
}
// ********** End Class UAuracronAntiCheatBridge Function VerifyIntegrityHash **********************

// ********** Begin Class UAuracronAntiCheatBridge *************************************************
void UAuracronAntiCheatBridge::StaticRegisterNativesUAuracronAntiCheatBridge()
{
	UClass* Class = UAuracronAntiCheatBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzeInputPatterns", &UAuracronAntiCheatBridge::execAnalyzeInputPatterns },
		{ "BanPlayer", &UAuracronAntiCheatBridge::execBanPlayer },
		{ "DecryptData", &UAuracronAntiCheatBridge::execDecryptData },
		{ "DetectSuspiciousBehavior", &UAuracronAntiCheatBridge::execDetectSuspiciousBehavior },
		{ "EncryptData", &UAuracronAntiCheatBridge::execEncryptData },
		{ "GenerateIntegrityHash", &UAuracronAntiCheatBridge::execGenerateIntegrityHash },
		{ "GetSecurityStatistics", &UAuracronAntiCheatBridge::execGetSecurityStatistics },
		{ "KickPlayer", &UAuracronAntiCheatBridge::execKickPlayer },
		{ "MonitorPlayer", &UAuracronAntiCheatBridge::execMonitorPlayer },
		{ "MonitorSuspiciousPerformance", &UAuracronAntiCheatBridge::execMonitorSuspiciousPerformance },
		{ "ReportCheatDetection", &UAuracronAntiCheatBridge::execReportCheatDetection },
		{ "StopMonitoringPlayer", &UAuracronAntiCheatBridge::execStopMonitoringPlayer },
		{ "TakeAntiCheatAction", &UAuracronAntiCheatBridge::execTakeAntiCheatAction },
		{ "ValidateAbilityUsage", &UAuracronAntiCheatBridge::execValidateAbilityUsage },
		{ "ValidateActionTiming", &UAuracronAntiCheatBridge::execValidateActionTiming },
		{ "ValidateDamageDealt", &UAuracronAntiCheatBridge::execValidateDamageDealt },
		{ "ValidatePlayerMovement", &UAuracronAntiCheatBridge::execValidatePlayerMovement },
		{ "ValidatePlayerPosition", &UAuracronAntiCheatBridge::execValidatePlayerPosition },
		{ "VerifyClientIntegrity", &UAuracronAntiCheatBridge::execVerifyClientIntegrity },
		{ "VerifyIntegrityHash", &UAuracronAntiCheatBridge::execVerifyIntegrityHash },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAntiCheatBridge;
UClass* UAuracronAntiCheatBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronAntiCheatBridge;
	if (!Z_Registration_Info_UClass_UAuracronAntiCheatBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAntiCheatBridge"),
			Z_Registration_Info_UClass_UAuracronAntiCheatBridge.InnerSingleton,
			StaticRegisterNativesUAuracronAntiCheatBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAntiCheatBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister()
{
	return UAuracronAntiCheatBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAntiCheatBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|AntiCheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema Anti-Cheat\n * Respons\xc3\x83\xc2\xa1vel pela detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o e preven\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o completa de cheating\n */" },
#endif
		{ "DisplayName", "AURACRON Anti-Cheat Bridge" },
		{ "IncludePath", "AuracronAntiCheatBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema Anti-Cheat\nRespons\xc3\x83\xc2\xa1vel pela detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o e preven\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o completa de cheating" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveDetections_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es ativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detec\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoredPlayers_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jogadores monitorados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jogadores monitorados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecurityStatistics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\x83\xc2\xadsticas de seguran\xc3\x83\xc2\xa7""a */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\x83\xc2\xadsticas de seguran\xc3\x83\xc2\xa7""a" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCheatDetected_MetaData[] = {
		{ "Category", "AURACRON AntiCheat|Events" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAntiCheatActionTaken_MetaData[] = {
		{ "Category", "AURACRON AntiCheat|Events" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerBanned_MetaData[] = {
		{ "Category", "AURACRON AntiCheat|Events" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerKicked_MetaData[] = {
		{ "Category", "AURACRON AntiCheat|Events" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveBans_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Armazenamento de bans e kicks\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armazenamento de bans e kicks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentKicks_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Public/AuracronAntiCheatBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AntiCheatConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveDetections_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveDetections;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MonitoredPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MonitoredPlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SecurityStatistics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SecurityStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SecurityStatistics;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCheatDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAntiCheatActionTaken;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerBanned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerKicked;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveBans_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveBans_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveBans;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RecentKicks_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentKicks;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_AnalyzeInputPatterns, "AnalyzeInputPatterns" }, // 2875761702
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_BanPlayer, "BanPlayer" }, // 1440657762
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_DecryptData, "DecryptData" }, // 994491612
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_DetectSuspiciousBehavior, "DetectSuspiciousBehavior" }, // 1727034744
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_EncryptData, "EncryptData" }, // 3276607047
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_GenerateIntegrityHash, "GenerateIntegrityHash" }, // 2503109022
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_GetSecurityStatistics, "GetSecurityStatistics" }, // 1177079667
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_KickPlayer, "KickPlayer" }, // 1996182688
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorPlayer, "MonitorPlayer" }, // 3926866031
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_MonitorSuspiciousPerformance, "MonitorSuspiciousPerformance" }, // 3239114297
		{ &Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature, "OnAntiCheatActionTaken__DelegateSignature" }, // 940218683
		{ &Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature, "OnCheatDetected__DelegateSignature" }, // 2148322576
		{ &Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature, "OnPlayerBanned__DelegateSignature" }, // 397843713
		{ &Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature, "OnPlayerKicked__DelegateSignature" }, // 3732171575
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ReportCheatDetection, "ReportCheatDetection" }, // 3209425465
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_StopMonitoringPlayer, "StopMonitoringPlayer" }, // 3577420464
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_TakeAntiCheatAction, "TakeAntiCheatAction" }, // 1776603821
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateAbilityUsage, "ValidateAbilityUsage" }, // 510487241
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateActionTiming, "ValidateActionTiming" }, // 1557474886
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidateDamageDealt, "ValidateDamageDealt" }, // 3092489456
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerMovement, "ValidatePlayerMovement" }, // 1005415226
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_ValidatePlayerPosition, "ValidatePlayerPosition" }, // 1873138533
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyClientIntegrity, "VerifyClientIntegrity" }, // 2206608516
		{ &Z_Construct_UFunction_UAuracronAntiCheatBridge_VerifyIntegrityHash, "VerifyIntegrityHash" }, // 306658053
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAntiCheatBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_AntiCheatConfiguration = { "AntiCheatConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, AntiCheatConfiguration), Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatConfiguration_MetaData), NewProp_AntiCheatConfiguration_MetaData) }; // 165712517
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveDetections_Inner = { "ActiveDetections", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronCheatDetection, METADATA_PARAMS(0, nullptr) }; // 578795733
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveDetections = { "ActiveDetections", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, ActiveDetections), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveDetections_MetaData), NewProp_ActiveDetections_MetaData) }; // 578795733
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_MonitoredPlayers_Inner = { "MonitoredPlayers", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_MonitoredPlayers = { "MonitoredPlayers", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, MonitoredPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoredPlayers_MetaData), NewProp_MonitoredPlayers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics_ValueProp = { "SecurityStatistics", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics_Key_KeyProp = { "SecurityStatistics_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics = { "SecurityStatistics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, SecurityStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecurityStatistics_MetaData), NewProp_SecurityStatistics_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnCheatDetected = { "OnCheatDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, OnCheatDetected), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnCheatDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCheatDetected_MetaData), NewProp_OnCheatDetected_MetaData) }; // 2148322576
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnAntiCheatActionTaken = { "OnAntiCheatActionTaken", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, OnAntiCheatActionTaken), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnAntiCheatActionTaken__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAntiCheatActionTaken_MetaData), NewProp_OnAntiCheatActionTaken_MetaData) }; // 940218683
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnPlayerBanned = { "OnPlayerBanned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, OnPlayerBanned), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerBanned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerBanned_MetaData), NewProp_OnPlayerBanned_MetaData) }; // 397843713
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnPlayerKicked = { "OnPlayerKicked", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, OnPlayerKicked), Z_Construct_UDelegateFunction_UAuracronAntiCheatBridge_OnPlayerKicked__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerKicked_MetaData), NewProp_OnPlayerKicked_MetaData) }; // 3732171575
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans_ValueProp = { "ActiveBans", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronBanInfo, METADATA_PARAMS(0, nullptr) }; // 4177949474
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans_Key_KeyProp = { "ActiveBans_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans = { "ActiveBans", nullptr, (EPropertyFlags)0x0040000000020001, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, ActiveBans), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveBans_MetaData), NewProp_ActiveBans_MetaData) }; // 4177949474
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_RecentKicks_Inner = { "RecentKicks", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronKickInfo, METADATA_PARAMS(0, nullptr) }; // 1667149363
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_RecentKicks = { "RecentKicks", nullptr, (EPropertyFlags)0x0040000000020001, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAntiCheatBridge, RecentKicks), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentKicks_MetaData), NewProp_RecentKicks_MetaData) }; // 1667149363
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_AntiCheatConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveDetections_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveDetections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_MonitoredPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_MonitoredPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_SecurityStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnCheatDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnAntiCheatActionTaken,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnPlayerBanned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_OnPlayerKicked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_ActiveBans,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_RecentKicks_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::NewProp_RecentKicks,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAntiCheatBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::ClassParams = {
	&UAuracronAntiCheatBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAntiCheatBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronAntiCheatBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAntiCheatBridge.OuterSingleton, Z_Construct_UClass_UAuracronAntiCheatBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAntiCheatBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronAntiCheatBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_AntiCheatConfiguration(TEXT("AntiCheatConfiguration"));
	static FName Name_MonitoredPlayers(TEXT("MonitoredPlayers"));
	const bool bIsValid = true
		&& Name_AntiCheatConfiguration == ClassReps[(int32)ENetFields_Private::AntiCheatConfiguration].Property->GetFName()
		&& Name_MonitoredPlayers == ClassReps[(int32)ENetFields_Private::MonitoredPlayers].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronAntiCheatBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAntiCheatBridge);
UAuracronAntiCheatBridge::~UAuracronAntiCheatBridge() {}
// ********** End Class UAuracronAntiCheatBridge ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronCheatType_StaticEnum, TEXT("EAuracronCheatType"), &Z_Registration_Info_UEnum_EAuracronCheatType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2592168363U) },
		{ EAuracronCheatSeverity_StaticEnum, TEXT("EAuracronCheatSeverity"), &Z_Registration_Info_UEnum_EAuracronCheatSeverity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1869528791U) },
		{ EAuracronAntiCheatAction_StaticEnum, TEXT("EAuracronAntiCheatAction"), &Z_Registration_Info_UEnum_EAuracronAntiCheatAction, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4279590589U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronBanInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronBanInfo_Statics::NewStructOps, TEXT("AuracronBanInfo"), &Z_Registration_Info_UScriptStruct_FAuracronBanInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBanInfo), 4177949474U) },
		{ FAuracronKickInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronKickInfo_Statics::NewStructOps, TEXT("AuracronKickInfo"), &Z_Registration_Info_UScriptStruct_FAuracronKickInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronKickInfo), 1667149363U) },
		{ FAuracronCheatDetection::StaticStruct, Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics::NewStructOps, TEXT("AuracronCheatDetection"), &Z_Registration_Info_UScriptStruct_FAuracronCheatDetection, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCheatDetection), 578795733U) },
		{ FAuracronAntiCheatConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics::NewStructOps, TEXT("AuracronAntiCheatConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronAntiCheatConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAntiCheatConfiguration), 165712517U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAntiCheatBridge, UAuracronAntiCheatBridge::StaticClass, TEXT("UAuracronAntiCheatBridge"), &Z_Registration_Info_UClass_UAuracronAntiCheatBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAntiCheatBridge), 1220088860U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_1754107174(TEXT("/Script/AuracronAntiCheatBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h__Script_AuracronAntiCheatBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
