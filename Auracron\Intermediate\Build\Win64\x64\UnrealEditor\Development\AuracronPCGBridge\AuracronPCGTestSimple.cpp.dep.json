{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\auracron\\source\\auracronpcgbridge\\private\\auracronpcgtestsimple.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronpcgbridge\\definitions.auracronpcgbridge.h", "c:\\aura\\projeto\\auracron\\source\\auracronpcgbridge\\public\\auracronpcgtestsimple.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronpcgbridge\\uht\\auracronpcgtestsimple.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}