// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "CommunityHealingManager.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_CommunityHealingManager_generated_h
#error "CommunityHealingManager.generated.h already included, missing '#pragma once' in CommunityHealingManager.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_CommunityHealingManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EHealingSessionType : uint8;
struct FHealingSession;

// ********** Begin ScriptStruct FHealingSession ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_36_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHealingSession_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHealingSession;
// ********** End ScriptStruct FHealingSession *****************************************************

// ********** Begin ScriptStruct FHealerProfile ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_84_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHealerProfile_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHealerProfile;
// ********** End ScriptStruct FHealerProfile ******************************************************

// ********** Begin ScriptStruct FPlayerHealingHistory *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_127_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerHealingHistory;
// ********** End ScriptStruct FPlayerHealingHistory ***********************************************

// ********** Begin Class UCommunityHealingManager *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetHealerRating); \
	DECLARE_FUNCTION(execGetTopHealers); \
	DECLARE_FUNCTION(execGetTotalHealingSessionsCompleted); \
	DECLARE_FUNCTION(execGetCommunityHealingEffectiveness); \
	DECLARE_FUNCTION(execUpdateHealingProgress); \
	DECLARE_FUNCTION(execIsPlayerInHealingSession); \
	DECLARE_FUNCTION(execGetHealingSession); \
	DECLARE_FUNCTION(execGetActiveHealingSessions); \
	DECLARE_FUNCTION(execFindBestMatchedHealer); \
	DECLARE_FUNCTION(execFindAvailableHealers); \
	DECLARE_FUNCTION(execUnregisterHealer); \
	DECLARE_FUNCTION(execRegisterHealer); \
	DECLARE_FUNCTION(execCancelHealingSession); \
	DECLARE_FUNCTION(execCompleteHealingSession); \
	DECLARE_FUNCTION(execAddHealerToSession); \
	DECLARE_FUNCTION(execInitiateHealingSession);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UCommunityHealingManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUCommunityHealingManager(); \
	friend struct Z_Construct_UClass_UCommunityHealingManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UCommunityHealingManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UCommunityHealingManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UCommunityHealingManager_NoRegister) \
	DECLARE_SERIALIZER(UCommunityHealingManager)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UCommunityHealingManager(UCommunityHealingManager&&) = delete; \
	UCommunityHealingManager(const UCommunityHealingManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UCommunityHealingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UCommunityHealingManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UCommunityHealingManager) \
	NO_API virtual ~UCommunityHealingManager();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_142_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h_145_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UCommunityHealingManager;

// ********** End Class UCommunityHealingManager ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h

// ********** Begin Enum EHealingSessionType *******************************************************
#define FOREACH_ENUM_EHEALINGSESSIONTYPE(op) \
	op(EHealingSessionType::PeerSupport) \
	op(EHealingSessionType::Mentorship) \
	op(EHealingSessionType::GroupTherapy) \
	op(EHealingSessionType::CrisisIntervention) \
	op(EHealingSessionType::CelebrationCircle) 

enum class EHealingSessionType : uint8;
template<> struct TIsUEnumClass<EHealingSessionType> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHealingSessionType>();
// ********** End Enum EHealingSessionType *********************************************************

// ********** Begin Enum EHealingStatus ************************************************************
#define FOREACH_ENUM_EHEALINGSTATUS(op) \
	op(EHealingStatus::Pending) \
	op(EHealingStatus::Active) \
	op(EHealingStatus::Completed) \
	op(EHealingStatus::Cancelled) \
	op(EHealingStatus::Failed) 

enum class EHealingStatus : uint8;
template<> struct TIsUEnumClass<EHealingStatus> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHealingStatus>();
// ********** End Enum EHealingStatus **************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
