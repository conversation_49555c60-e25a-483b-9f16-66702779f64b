﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Material System Header
// Bridge 2.11: PCG Framework - Material Assignment

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"
#include "Metadata/PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
// #include "Components/StaticMeshComponent.h" // Removido - nÃ£o necessÃ¡rio
#include "Engine/StaticMesh.h"
#include "Curves/CurveFloat.h"
#include "Curves/CurveLinearColor.h"

#include "AuracronPCGMaterialSystem.generated.h"

// Material selection modes
UENUM(BlueprintType)
enum class EAuracronPCGMaterialSelectionMode : uint8
{
    ByAttribute             UMETA(DisplayName = "By Attribute"),
    ByDistance              UMETA(DisplayName = "By Distance"),
    ByHeight                UMETA(DisplayName = "By Height"),
    BySlope                 UMETA(DisplayName = "By Slope"),
    ByNoise                 UMETA(DisplayName = "By Noise"),
    ByDensity               UMETA(DisplayName = "By Density"),
    ByProximity             UMETA(DisplayName = "By Proximity"),
    ByRandom                UMETA(DisplayName = "By Random"),
    ByBiome                 UMETA(DisplayName = "By Biome"),
    ByLighting              UMETA(DisplayName = "By Lighting"),
    ByWeather               UMETA(DisplayName = "By Weather"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Material blending modes
UENUM(BlueprintType)
enum class EAuracronPCGMaterialBlendMode : uint8
{
    Replace                 UMETA(DisplayName = "Replace"),
    Additive                UMETA(DisplayName = "Additive"),
    Multiply                UMETA(DisplayName = "Multiply"),
    Overlay                 UMETA(DisplayName = "Overlay"),
    SoftLight               UMETA(DisplayName = "Soft Light"),
    HardLight               UMETA(DisplayName = "Hard Light"),
    Screen                  UMETA(DisplayName = "Screen"),
    ColorBurn               UMETA(DisplayName = "Color Burn"),
    ColorDodge              UMETA(DisplayName = "Color Dodge"),
    Difference              UMETA(DisplayName = "Difference"),
    Exclusion               UMETA(DisplayName = "Exclusion"),
    LinearBurn              UMETA(DisplayName = "Linear Burn"),
    LinearDodge             UMETA(DisplayName = "Linear Dodge"),
    VividLight              UMETA(DisplayName = "Vivid Light"),
    LinearLight             UMETA(DisplayName = "Linear Light"),
    PinLight                UMETA(DisplayName = "Pin Light"),
    Custom                  UMETA(DisplayName = "Custom")
};

// UV generation modes
UENUM(BlueprintType)
enum class EAuracronPCGUVGenerationMode : uint8
{
    WorldSpace              UMETA(DisplayName = "World Space"),
    LocalSpace              UMETA(DisplayName = "Local Space"),
    Planar                  UMETA(DisplayName = "Planar"),
    Cylindrical             UMETA(DisplayName = "Cylindrical"),
    Spherical               UMETA(DisplayName = "Spherical"),
    Box                     UMETA(DisplayName = "Box"),
    Triplanar               UMETA(DisplayName = "Triplanar"),
    FromAttribute           UMETA(DisplayName = "From Attribute"),
    Procedural              UMETA(DisplayName = "Procedural"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Texture coordinate channels
UENUM(BlueprintType)
enum class EAuracronPCGTextureCoordinateChannel : uint8
{
    UV0                     UMETA(DisplayName = "UV Channel 0"),
    UV1                     UMETA(DisplayName = "UV Channel 1"),
    UV2                     UMETA(DisplayName = "UV Channel 2"),
    UV3                     UMETA(DisplayName = "UV Channel 3"),
    UV4                     UMETA(DisplayName = "UV Channel 4"),
    UV5                     UMETA(DisplayName = "UV Channel 5"),
    UV6                     UMETA(DisplayName = "UV Channel 6"),
    UV7                     UMETA(DisplayName = "UV Channel 7")
};

// Material parameter types
UENUM(BlueprintType)
enum class EAuracronPCGMaterialParameterType : uint8
{
    Scalar                  UMETA(DisplayName = "Scalar"),
    Vector                  UMETA(DisplayName = "Vector"),
    Color                   UMETA(DisplayName = "Color"),
    Texture                 UMETA(DisplayName = "Texture"),
    Boolean                 UMETA(DisplayName = "Boolean"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// MATERIAL SELECTION DESCRIPTOR
// =============================================================================

/**
 * Material Selection Descriptor
 * Describes parameters for material selection and assignment
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGMaterialSelectionDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    EAuracronPCGMaterialSelectionMode SelectionMode = EAuracronPCGMaterialSelectionMode::ByAttribute;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    FString AttributeName = TEXT("MaterialType");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialOptions;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    TArray<FString> MaterialTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Selection")
    TArray<float> SelectionWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Based", meta = (EditCondition = "SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance"))
    FVector ReferenceLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Based", meta = (EditCondition = "SelectionMode == EAuracronPCGMaterialSelectionMode::ByDistance"))
    TArray<float> DistanceThresholds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Height Based", meta = (EditCondition = "SelectionMode == EAuracronPCGMaterialSelectionMode::ByHeight"))
    TArray<float> HeightThresholds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slope Based", meta = (EditCondition = "SelectionMode == EAuracronPCGMaterialSelectionMode::BySlope"))
    TArray<float> SlopeThresholds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random")
    int32 RandomSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Random")
    bool bDeterministicSelection = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fallback")
    TSoftObjectPtr<UMaterialInterface> FallbackMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseWeightedSelection = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bAllowMaterialBlending = false;

    FAuracronPCGMaterialSelectionDescriptor()
    {
        SelectionMode = EAuracronPCGMaterialSelectionMode::ByAttribute;
        AttributeName = TEXT("MaterialType");
        ReferenceLocation = FVector::ZeroVector;
        RandomSeed = 12345;
        bDeterministicSelection = true;
        bUseWeightedSelection = false;
        bAllowMaterialBlending = false;
    }
};

// =============================================================================
// MATERIAL BLENDING DESCRIPTOR
// =============================================================================

/**
 * Material Blending Descriptor
 * Describes parameters for material blending operations
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGMaterialBlendingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    EAuracronPCGMaterialBlendMode BlendMode = EAuracronPCGMaterialBlendMode::Replace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    float BlendStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    bool bUseBlendMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending", meta = (EditCondition = "bUseBlendMask"))
    TSoftObjectPtr<UTexture2D> BlendMask;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending", meta = (EditCondition = "bUseBlendMask"))
    bool bInvertBlendMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    float TransitionWidth = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    bool bSmoothTransition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
    TSoftObjectPtr<UCurveFloat> TransitionCurve;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bPreserveOriginalAlpha = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalizeBlendWeights = true;

    FAuracronPCGMaterialBlendingDescriptor()
    {
        BlendMode = EAuracronPCGMaterialBlendMode::Replace;
        BlendStrength = 1.0f;
        bUseBlendMask = false;
        bInvertBlendMask = false;
        TransitionWidth = 0.1f;
        bSmoothTransition = true;
        bPreserveOriginalAlpha = false;
        bNormalizeBlendWeights = true;
    }
};

// =============================================================================
// UV GENERATION DESCRIPTOR
// =============================================================================

/**
 * UV Generation Descriptor
 * Describes parameters for UV coordinate generation
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGUVGenerationDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation")
    EAuracronPCGUVGenerationMode GenerationMode = EAuracronPCGUVGenerationMode::WorldSpace;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation")
    EAuracronPCGTextureCoordinateChannel TargetChannel = EAuracronPCGTextureCoordinateChannel::UV0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale")
    FVector2D UVScale = FVector2D(1.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale")
    FVector2D UVOffset = FVector2D(0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scale")
    float UVRotation = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projection", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::Planar"))
    FVector ProjectionAxis = FVector::UpVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projection", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::Cylindrical || GenerationMode == EAuracronPCGUVGenerationMode::Spherical"))
    FVector ProjectionCenter = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projection", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::Cylindrical"))
    float CylinderRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projection", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::Spherical"))
    float SphereRadius = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Triplanar", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::Triplanar"))
    float TriplanarBlendSharpness = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::FromAttribute"))
    FString UAttributeName = TEXT("U");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute", meta = (EditCondition = "GenerationMode == EAuracronPCGUVGenerationMode::FromAttribute"))
    FString VAttributeName = TEXT("V");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalizeUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bWrapUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bFlipU = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bFlipV = false;

    FAuracronPCGUVGenerationDescriptor()
    {
        GenerationMode = EAuracronPCGUVGenerationMode::WorldSpace;
        TargetChannel = EAuracronPCGTextureCoordinateChannel::UV0;
        UVScale = FVector2D(1.0f, 1.0f);
        UVOffset = FVector2D(0.0f, 0.0f);
        UVRotation = 0.0f;
        ProjectionAxis = FVector::UpVector;
        ProjectionCenter = FVector::ZeroVector;
        CylinderRadius = 100.0f;
        SphereRadius = 100.0f;
        TriplanarBlendSharpness = 1.0f;
        UAttributeName = TEXT("U");
        VAttributeName = TEXT("V");
        bNormalizeUVs = true;
        bWrapUVs = true;
        bFlipU = false;
        bFlipV = false;
    }
};

// =============================================================================
// MATERIAL PARAMETER DESCRIPTOR
// =============================================================================

/**
 * Material Parameter Descriptor
 * Describes parameters for dynamic material parameter assignment
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGMaterialParameterDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    FString ParameterName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    EAuracronPCGMaterialParameterType ParameterType = EAuracronPCGMaterialParameterType::Scalar;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    FString SourceAttributeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scalar", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Scalar"))
    float ScalarValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scalar", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Scalar"))
    FVector2D ScalarRange = FVector2D(0.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vector", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Vector"))
    FVector VectorValue = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Color"))
    FLinearColor ColorValue = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Color"))
    TSoftObjectPtr<UCurveLinearColor> ColorCurve;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Texture"))
    TSoftObjectPtr<UTexture> TextureValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Boolean", meta = (EditCondition = "ParameterType == EAuracronPCGMaterialParameterType::Boolean"))
    bool BooleanValue = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseAttributeAsSource = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bNormalizeValue = false;

    FAuracronPCGMaterialParameterDescriptor()
    {
        ParameterType = EAuracronPCGMaterialParameterType::Scalar;
        ScalarValue = 0.0f;
        ScalarRange = FVector2D(0.0f, 1.0f);
        VectorValue = FVector::ZeroVector;
        ColorValue = FLinearColor::White;
        BooleanValue = false;
        bUseAttributeAsSource = true;
        bNormalizeValue = false;
    }
};

// =============================================================================
// ADVANCED MATERIAL SELECTOR
// =============================================================================

/**
 * Advanced Material Selector
 * Selects materials based on various criteria and attributes
 */

public:
    UAuracronPCGAdvancedMaterialSelectorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGAdvancedMaterialSelectorSettings();

    // Material selection configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Selection")
    FAuracronPCGMaterialSelectionDescriptor SelectionDescriptor;

    // Multiple selection criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Criteria")
    bool bUseMultipleCriteria = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Criteria", meta = (EditCondition = "bUseMultipleCriteria"))
    TArray<FAuracronPCGMaterialSelectionDescriptor> AdditionalCriteria;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Criteria", meta = (EditCondition = "bUseMultipleCriteria"))
    TArray<float> CriteriaWeights;

    // Material variation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    bool bEnableMaterialVariation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bEnableMaterialVariation"))
    float VariationStrength = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (EditCondition = "bEnableMaterialVariation"))
    int32 VariationSeed = 54321;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputSelectionInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString MaterialIndexAttribute = TEXT("MaterialIndex");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString MaterialNameAttribute = TEXT("MaterialName");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGAdvancedMaterialSelectorElement, UAuracronPCGAdvancedMaterialSelectorSettings)

// =============================================================================
// MATERIAL BLENDER
// =============================================================================

/**
 * Material Blender
 * Blends multiple materials using various blending modes and masks
 */

public:
    UAuracronPCGMaterialBlenderSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGMaterialBlenderSettings();

    // Blending configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blending")
    FAuracronPCGMaterialBlendingDescriptor BlendingDescriptor;

    // Source materials
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TArray<TSoftObjectPtr<UMaterialInterface>> SourceMaterials;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TArray<float> MaterialWeights;

    // Blending masks
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masks")
    bool bUseAttributeAsMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masks", meta = (EditCondition = "bUseAttributeAsMask"))
    FString MaskAttributeName = TEXT("BlendMask");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masks")
    bool bUseProceduralMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masks", meta = (EditCondition = "bUseProceduralMask"))
    float MaskNoiseScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Masks", meta = (EditCondition = "bUseProceduralMask"))
    int32 MaskNoiseSeed = 98765;

    // Advanced blending
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bCreateDynamicMaterial = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bOptimizeBlending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    int32 MaxBlendLayers = 4;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputBlendWeights = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString BlendWeightAttribute = TEXT("BlendWeight");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGMaterialBlenderElement, UAuracronPCGMaterialBlenderSettings)

// =============================================================================
// UV COORDINATE GENERATOR
// =============================================================================

/**
 * UV Coordinate Generator
 * Generates UV coordinates using various projection methods
 */

public:
    UAuracronPCGUVCoordinateGeneratorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGUVCoordinateGeneratorSettings();

    // UV generation configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UV Generation")
    FAuracronPCGUVGenerationDescriptor UVDescriptor;

    // Multiple UV channels
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Channel")
    bool bGenerateMultipleChannels = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multi-Channel", meta = (EditCondition = "bGenerateMultipleChannels"))
    TArray<FAuracronPCGUVGenerationDescriptor> AdditionalChannels;

    // UV animation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableUVAnimation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (EditCondition = "bEnableUVAnimation"))
    FVector2D AnimationSpeed = FVector2D(0.1f, 0.1f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (EditCondition = "bEnableUVAnimation"))
    bool bUseTimeBasedAnimation = true;

    // UV distortion
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distortion")
    bool bApplyUVDistortion = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distortion", meta = (EditCondition = "bApplyUVDistortion"))
    float DistortionStrength = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distortion", meta = (EditCondition = "bApplyUVDistortion"))
    float DistortionScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distortion", meta = (EditCondition = "bApplyUVDistortion"))
    int32 DistortionSeed = 13579;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputUVAttributes = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString UCoordinateAttribute = TEXT("U");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString VCoordinateAttribute = TEXT("V");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGUVCoordinateGeneratorElement, UAuracronPCGUVCoordinateGeneratorSettings)

// =============================================================================
// MATERIAL PARAMETER CONTROLLER
// =============================================================================

/**
 * Material Parameter Controller
 * Controls dynamic material parameters based on point attributes
 */

public:
    UAuracronPCGMaterialParameterControllerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGMaterialParameterControllerSettings();

    // Parameter configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TArray<FAuracronPCGMaterialParameterDescriptor> ParameterDescriptors;

    // Target material
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
    TSoftObjectPtr<UMaterialInterface> TargetMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
    bool bCreateMaterialInstances = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
    bool bUseMaterialParameterCollection = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target", meta = (EditCondition = "bUseMaterialParameterCollection"))
    TSoftObjectPtr<UMaterialParameterCollection> ParameterCollection;

    // Parameter animation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    bool bEnableParameterAnimation = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (EditCondition = "bEnableParameterAnimation"))
    float AnimationSpeed = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation", meta = (EditCondition = "bEnableParameterAnimation"))
    bool bLoopAnimation = true;

    // Parameter curves
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curves")
    bool bUseParameterCurves = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curves", meta = (EditCondition = "bUseParameterCurves"))
    TMap<FString, TSoftObjectPtr<UCurveFloat>> ScalarCurves;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curves", meta = (EditCondition = "bUseParameterCurves"))
    TMap<FString, TSoftObjectPtr<UCurveLinearColor>> ColorCurves;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputParameterValues = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputMaterialInstances = true;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGMaterialParameterControllerElement, UAuracronPCGMaterialParameterControllerSettings)

// =============================================================================
// MATERIAL SYSTEM UTILITIES
// =============================================================================

/**
 * Material System Utilities
 * Utility functions for material assignment and manipulation
 */

public:
    // Material selection utilities
    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static UMaterialInterface* SelectMaterial(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static TArray<UMaterialInterface*> SelectMultipleMaterials(const FPCGPoint& Point, const TArray<FAuracronPCGMaterialSelectionDescriptor>& SelectionDescriptors, const TArray<float>& Weights);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static int32 GetMaterialIndexByAttribute(const FPCGPoint& Point, const FString& AttributeName, const TArray<FString>& MaterialTags);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static UMaterialInterface* GetMaterialByDistance(const FVector& Position, const FVector& ReferenceLocation, const TArray<float>& DistanceThresholds, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials);

    // Material blending utilities
    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static UMaterialInstanceDynamic* BlendMaterials(const TArray<UMaterialInterface*>& SourceMaterials, const TArray<float>& BlendWeights, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static float CalculateBlendWeight(const FPCGPoint& Point, const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static UMaterialInstanceDynamic* CreateDynamicMaterialInstance(UMaterialInterface* BaseMaterial, const TArray<FAuracronPCGMaterialParameterDescriptor>& Parameters);

    // UV generation utilities
    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector2D GenerateUVCoordinates(const FVector& Position, const FAuracronPCGUVGenerationDescriptor& UVDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector2D GenerateWorldSpaceUV(const FVector& Position, const FVector2D& Scale, const FVector2D& Offset);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector2D GeneratePlanarUV(const FVector& Position, const FVector& ProjectionAxis, const FVector2D& Scale);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector2D GenerateCylindricalUV(const FVector& Position, const FVector& Center, float Radius, const FVector& Axis);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector2D GenerateSphericalUV(const FVector& Position, const FVector& Center, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static TArray<FVector2D> GenerateTriplanarUV(const FVector& Position, const FVector& Normal, float BlendSharpness);

    // Parameter utilities
    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static void SetMaterialParameter(UMaterialInstanceDynamic* MaterialInstance, const FAuracronPCGMaterialParameterDescriptor& ParameterDescriptor, const FPCGPoint& Point);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static float GetAttributeAsFloat(const FPCGPoint& Point, const FString& AttributeName, float DefaultValue = 0.0f);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FVector GetAttributeAsVector(const FPCGPoint& Point, const FString& AttributeName, const FVector& DefaultValue = FVector::ZeroVector);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FLinearColor GetAttributeAsColor(const FPCGPoint& Point, const FString& AttributeName, const FLinearColor& DefaultValue = FLinearColor::White);

    // Validation utilities
    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static bool ValidateMaterialSelectionDescriptor(const FAuracronPCGMaterialSelectionDescriptor& SelectionDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static bool ValidateMaterialBlendingDescriptor(const FAuracronPCGMaterialBlendingDescriptor& BlendingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static bool ValidateUVGenerationDescriptor(const FAuracronPCGUVGenerationDescriptor& UVDescriptor);

    UFUNCTION(BlueprintCallable, Category = "Material System Utils")
    static FAuracronPCGMaterialSelectionDescriptor CreateDefaultMaterialSelectionDescriptor(EAuracronPCGMaterialSelectionMode SelectionMode);
};

// Namespace for material system utility functions
namespace AuracronPCGMaterialSystemUtils
{
    AURACRONPCGBRIDGE_API UMaterialInterface* LoadMaterialSafe(const TSoftObjectPtr<UMaterialInterface>& MaterialPtr);
    AURACRONPCGBRIDGE_API UTexture* LoadTextureSafe(const TSoftObjectPtr<UTexture>& TexturePtr);
    AURACRONPCGBRIDGE_API UCurveFloat* LoadCurveSafe(const TSoftObjectPtr<UCurveFloat>& CurvePtr);

    AURACRONPCGBRIDGE_API float EvaluateSelectionCriteria(const FPCGPoint& Point, const FAuracronPCGMaterialSelectionDescriptor& Descriptor);
    AURACRONPCGBRIDGE_API float CalculateHeightFactor(const FVector& Position, const TArray<float>& HeightThresholds);
    AURACRONPCGBRIDGE_API float CalculateSlopeFactor(const FVector& Normal, const TArray<float>& SlopeThresholds);
    AURACRONPCGBRIDGE_API float CalculateDistanceFactor(const FVector& Position, const FVector& Reference, const TArray<float>& DistanceThresholds);

    AURACRONPCGBRIDGE_API FVector2D ApplyUVTransform(const FVector2D& UV, const FVector2D& Scale, const FVector2D& Offset, float Rotation);
    AURACRONPCGBRIDGE_API FVector2D WrapUVCoordinates(const FVector2D& UV, bool bWrapU, bool bWrapV);
    AURACRONPCGBRIDGE_API FVector2D FlipUVCoordinates(const FVector2D& UV, bool bFlipU, bool bFlipV);

    AURACRONPCGBRIDGE_API float BlendMaterialValues(float ValueA, float ValueB, EAuracronPCGMaterialBlendMode BlendMode, float BlendFactor);
    AURACRONPCGBRIDGE_API FLinearColor BlendMaterialColors(const FLinearColor& ColorA, const FLinearColor& ColorB, EAuracronPCGMaterialBlendMode BlendMode, float BlendFactor);

    AURACRONPCGBRIDGE_API void SetScalarParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, float Value);
    AURACRONPCGBRIDGE_API void SetVectorParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, const FVector& Value);
    AURACRONPCGBRIDGE_API void SetColorParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, const FLinearColor& Value);
    AURACRONPCGBRIDGE_API void SetTextureParameter(UMaterialInstanceDynamic* MaterialInstance, const FString& ParameterName, UTexture* Value);

    AURACRONPCGBRIDGE_API FString GenerateMaterialInstanceName(const FString& BaseName, int32 Index);
    AURACRONPCGBRIDGE_API bool IsMaterialCompatible(UMaterialInterface* Material, const TArray<FString>& RequiredParameters);
    AURACRONPCGBRIDGE_API TArray<FString> GetMaterialParameterNames(UMaterialInterface* Material);
}
