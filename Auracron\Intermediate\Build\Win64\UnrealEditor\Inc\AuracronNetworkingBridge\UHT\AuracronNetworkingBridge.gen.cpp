// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronNetworkingBridge.h"
#include "AuracronEOSBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronNetworkingBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONEOSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSessionConfiguration();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronNetworkingBridge();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState();
AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature();
AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature();
AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature();
AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature();
AURACRONNETWORKINGBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerInfo();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronReplicationConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronNetworkingBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronNetworkConnectionType ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronNetworkConnectionType;
static UEnum* EAuracronNetworkConnectionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronNetworkConnectionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronNetworkConnectionType>()
{
	return EAuracronNetworkConnectionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Client.DisplayName", "Client" },
		{ "Client.Name", "EAuracronNetworkConnectionType::Client" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para tipos de conex\xc3\xa3o de rede\n */" },
#endif
		{ "Dedicated.DisplayName", "Dedicated Server" },
		{ "Dedicated.Name", "EAuracronNetworkConnectionType::Dedicated" },
		{ "Listen.DisplayName", "Listen Server" },
		{ "Listen.Name", "EAuracronNetworkConnectionType::Listen" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronNetworkConnectionType::None" },
		{ "Standalone.DisplayName", "Standalone" },
		{ "Standalone.Name", "EAuracronNetworkConnectionType::Standalone" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para tipos de conex\xc3\xa3o de rede" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronNetworkConnectionType::None", (int64)EAuracronNetworkConnectionType::None },
		{ "EAuracronNetworkConnectionType::Listen", (int64)EAuracronNetworkConnectionType::Listen },
		{ "EAuracronNetworkConnectionType::Dedicated", (int64)EAuracronNetworkConnectionType::Dedicated },
		{ "EAuracronNetworkConnectionType::Client", (int64)EAuracronNetworkConnectionType::Client },
		{ "EAuracronNetworkConnectionType::Standalone", (int64)EAuracronNetworkConnectionType::Standalone },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronNetworkConnectionType",
	"EAuracronNetworkConnectionType",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronNetworkConnectionType.InnerSingleton;
}
// ********** End Enum EAuracronNetworkConnectionType **********************************************

// ********** Begin Enum EAuracronSessionState *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSessionState;
static UEnum* EAuracronSessionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSessionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronSessionState"));
	}
	return Z_Registration_Info_UEnum_EAuracronSessionState.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronSessionState>()
{
	return EAuracronSessionState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para estado da sess\xc3\xa3o\n */" },
#endif
		{ "Creating.DisplayName", "Creating" },
		{ "Creating.Name", "EAuracronSessionState::Creating" },
		{ "Disconnected.DisplayName", "Disconnected" },
		{ "Disconnected.Name", "EAuracronSessionState::Disconnected" },
		{ "Ending.DisplayName", "Ending" },
		{ "Ending.Name", "EAuracronSessionState::Ending" },
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronSessionState::Error" },
		{ "InGame.DisplayName", "In Game" },
		{ "InGame.Name", "EAuracronSessionState::InGame" },
		{ "InLobby.DisplayName", "In Lobby" },
		{ "InLobby.Name", "EAuracronSessionState::InLobby" },
		{ "Joining.DisplayName", "Joining" },
		{ "Joining.Name", "EAuracronSessionState::Joining" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronSessionState::None" },
		{ "Searching.DisplayName", "Searching" },
		{ "Searching.Name", "EAuracronSessionState::Searching" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para estado da sess\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSessionState::None", (int64)EAuracronSessionState::None },
		{ "EAuracronSessionState::Creating", (int64)EAuracronSessionState::Creating },
		{ "EAuracronSessionState::Searching", (int64)EAuracronSessionState::Searching },
		{ "EAuracronSessionState::Joining", (int64)EAuracronSessionState::Joining },
		{ "EAuracronSessionState::InLobby", (int64)EAuracronSessionState::InLobby },
		{ "EAuracronSessionState::InGame", (int64)EAuracronSessionState::InGame },
		{ "EAuracronSessionState::Ending", (int64)EAuracronSessionState::Ending },
		{ "EAuracronSessionState::Disconnected", (int64)EAuracronSessionState::Disconnected },
		{ "EAuracronSessionState::Error", (int64)EAuracronSessionState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronSessionState",
	"EAuracronSessionState",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSessionState.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSessionState.InnerSingleton;
}
// ********** End Enum EAuracronSessionState *******************************************************

// ********** Begin Enum EAuracronServerValidationType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronServerValidationType;
static UEnum* EAuracronServerValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronServerValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronServerValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronServerValidationType"));
	}
	return Z_Registration_Info_UEnum_EAuracronServerValidationType.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronServerValidationType>()
{
	return EAuracronServerValidationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ability.DisplayName", "Ability" },
		{ "Ability.Name", "EAuracronServerValidationType::Ability" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para tipos de valida\xc3\xa7\xc3\xa3o server-side\n */" },
#endif
		{ "Damage.DisplayName", "Damage" },
		{ "Damage.Name", "EAuracronServerValidationType::Damage" },
		{ "Experience.DisplayName", "Experience" },
		{ "Experience.Name", "EAuracronServerValidationType::Experience" },
		{ "Gold.DisplayName", "Gold" },
		{ "Gold.Name", "EAuracronServerValidationType::Gold" },
		{ "Input.DisplayName", "Input" },
		{ "Input.Name", "EAuracronServerValidationType::Input" },
		{ "ItemUsage.DisplayName", "Item Usage" },
		{ "ItemUsage.Name", "EAuracronServerValidationType::ItemUsage" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
		{ "Movement.DisplayName", "Movement" },
		{ "Movement.Name", "EAuracronServerValidationType::Movement" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronServerValidationType::None" },
		{ "Position.DisplayName", "Position" },
		{ "Position.Name", "EAuracronServerValidationType::Position" },
		{ "Timing.DisplayName", "Timing" },
		{ "Timing.Name", "EAuracronServerValidationType::Timing" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para tipos de valida\xc3\xa7\xc3\xa3o server-side" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronServerValidationType::None", (int64)EAuracronServerValidationType::None },
		{ "EAuracronServerValidationType::Movement", (int64)EAuracronServerValidationType::Movement },
		{ "EAuracronServerValidationType::Ability", (int64)EAuracronServerValidationType::Ability },
		{ "EAuracronServerValidationType::Damage", (int64)EAuracronServerValidationType::Damage },
		{ "EAuracronServerValidationType::ItemUsage", (int64)EAuracronServerValidationType::ItemUsage },
		{ "EAuracronServerValidationType::Experience", (int64)EAuracronServerValidationType::Experience },
		{ "EAuracronServerValidationType::Gold", (int64)EAuracronServerValidationType::Gold },
		{ "EAuracronServerValidationType::Position", (int64)EAuracronServerValidationType::Position },
		{ "EAuracronServerValidationType::Timing", (int64)EAuracronServerValidationType::Timing },
		{ "EAuracronServerValidationType::Input", (int64)EAuracronServerValidationType::Input },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronServerValidationType",
	"EAuracronServerValidationType",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType()
{
	if (!Z_Registration_Info_UEnum_EAuracronServerValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronServerValidationType.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronServerValidationType.InnerSingleton;
}
// ********** End Enum EAuracronServerValidationType ***********************************************

// ********** Begin ScriptStruct FAuracronNetworkingSessionConfiguration ***************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration;
class UScriptStruct* FAuracronNetworkingSessionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronNetworkingSessionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de sess\xc3\xa3o multiplayer\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de sess\xc3\xa3o multiplayer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionName_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da sess\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da sess\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "2" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de jogadores */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinPlayersToStart_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "2" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xadnimo de jogadores para iniciar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xadnimo de jogadores para iniciar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPublic_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sess\xc3\xa3o \xc3\xa9 p\xc3\xba""blica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sess\xc3\xa3o \xc3\xa9 p\xc3\xba""blica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowSpectators_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite espectadores */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite espectadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAntiCheat_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usa anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usa anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredRegion_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\xa3o preferida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\xa3o preferida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameMode_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Modo de jogo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modo de jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapName_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mapa da partida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mapa da partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMatchDuration_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "3600" },
		{ "ClampMin", "600" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o m\xc3\xa1xima da partida (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o m\xc3\xa1xima da partida (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionTimeout_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "120" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo limite para conex\xc3\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo limite para conex\xc3\xa3o (em segundos)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPing_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "500" },
		{ "ClampMin", "50" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ping m\xc3\xa1ximo permitido */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ping m\xc3\xa1ximo permitido" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVoiceChat_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usa voice chat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usa voice chat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowReconnection_MetaData[] = {
		{ "Category", "Session Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite reconex\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite reconex\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReconnectionTimeLimit_MetaData[] = {
		{ "Category", "Session Configuration" },
		{ "ClampMax", "300" },
		{ "ClampMin", "30" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo para reconex\xc3\xa3o (em segundos) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo para reconex\xc3\xa3o (em segundos)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinPlayersToStart;
	static void NewProp_bIsPublic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPublic;
	static void NewProp_bAllowSpectators_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowSpectators;
	static void NewProp_bUseAntiCheat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAntiCheat;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PreferredRegion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MapName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxMatchDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConnectionTimeout;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPing;
	static void NewProp_bUseVoiceChat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVoiceChat;
	static void NewProp_bAllowReconnection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowReconnection;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReconnectionTimeLimit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronNetworkingSessionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_SessionName = { "SessionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, SessionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionName_MetaData), NewProp_SessionName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MinPlayersToStart = { "MinPlayersToStart", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, MinPlayersToStart), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinPlayersToStart_MetaData), NewProp_MinPlayersToStart_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bIsPublic_SetBit(void* Obj)
{
	((FAuracronNetworkingSessionConfiguration*)Obj)->bIsPublic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bIsPublic = { "bIsPublic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bIsPublic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPublic_MetaData), NewProp_bIsPublic_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowSpectators_SetBit(void* Obj)
{
	((FAuracronNetworkingSessionConfiguration*)Obj)->bAllowSpectators = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowSpectators = { "bAllowSpectators", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowSpectators_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowSpectators_MetaData), NewProp_bAllowSpectators_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseAntiCheat_SetBit(void* Obj)
{
	((FAuracronNetworkingSessionConfiguration*)Obj)->bUseAntiCheat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseAntiCheat = { "bUseAntiCheat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseAntiCheat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAntiCheat_MetaData), NewProp_bUseAntiCheat_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_PreferredRegion = { "PreferredRegion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, PreferredRegion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredRegion_MetaData), NewProp_PreferredRegion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_GameMode = { "GameMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, GameMode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameMode_MetaData), NewProp_GameMode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MapName = { "MapName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, MapName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapName_MetaData), NewProp_MapName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxMatchDuration = { "MaxMatchDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, MaxMatchDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMatchDuration_MetaData), NewProp_MaxMatchDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_ConnectionTimeout = { "ConnectionTimeout", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, ConnectionTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionTimeout_MetaData), NewProp_ConnectionTimeout_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxPing = { "MaxPing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, MaxPing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPing_MetaData), NewProp_MaxPing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseVoiceChat_SetBit(void* Obj)
{
	((FAuracronNetworkingSessionConfiguration*)Obj)->bUseVoiceChat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseVoiceChat = { "bUseVoiceChat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseVoiceChat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVoiceChat_MetaData), NewProp_bUseVoiceChat_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowReconnection_SetBit(void* Obj)
{
	((FAuracronNetworkingSessionConfiguration*)Obj)->bAllowReconnection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowReconnection = { "bAllowReconnection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingSessionConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowReconnection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowReconnection_MetaData), NewProp_bAllowReconnection_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_ReconnectionTimeLimit = { "ReconnectionTimeLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingSessionConfiguration, ReconnectionTimeLimit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReconnectionTimeLimit_MetaData), NewProp_ReconnectionTimeLimit_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_SessionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MinPlayersToStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bIsPublic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowSpectators,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseAntiCheat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_PreferredRegion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_GameMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MapName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxMatchDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_ConnectionTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_MaxPing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bUseVoiceChat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_bAllowReconnection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewProp_ReconnectionTimeLimit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronNetworkingSessionConfiguration",
	Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::PropPointers),
	sizeof(FAuracronNetworkingSessionConfiguration),
	alignof(FAuracronNetworkingSessionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronNetworkingSessionConfiguration *****************************

// ********** Begin ScriptStruct FAuracronPlayerInfo ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo;
class UScriptStruct* FAuracronPlayerInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPlayerInfo, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronPlayerInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para informa\xc3\xa7\xc3\xb5""es de jogador\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para informa\xc3\xa7\xc3\xb5""es de jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerName_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccountLevel_MetaData[] = {
		{ "Category", "Player Info" },
		{ "ClampMax", "500" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel da conta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel da conta" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompetitiveRank_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ranking competitivo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ranking competitivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPing_MetaData[] = {
		{ "Category", "Player Info" },
		{ "ClampMax", "1000" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ping atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ping atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsConnected_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\xa1 conectado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\xa1 conectado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsReady_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Est\xc3\xa1 pronto para iniciar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Est\xc3\xa1 pronto para iniciar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamID_MetaData[] = {
		{ "Category", "Player Info" },
		{ "ClampMax", "1" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time do jogador (0 = Team A, 1 = Team B) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time do jogador (0 = Team A, 1 = Team B)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamPosition_MetaData[] = {
		{ "Category", "Player Info" },
		{ "ClampMax", "4" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o no time (0-4) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o no time (0-4)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedChampion_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Campe\xc3\xa3o selecionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Campe\xc3\xa3o selecionado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedSigilos_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** S\xc3\xadgilos selecionados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "S\xc3\xadgilos selecionados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchStats_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas da partida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas da partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionTime_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de conex\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de conex\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastActivity_MetaData[] = {
		{ "Category", "Player Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x9altima atividade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x9altima atividade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AccountLevel;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CompetitiveRank;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPing;
	static void NewProp_bIsConnected_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsConnected;
	static void NewProp_bIsReady_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsReady;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamPosition;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectedChampion;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectedSigilos_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedSigilos;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MatchStats_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MatchStats_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MatchStats;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectionTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastActivity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPlayerInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_PlayerName = { "PlayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, PlayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerName_MetaData), NewProp_PlayerName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_AccountLevel = { "AccountLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, AccountLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccountLevel_MetaData), NewProp_AccountLevel_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_CompetitiveRank = { "CompetitiveRank", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, CompetitiveRank), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompetitiveRank_MetaData), NewProp_CompetitiveRank_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_CurrentPing = { "CurrentPing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, CurrentPing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPing_MetaData), NewProp_CurrentPing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsConnected_SetBit(void* Obj)
{
	((FAuracronPlayerInfo*)Obj)->bIsConnected = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsConnected = { "bIsConnected", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPlayerInfo), &Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsConnected_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsConnected_MetaData), NewProp_bIsConnected_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsReady_SetBit(void* Obj)
{
	((FAuracronPlayerInfo*)Obj)->bIsReady = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsReady = { "bIsReady", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPlayerInfo), &Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsReady_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsReady_MetaData), NewProp_bIsReady_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, TeamID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamID_MetaData), NewProp_TeamID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_TeamPosition = { "TeamPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, TeamPosition), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamPosition_MetaData), NewProp_TeamPosition_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedChampion = { "SelectedChampion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, SelectedChampion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedChampion_MetaData), NewProp_SelectedChampion_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedSigilos_Inner = { "SelectedSigilos", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedSigilos = { "SelectedSigilos", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, SelectedSigilos), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedSigilos_MetaData), NewProp_SelectedSigilos_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats_ValueProp = { "MatchStats", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats_Key_KeyProp = { "MatchStats_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats = { "MatchStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, MatchStats), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchStats_MetaData), NewProp_MatchStats_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_ConnectionTime = { "ConnectionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, ConnectionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionTime_MetaData), NewProp_ConnectionTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_LastActivity = { "LastActivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerInfo, LastActivity), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastActivity_MetaData), NewProp_LastActivity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_PlayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_AccountLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_CompetitiveRank,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_CurrentPing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsConnected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_bIsReady,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_TeamPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedChampion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedSigilos_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_SelectedSigilos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_MatchStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_ConnectionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewProp_LastActivity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronPlayerInfo",
	Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::PropPointers),
	sizeof(FAuracronPlayerInfo),
	alignof(FAuracronPlayerInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPlayerInfo *************************************************

// ********** Begin ScriptStruct FAuracronNetworkingAntiCheatConfiguration *************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration;
class UScriptStruct* FAuracronNetworkingAntiCheatConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronNetworkingAntiCheatConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de anti-cheat\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateMovement_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valida\xc3\xa7\xc3\xa3o de movimento habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o de movimento habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateAbilities_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valida\xc3\xa7\xc3\xa3o de habilidades habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o de habilidades habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateDamage_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valida\xc3\xa7\xc3\xa3o de dano habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o de dano habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidateTiming_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valida\xc3\xa7\xc3\xa3o de timing habilitada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida\xc3\xa7\xc3\xa3o de timing habilitada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementTolerance_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Toler\xc3\xa2ncia de movimento (unidades por segundo) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Toler\xc3\xa2ncia de movimento (unidades por segundo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PingTolerance_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "500" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Toler\xc3\xa2ncia de ping (ms) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Toler\xc3\xa2ncia de ping (ms)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActionsPerSecond_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "50" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\xa1ximo de a\xc3\xa7\xc3\xb5""es por segundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\xa1ximo de a\xc3\xa7\xc3\xb5""es por segundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpamDetectionCooldown_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de cooldown para detec\xc3\xa7\xc3\xa3o de spam */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de cooldown para detec\xc3\xa7\xc3\xa3o de spam" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxViolationsBeforeKick_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de viola\xc3\xa7\xc3\xb5""es antes de kick */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de viola\xc3\xa7\xc3\xb5""es antes de kick" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogSuspiciousActivity_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Log de atividades suspeitas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Log de atividades suspeitas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoReportCheaters_MetaData[] = {
		{ "Category", "Anti-Cheat Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reportar automaticamente cheaters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reportar automaticamente cheaters" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bValidateMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateMovement;
	static void NewProp_bValidateAbilities_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateAbilities;
	static void NewProp_bValidateDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateDamage;
	static void NewProp_bValidateTiming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidateTiming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementTolerance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PingTolerance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActionsPerSecond;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpamDetectionCooldown;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxViolationsBeforeKick;
	static void NewProp_bLogSuspiciousActivity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogSuspiciousActivity;
	static void NewProp_bAutoReportCheaters_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoReportCheaters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronNetworkingAntiCheatConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateMovement_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bValidateMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateMovement = { "bValidateMovement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateMovement_MetaData), NewProp_bValidateMovement_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateAbilities_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bValidateAbilities = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateAbilities = { "bValidateAbilities", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateAbilities_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateAbilities_MetaData), NewProp_bValidateAbilities_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateDamage_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bValidateDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateDamage = { "bValidateDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateDamage_MetaData), NewProp_bValidateDamage_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateTiming_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bValidateTiming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateTiming = { "bValidateTiming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateTiming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidateTiming_MetaData), NewProp_bValidateTiming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MovementTolerance = { "MovementTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingAntiCheatConfiguration, MovementTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementTolerance_MetaData), NewProp_MovementTolerance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_PingTolerance = { "PingTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingAntiCheatConfiguration, PingTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PingTolerance_MetaData), NewProp_PingTolerance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MaxActionsPerSecond = { "MaxActionsPerSecond", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingAntiCheatConfiguration, MaxActionsPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActionsPerSecond_MetaData), NewProp_MaxActionsPerSecond_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_SpamDetectionCooldown = { "SpamDetectionCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingAntiCheatConfiguration, SpamDetectionCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpamDetectionCooldown_MetaData), NewProp_SpamDetectionCooldown_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MaxViolationsBeforeKick = { "MaxViolationsBeforeKick", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkingAntiCheatConfiguration, MaxViolationsBeforeKick), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxViolationsBeforeKick_MetaData), NewProp_MaxViolationsBeforeKick_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bLogSuspiciousActivity_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bLogSuspiciousActivity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bLogSuspiciousActivity = { "bLogSuspiciousActivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bLogSuspiciousActivity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogSuspiciousActivity_MetaData), NewProp_bLogSuspiciousActivity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bAutoReportCheaters_SetBit(void* Obj)
{
	((FAuracronNetworkingAntiCheatConfiguration*)Obj)->bAutoReportCheaters = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bAutoReportCheaters = { "bAutoReportCheaters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkingAntiCheatConfiguration), &Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bAutoReportCheaters_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoReportCheaters_MetaData), NewProp_bAutoReportCheaters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bValidateTiming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MovementTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_PingTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MaxActionsPerSecond,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_SpamDetectionCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_MaxViolationsBeforeKick,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bLogSuspiciousActivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewProp_bAutoReportCheaters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronNetworkingAntiCheatConfiguration",
	Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::PropPointers),
	sizeof(FAuracronNetworkingAntiCheatConfiguration),
	alignof(FAuracronNetworkingAntiCheatConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronNetworkingAntiCheatConfiguration ***************************

// ********** Begin ScriptStruct FAuracronReplicationConfiguration *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration;
class UScriptStruct* FAuracronReplicationConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronReplicationConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronReplicationConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseReplicationGraph_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Replication Graph */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Replication Graph" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseIrisNetworking_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Iris Networking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Iris Networking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNetworkPrediction_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar Network Prediction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar Network Prediction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationFrequency_MetaData[] = {
		{ "Category", "Replication Configuration" },
		{ "ClampMax", "120" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia de replica\xc3\xa7\xc3\xa3o (Hz) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia de replica\xc3\xa7\xc3\xa3o (Hz)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationDistance_MetaData[] = {
		{ "Category", "Replication Configuration" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia m\xc3\xa1xima de replica\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia m\xc3\xa1xima de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePacketCompression_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar compress\xc3\xa3o de pacotes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar compress\xc3\xa3o de pacotes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDeltaCompression_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar delta compression */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar delta compression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionReplicationPriority_MetaData[] = {
		{ "Category", "Replication Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de replica\xc3\xa7\xc3\xa3o para campe\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de replica\xc3\xa7\xc3\xa3o para campe\xc3\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityReplicationPriority_MetaData[] = {
		{ "Category", "Replication Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de replica\xc3\xa7\xc3\xa3o para habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de replica\xc3\xa7\xc3\xa3o para habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectReplicationPriority_MetaData[] = {
		{ "Category", "Replication Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de replica\xc3\xa7\xc3\xa3o para efeitos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de replica\xc3\xa7\xc3\xa3o para efeitos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceBasedRelevancy_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar relev\xc3\xa2ncia baseada em dist\xc3\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar relev\xc3\xa2ncia baseada em dist\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLineOfSightRelevancy_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar relev\xc3\xa2ncia baseada em line of sight */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar relev\xc3\xa2ncia baseada em line of sight" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFrustumCulling_MetaData[] = {
		{ "Category", "Replication Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar culling baseado em frustum */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar culling baseado em frustum" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUseReplicationGraph_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseReplicationGraph;
	static void NewProp_bUseIrisNetworking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseIrisNetworking;
	static void NewProp_bUseNetworkPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNetworkPrediction;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReplicationFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReplicationDistance;
	static void NewProp_bUsePacketCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePacketCompression;
	static void NewProp_bUseDeltaCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDeltaCompression;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChampionReplicationPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityReplicationPriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectReplicationPriority;
	static void NewProp_bUseDistanceBasedRelevancy_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceBasedRelevancy;
	static void NewProp_bUseLineOfSightRelevancy_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLineOfSightRelevancy;
	static void NewProp_bUseFrustumCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFrustumCulling;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronReplicationConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseReplicationGraph_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseReplicationGraph = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseReplicationGraph = { "bUseReplicationGraph", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseReplicationGraph_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseReplicationGraph_MetaData), NewProp_bUseReplicationGraph_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseIrisNetworking_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseIrisNetworking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseIrisNetworking = { "bUseIrisNetworking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseIrisNetworking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseIrisNetworking_MetaData), NewProp_bUseIrisNetworking_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseNetworkPrediction_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseNetworkPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseNetworkPrediction = { "bUseNetworkPrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseNetworkPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNetworkPrediction_MetaData), NewProp_bUseNetworkPrediction_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_ReplicationFrequency = { "ReplicationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReplicationConfiguration, ReplicationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationFrequency_MetaData), NewProp_ReplicationFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_MaxReplicationDistance = { "MaxReplicationDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReplicationConfiguration, MaxReplicationDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationDistance_MetaData), NewProp_MaxReplicationDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUsePacketCompression_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUsePacketCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUsePacketCompression = { "bUsePacketCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUsePacketCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePacketCompression_MetaData), NewProp_bUsePacketCompression_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDeltaCompression_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseDeltaCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDeltaCompression = { "bUseDeltaCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDeltaCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDeltaCompression_MetaData), NewProp_bUseDeltaCompression_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_ChampionReplicationPriority = { "ChampionReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReplicationConfiguration, ChampionReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionReplicationPriority_MetaData), NewProp_ChampionReplicationPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_AbilityReplicationPriority = { "AbilityReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReplicationConfiguration, AbilityReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityReplicationPriority_MetaData), NewProp_AbilityReplicationPriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_EffectReplicationPriority = { "EffectReplicationPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronReplicationConfiguration, EffectReplicationPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectReplicationPriority_MetaData), NewProp_EffectReplicationPriority_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDistanceBasedRelevancy_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseDistanceBasedRelevancy = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDistanceBasedRelevancy = { "bUseDistanceBasedRelevancy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDistanceBasedRelevancy_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceBasedRelevancy_MetaData), NewProp_bUseDistanceBasedRelevancy_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseLineOfSightRelevancy_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseLineOfSightRelevancy = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseLineOfSightRelevancy = { "bUseLineOfSightRelevancy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseLineOfSightRelevancy_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLineOfSightRelevancy_MetaData), NewProp_bUseLineOfSightRelevancy_MetaData) };
void Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseFrustumCulling_SetBit(void* Obj)
{
	((FAuracronReplicationConfiguration*)Obj)->bUseFrustumCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseFrustumCulling = { "bUseFrustumCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronReplicationConfiguration), &Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseFrustumCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFrustumCulling_MetaData), NewProp_bUseFrustumCulling_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseReplicationGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseIrisNetworking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseNetworkPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_ReplicationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_MaxReplicationDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUsePacketCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDeltaCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_ChampionReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_AbilityReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_EffectReplicationPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseDistanceBasedRelevancy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseLineOfSightRelevancy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewProp_bUseFrustumCulling,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronReplicationConfiguration",
	Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::PropPointers),
	sizeof(FAuracronReplicationConfiguration),
	alignof(FAuracronReplicationConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronReplicationConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronReplicationConfiguration ***********************************

// ********** Begin Delegate FOnSessionCreated *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics
{
	struct AuracronNetworkingBridge_eventOnSessionCreated_Parms
	{
		FString SessionName;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando sess\xc3\xa3o \xc3\xa9 criada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando sess\xc3\xa3o \xc3\xa9 criada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionName;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_SessionName = { "SessionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSessionCreated_Parms, SessionName), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventOnSessionCreated_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventOnSessionCreated_Parms), &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_SessionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnSessionCreated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSessionCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSessionCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNetworkingBridge::FOnSessionCreated_DelegateWrapper(const FMulticastScriptDelegate& OnSessionCreated, const FString& SessionName, bool bSuccess)
{
	struct AuracronNetworkingBridge_eventOnSessionCreated_Parms
	{
		FString SessionName;
		bool bSuccess;
	};
	AuracronNetworkingBridge_eventOnSessionCreated_Parms Parms;
	Parms.SessionName=SessionName;
	Parms.bSuccess=bSuccess ? true : false;
	OnSessionCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSessionCreated *******************************************************

// ********** Begin Delegate FOnPlayerConnected ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics
{
	struct AuracronNetworkingBridge_eventOnPlayerConnected_Parms
	{
		FAuracronPlayerInfo PlayerInfo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando jogador se conecta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando jogador se conecta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerInfo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::NewProp_PlayerInfo = { "PlayerInfo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnPlayerConnected_Parms, PlayerInfo), Z_Construct_UScriptStruct_FAuracronPlayerInfo, METADATA_PARAMS(0, nullptr) }; // 1214137280
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::NewProp_PlayerInfo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnPlayerConnected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnPlayerConnected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnPlayerConnected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNetworkingBridge::FOnPlayerConnected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerConnected, FAuracronPlayerInfo PlayerInfo)
{
	struct AuracronNetworkingBridge_eventOnPlayerConnected_Parms
	{
		FAuracronPlayerInfo PlayerInfo;
	};
	AuracronNetworkingBridge_eventOnPlayerConnected_Parms Parms;
	Parms.PlayerInfo=PlayerInfo;
	OnPlayerConnected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerConnected ******************************************************

// ********** Begin Delegate FOnPlayerDisconnected *************************************************
struct Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics
{
	struct AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms
	{
		FString PlayerID;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando jogador se desconecta */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando jogador se desconecta" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms, Reason), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnPlayerDisconnected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNetworkingBridge::FOnPlayerDisconnected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDisconnected, const FString& PlayerID, const FString& Reason)
{
	struct AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms
	{
		FString PlayerID;
		FString Reason;
	};
	AuracronNetworkingBridge_eventOnPlayerDisconnected_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.Reason=Reason;
	OnPlayerDisconnected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPlayerDisconnected ***************************************************

// ********** Begin Delegate FOnSuspiciousActivityDetected *****************************************
struct Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics
{
	struct AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms
	{
		FString PlayerID;
		FString ActivityType;
		FString Evidence;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando atividade suspeita \xc3\xa9 detectada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando atividade suspeita \xc3\xa9 detectada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActivityType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms, PlayerID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_ActivityType = { "ActivityType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms, ActivityType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_Evidence = { "Evidence", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms, Evidence), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_ActivityType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::NewProp_Evidence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnSuspiciousActivityDetected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNetworkingBridge::FOnSuspiciousActivityDetected_DelegateWrapper(const FMulticastScriptDelegate& OnSuspiciousActivityDetected, const FString& PlayerID, const FString& ActivityType, const FString& Evidence)
{
	struct AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms
	{
		FString PlayerID;
		FString ActivityType;
		FString Evidence;
	};
	AuracronNetworkingBridge_eventOnSuspiciousActivityDetected_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.ActivityType=ActivityType;
	Parms.Evidence=Evidence;
	OnSuspiciousActivityDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSuspiciousActivityDetected *******************************************

// ********** Begin Delegate FOnSessionStateChanged ************************************************
struct Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics
{
	struct AuracronNetworkingBridge_eventOnSessionStateChanged_Parms
	{
		EAuracronSessionState OldState;
		EAuracronSessionState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando estado da sess\xc3\xa3o muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando estado da sess\xc3\xa3o muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSessionStateChanged_Parms, OldState), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState, METADATA_PARAMS(0, nullptr) }; // 914745319
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventOnSessionStateChanged_Parms, NewState), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState, METADATA_PARAMS(0, nullptr) }; // 914745319
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnSessionStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSessionStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::AuracronNetworkingBridge_eventOnSessionStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronNetworkingBridge::FOnSessionStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSessionStateChanged, EAuracronSessionState OldState, EAuracronSessionState NewState)
{
	struct AuracronNetworkingBridge_eventOnSessionStateChanged_Parms
	{
		EAuracronSessionState OldState;
		EAuracronSessionState NewState;
	};
	AuracronNetworkingBridge_eventOnSessionStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnSessionStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnSessionStateChanged **************************************************

// ********** Begin Class UAuracronNetworkingBridge Function BanPlayer *****************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics
{
	struct AuracronNetworkingBridge_eventBanPlayer_Parms
	{
		FString PlayerID;
		FString Reason;
		int32 DurationMinutes;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Players" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Banir jogador\n     */" },
#endif
		{ "CPP_Default_DurationMinutes", "0" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Banir jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DurationMinutes;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventBanPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventBanPlayer_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_DurationMinutes = { "DurationMinutes", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventBanPlayer_Parms, DurationMinutes), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventBanPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventBanPlayer_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_DurationMinutes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "BanPlayer", Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::AuracronNetworkingBridge_eventBanPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::AuracronNetworkingBridge_eventBanPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execBanPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_GET_PROPERTY(FIntProperty,Z_Param_DurationMinutes);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BanPlayer(Z_Param_PlayerID,Z_Param_Reason,Z_Param_DurationMinutes);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function BanPlayer *******************************

// ********** Begin Class UAuracronNetworkingBridge Function CreateSession *************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics
{
	struct AuracronNetworkingBridge_eventCreateSession_Parms
	{
		FAuracronSessionConfiguration SessionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar sess\xc3\xa3o multiplayer\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar sess\xc3\xa3o multiplayer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventCreateSession_Parms, SessionConfig), Z_Construct_UScriptStruct_FAuracronSessionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 2090834983
void Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventCreateSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventCreateSession_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_SessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "CreateSession", Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::AuracronNetworkingBridge_eventCreateSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::AuracronNetworkingBridge_eventCreateSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execCreateSession)
{
	P_GET_STRUCT_REF(FAuracronSessionConfiguration,Z_Param_Out_SessionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateSession(Z_Param_Out_SessionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function CreateSession ***************************

// ********** Begin Class UAuracronNetworkingBridge Function DestroySession ************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics
{
	struct AuracronNetworkingBridge_eventDestroySession_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Destruir sess\xc3\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destruir sess\xc3\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventDestroySession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventDestroySession_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "DestroySession", Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::AuracronNetworkingBridge_eventDestroySession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::AuracronNetworkingBridge_eventDestroySession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execDestroySession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DestroySession();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function DestroySession **************************

// ********** Begin Class UAuracronNetworkingBridge Function FindSessions **************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics
{
	struct AuracronNetworkingBridge_eventFindSessions_Parms
	{
		int32 MaxResults;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Buscar sess\xc3\xb5""es dispon\xc3\xadveis\n     */" },
#endif
		{ "CPP_Default_MaxResults", "50" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buscar sess\xc3\xb5""es dispon\xc3\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventFindSessions_Parms, MaxResults), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventFindSessions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventFindSessions_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "FindSessions", Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::AuracronNetworkingBridge_eventFindSessions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::AuracronNetworkingBridge_eventFindSessions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execFindSessions)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxResults);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FindSessions(Z_Param_MaxResults);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function FindSessions ****************************

// ********** Begin Class UAuracronNetworkingBridge Function GetAverageSessionPing *****************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics
{
	struct AuracronNetworkingBridge_eventGetAverageSessionPing_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter ping m\xc3\xa9""dio da sess\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ping m\xc3\xa9""dio da sess\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetAverageSessionPing_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetAverageSessionPing", Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::AuracronNetworkingBridge_eventGetAverageSessionPing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::AuracronNetworkingBridge_eventGetAverageSessionPing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetAverageSessionPing)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageSessionPing();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetAverageSessionPing *******************

// ********** Begin Class UAuracronNetworkingBridge Function GetBandwidthUsage *********************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics
{
	struct AuracronNetworkingBridge_eventGetBandwidthUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter largura de banda utilizada\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter largura de banda utilizada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetBandwidthUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetBandwidthUsage", Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::AuracronNetworkingBridge_eventGetBandwidthUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::AuracronNetworkingBridge_eventGetBandwidthUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetBandwidthUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBandwidthUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetBandwidthUsage ***********************

// ********** Begin Class UAuracronNetworkingBridge Function GetConnectedPlayers *******************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics
{
	struct AuracronNetworkingBridge_eventGetConnectedPlayers_Parms
	{
		TArray<FAuracronPlayerInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Players" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter lista de jogadores conectados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter lista de jogadores conectados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPlayerInfo, METADATA_PARAMS(0, nullptr) }; // 1214137280
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetConnectedPlayers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1214137280
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetConnectedPlayers", Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::AuracronNetworkingBridge_eventGetConnectedPlayers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::AuracronNetworkingBridge_eventGetConnectedPlayers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetConnectedPlayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPlayerInfo>*)Z_Param__Result=P_THIS->GetConnectedPlayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetConnectedPlayers *********************

// ********** Begin Class UAuracronNetworkingBridge Function GetCurrentSessionInfo *****************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics
{
	struct AuracronNetworkingBridge_eventGetCurrentSessionInfo_Parms
	{
		FAuracronNetworkingSessionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter informa\xc3\xa7\xc3\xb5""es da sess\xc3\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es da sess\xc3\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetCurrentSessionInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration, METADATA_PARAMS(0, nullptr) }; // 3299819686
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetCurrentSessionInfo", Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::AuracronNetworkingBridge_eventGetCurrentSessionInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::AuracronNetworkingBridge_eventGetCurrentSessionInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetCurrentSessionInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronNetworkingSessionConfiguration*)Z_Param__Result=P_THIS->GetCurrentSessionInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetCurrentSessionInfo *******************

// ********** Begin Class UAuracronNetworkingBridge Function GetNetworkStatistics ******************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics
{
	struct AuracronNetworkingBridge_eventGetNetworkStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estat\xc3\xadsticas de rede\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estat\xc3\xadsticas de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetNetworkStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetNetworkStatistics", Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::AuracronNetworkingBridge_eventGetNetworkStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::AuracronNetworkingBridge_eventGetNetworkStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetNetworkStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetNetworkStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetNetworkStatistics ********************

// ********** Begin Class UAuracronNetworkingBridge Function GetPacketLossRate *********************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics
{
	struct AuracronNetworkingBridge_eventGetPacketLossRate_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter taxa de perda de pacotes\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter taxa de perda de pacotes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetPacketLossRate_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetPacketLossRate", Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::AuracronNetworkingBridge_eventGetPacketLossRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::AuracronNetworkingBridge_eventGetPacketLossRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetPacketLossRate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPacketLossRate();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetPacketLossRate ***********************

// ********** Begin Class UAuracronNetworkingBridge Function GetPlayerInfo *************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics
{
	struct AuracronNetworkingBridge_eventGetPlayerInfo_Parms
	{
		FString PlayerID;
		FAuracronPlayerInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Players" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter informa\xc3\xa7\xc3\xb5""es de um jogador espec\xc3\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de um jogador espec\xc3\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetPlayerInfo_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetPlayerInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPlayerInfo, METADATA_PARAMS(0, nullptr) }; // 1214137280
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetPlayerInfo", Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::AuracronNetworkingBridge_eventGetPlayerInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::AuracronNetworkingBridge_eventGetPlayerInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetPlayerInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPlayerInfo*)Z_Param__Result=P_THIS->GetPlayerInfo(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetPlayerInfo ***************************

// ********** Begin Class UAuracronNetworkingBridge Function GetSessionState ***********************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics
{
	struct AuracronNetworkingBridge_eventGetSessionState_Parms
	{
		EAuracronSessionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estado atual da sess\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estado atual da sess\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventGetSessionState_Parms, ReturnValue), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState, METADATA_PARAMS(0, nullptr) }; // 914745319
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "GetSessionState", Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::AuracronNetworkingBridge_eventGetSessionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::AuracronNetworkingBridge_eventGetSessionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execGetSessionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronSessionState*)Z_Param__Result=P_THIS->GetSessionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function GetSessionState *************************

// ********** Begin Class UAuracronNetworkingBridge Function IsPlayerUnderSurveillance *************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics
{
	struct AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se jogador est\xc3\xa1 sendo monitorado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se jogador est\xc3\xa1 sendo monitorado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "IsPlayerUnderSurveillance", Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::AuracronNetworkingBridge_eventIsPlayerUnderSurveillance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execIsPlayerUnderSurveillance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerUnderSurveillance(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function IsPlayerUnderSurveillance ***************

// ********** Begin Class UAuracronNetworkingBridge Function JoinSession ***************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics
{
	struct AuracronNetworkingBridge_eventJoinSession_Parms
	{
		FString SessionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Entrar em sess\xc3\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entrar em sess\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventJoinSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventJoinSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventJoinSession_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "JoinSession", Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::AuracronNetworkingBridge_eventJoinSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::AuracronNetworkingBridge_eventJoinSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execJoinSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->JoinSession(Z_Param_SessionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function JoinSession *****************************

// ********** Begin Class UAuracronNetworkingBridge Function KickPlayer ****************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics
{
	struct AuracronNetworkingBridge_eventKickPlayer_Parms
	{
		FString PlayerID;
		FString Reason;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Players" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Kickar jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Kickar jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventKickPlayer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventKickPlayer_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventKickPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventKickPlayer_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "KickPlayer", Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::AuracronNetworkingBridge_eventKickPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::AuracronNetworkingBridge_eventKickPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execKickPlayer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->KickPlayer(Z_Param_PlayerID,Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function KickPlayer ******************************

// ********** Begin Class UAuracronNetworkingBridge Function LeaveSession **************************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics
{
	struct AuracronNetworkingBridge_eventLeaveSession_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Sair da sess\xc3\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sair da sess\xc3\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventLeaveSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventLeaveSession_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "LeaveSession", Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::AuracronNetworkingBridge_eventLeaveSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::AuracronNetworkingBridge_eventLeaveSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execLeaveSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LeaveSession();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function LeaveSession ****************************

// ********** Begin Class UAuracronNetworkingBridge Function OnRep_ConnectedPlayers ****************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnRep_ConnectedPlayers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execOnRep_ConnectedPlayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ConnectedPlayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function OnRep_ConnectedPlayers ******************

// ********** Begin Class UAuracronNetworkingBridge Function OnRep_ConnectionType ******************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnRep_ConnectionType", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execOnRep_ConnectionType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ConnectionType();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function OnRep_ConnectionType ********************

// ********** Begin Class UAuracronNetworkingBridge Function OnRep_SessionState ********************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "OnRep_SessionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execOnRep_SessionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SessionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function OnRep_SessionState **********************

// ********** Begin Class UAuracronNetworkingBridge Function ReportSuspiciousActivity **************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics
{
	struct AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms
	{
		FString PlayerID;
		FString ActivityDescription;
		TMap<FString,FString> Evidence;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Networking|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reportar atividade suspeita\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reportar atividade suspeita" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityDescription_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Evidence_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActivityDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Evidence;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ActivityDescription = { "ActivityDescription", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms, ActivityDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityDescription_MetaData), NewProp_ActivityDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence_ValueProp = { "Evidence", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence_Key_KeyProp = { "Evidence_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence = { "Evidence", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms, Evidence), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Evidence_MetaData), NewProp_Evidence_MetaData) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ActivityDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_Evidence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "ReportSuspiciousActivity", Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::AuracronNetworkingBridge_eventReportSuspiciousActivity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execReportSuspiciousActivity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActivityDescription);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Evidence);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReportSuspiciousActivity(Z_Param_PlayerID,Z_Param_ActivityDescription,Z_Param_Out_Evidence);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function ReportSuspiciousActivity ****************

// ********** Begin Class UAuracronNetworkingBridge Function UpdatePlayerInfo **********************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics
{
	struct AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms
	{
		FString PlayerID;
		FAuracronPlayerInfo PlayerInfo;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Networking|Players" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar informa\xc3\xa7\xc3\xb5""es de jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar informa\xc3\xa7\xc3\xb5""es de jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerInfo_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerInfo;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_PlayerInfo = { "PlayerInfo", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms, PlayerInfo), Z_Construct_UScriptStruct_FAuracronPlayerInfo, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerInfo_MetaData), NewProp_PlayerInfo_MetaData) }; // 1214137280
void Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_PlayerInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "UpdatePlayerInfo", Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::AuracronNetworkingBridge_eventUpdatePlayerInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execUpdatePlayerInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FAuracronPlayerInfo,Z_Param_Out_PlayerInfo);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdatePlayerInfo(Z_Param_PlayerID,Z_Param_Out_PlayerInfo);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function UpdatePlayerInfo ************************

// ********** Begin Class UAuracronNetworkingBridge Function ValidatePlayerAction ******************
struct Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics
{
	struct AuracronNetworkingBridge_eventValidatePlayerAction_Parms
	{
		FString PlayerID;
		EAuracronServerValidationType ValidationType;
		TMap<FString,FString> ActionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Networking|Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validar a\xc3\xa7\xc3\xa3o do jogador no servidor\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar a\xc3\xa7\xc3\xa3o do jogador no servidor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ValidationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ValidationType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventValidatePlayerAction_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ValidationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ValidationType = { "ValidationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventValidatePlayerAction_Parms, ValidationType), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronServerValidationType, METADATA_PARAMS(0, nullptr) }; // 278967133
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronNetworkingBridge_eventValidatePlayerAction_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
void Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronNetworkingBridge_eventValidatePlayerAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronNetworkingBridge_eventValidatePlayerAction_Parms), &Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ValidationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ValidationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ActionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronNetworkingBridge, nullptr, "ValidatePlayerAction", Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::AuracronNetworkingBridge_eventValidatePlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::AuracronNetworkingBridge_eventValidatePlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronNetworkingBridge::execValidatePlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_ENUM(EAuracronServerValidationType,Z_Param_ValidationType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePlayerAction(Z_Param_PlayerID,EAuracronServerValidationType(Z_Param_ValidationType),Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronNetworkingBridge Function ValidatePlayerAction ********************

// ********** Begin Class UAuracronNetworkingBridge ************************************************
void UAuracronNetworkingBridge::StaticRegisterNativesUAuracronNetworkingBridge()
{
	UClass* Class = UAuracronNetworkingBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BanPlayer", &UAuracronNetworkingBridge::execBanPlayer },
		{ "CreateSession", &UAuracronNetworkingBridge::execCreateSession },
		{ "DestroySession", &UAuracronNetworkingBridge::execDestroySession },
		{ "FindSessions", &UAuracronNetworkingBridge::execFindSessions },
		{ "GetAverageSessionPing", &UAuracronNetworkingBridge::execGetAverageSessionPing },
		{ "GetBandwidthUsage", &UAuracronNetworkingBridge::execGetBandwidthUsage },
		{ "GetConnectedPlayers", &UAuracronNetworkingBridge::execGetConnectedPlayers },
		{ "GetCurrentSessionInfo", &UAuracronNetworkingBridge::execGetCurrentSessionInfo },
		{ "GetNetworkStatistics", &UAuracronNetworkingBridge::execGetNetworkStatistics },
		{ "GetPacketLossRate", &UAuracronNetworkingBridge::execGetPacketLossRate },
		{ "GetPlayerInfo", &UAuracronNetworkingBridge::execGetPlayerInfo },
		{ "GetSessionState", &UAuracronNetworkingBridge::execGetSessionState },
		{ "IsPlayerUnderSurveillance", &UAuracronNetworkingBridge::execIsPlayerUnderSurveillance },
		{ "JoinSession", &UAuracronNetworkingBridge::execJoinSession },
		{ "KickPlayer", &UAuracronNetworkingBridge::execKickPlayer },
		{ "LeaveSession", &UAuracronNetworkingBridge::execLeaveSession },
		{ "OnRep_ConnectedPlayers", &UAuracronNetworkingBridge::execOnRep_ConnectedPlayers },
		{ "OnRep_ConnectionType", &UAuracronNetworkingBridge::execOnRep_ConnectionType },
		{ "OnRep_SessionState", &UAuracronNetworkingBridge::execOnRep_SessionState },
		{ "ReportSuspiciousActivity", &UAuracronNetworkingBridge::execReportSuspiciousActivity },
		{ "UpdatePlayerInfo", &UAuracronNetworkingBridge::execUpdatePlayerInfo },
		{ "ValidatePlayerAction", &UAuracronNetworkingBridge::execValidatePlayerAction },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronNetworkingBridge;
UClass* UAuracronNetworkingBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronNetworkingBridge;
	if (!Z_Registration_Info_UClass_UAuracronNetworkingBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronNetworkingBridge"),
			Z_Registration_Info_UClass_UAuracronNetworkingBridge.InnerSingleton,
			StaticRegisterNativesUAuracronNetworkingBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronNetworkingBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister()
{
	return UAuracronNetworkingBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronNetworkingBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema Multiplayer 5v5\n * Respons\xc3\xa1vel pelo gerenciamento completo de networking autoritativo\n */" },
#endif
		{ "DisplayName", "AURACRON Networking Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronNetworkingBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema Multiplayer 5v5\nRespons\xc3\xa1vel pelo gerenciamento completo de networking autoritativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSessionConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o da sess\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o da sess\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de replica\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSessionState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual da sess\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual da sess\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentConnectionType_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de conex\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de conex\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedPlayers_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jogadores conectados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jogadores conectados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkStatistics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas de rede */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas de rede" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSessionCreated_MetaData[] = {
		{ "Category", "AURACRON Networking|Events" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerConnected_MetaData[] = {
		{ "Category", "AURACRON Networking|Events" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerDisconnected_MetaData[] = {
		{ "Category", "AURACRON Networking|Events" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSuspiciousActivityDetected_MetaData[] = {
		{ "Category", "AURACRON Networking|Events" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSessionStateChanged_MetaData[] = {
		{ "Category", "AURACRON Networking|Events" },
		{ "ModuleRelativePath", "Public/AuracronNetworkingBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentSessionConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AntiCheatConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReplicationConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentSessionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentSessionState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentConnectionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentConnectionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectedPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectedPlayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkStatistics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NetworkStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NetworkStatistics;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSessionCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerConnected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerDisconnected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSuspiciousActivityDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSessionStateChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_BanPlayer, "BanPlayer" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_CreateSession, "CreateSession" }, // 770617018
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_DestroySession, "DestroySession" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_FindSessions, "FindSessions" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetAverageSessionPing, "GetAverageSessionPing" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetBandwidthUsage, "GetBandwidthUsage" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetConnectedPlayers, "GetConnectedPlayers" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetCurrentSessionInfo, "GetCurrentSessionInfo" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetNetworkStatistics, "GetNetworkStatistics" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetPacketLossRate, "GetPacketLossRate" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetPlayerInfo, "GetPlayerInfo" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_GetSessionState, "GetSessionState" }, // 887869596
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_IsPlayerUnderSurveillance, "IsPlayerUnderSurveillance" }, // **********
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_JoinSession, "JoinSession" }, // 3603500907
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_KickPlayer, "KickPlayer" }, // 3347635161
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_LeaveSession, "LeaveSession" }, // 1277041150
		{ &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature, "OnPlayerConnected__DelegateSignature" }, // 2249834433
		{ &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature, "OnPlayerDisconnected__DelegateSignature" }, // 3695600169
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectedPlayers, "OnRep_ConnectedPlayers" }, // 2418565143
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_ConnectionType, "OnRep_ConnectionType" }, // 3937929530
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_OnRep_SessionState, "OnRep_SessionState" }, // 504365396
		{ &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature, "OnSessionCreated__DelegateSignature" }, // 2361560682
		{ &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature, "OnSessionStateChanged__DelegateSignature" }, // 3928611927
		{ &Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature, "OnSuspiciousActivityDetected__DelegateSignature" }, // 1269788890
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_ReportSuspiciousActivity, "ReportSuspiciousActivity" }, // 516502384
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_UpdatePlayerInfo, "UpdatePlayerInfo" }, // 3112128128
		{ &Z_Construct_UFunction_UAuracronNetworkingBridge_ValidatePlayerAction, "ValidatePlayerAction" }, // 2119970196
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronNetworkingBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionConfig = { "CurrentSessionConfig", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, CurrentSessionConfig), Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSessionConfig_MetaData), NewProp_CurrentSessionConfig_MetaData) }; // 3299819686
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_AntiCheatConfig = { "AntiCheatConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, AntiCheatConfig), Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatConfig_MetaData), NewProp_AntiCheatConfig_MetaData) }; // 4153452695
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ReplicationConfig = { "ReplicationConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, ReplicationConfig), Z_Construct_UScriptStruct_FAuracronReplicationConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationConfig_MetaData), NewProp_ReplicationConfig_MetaData) }; // 1568173466
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionState = { "CurrentSessionState", "OnRep_SessionState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, CurrentSessionState), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSessionState_MetaData), NewProp_CurrentSessionState_MetaData) }; // 914745319
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentConnectionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentConnectionType = { "CurrentConnectionType", "OnRep_ConnectionType", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, CurrentConnectionType), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronNetworkConnectionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentConnectionType_MetaData), NewProp_CurrentConnectionType_MetaData) }; // 3190657811
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ConnectedPlayers_Inner = { "ConnectedPlayers", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPlayerInfo, METADATA_PARAMS(0, nullptr) }; // 1214137280
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ConnectedPlayers = { "ConnectedPlayers", "OnRep_ConnectedPlayers", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, ConnectedPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedPlayers_MetaData), NewProp_ConnectedPlayers_MetaData) }; // 1214137280
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics_ValueProp = { "NetworkStatistics", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics_Key_KeyProp = { "NetworkStatistics_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics = { "NetworkStatistics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, NetworkStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkStatistics_MetaData), NewProp_NetworkStatistics_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSessionCreated = { "OnSessionCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, OnSessionCreated), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSessionCreated_MetaData), NewProp_OnSessionCreated_MetaData) }; // 2361560682
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnPlayerConnected = { "OnPlayerConnected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, OnPlayerConnected), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerConnected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerConnected_MetaData), NewProp_OnPlayerConnected_MetaData) }; // 2249834433
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnPlayerDisconnected = { "OnPlayerDisconnected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, OnPlayerDisconnected), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnPlayerDisconnected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerDisconnected_MetaData), NewProp_OnPlayerDisconnected_MetaData) }; // 3695600169
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSuspiciousActivityDetected = { "OnSuspiciousActivityDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, OnSuspiciousActivityDetected), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSuspiciousActivityDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSuspiciousActivityDetected_MetaData), NewProp_OnSuspiciousActivityDetected_MetaData) }; // 1269788890
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSessionStateChanged = { "OnSessionStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronNetworkingBridge, OnSessionStateChanged), Z_Construct_UDelegateFunction_UAuracronNetworkingBridge_OnSessionStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSessionStateChanged_MetaData), NewProp_OnSessionStateChanged_MetaData) }; // 3928611927
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronNetworkingBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_AntiCheatConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ReplicationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentSessionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentConnectionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_CurrentConnectionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ConnectedPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_ConnectedPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_NetworkStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSessionCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnPlayerConnected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnPlayerDisconnected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSuspiciousActivityDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronNetworkingBridge_Statics::NewProp_OnSessionStateChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNetworkingBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronNetworkingBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNetworkingBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronNetworkingBridge_Statics::ClassParams = {
	&UAuracronNetworkingBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronNetworkingBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNetworkingBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronNetworkingBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronNetworkingBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronNetworkingBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronNetworkingBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronNetworkingBridge.OuterSingleton, Z_Construct_UClass_UAuracronNetworkingBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronNetworkingBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronNetworkingBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentSessionConfig(TEXT("CurrentSessionConfig"));
	static FName Name_CurrentSessionState(TEXT("CurrentSessionState"));
	static FName Name_CurrentConnectionType(TEXT("CurrentConnectionType"));
	static FName Name_ConnectedPlayers(TEXT("ConnectedPlayers"));
	const bool bIsValid = true
		&& Name_CurrentSessionConfig == ClassReps[(int32)ENetFields_Private::CurrentSessionConfig].Property->GetFName()
		&& Name_CurrentSessionState == ClassReps[(int32)ENetFields_Private::CurrentSessionState].Property->GetFName()
		&& Name_CurrentConnectionType == ClassReps[(int32)ENetFields_Private::CurrentConnectionType].Property->GetFName()
		&& Name_ConnectedPlayers == ClassReps[(int32)ENetFields_Private::ConnectedPlayers].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronNetworkingBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronNetworkingBridge);
UAuracronNetworkingBridge::~UAuracronNetworkingBridge() {}
// ********** End Class UAuracronNetworkingBridge **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronNetworkConnectionType_StaticEnum, TEXT("EAuracronNetworkConnectionType"), &Z_Registration_Info_UEnum_EAuracronNetworkConnectionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3190657811U) },
		{ EAuracronSessionState_StaticEnum, TEXT("EAuracronSessionState"), &Z_Registration_Info_UEnum_EAuracronSessionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 914745319U) },
		{ EAuracronServerValidationType_StaticEnum, TEXT("EAuracronServerValidationType"), &Z_Registration_Info_UEnum_EAuracronServerValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 278967133U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronNetworkingSessionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronNetworkingSessionConfiguration_Statics::NewStructOps, TEXT("AuracronNetworkingSessionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronNetworkingSessionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronNetworkingSessionConfiguration), 3299819686U) },
		{ FAuracronPlayerInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronPlayerInfo_Statics::NewStructOps, TEXT("AuracronPlayerInfo"), &Z_Registration_Info_UScriptStruct_FAuracronPlayerInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPlayerInfo), 1214137280U) },
		{ FAuracronNetworkingAntiCheatConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration_Statics::NewStructOps, TEXT("AuracronNetworkingAntiCheatConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronNetworkingAntiCheatConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronNetworkingAntiCheatConfiguration), 4153452695U) },
		{ FAuracronReplicationConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronReplicationConfiguration_Statics::NewStructOps, TEXT("AuracronReplicationConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronReplicationConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronReplicationConfiguration), 1568173466U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronNetworkingBridge, UAuracronNetworkingBridge::StaticClass, TEXT("UAuracronNetworkingBridge"), &Z_Registration_Info_UClass_UAuracronNetworkingBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronNetworkingBridge), 765095854U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_3397444554(TEXT("/Script/AuracronNetworkingBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronNetworkingBridge_h__Script_AuracronNetworkingBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
