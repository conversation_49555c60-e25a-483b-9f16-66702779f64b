{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronpcgbridge\\permoduleinline.gen.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronpcgbridge\\definitions.auracronpcgbridge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}