// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMultiplayerExample.h"

#ifdef AURACRONNETWORKINGBRIDGE_AuracronMultiplayerExample_generated_h
#error "AuracronMultiplayerExample.generated.h already included, missing '#pragma once' in AuracronMultiplayerExample.h"
#endif
#define AURACRONNETWORKINGBRIDGE_AuracronMultiplayerExample_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 

// ********** Begin Class AAuracronMultiplayerExample **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSyncGameStateAcrossNetwork); \
	DECLARE_FUNCTION(execSetupServerAuthority); \
	DECLARE_FUNCTION(execValidatePlayerAction); \
	DECLARE_FUNCTION(execInitializeAntiCheat); \
	DECLARE_FUNCTION(execRegisterGameObjectsForReplication); \
	DECLARE_FUNCTION(execSetupIrisReplication); \
	DECLARE_FUNCTION(execJoinSession); \
	DECLARE_FUNCTION(execCreateCompetitiveSession); \
	DECLARE_FUNCTION(execCreateCooperativeSession);


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_CALLBACK_WRAPPERS
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerExample_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronMultiplayerExample(); \
	friend struct Z_Construct_UClass_AAuracronMultiplayerExample_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerExample_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronMultiplayerExample, AGameModeBase, COMPILED_IN_FLAGS(0 | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronNetworkingBridge"), Z_Construct_UClass_AAuracronMultiplayerExample_NoRegister) \
	DECLARE_SERIALIZER(AAuracronMultiplayerExample)


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronMultiplayerExample(AAuracronMultiplayerExample&&) = delete; \
	AAuracronMultiplayerExample(const AAuracronMultiplayerExample&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronMultiplayerExample); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronMultiplayerExample); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronMultiplayerExample) \
	NO_API virtual ~AAuracronMultiplayerExample();


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_20_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_23_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronMultiplayerExample;

// ********** End Class AAuracronMultiplayerExample ************************************************

// ********** Begin Class AAuracronMultiplayerPlayerController *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execHandleServerReconciliation); \
	DECLARE_FUNCTION(execEnableClientPrediction); \
	DECLARE_FUNCTION(execReceiveValidatedAction); \
	DECLARE_FUNCTION(execSendActionToServer);


AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronMultiplayerPlayerController(); \
	friend struct Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronMultiplayerPlayerController, APlayerController, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronNetworkingBridge"), Z_Construct_UClass_AAuracronMultiplayerPlayerController_NoRegister) \
	DECLARE_SERIALIZER(AAuracronMultiplayerPlayerController)


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronMultiplayerPlayerController(AAuracronMultiplayerPlayerController&&) = delete; \
	AAuracronMultiplayerPlayerController(const AAuracronMultiplayerPlayerController&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronMultiplayerPlayerController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronMultiplayerPlayerController); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronMultiplayerPlayerController) \
	NO_API virtual ~AAuracronMultiplayerPlayerController();


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_117_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h_120_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronMultiplayerPlayerController;

// ********** End Class AAuracronMultiplayerPlayerController ***************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
