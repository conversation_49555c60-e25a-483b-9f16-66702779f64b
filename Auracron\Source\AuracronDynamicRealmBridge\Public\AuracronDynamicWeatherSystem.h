/**
 * AuracronDynamicWeatherSystem.h
 * 
 * Advanced dynamic weather system that creates immersive weather effects
 * that respond to gameplay events, player actions, and match progression.
 * 
 * Features:
 * - Real-time weather generation
 * - Gameplay-responsive weather changes
 * - Layer-specific weather effects
 * - Performance-optimized weather rendering
 * - Audio-visual weather integration
 * 
 * Uses UE 5.6 modern weather, VFX, and audio frameworks for production-ready
 * dynamic weather experiences.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Engine/World.h"
#include "Engine/DirectionalLight.h"
#include "Engine/SkyLight.h"
#include "Engine/ExponentialHeightFog.h"
#include "Components/LightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/ExponentialHeightFogComponent.h"
#include "GameplayTagContainer.h"
#include "TimerManager.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronDynamicWeatherSystem.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronPrismalFlow;
class AAuracronPrismalIsland;

/**
 * Dynamic weather types
 */
UENUM(BlueprintType)
enum class EDynamicWeatherType : uint8
{
    Clear           UMETA(DisplayName = "Clear"),
    Cloudy          UMETA(DisplayName = "Cloudy"),
    Rain            UMETA(DisplayName = "Rain"),
    Storm           UMETA(DisplayName = "Storm"),
    Fog             UMETA(DisplayName = "Fog"),
    Wind            UMETA(DisplayName = "Wind"),
    Mystical        UMETA(DisplayName = "Mystical"),
    Prismal         UMETA(DisplayName = "Prismal"),
    Chaos           UMETA(DisplayName = "Chaos"),
    Ethereal        UMETA(DisplayName = "Ethereal"),
    Abyssal         UMETA(DisplayName = "Abyssal"),
    Transcendent    UMETA(DisplayName = "Transcendent")
};

/**
 * Weather intensity levels
 */
UENUM(BlueprintType)
enum class EWeatherIntensity : uint8
{
    None            UMETA(DisplayName = "None"),
    Light           UMETA(DisplayName = "Light"),
    Moderate        UMETA(DisplayName = "Moderate"),
    Heavy           UMETA(DisplayName = "Heavy"),
    Extreme         UMETA(DisplayName = "Extreme"),
    Supernatural    UMETA(DisplayName = "Supernatural")
};

/**
 * Weather trigger events
 */
UENUM(BlueprintType)
enum class EWeatherTriggerEvent : uint8
{
    MatchStart      UMETA(DisplayName = "Match Start"),
    TeamFight       UMETA(DisplayName = "Team Fight"),
    IslandActivation UMETA(DisplayName = "Island Activation"),
    FlowSurge       UMETA(DisplayName = "Flow Surge"),
    LayerEvolution  UMETA(DisplayName = "Layer Evolution"),
    PlayerDeath     UMETA(DisplayName = "Player Death"),
    ObjectiveComplete UMETA(DisplayName = "Objective Complete"),
    TimeProgression UMETA(DisplayName = "Time Progression"),
    EnvironmentalShift UMETA(DisplayName = "Environmental Shift")
};

/**
 * Dynamic weather configuration
 */
USTRUCT(BlueprintType)
struct AURACRONYNAMICREALMBRIDGE_API FAuracronDynamicWeatherConfig
{
    GENERATED_BODY()

    /** Weather type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    EDynamicWeatherType WeatherType;

    /** Weather intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    EWeatherIntensity Intensity;

    /** Duration (0 = permanent) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    float Duration;

    /** Transition time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    float TransitionTime;

    /** Affected layers */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    TArray<EAuracronRealmLayer> AffectedLayers;

    /** Weather VFX system */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    TObjectPtr<UNiagaraSystem> WeatherVFX;

    /** Weather audio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    TObjectPtr<USoundBase> WeatherAudio;

    /** Gameplay effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    TArray<TSubclassOf<UGameplayEffect>> WeatherEffects;

    /** Weather tags */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    FGameplayTagContainer WeatherTags;

    FAuracronDynamicWeatherConfig()
    {
        WeatherType = EDynamicWeatherType::Clear;
        Intensity = EWeatherIntensity::Light;
        Duration = 0.0f;
        TransitionTime = 10.0f;
        WeatherVFX = nullptr;
        WeatherAudio = nullptr;
    }
};

/**
 * Weather state data
 */
USTRUCT(BlueprintType)
struct AURACRONYNAMICREALMBRIDGE_API FAuracronWeatherState
{
    GENERATED_BODY()

    /** Current weather configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    FAuracronDynamicWeatherConfig CurrentWeather;

    /** Weather start time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    float WeatherStartTime;

    /** Transition progress (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    float TransitionProgress;

    /** Is transitioning */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    bool bIsTransitioning;

    /** Previous weather type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    EDynamicWeatherType PreviousWeatherType;

    /** Weather activity level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    float ActivityLevel;

    FAuracronWeatherState()
    {
        WeatherStartTime = 0.0f;
        TransitionProgress = 0.0f;
        bIsTransitioning = false;
        PreviousWeatherType = EDynamicWeatherType::Clear;
        ActivityLevel = 1.0f;
    }
};

/**
 * Auracron Dynamic Weather System
 * 
 * Advanced weather system that creates dynamic, responsive weather effects
 * based on gameplay events, player actions, and environmental conditions.
 * Integrates with the realm evolution and flow systems for immersive experiences.
 */
UCLASS(BlueprintType)
class AURACRONYNAMICREALMBRIDGE_API UAuracronDynamicWeatherSystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // === Core Weather Management ===
    
    /** Initialize dynamic weather system */
    UFUNCTION(BlueprintCallable, Category = "Dynamic Weather")
    void InitializeWeatherSystem();

    /** Update weather system */
    UFUNCTION(BlueprintCallable, Category = "Dynamic Weather")
    void UpdateWeatherSystem(float DeltaTime);

    /** Change weather to specific type */
    UFUNCTION(BlueprintCallable, Category = "Dynamic Weather")
    void ChangeWeather(EDynamicWeatherType NewWeatherType, EWeatherIntensity Intensity, float Duration = 0.0f);

    /** Trigger weather event */
    UFUNCTION(BlueprintCallable, Category = "Dynamic Weather")
    void TriggerWeatherEvent(EWeatherTriggerEvent TriggerEvent, const FVector& Location);

    /** Get current weather state */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Dynamic Weather")
    FAuracronWeatherState GetCurrentWeatherState() const;

    // === Layer-Specific Weather ===
    
    /** Set weather for specific layer */
    UFUNCTION(BlueprintCallable, Category = "Layer Weather")
    void SetLayerWeather(EAuracronRealmLayer Layer, EDynamicWeatherType WeatherType, EWeatherIntensity Intensity);

    /** Get weather for layer */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Layer Weather")
    FAuracronDynamicWeatherConfig GetLayerWeather(EAuracronRealmLayer Layer) const;

    /** Update layer weather effects */
    UFUNCTION(BlueprintCallable, Category = "Layer Weather")
    void UpdateLayerWeatherEffects(EAuracronRealmLayer Layer, float DeltaTime);

    // === Gameplay Integration ===
    
    /** Apply weather effects to player */
    UFUNCTION(BlueprintCallable, Category = "Gameplay Weather")
    void ApplyWeatherEffectsToPlayer(APawn* Player);

    /** Remove weather effects from player */
    UFUNCTION(BlueprintCallable, Category = "Gameplay Weather")
    void RemoveWeatherEffectsFromPlayer(APawn* Player);

    /** Check if weather affects location */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gameplay Weather")
    bool DoesWeatherAffectLocation(const FVector& Location) const;

    /** Get weather intensity at location */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Gameplay Weather")
    float GetWeatherIntensityAtLocation(const FVector& Location) const;

    // === Configuration ===
    
    /** Enable/disable dynamic weather */
    UFUNCTION(BlueprintCallable, Category = "Weather Configuration")
    void SetWeatherSystemEnabled(bool bEnabled);

    /** Set weather update frequency */
    UFUNCTION(BlueprintCallable, Category = "Weather Configuration")
    void SetWeatherUpdateFrequency(float Frequency);

    /** Set weather transition speed */
    UFUNCTION(BlueprintCallable, Category = "Weather Configuration")
    void SetWeatherTransitionSpeed(float Speed);

    // === Events ===
    
    /** Called when weather changes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Weather Events")
    void OnWeatherChanged(EDynamicWeatherType OldWeather, EDynamicWeatherType NewWeather);

    /** Called when weather transition completes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Weather Events")
    void OnWeatherTransitionComplete(EDynamicWeatherType WeatherType);

    /** Called when weather event is triggered */
    UFUNCTION(BlueprintImplementableEvent, Category = "Weather Events")
    void OnWeatherEventTriggered(EWeatherTriggerEvent TriggerEvent, const FVector& Location);

protected:
    // === Configuration ===
    
    /** Enable weather system */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bWeatherSystemEnabled;

    /** Weather update frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float WeatherUpdateFrequency;

    /** Weather transition speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float WeatherTransitionSpeed;

    /** Enable gameplay weather effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableGameplayEffects;

    /** Enable performance optimization */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnablePerformanceOptimization;

    // === Weather State ===
    
    /** Current global weather state */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    FAuracronWeatherState GlobalWeatherState;

    /** Layer-specific weather states */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    TMap<EAuracronRealmLayer, FAuracronWeatherState> LayerWeatherStates;

    /** Weather configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    TMap<EDynamicWeatherType, FAuracronDynamicWeatherConfig> WeatherConfigurations;

    /** Active weather VFX components */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    TMap<EAuracronRealmLayer, TObjectPtr<UNiagaraComponent>> ActiveWeatherVFX;

    /** Active weather audio components */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather State")
    TMap<EAuracronRealmLayer, TObjectPtr<UAudioComponent>> ActiveWeatherAudio;

private:
    // === Core Implementation ===
    void InitializeWeatherConfigurations();
    void SetupWeatherTriggers();
    void StartWeatherUpdates();
    void ProcessWeatherTransitions(float DeltaTime);
    void UpdateGlobalWeather(float DeltaTime);
    void UpdateLayerWeather(EAuracronRealmLayer Layer, float DeltaTime);
    
    // === Weather Generation ===
    EDynamicWeatherType GenerateWeatherForEvent(EWeatherTriggerEvent TriggerEvent);
    EWeatherIntensity CalculateWeatherIntensity(EDynamicWeatherType WeatherType, const FVector& Location);
    float CalculateWeatherDuration(EDynamicWeatherType WeatherType, EWeatherIntensity Intensity);
    bool ShouldTriggerWeatherChange();
    
    // === Weather Effects ===
    void ApplyWeatherVFX(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig);
    void ApplyWeatherAudio(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig);
    void ApplyWeatherLighting(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig);
    void ApplyWeatherFog(EAuracronRealmLayer Layer, const FAuracronDynamicWeatherConfig& WeatherConfig);
    void RemoveWeatherEffects(EAuracronRealmLayer Layer);
    
    // === Gameplay Integration ===
    void ProcessWeatherGameplayEffects(const FAuracronDynamicWeatherConfig& WeatherConfig);
    void UpdatePlayerWeatherEffects(APawn* Player, const FAuracronDynamicWeatherConfig& WeatherConfig);
    void ApplyWeatherMovementEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity);
    void ApplyWeatherVisibilityEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity);
    void ApplyWeatherCombatEffects(APawn* Player, EDynamicWeatherType WeatherType, float Intensity);
    
    // === Environmental Integration ===
    void IntegrateWithRealmEvolution();
    void IntegrateWithFlowSystem();
    void IntegrateWithIslandSystem();
    void UpdateWeatherBasedOnRealmState();
    void UpdateWeatherBasedOnPlayerActivity();
    
    // === Utility Methods ===
    FLinearColor GetWeatherColor(EDynamicWeatherType WeatherType) const;
    float GetWeatherIntensityMultiplier(EWeatherIntensity Intensity) const;
    FVector GetWeatherWindDirection(EDynamicWeatherType WeatherType) const;
    float GetWeatherWindStrength(EDynamicWeatherType WeatherType, EWeatherIntensity Intensity) const;
    bool IsWeatherTypeValidForLayer(EDynamicWeatherType WeatherType, EAuracronRealmLayer Layer) const;
    FVector GetLayerCenter(EAuracronRealmLayer Layer) const;
    void OptimizeWeatherPerformance();
    
    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    UPROPERTY()
    TObjectPtr<ADirectionalLight> CachedSunLight;

    UPROPERTY()
    TObjectPtr<ASkyLight> CachedSkyLight;

    UPROPERTY()
    TObjectPtr<AExponentialHeightFog> CachedFog;

    // === Weather Trigger System ===
    TMap<EWeatherTriggerEvent, TArray<EDynamicWeatherType>> TriggerWeatherTypes;
    TMap<EWeatherTriggerEvent, float> TriggerProbabilities;
    TArray<EWeatherTriggerEvent> ActiveTriggers;
    
    // === Player Effect Tracking ===
    TMap<TObjectPtr<APawn>, TArray<FActiveGameplayEffectHandle>> PlayerWeatherEffects;
    TMap<TObjectPtr<APawn>, float> PlayerOriginalMovementSpeeds;
    
    // === Timers ===
    FTimerHandle WeatherUpdateTimer;
    FTimerHandle WeatherTransitionTimer;
    FTimerHandle PerformanceOptimizationTimer;
    
    // === State Tracking ===
    bool bIsInitialized;
    float LastWeatherUpdateTime;
    float LastTransitionTime;
    float LastPerformanceOptimization;
    int32 TotalWeatherChanges;
};
