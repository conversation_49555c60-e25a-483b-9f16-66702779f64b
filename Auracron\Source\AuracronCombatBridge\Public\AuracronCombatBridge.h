// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Combate 3D Vertical Bridge
// IntegraÃ§Ã£o C++ para combate 3D com targeting vertical usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "Components/ActorComponent.h"
#include "GameplayAbilities/Public/AbilitySystemComponent.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayAbilities/Public/GameplayEffect.h"
#include "Abilities/GameplayAbilityTargetActor.h"
#include "Abilities/GameplayAbilityTargetActor_Trace.h"
#include "Abilities/GameplayAbilityTargetActor_Radius.h"
// Forward declaration for GameplayAbility compatibility
class AGameplayAbilityWorldReticle;
#include "GameplayTagContainer.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/HitResult.h"
#include "Engine/OverlapResult.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"
#include "Chaos/ChaosEngineInterface.h"
#include "PhysicsEngine/BodyInstance.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
// Forward declaration for FieldSystem compatibility
class UFieldSystemComponent;
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "Components/TimelineComponent.h"
#include "Curves/CurveFloat.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "Sound/SoundCue.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "InputMappingContext.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "AIController.h"
#include "StateTreeTypes.h"
#include "StateTreeExecutionContext.h"
#include "GameplayTask.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "Async/AsyncWork.h"
#include "HAL/ThreadSafeBool.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "AuracronCombatBridge.generated.h"

// Module API Definition
#ifndef AURACRONCOMBABRIDGE_API
#define AURACRONCOMBABRIDGE_API
#endif

// Forward Declarations
class UAbilitySystemComponent;
class UGameplayAbility;
class UGameplayEffect;
class UGameplayAbilityTargetActor;
class UGameplayAbilityWorldReticle;
class UFieldSystemComponent;
class UGeometryCollectionComponent;
class UNiagaraSystem;
class UNiagaraComponent;

/**
 * EnumeraÃ§Ã£o para tipos de dano
 */
UENUM(BlueprintType)
enum class EAuracronDamageType : uint8
{
    None            UMETA(DisplayName = "None"),
    Physical        UMETA(DisplayName = "Physical"),
    Magical         UMETA(DisplayName = "Magical"),
    TrueDamage      UMETA(DisplayName = "True Damage"),
    Healing         UMETA(DisplayName = "Healing"),
    Shield          UMETA(DisplayName = "Shield"),
    Percentage      UMETA(DisplayName = "Percentage"),
    OverTime        UMETA(DisplayName = "Over Time"),
    Area            UMETA(DisplayName = "Area of Effect")
};

/**
 * EnumeraÃ§Ã£o para camadas de combate 3D
 */
UENUM(BlueprintType)
enum class EAuracronCombatLayer : uint8
{
    Surface         UMETA(DisplayName = "Surface Layer"),
    Sky             UMETA(DisplayName = "Sky Layer"),
    Underground     UMETA(DisplayName = "Underground Layer"),
    All             UMETA(DisplayName = "All Layers")
};

/**
 * EnumeraÃ§Ã£o para tipos de targeting
 */
UENUM(BlueprintType)
enum class EAuracronTargetingType : uint8
{
    None                UMETA(DisplayName = "None"),
    SingleTarget        UMETA(DisplayName = "Single Target"),
    LineTrace           UMETA(DisplayName = "Line Trace"),
    AreaOfEffect        UMETA(DisplayName = "Area of Effect"),
    Cone                UMETA(DisplayName = "Cone"),
    Sphere              UMETA(DisplayName = "Sphere"),
    Box                 UMETA(DisplayName = "Box"),
    Cylinder            UMETA(DisplayName = "Cylinder"),
    VerticalColumn      UMETA(DisplayName = "Vertical Column"),
    CrossLayer          UMETA(DisplayName = "Cross Layer"),
    GroundTarget        UMETA(DisplayName = "Ground Target"),
    SkyTarget           UMETA(DisplayName = "Sky Target"),
    UndergroundTarget   UMETA(DisplayName = "Underground Target"),
    SmartTarget         UMETA(DisplayName = "Smart Target"),
    PredictiveTarget    UMETA(DisplayName = "Predictive Target"),
    ChainTarget         UMETA(DisplayName = "Chain Target")
};

/**
 * Elemental damage types for advanced combat
 */
UENUM(BlueprintType)
enum class EAuracronElementalType : uint8
{
    None            UMETA(DisplayName = "None"),
    Fire            UMETA(DisplayName = "Fire"),
    Water           UMETA(DisplayName = "Water"),
    Earth           UMETA(DisplayName = "Earth"),
    Air             UMETA(DisplayName = "Air"),
    Lightning       UMETA(DisplayName = "Lightning"),
    Ice             UMETA(DisplayName = "Ice"),
    Poison          UMETA(DisplayName = "Poison"),
    Shadow          UMETA(DisplayName = "Shadow"),
    Light           UMETA(DisplayName = "Light"),
    Chaos           UMETA(DisplayName = "Chaos"),
    Order           UMETA(DisplayName = "Order"),
    Void            UMETA(DisplayName = "Void")
};

/**
 * AI Combat behavior types
 */
UENUM(BlueprintType)
enum class EAuracronAICombatBehavior : uint8
{
    Passive         UMETA(DisplayName = "Passive"),
    Defensive       UMETA(DisplayName = "Defensive"),
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Tactical        UMETA(DisplayName = "Tactical"),
    Berserker       UMETA(DisplayName = "Berserker"),
    Support         UMETA(DisplayName = "Support"),
    Assassin        UMETA(DisplayName = "Assassin"),
    Tank            UMETA(DisplayName = "Tank"),
    Adaptive        UMETA(DisplayName = "Adaptive"),
    Learning        UMETA(DisplayName = "Learning")
};

/**
 * Combat combo types
 */
UENUM(BlueprintType)
enum class EAuracronComboType : uint8
{
    None            UMETA(DisplayName = "None"),
    Light           UMETA(DisplayName = "Light Combo"),
    Heavy           UMETA(DisplayName = "Heavy Combo"),
    Special         UMETA(DisplayName = "Special Combo"),
    Ultimate        UMETA(DisplayName = "Ultimate Combo"),
    Elemental       UMETA(DisplayName = "Elemental Combo"),
    Chain           UMETA(DisplayName = "Chain Combo"),
    Finisher        UMETA(DisplayName = "Finisher Combo")
};

/**
 * Estrutura para configuraÃ§Ã£o de dano
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronDamageConfiguration
{
    GENERATED_BODY()

    /** Tipo de dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    EAuracronDamageType DamageType = EAuracronDamageType::Physical;

    /** Valor base do dano */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float BaseDamage = 100.0f;

    /** Escalonamento com Attack Damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float AttackDamageScaling = 1.0f;

    /** Escalonamento com Ability Power */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float AbilityPowerScaling = 0.0f;

    /** Escalonamento com HP mÃ¡ximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MaxHealthScaling = 0.0f;

    /** Escalonamento com HP atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float CurrentHealthScaling = 0.0f;

    /** Pode causar crÃ­tico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bCanCrit = true;

    /** Ignora armadura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bIgnoresArmor = false;

    /** Ignora resistÃªncia mÃ¡gica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bIgnoresMagicResistance = false;

    /** Penetra shields */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bPenetratesShields = false;

    /** Dano ao longo do tempo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    bool bDamageOverTime = false;

    /** DuraÃ§Ã£o do DoT (se aplicÃ¡vel) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.1", ClampMax = "60.0"))
    float DotDuration = 5.0f;

    /** Intervalo do DoT (se aplicÃ¡vel) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float DotInterval = 1.0f;

    /** Camadas afetadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage Configuration")
    TArray<EAuracronCombatLayer> AffectedLayers;
};

/**
 * Estrutura para configuraÃ§Ã£o de targeting 3D
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronTargetingConfiguration
{
    GENERATED_BODY()

    /** Tipo de targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    EAuracronTargetingType TargetingType = EAuracronTargetingType::SingleTarget;

    /** Alcance mÃ¡ximo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "50.0", ClampMax = "5000.0"))
    float MaxRange = 800.0f;

    /** Alcance mÃ­nimo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "1000.0"))
    float MinRange = 0.0f;

    /** Raio da Ã¡rea de efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float AreaOfEffectRadius = 300.0f;

    /** Altura da Ã¡rea de efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float AreaOfEffectHeight = 200.0f;

    /** Ã‚ngulo do cone (para targeting cÃ´nico) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "1.0", ClampMax = "360.0"))
    float ConeAngle = 45.0f;

    /** Permite targeting atravÃ©s de camadas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bAllowCrossLayerTargeting = false;

    /** Requer line of sight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bRequiresLineOfSight = true;

    /** Ignora obstÃ¡culos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bIgnoresObstacles = false;

    /** Targeting apenas aliados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bTargetAlliesOnly = false;

    /** Targeting apenas inimigos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bTargetEnemiesOnly = true;

    /** Inclui o prÃ³prio caster */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    bool bIncludeSelf = false;

    /** Camadas de targeting permitidas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<EAuracronCombatLayer> AllowedTargetingLayers;

    /** Canais de colisÃ£o para line of sight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<TEnumAsByte<ECollisionChannel>> LineOfSightChannels;

    /** Canais de colisÃ£o para targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration")
    TArray<TEnumAsByte<ECollisionChannel>> TargetingChannels;

    /** Offset vertical para targeting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "-1000.0", ClampMax = "1000.0"))
    float VerticalOffset = 0.0f;

    /** PrecisÃ£o do targeting (0.0 = perfeito, 1.0 = muito impreciso) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TargetingAccuracy = 0.95f;
};

/**
 * Estrutura para resultado de targeting
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronTargetingResult
{
    GENERATED_BODY()

    /** Targeting foi bem-sucedido */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    bool bSuccessful = false;

    /** Atores alvo encontrados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<TObjectPtr<AActor>> TargetActors;

    /** PosiÃ§Ãµes alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<FVector> TargetLocations;

    /** InformaÃ§Ãµes de hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    TArray<FHitResult> HitResults;

    /** Camada onde o targeting ocorreu */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    EAuracronCombatLayer TargetLayer = EAuracronCombatLayer::Surface;

    /** DistÃ¢ncia atÃ© o alvo principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result", meta = (ClampMin = "0.0"))
    float DistanceToTarget = 0.0f;

    /** Ã‚ngulo atÃ© o alvo principal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result", meta = (ClampMin = "0.0", ClampMax = "360.0"))
    float AngleToTarget = 0.0f;

    /** Tem line of sight para o alvo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Targeting Result")
    bool bHasLineOfSight = false;
};

/**
 * Estrutura para configuraÃ§Ã£o de efeitos de combate
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronCombatEffectsConfiguration
{
    GENERATED_BODY()

    /** Efeito de impacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ImpactEffect;

    /** Efeito de projÃ©til */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ProjectileEffect;

    /** Efeito de Ã¡rea */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> AreaEffect;

    /** Efeito de cura */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> HealingEffect;

    /** Efeito de shield */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<UNiagaraSystem> ShieldEffect;

    /** Som de impacto */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<USoundCue> ImpactSound;

    /** Som de projÃ©til */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<USoundCue> ProjectileSound;

    /** Som de habilidade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    TSoftObjectPtr<USoundBase> AbilitySound;

    /** DuraÃ§Ã£o dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float EffectDuration = 2.0f;

    /** Escala dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float EffectScale = 1.0f;

    /** Cor dos efeitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    FLinearColor EffectColor = FLinearColor::White;

    /** Usar Field System para destruiÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects")
    bool bUseFieldSystemDestruction = false;

    /** ForÃ§a do Field System */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float FieldSystemForce = 1000.0f;

    /** Raio do Field System */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Effects", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float FieldSystemRadius = 500.0f;
};

/**
 * Enhanced Input combat action configuration
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronEnhancedInputConfig
{
    GENERATED_BODY()

    /** Input Action for basic attack */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputAction> BasicAttackAction;

    /** Input Action for heavy attack */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputAction> HeavyAttackAction;

    /** Input Action for special ability */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputAction> SpecialAbilityAction;

    /** Input Action for dodge/dash */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputAction> DodgeAction;

    /** Input Action for block/parry */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputAction> BlockAction;

    /** Input Mapping Context */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input")
    TSoftObjectPtr<UInputMappingContext> CombatInputContext;

    /** Combo window timing */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float ComboWindowTime = 0.8f;

    /** Maximum combo chain length */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enhanced Input", meta = (ClampMin = "1", ClampMax = "10"))
    int32 MaxComboChain = 5;

    FAuracronEnhancedInputConfig()
    {
        ComboWindowTime = 0.8f;
        MaxComboChain = 5;
    }
};

/**
 * AI Combat behavior configuration
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronAICombatConfig
{
    GENERATED_BODY()

    /** AI Behavior type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    EAuracronAICombatBehavior BehaviorType;

    /** Behavior Tree asset */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    TSoftObjectPtr<UBehaviorTree> BehaviorTree;

    /** State Tree asset */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    TSoftObjectPtr<UStateTree> StateTree;

    /** Aggression level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AggressionLevel;

    /** Reaction time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float ReactionTime;

    /** Combat range preference */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat", meta = (ClampMin = "100.0", ClampMax = "2000.0"))
    float PreferredCombatRange;

    /** Enable learning behavior */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat")
    bool bEnableLearning;

    /** Learning adaptation rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Combat", meta = (ClampMin = "0.01", ClampMax = "1.0"))
    float LearningRate;

    FAuracronAICombatConfig()
    {
        BehaviorType = EAuracronAICombatBehavior::Tactical;
        AggressionLevel = 0.5f;
        ReactionTime = 0.3f;
        PreferredCombatRange = 800.0f;
        bEnableLearning = true;
        LearningRate = 0.1f;
    }
};

/**
 * Elemental damage configuration
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronElementalDamageConfig
{
    GENERATED_BODY()

    /** Primary elemental type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage")
    EAuracronElementalType PrimaryElement;

    /** Secondary elemental type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage")
    EAuracronElementalType SecondaryElement;

    /** Elemental damage multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float ElementalMultiplier;

    /** Elemental resistances */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage")
    TMap<EAuracronElementalType, float> ElementalResistances;

    /** Elemental weaknesses */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage")
    TMap<EAuracronElementalType, float> ElementalWeaknesses;

    /** Status effect chance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float StatusEffectChance;

    /** Status effect duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Elemental Damage", meta = (ClampMin = "1.0", ClampMax = "30.0"))
    float StatusEffectDuration;

    FAuracronElementalDamageConfig()
    {
        PrimaryElement = EAuracronElementalType::None;
        SecondaryElement = EAuracronElementalType::None;
        ElementalMultiplier = 1.0f;
        StatusEffectChance = 0.1f;
        StatusEffectDuration = 5.0f;
    }
};

/**
 * Combat analytics data
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronCombatAnalytics
{
    GENERATED_BODY()

    /** Total damage dealt */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    float TotalDamageDealt;

    /** Total damage received */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    float TotalDamageReceived;

    /** Number of hits landed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 HitsLanded;

    /** Number of hits missed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 HitsMissed;

    /** Number of critical hits */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 CriticalHits;

    /** Number of blocks/parries */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 BlocksParries;

    /** Number of dodges */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 Dodges;

    /** Longest combo achieved */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    int32 LongestCombo;

    /** Average reaction time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    float AverageReactionTime;

    /** Combat efficiency score */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    float EfficiencyScore;

    /** Timestamp of last update */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Analytics")
    FDateTime LastUpdateTime;

    FAuracronCombatAnalytics()
    {
        TotalDamageDealt = 0.0f;
        TotalDamageReceived = 0.0f;
        HitsLanded = 0;
        HitsMissed = 0;
        CriticalHits = 0;
        BlocksParries = 0;
        Dodges = 0;
        LongestCombo = 0;
        AverageReactionTime = 0.0f;
        EfficiencyScore = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Combat combo configuration
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronComboConfig
{
    GENERATED_BODY()

    /** Combo type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    EAuracronComboType ComboType;

    /** Input sequence for combo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<FString> InputSequence;

    /** Timing windows for each input */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<float> TimingWindows;

    /** Damage multipliers for each hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<float> DamageMultipliers;

    /** Effects for each hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<TSoftObjectPtr<UNiagaraSystem>> ComboEffects;

    /** Sounds for each hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System")
    TArray<TSoftObjectPtr<USoundCue>> ComboSounds;

    /** Required combo points */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System", meta = (ClampMin = "1", ClampMax = "100"))
    int32 RequiredComboPoints;

    /** Combo cooldown */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combo System", meta = (ClampMin = "0.0", ClampMax = "60.0"))
    float ComboCooldown;

    FAuracronComboConfig()
    {
        ComboType = EAuracronComboType::Light;
        RequiredComboPoints = 10;
        ComboCooldown = 5.0f;
    }
};

/**
 * Advanced physics destruction configuration
 */
USTRUCT(BlueprintType)
struct AURACRONCOMBABRIDGE_API FAuracronAdvancedDestructionConfig
{
    GENERATED_BODY()

    /** Enable Chaos destruction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction")
    bool bEnableChaosDestruction;

    /** Destruction threshold */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction", meta = (ClampMin = "0.0", ClampMax = "10000.0"))
    float DestructionThreshold;

    /** Fracture impulse */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction", meta = (ClampMin = "0.0", ClampMax = "50000.0"))
    float FractureImpulse;

    /** Debris lifetime */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float DebrisLifetime;

    /** Enable procedural damage */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction")
    bool bEnableProceduralDamage;

    /** Damage propagation radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced Destruction", meta = (ClampMin = "0.0", ClampMax = "2000.0"))
    float DamagePropagationRadius;

    FAuracronAdvancedDestructionConfig()
    {
        bEnableChaosDestruction = true;
        DestructionThreshold = 1000.0f;
        FractureImpulse = 5000.0f;
        DebrisLifetime = 30.0f;
        bEnableProceduralDamage = true;
        DamagePropagationRadius = 500.0f;
    }
};

/**
 * Classe principal do Bridge para Sistema de Combate 3D Vertical
 * ResponsÃ¡vel pelo gerenciamento completo de combate com targeting vertical
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Combat", meta = (DisplayName = "AURACRON Combat Bridge", BlueprintSpawnableComponent))
class AURACRONCOMBABRIDGE_API UAuracronCombatBridge : public UActorComponent
{
    GENERATED_BODY()

public:
    UAuracronCombatBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Combat Management ===

    /**
     * Executar targeting 3D
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    FAuracronTargetingResult ExecuteTargeting(const FAuracronTargetingConfiguration& TargetingConfig, const FVector& SourceLocation, const FVector& TargetDirection);

    /**
     * Aplicar dano a um alvo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    bool ApplyDamageToTarget(AActor* TargetActor, const FAuracronDamageConfiguration& DamageConfig, AActor* SourceActor = nullptr);

    /**
     * Aplicar dano em Ã¡rea
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    bool ApplyAreaDamage(const FVector& Location, const FAuracronDamageConfiguration& DamageConfig, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor = nullptr);

    /**
     * Verificar line of sight entre dois pontos
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    bool CheckLineOfSight(const FVector& StartLocation, const FVector& EndLocation, const TArray<TEnumAsByte<ECollisionChannel>>& CollisionChannels);

    /**
     * Obter alvos em raio
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInRadius(const FVector& Location, float Radius, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Obter alvos em cone
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInCone(const FVector& SourceLocation, const FVector& Direction, float Range, float ConeAngle, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Obter alvos em coluna vertical
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    TArray<AActor*> GetTargetsInVerticalColumn(const FVector& Location, float Radius, float Height, const FAuracronTargetingConfiguration& TargetingConfig);

    /**
     * Calcular dano final
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Damage", CallInEditor)
    float CalculateFinalDamage(const FAuracronDamageConfiguration& DamageConfig, AActor* SourceActor, AActor* TargetActor);

    /**
     * Verificar se alvo Ã© vÃ¡lido
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Targeting", CallInEditor)
    bool IsValidTarget(AActor* TargetActor, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor = nullptr);

    /**
     * Obter camada de combate de um ator
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Layers", CallInEditor)
    EAuracronCombatLayer GetActorCombatLayer(AActor* Actor) const;

    // === Effects and Destruction ===

    /**
     * Spawnar efeitos de combate
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Effects", CallInEditor)
    bool SpawnCombatEffects(const FVector& Location, const FAuracronCombatEffectsConfiguration& EffectsConfig);

    /**
     * Aplicar Field System para destruiÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Destruction", CallInEditor)
    bool ApplyFieldSystemDestruction(const FVector& Location, float Force, float Radius);

    /**
     * Criar explosÃ£o com Chaos Physics
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Destruction", CallInEditor)
    bool CreateChaosExplosion(const FVector& Location, float Force, float Radius, bool bAffectAllLayers = false);

    // === Enhanced Input System ===

    /** Initialize Enhanced Input system */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Enhanced Input")
    bool InitializeEnhancedInputSystem();

    /** Setup combat input actions */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Enhanced Input")
    void SetupCombatInputActions(const FAuracronEnhancedInputConfig& InputConfig);

    /** Process combo input */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Enhanced Input")
    bool ProcessComboInput(const FString& InputName, float InputTime);

    /** Execute combo sequence */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Enhanced Input")
    bool ExecuteComboSequence(const FAuracronComboConfig& ComboConfig);

    /** Reset combo chain */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Enhanced Input")
    void ResetComboChain();

    // === AI Combat System ===

    /** Initialize AI combat behavior */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|AI")
    bool InitializeAICombatBehavior(const FAuracronAICombatConfig& AIConfig);

    /** Update AI combat state */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|AI")
    void UpdateAICombatState(AActor* TargetActor, float DeltaTime);

    /** Execute AI combat decision */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|AI")
    bool ExecuteAICombatDecision(const FString& DecisionType, const TMap<FString, FString>& Parameters);

    /** Adapt AI behavior based on combat data */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|AI")
    void AdaptAIBehavior(const FAuracronCombatAnalytics& CombatData);

    // === Elemental Damage System ===

    /** Apply elemental damage */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Elemental")
    float ApplyElementalDamage(AActor* TargetActor, const FAuracronElementalDamageConfig& ElementalConfig, float BaseDamage);

    /** Calculate elemental effectiveness */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Elemental")
    float CalculateElementalEffectiveness(EAuracronElementalType AttackElement, EAuracronElementalType DefenseElement);

    /** Apply elemental status effect */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Elemental")
    bool ApplyElementalStatusEffect(AActor* TargetActor, EAuracronElementalType ElementType, float Duration);

    /** Get elemental resistances */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Elemental")
    TMap<EAuracronElementalType, float> GetElementalResistances(AActor* Actor) const;

    // === Combat Analytics ===

    /** Update combat analytics */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Analytics")
    void UpdateCombatAnalytics(const FString& EventType, const TMap<FString, FString>& EventData);

    /** Get combat analytics */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Analytics")
    FAuracronCombatAnalytics GetCombatAnalytics() const;

    /** Calculate combat efficiency */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AURACRON Combat|Analytics")
    float CalculateCombatEfficiency() const;

    /** Export combat data */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Analytics")
    bool ExportCombatData(const FString& FilePath) const;

    // === Advanced Destruction ===

    /** Create advanced destruction */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Advanced Destruction")
    bool CreateAdvancedDestruction(const FVector& Location, const FAuracronAdvancedDestructionConfig& DestructionConfig);

    /** Apply procedural damage */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Advanced Destruction")
    bool ApplyProceduralDamage(AActor* TargetActor, const FVector& ImpactLocation, float Damage);

    /** Simulate destruction physics */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Combat|Advanced Destruction")
    void SimulateDestructionPhysics(const FVector& Location, float Force, float Radius);

protected:
    // === Internal Management Methods ===

    /** Inicializar sistema de combate */
    bool InitializeCombatSystem();

    /** Configurar canais de colisÃ£o */
    bool SetupCollisionChannels();

    /** Executar line trace */
    bool ExecuteLineTrace(const FVector& Start, const FVector& End, FHitResult& OutHit, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Executar sphere trace */
    bool ExecuteSphereTrace(const FVector& Start, const FVector& End, float Radius, TArray<FHitResult>& OutHits, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Executar overlap test */
    bool ExecuteOverlapTest(const FVector& Location, float Radius, TArray<FOverlapResult>& OutOverlaps, const TArray<TEnumAsByte<ECollisionChannel>>& Channels);

    /** Filtrar alvos por configuraÃ§Ã£o */
    TArray<AActor*> FilterTargetsByConfiguration(const TArray<AActor*>& PotentialTargets, const FAuracronTargetingConfiguration& TargetingConfig, AActor* SourceActor);

    /** Validar configuraÃ§Ã£o de targeting */
    bool ValidateTargetingConfiguration(const FAuracronTargetingConfiguration& TargetingConfig) const;

    /** Validar configuraÃ§Ã£o de dano */
    bool ValidateDamageConfiguration(const FAuracronDamageConfiguration& DamageConfig) const;

    // === Enhanced Input Implementation ===
    void InitializeInputMappingContext();
    void BindInputActions();
    void ProcessInputBuffer();
    bool ValidateComboSequence(const TArray<FString>& InputSequence, const TArray<float>& TimingSequence);
    void ExecuteComboStep(int32 ComboStep, const FAuracronComboConfig& ComboConfig);
    void HandleComboTimeout();

    // === AI Combat Implementation ===
    void InitializeBehaviorTree();
    void InitializeStateTree();
    void UpdateAIBlackboard(const TMap<FString, FString>& UpdateData);
    void ProcessAILearning(const FAuracronCombatAnalytics& CombatData);
    FString SelectAICombatAction(const TMap<FString, float>& ContextData);
    void ExecuteAIAction(const FString& ActionName, const TMap<FString, FString>& Parameters);

    // === Elemental System Implementation ===
    void InitializeElementalSystem();
    float CalculateElementalDamageModifier(EAuracronElementalType AttackElement, const TMap<EAuracronElementalType, float>& Resistances);
    void ApplyElementalEffect(AActor* TargetActor, EAuracronElementalType ElementType, float Intensity, float Duration);
    void ProcessElementalInteractions(EAuracronElementalType Element1, EAuracronElementalType Element2, const FVector& Location);
    void UpdateElementalEffects(float DeltaTime);

    // === Analytics Implementation ===
    void InitializeAnalyticsSystem();
    void LogCombatEvent(const FString& EventType, const TMap<FString, FString>& EventData);
    void UpdateAnalyticsMetrics();
    void ProcessAnalyticsData();
    void ExportAnalyticsToFile(const FString& FilePath) const;

    // === Advanced Destruction Implementation ===
    void InitializeChaosDestruction();
    void CreateDestructionField(const FVector& Location, float Force, float Radius);
    void ProcessDestructionDamage(AActor* Actor, const FVector& ImpactLocation, float Damage);
    void SimulateFracturePhysics(UPrimitiveComponent* Component, const FVector& ImpactLocation, float Force);
    void ManageDebrisLifetime();

public:
    // === Configuration Properties ===

    /** ConfiguraÃ§Ãµes de targeting padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronTargetingConfiguration DefaultTargetingConfig;

    /** ConfiguraÃ§Ãµes de dano padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronDamageConfiguration DefaultDamageConfig;

    /** ConfiguraÃ§Ãµes de efeitos padrÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronCombatEffectsConfiguration DefaultEffectsConfig;

    /** Enhanced Input configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronEnhancedInputConfig EnhancedInputConfig;

    /** AI Combat configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronAICombatConfig AICombatConfig;

    /** Elemental damage configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronElementalDamageConfig ElementalDamageConfig;

    /** Advanced destruction configuration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAuracronAdvancedDestructionConfig AdvancedDestructionConfig;

    /** Available combo configurations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TArray<FAuracronComboConfig> AvailableCombos;

    /** Camada de combate atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_CurrentCombatLayer)
    EAuracronCombatLayer CurrentCombatLayer = EAuracronCombatLayer::Surface;

    /** Ãšltimo resultado de targeting */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    FAuracronTargetingResult LastTargetingResult;

    /** ReferÃªncia ao AbilitySystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAbilitySystemComponent> AbilitySystemComponent;

    /** ReferÃªncia ao FieldSystemComponent */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UFieldSystemComponent> FieldSystemComponent;

    /** Enhanced Input Component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UEnhancedInputComponent> EnhancedInputComponent;

    /** Current combat analytics */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    FAuracronCombatAnalytics CurrentCombatAnalytics;

    /** Current combo chain */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<FString> CurrentComboChain;

    /** Current combo points */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    int32 CurrentComboPoints;

    /** Active elemental effects */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TMap<EAuracronElementalType, float> ActiveElementalEffects;

private:
    // === Internal State ===

    /** Sistema inicializado */
    bool bSystemInitialized = false;

    /** Componentes de efeitos ativos */
    TArray<TObjectPtr<UNiagaraComponent>> ActiveCombatEffects;

    /** Mutex para thread safety */
    mutable FCriticalSection CombatMutex;

    // === Enhanced Input State ===
    TArray<FString> ComboInputBuffer;
    TArray<float> ComboTimingBuffer;
    float LastComboInputTime;
    int32 CurrentComboIndex;
    bool bComboWindowActive;

    // === AI Combat State ===
    TObjectPtr<UBehaviorTree> CurrentBehaviorTree;
    TObjectPtr<UStateTree> CurrentStateTree;
    TObjectPtr<UBlackboardComponent> AIBlackboard;
    TMap<FString, float> AILearningData;
    float LastAIDecisionTime;

    // === Analytics State ===
    TArray<FString> CombatEventLog;
    TMap<FString, int32> EventCounters;
    float CombatStartTime;
    float LastAnalyticsUpdate;

    // === Async Operations ===
    FStreamableManager StreamableManager;
    TArray<TSharedPtr<FStreamableHandle>> ActiveStreamingHandles;

    // === Replication Callbacks ===

    UFUNCTION()
    void OnRep_CurrentCombatLayer();

public:
    // === Delegates ===

    /** Delegate chamado quando targeting Ã© executado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTargetingExecuted, FAuracronTargetingResult, Result, FAuracronTargetingConfiguration, Config);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnTargetingExecuted OnTargetingExecuted;

    /** Delegate chamado quando dano Ã© aplicado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnDamageApplied, AActor*, TargetActor, float, DamageAmount, EAuracronDamageType, DamageType);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnDamageApplied OnDamageApplied;

    /** Delegate chamado quando camada de combate muda */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCombatLayerChanged, EAuracronCombatLayer, OldLayer, EAuracronCombatLayer, NewLayer);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnCombatLayerChanged OnCombatLayerChanged;

    /** Delegate chamado quando efeitos sÃ£o spawnados */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCombatEffectsSpawned, FVector, Location, FAuracronCombatEffectsConfiguration, EffectsConfig);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnCombatEffectsSpawned OnCombatEffectsSpawned;

    /** Delegate chamado quando combo Ã© executado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnComboExecuted, EAuracronComboType, ComboType, int32, ComboStep, float, DamageMultiplier);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnComboExecuted OnComboExecuted;

    /** Delegate chamado quando dano elemental Ã© aplicado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_FourParams(FOnElementalDamageApplied, AActor*, TargetActor, EAuracronElementalType, ElementType, float, Damage, bool, bStatusEffectApplied);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnElementalDamageApplied OnElementalDamageApplied;

    /** Delegate chamado quando AI toma decisÃ£o de combate */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAICombatDecision, EAuracronAICombatBehavior, BehaviorType, FString, DecisionType, float, ConfidenceLevel);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnAICombatDecision OnAICombatDecision;

    /** Delegate chamado quando destruiÃ§Ã£o avanÃ§ada ocorre */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAdvancedDestruction, FVector, Location, float, DestructionForce, int32, AffectedObjects);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnAdvancedDestruction OnAdvancedDestruction;

    /** Delegate chamado quando analytics sÃ£o atualizados */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCombatAnalyticsUpdated, FAuracronCombatAnalytics, Analytics, float, EfficiencyScore);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Combat|Events")
    FOnCombatAnalyticsUpdated OnCombatAnalyticsUpdated;
};

