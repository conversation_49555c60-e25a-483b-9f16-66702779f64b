// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPhysicsBridge.h"

#ifdef AURACRONPHYSICSBRIDGE_AuracronPhysicsBridge_generated_h
#error "AuracronPhysicsBridge.generated.h already included, missing '#pragma once' in AuracronPhysicsBridge.h"
#endif
#define AURACRONPHYSICSBRIDGE_AuracronPhysicsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AGeometryCollectionActor;
class UPhysicalMaterial;
enum class EAuracronFluidType : uint8;
enum class EAuracronPhysicsQuality : uint8;
struct FAuracronAdvancedConstraintConfig;
struct FAuracronChaosClothConfig;
struct FAuracronDestructionConfiguration;
struct FAuracronFieldSystemConfiguration;
struct FAuracronFluidSimulationConfig;
struct FAuracronSoftBodyConfig;

// ********** Begin ScriptStruct FAuracronChaosPhysicsConfiguration ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_161_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChaosPhysicsConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChaosPhysicsConfiguration;
// ********** End ScriptStruct FAuracronChaosPhysicsConfiguration **********************************

// ********** Begin ScriptStruct FAuracronDestructionConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_230_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDestructionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDestructionConfiguration;
// ********** End ScriptStruct FAuracronDestructionConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronFieldSystemConfiguration *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_299_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFieldSystemConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFieldSystemConfiguration;
// ********** End ScriptStruct FAuracronFieldSystemConfiguration ***********************************

// ********** Begin ScriptStruct FAuracronFluidSimulationConfig ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_364_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronFluidSimulationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronFluidSimulationConfig;
// ********** End ScriptStruct FAuracronFluidSimulationConfig **************************************

// ********** Begin ScriptStruct FAuracronSoftBodyConfig *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_452_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSoftBodyConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSoftBodyConfig;
// ********** End ScriptStruct FAuracronSoftBodyConfig *********************************************

// ********** Begin ScriptStruct FAuracronAdvancedConstraintConfig *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_525_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAdvancedConstraintConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAdvancedConstraintConfig;
// ********** End ScriptStruct FAuracronAdvancedConstraintConfig ***********************************

// ********** Begin ScriptStruct FAuracronChaosClothConfig *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_616_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronChaosClothConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronChaosClothConfig;
// ********** End ScriptStruct FAuracronChaosClothConfig *******************************************

// ********** Begin Delegate FOnObjectDestroyed ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1221_DELEGATE \
static void FOnObjectDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectDestroyed, AActor* DestroyedActor, FAuracronDestructionConfiguration DestructionConfig);


// ********** End Delegate FOnObjectDestroyed ******************************************************

// ********** Begin Delegate FOnFieldSystemApplied *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1226_DELEGATE \
static void FOnFieldSystemApplied_DelegateWrapper(const FMulticastScriptDelegate& OnFieldSystemApplied, FVector Location, FAuracronFieldSystemConfiguration FieldConfig);


// ********** End Delegate FOnFieldSystemApplied ***************************************************

// ********** Begin Delegate FOnFluidSimulationCreated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1231_DELEGATE \
static void FOnFluidSimulationCreated_DelegateWrapper(const FMulticastScriptDelegate& OnFluidSimulationCreated, FVector Location, EAuracronFluidType FluidType, int32 ParticleCount);


// ********** End Delegate FOnFluidSimulationCreated ***********************************************

// ********** Begin Delegate FOnSoftBodyDeformed ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1236_DELEGATE \
static void FOnSoftBodyDeformed_DelegateWrapper(const FMulticastScriptDelegate& OnSoftBodyDeformed, AActor* TargetActor, FVector DeformationLocation, float DeformationForce, float DeformationRadius);


// ********** End Delegate FOnSoftBodyDeformed *****************************************************

// ********** Begin Delegate FOnClothTorn **********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1241_DELEGATE \
static void FOnClothTorn_DelegateWrapper(const FMulticastScriptDelegate& OnClothTorn, AActor* ClothActor, FVector TearLocation, float TearRadius);


// ********** End Delegate FOnClothTorn ************************************************************

// ********** Begin Delegate FOnConstraintBroken ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1246_DELEGATE \
static void FOnConstraintBroken_DelegateWrapper(const FMulticastScriptDelegate& OnConstraintBroken, int32 ConstraintID, AActor* FirstActor, AActor* SecondActor);


// ********** End Delegate FOnConstraintBroken *****************************************************

// ********** Begin Delegate FOnVehicleCreated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1251_DELEGATE \
static void FOnVehicleCreated_DelegateWrapper(const FMulticastScriptDelegate& OnVehicleCreated, AActor* VehicleActor, const FString& VehicleType);


// ********** End Delegate FOnVehicleCreated *******************************************************

// ********** Begin Delegate FOnPhysicsPerformanceUpdated ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_1256_DELEGATE \
static void FOnPhysicsPerformanceUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnPhysicsPerformanceUpdated, float FrameTime, int32 ActiveObjects, float MemoryUsage);


// ********** End Delegate FOnPhysicsPerformanceUpdated ********************************************

// ********** Begin Class UAuracronPhysicsBridge ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execExportPhysicsAnalytics); \
	DECLARE_FUNCTION(execGetPhysicsMemoryUsage); \
	DECLARE_FUNCTION(execGetActivePhysicsObjectCount); \
	DECLARE_FUNCTION(execGetPhysicsPerformanceMetrics); \
	DECLARE_FUNCTION(execSetMaterialElectricalProperties); \
	DECLARE_FUNCTION(execEnableMaterialTemperatureSimulation); \
	DECLARE_FUNCTION(execSetMaterialSurfaceProperties); \
	DECLARE_FUNCTION(execCreateAdvancedPhysicalMaterial); \
	DECLARE_FUNCTION(execSetPhysicsAuthority); \
	DECLARE_FUNCTION(execSynchronizePhysicsState); \
	DECLARE_FUNCTION(execSetPhysicsPrediction); \
	DECLARE_FUNCTION(execEnablePhysicsReplication); \
	DECLARE_FUNCTION(execInitializeNetworkPhysicsSystem); \
	DECLARE_FUNCTION(execEnableVehicleDifferential); \
	DECLARE_FUNCTION(execApplyVehicleAerodynamics); \
	DECLARE_FUNCTION(execSetVehicleTireProperties); \
	DECLARE_FUNCTION(execSetVehicleSuspensionProperties); \
	DECLARE_FUNCTION(execCreateAdvancedVehicle); \
	DECLARE_FUNCTION(execInitializeChaosVehicleSystem); \
	DECLARE_FUNCTION(execTearClothAtLocation); \
	DECLARE_FUNCTION(execPinClothVertices); \
	DECLARE_FUNCTION(execEnableClothMachineLearning); \
	DECLARE_FUNCTION(execSetClothWindProperties); \
	DECLARE_FUNCTION(execCreateClothSimulation); \
	DECLARE_FUNCTION(execInitializeChaosClothSystem); \
	DECLARE_FUNCTION(execSetConstraintSpringProperties); \
	DECLARE_FUNCTION(execEnableConstraintMotor); \
	DECLARE_FUNCTION(execBreakConstraint); \
	DECLARE_FUNCTION(execModifyConstraintProperties); \
	DECLARE_FUNCTION(execCreateAdvancedConstraint); \
	DECLARE_FUNCTION(execFractureSoftBody); \
	DECLARE_FUNCTION(execEnableSoftBodyPlasticity); \
	DECLARE_FUNCTION(execSetSoftBodyMaterialProperties); \
	DECLARE_FUNCTION(execApplySoftBodyDeformation); \
	DECLARE_FUNCTION(execConvertActorToSoftBody); \
	DECLARE_FUNCTION(execInitializeSoftBodySystem); \
	DECLARE_FUNCTION(execGetFluidVelocityAtLocation); \
	DECLARE_FUNCTION(execGetFluidDensityAtLocation); \
	DECLARE_FUNCTION(execApplyFluidForce); \
	DECLARE_FUNCTION(execSetFluidTemperature); \
	DECLARE_FUNCTION(execRemoveFluidParticlesInArea); \
	DECLARE_FUNCTION(execAddFluidParticles); \
	DECLARE_FUNCTION(execCreateFluidSimulation); \
	DECLARE_FUNCTION(execInitializeFluidSimulation); \
	DECLARE_FUNCTION(execSetPhysicsQuality); \
	DECLARE_FUNCTION(execCleanupInactivePhysicsObjects); \
	DECLARE_FUNCTION(execOptimizePhysicsByDistance); \
	DECLARE_FUNCTION(execCreateSpecialPhysicsZone); \
	DECLARE_FUNCTION(execApplyRealmPhysicsToObject); \
	DECLARE_FUNCTION(execConfigureRealmPhysics); \
	DECLARE_FUNCTION(execCreateVortexField); \
	DECLARE_FUNCTION(execCreateDirectionalForceField); \
	DECLARE_FUNCTION(execCreateRadialForceField); \
	DECLARE_FUNCTION(execApplyFieldSystem); \
	DECLARE_FUNCTION(execConvertToGeometryCollection); \
	DECLARE_FUNCTION(execFractureObject); \
	DECLARE_FUNCTION(execCreateExplosion); \
	DECLARE_FUNCTION(execDestroyObject); \
	DECLARE_FUNCTION(execApplyCustomGravityToObject); \
	DECLARE_FUNCTION(execSetCustomGravity); \
	DECLARE_FUNCTION(execApplyTorqueToObject); \
	DECLARE_FUNCTION(execApplyImpulseToObject); \
	DECLARE_FUNCTION(execApplyForceToObject);


AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPhysicsBridge(); \
	friend struct Z_Construct_UClass_UAuracronPhysicsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPhysicsBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronPhysicsBridge"), Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPhysicsBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ChaosPhysicsConfiguration=NETFIELD_REP_START, \
		NETFIELD_REP_END=ChaosPhysicsConfiguration	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPhysicsBridge(UAuracronPhysicsBridge&&) = delete; \
	UAuracronPhysicsBridge(const UAuracronPhysicsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPhysicsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPhysicsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronPhysicsBridge) \
	NO_API virtual ~UAuracronPhysicsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_702_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h_705_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPhysicsBridge;

// ********** End Class UAuracronPhysicsBridge *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronPhysicsBridge_h

// ********** Begin Enum EAuracronPhysicsType ******************************************************
#define FOREACH_ENUM_EAURACRONPHYSICSTYPE(op) \
	op(EAuracronPhysicsType::None) \
	op(EAuracronPhysicsType::RigidBody) \
	op(EAuracronPhysicsType::SoftBody) \
	op(EAuracronPhysicsType::Fluid) \
	op(EAuracronPhysicsType::Cloth) \
	op(EAuracronPhysicsType::Destruction) \
	op(EAuracronPhysicsType::FieldSystem) \
	op(EAuracronPhysicsType::Constraint) \
	op(EAuracronPhysicsType::Vehicle) \
	op(EAuracronPhysicsType::Character) 

enum class EAuracronPhysicsType : uint8;
template<> struct TIsUEnumClass<EAuracronPhysicsType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsType>();
// ********** End Enum EAuracronPhysicsType ********************************************************

// ********** Begin Enum EAuracronDestructionType **************************************************
#define FOREACH_ENUM_EAURACRONDESTRUCTIONTYPE(op) \
	op(EAuracronDestructionType::None) \
	op(EAuracronDestructionType::Fracture) \
	op(EAuracronDestructionType::Explosion) \
	op(EAuracronDestructionType::Slice) \
	op(EAuracronDestructionType::Crumble) \
	op(EAuracronDestructionType::Shatter) \
	op(EAuracronDestructionType::Melt) \
	op(EAuracronDestructionType::Dissolve) \
	op(EAuracronDestructionType::Vaporize) \
	op(EAuracronDestructionType::Procedural) \
	op(EAuracronDestructionType::Volumetric) 

enum class EAuracronDestructionType : uint8;
template<> struct TIsUEnumClass<EAuracronDestructionType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronDestructionType>();
// ********** End Enum EAuracronDestructionType ****************************************************

// ********** Begin Enum EAuracronFluidType ********************************************************
#define FOREACH_ENUM_EAURACRONFLUIDTYPE(op) \
	op(EAuracronFluidType::None) \
	op(EAuracronFluidType::Water) \
	op(EAuracronFluidType::Oil) \
	op(EAuracronFluidType::Lava) \
	op(EAuracronFluidType::Gas) \
	op(EAuracronFluidType::Plasma) \
	op(EAuracronFluidType::Viscous) \
	op(EAuracronFluidType::Particle) \
	op(EAuracronFluidType::SPH) \
	op(EAuracronFluidType::GridBased) \
	op(EAuracronFluidType::Hybrid) 

enum class EAuracronFluidType : uint8;
template<> struct TIsUEnumClass<EAuracronFluidType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronFluidType>();
// ********** End Enum EAuracronFluidType **********************************************************

// ********** Begin Enum EAuracronSoftBodyType *****************************************************
#define FOREACH_ENUM_EAURACRONSOFTBODYTYPE(op) \
	op(EAuracronSoftBodyType::None) \
	op(EAuracronSoftBodyType::Cloth) \
	op(EAuracronSoftBodyType::Rubber) \
	op(EAuracronSoftBodyType::Jelly) \
	op(EAuracronSoftBodyType::Muscle) \
	op(EAuracronSoftBodyType::Skin) \
	op(EAuracronSoftBodyType::Hair) \
	op(EAuracronSoftBodyType::Rope) \
	op(EAuracronSoftBodyType::Chain) \
	op(EAuracronSoftBodyType::Membrane) 

enum class EAuracronSoftBodyType : uint8;
template<> struct TIsUEnumClass<EAuracronSoftBodyType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronSoftBodyType>();
// ********** End Enum EAuracronSoftBodyType *******************************************************

// ********** Begin Enum EAuracronConstraintType ***************************************************
#define FOREACH_ENUM_EAURACRONCONSTRAINTTYPE(op) \
	op(EAuracronConstraintType::None) \
	op(EAuracronConstraintType::Fixed) \
	op(EAuracronConstraintType::Hinge) \
	op(EAuracronConstraintType::Prismatic) \
	op(EAuracronConstraintType::Spherical) \
	op(EAuracronConstraintType::Universal) \
	op(EAuracronConstraintType::Distance) \
	op(EAuracronConstraintType::Spring) \
	op(EAuracronConstraintType::Motor) \
	op(EAuracronConstraintType::Servo) \
	op(EAuracronConstraintType::Gear) 

enum class EAuracronConstraintType : uint8;
template<> struct TIsUEnumClass<EAuracronConstraintType> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronConstraintType>();
// ********** End Enum EAuracronConstraintType *****************************************************

// ********** Begin Enum EAuracronPhysicsQuality ***************************************************
#define FOREACH_ENUM_EAURACRONPHYSICSQUALITY(op) \
	op(EAuracronPhysicsQuality::Low) \
	op(EAuracronPhysicsQuality::Medium) \
	op(EAuracronPhysicsQuality::High) \
	op(EAuracronPhysicsQuality::Ultra) \
	op(EAuracronPhysicsQuality::Cinematic) \
	op(EAuracronPhysicsQuality::Custom) 

enum class EAuracronPhysicsQuality : uint8;
template<> struct TIsUEnumClass<EAuracronPhysicsQuality> { enum { Value = true }; };
template<> AURACRONPHYSICSBRIDGE_API UEnum* StaticEnum<EAuracronPhysicsQuality>();
// ********** End Enum EAuracronPhysicsQuality *****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
