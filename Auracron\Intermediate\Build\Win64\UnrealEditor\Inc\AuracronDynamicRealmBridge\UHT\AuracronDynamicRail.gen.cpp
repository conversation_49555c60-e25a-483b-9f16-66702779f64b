// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicRail.h"
#include "ActiveGameplayEffectHandle.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicRealmBridge.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronDynamicRail() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailMovementData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailVisualConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FActiveGameplayEffectHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FRailMovementData *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRailMovementData;
class UScriptStruct* FRailMovementData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRailMovementData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRailMovementData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRailMovementData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("RailMovementData"));
	}
	return Z_Registration_Info_UScriptStruct_FRailMovementData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRailMovementData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Rail movement data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail movement data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Movement speed on this rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement speed on this rail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Acceleration_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Acceleration on rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Acceleration on rail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Deceleration_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Deceleration when leaving rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deceleration when leaving rail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanReverseDirection_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Can change direction on rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can change direction on rail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanExitAnywhere_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Can exit rail at any point */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can exit rail at any point" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyCostPerSecond_MetaData[] = {
		{ "Category", "Rail Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Energy cost per second on rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Energy cost per second on rail" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Acceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Deceleration;
	static void NewProp_bCanReverseDirection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanReverseDirection;
	static void NewProp_bCanExitAnywhere_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanExitAnywhere;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyCostPerSecond;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRailMovementData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailMovementData, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_Acceleration = { "Acceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailMovementData, Acceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Acceleration_MetaData), NewProp_Acceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_Deceleration = { "Deceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailMovementData, Deceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Deceleration_MetaData), NewProp_Deceleration_MetaData) };
void Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanReverseDirection_SetBit(void* Obj)
{
	((FRailMovementData*)Obj)->bCanReverseDirection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanReverseDirection = { "bCanReverseDirection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailMovementData), &Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanReverseDirection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanReverseDirection_MetaData), NewProp_bCanReverseDirection_MetaData) };
void Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanExitAnywhere_SetBit(void* Obj)
{
	((FRailMovementData*)Obj)->bCanExitAnywhere = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanExitAnywhere = { "bCanExitAnywhere", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailMovementData), &Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanExitAnywhere_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanExitAnywhere_MetaData), NewProp_bCanExitAnywhere_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_EnergyCostPerSecond = { "EnergyCostPerSecond", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailMovementData, EnergyCostPerSecond), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyCostPerSecond_MetaData), NewProp_EnergyCostPerSecond_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRailMovementData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_Acceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_Deceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanReverseDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_bCanExitAnywhere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailMovementData_Statics::NewProp_EnergyCostPerSecond,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailMovementData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRailMovementData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"RailMovementData",
	Z_Construct_UScriptStruct_FRailMovementData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailMovementData_Statics::PropPointers),
	sizeof(FRailMovementData),
	alignof(FRailMovementData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailMovementData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRailMovementData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRailMovementData()
{
	if (!Z_Registration_Info_UScriptStruct_FRailMovementData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRailMovementData.InnerSingleton, Z_Construct_UScriptStruct_FRailMovementData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRailMovementData.InnerSingleton;
}
// ********** End ScriptStruct FRailMovementData ***************************************************

// ********** Begin ScriptStruct FRailVisualConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRailVisualConfig;
class UScriptStruct* FRailVisualConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRailVisualConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRailVisualConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRailVisualConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("RailVisualConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FRailVisualConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRailVisualConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Rail visual configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail visual configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailMesh_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rail mesh */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailMaterial_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rail material */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailEffect_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Particle effect along rail */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Particle effect along rail" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementTrailEffect_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Movement trail effect */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement trail effect" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationEffect_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rail activation effect */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail activation effect" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailColor_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rail color */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail color" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectIntensity_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Effect intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effect intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bVisibleDuringDay_MetaData[] = {
		{ "Category", "Rail Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visibility during day/night */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visibility during day/night" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bVisibleDuringNight_MetaData[] = {
		{ "Category", "Rail Visuals" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MovementTrailEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivationEffect;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RailColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectIntensity;
	static void NewProp_bVisibleDuringDay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisibleDuringDay;
	static void NewProp_bVisibleDuringNight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisibleDuringNight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRailVisualConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailMesh = { "RailMesh", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, RailMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailMesh_MetaData), NewProp_RailMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailMaterial = { "RailMaterial", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, RailMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailMaterial_MetaData), NewProp_RailMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailEffect = { "RailEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, RailEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailEffect_MetaData), NewProp_RailEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_MovementTrailEffect = { "MovementTrailEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, MovementTrailEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementTrailEffect_MetaData), NewProp_MovementTrailEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_ActivationEffect = { "ActivationEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, ActivationEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationEffect_MetaData), NewProp_ActivationEffect_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailColor = { "RailColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, RailColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailColor_MetaData), NewProp_RailColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_EffectIntensity = { "EffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailVisualConfig, EffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectIntensity_MetaData), NewProp_EffectIntensity_MetaData) };
void Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringDay_SetBit(void* Obj)
{
	((FRailVisualConfig*)Obj)->bVisibleDuringDay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringDay = { "bVisibleDuringDay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailVisualConfig), &Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringDay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bVisibleDuringDay_MetaData), NewProp_bVisibleDuringDay_MetaData) };
void Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringNight_SetBit(void* Obj)
{
	((FRailVisualConfig*)Obj)->bVisibleDuringNight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringNight = { "bVisibleDuringNight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailVisualConfig), &Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringNight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bVisibleDuringNight_MetaData), NewProp_bVisibleDuringNight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRailVisualConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_MovementTrailEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_ActivationEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_RailColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_EffectIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewProp_bVisibleDuringNight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailVisualConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRailVisualConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"RailVisualConfig",
	Z_Construct_UScriptStruct_FRailVisualConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailVisualConfig_Statics::PropPointers),
	sizeof(FRailVisualConfig),
	alignof(FRailVisualConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailVisualConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRailVisualConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRailVisualConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FRailVisualConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRailVisualConfig.InnerSingleton, Z_Construct_UScriptStruct_FRailVisualConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRailVisualConfig.InnerSingleton;
}
// ********** End ScriptStruct FRailVisualConfig ***************************************************

// ********** Begin Class AAuracronDynamicRail Function ActivateRail *******************************
struct Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rail management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "ActivateRail", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execActivateRail)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateRail();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function ActivateRail *********************************

// ********** Begin Class AAuracronDynamicRail Function CanPlayerUseRail ***************************
struct Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics
{
	struct AuracronDynamicRail_eventCanPlayerUseRail_Parms
	{
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player interaction" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventCanPlayerUseRail_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventCanPlayerUseRail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventCanPlayerUseRail_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "CanPlayerUseRail", Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::AuracronDynamicRail_eventCanPlayerUseRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::AuracronDynamicRail_eventCanPlayerUseRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execCanPlayerUseRail)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPlayerUseRail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function CanPlayerUseRail *****************************

// ********** Begin Class AAuracronDynamicRail Function DeactivateRail *****************************
struct Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Management" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "DeactivateRail", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execDeactivateRail)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateRail();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function DeactivateRail *******************************

// ********** Begin Class AAuracronDynamicRail Function GetPlayerRailPosition **********************
struct Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics
{
	struct AuracronDynamicRail_eventGetPlayerRailPosition_Parms
	{
		APawn* Player;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Control" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetPlayerRailPosition_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetPlayerRailPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "GetPlayerRailPosition", Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::AuracronDynamicRail_eventGetPlayerRailPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::AuracronDynamicRail_eventGetPlayerRailPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execGetPlayerRailPosition)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetPlayerRailPosition(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function GetPlayerRailPosition ************************

// ********** Begin Class AAuracronDynamicRail Function GetPlayerRailProgress **********************
struct Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics
{
	struct AuracronDynamicRail_eventGetPlayerRailProgress_Parms
	{
		APawn* Player;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Control" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetPlayerRailProgress_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetPlayerRailProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "GetPlayerRailProgress", Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::AuracronDynamicRail_eventGetPlayerRailProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::AuracronDynamicRail_eventGetPlayerRailProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execGetPlayerRailProgress)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPlayerRailProgress(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function GetPlayerRailProgress ************************

// ********** Begin Class AAuracronDynamicRail Function GetPlayersOnRail ***************************
struct Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics
{
	struct AuracronDynamicRail_eventGetPlayersOnRail_Parms
	{
		TArray<APawn*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetPlayersOnRail_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "GetPlayersOnRail", Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::AuracronDynamicRail_eventGetPlayersOnRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::AuracronDynamicRail_eventGetPlayersOnRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execGetPlayersOnRail)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<APawn*>*)Z_Param__Result=P_THIS->GetPlayersOnRail();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function GetPlayersOnRail *****************************

// ********** Begin Class AAuracronDynamicRail Function GetRailPath ********************************
struct Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics
{
	struct AuracronDynamicRail_eventGetRailPath_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Management" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventGetRailPath_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "GetRailPath", Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::AuracronDynamicRail_eventGetRailPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::AuracronDynamicRail_eventGetRailPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execGetRailPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetRailPath();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function GetRailPath **********************************

// ********** Begin Class AAuracronDynamicRail Function IsPlayerOnRail *****************************
struct Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics
{
	struct AuracronDynamicRail_eventIsPlayerOnRail_Parms
	{
		APawn* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventIsPlayerOnRail_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventIsPlayerOnRail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventIsPlayerOnRail_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "IsPlayerOnRail", Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::AuracronDynamicRail_eventIsPlayerOnRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::AuracronDynamicRail_eventIsPlayerOnRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execIsPlayerOnRail)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerOnRail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function IsPlayerOnRail *******************************

// ********** Begin Class AAuracronDynamicRail Function IsRailActive *******************************
struct Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics
{
	struct AuracronDynamicRail_eventIsRailActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Management" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventIsRailActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventIsRailActive_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "IsRailActive", Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::AuracronDynamicRail_eventIsRailActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::AuracronDynamicRail_eventIsRailActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execIsRailActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRailActive();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function IsRailActive *********************************

// ********** Begin Class AAuracronDynamicRail Function OnEntryPointBeginOverlap *******************
struct Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics
{
	struct AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnEntryPointBeginOverlap", Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::AuracronDynamicRail_eventOnEntryPointBeginOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execOnEntryPointBeginOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnEntryPointBeginOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function OnEntryPointBeginOverlap *********************

// ********** Begin Class AAuracronDynamicRail Function OnExitPointBeginOverlap ********************
struct Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics
{
	struct AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnExitPointBeginOverlap", Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::AuracronDynamicRail_eventOnExitPointBeginOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execOnExitPointBeginOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnExitPointBeginOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function OnExitPointBeginOverlap **********************

// ********** Begin Class AAuracronDynamicRail Function OnPlayerEnteredRail ************************
struct AuracronDynamicRail_eventOnPlayerEnteredRail_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronDynamicRail_OnPlayerEnteredRail = FName(TEXT("OnPlayerEnteredRail"));
void AAuracronDynamicRail::OnPlayerEnteredRail(APawn* Player)
{
	AuracronDynamicRail_eventOnPlayerEnteredRail_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronDynamicRail_OnPlayerEnteredRail);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnPlayerEnteredRail_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnPlayerEnteredRail", Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::PropPointers), sizeof(AuracronDynamicRail_eventOnPlayerEnteredRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicRail_eventOnPlayerEnteredRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronDynamicRail Function OnPlayerEnteredRail **************************

// ********** Begin Class AAuracronDynamicRail Function OnPlayerExitedRail *************************
struct AuracronDynamicRail_eventOnPlayerExitedRail_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronDynamicRail_OnPlayerExitedRail = FName(TEXT("OnPlayerExitedRail"));
void AAuracronDynamicRail::OnPlayerExitedRail(APawn* Player)
{
	AuracronDynamicRail_eventOnPlayerExitedRail_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronDynamicRail_OnPlayerExitedRail);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnPlayerExitedRail_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnPlayerExitedRail", Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::PropPointers), sizeof(AuracronDynamicRail_eventOnPlayerExitedRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicRail_eventOnPlayerExitedRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronDynamicRail Function OnPlayerExitedRail ***************************

// ********** Begin Class AAuracronDynamicRail Function OnPlayerReachedDestination *****************
struct AuracronDynamicRail_eventOnPlayerReachedDestination_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronDynamicRail_OnPlayerReachedDestination = FName(TEXT("OnPlayerReachedDestination"));
void AAuracronDynamicRail::OnPlayerReachedDestination(APawn* Player)
{
	AuracronDynamicRail_eventOnPlayerReachedDestination_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronDynamicRail_OnPlayerReachedDestination);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventOnPlayerReachedDestination_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnPlayerReachedDestination", Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::PropPointers), sizeof(AuracronDynamicRail_eventOnPlayerReachedDestination_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicRail_eventOnPlayerReachedDestination_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronDynamicRail Function OnPlayerReachedDestination *******************

// ********** Begin Class AAuracronDynamicRail Function OnRailActivated ****************************
static FName NAME_AAuracronDynamicRail_OnRailActivated = FName(TEXT("OnRailActivated"));
void AAuracronDynamicRail::OnRailActivated()
{
	UFunction* Func = FindFunctionChecked(NAME_AAuracronDynamicRail_OnRailActivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnRailActivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronDynamicRail Function OnRailActivated ******************************

// ********** Begin Class AAuracronDynamicRail Function OnRailDeactivated **************************
static FName NAME_AAuracronDynamicRail_OnRailDeactivated = FName(TEXT("OnRailDeactivated"));
void AAuracronDynamicRail::OnRailDeactivated()
{
	UFunction* Func = FindFunctionChecked(NAME_AAuracronDynamicRail_OnRailDeactivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "OnRailDeactivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronDynamicRail Function OnRailDeactivated ****************************

// ********** Begin Class AAuracronDynamicRail Function PlayRailActivationEffects ******************
struct Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "PlayRailActivationEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execPlayRailActivationEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayRailActivationEffects();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function PlayRailActivationEffects ********************

// ********** Begin Class AAuracronDynamicRail Function PlayRailDeactivationEffects ****************
struct Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "PlayRailDeactivationEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execPlayRailDeactivationEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayRailDeactivationEffects();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function PlayRailDeactivationEffects ******************

// ********** Begin Class AAuracronDynamicRail Function SetPlayerRailSpeed *************************
struct Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics
{
	struct AuracronDynamicRail_eventSetPlayerRailSpeed_Parms
	{
		APawn* Player;
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Control" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventSetPlayerRailSpeed_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventSetPlayerRailSpeed_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "SetPlayerRailSpeed", Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::AuracronDynamicRail_eventSetPlayerRailSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::AuracronDynamicRail_eventSetPlayerRailSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execSetPlayerRailSpeed)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerRailSpeed(Z_Param_Player,Z_Param_Speed);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function SetPlayerRailSpeed ***************************

// ********** Begin Class AAuracronDynamicRail Function SetRailPath ********************************
struct Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics
{
	struct AuracronDynamicRail_eventSetRailPath_Parms
	{
		TArray<FVector> PathPoints;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Management" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PathPoints;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::NewProp_PathPoints_Inner = { "PathPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::NewProp_PathPoints = { "PathPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventSetRailPath_Parms, PathPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathPoints_MetaData), NewProp_PathPoints_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::NewProp_PathPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::NewProp_PathPoints,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "SetRailPath", Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::AuracronDynamicRail_eventSetRailPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::AuracronDynamicRail_eventSetRailPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execSetRailPath)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_PathPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRailPath(Z_Param_Out_PathPoints);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function SetRailPath **********************************

// ********** Begin Class AAuracronDynamicRail Function StartPlayerMovement ************************
struct Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics
{
	struct AuracronDynamicRail_eventStartPlayerMovement_Parms
	{
		APawn* Player;
		bool bForwardDirection;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "CPP_Default_bForwardDirection", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_bForwardDirection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bForwardDirection;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventStartPlayerMovement_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_bForwardDirection_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventStartPlayerMovement_Parms*)Obj)->bForwardDirection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_bForwardDirection = { "bForwardDirection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventStartPlayerMovement_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_bForwardDirection_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRail_eventStartPlayerMovement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRail_eventStartPlayerMovement_Parms), &Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_bForwardDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "StartPlayerMovement", Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::AuracronDynamicRail_eventStartPlayerMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::AuracronDynamicRail_eventStartPlayerMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execStartPlayerMovement)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_UBOOL(Z_Param_bForwardDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartPlayerMovement(Z_Param_Player,Z_Param_bForwardDirection);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function StartPlayerMovement **************************

// ********** Begin Class AAuracronDynamicRail Function StopPlayerMovement *************************
struct Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics
{
	struct AuracronDynamicRail_eventStopPlayerMovement_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventStopPlayerMovement_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "StopPlayerMovement", Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::AuracronDynamicRail_eventStopPlayerMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::AuracronDynamicRail_eventStopPlayerMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execStopPlayerMovement)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPlayerMovement(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function StopPlayerMovement ***************************

// ********** Begin Class AAuracronDynamicRail Function UpdateAxisRailBehavior *********************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics
{
	struct AuracronDynamicRail_eventUpdateAxisRailBehavior_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Behavior" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdateAxisRailBehavior_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdateAxisRailBehavior", Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::AuracronDynamicRail_eventUpdateAxisRailBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::AuracronDynamicRail_eventUpdateAxisRailBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdateAxisRailBehavior)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAxisRailBehavior(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdateAxisRailBehavior ***********************

// ********** Begin Class AAuracronDynamicRail Function UpdateLunarRailBehavior ********************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics
{
	struct AuracronDynamicRail_eventUpdateLunarRailBehavior_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Behavior" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdateLunarRailBehavior_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdateLunarRailBehavior", Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::AuracronDynamicRail_eventUpdateLunarRailBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::AuracronDynamicRail_eventUpdateLunarRailBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdateLunarRailBehavior)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLunarRailBehavior(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdateLunarRailBehavior **********************

// ********** Begin Class AAuracronDynamicRail Function UpdatePlayerMovement ***********************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics
{
	struct AuracronDynamicRail_eventUpdatePlayerMovement_Parms
	{
		APawn* Player;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Movement Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Movement control\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdatePlayerMovement_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdatePlayerMovement_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdatePlayerMovement", Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::AuracronDynamicRail_eventUpdatePlayerMovement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::AuracronDynamicRail_eventUpdatePlayerMovement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdatePlayerMovement)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerMovement(Z_Param_Player,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdatePlayerMovement *************************

// ********** Begin Class AAuracronDynamicRail Function UpdateRailVisibility ***********************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdateRailVisibility", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdateRailVisibility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRailVisibility();
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdateRailVisibility *************************

// ********** Begin Class AAuracronDynamicRail Function UpdateRailVisuals **************************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics
{
	struct AuracronDynamicRail_eventUpdateRailVisuals_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual and audio\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual and audio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdateRailVisuals_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdateRailVisuals", Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::AuracronDynamicRail_eventUpdateRailVisuals_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::AuracronDynamicRail_eventUpdateRailVisuals_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdateRailVisuals)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRailVisuals(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdateRailVisuals ****************************

// ********** Begin Class AAuracronDynamicRail Function UpdateSolarRailBehavior ********************
struct Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics
{
	struct AuracronDynamicRail_eventUpdateSolarRailBehavior_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rail Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rail type specific behavior\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail type specific behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRail_eventUpdateSolarRailBehavior_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronDynamicRail, nullptr, "UpdateSolarRailBehavior", Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::AuracronDynamicRail_eventUpdateSolarRailBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::AuracronDynamicRail_eventUpdateSolarRailBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronDynamicRail::execUpdateSolarRailBehavior)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSolarRailBehavior(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronDynamicRail Function UpdateSolarRailBehavior **********************

// ********** Begin Class AAuracronDynamicRail *****************************************************
void AAuracronDynamicRail::StaticRegisterNativesAAuracronDynamicRail()
{
	UClass* Class = AAuracronDynamicRail::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateRail", &AAuracronDynamicRail::execActivateRail },
		{ "CanPlayerUseRail", &AAuracronDynamicRail::execCanPlayerUseRail },
		{ "DeactivateRail", &AAuracronDynamicRail::execDeactivateRail },
		{ "GetPlayerRailPosition", &AAuracronDynamicRail::execGetPlayerRailPosition },
		{ "GetPlayerRailProgress", &AAuracronDynamicRail::execGetPlayerRailProgress },
		{ "GetPlayersOnRail", &AAuracronDynamicRail::execGetPlayersOnRail },
		{ "GetRailPath", &AAuracronDynamicRail::execGetRailPath },
		{ "IsPlayerOnRail", &AAuracronDynamicRail::execIsPlayerOnRail },
		{ "IsRailActive", &AAuracronDynamicRail::execIsRailActive },
		{ "OnEntryPointBeginOverlap", &AAuracronDynamicRail::execOnEntryPointBeginOverlap },
		{ "OnExitPointBeginOverlap", &AAuracronDynamicRail::execOnExitPointBeginOverlap },
		{ "PlayRailActivationEffects", &AAuracronDynamicRail::execPlayRailActivationEffects },
		{ "PlayRailDeactivationEffects", &AAuracronDynamicRail::execPlayRailDeactivationEffects },
		{ "SetPlayerRailSpeed", &AAuracronDynamicRail::execSetPlayerRailSpeed },
		{ "SetRailPath", &AAuracronDynamicRail::execSetRailPath },
		{ "StartPlayerMovement", &AAuracronDynamicRail::execStartPlayerMovement },
		{ "StopPlayerMovement", &AAuracronDynamicRail::execStopPlayerMovement },
		{ "UpdateAxisRailBehavior", &AAuracronDynamicRail::execUpdateAxisRailBehavior },
		{ "UpdateLunarRailBehavior", &AAuracronDynamicRail::execUpdateLunarRailBehavior },
		{ "UpdatePlayerMovement", &AAuracronDynamicRail::execUpdatePlayerMovement },
		{ "UpdateRailVisibility", &AAuracronDynamicRail::execUpdateRailVisibility },
		{ "UpdateRailVisuals", &AAuracronDynamicRail::execUpdateRailVisuals },
		{ "UpdateSolarRailBehavior", &AAuracronDynamicRail::execUpdateSolarRailBehavior },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronDynamicRail;
UClass* AAuracronDynamicRail::GetPrivateStaticClass()
{
	using TClass = AAuracronDynamicRail;
	if (!Z_Registration_Info_UClass_AAuracronDynamicRail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronDynamicRail"),
			Z_Registration_Info_UClass_AAuracronDynamicRail.InnerSingleton,
			StaticRegisterNativesAAuracronDynamicRail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronDynamicRail.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister()
{
	return AAuracronDynamicRail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronDynamicRail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Dynamic Rail\n * \n * Dynamic movement rails for rapid traversal:\n * \n * Solar Trilhos (Golden):\n * - Golden particles with heat distortion\n * - High-speed movement during day\n * - Reduced effectiveness at night\n * \n * Axis Trilhos (Silver):\n * - Instant vertical movement between layers\n * - Neutral appearance\n * - Always available\n * \n * Lunar Trilhos (Ethereal Blue):\n * - Ethereal blue-white appearance\n * - Visible only at night\n * - Provides stealth bonus\n * - Enhanced movement speed in darkness\n */" },
#endif
		{ "IncludePath", "AuracronDynamicRail.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Dynamic Rail\n\nDynamic movement rails for rapid traversal:\n\nSolar Trilhos (Golden):\n- Golden particles with heat distortion\n- High-speed movement during day\n- Reduced effectiveness at night\n\nAxis Trilhos (Silver):\n- Instant vertical movement between layers\n- Neutral appearance\n- Always available\n\nLunar Trilhos (Ethereal Blue):\n- Ethereal blue-white appearance\n- Visible only at night\n- Provides stealth bonus\n- Enhanced movement speed in darkness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailType_MetaData[] = {
		{ "Category", "Rail Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rail configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementData_MetaData[] = {
		{ "Category", "Rail Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualConfig_MetaData[] = {
		{ "Category", "Rail Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Rail Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoActivate_MetaData[] = {
		{ "Category", "Rail Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailSpline_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailMeshComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailEffectComponents_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailAudioComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EntryPoints_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExitPoints_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayersOnRail_MetaData[] = {
		{ "Category", "Player Tracking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailProgress_MetaData[] = {
		{ "Category", "Player Tracking" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailDirection_MetaData[] = {
		{ "Category", "Player Tracking" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailSpeed_MetaData[] = {
		{ "Category", "Player Tracking" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailLength_MetaData[] = {
		{ "Category", "Rail State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rail state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Rail State" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEffectIntensity_MetaData[] = {
		{ "Category", "Rail State" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastActivationTime_MetaData[] = {
		{ "Category", "Rail State" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectedByTimeOfDay_MetaData[] = {
		{ "Category", "Time Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Time-based behavior\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time-based behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DayEffectiveness_MetaData[] = {
		{ "Category", "Time Behavior" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NightEffectiveness_MetaData[] = {
		{ "Category", "Time Behavior" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailActivationSound_MetaData[] = {
		{ "Category", "Audio Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio assets\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailActiveLoopSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailDeactivationSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerMovementSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached references\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicRailMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dynamic materials\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailDataMap_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerStealthEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PotentialUsers_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RailType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualConfig;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bAutoActivate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoActivate;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailMeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RailMeshComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailEffectComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RailEffectComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailAudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EntryPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EntryPoints;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ExitPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExitPoints;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayersOnRail_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayersOnRail;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerRailProgress_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailProgress_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailProgress;
	static const UECodeGen_Private::FBoolPropertyParams NewProp_PlayerRailDirection_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailDirection_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailDirection;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerRailSpeed_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailSpeed_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RailLength;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentEffectIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastActivationTime;
	static void NewProp_bAffectedByTimeOfDay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectedByTimeOfDay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DayEffectiveness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NightEffectiveness;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailActivationSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailActiveLoopSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailDeactivationSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerMovementSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DynamicRailMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DynamicRailMaterials;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRailDataMap_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailDataMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailDataMap;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRailEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerStealthEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerStealthEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerStealthEffects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PotentialUsers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PotentialUsers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronDynamicRail_ActivateRail, "ActivateRail" }, // 334300020
		{ &Z_Construct_UFunction_AAuracronDynamicRail_CanPlayerUseRail, "CanPlayerUseRail" }, // 1637870822
		{ &Z_Construct_UFunction_AAuracronDynamicRail_DeactivateRail, "DeactivateRail" }, // 275131362
		{ &Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailPosition, "GetPlayerRailPosition" }, // 559259618
		{ &Z_Construct_UFunction_AAuracronDynamicRail_GetPlayerRailProgress, "GetPlayerRailProgress" }, // 2538574876
		{ &Z_Construct_UFunction_AAuracronDynamicRail_GetPlayersOnRail, "GetPlayersOnRail" }, // 2040161532
		{ &Z_Construct_UFunction_AAuracronDynamicRail_GetRailPath, "GetRailPath" }, // 3586651221
		{ &Z_Construct_UFunction_AAuracronDynamicRail_IsPlayerOnRail, "IsPlayerOnRail" }, // 4223637279
		{ &Z_Construct_UFunction_AAuracronDynamicRail_IsRailActive, "IsRailActive" }, // 3412796861
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnEntryPointBeginOverlap, "OnEntryPointBeginOverlap" }, // 1838669356
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnExitPointBeginOverlap, "OnExitPointBeginOverlap" }, // 313111620
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerEnteredRail, "OnPlayerEnteredRail" }, // 2114752066
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerExitedRail, "OnPlayerExitedRail" }, // 149398293
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnPlayerReachedDestination, "OnPlayerReachedDestination" }, // 2712231427
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnRailActivated, "OnRailActivated" }, // 1675982987
		{ &Z_Construct_UFunction_AAuracronDynamicRail_OnRailDeactivated, "OnRailDeactivated" }, // 2554048929
		{ &Z_Construct_UFunction_AAuracronDynamicRail_PlayRailActivationEffects, "PlayRailActivationEffects" }, // 2547785197
		{ &Z_Construct_UFunction_AAuracronDynamicRail_PlayRailDeactivationEffects, "PlayRailDeactivationEffects" }, // 2679891756
		{ &Z_Construct_UFunction_AAuracronDynamicRail_SetPlayerRailSpeed, "SetPlayerRailSpeed" }, // 3177116420
		{ &Z_Construct_UFunction_AAuracronDynamicRail_SetRailPath, "SetRailPath" }, // 1977554803
		{ &Z_Construct_UFunction_AAuracronDynamicRail_StartPlayerMovement, "StartPlayerMovement" }, // 684277225
		{ &Z_Construct_UFunction_AAuracronDynamicRail_StopPlayerMovement, "StopPlayerMovement" }, // 608495633
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdateAxisRailBehavior, "UpdateAxisRailBehavior" }, // 4290122536
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdateLunarRailBehavior, "UpdateLunarRailBehavior" }, // 2924259673
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdatePlayerMovement, "UpdatePlayerMovement" }, // 3704439518
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisibility, "UpdateRailVisibility" }, // 2106710239
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdateRailVisuals, "UpdateRailVisuals" }, // 995830379
		{ &Z_Construct_UFunction_AAuracronDynamicRail_UpdateSolarRailBehavior, "UpdateSolarRailBehavior" }, // 2140144120
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronDynamicRail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailType = { "RailType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailType_MetaData), NewProp_RailType_MetaData) }; // 913509891
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_MovementData = { "MovementData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, MovementData), Z_Construct_UScriptStruct_FRailMovementData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementData_MetaData), NewProp_MovementData_MetaData) }; // 3338471922
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_VisualConfig = { "VisualConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, VisualConfig), Z_Construct_UScriptStruct_FRailVisualConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualConfig_MetaData), NewProp_VisualConfig_MetaData) }; // 1742044801
void Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((AAuracronDynamicRail*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronDynamicRail), &Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAutoActivate_SetBit(void* Obj)
{
	((AAuracronDynamicRail*)Obj)->bAutoActivate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAutoActivate = { "bAutoActivate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronDynamicRail), &Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAutoActivate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoActivate_MetaData), NewProp_bAutoActivate_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailSpline = { "RailSpline", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailSpline_MetaData), NewProp_RailSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailMeshComponents_Inner = { "RailMeshComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USplineMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailMeshComponents = { "RailMeshComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailMeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailMeshComponents_MetaData), NewProp_RailMeshComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailEffectComponents_Inner = { "RailEffectComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailEffectComponents = { "RailEffectComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailEffectComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailEffectComponents_MetaData), NewProp_RailEffectComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailAudioComponent = { "RailAudioComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailAudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailAudioComponent_MetaData), NewProp_RailAudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_EntryPoints_Inner = { "EntryPoints", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_EntryPoints = { "EntryPoints", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, EntryPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EntryPoints_MetaData), NewProp_EntryPoints_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_ExitPoints_Inner = { "ExitPoints", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_ExitPoints = { "ExitPoints", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, ExitPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExitPoints_MetaData), NewProp_ExitPoints_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayersOnRail_Inner = { "PlayersOnRail", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayersOnRail = { "PlayersOnRail", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayersOnRail), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayersOnRail_MetaData), NewProp_PlayersOnRail_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress_ValueProp = { "PlayerRailProgress", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress_Key_KeyProp = { "PlayerRailProgress_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress = { "PlayerRailProgress", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerRailProgress), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailProgress_MetaData), NewProp_PlayerRailProgress_MetaData) };
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection_ValueProp = { "PlayerRailDirection", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection_Key_KeyProp = { "PlayerRailDirection_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection = { "PlayerRailDirection", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerRailDirection), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailDirection_MetaData), NewProp_PlayerRailDirection_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed_ValueProp = { "PlayerRailSpeed", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed_Key_KeyProp = { "PlayerRailSpeed_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed = { "PlayerRailSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerRailSpeed), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailSpeed_MetaData), NewProp_PlayerRailSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailLength = { "RailLength", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailLength_MetaData), NewProp_RailLength_MetaData) };
void Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((AAuracronDynamicRail*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronDynamicRail), &Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_CurrentEffectIntensity = { "CurrentEffectIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, CurrentEffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEffectIntensity_MetaData), NewProp_CurrentEffectIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_LastActivationTime = { "LastActivationTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, LastActivationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastActivationTime_MetaData), NewProp_LastActivationTime_MetaData) };
void Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAffectedByTimeOfDay_SetBit(void* Obj)
{
	((AAuracronDynamicRail*)Obj)->bAffectedByTimeOfDay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAffectedByTimeOfDay = { "bAffectedByTimeOfDay", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronDynamicRail), &Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAffectedByTimeOfDay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectedByTimeOfDay_MetaData), NewProp_bAffectedByTimeOfDay_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DayEffectiveness = { "DayEffectiveness", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, DayEffectiveness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DayEffectiveness_MetaData), NewProp_DayEffectiveness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_NightEffectiveness = { "NightEffectiveness", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, NightEffectiveness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NightEffectiveness_MetaData), NewProp_NightEffectiveness_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailActivationSound = { "RailActivationSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailActivationSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailActivationSound_MetaData), NewProp_RailActivationSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailActiveLoopSound = { "RailActiveLoopSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailActiveLoopSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailActiveLoopSound_MetaData), NewProp_RailActiveLoopSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailDeactivationSound = { "RailDeactivationSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, RailDeactivationSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailDeactivationSound_MetaData), NewProp_RailDeactivationSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerMovementSound = { "PlayerMovementSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerMovementSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerMovementSound_MetaData), NewProp_PlayerMovementSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DynamicRailMaterials_Inner = { "DynamicRailMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DynamicRailMaterials = { "DynamicRailMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, DynamicRailMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicRailMaterials_MetaData), NewProp_DynamicRailMaterials_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap_ValueProp = { "PlayerRailDataMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPlayerRailData, METADATA_PARAMS(0, nullptr) }; // 2949666401
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap_Key_KeyProp = { "PlayerRailDataMap_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap = { "PlayerRailDataMap", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerRailDataMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailDataMap_MetaData), NewProp_PlayerRailDataMap_MetaData) }; // 2949666401
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects_ValueProp = { "PlayerRailEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects_Key_KeyProp = { "PlayerRailEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects = { "PlayerRailEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerRailEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailEffects_MetaData), NewProp_PlayerRailEffects_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects_ValueProp = { "PlayerStealthEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects_Key_KeyProp = { "PlayerStealthEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects = { "PlayerStealthEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PlayerStealthEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerStealthEffects_MetaData), NewProp_PlayerStealthEffects_MetaData) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PotentialUsers_Inner = { "PotentialUsers", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PotentialUsers = { "PotentialUsers", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronDynamicRail, PotentialUsers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PotentialUsers_MetaData), NewProp_PotentialUsers_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronDynamicRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_MovementData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_VisualConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAutoActivate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailMeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailMeshComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailEffectComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailEffectComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailAudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_EntryPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_EntryPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_ExitPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_ExitPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayersOnRail_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayersOnRail,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_CurrentEffectIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_LastActivationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_bAffectedByTimeOfDay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DayEffectiveness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_NightEffectiveness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailActivationSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailActiveLoopSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_RailDeactivationSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerMovementSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_CachedRealmSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DynamicRailMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_DynamicRailMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailDataMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerRailEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PlayerStealthEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PotentialUsers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronDynamicRail_Statics::NewProp_PotentialUsers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronDynamicRail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronDynamicRail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronDynamicRail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronDynamicRail_Statics::ClassParams = {
	&AAuracronDynamicRail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronDynamicRail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronDynamicRail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronDynamicRail_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronDynamicRail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronDynamicRail()
{
	if (!Z_Registration_Info_UClass_AAuracronDynamicRail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronDynamicRail.OuterSingleton, Z_Construct_UClass_AAuracronDynamicRail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronDynamicRail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronDynamicRail);
AAuracronDynamicRail::~AAuracronDynamicRail() {}
// ********** End Class AAuracronDynamicRail *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRailMovementData::StaticStruct, Z_Construct_UScriptStruct_FRailMovementData_Statics::NewStructOps, TEXT("RailMovementData"), &Z_Registration_Info_UScriptStruct_FRailMovementData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRailMovementData), 3338471922U) },
		{ FRailVisualConfig::StaticStruct, Z_Construct_UScriptStruct_FRailVisualConfig_Statics::NewStructOps, TEXT("RailVisualConfig"), &Z_Registration_Info_UScriptStruct_FRailVisualConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRailVisualConfig), 1742044801U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronDynamicRail, AAuracronDynamicRail::StaticClass, TEXT("AAuracronDynamicRail"), &Z_Registration_Info_UClass_AAuracronDynamicRail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronDynamicRail), 675251374U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_3796006809(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
