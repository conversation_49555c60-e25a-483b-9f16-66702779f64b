// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronSigilEffects.h"
#include "AttributeSet.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronSigilEffects() {}

// ********** Begin Cross Module References ********************************************************
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAbility();
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAbility_NoRegister();
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAttributeSet();
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAttributeSet_NoRegister();
AURACRONSIGILOSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAttributeSet();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAttributeData();
METASOUNDENGINE_API UClass* Z_Construct_UClass_UMetaSoundSource_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronSigilosBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_ArchetypeLevel *****************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_ArchetypeLevel_Parms
	{
		FGameplayAttributeData OldArchetypeLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldArchetypeLevel_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldArchetypeLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::NewProp_OldArchetypeLevel = { "OldArchetypeLevel", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_ArchetypeLevel_Parms, OldArchetypeLevel), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldArchetypeLevel_MetaData), NewProp_OldArchetypeLevel_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::NewProp_OldArchetypeLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_ArchetypeLevel", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::AuracronSigilAttributeSet_eventOnRep_ArchetypeLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::AuracronSigilAttributeSet_eventOnRep_ArchetypeLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_ArchetypeLevel)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldArchetypeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ArchetypeLevel(Z_Param_Out_OldArchetypeLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_ArchetypeLevel *******************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_ArmorPenetration ***************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_ArmorPenetration_Parms
	{
		FGameplayAttributeData OldArmorPenetration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldArmorPenetration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldArmorPenetration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::NewProp_OldArmorPenetration = { "OldArmorPenetration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_ArmorPenetration_Parms, OldArmorPenetration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldArmorPenetration_MetaData), NewProp_OldArmorPenetration_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::NewProp_OldArmorPenetration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_ArmorPenetration", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::AuracronSigilAttributeSet_eventOnRep_ArmorPenetration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::AuracronSigilAttributeSet_eventOnRep_ArmorPenetration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_ArmorPenetration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldArmorPenetration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ArmorPenetration(Z_Param_Out_OldArmorPenetration);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_ArmorPenetration *****************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_CriticalChance *****************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_CriticalChance_Parms
	{
		FGameplayAttributeData OldCriticalChance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldCriticalChance_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldCriticalChance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance = { "OldCriticalChance", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_CriticalChance_Parms, OldCriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldCriticalChance_MetaData), NewProp_OldCriticalChance_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::NewProp_OldCriticalChance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_CriticalChance", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::AuracronSigilAttributeSet_eventOnRep_CriticalChance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::AuracronSigilAttributeSet_eventOnRep_CriticalChance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_CriticalChance)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldCriticalChance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CriticalChance(Z_Param_Out_OldCriticalChance);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_CriticalChance *******************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_DamageReflection ***************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_DamageReflection_Parms
	{
		FGameplayAttributeData OldDamageReflection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldDamageReflection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldDamageReflection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::NewProp_OldDamageReflection = { "OldDamageReflection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_DamageReflection_Parms, OldDamageReflection), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldDamageReflection_MetaData), NewProp_OldDamageReflection_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::NewProp_OldDamageReflection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_DamageReflection", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::AuracronSigilAttributeSet_eventOnRep_DamageReflection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::AuracronSigilAttributeSet_eventOnRep_DamageReflection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_DamageReflection)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldDamageReflection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_DamageReflection(Z_Param_Out_OldDamageReflection);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_DamageReflection *****************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_ElementalDamage ****************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_ElementalDamage_Parms
	{
		FGameplayAttributeData OldElementalDamage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldElementalDamage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldElementalDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::NewProp_OldElementalDamage = { "OldElementalDamage", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_ElementalDamage_Parms, OldElementalDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldElementalDamage_MetaData), NewProp_OldElementalDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::NewProp_OldElementalDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_ElementalDamage", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::AuracronSigilAttributeSet_eventOnRep_ElementalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::AuracronSigilAttributeSet_eventOnRep_ElementalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_ElementalDamage)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldElementalDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ElementalDamage(Z_Param_Out_OldElementalDamage);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_ElementalDamage ******************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_FusionEnergy *******************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_FusionEnergy_Parms
	{
		FGameplayAttributeData OldFusionEnergy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldFusionEnergy_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldFusionEnergy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::NewProp_OldFusionEnergy = { "OldFusionEnergy", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_FusionEnergy_Parms, OldFusionEnergy), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldFusionEnergy_MetaData), NewProp_OldFusionEnergy_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::NewProp_OldFusionEnergy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_FusionEnergy", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::AuracronSigilAttributeSet_eventOnRep_FusionEnergy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::AuracronSigilAttributeSet_eventOnRep_FusionEnergy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_FusionEnergy)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldFusionEnergy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_FusionEnergy(Z_Param_Out_OldFusionEnergy);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_FusionEnergy *********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_HealingPower *******************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_HealingPower_Parms
	{
		FGameplayAttributeData OldHealingPower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldHealingPower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldHealingPower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::NewProp_OldHealingPower = { "OldHealingPower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_HealingPower_Parms, OldHealingPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldHealingPower_MetaData), NewProp_OldHealingPower_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::NewProp_OldHealingPower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_HealingPower", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::AuracronSigilAttributeSet_eventOnRep_HealingPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::AuracronSigilAttributeSet_eventOnRep_HealingPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_HealingPower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldHealingPower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_HealingPower(Z_Param_Out_OldHealingPower);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_HealingPower *********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_MovementSpeed ******************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_MovementSpeed_Parms
	{
		FGameplayAttributeData OldMovementSpeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMovementSpeed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMovementSpeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed = { "OldMovementSpeed", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_MovementSpeed_Parms, OldMovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMovementSpeed_MetaData), NewProp_OldMovementSpeed_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::NewProp_OldMovementSpeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_MovementSpeed", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::AuracronSigilAttributeSet_eventOnRep_MovementSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::AuracronSigilAttributeSet_eventOnRep_MovementSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_MovementSpeed)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldMovementSpeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MovementSpeed(Z_Param_Out_OldMovementSpeed);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_MovementSpeed ********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_ShieldStrength *****************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_ShieldStrength_Parms
	{
		FGameplayAttributeData OldShieldStrength;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldShieldStrength_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldShieldStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::NewProp_OldShieldStrength = { "OldShieldStrength", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_ShieldStrength_Parms, OldShieldStrength), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldShieldStrength_MetaData), NewProp_OldShieldStrength_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::NewProp_OldShieldStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_ShieldStrength", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::AuracronSigilAttributeSet_eventOnRep_ShieldStrength_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::AuracronSigilAttributeSet_eventOnRep_ShieldStrength_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_ShieldStrength)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldShieldStrength);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ShieldStrength(Z_Param_Out_OldShieldStrength);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_ShieldStrength *******************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_SigilCooldownReduction *********
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_SigilCooldownReduction_Parms
	{
		FGameplayAttributeData OldSigilCooldownReduction;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilCooldownReduction_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilCooldownReduction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::NewProp_OldSigilCooldownReduction = { "OldSigilCooldownReduction", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_SigilCooldownReduction_Parms, OldSigilCooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilCooldownReduction_MetaData), NewProp_OldSigilCooldownReduction_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::NewProp_OldSigilCooldownReduction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_SigilCooldownReduction", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::AuracronSigilAttributeSet_eventOnRep_SigilCooldownReduction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::AuracronSigilAttributeSet_eventOnRep_SigilCooldownReduction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_SigilCooldownReduction)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilCooldownReduction);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilCooldownReduction(Z_Param_Out_OldSigilCooldownReduction);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_SigilCooldownReduction ***********

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_SigilDuration ******************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_SigilDuration_Parms
	{
		FGameplayAttributeData OldSigilDuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilDuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::NewProp_OldSigilDuration = { "OldSigilDuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_SigilDuration_Parms, OldSigilDuration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilDuration_MetaData), NewProp_OldSigilDuration_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::NewProp_OldSigilDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_SigilDuration", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::AuracronSigilAttributeSet_eventOnRep_SigilDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::AuracronSigilAttributeSet_eventOnRep_SigilDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_SigilDuration)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilDuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilDuration(Z_Param_Out_OldSigilDuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_SigilDuration ********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_SigilPower *********************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_SigilPower_Parms
	{
		FGameplayAttributeData OldSigilPower;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldSigilPower_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldSigilPower;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::NewProp_OldSigilPower = { "OldSigilPower", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_SigilPower_Parms, OldSigilPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldSigilPower_MetaData), NewProp_OldSigilPower_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::NewProp_OldSigilPower,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_SigilPower", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::AuracronSigilAttributeSet_eventOnRep_SigilPower_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::AuracronSigilAttributeSet_eventOnRep_SigilPower_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_SigilPower)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldSigilPower);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SigilPower(Z_Param_Out_OldSigilPower);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_SigilPower ***********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_TeleportRange ******************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_TeleportRange_Parms
	{
		FGameplayAttributeData OldTeleportRange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldTeleportRange_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldTeleportRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::NewProp_OldTeleportRange = { "OldTeleportRange", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_TeleportRange_Parms, OldTeleportRange), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldTeleportRange_MetaData), NewProp_OldTeleportRange_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::NewProp_OldTeleportRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_TeleportRange", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::AuracronSigilAttributeSet_eventOnRep_TeleportRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::AuracronSigilAttributeSet_eventOnRep_TeleportRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_TeleportRange)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldTeleportRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TeleportRange(Z_Param_Out_OldTeleportRange);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_TeleportRange ********************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_TimeDistortion *****************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_TimeDistortion_Parms
	{
		FGameplayAttributeData OldTimeDistortion;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldTimeDistortion_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldTimeDistortion;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::NewProp_OldTimeDistortion = { "OldTimeDistortion", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_TimeDistortion_Parms, OldTimeDistortion), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldTimeDistortion_MetaData), NewProp_OldTimeDistortion_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::NewProp_OldTimeDistortion,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_TimeDistortion", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::AuracronSigilAttributeSet_eventOnRep_TimeDistortion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::AuracronSigilAttributeSet_eventOnRep_TimeDistortion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_TimeDistortion)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldTimeDistortion);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_TimeDistortion(Z_Param_Out_OldTimeDistortion);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_TimeDistortion *******************

// ********** Begin Class UAuracronSigilAttributeSet Function OnRep_VisionRange ********************
struct Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics
{
	struct AuracronSigilAttributeSet_eventOnRep_VisionRange_Parms
	{
		FGameplayAttributeData OldVisionRange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldVisionRange_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldVisionRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::NewProp_OldVisionRange = { "OldVisionRange", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronSigilAttributeSet_eventOnRep_VisionRange_Parms, OldVisionRange), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldVisionRange_MetaData), NewProp_OldVisionRange_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::NewProp_OldVisionRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAttributeSet, nullptr, "OnRep_VisionRange", Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::AuracronSigilAttributeSet_eventOnRep_VisionRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00480400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::AuracronSigilAttributeSet_eventOnRep_VisionRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAttributeSet::execOnRep_VisionRange)
{
	P_GET_STRUCT_REF(FGameplayAttributeData,Z_Param_Out_OldVisionRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_VisionRange(Z_Param_Out_OldVisionRange);
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAttributeSet Function OnRep_VisionRange **********************

// ********** Begin Class UAuracronSigilAttributeSet ***********************************************
void UAuracronSigilAttributeSet::StaticRegisterNativesUAuracronSigilAttributeSet()
{
	UClass* Class = UAuracronSigilAttributeSet::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "OnRep_ArchetypeLevel", &UAuracronSigilAttributeSet::execOnRep_ArchetypeLevel },
		{ "OnRep_ArmorPenetration", &UAuracronSigilAttributeSet::execOnRep_ArmorPenetration },
		{ "OnRep_CriticalChance", &UAuracronSigilAttributeSet::execOnRep_CriticalChance },
		{ "OnRep_DamageReflection", &UAuracronSigilAttributeSet::execOnRep_DamageReflection },
		{ "OnRep_ElementalDamage", &UAuracronSigilAttributeSet::execOnRep_ElementalDamage },
		{ "OnRep_FusionEnergy", &UAuracronSigilAttributeSet::execOnRep_FusionEnergy },
		{ "OnRep_HealingPower", &UAuracronSigilAttributeSet::execOnRep_HealingPower },
		{ "OnRep_MovementSpeed", &UAuracronSigilAttributeSet::execOnRep_MovementSpeed },
		{ "OnRep_ShieldStrength", &UAuracronSigilAttributeSet::execOnRep_ShieldStrength },
		{ "OnRep_SigilCooldownReduction", &UAuracronSigilAttributeSet::execOnRep_SigilCooldownReduction },
		{ "OnRep_SigilDuration", &UAuracronSigilAttributeSet::execOnRep_SigilDuration },
		{ "OnRep_SigilPower", &UAuracronSigilAttributeSet::execOnRep_SigilPower },
		{ "OnRep_TeleportRange", &UAuracronSigilAttributeSet::execOnRep_TeleportRange },
		{ "OnRep_TimeDistortion", &UAuracronSigilAttributeSet::execOnRep_TimeDistortion },
		{ "OnRep_VisionRange", &UAuracronSigilAttributeSet::execOnRep_VisionRange },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronSigilAttributeSet;
UClass* UAuracronSigilAttributeSet::GetPrivateStaticClass()
{
	using TClass = UAuracronSigilAttributeSet;
	if (!Z_Registration_Info_UClass_UAuracronSigilAttributeSet.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronSigilAttributeSet"),
			Z_Registration_Info_UClass_UAuracronSigilAttributeSet.InnerSingleton,
			StaticRegisterNativesUAuracronSigilAttributeSet,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronSigilAttributeSet.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronSigilAttributeSet_NoRegister()
{
	return UAuracronSigilAttributeSet::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronSigilAttributeSet_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Attribute Set for Auracron Sigil System\n * Defines all attributes that can be modified by Sigils\n */" },
#endif
		{ "IncludePath", "AuracronSigilEffects.h" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute Set for Auracron Sigil System\nDefines all attributes that can be modified by Sigils" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilPower_MetaData[] = {
		{ "Category", "Sigil Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Core Attributes ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Core Attributes ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilDuration_MetaData[] = {
		{ "Category", "Sigil Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigilCooldownReduction_MetaData[] = {
		{ "Category", "Sigil Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionEnergy_MetaData[] = {
		{ "Category", "Sigil Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArchetypeLevel_MetaData[] = {
		{ "Category", "Sigil Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShieldStrength_MetaData[] = {
		{ "Category", "Aegis Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Aegis Attributes ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Aegis Attributes ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageReflection_MetaData[] = {
		{ "Category", "Aegis Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeDistortion_MetaData[] = {
		{ "Category", "Aegis Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementalDamage_MetaData[] = {
		{ "Category", "Ruin Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Ruin Attributes ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Ruin Attributes ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArmorPenetration_MetaData[] = {
		{ "Category", "Ruin Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChance_MetaData[] = {
		{ "Category", "Ruin Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingPower_MetaData[] = {
		{ "Category", "Vesper Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Vesper Attributes ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Vesper Attributes ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Vesper Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRange_MetaData[] = {
		{ "Category", "Vesper Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeleportRange_MetaData[] = {
		{ "Category", "Vesper Attributes" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilPower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SigilCooldownReduction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FusionEnergy;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ArchetypeLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShieldStrength;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DamageReflection;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TimeDistortion;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementalDamage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ArmorPenetration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CriticalChance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealingPower;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisionRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TeleportRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArchetypeLevel, "OnRep_ArchetypeLevel" }, // 2133758931
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ArmorPenetration, "OnRep_ArmorPenetration" }, // 3493083431
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_CriticalChance, "OnRep_CriticalChance" }, // 880351768
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_DamageReflection, "OnRep_DamageReflection" }, // 2488116398
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ElementalDamage, "OnRep_ElementalDamage" }, // 2848999732
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_FusionEnergy, "OnRep_FusionEnergy" }, // 2178293381
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_HealingPower, "OnRep_HealingPower" }, // 3501059858
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_MovementSpeed, "OnRep_MovementSpeed" }, // 229354706
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_ShieldStrength, "OnRep_ShieldStrength" }, // 2891840500
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilCooldownReduction, "OnRep_SigilCooldownReduction" }, // 654475404
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilDuration, "OnRep_SigilDuration" }, // 1011724861
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_SigilPower, "OnRep_SigilPower" }, // 3013435191
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TeleportRange, "OnRep_TeleportRange" }, // 829352740
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_TimeDistortion, "OnRep_TimeDistortion" }, // 3657216851
		{ &Z_Construct_UFunction_UAuracronSigilAttributeSet_OnRep_VisionRange, "OnRep_VisionRange" }, // 1659693450
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronSigilAttributeSet>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilPower = { "SigilPower", "OnRep_SigilPower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, SigilPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilPower_MetaData), NewProp_SigilPower_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilDuration = { "SigilDuration", "OnRep_SigilDuration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, SigilDuration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilDuration_MetaData), NewProp_SigilDuration_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilCooldownReduction = { "SigilCooldownReduction", "OnRep_SigilCooldownReduction", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, SigilCooldownReduction), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigilCooldownReduction_MetaData), NewProp_SigilCooldownReduction_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_FusionEnergy = { "FusionEnergy", "OnRep_FusionEnergy", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, FusionEnergy), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionEnergy_MetaData), NewProp_FusionEnergy_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ArchetypeLevel = { "ArchetypeLevel", "OnRep_ArchetypeLevel", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, ArchetypeLevel), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArchetypeLevel_MetaData), NewProp_ArchetypeLevel_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ShieldStrength = { "ShieldStrength", "OnRep_ShieldStrength", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, ShieldStrength), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShieldStrength_MetaData), NewProp_ShieldStrength_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_DamageReflection = { "DamageReflection", "OnRep_DamageReflection", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, DamageReflection), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageReflection_MetaData), NewProp_DamageReflection_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_TimeDistortion = { "TimeDistortion", "OnRep_TimeDistortion", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, TimeDistortion), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeDistortion_MetaData), NewProp_TimeDistortion_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ElementalDamage = { "ElementalDamage", "OnRep_ElementalDamage", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, ElementalDamage), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementalDamage_MetaData), NewProp_ElementalDamage_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ArmorPenetration = { "ArmorPenetration", "OnRep_ArmorPenetration", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, ArmorPenetration), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArmorPenetration_MetaData), NewProp_ArmorPenetration_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_CriticalChance = { "CriticalChance", "OnRep_CriticalChance", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, CriticalChance), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChance_MetaData), NewProp_CriticalChance_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_HealingPower = { "HealingPower", "OnRep_HealingPower", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, HealingPower), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingPower_MetaData), NewProp_HealingPower_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_MovementSpeed = { "MovementSpeed", "OnRep_MovementSpeed", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, MovementSpeed), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_VisionRange = { "VisionRange", "OnRep_VisionRange", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, VisionRange), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRange_MetaData), NewProp_VisionRange_MetaData) }; // 1532612004
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_TeleportRange = { "TeleportRange", "OnRep_TeleportRange", (EPropertyFlags)0x0010000100000034, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAttributeSet, TeleportRange), Z_Construct_UScriptStruct_FGameplayAttributeData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeleportRange_MetaData), NewProp_TeleportRange_MetaData) }; // 1532612004
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_SigilCooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_FusionEnergy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ArchetypeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ShieldStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_DamageReflection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_TimeDistortion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ElementalDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_ArmorPenetration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_CriticalChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_HealingPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_VisionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::NewProp_TeleportRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAttributeSet,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::ClassParams = {
	&UAuracronSigilAttributeSet::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::PropPointers),
	0,
	0x003000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronSigilAttributeSet()
{
	if (!Z_Registration_Info_UClass_UAuracronSigilAttributeSet.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronSigilAttributeSet.OuterSingleton, Z_Construct_UClass_UAuracronSigilAttributeSet_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronSigilAttributeSet.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronSigilAttributeSet::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_SigilPower(TEXT("SigilPower"));
	static FName Name_SigilDuration(TEXT("SigilDuration"));
	static FName Name_SigilCooldownReduction(TEXT("SigilCooldownReduction"));
	static FName Name_FusionEnergy(TEXT("FusionEnergy"));
	static FName Name_ArchetypeLevel(TEXT("ArchetypeLevel"));
	static FName Name_ShieldStrength(TEXT("ShieldStrength"));
	static FName Name_DamageReflection(TEXT("DamageReflection"));
	static FName Name_TimeDistortion(TEXT("TimeDistortion"));
	static FName Name_ElementalDamage(TEXT("ElementalDamage"));
	static FName Name_ArmorPenetration(TEXT("ArmorPenetration"));
	static FName Name_CriticalChance(TEXT("CriticalChance"));
	static FName Name_HealingPower(TEXT("HealingPower"));
	static FName Name_MovementSpeed(TEXT("MovementSpeed"));
	static FName Name_VisionRange(TEXT("VisionRange"));
	static FName Name_TeleportRange(TEXT("TeleportRange"));
	const bool bIsValid = true
		&& Name_SigilPower == ClassReps[(int32)ENetFields_Private::SigilPower].Property->GetFName()
		&& Name_SigilDuration == ClassReps[(int32)ENetFields_Private::SigilDuration].Property->GetFName()
		&& Name_SigilCooldownReduction == ClassReps[(int32)ENetFields_Private::SigilCooldownReduction].Property->GetFName()
		&& Name_FusionEnergy == ClassReps[(int32)ENetFields_Private::FusionEnergy].Property->GetFName()
		&& Name_ArchetypeLevel == ClassReps[(int32)ENetFields_Private::ArchetypeLevel].Property->GetFName()
		&& Name_ShieldStrength == ClassReps[(int32)ENetFields_Private::ShieldStrength].Property->GetFName()
		&& Name_DamageReflection == ClassReps[(int32)ENetFields_Private::DamageReflection].Property->GetFName()
		&& Name_TimeDistortion == ClassReps[(int32)ENetFields_Private::TimeDistortion].Property->GetFName()
		&& Name_ElementalDamage == ClassReps[(int32)ENetFields_Private::ElementalDamage].Property->GetFName()
		&& Name_ArmorPenetration == ClassReps[(int32)ENetFields_Private::ArmorPenetration].Property->GetFName()
		&& Name_CriticalChance == ClassReps[(int32)ENetFields_Private::CriticalChance].Property->GetFName()
		&& Name_HealingPower == ClassReps[(int32)ENetFields_Private::HealingPower].Property->GetFName()
		&& Name_MovementSpeed == ClassReps[(int32)ENetFields_Private::MovementSpeed].Property->GetFName()
		&& Name_VisionRange == ClassReps[(int32)ENetFields_Private::VisionRange].Property->GetFName()
		&& Name_TeleportRange == ClassReps[(int32)ENetFields_Private::TeleportRange].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronSigilAttributeSet"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronSigilAttributeSet);
UAuracronSigilAttributeSet::~UAuracronSigilAttributeSet() {}
// ********** End Class UAuracronSigilAttributeSet *************************************************

// ********** Begin Class UAuracronSigilAbility Function ApplySigilEffect **************************
static FName NAME_UAuracronSigilAbility_ApplySigilEffect = FName(TEXT("ApplySigilEffect"));
void UAuracronSigilAbility::ApplySigilEffect()
{
	UFunction* Func = FindFunctionChecked(NAME_UAuracronSigilAbility_ApplySigilEffect);
	if (!Func->GetOwnerClass()->HasAnyClassFlags(CLASS_Native))
	{
	ProcessEvent(Func,NULL);
	}
	else
	{
		ApplySigilEffect_Implementation();
	}
}
struct Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAbility, nullptr, "ApplySigilEffect", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronSigilAbility::execApplySigilEffect)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySigilEffect_Implementation();
	P_NATIVE_END;
}
// ********** End Class UAuracronSigilAbility Function ApplySigilEffect ****************************

// ********** Begin Class UAuracronSigilAbility Function OnSigilActivated **************************
static FName NAME_UAuracronSigilAbility_OnSigilActivated = FName(TEXT("OnSigilActivated"));
void UAuracronSigilAbility::OnSigilActivated()
{
	UFunction* Func = FindFunctionChecked(NAME_UAuracronSigilAbility_OnSigilActivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Sigil-specific Implementation ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Sigil-specific Implementation ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAbility, nullptr, "OnSigilActivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronSigilAbility Function OnSigilActivated ****************************

// ********** Begin Class UAuracronSigilAbility Function OnSigilDeactivated ************************
static FName NAME_UAuracronSigilAbility_OnSigilDeactivated = FName(TEXT("OnSigilDeactivated"));
void UAuracronSigilAbility::OnSigilDeactivated()
{
	UFunction* Func = FindFunctionChecked(NAME_UAuracronSigilAbility_OnSigilDeactivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sigil Ability" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronSigilAbility, nullptr, "OnSigilDeactivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronSigilAbility Function OnSigilDeactivated **************************

// ********** Begin Class UAuracronSigilAbility ****************************************************
void UAuracronSigilAbility::StaticRegisterNativesUAuracronSigilAbility()
{
	UClass* Class = UAuracronSigilAbility::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplySigilEffect", &UAuracronSigilAbility::execApplySigilEffect },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronSigilAbility;
UClass* UAuracronSigilAbility::GetPrivateStaticClass()
{
	using TClass = UAuracronSigilAbility;
	if (!Z_Registration_Info_UClass_UAuracronSigilAbility.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronSigilAbility"),
			Z_Registration_Info_UClass_UAuracronSigilAbility.InnerSingleton,
			StaticRegisterNativesUAuracronSigilAbility,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronSigilAbility.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronSigilAbility_NoRegister()
{
	return UAuracronSigilAbility::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronSigilAbility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base GameplayAbility for Auracron Sigils\n */" },
#endif
		{ "IncludePath", "AuracronSigilEffects.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base GameplayAbility for Auracron Sigils" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloType_MetaData[] = {
		{ "Category", "Sigil Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Type of Sigil this ability belongs to */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type of Sigil this ability belongs to" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PowerScaling_MetaData[] = {
		{ "Category", "Sigil Ability" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Power scaling factor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Power scaling factor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDuration_MetaData[] = {
		{ "Category", "Sigil Ability" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base duration of the ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base duration of the ability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseCooldown_MetaData[] = {
		{ "Category", "Sigil Ability" },
		{ "ClampMax", "180.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base cooldown of the ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base cooldown of the ability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaCost_MetaData[] = {
		{ "Category", "Sigil Ability" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana cost of the ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana cost of the ability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityVFX_MetaData[] = {
		{ "Category", "Sigil Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual effects for this ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual effects for this ability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityAudio_MetaData[] = {
		{ "Category", "Sigil Ability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio effects for this ability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronSigilEffects.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio effects for this ability" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigiloType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerScaling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaCost;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilityVFX;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AbilityAudio;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronSigilAbility_ApplySigilEffect, "ApplySigilEffect" }, // 1896311180
		{ &Z_Construct_UFunction_UAuracronSigilAbility_OnSigilActivated, "OnSigilActivated" }, // 962011789
		{ &Z_Construct_UFunction_UAuracronSigilAbility_OnSigilDeactivated, "OnSigilDeactivated" }, // 3796324569
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronSigilAbility>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_SigiloType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, SigiloType), Z_Construct_UEnum_AuracronSigilosBridge_EAuracronSigiloType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloType_MetaData), NewProp_SigiloType_MetaData) }; // 1094464071
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_PowerScaling = { "PowerScaling", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, PowerScaling), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PowerScaling_MetaData), NewProp_PowerScaling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_BaseDuration = { "BaseDuration", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, BaseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDuration_MetaData), NewProp_BaseDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_BaseCooldown = { "BaseCooldown", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, BaseCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseCooldown_MetaData), NewProp_BaseCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_ManaCost = { "ManaCost", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, ManaCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaCost_MetaData), NewProp_ManaCost_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_AbilityVFX = { "AbilityVFX", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, AbilityVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityVFX_MetaData), NewProp_AbilityVFX_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_AbilityAudio = { "AbilityAudio", nullptr, (EPropertyFlags)0x0024080000010015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronSigilAbility, AbilityAudio), Z_Construct_UClass_UMetaSoundSource_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityAudio_MetaData), NewProp_AbilityAudio_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronSigilAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_SigiloType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_PowerScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_BaseDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_BaseCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_ManaCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_AbilityVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronSigilAbility_Statics::NewProp_AbilityAudio,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAbility_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronSigilAbility_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameplayAbility,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronSigilosBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAbility_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronSigilAbility_Statics::ClassParams = {
	&UAuracronSigilAbility::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronSigilAbility_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAbility_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronSigilAbility_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronSigilAbility_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronSigilAbility()
{
	if (!Z_Registration_Info_UClass_UAuracronSigilAbility.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronSigilAbility.OuterSingleton, Z_Construct_UClass_UAuracronSigilAbility_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronSigilAbility.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronSigilAbility);
UAuracronSigilAbility::~UAuracronSigilAbility() {}
// ********** End Class UAuracronSigilAbility ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h__Script_AuracronSigilosBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronSigilAttributeSet, UAuracronSigilAttributeSet::StaticClass, TEXT("UAuracronSigilAttributeSet"), &Z_Registration_Info_UClass_UAuracronSigilAttributeSet, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronSigilAttributeSet), 1277127816U) },
		{ Z_Construct_UClass_UAuracronSigilAbility, UAuracronSigilAbility::StaticClass, TEXT("UAuracronSigilAbility"), &Z_Registration_Info_UClass_UAuracronSigilAbility, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronSigilAbility), 2206016327U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h__Script_AuracronSigilosBridge_2883339749(TEXT("/Script/AuracronSigilosBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h__Script_AuracronSigilosBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h__Script_AuracronSigilosBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
