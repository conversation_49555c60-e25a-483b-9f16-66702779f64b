// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionDebug.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionDebug_generated_h
#error "AuracronWorldPartitionDebug.generated.h already included, missing '#pragma once' in AuracronWorldPartitionDebug.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionDebug_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronWorldPartitionDebugManager;
class UWorld;
enum class EAuracronDebugColorScheme : uint8;
enum class EAuracronDebugInfoLevel : uint8;
enum class EAuracronDebugVisualizationMode : uint8;
struct FAuracronDebugCellInfo;
struct FAuracronDebugConfiguration;
struct FAuracronDebugPerformanceData;
struct FColor;

// ********** Begin ScriptStruct FAuracronDebugConfiguration ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDebugConfiguration;
// ********** End ScriptStruct FAuracronDebugConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDebugCellInfo ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_215_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDebugCellInfo;
// ********** End ScriptStruct FAuracronDebugCellInfo **********************************************

// ********** Begin ScriptStruct FAuracronDebugPerformanceData *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_269_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDebugPerformanceData;
// ********** End ScriptStruct FAuracronDebugPerformanceData ***************************************

// ********** Begin Delegate FOnDebugEvent *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_481_DELEGATE \
static void FOnDebugEvent_DelegateWrapper(const FMulticastScriptDelegate& OnDebugEvent, const FString& EventType, const FString& Details);


// ********** End Delegate FOnDebugEvent ***********************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_482_DELEGATE \
static void FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FAuracronDebugPerformanceData PerformanceData);


// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnStreamingEvent *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_483_DELEGATE \
static void FOnStreamingEvent_DelegateWrapper(const FMulticastScriptDelegate& OnStreamingEvent, const FString& CellId, const FString& EventDetails);


// ********** End Delegate FOnStreamingEvent *******************************************************

// ********** Begin Class UAuracronWorldPartitionDebugManager **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetDebugColor); \
	DECLARE_FUNCTION(execGetColorScheme); \
	DECLARE_FUNCTION(execSetColorScheme); \
	DECLARE_FUNCTION(execGetDebugInfoLevel); \
	DECLARE_FUNCTION(execSetDebugInfoLevel); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execExportDebugData); \
	DECLARE_FUNCTION(execLoadDebugSnapshot); \
	DECLARE_FUNCTION(execSaveDebugSnapshot); \
	DECLARE_FUNCTION(execTakeDebugSnapshot); \
	DECLARE_FUNCTION(execGetDebugCommandHelp); \
	DECLARE_FUNCTION(execGetAvailableDebugCommands); \
	DECLARE_FUNCTION(execExecuteDebugCommand); \
	DECLARE_FUNCTION(execDrawInspectorWindow); \
	DECLARE_FUNCTION(execGetInspectorData); \
	DECLARE_FUNCTION(execRefreshInspectorData); \
	DECLARE_FUNCTION(execIsWorldPartitionInspectorEnabled); \
	DECLARE_FUNCTION(execEnableWorldPartitionInspector); \
	DECLARE_FUNCTION(execDrawPerformanceMetrics); \
	DECLARE_FUNCTION(execGetPerformanceHistory); \
	DECLARE_FUNCTION(execGetCurrentPerformanceData); \
	DECLARE_FUNCTION(execStopProfiling); \
	DECLARE_FUNCTION(execStartProfiling); \
	DECLARE_FUNCTION(execIsPerformanceProfilerEnabled); \
	DECLARE_FUNCTION(execEnablePerformanceProfiler); \
	DECLARE_FUNCTION(execClearStreamingEventLog); \
	DECLARE_FUNCTION(execGetStreamingEventLog); \
	DECLARE_FUNCTION(execLogStreamingEvent); \
	DECLARE_FUNCTION(execIsStreamingDebugEnabled); \
	DECLARE_FUNCTION(execEnableStreamingDebug); \
	DECLARE_FUNCTION(execGetAllCellDebugInfo); \
	DECLARE_FUNCTION(execGetCellDebugInfo); \
	DECLARE_FUNCTION(execDrawLoadingProgress); \
	DECLARE_FUNCTION(execDrawStreamingStates); \
	DECLARE_FUNCTION(execDrawCellBounds); \
	DECLARE_FUNCTION(execDrawDebugVisualization); \
	DECLARE_FUNCTION(execGetVisualizationMode); \
	DECLARE_FUNCTION(execSetVisualizationMode); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionDebugManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionDebugManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionDebugManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionDebugManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionDebugManager(UAuracronWorldPartitionDebugManager&&) = delete; \
	UAuracronWorldPartitionDebugManager(const UAuracronWorldPartitionDebugManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionDebugManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionDebugManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionDebugManager) \
	NO_API virtual ~UAuracronWorldPartitionDebugManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_324_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h_327_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionDebugManager;

// ********** End Class UAuracronWorldPartitionDebugManager ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h

// ********** Begin Enum EAuracronCellStreamingState ***********************************************
#define FOREACH_ENUM_EAURACRONCELLSTREAMINGSTATE(op) \
	op(EAuracronCellStreamingState::Unloaded) \
	op(EAuracronCellStreamingState::Loading) \
	op(EAuracronCellStreamingState::Loaded) \
	op(EAuracronCellStreamingState::Unloading) \
	op(EAuracronCellStreamingState::Error) \
	op(EAuracronCellStreamingState::Failed) \
	op(EAuracronCellStreamingState::Preloading) \
	op(EAuracronCellStreamingState::Preloaded) 

enum class EAuracronCellStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronCellStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCellStreamingState>();
// ********** End Enum EAuracronCellStreamingState *************************************************

// ********** Begin Enum EAuracronDebugVisualizationMode *******************************************
#define FOREACH_ENUM_EAURACRONDEBUGVISUALIZATIONMODE(op) \
	op(EAuracronDebugVisualizationMode::None) \
	op(EAuracronDebugVisualizationMode::CellBounds) \
	op(EAuracronDebugVisualizationMode::StreamingStates) \
	op(EAuracronDebugVisualizationMode::LoadingProgress) \
	op(EAuracronDebugVisualizationMode::PerformanceMetrics) \
	op(EAuracronDebugVisualizationMode::ActorDistribution) \
	op(EAuracronDebugVisualizationMode::MemoryUsage) \
	op(EAuracronDebugVisualizationMode::All) 

enum class EAuracronDebugVisualizationMode : uint8;
template<> struct TIsUEnumClass<EAuracronDebugVisualizationMode> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugVisualizationMode>();
// ********** End Enum EAuracronDebugVisualizationMode *********************************************

// ********** Begin Enum EAuracronDebugInfoLevel ***************************************************
#define FOREACH_ENUM_EAURACRONDEBUGINFOLEVEL(op) \
	op(EAuracronDebugInfoLevel::Basic) \
	op(EAuracronDebugInfoLevel::Detailed) \
	op(EAuracronDebugInfoLevel::Verbose) \
	op(EAuracronDebugInfoLevel::Expert) 

enum class EAuracronDebugInfoLevel : uint8;
template<> struct TIsUEnumClass<EAuracronDebugInfoLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugInfoLevel>();
// ********** End Enum EAuracronDebugInfoLevel *****************************************************

// ********** Begin Enum EAuracronDebugColorScheme *************************************************
#define FOREACH_ENUM_EAURACRONDEBUGCOLORSCHEME(op) \
	op(EAuracronDebugColorScheme::Default) \
	op(EAuracronDebugColorScheme::HighContrast) \
	op(EAuracronDebugColorScheme::ColorBlind) \
	op(EAuracronDebugColorScheme::Monochrome) 

enum class EAuracronDebugColorScheme : uint8;
template<> struct TIsUEnumClass<EAuracronDebugColorScheme> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugColorScheme>();
// ********** End Enum EAuracronDebugColorScheme ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
