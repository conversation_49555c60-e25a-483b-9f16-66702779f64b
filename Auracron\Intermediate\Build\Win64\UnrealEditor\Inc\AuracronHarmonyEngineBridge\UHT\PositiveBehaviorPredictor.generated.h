// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PositiveBehaviorPredictor.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_PositiveBehaviorPredictor_generated_h
#error "PositiveBehaviorPredictor.generated.h already included, missing '#pragma once' in PositiveBehaviorPredictor.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_PositiveBehaviorPredictor_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FGameplayTag;
struct FPlayerBehaviorSnapshot;

// ********** Begin Class UPositiveBehaviorPredictor ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateBonusKindnessPoints); \
	DECLARE_FUNCTION(execShouldAmplifyRewards); \
	DECLARE_FUNCTION(execCalculateRewardMultiplier); \
	DECLARE_FUNCTION(execHasPositiveTrend); \
	DECLARE_FUNCTION(execCalculateConsistencyScore); \
	DECLARE_FUNCTION(execAnalyzePositiveBehaviorPatterns); \
	DECLARE_FUNCTION(execResetTrainingData); \
	DECLARE_FUNCTION(execGetModelAccuracy); \
	DECLARE_FUNCTION(execTrainPredictionModel); \
	DECLARE_FUNCTION(execAddTrainingData); \
	DECLARE_FUNCTION(execCalculateLeadershipPotential); \
	DECLARE_FUNCTION(execIdentifyPotentialCommunityLeaders); \
	DECLARE_FUNCTION(execIdentifyPotentialMentors); \
	DECLARE_FUNCTION(execIsPlayerLikelyToBePositive); \
	DECLARE_FUNCTION(execPredictPositiveBehaviorProbability);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UPositiveBehaviorPredictor_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPositiveBehaviorPredictor(); \
	friend struct Z_Construct_UClass_UPositiveBehaviorPredictor_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UPositiveBehaviorPredictor_NoRegister(); \
public: \
	DECLARE_CLASS2(UPositiveBehaviorPredictor, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UPositiveBehaviorPredictor_NoRegister) \
	DECLARE_SERIALIZER(UPositiveBehaviorPredictor)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPositiveBehaviorPredictor(UPositiveBehaviorPredictor&&) = delete; \
	UPositiveBehaviorPredictor(const UPositiveBehaviorPredictor&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPositiveBehaviorPredictor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPositiveBehaviorPredictor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPositiveBehaviorPredictor) \
	NO_API virtual ~UPositiveBehaviorPredictor();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_13_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPositiveBehaviorPredictor;

// ********** End Class UPositiveBehaviorPredictor *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_PositiveBehaviorPredictor_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
