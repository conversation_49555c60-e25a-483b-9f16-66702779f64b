// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Base Header
// Bridge 2.2: PCG Framework - Base Classes

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGContext.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "AuracronPCGBase.generated.h"

// Forward declarations
class UPCGComponent;
class UPCGGraph;
struct FPCGContext;

/**
 * Base class for all Auracron PCG Settings
 * Provides common functionality and interface for all PCG nodes
 */
UCLASS(BlueprintType, Blueprintable, Abstract, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API UAuracronPCGSettingsBase : public UPCGSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSettingsBase(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    // Auracron specific settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron Settings")
    FString NodeDescription = TEXT("Auracron PCG Node");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 BatchSize = 1000;

protected:
    // Override in derived classes
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Base class for all Auracron PCG Elements
 * Provides common execution framework
 */
class AURACRONPCGBRIDGE_API FAuracronPCGElementBase : public IPCGElement
{
public:
    FAuracronPCGElementBase() = default;
    virtual ~FAuracronPCGElementBase() = default;

    // IPCGElement interface
    virtual FPCGContext* Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node) override;

protected:
    // Override in derived classes
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    virtual void ProcessPoints(FPCGContext* Context, const FPCGDataCollection& InputData, FPCGDataCollection& OutputData) const;
};

/**
 * Simple example PCG node that transforms points
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API UAuracronPCGExampleSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronPCGExampleSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    FVector Offset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
    float Scale = 1.0f;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Example PCG element implementation
 */
class AURACRONPCGBRIDGE_API FAuracronPCGExampleElement : public FAuracronPCGElementBase
{
public:
    FAuracronPCGExampleElement() = default;
    virtual ~FAuracronPCGExampleElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
};
