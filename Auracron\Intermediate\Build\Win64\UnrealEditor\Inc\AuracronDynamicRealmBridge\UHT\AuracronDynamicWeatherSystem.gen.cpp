// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicWeatherSystem.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronDynamicWeatherSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWeatherState();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_ADirectionalLight_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AExponentialHeightFog_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ASkyLight_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EDynamicWeatherType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDynamicWeatherType;
static UEnum* EDynamicWeatherType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDynamicWeatherType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDynamicWeatherType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EDynamicWeatherType"));
	}
	return Z_Registration_Info_UEnum_EDynamicWeatherType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EDynamicWeatherType>()
{
	return EDynamicWeatherType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abyssal.DisplayName", "Abyssal" },
		{ "Abyssal.Name", "EDynamicWeatherType::Abyssal" },
		{ "BlueprintType", "true" },
		{ "Chaos.DisplayName", "Chaos" },
		{ "Chaos.Name", "EDynamicWeatherType::Chaos" },
		{ "Clear.DisplayName", "Clear" },
		{ "Clear.Name", "EDynamicWeatherType::Clear" },
		{ "Cloudy.DisplayName", "Cloudy" },
		{ "Cloudy.Name", "EDynamicWeatherType::Cloudy" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Dynamic weather types\n */" },
#endif
		{ "Ethereal.DisplayName", "Ethereal" },
		{ "Ethereal.Name", "EDynamicWeatherType::Ethereal" },
		{ "Fog.DisplayName", "Fog" },
		{ "Fog.Name", "EDynamicWeatherType::Fog" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
		{ "Mystical.DisplayName", "Mystical" },
		{ "Mystical.Name", "EDynamicWeatherType::Mystical" },
		{ "Prismal.DisplayName", "Prismal" },
		{ "Prismal.Name", "EDynamicWeatherType::Prismal" },
		{ "Rain.DisplayName", "Rain" },
		{ "Rain.Name", "EDynamicWeatherType::Rain" },
		{ "Storm.DisplayName", "Storm" },
		{ "Storm.Name", "EDynamicWeatherType::Storm" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic weather types" },
#endif
		{ "Transcendent.DisplayName", "Transcendent" },
		{ "Transcendent.Name", "EDynamicWeatherType::Transcendent" },
		{ "Wind.DisplayName", "Wind" },
		{ "Wind.Name", "EDynamicWeatherType::Wind" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDynamicWeatherType::Clear", (int64)EDynamicWeatherType::Clear },
		{ "EDynamicWeatherType::Cloudy", (int64)EDynamicWeatherType::Cloudy },
		{ "EDynamicWeatherType::Rain", (int64)EDynamicWeatherType::Rain },
		{ "EDynamicWeatherType::Storm", (int64)EDynamicWeatherType::Storm },
		{ "EDynamicWeatherType::Fog", (int64)EDynamicWeatherType::Fog },
		{ "EDynamicWeatherType::Wind", (int64)EDynamicWeatherType::Wind },
		{ "EDynamicWeatherType::Mystical", (int64)EDynamicWeatherType::Mystical },
		{ "EDynamicWeatherType::Prismal", (int64)EDynamicWeatherType::Prismal },
		{ "EDynamicWeatherType::Chaos", (int64)EDynamicWeatherType::Chaos },
		{ "EDynamicWeatherType::Ethereal", (int64)EDynamicWeatherType::Ethereal },
		{ "EDynamicWeatherType::Abyssal", (int64)EDynamicWeatherType::Abyssal },
		{ "EDynamicWeatherType::Transcendent", (int64)EDynamicWeatherType::Transcendent },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EDynamicWeatherType",
	"EDynamicWeatherType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType()
{
	if (!Z_Registration_Info_UEnum_EDynamicWeatherType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDynamicWeatherType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDynamicWeatherType.InnerSingleton;
}
// ********** End Enum EDynamicWeatherType *********************************************************

// ********** Begin Enum EWeatherIntensity *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWeatherIntensity;
static UEnum* EWeatherIntensity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWeatherIntensity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWeatherIntensity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EWeatherIntensity"));
	}
	return Z_Registration_Info_UEnum_EWeatherIntensity.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EWeatherIntensity>()
{
	return EWeatherIntensity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Weather intensity levels\n */" },
#endif
		{ "Extreme.DisplayName", "Extreme" },
		{ "Extreme.Name", "EWeatherIntensity::Extreme" },
		{ "Heavy.DisplayName", "Heavy" },
		{ "Heavy.Name", "EWeatherIntensity::Heavy" },
		{ "Light.DisplayName", "Light" },
		{ "Light.Name", "EWeatherIntensity::Light" },
		{ "Moderate.DisplayName", "Moderate" },
		{ "Moderate.Name", "EWeatherIntensity::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EWeatherIntensity::None" },
		{ "Supernatural.DisplayName", "Supernatural" },
		{ "Supernatural.Name", "EWeatherIntensity::Supernatural" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather intensity levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWeatherIntensity::None", (int64)EWeatherIntensity::None },
		{ "EWeatherIntensity::Light", (int64)EWeatherIntensity::Light },
		{ "EWeatherIntensity::Moderate", (int64)EWeatherIntensity::Moderate },
		{ "EWeatherIntensity::Heavy", (int64)EWeatherIntensity::Heavy },
		{ "EWeatherIntensity::Extreme", (int64)EWeatherIntensity::Extreme },
		{ "EWeatherIntensity::Supernatural", (int64)EWeatherIntensity::Supernatural },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EWeatherIntensity",
	"EWeatherIntensity",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity()
{
	if (!Z_Registration_Info_UEnum_EWeatherIntensity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWeatherIntensity.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWeatherIntensity.InnerSingleton;
}
// ********** End Enum EWeatherIntensity ***********************************************************

// ********** Begin Enum EWeatherTriggerEvent ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWeatherTriggerEvent;
static UEnum* EWeatherTriggerEvent_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWeatherTriggerEvent.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWeatherTriggerEvent.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EWeatherTriggerEvent"));
	}
	return Z_Registration_Info_UEnum_EWeatherTriggerEvent.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EWeatherTriggerEvent>()
{
	return EWeatherTriggerEvent_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Weather trigger events\n */" },
#endif
		{ "EnvironmentalShift.DisplayName", "Environmental Shift" },
		{ "EnvironmentalShift.Name", "EWeatherTriggerEvent::EnvironmentalShift" },
		{ "FlowSurge.DisplayName", "Flow Surge" },
		{ "FlowSurge.Name", "EWeatherTriggerEvent::FlowSurge" },
		{ "IslandActivation.DisplayName", "Island Activation" },
		{ "IslandActivation.Name", "EWeatherTriggerEvent::IslandActivation" },
		{ "LayerEvolution.DisplayName", "Layer Evolution" },
		{ "LayerEvolution.Name", "EWeatherTriggerEvent::LayerEvolution" },
		{ "MatchStart.DisplayName", "Match Start" },
		{ "MatchStart.Name", "EWeatherTriggerEvent::MatchStart" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
		{ "ObjectiveComplete.DisplayName", "Objective Complete" },
		{ "ObjectiveComplete.Name", "EWeatherTriggerEvent::ObjectiveComplete" },
		{ "PlayerDeath.DisplayName", "Player Death" },
		{ "PlayerDeath.Name", "EWeatherTriggerEvent::PlayerDeath" },
		{ "TeamFight.DisplayName", "Team Fight" },
		{ "TeamFight.Name", "EWeatherTriggerEvent::TeamFight" },
		{ "TimeProgression.DisplayName", "Time Progression" },
		{ "TimeProgression.Name", "EWeatherTriggerEvent::TimeProgression" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather trigger events" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWeatherTriggerEvent::MatchStart", (int64)EWeatherTriggerEvent::MatchStart },
		{ "EWeatherTriggerEvent::TeamFight", (int64)EWeatherTriggerEvent::TeamFight },
		{ "EWeatherTriggerEvent::IslandActivation", (int64)EWeatherTriggerEvent::IslandActivation },
		{ "EWeatherTriggerEvent::FlowSurge", (int64)EWeatherTriggerEvent::FlowSurge },
		{ "EWeatherTriggerEvent::LayerEvolution", (int64)EWeatherTriggerEvent::LayerEvolution },
		{ "EWeatherTriggerEvent::PlayerDeath", (int64)EWeatherTriggerEvent::PlayerDeath },
		{ "EWeatherTriggerEvent::ObjectiveComplete", (int64)EWeatherTriggerEvent::ObjectiveComplete },
		{ "EWeatherTriggerEvent::TimeProgression", (int64)EWeatherTriggerEvent::TimeProgression },
		{ "EWeatherTriggerEvent::EnvironmentalShift", (int64)EWeatherTriggerEvent::EnvironmentalShift },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EWeatherTriggerEvent",
	"EWeatherTriggerEvent",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent()
{
	if (!Z_Registration_Info_UEnum_EWeatherTriggerEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWeatherTriggerEvent.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWeatherTriggerEvent.InnerSingleton;
}
// ********** End Enum EWeatherTriggerEvent ********************************************************

// ********** Begin ScriptStruct FAuracronDynamicWeatherConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig;
class UScriptStruct* FAuracronDynamicWeatherConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronDynamicWeatherConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Dynamic weather configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic weather configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherType_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Duration (0 = permanent) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Duration (0 = permanent)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTime_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedLayers_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Affected layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Affected layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherVFX_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather VFX system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather VFX system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherAudio_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather audio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather audio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherEffects_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gameplay effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherTags_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather tags */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather tags" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeatherType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeatherType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Intensity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectedLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectedLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectedLayers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeatherVFX;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeatherAudio;
	static const UECodeGen_Private::FClassPropertyParams NewProp_WeatherEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WeatherEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WeatherTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDynamicWeatherConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherType = { "WeatherType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, WeatherType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherType_MetaData), NewProp_WeatherType_MetaData) }; // 2249285109
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Intensity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, Intensity), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) }; // 2616208733
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, TransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTime_MetaData), NewProp_TransitionTime_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers_Inner = { "AffectedLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers = { "AffectedLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, AffectedLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedLayers_MetaData), NewProp_AffectedLayers_MetaData) }; // 3153537035
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherVFX = { "WeatherVFX", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, WeatherVFX), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherVFX_MetaData), NewProp_WeatherVFX_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherAudio = { "WeatherAudio", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, WeatherAudio), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherAudio_MetaData), NewProp_WeatherAudio_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherEffects_Inner = { "WeatherEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherEffects = { "WeatherEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, WeatherEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherEffects_MetaData), NewProp_WeatherEffects_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherTags = { "WeatherTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicWeatherConfig, WeatherTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherTags_MetaData), NewProp_WeatherTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Intensity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_AffectedLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewProp_WeatherTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronDynamicWeatherConfig",
	Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::PropPointers),
	sizeof(FAuracronDynamicWeatherConfig),
	alignof(FAuracronDynamicWeatherConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDynamicWeatherConfig ***************************************

// ********** Begin ScriptStruct FAuracronWeatherState *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWeatherState;
class UScriptStruct* FAuracronWeatherState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWeatherState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWeatherState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWeatherState, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronWeatherState"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWeatherState.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWeatherState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Weather state data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather state data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWeather_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current weather configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current weather configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherStartTime_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather start time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather start time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition progress (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition progress (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTransitioning_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Is transitioning */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is transitioning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousWeatherType_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Previous weather type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Previous weather type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityLevel_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather activity level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather activity level" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentWeather;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static void NewProp_bIsTransitioning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTransitioning;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreviousWeatherType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreviousWeatherType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWeatherState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_CurrentWeather = { "CurrentWeather", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWeatherState, CurrentWeather), Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWeather_MetaData), NewProp_CurrentWeather_MetaData) }; // 1626599969
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_WeatherStartTime = { "WeatherStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWeatherState, WeatherStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherStartTime_MetaData), NewProp_WeatherStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWeatherState, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_bIsTransitioning_SetBit(void* Obj)
{
	((FAuracronWeatherState*)Obj)->bIsTransitioning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_bIsTransitioning = { "bIsTransitioning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWeatherState), &Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_bIsTransitioning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTransitioning_MetaData), NewProp_bIsTransitioning_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_PreviousWeatherType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_PreviousWeatherType = { "PreviousWeatherType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWeatherState, PreviousWeatherType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousWeatherType_MetaData), NewProp_PreviousWeatherType_MetaData) }; // 2249285109
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_ActivityLevel = { "ActivityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWeatherState, ActivityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityLevel_MetaData), NewProp_ActivityLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_CurrentWeather,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_WeatherStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_bIsTransitioning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_PreviousWeatherType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_PreviousWeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewProp_ActivityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronWeatherState",
	Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::PropPointers),
	sizeof(FAuracronWeatherState),
	alignof(FAuracronWeatherState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWeatherState()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWeatherState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWeatherState.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWeatherState.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWeatherState ***********************************************

// ********** Begin Class UAuracronDynamicWeatherSystem Function ApplyWeatherEffectsToPlayer *******
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics
{
	struct AuracronDynamicWeatherSystem_eventApplyWeatherEffectsToPlayer_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gameplay Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply weather effects to player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply weather effects to player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventApplyWeatherEffectsToPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "ApplyWeatherEffectsToPlayer", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::AuracronDynamicWeatherSystem_eventApplyWeatherEffectsToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::AuracronDynamicWeatherSystem_eventApplyWeatherEffectsToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execApplyWeatherEffectsToPlayer)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyWeatherEffectsToPlayer(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function ApplyWeatherEffectsToPlayer *********

// ********** Begin Class UAuracronDynamicWeatherSystem Function ChangeWeather *********************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics
{
	struct AuracronDynamicWeatherSystem_eventChangeWeather_Parms
	{
		EDynamicWeatherType NewWeatherType;
		EWeatherIntensity Intensity;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Change weather to specific type */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Change weather to specific type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewWeatherType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewWeatherType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Intensity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_NewWeatherType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_NewWeatherType = { "NewWeatherType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventChangeWeather_Parms, NewWeatherType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Intensity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventChangeWeather_Parms, Intensity), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity, METADATA_PARAMS(0, nullptr) }; // 2616208733
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventChangeWeather_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_NewWeatherType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_NewWeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Intensity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "ChangeWeather", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::AuracronDynamicWeatherSystem_eventChangeWeather_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::AuracronDynamicWeatherSystem_eventChangeWeather_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execChangeWeather)
{
	P_GET_ENUM(EDynamicWeatherType,Z_Param_NewWeatherType);
	P_GET_ENUM(EWeatherIntensity,Z_Param_Intensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ChangeWeather(EDynamicWeatherType(Z_Param_NewWeatherType),EWeatherIntensity(Z_Param_Intensity),Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function ChangeWeather ***********************

// ********** Begin Class UAuracronDynamicWeatherSystem Function DoesWeatherAffectLocation *********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics
{
	struct AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gameplay Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if weather affects location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if weather affects location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms), &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "DoesWeatherAffectLocation", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::AuracronDynamicWeatherSystem_eventDoesWeatherAffectLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execDoesWeatherAffectLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesWeatherAffectLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function DoesWeatherAffectLocation ***********

// ********** Begin Class UAuracronDynamicWeatherSystem Function GetCurrentWeatherState ************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics
{
	struct AuracronDynamicWeatherSystem_eventGetCurrentWeatherState_Parms
	{
		FAuracronWeatherState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current weather state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current weather state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventGetCurrentWeatherState_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWeatherState, METADATA_PARAMS(0, nullptr) }; // 3456929087
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "GetCurrentWeatherState", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::AuracronDynamicWeatherSystem_eventGetCurrentWeatherState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::AuracronDynamicWeatherSystem_eventGetCurrentWeatherState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execGetCurrentWeatherState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWeatherState*)Z_Param__Result=P_THIS->GetCurrentWeatherState();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function GetCurrentWeatherState **************

// ********** Begin Class UAuracronDynamicWeatherSystem Function GetLayerWeather *******************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics
{
	struct AuracronDynamicWeatherSystem_eventGetLayerWeather_Parms
	{
		EAuracronRealmLayer Layer;
		FAuracronDynamicWeatherConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get weather for layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get weather for layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventGetLayerWeather_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventGetLayerWeather_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig, METADATA_PARAMS(0, nullptr) }; // 1626599969
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "GetLayerWeather", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::AuracronDynamicWeatherSystem_eventGetLayerWeather_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::AuracronDynamicWeatherSystem_eventGetLayerWeather_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execGetLayerWeather)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDynamicWeatherConfig*)Z_Param__Result=P_THIS->GetLayerWeather(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function GetLayerWeather *********************

// ********** Begin Class UAuracronDynamicWeatherSystem Function GetWeatherIntensityAtLocation *****
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics
{
	struct AuracronDynamicWeatherSystem_eventGetWeatherIntensityAtLocation_Parms
	{
		FVector Location;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gameplay Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get weather intensity at location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get weather intensity at location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventGetWeatherIntensityAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventGetWeatherIntensityAtLocation_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "GetWeatherIntensityAtLocation", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::AuracronDynamicWeatherSystem_eventGetWeatherIntensityAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::AuracronDynamicWeatherSystem_eventGetWeatherIntensityAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execGetWeatherIntensityAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWeatherIntensityAtLocation(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function GetWeatherIntensityAtLocation *******

// ********** Begin Class UAuracronDynamicWeatherSystem Function InitializeWeatherSystem ***********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize dynamic weather system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize dynamic weather system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "InitializeWeatherSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execInitializeWeatherSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWeatherSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function InitializeWeatherSystem *************

// ********** Begin Class UAuracronDynamicWeatherSystem Function OnWeatherChanged ******************
struct AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms
{
	EDynamicWeatherType OldWeather;
	EDynamicWeatherType NewWeather;
};
static FName NAME_UAuracronDynamicWeatherSystem_OnWeatherChanged = FName(TEXT("OnWeatherChanged"));
void UAuracronDynamicWeatherSystem::OnWeatherChanged(EDynamicWeatherType OldWeather, EDynamicWeatherType NewWeather)
{
	AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms Parms;
	Parms.OldWeather=OldWeather;
	Parms.NewWeather=NewWeather;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronDynamicWeatherSystem_OnWeatherChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when weather changes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when weather changes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldWeather_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldWeather;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewWeather_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewWeather;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_OldWeather_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_OldWeather = { "OldWeather", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms, OldWeather), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_NewWeather_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_NewWeather = { "NewWeather", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms, NewWeather), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_OldWeather_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_OldWeather,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_NewWeather_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::NewProp_NewWeather,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "OnWeatherChanged", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::PropPointers), sizeof(AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicWeatherSystem_eventOnWeatherChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronDynamicWeatherSystem Function OnWeatherChanged ********************

// ********** Begin Class UAuracronDynamicWeatherSystem Function OnWeatherEventTriggered ***********
struct AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms
{
	EWeatherTriggerEvent TriggerEvent;
	FVector Location;
};
static FName NAME_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered = FName(TEXT("OnWeatherEventTriggered"));
void UAuracronDynamicWeatherSystem::OnWeatherEventTriggered(EWeatherTriggerEvent TriggerEvent, FVector const& Location)
{
	AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms Parms;
	Parms.TriggerEvent=TriggerEvent;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when weather event is triggered */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when weather event is triggered" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TriggerEvent_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TriggerEvent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_TriggerEvent_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_TriggerEvent = { "TriggerEvent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms, TriggerEvent), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent, METADATA_PARAMS(0, nullptr) }; // 2379718381
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_TriggerEvent_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_TriggerEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "OnWeatherEventTriggered", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::PropPointers), sizeof(AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C20800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicWeatherSystem_eventOnWeatherEventTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronDynamicWeatherSystem Function OnWeatherEventTriggered *************

// ********** Begin Class UAuracronDynamicWeatherSystem Function OnWeatherTransitionComplete *******
struct AuracronDynamicWeatherSystem_eventOnWeatherTransitionComplete_Parms
{
	EDynamicWeatherType WeatherType;
};
static FName NAME_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete = FName(TEXT("OnWeatherTransitionComplete"));
void UAuracronDynamicWeatherSystem::OnWeatherTransitionComplete(EDynamicWeatherType WeatherType)
{
	AuracronDynamicWeatherSystem_eventOnWeatherTransitionComplete_Parms Parms;
	Parms.WeatherType=WeatherType;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when weather transition completes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when weather transition completes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeatherType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeatherType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::NewProp_WeatherType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::NewProp_WeatherType = { "WeatherType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventOnWeatherTransitionComplete_Parms, WeatherType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::NewProp_WeatherType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::NewProp_WeatherType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "OnWeatherTransitionComplete", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::PropPointers), sizeof(AuracronDynamicWeatherSystem_eventOnWeatherTransitionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronDynamicWeatherSystem_eventOnWeatherTransitionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronDynamicWeatherSystem Function OnWeatherTransitionComplete *********

// ********** Begin Class UAuracronDynamicWeatherSystem Function RemoveWeatherEffectsFromPlayer ****
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics
{
	struct AuracronDynamicWeatherSystem_eventRemoveWeatherEffectsFromPlayer_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Gameplay Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove weather effects from player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove weather effects from player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventRemoveWeatherEffectsFromPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "RemoveWeatherEffectsFromPlayer", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::AuracronDynamicWeatherSystem_eventRemoveWeatherEffectsFromPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::AuracronDynamicWeatherSystem_eventRemoveWeatherEffectsFromPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execRemoveWeatherEffectsFromPlayer)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveWeatherEffectsFromPlayer(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function RemoveWeatherEffectsFromPlayer ******

// ********** Begin Class UAuracronDynamicWeatherSystem Function SetLayerWeather *******************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics
{
	struct AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms
	{
		EAuracronRealmLayer Layer;
		EDynamicWeatherType WeatherType;
		EWeatherIntensity Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set weather for specific layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set weather for specific layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeatherType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeatherType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Intensity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_WeatherType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_WeatherType = { "WeatherType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms, WeatherType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Intensity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms, Intensity), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherIntensity, METADATA_PARAMS(0, nullptr) }; // 2616208733
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_WeatherType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_WeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Intensity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "SetLayerWeather", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::AuracronDynamicWeatherSystem_eventSetLayerWeather_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execSetLayerWeather)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_ENUM(EDynamicWeatherType,Z_Param_WeatherType);
	P_GET_ENUM(EWeatherIntensity,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerWeather(EAuracronRealmLayer(Z_Param_Layer),EDynamicWeatherType(Z_Param_WeatherType),EWeatherIntensity(Z_Param_Intensity));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function SetLayerWeather *********************

// ********** Begin Class UAuracronDynamicWeatherSystem Function SetWeatherSystemEnabled ***********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics
{
	struct AuracronDynamicWeatherSystem_eventSetWeatherSystemEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable/disable dynamic weather */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/disable dynamic weather" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronDynamicWeatherSystem_eventSetWeatherSystemEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicWeatherSystem_eventSetWeatherSystemEnabled_Parms), &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "SetWeatherSystemEnabled", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::AuracronDynamicWeatherSystem_eventSetWeatherSystemEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::AuracronDynamicWeatherSystem_eventSetWeatherSystemEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execSetWeatherSystemEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWeatherSystemEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function SetWeatherSystemEnabled *************

// ********** Begin Class UAuracronDynamicWeatherSystem Function SetWeatherTransitionSpeed *********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics
{
	struct AuracronDynamicWeatherSystem_eventSetWeatherTransitionSpeed_Parms
	{
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set weather transition speed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set weather transition speed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventSetWeatherTransitionSpeed_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "SetWeatherTransitionSpeed", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::AuracronDynamicWeatherSystem_eventSetWeatherTransitionSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::AuracronDynamicWeatherSystem_eventSetWeatherTransitionSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execSetWeatherTransitionSpeed)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWeatherTransitionSpeed(Z_Param_Speed);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function SetWeatherTransitionSpeed ***********

// ********** Begin Class UAuracronDynamicWeatherSystem Function SetWeatherUpdateFrequency *********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics
{
	struct AuracronDynamicWeatherSystem_eventSetWeatherUpdateFrequency_Parms
	{
		float Frequency;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weather Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set weather update frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set weather update frequency" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventSetWeatherUpdateFrequency_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::NewProp_Frequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "SetWeatherUpdateFrequency", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::AuracronDynamicWeatherSystem_eventSetWeatherUpdateFrequency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::AuracronDynamicWeatherSystem_eventSetWeatherUpdateFrequency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execSetWeatherUpdateFrequency)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetWeatherUpdateFrequency(Z_Param_Frequency);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function SetWeatherUpdateFrequency ***********

// ********** Begin Class UAuracronDynamicWeatherSystem Function TriggerWeatherEvent ***************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics
{
	struct AuracronDynamicWeatherSystem_eventTriggerWeatherEvent_Parms
	{
		EWeatherTriggerEvent TriggerEvent;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Trigger weather event */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Trigger weather event" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TriggerEvent_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TriggerEvent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_TriggerEvent_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_TriggerEvent = { "TriggerEvent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventTriggerWeatherEvent_Parms, TriggerEvent), Z_Construct_UEnum_AuracronDynamicRealmBridge_EWeatherTriggerEvent, METADATA_PARAMS(0, nullptr) }; // 2379718381
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventTriggerWeatherEvent_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_TriggerEvent_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_TriggerEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "TriggerWeatherEvent", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::AuracronDynamicWeatherSystem_eventTriggerWeatherEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::AuracronDynamicWeatherSystem_eventTriggerWeatherEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execTriggerWeatherEvent)
{
	P_GET_ENUM(EWeatherTriggerEvent,Z_Param_TriggerEvent);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerWeatherEvent(EWeatherTriggerEvent(Z_Param_TriggerEvent),Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function TriggerWeatherEvent *****************

// ********** Begin Class UAuracronDynamicWeatherSystem Function UpdateLayerWeatherEffects *********
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics
{
	struct AuracronDynamicWeatherSystem_eventUpdateLayerWeatherEffects_Parms
	{
		EAuracronRealmLayer Layer;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update layer weather effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update layer weather effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventUpdateLayerWeatherEffects_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventUpdateLayerWeatherEffects_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "UpdateLayerWeatherEffects", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::AuracronDynamicWeatherSystem_eventUpdateLayerWeatherEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::AuracronDynamicWeatherSystem_eventUpdateLayerWeatherEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execUpdateLayerWeatherEffects)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLayerWeatherEffects(EAuracronRealmLayer(Z_Param_Layer),Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function UpdateLayerWeatherEffects ***********

// ********** Begin Class UAuracronDynamicWeatherSystem Function UpdateWeatherSystem ***************
struct Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics
{
	struct AuracronDynamicWeatherSystem_eventUpdateWeatherSystem_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update weather system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update weather system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicWeatherSystem_eventUpdateWeatherSystem_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicWeatherSystem, nullptr, "UpdateWeatherSystem", Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::AuracronDynamicWeatherSystem_eventUpdateWeatherSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::AuracronDynamicWeatherSystem_eventUpdateWeatherSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicWeatherSystem::execUpdateWeatherSystem)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateWeatherSystem(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicWeatherSystem Function UpdateWeatherSystem *****************

// ********** Begin Class UAuracronDynamicWeatherSystem ********************************************
void UAuracronDynamicWeatherSystem::StaticRegisterNativesUAuracronDynamicWeatherSystem()
{
	UClass* Class = UAuracronDynamicWeatherSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyWeatherEffectsToPlayer", &UAuracronDynamicWeatherSystem::execApplyWeatherEffectsToPlayer },
		{ "ChangeWeather", &UAuracronDynamicWeatherSystem::execChangeWeather },
		{ "DoesWeatherAffectLocation", &UAuracronDynamicWeatherSystem::execDoesWeatherAffectLocation },
		{ "GetCurrentWeatherState", &UAuracronDynamicWeatherSystem::execGetCurrentWeatherState },
		{ "GetLayerWeather", &UAuracronDynamicWeatherSystem::execGetLayerWeather },
		{ "GetWeatherIntensityAtLocation", &UAuracronDynamicWeatherSystem::execGetWeatherIntensityAtLocation },
		{ "InitializeWeatherSystem", &UAuracronDynamicWeatherSystem::execInitializeWeatherSystem },
		{ "RemoveWeatherEffectsFromPlayer", &UAuracronDynamicWeatherSystem::execRemoveWeatherEffectsFromPlayer },
		{ "SetLayerWeather", &UAuracronDynamicWeatherSystem::execSetLayerWeather },
		{ "SetWeatherSystemEnabled", &UAuracronDynamicWeatherSystem::execSetWeatherSystemEnabled },
		{ "SetWeatherTransitionSpeed", &UAuracronDynamicWeatherSystem::execSetWeatherTransitionSpeed },
		{ "SetWeatherUpdateFrequency", &UAuracronDynamicWeatherSystem::execSetWeatherUpdateFrequency },
		{ "TriggerWeatherEvent", &UAuracronDynamicWeatherSystem::execTriggerWeatherEvent },
		{ "UpdateLayerWeatherEffects", &UAuracronDynamicWeatherSystem::execUpdateLayerWeatherEffects },
		{ "UpdateWeatherSystem", &UAuracronDynamicWeatherSystem::execUpdateWeatherSystem },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem;
UClass* UAuracronDynamicWeatherSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronDynamicWeatherSystem;
	if (!Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronDynamicWeatherSystem"),
			Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.InnerSingleton,
			StaticRegisterNativesUAuracronDynamicWeatherSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem_NoRegister()
{
	return UAuracronDynamicWeatherSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Dynamic Weather System\n * \n * Advanced weather system that creates dynamic, responsive weather effects\n * based on gameplay events, player actions, and environmental conditions.\n * Integrates with the realm evolution and flow systems for immersive experiences.\n */" },
#endif
		{ "IncludePath", "AuracronDynamicWeatherSystem.h" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Dynamic Weather System\n\nAdvanced weather system that creates dynamic, responsive weather effects\nbased on gameplay events, player actions, and environmental conditions.\nIntegrates with the realm evolution and flow systems for immersive experiences." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWeatherSystemEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable weather system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable weather system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherUpdateFrequency_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather update frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather update frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherTransitionSpeed_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather transition speed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather transition speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGameplayEffects_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable gameplay weather effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable gameplay weather effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceOptimization_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalWeatherState_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current global weather state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current global weather state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerWeatherStates_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer-specific weather states */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer-specific weather states" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherConfigurations_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather configurations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveWeatherVFX_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active weather VFX components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active weather VFX components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveWeatherAudio_MetaData[] = {
		{ "Category", "Weather State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active weather audio components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active weather audio components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedSunLight_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedSkyLight_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedFog_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicWeatherSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bWeatherSystemEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWeatherSystemEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherUpdateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherTransitionSpeed;
	static void NewProp_bEnableGameplayEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGameplayEffects;
	static void NewProp_bEnablePerformanceOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceOptimization;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GlobalWeatherState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerWeatherStates_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerWeatherStates_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerWeatherStates_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerWeatherStates;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WeatherConfigurations_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeatherConfigurations_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeatherConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_WeatherConfigurations;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveWeatherVFX_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActiveWeatherVFX_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActiveWeatherVFX_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveWeatherVFX;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveWeatherAudio_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActiveWeatherAudio_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActiveWeatherAudio_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveWeatherAudio;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedSunLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedSkyLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedFog;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ApplyWeatherEffectsToPlayer, "ApplyWeatherEffectsToPlayer" }, // 2091228371
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_ChangeWeather, "ChangeWeather" }, // 4288840855
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_DoesWeatherAffectLocation, "DoesWeatherAffectLocation" }, // 1438868467
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetCurrentWeatherState, "GetCurrentWeatherState" }, // 996945087
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetLayerWeather, "GetLayerWeather" }, // 3360326531
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_GetWeatherIntensityAtLocation, "GetWeatherIntensityAtLocation" }, // 2037136837
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_InitializeWeatherSystem, "InitializeWeatherSystem" }, // 862462566
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherChanged, "OnWeatherChanged" }, // 2177047174
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherEventTriggered, "OnWeatherEventTriggered" }, // 3617015661
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_OnWeatherTransitionComplete, "OnWeatherTransitionComplete" }, // 2398531837
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_RemoveWeatherEffectsFromPlayer, "RemoveWeatherEffectsFromPlayer" }, // 4136914882
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetLayerWeather, "SetLayerWeather" }, // 170759730
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherSystemEnabled, "SetWeatherSystemEnabled" }, // 2547214147
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherTransitionSpeed, "SetWeatherTransitionSpeed" }, // 3786432449
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_SetWeatherUpdateFrequency, "SetWeatherUpdateFrequency" }, // 2880535134
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_TriggerWeatherEvent, "TriggerWeatherEvent" }, // 3659708826
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateLayerWeatherEffects, "UpdateLayerWeatherEffects" }, // 2206507314
		{ &Z_Construct_UFunction_UAuracronDynamicWeatherSystem_UpdateWeatherSystem, "UpdateWeatherSystem" }, // 2833772477
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronDynamicWeatherSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bWeatherSystemEnabled_SetBit(void* Obj)
{
	((UAuracronDynamicWeatherSystem*)Obj)->bWeatherSystemEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bWeatherSystemEnabled = { "bWeatherSystemEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicWeatherSystem), &Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bWeatherSystemEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWeatherSystemEnabled_MetaData), NewProp_bWeatherSystemEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherUpdateFrequency = { "WeatherUpdateFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, WeatherUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherUpdateFrequency_MetaData), NewProp_WeatherUpdateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherTransitionSpeed = { "WeatherTransitionSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, WeatherTransitionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherTransitionSpeed_MetaData), NewProp_WeatherTransitionSpeed_MetaData) };
void Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnableGameplayEffects_SetBit(void* Obj)
{
	((UAuracronDynamicWeatherSystem*)Obj)->bEnableGameplayEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnableGameplayEffects = { "bEnableGameplayEffects", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicWeatherSystem), &Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnableGameplayEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGameplayEffects_MetaData), NewProp_bEnableGameplayEffects_MetaData) };
void Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnablePerformanceOptimization_SetBit(void* Obj)
{
	((UAuracronDynamicWeatherSystem*)Obj)->bEnablePerformanceOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnablePerformanceOptimization = { "bEnablePerformanceOptimization", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicWeatherSystem), &Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnablePerformanceOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceOptimization_MetaData), NewProp_bEnablePerformanceOptimization_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_GlobalWeatherState = { "GlobalWeatherState", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, GlobalWeatherState), Z_Construct_UScriptStruct_FAuracronWeatherState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalWeatherState_MetaData), NewProp_GlobalWeatherState_MetaData) }; // 3456929087
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_ValueProp = { "LayerWeatherStates", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronWeatherState, METADATA_PARAMS(0, nullptr) }; // 3456929087
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_Key_KeyProp = { "LayerWeatherStates_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates = { "LayerWeatherStates", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, LayerWeatherStates), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerWeatherStates_MetaData), NewProp_LayerWeatherStates_MetaData) }; // 3153537035 3456929087
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_ValueProp = { "WeatherConfigurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig, METADATA_PARAMS(0, nullptr) }; // 1626599969
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_Key_KeyProp = { "WeatherConfigurations_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EDynamicWeatherType, METADATA_PARAMS(0, nullptr) }; // 2249285109
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations = { "WeatherConfigurations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, WeatherConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherConfigurations_MetaData), NewProp_WeatherConfigurations_MetaData) }; // 2249285109 1626599969
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_ValueProp = { "ActiveWeatherVFX", nullptr, (EPropertyFlags)0x0104000000080009, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_Key_KeyProp = { "ActiveWeatherVFX_Key", nullptr, (EPropertyFlags)0x0100000000080009, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX = { "ActiveWeatherVFX", nullptr, (EPropertyFlags)0x012408800000000d, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, ActiveWeatherVFX), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveWeatherVFX_MetaData), NewProp_ActiveWeatherVFX_MetaData) }; // 3153537035
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_ValueProp = { "ActiveWeatherAudio", nullptr, (EPropertyFlags)0x0104000000080009, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_Key_KeyProp = { "ActiveWeatherAudio_Key", nullptr, (EPropertyFlags)0x0100000000080009, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio = { "ActiveWeatherAudio", nullptr, (EPropertyFlags)0x012408800000000d, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, ActiveWeatherAudio), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveWeatherAudio_MetaData), NewProp_ActiveWeatherAudio_MetaData) }; // 3153537035
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedSunLight = { "CachedSunLight", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, CachedSunLight), Z_Construct_UClass_ADirectionalLight_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedSunLight_MetaData), NewProp_CachedSunLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedSkyLight = { "CachedSkyLight", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, CachedSkyLight), Z_Construct_UClass_ASkyLight_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedSkyLight_MetaData), NewProp_CachedSkyLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedFog = { "CachedFog", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicWeatherSystem, CachedFog), Z_Construct_UClass_AExponentialHeightFog_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedFog_MetaData), NewProp_CachedFog_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bWeatherSystemEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherTransitionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnableGameplayEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_bEnablePerformanceOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_GlobalWeatherState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_LayerWeatherStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_WeatherConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_ActiveWeatherAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedRealmSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedSunLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedSkyLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::NewProp_CachedFog,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::ClassParams = {
	&UAuracronDynamicWeatherSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.OuterSingleton, Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem.OuterSingleton;
}
UAuracronDynamicWeatherSystem::UAuracronDynamicWeatherSystem() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronDynamicWeatherSystem);
UAuracronDynamicWeatherSystem::~UAuracronDynamicWeatherSystem() {}
// ********** End Class UAuracronDynamicWeatherSystem **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EDynamicWeatherType_StaticEnum, TEXT("EDynamicWeatherType"), &Z_Registration_Info_UEnum_EDynamicWeatherType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2249285109U) },
		{ EWeatherIntensity_StaticEnum, TEXT("EWeatherIntensity"), &Z_Registration_Info_UEnum_EWeatherIntensity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2616208733U) },
		{ EWeatherTriggerEvent_StaticEnum, TEXT("EWeatherTriggerEvent"), &Z_Registration_Info_UEnum_EWeatherTriggerEvent, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2379718381U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDynamicWeatherConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics::NewStructOps, TEXT("AuracronDynamicWeatherConfig"), &Z_Registration_Info_UScriptStruct_FAuracronDynamicWeatherConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDynamicWeatherConfig), 1626599969U) },
		{ FAuracronWeatherState::StaticStruct, Z_Construct_UScriptStruct_FAuracronWeatherState_Statics::NewStructOps, TEXT("AuracronWeatherState"), &Z_Registration_Info_UScriptStruct_FAuracronWeatherState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWeatherState), 3456929087U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronDynamicWeatherSystem, UAuracronDynamicWeatherSystem::StaticClass, TEXT("UAuracronDynamicWeatherSystem"), &Z_Registration_Info_UClass_UAuracronDynamicWeatherSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronDynamicWeatherSystem), 1642404598U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_1091600782(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
