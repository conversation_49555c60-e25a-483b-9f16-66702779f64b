// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronSigilEffects.h"

#ifdef AURACRONSIGILOSBRIDGE_AuracronSigilEffects_generated_h
#error "AuracronSigilEffects.generated.h already included, missing '#pragma once' in AuracronSigilEffects.h"
#endif
#define AURACRONSIGILOSBRIDGE_AuracronSigilEffects_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"
#include "Net/Core/PushModel/PushModelMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

struct FGameplayAttributeData;

// ********** Begin Class UAuracronSigilAttributeSet ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_TeleportRange); \
	DECLARE_FUNCTION(execOnRep_VisionRange); \
	DECLARE_FUNCTION(execOnRep_MovementSpeed); \
	DECLARE_FUNCTION(execOnRep_HealingPower); \
	DECLARE_FUNCTION(execOnRep_CriticalChance); \
	DECLARE_FUNCTION(execOnRep_ArmorPenetration); \
	DECLARE_FUNCTION(execOnRep_ElementalDamage); \
	DECLARE_FUNCTION(execOnRep_TimeDistortion); \
	DECLARE_FUNCTION(execOnRep_DamageReflection); \
	DECLARE_FUNCTION(execOnRep_ShieldStrength); \
	DECLARE_FUNCTION(execOnRep_ArchetypeLevel); \
	DECLARE_FUNCTION(execOnRep_FusionEnergy); \
	DECLARE_FUNCTION(execOnRep_SigilCooldownReduction); \
	DECLARE_FUNCTION(execOnRep_SigilDuration); \
	DECLARE_FUNCTION(execOnRep_SigilPower);


AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAttributeSet_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronSigilAttributeSet(); \
	friend struct Z_Construct_UClass_UAuracronSigilAttributeSet_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAttributeSet_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronSigilAttributeSet, UAttributeSet, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronSigilosBridge"), Z_Construct_UClass_UAuracronSigilAttributeSet_NoRegister) \
	DECLARE_SERIALIZER(UAuracronSigilAttributeSet) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		SigilPower=NETFIELD_REP_START, \
		SigilDuration, \
		SigilCooldownReduction, \
		FusionEnergy, \
		ArchetypeLevel, \
		ShieldStrength, \
		DamageReflection, \
		TimeDistortion, \
		ElementalDamage, \
		ArmorPenetration, \
		CriticalChance, \
		HealingPower, \
		MovementSpeed, \
		VisionRange, \
		TeleportRange, \
		NETFIELD_REP_END=TeleportRange	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API) \
private: \
	REPLICATED_BASE_CLASS(UAuracronSigilAttributeSet) \
public:


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronSigilAttributeSet(UAuracronSigilAttributeSet&&) = delete; \
	UAuracronSigilAttributeSet(const UAuracronSigilAttributeSet&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronSigilAttributeSet); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronSigilAttributeSet); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronSigilAttributeSet) \
	NO_API virtual ~UAuracronSigilAttributeSet();


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_29_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_32_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronSigilAttributeSet;

// ********** End Class UAuracronSigilAttributeSet *************************************************

// ********** Begin Class UAuracronSigilAbility ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execApplySigilEffect);


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_CALLBACK_WRAPPERS
AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAbility_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronSigilAbility(); \
	friend struct Z_Construct_UClass_UAuracronSigilAbility_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONSIGILOSBRIDGE_API UClass* Z_Construct_UClass_UAuracronSigilAbility_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronSigilAbility, UGameplayAbility, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AuracronSigilosBridge"), Z_Construct_UClass_UAuracronSigilAbility_NoRegister) \
	DECLARE_SERIALIZER(UAuracronSigilAbility)


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronSigilAbility(UAuracronSigilAbility&&) = delete; \
	UAuracronSigilAbility(const UAuracronSigilAbility&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronSigilAbility); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronSigilAbility); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(UAuracronSigilAbility) \
	NO_API virtual ~UAuracronSigilAbility();


#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_161_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h_164_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronSigilAbility;

// ********** End Class UAuracronSigilAbility ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronSigilosBridge_Public_AuracronSigilEffects_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
