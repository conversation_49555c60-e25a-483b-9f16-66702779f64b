// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "HarmonyRewardsSystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeHarmonyRewardsSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyRewardsSystem();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyRewardsSystem_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FKindnessReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerRewardProgress();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERewardTier ***************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERewardTier;
static UEnum* ERewardTier_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERewardTier.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERewardTier.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("ERewardTier"));
	}
	return Z_Registration_Info_UEnum_ERewardTier.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<ERewardTier>()
{
	return ERewardTier_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bronze.DisplayName", "Bronze" },
		{ "Bronze.Name", "ERewardTier::Bronze" },
		{ "Diamond.DisplayName", "Diamond" },
		{ "Diamond.Name", "ERewardTier::Diamond" },
		{ "Gold.DisplayName", "Gold" },
		{ "Gold.Name", "ERewardTier::Gold" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "ERewardTier::Legendary" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
		{ "Platinum.DisplayName", "Platinum" },
		{ "Platinum.Name", "ERewardTier::Platinum" },
		{ "Silver.DisplayName", "Silver" },
		{ "Silver.Name", "ERewardTier::Silver" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERewardTier::Bronze", (int64)ERewardTier::Bronze },
		{ "ERewardTier::Silver", (int64)ERewardTier::Silver },
		{ "ERewardTier::Gold", (int64)ERewardTier::Gold },
		{ "ERewardTier::Platinum", (int64)ERewardTier::Platinum },
		{ "ERewardTier::Diamond", (int64)ERewardTier::Diamond },
		{ "ERewardTier::Legendary", (int64)ERewardTier::Legendary },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"ERewardTier",
	"ERewardTier",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier()
{
	if (!Z_Registration_Info_UEnum_ERewardTier.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERewardTier.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERewardTier.InnerSingleton;
}
// ********** End Enum ERewardTier *****************************************************************

// ********** Begin Enum ERewardCategory ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERewardCategory;
static UEnum* ERewardCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERewardCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERewardCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("ERewardCategory"));
	}
	return Z_Registration_Info_UEnum_ERewardCategory.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<ERewardCategory>()
{
	return ERewardCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Consistency.DisplayName", "Consistency" },
		{ "Consistency.Name", "ERewardCategory::Consistency" },
		{ "Healing.DisplayName", "Community Healing" },
		{ "Healing.Name", "ERewardCategory::Healing" },
		{ "Innovation.DisplayName", "Innovation" },
		{ "Innovation.Name", "ERewardCategory::Innovation" },
		{ "Kindness.DisplayName", "Kindness" },
		{ "Kindness.Name", "ERewardCategory::Kindness" },
		{ "Leadership.DisplayName", "Leadership" },
		{ "Leadership.Name", "ERewardCategory::Leadership" },
		{ "Mentorship.DisplayName", "Mentorship" },
		{ "Mentorship.Name", "ERewardCategory::Mentorship" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
		{ "Resilience.DisplayName", "Resilience" },
		{ "Resilience.Name", "ERewardCategory::Resilience" },
		{ "Teamwork.DisplayName", "Teamwork" },
		{ "Teamwork.Name", "ERewardCategory::Teamwork" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERewardCategory::Kindness", (int64)ERewardCategory::Kindness },
		{ "ERewardCategory::Mentorship", (int64)ERewardCategory::Mentorship },
		{ "ERewardCategory::Leadership", (int64)ERewardCategory::Leadership },
		{ "ERewardCategory::Healing", (int64)ERewardCategory::Healing },
		{ "ERewardCategory::Consistency", (int64)ERewardCategory::Consistency },
		{ "ERewardCategory::Innovation", (int64)ERewardCategory::Innovation },
		{ "ERewardCategory::Teamwork", (int64)ERewardCategory::Teamwork },
		{ "ERewardCategory::Resilience", (int64)ERewardCategory::Resilience },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"ERewardCategory",
	"ERewardCategory",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory()
{
	if (!Z_Registration_Info_UEnum_ERewardCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERewardCategory.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERewardCategory.InnerSingleton;
}
// ********** End Enum ERewardCategory *************************************************************

// ********** Begin ScriptStruct FHarmonyReward ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHarmonyReward;
class UScriptStruct* FHarmonyReward::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHarmonyReward.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHarmonyReward.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHarmonyReward, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("HarmonyReward"));
	}
	return Z_Registration_Info_UScriptStruct_FHarmonyReward.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHarmonyReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tier_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardName_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KindnessPointsRequired_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceBonus_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrencyBonus_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardGameplayEffect_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedAbilities_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardTags_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOneTimeReward_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "HarmonyReward" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Tier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Tier;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FIntPropertyParams NewProp_KindnessPointsRequired;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperienceBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrencyBonus;
	static const UECodeGen_Private::FClassPropertyParams NewProp_RewardGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_UnlockedAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedAbilities;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RewardTags;
	static void NewProp_bIsOneTimeReward_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOneTimeReward;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHarmonyReward>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, Category), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 2605410775
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Tier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Tier = { "Tier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, Tier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tier_MetaData), NewProp_Tier_MetaData) }; // 711007694
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardName = { "RewardName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, RewardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardName_MetaData), NewProp_RewardName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_KindnessPointsRequired = { "KindnessPointsRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, KindnessPointsRequired), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KindnessPointsRequired_MetaData), NewProp_KindnessPointsRequired_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_ExperienceBonus = { "ExperienceBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, ExperienceBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceBonus_MetaData), NewProp_ExperienceBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_CurrencyBonus = { "CurrencyBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, CurrencyBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrencyBonus_MetaData), NewProp_CurrencyBonus_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardGameplayEffect = { "RewardGameplayEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, RewardGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardGameplayEffect_MetaData), NewProp_RewardGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_UnlockedAbilities_Inner = { "UnlockedAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_UnlockedAbilities = { "UnlockedAbilities", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, UnlockedAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedAbilities_MetaData), NewProp_UnlockedAbilities_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardTags = { "RewardTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyReward, RewardTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardTags_MetaData), NewProp_RewardTags_MetaData) }; // 2104890724
void Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsOneTimeReward_SetBit(void* Obj)
{
	((FHarmonyReward*)Obj)->bIsOneTimeReward = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsOneTimeReward = { "bIsOneTimeReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHarmonyReward), &Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsOneTimeReward_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOneTimeReward_MetaData), NewProp_bIsOneTimeReward_MetaData) };
void Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FHarmonyReward*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHarmonyReward), &Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHarmonyReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Tier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Tier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_KindnessPointsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_ExperienceBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_CurrencyBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_UnlockedAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_UnlockedAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_RewardTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsOneTimeReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewProp_bIsVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHarmonyReward_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"HarmonyReward",
	Z_Construct_UScriptStruct_FHarmonyReward_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyReward_Statics::PropPointers),
	sizeof(FHarmonyReward),
	alignof(FHarmonyReward),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyReward_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHarmonyReward_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHarmonyReward()
{
	if (!Z_Registration_Info_UScriptStruct_FHarmonyReward.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHarmonyReward.InnerSingleton, Z_Construct_UScriptStruct_FHarmonyReward_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHarmonyReward.InnerSingleton;
}
// ********** End ScriptStruct FHarmonyReward ******************************************************

// ********** Begin ScriptStruct FPlayerRewardProgress *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerRewardProgress;
class UScriptStruct* FPlayerRewardProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerRewardProgress, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("PlayerRewardProgress"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalKindnessPoints_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedRewards_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CategoryProgress_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTier_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TierProgress_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastRewardTime_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardsThisSession_MetaData[] = {
		{ "Category", "PlayerRewardProgress" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalKindnessPoints;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UnlockedRewards_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedRewards;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CategoryProgress_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CategoryProgress_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CategoryProgress_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CategoryProgress;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TierProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastRewardTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewardsThisSession;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerRewardProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_TotalKindnessPoints = { "TotalKindnessPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, TotalKindnessPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalKindnessPoints_MetaData), NewProp_TotalKindnessPoints_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_UnlockedRewards_Inner = { "UnlockedRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_UnlockedRewards = { "UnlockedRewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, UnlockedRewards), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedRewards_MetaData), NewProp_UnlockedRewards_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_ValueProp = { "CategoryProgress", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_Key_KeyProp = { "CategoryProgress_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory, METADATA_PARAMS(0, nullptr) }; // 2605410775
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress = { "CategoryProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, CategoryProgress), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CategoryProgress_MetaData), NewProp_CategoryProgress_MetaData) }; // 2605410775
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CurrentTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CurrentTier = { "CurrentTier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, CurrentTier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTier_MetaData), NewProp_CurrentTier_MetaData) }; // 711007694
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_TierProgress = { "TierProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, TierProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TierProgress_MetaData), NewProp_TierProgress_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_LastRewardTime = { "LastRewardTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, LastRewardTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastRewardTime_MetaData), NewProp_LastRewardTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_RewardsThisSession = { "RewardsThisSession", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRewardProgress, RewardsThisSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardsThisSession_MetaData), NewProp_RewardsThisSession_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_TotalKindnessPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_UnlockedRewards_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_UnlockedRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CategoryProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CurrentTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_CurrentTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_TierProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_LastRewardTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewProp_RewardsThisSession,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"PlayerRewardProgress",
	Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::PropPointers),
	sizeof(FPlayerRewardProgress),
	alignof(FPlayerRewardProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerRewardProgress()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.InnerSingleton, Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRewardProgress.InnerSingleton;
}
// ********** End ScriptStruct FPlayerRewardProgress ***********************************************

// ********** Begin Class UHarmonyRewardsSystem Function ApplyCurrencyBonus ************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics
{
	struct HarmonyRewardsSystem_eventApplyCurrencyBonus_Parms
	{
		FString PlayerID;
		float CurrencyBonus;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Currency and Progression\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Currency and Progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrencyBonus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventApplyCurrencyBonus_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::NewProp_CurrencyBonus = { "CurrencyBonus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventApplyCurrencyBonus_Parms, CurrencyBonus), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::NewProp_CurrencyBonus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "ApplyCurrencyBonus", Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::HarmonyRewardsSystem_eventApplyCurrencyBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::HarmonyRewardsSystem_eventApplyCurrencyBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execApplyCurrencyBonus)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CurrencyBonus);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyCurrencyBonus(Z_Param_PlayerID,Z_Param_CurrencyBonus);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function ApplyCurrencyBonus **************************

// ********** Begin Class UHarmonyRewardsSystem Function ApplyExperienceBonus **********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics
{
	struct HarmonyRewardsSystem_eventApplyExperienceBonus_Parms
	{
		FString PlayerID;
		float BonusMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BonusMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventApplyExperienceBonus_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::NewProp_BonusMultiplier = { "BonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventApplyExperienceBonus_Parms, BonusMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::NewProp_BonusMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "ApplyExperienceBonus", Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::HarmonyRewardsSystem_eventApplyExperienceBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::HarmonyRewardsSystem_eventApplyExperienceBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execApplyExperienceBonus)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BonusMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyExperienceBonus(Z_Param_PlayerID,Z_Param_BonusMultiplier);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function ApplyExperienceBonus ************************

// ********** Begin Class UHarmonyRewardsSystem Function CalculatePlayerTier ***********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics
{
	struct HarmonyRewardsSystem_eventCalculatePlayerTier_Parms
	{
		int32 TotalKindnessPoints;
		ERewardTier ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalKindnessPoints;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_TotalKindnessPoints = { "TotalKindnessPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculatePlayerTier_Parms, TotalKindnessPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculatePlayerTier_Parms, ReturnValue), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_TotalKindnessPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "CalculatePlayerTier", Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::HarmonyRewardsSystem_eventCalculatePlayerTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::HarmonyRewardsSystem_eventCalculatePlayerTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execCalculatePlayerTier)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TotalKindnessPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERewardTier*)Z_Param__Result=P_THIS->CalculatePlayerTier(Z_Param_TotalKindnessPoints);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function CalculatePlayerTier *************************

// ********** Begin Class UHarmonyRewardsSystem Function CalculateTierCurrencyBonus ****************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics
{
	struct HarmonyRewardsSystem_eventCalculateTierCurrencyBonus_Parms
	{
		ERewardTier Tier;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Tier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Tier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_Tier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_Tier = { "Tier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculateTierCurrencyBonus_Parms, Tier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculateTierCurrencyBonus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_Tier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_Tier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "CalculateTierCurrencyBonus", Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::HarmonyRewardsSystem_eventCalculateTierCurrencyBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::HarmonyRewardsSystem_eventCalculateTierCurrencyBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execCalculateTierCurrencyBonus)
{
	P_GET_ENUM(ERewardTier,Z_Param_Tier);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTierCurrencyBonus(ERewardTier(Z_Param_Tier));
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function CalculateTierCurrencyBonus ******************

// ********** Begin Class UHarmonyRewardsSystem Function CalculateTierExperienceBonus **************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics
{
	struct HarmonyRewardsSystem_eventCalculateTierExperienceBonus_Parms
	{
		ERewardTier Tier;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Tier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Tier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_Tier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_Tier = { "Tier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculateTierExperienceBonus_Parms, Tier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventCalculateTierExperienceBonus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_Tier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_Tier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "CalculateTierExperienceBonus", Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::HarmonyRewardsSystem_eventCalculateTierExperienceBonus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::HarmonyRewardsSystem_eventCalculateTierExperienceBonus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execCalculateTierExperienceBonus)
{
	P_GET_ENUM(ERewardTier,Z_Param_Tier);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTierExperienceBonus(ERewardTier(Z_Param_Tier));
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function CalculateTierExperienceBonus ****************

// ********** Begin Class UHarmonyRewardsSystem Function GetAvailableRewards ***********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics
{
	struct HarmonyRewardsSystem_eventGetAvailableRewards_Parms
	{
		FString PlayerID;
		TArray<FHarmonyReward> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetAvailableRewards_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetAvailableRewards_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetAvailableRewards", Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::HarmonyRewardsSystem_eventGetAvailableRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::HarmonyRewardsSystem_eventGetAvailableRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetAvailableRewards)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FHarmonyReward>*)Z_Param__Result=P_THIS->GetAvailableRewards(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetAvailableRewards *************************

// ********** Begin Class UHarmonyRewardsSystem Function GetCurrentEventMultiplier *****************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics
{
	struct HarmonyRewardsSystem_eventGetCurrentEventMultiplier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetCurrentEventMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetCurrentEventMultiplier", Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::HarmonyRewardsSystem_eventGetCurrentEventMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::HarmonyRewardsSystem_eventGetCurrentEventMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetCurrentEventMultiplier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentEventMultiplier();
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetCurrentEventMultiplier *******************

// ********** Begin Class UHarmonyRewardsSystem Function GetNextTier *******************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics
{
	struct HarmonyRewardsSystem_eventGetNextTier_Parms
	{
		ERewardTier CurrentTier;
		ERewardTier ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTier;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_CurrentTier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_CurrentTier = { "CurrentTier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetNextTier_Parms, CurrentTier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetNextTier_Parms, ReturnValue), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_CurrentTier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_CurrentTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetNextTier", Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::HarmonyRewardsSystem_eventGetNextTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::HarmonyRewardsSystem_eventGetNextTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetNextTier)
{
	P_GET_ENUM(ERewardTier,Z_Param_CurrentTier);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERewardTier*)Z_Param__Result=P_THIS->GetNextTier(ERewardTier(Z_Param_CurrentTier));
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetNextTier *********************************

// ********** Begin Class UHarmonyRewardsSystem Function GetPlayerProgress *************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics
{
	struct HarmonyRewardsSystem_eventGetPlayerProgress_Parms
	{
		FString PlayerID;
		FPlayerRewardProgress ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Progress Tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress Tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetPlayerProgress_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetPlayerProgress_Parms, ReturnValue), Z_Construct_UScriptStruct_FPlayerRewardProgress, METADATA_PARAMS(0, nullptr) }; // 2978250964
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetPlayerProgress", Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::HarmonyRewardsSystem_eventGetPlayerProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::HarmonyRewardsSystem_eventGetPlayerProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetPlayerProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPlayerRewardProgress*)Z_Param__Result=P_THIS->GetPlayerProgress(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetPlayerProgress ***************************

// ********** Begin Class UHarmonyRewardsSystem Function GetProgressToNextTier *********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics
{
	struct HarmonyRewardsSystem_eventGetProgressToNextTier_Parms
	{
		FString PlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetProgressToNextTier_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetProgressToNextTier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetProgressToNextTier", Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::HarmonyRewardsSystem_eventGetProgressToNextTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::HarmonyRewardsSystem_eventGetProgressToNextTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetProgressToNextTier)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetProgressToNextTier(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetProgressToNextTier ***********************

// ********** Begin Class UHarmonyRewardsSystem Function GetReward *********************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics
{
	struct HarmonyRewardsSystem_eventGetReward_Parms
	{
		FString RewardID;
		FHarmonyReward ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetReward_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetReward_Parms, ReturnValue), Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetReward", Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::HarmonyRewardsSystem_eventGetReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::HarmonyRewardsSystem_eventGetReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FHarmonyReward*)Z_Param__Result=P_THIS->GetReward(Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetReward ***********************************

// ********** Begin Class UHarmonyRewardsSystem Function GetTierDisplayName ************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics
{
	struct HarmonyRewardsSystem_eventGetTierDisplayName_Parms
	{
		ERewardTier Tier;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Tier_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Tier;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_Tier_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_Tier = { "Tier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetTierDisplayName_Parms, Tier), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetTierDisplayName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_Tier_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_Tier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetTierDisplayName", Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::HarmonyRewardsSystem_eventGetTierDisplayName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::HarmonyRewardsSystem_eventGetTierDisplayName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetTierDisplayName)
{
	P_GET_ENUM(ERewardTier,Z_Param_Tier);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetTierDisplayName(ERewardTier(Z_Param_Tier));
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetTierDisplayName **************************

// ********** Begin Class UHarmonyRewardsSystem Function GetUnlockedRewards ************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics
{
	struct HarmonyRewardsSystem_eventGetUnlockedRewards_Parms
	{
		FString PlayerID;
		TArray<FHarmonyReward> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetUnlockedRewards_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGetUnlockedRewards_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GetUnlockedRewards", Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::HarmonyRewardsSystem_eventGetUnlockedRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::HarmonyRewardsSystem_eventGetUnlockedRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGetUnlockedRewards)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FHarmonyReward>*)Z_Param__Result=P_THIS->GetUnlockedRewards(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GetUnlockedRewards **************************

// ********** Begin Class UHarmonyRewardsSystem Function GrantReward *******************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics
{
	struct HarmonyRewardsSystem_eventGrantReward_Parms
	{
		FString PlayerID;
		FString RewardID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGrantReward_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventGrantReward_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
void Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyRewardsSystem_eventGrantReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyRewardsSystem_eventGrantReward_Parms), &Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "GrantReward", Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::HarmonyRewardsSystem_eventGrantReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::HarmonyRewardsSystem_eventGrantReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execGrantReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GrantReward(Z_Param_PlayerID,Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function GrantReward *********************************

// ********** Begin Class UHarmonyRewardsSystem Function IsRewardUnlocked **************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics
{
	struct HarmonyRewardsSystem_eventIsRewardUnlocked_Parms
	{
		FString PlayerID;
		FString RewardID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventIsRewardUnlocked_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventIsRewardUnlocked_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
void Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyRewardsSystem_eventIsRewardUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyRewardsSystem_eventIsRewardUnlocked_Parms), &Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "IsRewardUnlocked", Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::HarmonyRewardsSystem_eventIsRewardUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::HarmonyRewardsSystem_eventIsRewardUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execIsRewardUnlocked)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRewardUnlocked(Z_Param_PlayerID,Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function IsRewardUnlocked ****************************

// ********** Begin Class UHarmonyRewardsSystem Function IsSpecialEventActive **********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics
{
	struct HarmonyRewardsSystem_eventIsSpecialEventActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyRewardsSystem_eventIsSpecialEventActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyRewardsSystem_eventIsSpecialEventActive_Parms), &Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "IsSpecialEventActive", Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::HarmonyRewardsSystem_eventIsSpecialEventActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::HarmonyRewardsSystem_eventIsSpecialEventActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execIsSpecialEventActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSpecialEventActive();
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function IsSpecialEventActive ************************

// ********** Begin Class UHarmonyRewardsSystem Function ProcessKindnessReward *********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics
{
	struct HarmonyRewardsSystem_eventProcessKindnessReward_Parms
	{
		FString PlayerID;
		FKindnessReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Reward Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Reward Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventProcessKindnessReward_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventProcessKindnessReward_Parms, Reward), Z_Construct_UScriptStruct_FKindnessReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4200945998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "ProcessKindnessReward", Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::HarmonyRewardsSystem_eventProcessKindnessReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::HarmonyRewardsSystem_eventProcessKindnessReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execProcessKindnessReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FKindnessReward,Z_Param_Out_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessKindnessReward(Z_Param_PlayerID,Z_Param_Out_Reward);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function ProcessKindnessReward ***********************

// ********** Begin Class UHarmonyRewardsSystem Function RegisterReward ****************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics
{
	struct HarmonyRewardsSystem_eventRegisterReward_Parms
	{
		FHarmonyReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Reward Management\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reward Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventRegisterReward_Parms, Reward), Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4013152750
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "RegisterReward", Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::HarmonyRewardsSystem_eventRegisterReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::HarmonyRewardsSystem_eventRegisterReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execRegisterReward)
{
	P_GET_STRUCT_REF(FHarmonyReward,Z_Param_Out_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterReward(Z_Param_Out_Reward);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function RegisterReward ******************************

// ********** Begin Class UHarmonyRewardsSystem Function TriggerSpecialEvent ***********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics
{
	struct HarmonyRewardsSystem_eventTriggerSpecialEvent_Parms
	{
		FString EventName;
		float BonusMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Special Events\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BonusMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::NewProp_EventName = { "EventName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventTriggerSpecialEvent_Parms, EventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventName_MetaData), NewProp_EventName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::NewProp_BonusMultiplier = { "BonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventTriggerSpecialEvent_Parms, BonusMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::NewProp_EventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::NewProp_BonusMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "TriggerSpecialEvent", Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::HarmonyRewardsSystem_eventTriggerSpecialEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::HarmonyRewardsSystem_eventTriggerSpecialEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execTriggerSpecialEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BonusMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerSpecialEvent(Z_Param_EventName,Z_Param_BonusMultiplier);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function TriggerSpecialEvent *************************

// ********** Begin Class UHarmonyRewardsSystem Function UnregisterReward **************************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics
{
	struct HarmonyRewardsSystem_eventUnregisterReward_Parms
	{
		FString RewardID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventUnregisterReward_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::NewProp_RewardID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "UnregisterReward", Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::HarmonyRewardsSystem_eventUnregisterReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::HarmonyRewardsSystem_eventUnregisterReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execUnregisterReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterReward(Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function UnregisterReward ****************************

// ********** Begin Class UHarmonyRewardsSystem Function UpdatePlayerProgress **********************
struct Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics
{
	struct HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms
	{
		FString PlayerID;
		int32 KindnessPointsGained;
		ERewardCategory Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Rewards" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_KindnessPointsGained;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_KindnessPointsGained = { "KindnessPointsGained", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms, KindnessPointsGained), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms, Category), Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardCategory, METADATA_PARAMS(0, nullptr) }; // 2605410775
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_KindnessPointsGained,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyRewardsSystem, nullptr, "UpdatePlayerProgress", Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::HarmonyRewardsSystem_eventUpdatePlayerProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyRewardsSystem::execUpdatePlayerProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FIntProperty,Z_Param_KindnessPointsGained);
	P_GET_ENUM(ERewardCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerProgress(Z_Param_PlayerID,Z_Param_KindnessPointsGained,ERewardCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UHarmonyRewardsSystem Function UpdatePlayerProgress ************************

// ********** Begin Class UHarmonyRewardsSystem ****************************************************
void UHarmonyRewardsSystem::StaticRegisterNativesUHarmonyRewardsSystem()
{
	UClass* Class = UHarmonyRewardsSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyCurrencyBonus", &UHarmonyRewardsSystem::execApplyCurrencyBonus },
		{ "ApplyExperienceBonus", &UHarmonyRewardsSystem::execApplyExperienceBonus },
		{ "CalculatePlayerTier", &UHarmonyRewardsSystem::execCalculatePlayerTier },
		{ "CalculateTierCurrencyBonus", &UHarmonyRewardsSystem::execCalculateTierCurrencyBonus },
		{ "CalculateTierExperienceBonus", &UHarmonyRewardsSystem::execCalculateTierExperienceBonus },
		{ "GetAvailableRewards", &UHarmonyRewardsSystem::execGetAvailableRewards },
		{ "GetCurrentEventMultiplier", &UHarmonyRewardsSystem::execGetCurrentEventMultiplier },
		{ "GetNextTier", &UHarmonyRewardsSystem::execGetNextTier },
		{ "GetPlayerProgress", &UHarmonyRewardsSystem::execGetPlayerProgress },
		{ "GetProgressToNextTier", &UHarmonyRewardsSystem::execGetProgressToNextTier },
		{ "GetReward", &UHarmonyRewardsSystem::execGetReward },
		{ "GetTierDisplayName", &UHarmonyRewardsSystem::execGetTierDisplayName },
		{ "GetUnlockedRewards", &UHarmonyRewardsSystem::execGetUnlockedRewards },
		{ "GrantReward", &UHarmonyRewardsSystem::execGrantReward },
		{ "IsRewardUnlocked", &UHarmonyRewardsSystem::execIsRewardUnlocked },
		{ "IsSpecialEventActive", &UHarmonyRewardsSystem::execIsSpecialEventActive },
		{ "ProcessKindnessReward", &UHarmonyRewardsSystem::execProcessKindnessReward },
		{ "RegisterReward", &UHarmonyRewardsSystem::execRegisterReward },
		{ "TriggerSpecialEvent", &UHarmonyRewardsSystem::execTriggerSpecialEvent },
		{ "UnregisterReward", &UHarmonyRewardsSystem::execUnregisterReward },
		{ "UpdatePlayerProgress", &UHarmonyRewardsSystem::execUpdatePlayerProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UHarmonyRewardsSystem;
UClass* UHarmonyRewardsSystem::GetPrivateStaticClass()
{
	using TClass = UHarmonyRewardsSystem;
	if (!Z_Registration_Info_UClass_UHarmonyRewardsSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("HarmonyRewardsSystem"),
			Z_Registration_Info_UClass_UHarmonyRewardsSystem.InnerSingleton,
			StaticRegisterNativesUHarmonyRewardsSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UHarmonyRewardsSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UHarmonyRewardsSystem_NoRegister()
{
	return UHarmonyRewardsSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UHarmonyRewardsSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Harmony Rewards System\n * Manages positive reinforcement through rewards, achievements, and progression\n */" },
#endif
		{ "IncludePath", "HarmonyRewardsSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Harmony Rewards System\nManages positive reinforcement through rewards, achievements, and progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRewardsPerSession_MetaData[] = {
		{ "Category", "Rewards Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardCooldownTime_MetaData[] = {
		{ "Category", "Rewards Config" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableProgressiveRewards_MetaData[] = {
		{ "Category", "Rewards Config" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSpecialEvents_MetaData[] = {
		{ "Category", "Rewards Config" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardsDataTable_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data Tables\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Tables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TierRequirementsDataTable_MetaData[] = {
		{ "Category", "Data" },
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredRewards_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Runtime Data\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Runtime Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerProgress_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastRewardTimes_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSpecialEventActive_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Special Events\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEventName_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEventMultiplier_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventEndTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TierKindnessRequirements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tier Requirements\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyRewardsSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tier Requirements" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRewardsPerSession;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardCooldownTime;
	static void NewProp_bEnableProgressiveRewards_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableProgressiveRewards;
	static void NewProp_bEnableSpecialEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSpecialEvents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RewardsDataTable;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TierRequirementsDataTable;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredRewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegisteredRewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredRewards;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerProgress_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerProgress_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerProgress;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastRewardTimes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastRewardTimes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LastRewardTimes;
	static void NewProp_bSpecialEventActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSpecialEventActive;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentEventName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentEventMultiplier;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventEndTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TierKindnessRequirements_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TierKindnessRequirements_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TierKindnessRequirements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TierKindnessRequirements;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyCurrencyBonus, "ApplyCurrencyBonus" }, // 433537690
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_ApplyExperienceBonus, "ApplyExperienceBonus" }, // 817184382
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_CalculatePlayerTier, "CalculatePlayerTier" }, // 3578533948
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierCurrencyBonus, "CalculateTierCurrencyBonus" }, // 1661493103
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_CalculateTierExperienceBonus, "CalculateTierExperienceBonus" }, // 1957887719
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetAvailableRewards, "GetAvailableRewards" }, // 3893693045
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetCurrentEventMultiplier, "GetCurrentEventMultiplier" }, // 1429054702
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetNextTier, "GetNextTier" }, // 3394933406
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetPlayerProgress, "GetPlayerProgress" }, // 305066660
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetProgressToNextTier, "GetProgressToNextTier" }, // 3021875098
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetReward, "GetReward" }, // 1412587849
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetTierDisplayName, "GetTierDisplayName" }, // 1260260660
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GetUnlockedRewards, "GetUnlockedRewards" }, // 2870447368
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_GrantReward, "GrantReward" }, // 788643481
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_IsRewardUnlocked, "IsRewardUnlocked" }, // 3503201045
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_IsSpecialEventActive, "IsSpecialEventActive" }, // 2381166649
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_ProcessKindnessReward, "ProcessKindnessReward" }, // 1977787738
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_RegisterReward, "RegisterReward" }, // 2200389940
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_TriggerSpecialEvent, "TriggerSpecialEvent" }, // 647458539
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_UnregisterReward, "UnregisterReward" }, // 1946326673
		{ &Z_Construct_UFunction_UHarmonyRewardsSystem_UpdatePlayerProgress, "UpdatePlayerProgress" }, // 2069908048
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UHarmonyRewardsSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_MaxRewardsPerSession = { "MaxRewardsPerSession", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, MaxRewardsPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRewardsPerSession_MetaData), NewProp_MaxRewardsPerSession_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RewardCooldownTime = { "RewardCooldownTime", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, RewardCooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardCooldownTime_MetaData), NewProp_RewardCooldownTime_MetaData) };
void Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableProgressiveRewards_SetBit(void* Obj)
{
	((UHarmonyRewardsSystem*)Obj)->bEnableProgressiveRewards = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableProgressiveRewards = { "bEnableProgressiveRewards", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyRewardsSystem), &Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableProgressiveRewards_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableProgressiveRewards_MetaData), NewProp_bEnableProgressiveRewards_MetaData) };
void Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableSpecialEvents_SetBit(void* Obj)
{
	((UHarmonyRewardsSystem*)Obj)->bEnableSpecialEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableSpecialEvents = { "bEnableSpecialEvents", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyRewardsSystem), &Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableSpecialEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSpecialEvents_MetaData), NewProp_bEnableSpecialEvents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RewardsDataTable = { "RewardsDataTable", nullptr, (EPropertyFlags)0x0124080000010015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, RewardsDataTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardsDataTable_MetaData), NewProp_RewardsDataTable_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierRequirementsDataTable = { "TierRequirementsDataTable", nullptr, (EPropertyFlags)0x0124080000010015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, TierRequirementsDataTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TierRequirementsDataTable_MetaData), NewProp_TierRequirementsDataTable_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards_ValueProp = { "RegisteredRewards", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards_Key_KeyProp = { "RegisteredRewards_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards = { "RegisteredRewards", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, RegisteredRewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredRewards_MetaData), NewProp_RegisteredRewards_MetaData) }; // 4013152750
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress_ValueProp = { "PlayerProgress", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPlayerRewardProgress, METADATA_PARAMS(0, nullptr) }; // 2978250964
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress_Key_KeyProp = { "PlayerProgress_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress = { "PlayerProgress", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, PlayerProgress), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerProgress_MetaData), NewProp_PlayerProgress_MetaData) }; // 2978250964
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes_ValueProp = { "LastRewardTimes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes_Key_KeyProp = { "LastRewardTimes_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes = { "LastRewardTimes", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, LastRewardTimes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastRewardTimes_MetaData), NewProp_LastRewardTimes_MetaData) };
void Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bSpecialEventActive_SetBit(void* Obj)
{
	((UHarmonyRewardsSystem*)Obj)->bSpecialEventActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bSpecialEventActive = { "bSpecialEventActive", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyRewardsSystem), &Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bSpecialEventActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSpecialEventActive_MetaData), NewProp_bSpecialEventActive_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_CurrentEventName = { "CurrentEventName", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, CurrentEventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEventName_MetaData), NewProp_CurrentEventName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_CurrentEventMultiplier = { "CurrentEventMultiplier", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, CurrentEventMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEventMultiplier_MetaData), NewProp_CurrentEventMultiplier_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_EventEndTime = { "EventEndTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, EventEndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventEndTime_MetaData), NewProp_EventEndTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_ValueProp = { "TierKindnessRequirements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_Key_KeyProp = { "TierKindnessRequirements_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_ERewardTier, METADATA_PARAMS(0, nullptr) }; // 711007694
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements = { "TierKindnessRequirements", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyRewardsSystem, TierKindnessRequirements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TierKindnessRequirements_MetaData), NewProp_TierKindnessRequirements_MetaData) }; // 711007694
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UHarmonyRewardsSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_MaxRewardsPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RewardCooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableProgressiveRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bEnableSpecialEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RewardsDataTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierRequirementsDataTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_RegisteredRewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_PlayerProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_LastRewardTimes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_bSpecialEventActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_CurrentEventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_CurrentEventMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_EventEndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyRewardsSystem_Statics::NewProp_TierKindnessRequirements,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyRewardsSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UHarmonyRewardsSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyRewardsSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UHarmonyRewardsSystem_Statics::ClassParams = {
	&UHarmonyRewardsSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UHarmonyRewardsSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyRewardsSystem_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyRewardsSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UHarmonyRewardsSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UHarmonyRewardsSystem()
{
	if (!Z_Registration_Info_UClass_UHarmonyRewardsSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UHarmonyRewardsSystem.OuterSingleton, Z_Construct_UClass_UHarmonyRewardsSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UHarmonyRewardsSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UHarmonyRewardsSystem);
UHarmonyRewardsSystem::~UHarmonyRewardsSystem() {}
// ********** End Class UHarmonyRewardsSystem ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERewardTier_StaticEnum, TEXT("ERewardTier"), &Z_Registration_Info_UEnum_ERewardTier, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 711007694U) },
		{ ERewardCategory_StaticEnum, TEXT("ERewardCategory"), &Z_Registration_Info_UEnum_ERewardCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2605410775U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FHarmonyReward::StaticStruct, Z_Construct_UScriptStruct_FHarmonyReward_Statics::NewStructOps, TEXT("HarmonyReward"), &Z_Registration_Info_UScriptStruct_FHarmonyReward, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHarmonyReward), 4013152750U) },
		{ FPlayerRewardProgress::StaticStruct, Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics::NewStructOps, TEXT("PlayerRewardProgress"), &Z_Registration_Info_UScriptStruct_FPlayerRewardProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerRewardProgress), 2978250964U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UHarmonyRewardsSystem, UHarmonyRewardsSystem::StaticClass, TEXT("UHarmonyRewardsSystem"), &Z_Registration_Info_UClass_UHarmonyRewardsSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UHarmonyRewardsSystem), 3561684337U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_2489912658(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
