// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "HarmonyEngineAdvancedML.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_HarmonyEngineAdvancedML_generated_h
#error "HarmonyEngineAdvancedML.generated.h already included, missing '#pragma once' in HarmonyEngineAdvancedML.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_HarmonyEngineAdvancedML_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EEmotionalState : uint8;
enum class EHarmonyMLModelType : uint8;
enum class EInterventionType : uint8;
enum class EInterventionType : uint8; 
struct FAdvancedBehaviorPrediction;
struct FHarmonyMLModelMetrics;
struct FHarmonyMLTrainingData;
struct FPlayerBehaviorSnapshot;

// ********** Begin ScriptStruct FHarmonyMLTrainingData ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_69_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHarmonyMLTrainingData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHarmonyMLTrainingData;
// ********** End ScriptStruct FHarmonyMLTrainingData **********************************************

// ********** Begin ScriptStruct FHarmonyMLModelMetrics ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_123_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHarmonyMLModelMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHarmonyMLModelMetrics;
// ********** End ScriptStruct FHarmonyMLModelMetrics **********************************************

// ********** Begin ScriptStruct FAdvancedBehaviorPrediction ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_176_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAdvancedBehaviorPrediction_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAdvancedBehaviorPrediction;
// ********** End ScriptStruct FAdvancedBehaviorPrediction *****************************************

// ********** Begin Class UHarmonyEngineAdvancedML *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetModelPerformanceSummary); \
	DECLARE_FUNCTION(execAnalyzeInterventionPatterns); \
	DECLARE_FUNCTION(execGetCommunityBehaviorTrends); \
	DECLARE_FUNCTION(execGenerateBehavioralInsights); \
	DECLARE_FUNCTION(execGetOptimalInterventionStrategy); \
	DECLARE_FUNCTION(execUpdateModelWeights); \
	DECLARE_FUNCTION(execOptimizeModelParameters); \
	DECLARE_FUNCTION(execProcessHealingSessionForTraining); \
	DECLARE_FUNCTION(execProcessInterventionOutcomeForTraining); \
	DECLARE_FUNCTION(execProcessBehaviorSnapshotForTraining); \
	DECLARE_FUNCTION(execAddTrainingDataPoint); \
	DECLARE_FUNCTION(execPredictHealingSessionSuccess); \
	DECLARE_FUNCTION(execPredictInterventionEffectiveness); \
	DECLARE_FUNCTION(execPredictEmotionalTrajectory); \
	DECLARE_FUNCTION(execGenerateAdvancedPrediction); \
	DECLARE_FUNCTION(execValidateModelPerformance); \
	DECLARE_FUNCTION(execGetModelMetrics); \
	DECLARE_FUNCTION(execTrainModel); \
	DECLARE_FUNCTION(execTrainAllModels); \
	DECLARE_FUNCTION(execInitializeMLSystem);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineAdvancedML_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUHarmonyEngineAdvancedML(); \
	friend struct Z_Construct_UClass_UHarmonyEngineAdvancedML_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineAdvancedML_NoRegister(); \
public: \
	DECLARE_CLASS2(UHarmonyEngineAdvancedML, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UHarmonyEngineAdvancedML_NoRegister) \
	DECLARE_SERIALIZER(UHarmonyEngineAdvancedML)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UHarmonyEngineAdvancedML(UHarmonyEngineAdvancedML&&) = delete; \
	UHarmonyEngineAdvancedML(const UHarmonyEngineAdvancedML&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UHarmonyEngineAdvancedML); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UHarmonyEngineAdvancedML); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UHarmonyEngineAdvancedML) \
	NO_API virtual ~UHarmonyEngineAdvancedML();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_226_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h_229_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UHarmonyEngineAdvancedML;

// ********** End Class UHarmonyEngineAdvancedML ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineAdvancedML_h

// ********** Begin Enum EHarmonyMLModelType *******************************************************
#define FOREACH_ENUM_EHARMONYMLMODELTYPE(op) \
	op(EHarmonyMLModelType::BehavioralPrediction) \
	op(EHarmonyMLModelType::EmotionalTrajectory) \
	op(EHarmonyMLModelType::InterventionOptimization) \
	op(EHarmonyMLModelType::CommunityDynamics) \
	op(EHarmonyMLModelType::ToxicityDetection) \
	op(EHarmonyMLModelType::PositivityAmplification) \
	op(EHarmonyMLModelType::CrisisPreventionModel) \
	op(EHarmonyMLModelType::HealingEffectiveness) 

enum class EHarmonyMLModelType : uint8;
template<> struct TIsUEnumClass<EHarmonyMLModelType> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHarmonyMLModelType>();
// ********** End Enum EHarmonyMLModelType *********************************************************

// ********** Begin Enum EMLTrainingDataType *******************************************************
#define FOREACH_ENUM_EMLTRAININGDATATYPE(op) \
	op(EMLTrainingDataType::BehaviorSnapshot) \
	op(EMLTrainingDataType::ChatMessage) \
	op(EMLTrainingDataType::GameplayAction) \
	op(EMLTrainingDataType::InterventionOutcome) \
	op(EMLTrainingDataType::HealingSession) \
	op(EMLTrainingDataType::CommunityInteraction) \
	op(EMLTrainingDataType::EmotionalTransition) 

enum class EMLTrainingDataType : uint8;
template<> struct TIsUEnumClass<EMLTrainingDataType> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EMLTrainingDataType>();
// ********** End Enum EMLTrainingDataType *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
