// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronAdaptiveCreaturesBridge_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronAdaptiveCreaturesBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronAdaptiveCreaturesBridge.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronAdaptiveCreaturesBridge",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x5B75AF35,
				0xAB94A2B4,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronAdaptiveCreaturesBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronAdaptiveCreaturesBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronAdaptiveCreaturesBridge(Z_Construct_UPackage__Script_AuracronAdaptiveCreaturesBridge, TEXT("/Script/AuracronAdaptiveCreaturesBridge"), Z_Registration_Info_UPackage__Script_AuracronAdaptiveCreaturesBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x5B75AF35, 0xAB94A2B4));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
