// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanFramework/Public/Systems/AuracronMetaHumanDNASystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanDNASystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister();
AURACRONMETAHUMANFRAMEWORK_API UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation();
AURACRONMETAHUMANFRAMEWORK_API UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAJointConfig();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAMetadata();
AURACRONMETAHUMANFRAMEWORK_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAResult();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanFramework();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronDNAOperation *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDNAOperation;
static UEnum* EAuracronDNAOperation_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDNAOperation.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDNAOperation.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("EAuracronDNAOperation"));
	}
	return Z_Registration_Info_UEnum_EAuracronDNAOperation.OuterSingleton;
}
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronDNAOperation>()
{
	return EAuracronDNAOperation_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Calibrate.DisplayName", "Calibrate DNA" },
		{ "Calibrate.Name", "EAuracronDNAOperation::Calibrate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * DNA Processing Operation Types\n */" },
#endif
		{ "Convert.DisplayName", "Convert DNA" },
		{ "Convert.Name", "EAuracronDNAOperation::Convert" },
		{ "Extract.DisplayName", "Extract DNA" },
		{ "Extract.Name", "EAuracronDNAOperation::Extract" },
		{ "Load.DisplayName", "Load DNA" },
		{ "Load.Name", "EAuracronDNAOperation::Load" },
		{ "Merge.DisplayName", "Merge DNA" },
		{ "Merge.Name", "EAuracronDNAOperation::Merge" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
		{ "Optimize.DisplayName", "Optimize DNA" },
		{ "Optimize.Name", "EAuracronDNAOperation::Optimize" },
		{ "Save.DisplayName", "Save DNA" },
		{ "Save.Name", "EAuracronDNAOperation::Save" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA Processing Operation Types" },
#endif
		{ "Validate.DisplayName", "Validate DNA" },
		{ "Validate.Name", "EAuracronDNAOperation::Validate" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDNAOperation::Load", (int64)EAuracronDNAOperation::Load },
		{ "EAuracronDNAOperation::Save", (int64)EAuracronDNAOperation::Save },
		{ "EAuracronDNAOperation::Validate", (int64)EAuracronDNAOperation::Validate },
		{ "EAuracronDNAOperation::Optimize", (int64)EAuracronDNAOperation::Optimize },
		{ "EAuracronDNAOperation::Merge", (int64)EAuracronDNAOperation::Merge },
		{ "EAuracronDNAOperation::Extract", (int64)EAuracronDNAOperation::Extract },
		{ "EAuracronDNAOperation::Calibrate", (int64)EAuracronDNAOperation::Calibrate },
		{ "EAuracronDNAOperation::Convert", (int64)EAuracronDNAOperation::Convert },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	"EAuracronDNAOperation",
	"EAuracronDNAOperation",
	Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation()
{
	if (!Z_Registration_Info_UEnum_EAuracronDNAOperation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDNAOperation.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDNAOperation.InnerSingleton;
}
// ********** End Enum EAuracronDNAOperation *******************************************************

// ********** Begin ScriptStruct FAuracronDNAResult ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAResult;
class UScriptStruct* FAuracronDNAResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAResult, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAResult"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAResult.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * DNA Processing Result\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA Processing Result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Operation_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Operation that was performed */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Operation that was performed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSuccess_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether the operation was successful */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether the operation was successful" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Result message */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Result message" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessingTimeMS_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Processing time in milliseconds */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processing time in milliseconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OutputPath_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Output file path (if applicable) */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Output file path (if applicable)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalData_MetaData[] = {
		{ "Category", "Result" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Additional data as JSON string */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional data as JSON string" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Operation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Operation;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProcessingTimeMS;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OutputPath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Operation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Operation = { "Operation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAResult, Operation), Z_Construct_UEnum_AuracronMetaHumanFramework_EAuracronDNAOperation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Operation_MetaData), NewProp_Operation_MetaData) }; // 1402672960
void Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((FAuracronDNAResult*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDNAResult), &Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSuccess_MetaData), NewProp_bSuccess_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAResult, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_ProcessingTimeMS = { "ProcessingTimeMS", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAResult, ProcessingTimeMS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessingTimeMS_MetaData), NewProp_ProcessingTimeMS_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_OutputPath = { "OutputPath", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAResult, OutputPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OutputPath_MetaData), NewProp_OutputPath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_AdditionalData = { "AdditionalData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAResult, AdditionalData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalData_MetaData), NewProp_AdditionalData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Operation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Operation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_ProcessingTimeMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_OutputPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewProp_AdditionalData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAResult",
	Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::PropPointers),
	sizeof(FAuracronDNAResult),
	alignof(FAuracronDNAResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAResult()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAResult.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAResult.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAResult **************************************************

// ********** Begin ScriptStruct FAuracronDNAMetadata **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata;
class UScriptStruct* FAuracronDNAMetadata::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAMetadata, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAMetadata"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * DNA Metadata Structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA Metadata Structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FileName_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** DNA file name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA file name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Version_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** DNA version */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA version" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Type_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** DNA type */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Creation timestamp */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creation timestamp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FileSize_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** File size in bytes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "File size in bytes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshCount_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of meshes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of meshes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JointCount_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of joints */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of joints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeCount_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of blend shapes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of blend shapes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalData_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Additional metadata as JSON */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Additional metadata as JSON" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FileName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Version;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Type;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FInt64PropertyParams NewProp_FileSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_JointCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BlendShapeCount;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAMetadata>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_FileName = { "FileName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, FileName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FileName_MetaData), NewProp_FileName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_Version = { "Version", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, Version), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Version_MetaData), NewProp_Version_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, Type), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Type_MetaData), NewProp_Type_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FInt64PropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_FileSize = { "FileSize", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int64, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, FileSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FileSize_MetaData), NewProp_FileSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_MeshCount = { "MeshCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, MeshCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshCount_MetaData), NewProp_MeshCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_JointCount = { "JointCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, JointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JointCount_MetaData), NewProp_JointCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_BlendShapeCount = { "BlendShapeCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, BlendShapeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeCount_MetaData), NewProp_BlendShapeCount_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_AdditionalData = { "AdditionalData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAMetadata, AdditionalData), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalData_MetaData), NewProp_AdditionalData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_FileName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_Version,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_FileSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_MeshCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_JointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_BlendShapeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewProp_AdditionalData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAMetadata",
	Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::PropPointers),
	sizeof(FAuracronDNAMetadata),
	alignof(FAuracronDNAMetadata),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAMetadata()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAMetadata ************************************************

// ********** Begin ScriptStruct FAuracronDNABlendShapeConfig **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig;
class UScriptStruct* FAuracronDNABlendShapeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNABlendShapeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * DNA Blend Shape Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA Blend Shape Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshIndex_MetaData[] = {
		{ "Category", "Blend Shape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh index */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh index" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetIndex_MetaData[] = {
		{ "Category", "Blend Shape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target index */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target index" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexDeltas_MetaData[] = {
		{ "Category", "Blend Shape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vertex deltas */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vertex deltas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendShapeName_MetaData[] = {
		{ "Category", "Blend Shape" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blend shape name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend shape name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeightMultiplier_MetaData[] = {
		{ "Category", "Blend Shape" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weight multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weight multiplier" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VertexDeltas_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VertexDeltas;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlendShapeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeightMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNABlendShapeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNABlendShapeConfig, MeshIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshIndex_MetaData), NewProp_MeshIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_TargetIndex = { "TargetIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNABlendShapeConfig, TargetIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetIndex_MetaData), NewProp_TargetIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_VertexDeltas_Inner = { "VertexDeltas", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_VertexDeltas = { "VertexDeltas", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNABlendShapeConfig, VertexDeltas), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexDeltas_MetaData), NewProp_VertexDeltas_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_BlendShapeName = { "BlendShapeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNABlendShapeConfig, BlendShapeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendShapeName_MetaData), NewProp_BlendShapeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_WeightMultiplier = { "WeightMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNABlendShapeConfig, WeightMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeightMultiplier_MetaData), NewProp_WeightMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_TargetIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_VertexDeltas_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_VertexDeltas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_BlendShapeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewProp_WeightMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNABlendShapeConfig",
	Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::PropPointers),
	sizeof(FAuracronDNABlendShapeConfig),
	alignof(FAuracronDNABlendShapeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNABlendShapeConfig ****************************************

// ********** Begin ScriptStruct FAuracronDNAJointConfig *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig;
class UScriptStruct* FAuracronDNAJointConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDNAJointConfig, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanFramework(), TEXT("AuracronDNAJointConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * DNA Joint Configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DNA Joint Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JointIndex_MetaData[] = {
		{ "Category", "Joint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Joint index */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Joint index" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JointName_MetaData[] = {
		{ "Category", "Joint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Joint name */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Joint name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NeutralTranslation_MetaData[] = {
		{ "Category", "Joint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Neutral translation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Neutral translation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NeutralRotation_MetaData[] = {
		{ "Category", "Joint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Neutral rotation */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Neutral rotation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParentJointIndex_MetaData[] = {
		{ "Category", "Joint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parent joint index */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parent joint index" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_JointIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_JointName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NeutralTranslation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NeutralRotation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ParentJointIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDNAJointConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_JointIndex = { "JointIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAJointConfig, JointIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JointIndex_MetaData), NewProp_JointIndex_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_JointName = { "JointName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAJointConfig, JointName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JointName_MetaData), NewProp_JointName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_NeutralTranslation = { "NeutralTranslation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAJointConfig, NeutralTranslation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NeutralTranslation_MetaData), NewProp_NeutralTranslation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_NeutralRotation = { "NeutralRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAJointConfig, NeutralRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NeutralRotation_MetaData), NewProp_NeutralRotation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_ParentJointIndex = { "ParentJointIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDNAJointConfig, ParentJointIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParentJointIndex_MetaData), NewProp_ParentJointIndex_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_JointIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_JointName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_NeutralTranslation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_NeutralRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewProp_ParentJointIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
	nullptr,
	&NewStructOps,
	"AuracronDNAJointConfig",
	Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::PropPointers),
	sizeof(FAuracronDNAJointConfig),
	alignof(FAuracronDNAJointConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDNAJointConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDNAJointConfig *********************************************

// ********** Begin Delegate FAuracronDNAOperationComplete *****************************************
struct Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms
	{
		FAuracronDNAResult Result;
		FString OperationID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms, Result), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 2769742587
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::NewProp_OperationID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework, nullptr, "AuracronDNAOperationComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::_Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FAuracronDNAOperationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronDNAOperationComplete, FAuracronDNAResult const& Result, const FString& OperationID)
{
	struct _Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms
	{
		FAuracronDNAResult Result;
		FString OperationID;
	};
	_Script_AuracronMetaHumanFramework_eventAuracronDNAOperationComplete_Parms Parms;
	Parms.Result=Result;
	Parms.OperationID=OperationID;
	AuracronDNAOperationComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FAuracronDNAOperationComplete *******************************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetAsyncOperationResult *************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetAsyncOperationResult_Parms
	{
		FString OperationID;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get async operation result\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get async operation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetAsyncOperationResult_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetAsyncOperationResult_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetAsyncOperationResult", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::AuracronMetaHumanDNASystem_eventGetAsyncOperationResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::AuracronMetaHumanDNASystem_eventGetAsyncOperationResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetAsyncOperationResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->GetAsyncOperationResult(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetAsyncOperationResult ***************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetBlendShapeCount ******************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetBlendShapeCount_Parms
	{
		int32 MeshIndex;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|BlendShapes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get blend shape count for mesh\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get blend shape count for mesh" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeCount_Parms, MeshIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetBlendShapeCount", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetBlendShapeCount)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetBlendShapeCount(Z_Param_MeshIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetBlendShapeCount ********************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetBlendShapeName *******************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms
	{
		int32 MeshIndex;
		int32 TargetIndex;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|BlendShapes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get blend shape name\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get blend shape name" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms, MeshIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_TargetIndex = { "TargetIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms, TargetIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_TargetIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetBlendShapeName", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetBlendShapeName)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetBlendShapeName(Z_Param_MeshIndex,Z_Param_TargetIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetBlendShapeName *********************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetBlendShapeTargetDeltas ***********
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms
	{
		int32 MeshIndex;
		int32 TargetIndex;
		TArray<FVector> OutDeltas;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|BlendShapes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get blend shape target deltas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get blend shape target deltas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutDeltas_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OutDeltas;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms, MeshIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_TargetIndex = { "TargetIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms, TargetIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_OutDeltas_Inner = { "OutDeltas", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_OutDeltas = { "OutDeltas", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms, OutDeltas), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_TargetIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_OutDeltas_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_OutDeltas,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetBlendShapeTargetDeltas", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::AuracronMetaHumanDNASystem_eventGetBlendShapeTargetDeltas_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetBlendShapeTargetDeltas)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetIndex);
	P_GET_TARRAY_REF(FVector,Z_Param_Out_OutDeltas);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->GetBlendShapeTargetDeltas(Z_Param_MeshIndex,Z_Param_TargetIndex,Z_Param_Out_OutDeltas);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetBlendShapeTargetDeltas *************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetJointCount ***********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetJointCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Joints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get joint count\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get joint count" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetJointCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetJointCount", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::AuracronMetaHumanDNASystem_eventGetJointCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::AuracronMetaHumanDNASystem_eventGetJointCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetJointCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetJointCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetJointCount *************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetJointName ************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetJointName_Parms
	{
		int32 JointIndex;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Joints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get joint name\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get joint name" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_JointIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::NewProp_JointIndex = { "JointIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetJointName_Parms, JointIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetJointName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::NewProp_JointIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetJointName", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::AuracronMetaHumanDNASystem_eventGetJointName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::AuracronMetaHumanDNASystem_eventGetJointName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetJointName)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_JointIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetJointName(Z_Param_JointIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetJointName **************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetJointParentIndex *****************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetJointParentIndex_Parms
	{
		int32 JointIndex;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Joints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get joint parent index\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get joint parent index" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_JointIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::NewProp_JointIndex = { "JointIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetJointParentIndex_Parms, JointIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetJointParentIndex_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::NewProp_JointIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetJointParentIndex", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::AuracronMetaHumanDNASystem_eventGetJointParentIndex_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::AuracronMetaHumanDNASystem_eventGetJointParentIndex_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetJointParentIndex)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_JointIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetJointParentIndex(Z_Param_JointIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetJointParentIndex *******************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetMeshCount ************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetMeshCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Meshes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get mesh count\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get mesh count" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetMeshCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetMeshCount", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::AuracronMetaHumanDNASystem_eventGetMeshCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::AuracronMetaHumanDNASystem_eventGetMeshCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetMeshCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMeshCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetMeshCount **************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetMeshName *************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetMeshName_Parms
	{
		int32 MeshIndex;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Meshes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get mesh name\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get mesh name" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetMeshName_Parms, MeshIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetMeshName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetMeshName", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::AuracronMetaHumanDNASystem_eventGetMeshName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::AuracronMetaHumanDNASystem_eventGetMeshName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetMeshName)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetMeshName(Z_Param_MeshIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetMeshName ***************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetSystemStatus *********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetSystemStatus_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get DNA system status\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get DNA system status" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetSystemStatus_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetSystemStatus", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::AuracronMetaHumanDNASystem_eventGetSystemStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::AuracronMetaHumanDNASystem_eventGetSystemStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetSystemStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSystemStatus();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetSystemStatus ***********************

// ********** Begin Class UAuracronMetaHumanDNASystem Function GetVertexCount **********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics
{
	struct AuracronMetaHumanDNASystem_eventGetVertexCount_Parms
	{
		int32 MeshIndex;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Meshes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get vertex count for mesh\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get vertex count for mesh" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MeshIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::NewProp_MeshIndex = { "MeshIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetVertexCount_Parms, MeshIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventGetVertexCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::NewProp_MeshIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "GetVertexCount", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::AuracronMetaHumanDNASystem_eventGetVertexCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::AuracronMetaHumanDNASystem_eventGetVertexCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execGetVertexCount)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MeshIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetVertexCount(Z_Param_MeshIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function GetVertexCount ************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function Initialize **************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics
{
	struct AuracronMetaHumanDNASystem_eventInitialize_Parms
	{
		UAuracronMetaHumanFramework* InFramework;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Initialize DNA system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize DNA system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InFramework;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_InFramework = { "InFramework", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventInitialize_Parms, InFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNASystem_eventInitialize_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNASystem_eventInitialize_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_InFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "Initialize", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::AuracronMetaHumanDNASystem_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::AuracronMetaHumanDNASystem_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execInitialize)
{
	P_GET_OBJECT(UAuracronMetaHumanFramework,Z_Param_InFramework);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Initialize(Z_Param_InFramework);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function Initialize ****************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function IsAsyncOperationComplete ************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics
{
	struct AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms
	{
		FString OperationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if async operation is complete\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if async operation is complete" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OperationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OperationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_OperationID = { "OperationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms, OperationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OperationID_MetaData), NewProp_OperationID_MetaData) };
void Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_OperationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "IsAsyncOperationComplete", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::AuracronMetaHumanDNASystem_eventIsAsyncOperationComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execIsAsyncOperationComplete)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OperationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAsyncOperationComplete(Z_Param_OperationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function IsAsyncOperationComplete **************

// ********** Begin Class UAuracronMetaHumanDNASystem Function IsInitialized ***********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics
{
	struct AuracronMetaHumanDNASystem_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Check if DNA system is initialized\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if DNA system is initialized" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMetaHumanDNASystem_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMetaHumanDNASystem_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::AuracronMetaHumanDNASystem_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::AuracronMetaHumanDNASystem_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function IsInitialized *************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function LoadDNAFromFile *********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics
{
	struct AuracronMetaHumanDNASystem_eventLoadDNAFromFile_Parms
	{
		FString FilePath;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Load DNA from file\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Load DNA from file" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventLoadDNAFromFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventLoadDNAFromFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "LoadDNAFromFile", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::AuracronMetaHumanDNASystem_eventLoadDNAFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::AuracronMetaHumanDNASystem_eventLoadDNAFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execLoadDNAFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->LoadDNAFromFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function LoadDNAFromFile ***********************

// ********** Begin Class UAuracronMetaHumanDNASystem Function LoadDNAFromFileAsync ****************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics
{
	struct AuracronMetaHumanDNASystem_eventLoadDNAFromFileAsync_Parms
	{
		FString FilePath;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Load DNA from file asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Load DNA from file asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventLoadDNAFromFileAsync_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventLoadDNAFromFileAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "LoadDNAFromFileAsync", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::AuracronMetaHumanDNASystem_eventLoadDNAFromFileAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::AuracronMetaHumanDNASystem_eventLoadDNAFromFileAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execLoadDNAFromFileAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->LoadDNAFromFileAsync(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function LoadDNAFromFileAsync ******************

// ********** Begin Class UAuracronMetaHumanDNASystem Function OptimizeDNA *************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics
{
	struct AuracronMetaHumanDNASystem_eventOptimizeDNA_Parms
	{
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Optimize DNA data\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize DNA data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventOptimizeDNA_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "OptimizeDNA", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::AuracronMetaHumanDNASystem_eventOptimizeDNA_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::AuracronMetaHumanDNASystem_eventOptimizeDNA_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execOptimizeDNA)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->OptimizeDNA();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function OptimizeDNA ***************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function SaveDNAToFile ***********************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics
{
	struct AuracronMetaHumanDNASystem_eventSaveDNAToFile_Parms
	{
		FString FilePath;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Save DNA to file\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save DNA to file" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSaveDNAToFile_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSaveDNAToFile_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "SaveDNAToFile", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::AuracronMetaHumanDNASystem_eventSaveDNAToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::AuracronMetaHumanDNASystem_eventSaveDNAToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execSaveDNAToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->SaveDNAToFile(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function SaveDNAToFile *************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function SaveDNAToFileAsync ******************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics
{
	struct AuracronMetaHumanDNASystem_eventSaveDNAToFileAsync_Parms
	{
		FString FilePath;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Async" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Save DNA to file asynchronously\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save DNA to file asynchronously" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSaveDNAToFileAsync_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSaveDNAToFileAsync_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "SaveDNAToFileAsync", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::AuracronMetaHumanDNASystem_eventSaveDNAToFileAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::AuracronMetaHumanDNASystem_eventSaveDNAToFileAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execSaveDNAToFileAsync)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->SaveDNAToFileAsync(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function SaveDNAToFileAsync ********************

// ********** Begin Class UAuracronMetaHumanDNASystem Function SetBlendShapeTargetDeltas ***********
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics
{
	struct AuracronMetaHumanDNASystem_eventSetBlendShapeTargetDeltas_Parms
	{
		FAuracronDNABlendShapeConfig Config;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|BlendShapes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set blend shape target deltas\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set blend shape target deltas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetBlendShapeTargetDeltas_Parms, Config), Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1684667556
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetBlendShapeTargetDeltas_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::NewProp_Config,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "SetBlendShapeTargetDeltas", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::AuracronMetaHumanDNASystem_eventSetBlendShapeTargetDeltas_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::AuracronMetaHumanDNASystem_eventSetBlendShapeTargetDeltas_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execSetBlendShapeTargetDeltas)
{
	P_GET_STRUCT_REF(FAuracronDNABlendShapeConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->SetBlendShapeTargetDeltas(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function SetBlendShapeTargetDeltas *************

// ********** Begin Class UAuracronMetaHumanDNASystem Function SetNeutralJointRotations ************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics
{
	struct AuracronMetaHumanDNASystem_eventSetNeutralJointRotations_Parms
	{
		TArray<FRotator> Rotations;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Joints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set neutral joint rotations\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set neutral joint rotations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Rotations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_Rotations_Inner = { "Rotations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_Rotations = { "Rotations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetNeutralJointRotations_Parms, Rotations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotations_MetaData), NewProp_Rotations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetNeutralJointRotations_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_Rotations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_Rotations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "SetNeutralJointRotations", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::AuracronMetaHumanDNASystem_eventSetNeutralJointRotations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::AuracronMetaHumanDNASystem_eventSetNeutralJointRotations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execSetNeutralJointRotations)
{
	P_GET_TARRAY_REF(FRotator,Z_Param_Out_Rotations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->SetNeutralJointRotations(Z_Param_Out_Rotations);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function SetNeutralJointRotations **************

// ********** Begin Class UAuracronMetaHumanDNASystem Function SetNeutralJointTranslations *********
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics
{
	struct AuracronMetaHumanDNASystem_eventSetNeutralJointTranslations_Parms
	{
		TArray<FVector> Translations;
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Joints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set neutral joint translations\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set neutral joint translations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Translations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Translations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Translations;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_Translations_Inner = { "Translations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_Translations = { "Translations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetNeutralJointTranslations_Parms, Translations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Translations_MetaData), NewProp_Translations_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventSetNeutralJointTranslations_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_Translations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_Translations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "SetNeutralJointTranslations", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::AuracronMetaHumanDNASystem_eventSetNeutralJointTranslations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::AuracronMetaHumanDNASystem_eventSetNeutralJointTranslations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execSetNeutralJointTranslations)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_Translations);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->SetNeutralJointTranslations(Z_Param_Out_Translations);
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function SetNeutralJointTranslations ***********

// ********** Begin Class UAuracronMetaHumanDNASystem Function Shutdown ****************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Shutdown DNA system\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown DNA system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function Shutdown ******************************

// ********** Begin Class UAuracronMetaHumanDNASystem Function ValidateDNA *************************
struct Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics
{
	struct AuracronMetaHumanDNASystem_eventValidateDNA_Parms
	{
		FAuracronDNAResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON MetaHuman|DNA|Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Validate DNA data\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate DNA data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMetaHumanDNASystem_eventValidateDNA_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDNAResult, METADATA_PARAMS(0, nullptr) }; // 2769742587
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronMetaHumanDNASystem, nullptr, "ValidateDNA", Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::AuracronMetaHumanDNASystem_eventValidateDNA_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::AuracronMetaHumanDNASystem_eventValidateDNA_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronMetaHumanDNASystem::execValidateDNA)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDNAResult*)Z_Param__Result=P_THIS->ValidateDNA();
	P_NATIVE_END;
}
// ********** End Class UAuracronMetaHumanDNASystem Function ValidateDNA ***************************

// ********** Begin Class UAuracronMetaHumanDNASystem **********************************************
void UAuracronMetaHumanDNASystem::StaticRegisterNativesUAuracronMetaHumanDNASystem()
{
	UClass* Class = UAuracronMetaHumanDNASystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetAsyncOperationResult", &UAuracronMetaHumanDNASystem::execGetAsyncOperationResult },
		{ "GetBlendShapeCount", &UAuracronMetaHumanDNASystem::execGetBlendShapeCount },
		{ "GetBlendShapeName", &UAuracronMetaHumanDNASystem::execGetBlendShapeName },
		{ "GetBlendShapeTargetDeltas", &UAuracronMetaHumanDNASystem::execGetBlendShapeTargetDeltas },
		{ "GetJointCount", &UAuracronMetaHumanDNASystem::execGetJointCount },
		{ "GetJointName", &UAuracronMetaHumanDNASystem::execGetJointName },
		{ "GetJointParentIndex", &UAuracronMetaHumanDNASystem::execGetJointParentIndex },
		{ "GetMeshCount", &UAuracronMetaHumanDNASystem::execGetMeshCount },
		{ "GetMeshName", &UAuracronMetaHumanDNASystem::execGetMeshName },
		{ "GetSystemStatus", &UAuracronMetaHumanDNASystem::execGetSystemStatus },
		{ "GetVertexCount", &UAuracronMetaHumanDNASystem::execGetVertexCount },
		{ "Initialize", &UAuracronMetaHumanDNASystem::execInitialize },
		{ "IsAsyncOperationComplete", &UAuracronMetaHumanDNASystem::execIsAsyncOperationComplete },
		{ "IsInitialized", &UAuracronMetaHumanDNASystem::execIsInitialized },
		{ "LoadDNAFromFile", &UAuracronMetaHumanDNASystem::execLoadDNAFromFile },
		{ "LoadDNAFromFileAsync", &UAuracronMetaHumanDNASystem::execLoadDNAFromFileAsync },
		{ "OptimizeDNA", &UAuracronMetaHumanDNASystem::execOptimizeDNA },
		{ "SaveDNAToFile", &UAuracronMetaHumanDNASystem::execSaveDNAToFile },
		{ "SaveDNAToFileAsync", &UAuracronMetaHumanDNASystem::execSaveDNAToFileAsync },
		{ "SetBlendShapeTargetDeltas", &UAuracronMetaHumanDNASystem::execSetBlendShapeTargetDeltas },
		{ "SetNeutralJointRotations", &UAuracronMetaHumanDNASystem::execSetNeutralJointRotations },
		{ "SetNeutralJointTranslations", &UAuracronMetaHumanDNASystem::execSetNeutralJointTranslations },
		{ "Shutdown", &UAuracronMetaHumanDNASystem::execShutdown },
		{ "ValidateDNA", &UAuracronMetaHumanDNASystem::execValidateDNA },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem;
UClass* UAuracronMetaHumanDNASystem::GetPrivateStaticClass()
{
	using TClass = UAuracronMetaHumanDNASystem;
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMetaHumanDNASystem"),
			Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.InnerSingleton,
			StaticRegisterNativesUAuracronMetaHumanDNASystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister()
{
	return UAuracronMetaHumanDNASystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|MetaHuman|DNA" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * AURACRON MetaHuman DNA System\n * Advanced DNA processing and manipulation system for UE 5.6\n */" },
#endif
		{ "IncludePath", "Systems/AuracronMetaHumanDNASystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AURACRON MetaHuman DNA System\nAdvanced DNA processing and manipulation system for UE 5.6" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDNAOperationComplete_MetaData[] = {
		{ "Category", "AURACRON MetaHuman|DNA|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when an async DNA operation completes */" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when an async DNA operation completes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerFramework_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Internal State ===\n" },
#endif
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Internal State ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastErrorMessage_MetaData[] = {
		{ "ModuleRelativePath", "Public/Systems/AuracronMetaHumanDNASystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDNAOperationComplete;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerFramework;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LastErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetAsyncOperationResult, "GetAsyncOperationResult" }, // 4193315272
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeCount, "GetBlendShapeCount" }, // 2147120152
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeName, "GetBlendShapeName" }, // 839602139
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetBlendShapeTargetDeltas, "GetBlendShapeTargetDeltas" }, // 2239360887
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointCount, "GetJointCount" }, // 2189779338
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointName, "GetJointName" }, // 1379595292
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetJointParentIndex, "GetJointParentIndex" }, // 702617538
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshCount, "GetMeshCount" }, // 250611319
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetMeshName, "GetMeshName" }, // 2079202308
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetSystemStatus, "GetSystemStatus" }, // 675614110
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_GetVertexCount, "GetVertexCount" }, // 4292286861
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Initialize, "Initialize" }, // 1858442223
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsAsyncOperationComplete, "IsAsyncOperationComplete" }, // 2613238710
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_IsInitialized, "IsInitialized" }, // 1903230140
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFile, "LoadDNAFromFile" }, // 1889419939
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_LoadDNAFromFileAsync, "LoadDNAFromFileAsync" }, // 2755591598
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_OptimizeDNA, "OptimizeDNA" }, // 472993402
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFile, "SaveDNAToFile" }, // 3300571765
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SaveDNAToFileAsync, "SaveDNAToFileAsync" }, // 3443403536
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetBlendShapeTargetDeltas, "SetBlendShapeTargetDeltas" }, // 1736878848
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointRotations, "SetNeutralJointRotations" }, // 2820252396
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_SetNeutralJointTranslations, "SetNeutralJointTranslations" }, // 3082711424
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_Shutdown, "Shutdown" }, // 576988226
		{ &Z_Construct_UFunction_UAuracronMetaHumanDNASystem_ValidateDNA, "ValidateDNA" }, // 3207089614
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMetaHumanDNASystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_OnDNAOperationComplete = { "OnDNAOperationComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanDNASystem, OnDNAOperationComplete), Z_Construct_UDelegateFunction_AuracronMetaHumanFramework_AuracronDNAOperationComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDNAOperationComplete_MetaData), NewProp_OnDNAOperationComplete_MetaData) }; // 1310663861
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_OwnerFramework = { "OwnerFramework", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanDNASystem, OwnerFramework), Z_Construct_UClass_UAuracronMetaHumanFramework_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerFramework_MetaData), NewProp_OwnerFramework_MetaData) };
void Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronMetaHumanDNASystem*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMetaHumanDNASystem), &Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_LastErrorMessage = { "LastErrorMessage", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMetaHumanDNASystem, LastErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastErrorMessage_MetaData), NewProp_LastErrorMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_OnDNAOperationComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_OwnerFramework,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::NewProp_LastErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronMetaHumanFramework,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::ClassParams = {
	&UAuracronMetaHumanDNASystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem()
{
	if (!Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.OuterSingleton, Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMetaHumanDNASystem);
// ********** End Class UAuracronMetaHumanDNASystem ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronDNAOperation_StaticEnum, TEXT("EAuracronDNAOperation"), &Z_Registration_Info_UEnum_EAuracronDNAOperation, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1402672960U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDNAResult::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAResult_Statics::NewStructOps, TEXT("AuracronDNAResult"), &Z_Registration_Info_UScriptStruct_FAuracronDNAResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAResult), 2769742587U) },
		{ FAuracronDNAMetadata::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics::NewStructOps, TEXT("AuracronDNAMetadata"), &Z_Registration_Info_UScriptStruct_FAuracronDNAMetadata, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAMetadata), 4184942404U) },
		{ FAuracronDNABlendShapeConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics::NewStructOps, TEXT("AuracronDNABlendShapeConfig"), &Z_Registration_Info_UScriptStruct_FAuracronDNABlendShapeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNABlendShapeConfig), 1684667556U) },
		{ FAuracronDNAJointConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics::NewStructOps, TEXT("AuracronDNAJointConfig"), &Z_Registration_Info_UScriptStruct_FAuracronDNAJointConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDNAJointConfig), 1766480129U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMetaHumanDNASystem, UAuracronMetaHumanDNASystem::StaticClass, TEXT("UAuracronMetaHumanDNASystem"), &Z_Registration_Info_UClass_UAuracronMetaHumanDNASystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMetaHumanDNASystem), 1535925523U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_2186183030(TEXT("/Script/AuracronMetaHumanFramework"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h__Script_AuracronMetaHumanFramework_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
