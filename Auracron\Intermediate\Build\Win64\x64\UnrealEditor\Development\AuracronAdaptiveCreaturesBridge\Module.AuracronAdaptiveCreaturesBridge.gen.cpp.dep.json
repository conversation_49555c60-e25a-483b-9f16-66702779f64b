{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronadaptivecreaturesbridge\\module.auracronadaptivecreaturesbridge.gen.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronadaptivecreaturesbridge\\definitions.auracronadaptivecreaturesbridge.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronadaptivecreaturesbridge\\uht\\auracronadaptivecreaturesbridge.gen.cpp", "c:\\aura\\projeto\\auracron\\source\\auracronadaptivecreaturesbridge\\public\\auracronadaptivecreaturesbridge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentitysubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\masssubsystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\masstypemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentityconcepts.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentityelementtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massentityelementtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massexternalsubsystemtraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\masssubsystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentitymanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentitytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structtypebitset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutilstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massprocessingtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massprocessingtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structarrayview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\sharedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\sharedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\masstestableensures.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\aitestsuite\\public\\testableensures.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massentitytypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentityquery.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massarchetypetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massarchetypegroup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massrequirements.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massrequirements.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massentityquery.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massobservermanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentityhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massentityhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massobservermanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massrequirementaccessdetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentitymanagerstorage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\async\\transactionallysafemutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massentitysubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massprocessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\masscommandbuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\massentityutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\massentity\\public\\masscommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\mttransactionallysafeaccessdetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\masscommands.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\massentity\\uht\\massprocessor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeschema.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeschema.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreepropertybindings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingbindablestructdescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\intermediate\\build\\win64\\unrealeditor\\inc\\propertybindingutils\\uht\\propertybindingbindablestructdescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingpath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\propertybag.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\propertybag.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\intermediate\\build\\win64\\unrealeditor\\inc\\propertybindingutils\\uht\\propertybindingtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\intermediate\\build\\win64\\unrealeditor\\inc\\propertybindingutils\\uht\\propertybindingpath.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingbinding.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\intermediate\\build\\win64\\unrealeditor\\inc\\propertybindingutils\\uht\\propertybindingbinding.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingbindingcollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\intermediate\\build\\win64\\unrealeditor\\inc\\propertybindingutils\\uht\\propertybindingbindingcollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreenodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\propertybindingutils\\source\\propertybindingutils\\public\\propertybindingdataview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreedelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeindextypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeindextypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreedelegate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreetasksstatus.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreetasksstatus.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreenodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreepropertyrefhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreepropertybindings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeinstancedata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstructcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstructcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\debugger\\statetreeruntimevalidation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeevents.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeevents.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeexecutiontypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeexecutionextension.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeexecutionextension.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreestatepath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeexecutiontypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreeinstancedata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetree.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreeexecutioncontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreenoderef.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\statetreereference.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\intermediate\\build\\win64\\unrealeditor\\inc\\statetreemodule\\uht\\statetreereference.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\statetree\\source\\statetreemodule\\public\\debugger\\statetreetrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkey.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aitypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navagentselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navagentselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\braincomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\airesourceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\airesourceinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\braincomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptioncomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\genericteamagentinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\genericteamagentinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptiontypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptiontypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aisubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aisystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\aisystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aisystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptioncomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aicontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionlistenerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionlistenerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aicontroller.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationinvokerpriority.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationinvokerpriority.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ai\\navigationmodifier.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navlinkdefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navlinkdefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdataresolution.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdataresolution.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationrelevantdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyarea.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctreecontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdirtyareascontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\movingwindowaveragefast.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationbounds.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationsystem.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronadaptivecreaturesbridge\\uht\\auracronadaptivecreaturesbridge.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronadaptivecreaturesbridge\\uht\\auracronadaptivecreaturesbridge.init.gen.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}