// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RealTimeInterventionSystem.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_RealTimeInterventionSystem_generated_h
#error "RealTimeInterventionSystem.generated.h already included, missing '#pragma once' in RealTimeInterventionSystem.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_RealTimeInterventionSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EInterventionMethod : uint8;
enum class EInterventionResult : uint8;
enum class EInterventionType : uint8;
enum class EInterventionType : uint8; 
struct FActiveIntervention;
struct FHarmonyInterventionData;
struct FInterventionStrategy;

// ********** Begin ScriptStruct FActiveIntervention ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_41_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FActiveIntervention_Statics; \
	static class UScriptStruct* StaticStruct();


struct FActiveIntervention;
// ********** End ScriptStruct FActiveIntervention *************************************************

// ********** Begin ScriptStruct FInterventionStrategy *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_94_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FInterventionStrategy_Statics; \
	static class UScriptStruct* StaticStruct();


struct FInterventionStrategy;
// ********** End ScriptStruct FInterventionStrategy ***********************************************

// ********** Begin Class URealTimeInterventionSystem **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetInterventionTypeStatistics); \
	DECLARE_FUNCTION(execGetTotalInterventionsToday); \
	DECLARE_FUNCTION(execGetOverallInterventionSuccessRate); \
	DECLARE_FUNCTION(execGetStrategyEffectiveness); \
	DECLARE_FUNCTION(execSelectOptimalMethod); \
	DECLARE_FUNCTION(execRegisterInterventionStrategy); \
	DECLARE_FUNCTION(execExecuteFollowUpIntervention); \
	DECLARE_FUNCTION(execCancelIntervention); \
	DECLARE_FUNCTION(execIsPlayerUnderIntervention); \
	DECLARE_FUNCTION(execGetIntervention); \
	DECLARE_FUNCTION(execGetActiveInterventions); \
	DECLARE_FUNCTION(execCompleteIntervention); \
	DECLARE_FUNCTION(execEscalateIntervention); \
	DECLARE_FUNCTION(execRespondToIntervention); \
	DECLARE_FUNCTION(execExecuteIntervention);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_URealTimeInterventionSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURealTimeInterventionSystem(); \
	friend struct Z_Construct_UClass_URealTimeInterventionSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_URealTimeInterventionSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(URealTimeInterventionSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_URealTimeInterventionSystem_NoRegister) \
	DECLARE_SERIALIZER(URealTimeInterventionSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	URealTimeInterventionSystem(URealTimeInterventionSystem&&) = delete; \
	URealTimeInterventionSystem(const URealTimeInterventionSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URealTimeInterventionSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URealTimeInterventionSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URealTimeInterventionSystem) \
	NO_API virtual ~URealTimeInterventionSystem();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_127_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h_130_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class URealTimeInterventionSystem;

// ********** End Class URealTimeInterventionSystem ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h

// ********** Begin Enum EInterventionMethod *******************************************************
#define FOREACH_ENUM_EINTERVENTIONMETHOD(op) \
	op(EInterventionMethod::InGameMessage) \
	op(EInterventionMethod::UINotification) \
	op(EInterventionMethod::AudioCue) \
	op(EInterventionMethod::VisualEffect) \
	op(EInterventionMethod::GameplayModifier) \
	op(EInterventionMethod::PeerConnection) \
	op(EInterventionMethod::MentorAssignment) \
	op(EInterventionMethod::BreakSuggestion) 

enum class EInterventionMethod : uint8;
template<> struct TIsUEnumClass<EInterventionMethod> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionMethod>();
// ********** End Enum EInterventionMethod *********************************************************

// ********** Begin Enum EInterventionResult *******************************************************
#define FOREACH_ENUM_EINTERVENTIONRESULT(op) \
	op(EInterventionResult::Pending) \
	op(EInterventionResult::Accepted) \
	op(EInterventionResult::Declined) \
	op(EInterventionResult::Ignored) \
	op(EInterventionResult::Successful) \
	op(EInterventionResult::Failed) \
	op(EInterventionResult::Escalated) 

enum class EInterventionResult : uint8;
template<> struct TIsUEnumClass<EInterventionResult> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionResult>();
// ********** End Enum EInterventionResult *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
