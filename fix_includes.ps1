# Script para corrigir todos os includes incorretos do PCG
# Executa em todos os arquivos .h e .cpp do projeto AuracronPCGBridge

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao de includes PCG..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Mapeamento de includes incorretos para corretos baseado na estrutura real do UE 5.6 PCG
$includeMap = @{
    # Includes básicos do PCG
    '#include "Elements/PCGSplitAttributes.h"' = '#include "Elements/PCGSplitPoints.h"'
    '#include "Elements/PCGSubgraph.h"' = '#include "PCGSubgraph.h"'
    '#include "Elements/PCGPropertyToParamData.h"' = '#include "Elements/PCGDataTableRowToParamData.h"'
    '#include "PCGSpatialData.h"' = '#include "Data/PCGSpatialData.h"'
    '#include "PCGMetadataAccessor.h"' = '#include "Metadata/PCGMetadataAccessor.h"'
    '#include "BlueprintGraph/Classes/K2Node.h"' = '#include "K2Node.h"'

    # Elements que existem no UE 5.6 - CORRIGIDOS
    '#include "Elements/PCGTextureSampler.h"' = '#include "Elements/PCGTextureSample.h"'
    '#include "Elements/PCGSplineProjection.h"' = '#include "Elements/PCGProjectionElement.h"'
    '#include "Elements/PCGVolumeSampler.h"' = '#include "Elements/PCGVolumeSampler.h"'
    '#include "Elements/PCGVolumeToPoints.h"' = '#include "Elements/PCGVolumeSampler.h"'
    '#include "Elements/PCGSurfaceToPoints.h"' = '#include "Elements/PCGSurfaceSampler.h"'
    '#include "Elements/PCGSplineToPoints.h"' = '#include "Elements/PCGGetSplineControlPoints.h"'
    '#include "Elements/PCGSplineSampler.h"' = '#include "Elements/PCGGetSplineControlPoints.h"'
    '#include "Elements/PCGDensityNoise.h"' = '#include "Elements/PCGDensityFilter.h"'

    # Data types
    '#include "PCGPointData.h"' = '#include "Data/PCGPointData.h"'
    '#include "PCGMetadata.h"' = '#include "Metadata/PCGMetadata.h"'
    '#include "PCGVolumeData.h"' = '#include "Data/PCGVolumeData.h"'
    '#include "PCGSurfaceData.h"' = '#include "Data/PCGSurfaceData.h"'
    '#include "PCGLandscapeData.h"' = '#include "Data/PCGLandscapeData.h"'

    # ProceduralMeshComponent - REMOVIDO (não existe no UE 5.6)
    '#include "ProceduralMeshComponent/Public/ProceduralMeshComponent.h"' = '#include "Components/StaticMeshComponent.h"'
    '#include "ProceduralMeshComponent.h"' = '#include "Components/StaticMeshComponent.h"'

    # UObject Property - CORRIGIDO
    '#include "UObject/Property.h"' = '#include "UObject/PropertyPortFlags.h"'
    '#include "Elements/PCGPointFilter.h"' = '#include "Elements/PCGFilterByAttribute.h"'
    '#include "Elements/PCGPointTransform.h"' = '#include "Elements/PCGTransformPoints.h"'
    '#include "Elements/PCGPointMerger.h"' = '#include "Elements/PCGUnionElement.h"'
    '#include "Elements/PCGPointSplitter.h"' = '#include "Elements/PCGSplitPoints.h"'
    '#include "Elements/PCGAttributeReducer.h"' = '#include "Elements/PCGAttributeSelectElement.h"'
    '#include "Elements/PCGMeshSampler.h"' = '#include "Elements/PCGStaticMeshSpawner.h"'
    '#include "Elements/PCGLandscapeSplineSampler.h"' = '#include "Elements/PCGLandscapeSplineToPoints.h"'
    '#include "Elements/PCGPointGenerator.h"' = '#include "Elements/PCGCreatePoints.h"'
    '#include "Elements/PCGBoundsModifier.h"' = '#include "Elements/PCGBounds.h"'
    '#include "Elements/PCGDensityFilter.h"' = '#include "Elements/PCGDensityNoise.h"'
    '#include "Elements/PCGNormalToDensity.h"' = '#include "Elements/PCGNormalToVector.h"'
    '#include "Elements/PCGCopyPoints.h"' = '#include "Elements/PCGDuplicatePoints.h"'
    '#include "Elements/PCGPointDistance.h"' = '#include "Elements/PCGDistance.h"'
    '#include "Elements/PCGPointProjection.h"' = '#include "Elements/PCGProjectionElement.h"'
    '#include "Elements/PCGPointSampler.h"' = '#include "Elements/PCGSamplePoints.h"'
    '#include "Elements/PCGPointScatter.h"' = '#include "Elements/PCGScatterPoints.h"'
    '#include "Elements/PCGPointCluster.h"' = '#include "Elements/PCGClusterPoints.h"'
    '#include "Elements/PCGPointNoise.h"' = '#include "Elements/PCGAddNoise.h"'
    '#include "Elements/PCGPointSort.h"' = '#include "Elements/PCGSortPoints.h"'
    '#include "Elements/PCGPointDebug.h"' = '#include "Elements/PCGDebugElement.h"'
    '#include "Elements/PCGDataTableReader.h"' = '#include "Elements/PCGDataTableRowToParamData.h"'
    '#include "Elements/PCGWorldQuery.h"' = '#include "Elements/PCGWorldRayHitElement.h"'
    '#include "Elements/PCGCollisionShape.h"' = '#include "Elements/PCGCollisionShapeElement.h"'
    '#include "Elements/PCGPhysicsVolume.h"' = '#include "Elements/PCGPhysicsVolumeElement.h"'
    '#include "Elements/PCGActorSelector.h"' = '#include "Elements/PCGActorSelectorElement.h"'
    '#include "Elements/PCGComponentSelector.h"' = '#include "Elements/PCGComponentSelectorElement.h"'
    '#include "Elements/PCGLandscapeCache.h"' = '#include "Elements/PCGLandscapeElement.h"'
    '#include "Elements/PCGRenderTarget.h"' = '#include "Elements/PCGRenderTargetElement.h"'
    '#include "Elements/PCGTextureData.h"' = '#include "Data/PCGTextureData.h"'
    '#include "Elements/PCGVolumeData.h"' = '#include "Data/PCGVolumeData.h"'
    '#include "Elements/PCGLandscapeData.h"' = '#include "Data/PCGLandscapeData.h"'
    '#include "Elements/PCGSplineData.h"' = '#include "Data/PCGSplineData.h"'
    '#include "Elements/PCGPrimitiveData.h"' = '#include "Data/PCGPrimitiveData.h"'
    '#include "Elements/PCGPointData.h"' = '#include "Data/PCGPointData.h"'
    '#include "Elements/PCGSurfaceData.h"' = '#include "Data/PCGSurfaceData.h"'
    '#include "Elements/PCGRenderTargetData.h"' = '#include "Data/PCGRenderTargetData.h"'
    '#include "Elements/PCGCollisionData.h"' = '#include "Data/PCGCollisionShapeData.h"'
    '#include "Elements/PCGWorldData.h"' = '#include "Data/PCGWorldData.h"'
    '#include "Elements/PCGActorData.h"' = '#include "Data/PCGActorData.h"'
    '#include "Elements/PCGComponentData.h"' = '#include "Data/PCGComponentData.h"'
    '#include "Elements/PCGMeshData.h"' = '#include "Data/PCGMeshData.h"'
    '#include "Elements/PCGPolyLineData.h"' = '#include "Data/PCGPolyLineData.h"'
    '#include "Elements/PCGDifferenceData.h"' = '#include "Data/PCGDifferenceData.h"'
    '#include "Elements/PCGIntersectionData.h"' = '#include "Data/PCGIntersectionData.h"'
    '#include "Elements/PCGUnionData.h"' = '#include "Data/PCGUnionData.h"'
    '#include "Elements/PCGProjectionData.h"' = '#include "Data/PCGProjectionData.h"'
}

# Encontra todos os arquivos .h e .cpp
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h", "*.cpp" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalReplacements = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # Aplica todas as substituicoes
    foreach ($oldInclude in $includeMap.Keys) {
        $newInclude = $includeMap[$oldInclude]
        if ($content -match [regex]::Escape($oldInclude)) {
            $matches = [regex]::Matches($content, [regex]::Escape($oldInclude))
            $count = $matches.Count
            $content = $content -replace [regex]::Escape($oldInclude), $newInclude
            $fileReplacements += $count
            Write-Host "    Substituido: $oldInclude -> $newInclude ($count vezes)" -ForegroundColor Yellow
        }
    }
    
    # Salva o arquivo se houve mudancas
    if ($fileReplacements -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalReplacements += $fileReplacements
        $filesModified++
        Write-Host "  OK $fileReplacements substituicoes aplicadas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma substituicao necessaria" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de substituicoes: $totalReplacements" -ForegroundColor Green

if ($totalReplacements -gt 0) {
    Write-Host "`nCorrecao de includes concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma correcao de include necessaria." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
