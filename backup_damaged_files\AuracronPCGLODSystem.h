﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - LOD System Header
// Bridge 2.12: PCG Framework - LOD e Optimization

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGBasePointData.h"
#include "Data/PCGSpatialData.h"
#include "Metadata/PCGMetadata.h"

// Engine includes
#include "Engine/World.h"
// #include "Components/StaticMeshComponent.h" // Removido - nÃ£o necessÃ¡rio
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/LODActor.h"
#include "WorldPartition/HLOD/HLODLayer.h"
#include "WorldPartition/HLOD/HLODBuilder.h"
#include "Curves/CurveFloat.h"

#include "AuracronPCGLODSystem.generated.h"

// LOD generation modes
UENUM(BlueprintType)
enum class EAuracronPCGLODGenerationMode : uint8
{
    Automatic               UMETA(DisplayName = "Automatic"),
    DistanceBased           UMETA(DisplayName = "Distance Based"),
    ScreenSize              UMETA(DisplayName = "Screen Size"),
    VertexCount             UMETA(DisplayName = "Vertex Count"),
    TriangleCount           UMETA(DisplayName = "Triangle Count"),
    Custom                  UMETA(DisplayName = "Custom"),
    Nanite                  UMETA(DisplayName = "Nanite"),
    HLOD                    UMETA(DisplayName = "HLOD"),
    Impostor                UMETA(DisplayName = "Impostor")
};

// Culling modes
UENUM(BlueprintType)
enum class EAuracronPCGCullingMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    Distance                UMETA(DisplayName = "Distance"),
    Frustum                 UMETA(DisplayName = "Frustum"),
    Occlusion               UMETA(DisplayName = "Occlusion"),
    ScreenSize              UMETA(DisplayName = "Screen Size"),
    Combined                UMETA(DisplayName = "Combined"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Instancing optimization modes
UENUM(BlueprintType)
enum class EAuracronPCGInstancingMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    Static                  UMETA(DisplayName = "Static"),
    Hierarchical            UMETA(DisplayName = "Hierarchical"),
    Clustered               UMETA(DisplayName = "Clustered"),
    GPU                     UMETA(DisplayName = "GPU"),
    Nanite                  UMETA(DisplayName = "Nanite"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Performance profiling types
UENUM(BlueprintType)
enum class EAuracronPCGPerformanceMetric : uint8
{
    RenderTime              UMETA(DisplayName = "Render Time"),
    DrawCalls               UMETA(DisplayName = "Draw Calls"),
    TriangleCount           UMETA(DisplayName = "Triangle Count"),
    VertexCount             UMETA(DisplayName = "Vertex Count"),
    MemoryUsage             UMETA(DisplayName = "Memory Usage"),
    InstanceCount           UMETA(DisplayName = "Instance Count"),
    CulledInstances         UMETA(DisplayName = "Culled Instances"),
    LODLevel                UMETA(DisplayName = "LOD Level"),
    ScreenSize              UMETA(DisplayName = "Screen Size"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Mesh simplification algorithms
UENUM(BlueprintType)
enum class EAuracronPCGMeshSimplificationAlgorithm : uint8
{
    QuadricErrorMetrics     UMETA(DisplayName = "Quadric Error Metrics"),
    EdgeCollapse            UMETA(DisplayName = "Edge Collapse"),
    VertexClustering        UMETA(DisplayName = "Vertex Clustering"),
    Progressive             UMETA(DisplayName = "Progressive"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// LOD GENERATION DESCRIPTOR
// =============================================================================

/**
 * LOD Generation Descriptor
 * Describes parameters for LOD generation and optimization
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGLODGenerationDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    EAuracronPCGLODGenerationMode GenerationMode = EAuracronPCGLODGenerationMode::DistanceBased;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    int32 MaxLODLevels = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Based", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::DistanceBased"))
    TArray<float> LODDistances = {500.0f, 1000.0f, 2000.0f, 4000.0f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Screen Size", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::ScreenSize"))
    TArray<float> ScreenSizeThresholds = {0.5f, 0.25f, 0.125f, 0.0625f};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertex Count", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::VertexCount"))
    TArray<int32> VertexCountTargets = {1000, 500, 250, 125};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Triangle Count", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::TriangleCount"))
    TArray<int32> TriangleCountTargets = {500, 250, 125, 62};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    EAuracronPCGMeshSimplificationAlgorithm SimplificationAlgorithm = EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    float SimplificationQuality = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    bool bPreserveBoundaries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    bool bPreserveUVs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    bool bPreserveNormals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HLOD", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::HLOD"))
    TSoftObjectPtr<UHLODLayer> HLODLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HLOD", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::HLOD"))
    float HLODCellSize = 1600.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Nanite", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::Nanite"))
    bool bEnableNanite = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Nanite", meta = (EditCondition = "GenerationMode == EAuracronPCGLODGenerationMode::Nanite"))
    int32 NaniteTriangleThreshold = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bGenerateImpostors = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseDitheredLODTransition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float LODTransitionTime = 0.5f;

    FAuracronPCGLODGenerationDescriptor()
    {
        GenerationMode = EAuracronPCGLODGenerationMode::DistanceBased;
        MaxLODLevels = 4;
        LODDistances = {500.0f, 1000.0f, 2000.0f, 4000.0f};
        ScreenSizeThresholds = {0.5f, 0.25f, 0.125f, 0.0625f};
        VertexCountTargets = {1000, 500, 250, 125};
        TriangleCountTargets = {500, 250, 125, 62};
        SimplificationAlgorithm = EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics;
        SimplificationQuality = 0.8f;
        bPreserveBoundaries = true;
        bPreserveUVs = true;
        bPreserveNormals = true;
        HLODCellSize = 1600.0f;
        bEnableNanite = false;
        NaniteTriangleThreshold = 1000;
        bGenerateImpostors = false;
        bUseDitheredLODTransition = true;
        LODTransitionTime = 0.5f;
    }
};

// =============================================================================
// CULLING DESCRIPTOR
// =============================================================================

/**
 * Culling Descriptor
 * Describes parameters for culling optimization
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCullingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    EAuracronPCGCullingMode CullingMode = EAuracronPCGCullingMode::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Distance || CullingMode == EAuracronPCGCullingMode::Combined"))
    float MaxDrawDistance = 5000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Distance Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Distance || CullingMode == EAuracronPCGCullingMode::Combined"))
    float MinDrawDistance = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Frustum || CullingMode == EAuracronPCGCullingMode::Combined"))
    bool bEnableFrustumCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Frustum Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Frustum || CullingMode == EAuracronPCGCullingMode::Combined"))
    float FrustumCullingMargin = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Occlusion || CullingMode == EAuracronPCGCullingMode::Combined"))
    bool bEnableOcclusionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Occlusion Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::Occlusion || CullingMode == EAuracronPCGCullingMode::Combined"))
    float OcclusionCullingAccuracy = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Screen Size Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::ScreenSize || CullingMode == EAuracronPCGCullingMode::Combined"))
    float MinScreenSize = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Screen Size Culling", meta = (EditCondition = "CullingMode == EAuracronPCGCullingMode::ScreenSize || CullingMode == EAuracronPCGCullingMode::Combined"))
    bool bUseAdaptiveScreenSize = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnablePerInstanceCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableBatchedCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    int32 CullingBatchSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float CullingUpdateFrequency = 0.1f;

    FAuracronPCGCullingDescriptor()
    {
        CullingMode = EAuracronPCGCullingMode::Combined;
        MaxDrawDistance = 5000.0f;
        MinDrawDistance = 0.0f;
        bEnableFrustumCulling = true;
        FrustumCullingMargin = 100.0f;
        bEnableOcclusionCulling = true;
        OcclusionCullingAccuracy = 0.8f;
        MinScreenSize = 0.01f;
        bUseAdaptiveScreenSize = true;
        bEnablePerInstanceCulling = true;
        bEnableBatchedCulling = true;
        CullingBatchSize = 1000;
        CullingUpdateFrequency = 0.1f;
    }
};

// =============================================================================
// INSTANCING DESCRIPTOR
// =============================================================================

/**
 * Instancing Descriptor
 * Describes parameters for instancing optimization
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGInstancingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    EAuracronPCGInstancingMode InstancingMode = EAuracronPCGInstancingMode::Hierarchical;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    int32 MaxInstancesPerComponent = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    int32 MinInstancesForBatching = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hierarchical", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::Hierarchical"))
    int32 ClusterTreeDepth = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hierarchical", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::Hierarchical"))
    int32 MaxInstancesPerCluster = 16;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustered", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::Clustered"))
    float ClusterRadius = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustered", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::Clustered"))
    int32 MaxClustersPerComponent = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::GPU"))
    bool bEnableGPUCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GPU", meta = (EditCondition = "InstancingMode == EAuracronPCGInstancingMode::GPU"))
    int32 GPUCullingBatchSize = 64;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableInstanceMerging = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableInstanceSorting = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableInstanceCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float InstanceMergingThreshold = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseAsyncInstanceUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bEnableInstanceStreaming = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    int32 InstanceStreamingDistance = 2000;

    FAuracronPCGInstancingDescriptor()
    {
        InstancingMode = EAuracronPCGInstancingMode::Hierarchical;
        MaxInstancesPerComponent = 10000;
        MinInstancesForBatching = 10;
        ClusterTreeDepth = 10;
        MaxInstancesPerCluster = 16;
        ClusterRadius = 1000.0f;
        MaxClustersPerComponent = 100;
        bEnableGPUCulling = true;
        GPUCullingBatchSize = 64;
        bEnableInstanceMerging = true;
        bEnableInstanceSorting = true;
        bEnableInstanceCaching = true;
        InstanceMergingThreshold = 100.0f;
        bUseAsyncInstanceUpdates = true;
        bEnableInstanceStreaming = false;
        InstanceStreamingDistance = 2000;
    }
};

// =============================================================================
// PERFORMANCE PROFILING DESCRIPTOR
// =============================================================================

/**
 * Performance Profiling Descriptor
 * Describes parameters for performance monitoring and profiling
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGPerformanceProfilingDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    TArray<EAuracronPCGPerformanceMetric> MetricsToTrack;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bEnableRealTimeMonitoring = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    float ProfilingUpdateFrequency = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bLogPerformanceData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    bool bExportPerformanceData = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float MaxAcceptableRenderTime = 16.67f; // 60 FPS

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    int32 MaxAcceptableDrawCalls = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    int32 MaxAcceptableTriangles = 1000000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Thresholds")
    float MaxAcceptableMemoryUsage = 512.0f; // MB

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Alerts")
    bool bEnablePerformanceAlerts = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Alerts")
    bool bAutoOptimizeOnThresholdExceeded = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bTrackGPUPerformance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bTrackMemoryUsage = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    int32 PerformanceHistorySize = 100;

    FAuracronPCGPerformanceProfilingDescriptor()
    {
        MetricsToTrack = {
            EAuracronPCGPerformanceMetric::RenderTime,
            EAuracronPCGPerformanceMetric::DrawCalls,
            EAuracronPCGPerformanceMetric::TriangleCount,
            EAuracronPCGPerformanceMetric::MemoryUsage
        };
        bEnableRealTimeMonitoring = true;
        ProfilingUpdateFrequency = 1.0f;
        bLogPerformanceData = false;
        bExportPerformanceData = false;
        MaxAcceptableRenderTime = 16.67f;
        MaxAcceptableDrawCalls = 1000;
        MaxAcceptableTriangles = 1000000;
        MaxAcceptableMemoryUsage = 512.0f;
        bEnablePerformanceAlerts = true;
        bAutoOptimizeOnThresholdExceeded = false;
        bTrackGPUPerformance = true;
        bTrackMemoryUsage = true;
        PerformanceHistorySize = 100;
    }
};

// =============================================================================
// LOD GENERATOR
// =============================================================================

/**
 * LOD Generator
 * Generates Level of Detail meshes and configurations
 */

public:
    UAuracronPCGLODGeneratorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGLODGeneratorSettings();

    // LOD generation configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Generation")
    FAuracronPCGLODGenerationDescriptor LODDescriptor;

    // Source mesh configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source")
    bool bUseSourceMeshFromInput = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Source", meta = (EditCondition = "!bUseSourceMeshFromInput"))
    TSoftObjectPtr<UStaticMesh> SourceMesh;

    // LOD chain configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Chain")
    bool bGenerateFullLODChain = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Chain")
    bool bOptimizeLODTransitions = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD Chain")
    bool bValidateLODChain = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputLODMeshes = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputLODStatistics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString LODLevelAttribute = TEXT("LODLevel");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGLODGeneratorElement, UAuracronPCGLODGeneratorSettings)

// =============================================================================
// DISTANCE BASED CULLER
// =============================================================================

/**
 * Distance Based Culler
 * Culls instances based on distance and other criteria
 */

public:
    UAuracronPCGDistanceBasedCullerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGDistanceBasedCullerSettings();

    // Culling configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Culling")
    FAuracronPCGCullingDescriptor CullingDescriptor;

    // Reference point for distance calculations
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reference")
    bool bUseViewerLocation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reference", meta = (EditCondition = "!bUseViewerLocation"))
    FVector ReferenceLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Reference")
    bool bUpdateReferenceLocationDynamically = true;

    // Culling curves
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curves")
    bool bUseCullingCurve = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Curves", meta = (EditCondition = "bUseCullingCurve"))
    TSoftObjectPtr<UCurveFloat> CullingCurve;

    // Performance optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bCacheDistanceCalculations = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CacheUpdateFrequency = 0.1f;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputCullingInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString CullingStateAttribute = TEXT("CullingState");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString DistanceAttribute = TEXT("Distance");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGDistanceBasedCullerElement, UAuracronPCGDistanceBasedCullerSettings)

// =============================================================================
// INSTANCING OPTIMIZER
// =============================================================================

/**
 * Instancing Optimizer
 * Optimizes mesh instances for better performance
 */

public:
    UAuracronPCGInstancingOptimizerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGInstancingOptimizerSettings();

    // Instancing configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instancing")
    FAuracronPCGInstancingDescriptor InstancingDescriptor;

    // Mesh grouping
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping")
    bool bGroupByMesh = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping")
    bool bGroupByMaterial = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping")
    bool bGroupByLODLevel = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grouping")
    FString GroupingAttribute = TEXT("MeshGroup");

    // Instance optimization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bOptimizeInstanceTransforms = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bRemoveDuplicateInstances = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float DuplicateThreshold = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bSortInstancesByDistance = true;

    // Component creation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components")
    bool bCreateInstancedComponents = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components")
    bool bUseHierarchicalInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Components")
    bool bEnableGPUCulling = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputInstanceComponents = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputOptimizationStats = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString InstanceCountAttribute = TEXT("InstanceCount");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGInstancingOptimizerElement, UAuracronPCGInstancingOptimizerSettings)

// =============================================================================
// PERFORMANCE PROFILER
// =============================================================================

/**
 * Performance Profiler
 * Profiles and monitors performance metrics
 */

public:
    UAuracronPCGPerformanceProfilerSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGPerformanceProfilerSettings();

    // Profiling configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Profiling")
    FAuracronPCGPerformanceProfilingDescriptor ProfilingDescriptor;

    // Profiling scope
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scope")
    bool bProfileEntireGraph = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scope")
    bool bProfileCurrentNode = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scope")
    bool bProfileRenderingPipeline = true;

    // Data collection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Collection")
    bool bCollectFrameData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Collection")
    bool bCollectMemoryData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Collection")
    bool bCollectGPUData = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Collection")
    int32 DataCollectionFrames = 60;

    // Analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    bool bPerformStatisticalAnalysis = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    bool bDetectPerformanceBottlenecks = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    bool bGenerateOptimizationSuggestions = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputPerformanceReport = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputRawMetrics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString PerformanceScoreAttribute = TEXT("PerformanceScore");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGPerformanceProfilerElement, UAuracronPCGPerformanceProfilerSettings)

// =============================================================================
// MESH SIMPLIFIER
// =============================================================================

/**
 * Mesh Simplifier
 * Simplifies meshes for LOD generation
 */

public:
    UAuracronPCGMeshSimplifierSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGMeshSimplifierSettings();

    // Simplification algorithm
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simplification")
    EAuracronPCGMeshSimplificationAlgorithm SimplificationAlgorithm = EAuracronPCGMeshSimplificationAlgorithm::QuadricErrorMetrics;

    // Target reduction
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
    bool bUsePercentageReduction = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target", meta = (EditCondition = "bUsePercentageReduction"))
    float ReductionPercentage = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target", meta = (EditCondition = "!bUsePercentageReduction"))
    int32 TargetTriangleCount = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target", meta = (EditCondition = "!bUsePercentageReduction"))
    int32 TargetVertexCount = 1000;

    // Quality settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    float SimplificationQuality = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bPreserveBoundaries = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bPreserveUVSeams = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bPreserveNormals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    bool bPreserveMaterialBoundaries = true;

    // Advanced settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    float EdgeCollapseThreshold = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseAdaptiveSimplification = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bOptimizeForGPU = true;

    // Output settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    bool bOutputSimplificationStats = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Output")
    FString ReductionRatioAttribute = TEXT("ReductionRatio");

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGMeshSimplifierElement, UAuracronPCGMeshSimplifierSettings)

// =============================================================================
// LOD SYSTEM UTILITIES
// =============================================================================

/**
 * LOD System Utilities
 * Utility functions for LOD and optimization operations
 */

public:
    // LOD generation utilities
    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static TArray<UStaticMesh*> GenerateLODChain(UStaticMesh* SourceMesh, const FAuracronPCGLODGenerationDescriptor& LODDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static UStaticMesh* SimplifyMesh(UStaticMesh* SourceMesh, float ReductionPercentage, EAuracronPCGMeshSimplificationAlgorithm Algorithm);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static int32 CalculateOptimalLODLevel(const FVector& ViewerLocation, const FVector& ObjectLocation, const TArray<float>& LODDistances);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static float CalculateScreenSize(const FVector& ViewerLocation, const FVector& ObjectLocation, const FVector& ObjectBounds, float FOV);

    // Culling utilities
    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static bool ShouldCullInstance(const FVector& InstanceLocation, const FVector& ViewerLocation, const FAuracronPCGCullingDescriptor& CullingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static TArray<int32> PerformBatchCulling(const TArray<FVector>& InstanceLocations, const FVector& ViewerLocation, const FAuracronPCGCullingDescriptor& CullingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static bool IsInFrustum(const FVector& Location, const FVector& ViewerLocation, const FVector& ViewDirection, float FOV, float AspectRatio);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static float CalculateOcclusionFactor(const FVector& Location, const FVector& ViewerLocation, UWorld* World);

    // Instancing utilities
    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static UInstancedStaticMeshComponent* CreateOptimizedInstancedComponent(AActor* Owner, UStaticMesh* Mesh, const FAuracronPCGInstancingDescriptor& InstancingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static UHierarchicalInstancedStaticMeshComponent* CreateHierarchicalInstancedComponent(AActor* Owner, UStaticMesh* Mesh, const FAuracronPCGInstancingDescriptor& InstancingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static void OptimizeInstanceTransforms(TArray<FTransform>& Transforms, float MergingThreshold);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static TArray<FTransform> RemoveDuplicateInstances(const TArray<FTransform>& Transforms, float Threshold);

    // Performance utilities
    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static float MeasureRenderTime(UWorld* World, const TArray<UPrimitiveComponent*>& Components);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static int32 CountDrawCalls(const TArray<UPrimitiveComponent*>& Components);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static int32 CountTriangles(UStaticMesh* Mesh, int32 LODLevel = 0);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static float CalculateMemoryUsage(const TArray<UPrimitiveComponent*>& Components);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static float CalculatePerformanceScore(float RenderTime, int32 DrawCalls, int32 TriangleCount, float MemoryUsage);

    // Validation utilities
    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static bool ValidateLODGenerationDescriptor(const FAuracronPCGLODGenerationDescriptor& LODDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static bool ValidateCullingDescriptor(const FAuracronPCGCullingDescriptor& CullingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static bool ValidateInstancingDescriptor(const FAuracronPCGInstancingDescriptor& InstancingDescriptor);

    UFUNCTION(BlueprintCallable, Category = "LOD System Utils")
    static FAuracronPCGLODGenerationDescriptor CreateDefaultLODDescriptor(EAuracronPCGLODGenerationMode GenerationMode);
};

// Namespace for LOD system utility functions
namespace AuracronPCGLODSystemUtils
{
    AURACRONPCGBRIDGE_API UStaticMesh* LoadMeshSafe(const TSoftObjectPtr<UStaticMesh>& MeshPtr);
    AURACRONPCGBRIDGE_API UCurveFloat* LoadCurveSafe(const TSoftObjectPtr<UCurveFloat>& CurvePtr);
    AURACRONPCGBRIDGE_API UHLODLayer* LoadHLODLayerSafe(const TSoftObjectPtr<UHLODLayer>& HLODLayerPtr);

    AURACRONPCGBRIDGE_API float CalculateQuadricError(const FVector& Vertex, const TArray<FVector4>& Planes);
    AURACRONPCGBRIDGE_API TArray<FVector4> CalculateVertexPlanes(const FVector& Vertex, const TArray<FVector>& AdjacentVertices, const TArray<FVector>& FaceNormals);
    AURACRONPCGBRIDGE_API bool CanCollapseEdge(const FVector& VertexA, const FVector& VertexB, float Threshold);

    AURACRONPCGBRIDGE_API void SortInstancesByDistance(TArray<FTransform>& Transforms, const FVector& ReferenceLocation);
    AURACRONPCGBRIDGE_API TArray<TArray<FTransform>> ClusterInstances(const TArray<FTransform>& Transforms, float ClusterRadius, int32 MaxClustersPerComponent);
    AURACRONPCGBRIDGE_API void OptimizeInstanceBatches(TArray<FTransform>& Transforms, int32 MaxInstancesPerBatch);

    AURACRONPCGBRIDGE_API float CalculateDistanceCullingFactor(float Distance, float MinDistance, float MaxDistance);
    AURACRONPCGBRIDGE_API float CalculateScreenSizeCullingFactor(float ScreenSize, float MinScreenSize);
    AURACRONPCGBRIDGE_API bool IsOccluded(const FVector& Location, const FVector& ViewerLocation, UWorld* World, float Accuracy);

    AURACRONPCGBRIDGE_API void EnableNaniteForMesh(UStaticMesh* Mesh, int32 TriangleThreshold);
    AURACRONPCGBRIDGE_API void ConfigureHLODSettings(UStaticMesh* Mesh, const FAuracronPCGLODGenerationDescriptor& LODDescriptor);
    AURACRONPCGBRIDGE_API UTexture2D* GenerateImpostorTexture(UStaticMesh* Mesh, int32 TextureSize);

    AURACRONPCGBRIDGE_API FString GeneratePerformanceReport(const TArray<float>& RenderTimes, const TArray<int32>& DrawCalls, const TArray<int32>& TriangleCounts);
    AURACRONPCGBRIDGE_API TArray<FString> GenerateOptimizationSuggestions(float RenderTime, int32 DrawCalls, int32 TriangleCount, float MemoryUsage);
    AURACRONPCGBRIDGE_API void ExportPerformanceData(const FString& FilePath, const TMap<FString, float>& PerformanceData);
}
