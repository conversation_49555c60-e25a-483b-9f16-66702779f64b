// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "CommunityHealingManager.h"
#include "Engine/TimerHandle.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeCommunityHealingManager() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UCommunityHealingManager();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UCommunityHealingManager_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHealerProfile();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHealingSession();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerHealingHistory();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EHealingSessionType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHealingSessionType;
static UEnum* EHealingSessionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHealingSessionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHealingSessionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EHealingSessionType"));
	}
	return Z_Registration_Info_UEnum_EHealingSessionType.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHealingSessionType>()
{
	return EHealingSessionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CelebrationCircle.DisplayName", "Celebration Circle" },
		{ "CelebrationCircle.Name", "EHealingSessionType::CelebrationCircle" },
		{ "CrisisIntervention.DisplayName", "Crisis Intervention" },
		{ "CrisisIntervention.Name", "EHealingSessionType::CrisisIntervention" },
		{ "GroupTherapy.DisplayName", "Group Therapy" },
		{ "GroupTherapy.Name", "EHealingSessionType::GroupTherapy" },
		{ "Mentorship.DisplayName", "Mentorship" },
		{ "Mentorship.Name", "EHealingSessionType::Mentorship" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
		{ "PeerSupport.DisplayName", "Peer Support" },
		{ "PeerSupport.Name", "EHealingSessionType::PeerSupport" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHealingSessionType::PeerSupport", (int64)EHealingSessionType::PeerSupport },
		{ "EHealingSessionType::Mentorship", (int64)EHealingSessionType::Mentorship },
		{ "EHealingSessionType::GroupTherapy", (int64)EHealingSessionType::GroupTherapy },
		{ "EHealingSessionType::CrisisIntervention", (int64)EHealingSessionType::CrisisIntervention },
		{ "EHealingSessionType::CelebrationCircle", (int64)EHealingSessionType::CelebrationCircle },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EHealingSessionType",
	"EHealingSessionType",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType()
{
	if (!Z_Registration_Info_UEnum_EHealingSessionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHealingSessionType.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHealingSessionType.InnerSingleton;
}
// ********** End Enum EHealingSessionType *********************************************************

// ********** Begin Enum EHealingStatus ************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHealingStatus;
static UEnum* EHealingStatus_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHealingStatus.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHealingStatus.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EHealingStatus"));
	}
	return Z_Registration_Info_UEnum_EHealingStatus.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHealingStatus>()
{
	return EHealingStatus_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EHealingStatus::Active" },
		{ "BlueprintType", "true" },
		{ "Cancelled.DisplayName", "Cancelled" },
		{ "Cancelled.Name", "EHealingStatus::Cancelled" },
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "EHealingStatus::Completed" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EHealingStatus::Failed" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EHealingStatus::Pending" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHealingStatus::Pending", (int64)EHealingStatus::Pending },
		{ "EHealingStatus::Active", (int64)EHealingStatus::Active },
		{ "EHealingStatus::Completed", (int64)EHealingStatus::Completed },
		{ "EHealingStatus::Cancelled", (int64)EHealingStatus::Cancelled },
		{ "EHealingStatus::Failed", (int64)EHealingStatus::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EHealingStatus",
	"EHealingStatus",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus()
{
	if (!Z_Registration_Info_UEnum_EHealingStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHealingStatus.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHealingStatus.InnerSingleton;
}
// ********** End Enum EHealingStatus **************************************************************

// ********** Begin ScriptStruct FHealingSession ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHealingSession;
class UScriptStruct* FHealingSession::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHealingSession.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHealingSession.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHealingSession, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("HealingSession"));
	}
	return Z_Registration_Info_UScriptStruct_FHealingSession.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHealingSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionType_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Status_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictimPlayerID_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerPlayerIDs_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndTime_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingProgress_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingGoal_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionTags_MetaData[] = {
		{ "Category", "HealingSession" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Status_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Status;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VictimPlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealerPlayerIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HealerPlayerIDs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingProgress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealingGoal;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHealingSession>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, SessionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionType_MetaData), NewProp_SessionType_MetaData) }; // 2683699308
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_Status_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_Status = { "Status", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, Status), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingStatus, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Status_MetaData), NewProp_Status_MetaData) }; // 3798488262
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_VictimPlayerID = { "VictimPlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, VictimPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictimPlayerID_MetaData), NewProp_VictimPlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealerPlayerIDs_Inner = { "HealerPlayerIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealerPlayerIDs = { "HealerPlayerIDs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, HealerPlayerIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerPlayerIDs_MetaData), NewProp_HealerPlayerIDs_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_EndTime = { "EndTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, EndTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndTime_MetaData), NewProp_EndTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealingProgress = { "HealingProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, HealingProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingProgress_MetaData), NewProp_HealingProgress_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealingGoal = { "HealingGoal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, HealingGoal), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingGoal_MetaData), NewProp_HealingGoal_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionTags = { "SessionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealingSession, SessionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionTags_MetaData), NewProp_SessionTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_Status_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_Status,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_VictimPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealerPlayerIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealerPlayerIDs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_EndTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_HealingGoal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealingSession_Statics::NewProp_SessionTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHealingSession_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"HealingSession",
	Z_Construct_UScriptStruct_FHealingSession_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealingSession_Statics::PropPointers),
	sizeof(FHealingSession),
	alignof(FHealingSession),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealingSession_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHealingSession_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHealingSession()
{
	if (!Z_Registration_Info_UScriptStruct_FHealingSession.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHealingSession.InnerSingleton, Z_Construct_UScriptStruct_FHealingSession_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHealingSession.InnerSingleton;
}
// ********** End ScriptStruct FHealingSession *****************************************************

// ********** Begin ScriptStruct FHealerProfile ****************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHealerProfile;
class UScriptStruct* FHealerProfile::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHealerProfile.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHealerProfile.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHealerProfile, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("HealerProfile"));
	}
	return Z_Registration_Info_UScriptStruct_FHealerProfile.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHealerProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealingSkillLevel_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessfulHealingSessions_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageSessionRating_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specializations_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAvailable_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastActiveTime_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerTags_MetaData[] = {
		{ "Category", "HealerProfile" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealingSkillLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SuccessfulHealingSessions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageSessionRating;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Specializations_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Specializations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Specializations;
	static void NewProp_bIsAvailable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAvailable;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastActiveTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HealerTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHealerProfile>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_HealingSkillLevel = { "HealingSkillLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, HealingSkillLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealingSkillLevel_MetaData), NewProp_HealingSkillLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_SuccessfulHealingSessions = { "SuccessfulHealingSessions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, SuccessfulHealingSessions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessfulHealingSessions_MetaData), NewProp_SuccessfulHealingSessions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_AverageSessionRating = { "AverageSessionRating", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, AverageSessionRating), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageSessionRating_MetaData), NewProp_AverageSessionRating_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations_Inner = { "Specializations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations = { "Specializations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, Specializations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specializations_MetaData), NewProp_Specializations_MetaData) }; // 2683699308
void Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_bIsAvailable_SetBit(void* Obj)
{
	((FHealerProfile*)Obj)->bIsAvailable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_bIsAvailable = { "bIsAvailable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHealerProfile), &Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_bIsAvailable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAvailable_MetaData), NewProp_bIsAvailable_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_LastActiveTime = { "LastActiveTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, LastActiveTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastActiveTime_MetaData), NewProp_LastActiveTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_HealerTags = { "HealerTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHealerProfile, HealerTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerTags_MetaData), NewProp_HealerTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHealerProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_HealingSkillLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_SuccessfulHealingSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_AverageSessionRating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_Specializations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_bIsAvailable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_LastActiveTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHealerProfile_Statics::NewProp_HealerTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealerProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHealerProfile_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"HealerProfile",
	Z_Construct_UScriptStruct_FHealerProfile_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealerProfile_Statics::PropPointers),
	sizeof(FHealerProfile),
	alignof(FHealerProfile),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHealerProfile_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHealerProfile_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHealerProfile()
{
	if (!Z_Registration_Info_UScriptStruct_FHealerProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHealerProfile.InnerSingleton, Z_Construct_UScriptStruct_FHealerProfile_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHealerProfile.InnerSingleton;
}
// ********** End ScriptStruct FHealerProfile ******************************************************

// ********** Begin ScriptStruct FPlayerHealingHistory *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerHealingHistory;
class UScriptStruct* FPlayerHealingHistory::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerHealingHistory, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("PlayerHealingHistory"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Wrapper struct for TArray to use in TMap with UPROPERTY\n */" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wrapper struct for TArray to use in TMap with UPROPERTY" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionIDs_MetaData[] = {
		{ "Category", "PlayerHealingHistory" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SessionIDs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerHealingHistory>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::NewProp_SessionIDs_Inner = { "SessionIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::NewProp_SessionIDs = { "SessionIDs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerHealingHistory, SessionIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionIDs_MetaData), NewProp_SessionIDs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::NewProp_SessionIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::NewProp_SessionIDs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"PlayerHealingHistory",
	Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::PropPointers),
	sizeof(FPlayerHealingHistory),
	alignof(FPlayerHealingHistory),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerHealingHistory()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.InnerSingleton, Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerHealingHistory.InnerSingleton;
}
// ********** End ScriptStruct FPlayerHealingHistory ***********************************************

// ********** Begin Class UCommunityHealingManager Function AddHealerToSession *********************
struct Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics
{
	struct CommunityHealingManager_eventAddHealerToSession_Parms
	{
		FString SessionID;
		FString HealerPlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealerPlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventAddHealerToSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_HealerPlayerID = { "HealerPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventAddHealerToSession_Parms, HealerPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerPlayerID_MetaData), NewProp_HealerPlayerID_MetaData) };
void Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((CommunityHealingManager_eventAddHealerToSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(CommunityHealingManager_eventAddHealerToSession_Parms), &Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_HealerPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "AddHealerToSession", Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::CommunityHealingManager_eventAddHealerToSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::CommunityHealingManager_eventAddHealerToSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execAddHealerToSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_HealerPlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddHealerToSession(Z_Param_SessionID,Z_Param_HealerPlayerID);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function AddHealerToSession ***********************

// ********** Begin Class UCommunityHealingManager Function CancelHealingSession *******************
struct Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics
{
	struct CommunityHealingManager_eventCancelHealingSession_Parms
	{
		FString SessionID;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventCancelHealingSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventCancelHealingSession_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "CancelHealingSession", Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::CommunityHealingManager_eventCancelHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::CommunityHealingManager_eventCancelHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execCancelHealingSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelHealingSession(Z_Param_SessionID,Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function CancelHealingSession *********************

// ********** Begin Class UCommunityHealingManager Function CompleteHealingSession *****************
struct Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics
{
	struct CommunityHealingManager_eventCompleteHealingSession_Parms
	{
		FString SessionID;
		float SuccessRating;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuccessRating;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventCompleteHealingSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_SuccessRating = { "SuccessRating", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventCompleteHealingSession_Parms, SuccessRating), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((CommunityHealingManager_eventCompleteHealingSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(CommunityHealingManager_eventCompleteHealingSession_Parms), &Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_SuccessRating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "CompleteHealingSession", Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::CommunityHealingManager_eventCompleteHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::CommunityHealingManager_eventCompleteHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execCompleteHealingSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SuccessRating);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CompleteHealingSession(Z_Param_SessionID,Z_Param_SuccessRating);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function CompleteHealingSession *******************

// ********** Begin Class UCommunityHealingManager Function FindAvailableHealers *******************
struct Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics
{
	struct CommunityHealingManager_eventFindAvailableHealers_Parms
	{
		EHealingSessionType SessionType;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventFindAvailableHealers_Parms, SessionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventFindAvailableHealers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "FindAvailableHealers", Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::CommunityHealingManager_eventFindAvailableHealers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::CommunityHealingManager_eventFindAvailableHealers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execFindAvailableHealers)
{
	P_GET_ENUM(EHealingSessionType,Z_Param_SessionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->FindAvailableHealers(EHealingSessionType(Z_Param_SessionType));
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function FindAvailableHealers *********************

// ********** Begin Class UCommunityHealingManager Function FindBestMatchedHealer ******************
struct Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics
{
	struct CommunityHealingManager_eventFindBestMatchedHealer_Parms
	{
		FString VictimPlayerID;
		EHealingSessionType SessionType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictimPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VictimPlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_VictimPlayerID = { "VictimPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventFindBestMatchedHealer_Parms, VictimPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictimPlayerID_MetaData), NewProp_VictimPlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventFindBestMatchedHealer_Parms, SessionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventFindBestMatchedHealer_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_VictimPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "FindBestMatchedHealer", Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::CommunityHealingManager_eventFindBestMatchedHealer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::CommunityHealingManager_eventFindBestMatchedHealer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execFindBestMatchedHealer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VictimPlayerID);
	P_GET_ENUM(EHealingSessionType,Z_Param_SessionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->FindBestMatchedHealer(Z_Param_VictimPlayerID,EHealingSessionType(Z_Param_SessionType));
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function FindBestMatchedHealer ********************

// ********** Begin Class UCommunityHealingManager Function GetActiveHealingSessions ***************
struct Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics
{
	struct CommunityHealingManager_eventGetActiveHealingSessions_Parms
	{
		TArray<FHealingSession> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Session Management\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHealingSession, METADATA_PARAMS(0, nullptr) }; // 2339623
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetActiveHealingSessions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2339623
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetActiveHealingSessions", Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::CommunityHealingManager_eventGetActiveHealingSessions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::CommunityHealingManager_eventGetActiveHealingSessions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetActiveHealingSessions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FHealingSession>*)Z_Param__Result=P_THIS->GetActiveHealingSessions();
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetActiveHealingSessions *****************

// ********** Begin Class UCommunityHealingManager Function GetCommunityHealingEffectiveness *******
struct Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics
{
	struct CommunityHealingManager_eventGetCommunityHealingEffectiveness_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Analytics and Reporting\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics and Reporting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetCommunityHealingEffectiveness_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetCommunityHealingEffectiveness", Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::CommunityHealingManager_eventGetCommunityHealingEffectiveness_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::CommunityHealingManager_eventGetCommunityHealingEffectiveness_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetCommunityHealingEffectiveness)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCommunityHealingEffectiveness();
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetCommunityHealingEffectiveness *********

// ********** Begin Class UCommunityHealingManager Function GetHealerRating ************************
struct Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics
{
	struct CommunityHealingManager_eventGetHealerRating_Parms
	{
		FString HealerPlayerID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealerPlayerID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::NewProp_HealerPlayerID = { "HealerPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetHealerRating_Parms, HealerPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerPlayerID_MetaData), NewProp_HealerPlayerID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetHealerRating_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::NewProp_HealerPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetHealerRating", Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::CommunityHealingManager_eventGetHealerRating_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::CommunityHealingManager_eventGetHealerRating_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetHealerRating)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_HealerPlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealerRating(Z_Param_HealerPlayerID);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetHealerRating **************************

// ********** Begin Class UCommunityHealingManager Function GetHealingSession **********************
struct Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics
{
	struct CommunityHealingManager_eventGetHealingSession_Parms
	{
		FString SessionID;
		FHealingSession ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetHealingSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetHealingSession_Parms, ReturnValue), Z_Construct_UScriptStruct_FHealingSession, METADATA_PARAMS(0, nullptr) }; // 2339623
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetHealingSession", Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::CommunityHealingManager_eventGetHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::CommunityHealingManager_eventGetHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetHealingSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FHealingSession*)Z_Param__Result=P_THIS->GetHealingSession(Z_Param_SessionID);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetHealingSession ************************

// ********** Begin Class UCommunityHealingManager Function GetTopHealers **************************
struct Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics
{
	struct CommunityHealingManager_eventGetTopHealers_Parms
	{
		int32 Count;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "CPP_Default_Count", "10" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetTopHealers_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetTopHealers_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetTopHealers", Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::CommunityHealingManager_eventGetTopHealers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::CommunityHealingManager_eventGetTopHealers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetTopHealers)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetTopHealers(Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetTopHealers ****************************

// ********** Begin Class UCommunityHealingManager Function GetTotalHealingSessionsCompleted *******
struct Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics
{
	struct CommunityHealingManager_eventGetTotalHealingSessionsCompleted_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventGetTotalHealingSessionsCompleted_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "GetTotalHealingSessionsCompleted", Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::CommunityHealingManager_eventGetTotalHealingSessionsCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::CommunityHealingManager_eventGetTotalHealingSessionsCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execGetTotalHealingSessionsCompleted)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalHealingSessionsCompleted();
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function GetTotalHealingSessionsCompleted *********

// ********** Begin Class UCommunityHealingManager Function InitiateHealingSession *****************
struct Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics
{
	struct CommunityHealingManager_eventInitiateHealingSession_Parms
	{
		FString VictimPlayerID;
		EHealingSessionType SessionType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Healing Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Healing Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictimPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VictimPlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_VictimPlayerID = { "VictimPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventInitiateHealingSession_Parms, VictimPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictimPlayerID_MetaData), NewProp_VictimPlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventInitiateHealingSession_Parms, SessionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventInitiateHealingSession_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_VictimPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "InitiateHealingSession", Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::CommunityHealingManager_eventInitiateHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::CommunityHealingManager_eventInitiateHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execInitiateHealingSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VictimPlayerID);
	P_GET_ENUM(EHealingSessionType,Z_Param_SessionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->InitiateHealingSession(Z_Param_VictimPlayerID,EHealingSessionType(Z_Param_SessionType));
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function InitiateHealingSession *******************

// ********** Begin Class UCommunityHealingManager Function IsPlayerInHealingSession ***************
struct Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics
{
	struct CommunityHealingManager_eventIsPlayerInHealingSession_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventIsPlayerInHealingSession_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((CommunityHealingManager_eventIsPlayerInHealingSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(CommunityHealingManager_eventIsPlayerInHealingSession_Parms), &Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "IsPlayerInHealingSession", Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::CommunityHealingManager_eventIsPlayerInHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::CommunityHealingManager_eventIsPlayerInHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execIsPlayerInHealingSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerInHealingSession(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function IsPlayerInHealingSession *****************

// ********** Begin Class UCommunityHealingManager Function RegisterHealer *************************
struct Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics
{
	struct CommunityHealingManager_eventRegisterHealer_Parms
	{
		FString PlayerID;
		TArray<EHealingSessionType> Specializations;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Healer Management\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Healer Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specializations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Specializations_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Specializations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Specializations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventRegisterHealer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations_Inner = { "Specializations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations = { "Specializations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventRegisterHealer_Parms, Specializations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specializations_MetaData), NewProp_Specializations_MetaData) }; // 2683699308
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::NewProp_Specializations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "RegisterHealer", Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::CommunityHealingManager_eventRegisterHealer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::CommunityHealingManager_eventRegisterHealer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execRegisterHealer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_TARRAY_REF(EHealingSessionType,Z_Param_Out_Specializations);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterHealer(Z_Param_PlayerID,Z_Param_Out_Specializations);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function RegisterHealer ***************************

// ********** Begin Class UCommunityHealingManager Function UnregisterHealer ***********************
struct Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics
{
	struct CommunityHealingManager_eventUnregisterHealer_Parms
	{
		FString PlayerID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventUnregisterHealer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "UnregisterHealer", Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::CommunityHealingManager_eventUnregisterHealer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::CommunityHealingManager_eventUnregisterHealer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execUnregisterHealer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterHealer(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function UnregisterHealer *************************

// ********** Begin Class UCommunityHealingManager Function UpdateHealingProgress ******************
struct Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics
{
	struct CommunityHealingManager_eventUpdateHealingProgress_Parms
	{
		FString SessionID;
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Community Healing" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventUpdateHealingProgress_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CommunityHealingManager_eventUpdateHealingProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UCommunityHealingManager, nullptr, "UpdateHealingProgress", Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::CommunityHealingManager_eventUpdateHealingProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::CommunityHealingManager_eventUpdateHealingProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCommunityHealingManager::execUpdateHealingProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateHealingProgress(Z_Param_SessionID,Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UCommunityHealingManager Function UpdateHealingProgress ********************

// ********** Begin Class UCommunityHealingManager *************************************************
void UCommunityHealingManager::StaticRegisterNativesUCommunityHealingManager()
{
	UClass* Class = UCommunityHealingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddHealerToSession", &UCommunityHealingManager::execAddHealerToSession },
		{ "CancelHealingSession", &UCommunityHealingManager::execCancelHealingSession },
		{ "CompleteHealingSession", &UCommunityHealingManager::execCompleteHealingSession },
		{ "FindAvailableHealers", &UCommunityHealingManager::execFindAvailableHealers },
		{ "FindBestMatchedHealer", &UCommunityHealingManager::execFindBestMatchedHealer },
		{ "GetActiveHealingSessions", &UCommunityHealingManager::execGetActiveHealingSessions },
		{ "GetCommunityHealingEffectiveness", &UCommunityHealingManager::execGetCommunityHealingEffectiveness },
		{ "GetHealerRating", &UCommunityHealingManager::execGetHealerRating },
		{ "GetHealingSession", &UCommunityHealingManager::execGetHealingSession },
		{ "GetTopHealers", &UCommunityHealingManager::execGetTopHealers },
		{ "GetTotalHealingSessionsCompleted", &UCommunityHealingManager::execGetTotalHealingSessionsCompleted },
		{ "InitiateHealingSession", &UCommunityHealingManager::execInitiateHealingSession },
		{ "IsPlayerInHealingSession", &UCommunityHealingManager::execIsPlayerInHealingSession },
		{ "RegisterHealer", &UCommunityHealingManager::execRegisterHealer },
		{ "UnregisterHealer", &UCommunityHealingManager::execUnregisterHealer },
		{ "UpdateHealingProgress", &UCommunityHealingManager::execUpdateHealingProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UCommunityHealingManager;
UClass* UCommunityHealingManager::GetPrivateStaticClass()
{
	using TClass = UCommunityHealingManager;
	if (!Z_Registration_Info_UClass_UCommunityHealingManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("CommunityHealingManager"),
			Z_Registration_Info_UClass_UCommunityHealingManager.InnerSingleton,
			StaticRegisterNativesUCommunityHealingManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UCommunityHealingManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UCommunityHealingManager_NoRegister()
{
	return UCommunityHealingManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UCommunityHealingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Community Healing Manager\n * Manages peer support, mentorship programs, and community healing initiatives\n */" },
#endif
		{ "IncludePath", "CommunityHealingManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Community Healing Manager\nManages peer support, mentorship programs, and community healing initiatives" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentSessions_MetaData[] = {
		{ "Category", "Healing Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSessionDuration_MetaData[] = {
		{ "Category", "Healing Config" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinHealerSkillLevel_MetaData[] = {
		{ "Category", "Healing Config" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealersPerSession_MetaData[] = {
		{ "Category", "Healing Config" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticMatching_MetaData[] = {
		{ "Category", "Healing Config" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableSessionRecording_MetaData[] = {
		{ "Category", "Healing Config" },
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSessions_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data Storage\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredHealers_MetaData[] = {
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedSessions_MetaData[] = {
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerHealingHistory_MetaData[] = {
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Session Timers\n" },
#endif
		{ "ModuleRelativePath", "Public/CommunityHealingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session Timers" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentSessions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSessionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinHealerSkillLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxHealersPerSession;
	static void NewProp_bEnableAutomaticMatching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticMatching;
	static void NewProp_bEnableSessionRecording_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSessionRecording;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveSessions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveSessions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveSessions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredHealers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegisteredHealers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredHealers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedSessions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedSessions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerHealingHistory_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerHealingHistory_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerHealingHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionTimers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SessionTimers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UCommunityHealingManager_AddHealerToSession, "AddHealerToSession" }, // 3121537908
		{ &Z_Construct_UFunction_UCommunityHealingManager_CancelHealingSession, "CancelHealingSession" }, // 2035645829
		{ &Z_Construct_UFunction_UCommunityHealingManager_CompleteHealingSession, "CompleteHealingSession" }, // 1269928502
		{ &Z_Construct_UFunction_UCommunityHealingManager_FindAvailableHealers, "FindAvailableHealers" }, // 2220344400
		{ &Z_Construct_UFunction_UCommunityHealingManager_FindBestMatchedHealer, "FindBestMatchedHealer" }, // 2194900538
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetActiveHealingSessions, "GetActiveHealingSessions" }, // 812694362
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetCommunityHealingEffectiveness, "GetCommunityHealingEffectiveness" }, // 1578834804
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetHealerRating, "GetHealerRating" }, // 4044694388
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetHealingSession, "GetHealingSession" }, // 2837161054
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetTopHealers, "GetTopHealers" }, // 3015522008
		{ &Z_Construct_UFunction_UCommunityHealingManager_GetTotalHealingSessionsCompleted, "GetTotalHealingSessionsCompleted" }, // 2264709517
		{ &Z_Construct_UFunction_UCommunityHealingManager_InitiateHealingSession, "InitiateHealingSession" }, // 3939985810
		{ &Z_Construct_UFunction_UCommunityHealingManager_IsPlayerInHealingSession, "IsPlayerInHealingSession" }, // 2525341934
		{ &Z_Construct_UFunction_UCommunityHealingManager_RegisterHealer, "RegisterHealer" }, // 2289099526
		{ &Z_Construct_UFunction_UCommunityHealingManager_UnregisterHealer, "UnregisterHealer" }, // 3917873529
		{ &Z_Construct_UFunction_UCommunityHealingManager_UpdateHealingProgress, "UpdateHealingProgress" }, // 3302367239
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UCommunityHealingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxConcurrentSessions = { "MaxConcurrentSessions", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, MaxConcurrentSessions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentSessions_MetaData), NewProp_MaxConcurrentSessions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxSessionDuration = { "MaxSessionDuration", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, MaxSessionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSessionDuration_MetaData), NewProp_MaxSessionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MinHealerSkillLevel = { "MinHealerSkillLevel", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, MinHealerSkillLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinHealerSkillLevel_MetaData), NewProp_MinHealerSkillLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxHealersPerSession = { "MaxHealersPerSession", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, MaxHealersPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealersPerSession_MetaData), NewProp_MaxHealersPerSession_MetaData) };
void Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableAutomaticMatching_SetBit(void* Obj)
{
	((UCommunityHealingManager*)Obj)->bEnableAutomaticMatching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableAutomaticMatching = { "bEnableAutomaticMatching", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UCommunityHealingManager), &Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableAutomaticMatching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticMatching_MetaData), NewProp_bEnableAutomaticMatching_MetaData) };
void Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableSessionRecording_SetBit(void* Obj)
{
	((UCommunityHealingManager*)Obj)->bEnableSessionRecording = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableSessionRecording = { "bEnableSessionRecording", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UCommunityHealingManager), &Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableSessionRecording_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableSessionRecording_MetaData), NewProp_bEnableSessionRecording_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions_ValueProp = { "ActiveSessions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FHealingSession, METADATA_PARAMS(0, nullptr) }; // 2339623
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions_Key_KeyProp = { "ActiveSessions_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions = { "ActiveSessions", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, ActiveSessions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSessions_MetaData), NewProp_ActiveSessions_MetaData) }; // 2339623
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers_ValueProp = { "RegisteredHealers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FHealerProfile, METADATA_PARAMS(0, nullptr) }; // 843039954
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers_Key_KeyProp = { "RegisteredHealers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers = { "RegisteredHealers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, RegisteredHealers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredHealers_MetaData), NewProp_RegisteredHealers_MetaData) }; // 843039954
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_CompletedSessions_Inner = { "CompletedSessions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHealingSession, METADATA_PARAMS(0, nullptr) }; // 2339623
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_CompletedSessions = { "CompletedSessions", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, CompletedSessions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedSessions_MetaData), NewProp_CompletedSessions_MetaData) }; // 2339623
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory_ValueProp = { "PlayerHealingHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPlayerHealingHistory, METADATA_PARAMS(0, nullptr) }; // 2240332005
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory_Key_KeyProp = { "PlayerHealingHistory_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory = { "PlayerHealingHistory", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, PlayerHealingHistory), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerHealingHistory_MetaData), NewProp_PlayerHealingHistory_MetaData) }; // 2240332005
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers_ValueProp = { "SessionTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers_Key_KeyProp = { "SessionTimers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers = { "SessionTimers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCommunityHealingManager, SessionTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionTimers_MetaData), NewProp_SessionTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UCommunityHealingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxConcurrentSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxSessionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MinHealerSkillLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_MaxHealersPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableAutomaticMatching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_bEnableSessionRecording,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_ActiveSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_RegisteredHealers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_CompletedSessions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_CompletedSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_PlayerHealingHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCommunityHealingManager_Statics::NewProp_SessionTimers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UCommunityHealingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UCommunityHealingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UCommunityHealingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UCommunityHealingManager_Statics::ClassParams = {
	&UCommunityHealingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UCommunityHealingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UCommunityHealingManager_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UCommunityHealingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UCommunityHealingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UCommunityHealingManager()
{
	if (!Z_Registration_Info_UClass_UCommunityHealingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UCommunityHealingManager.OuterSingleton, Z_Construct_UClass_UCommunityHealingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UCommunityHealingManager.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UCommunityHealingManager);
UCommunityHealingManager::~UCommunityHealingManager() {}
// ********** End Class UCommunityHealingManager ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EHealingSessionType_StaticEnum, TEXT("EHealingSessionType"), &Z_Registration_Info_UEnum_EHealingSessionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2683699308U) },
		{ EHealingStatus_StaticEnum, TEXT("EHealingStatus"), &Z_Registration_Info_UEnum_EHealingStatus, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3798488262U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FHealingSession::StaticStruct, Z_Construct_UScriptStruct_FHealingSession_Statics::NewStructOps, TEXT("HealingSession"), &Z_Registration_Info_UScriptStruct_FHealingSession, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHealingSession), 2339623U) },
		{ FHealerProfile::StaticStruct, Z_Construct_UScriptStruct_FHealerProfile_Statics::NewStructOps, TEXT("HealerProfile"), &Z_Registration_Info_UScriptStruct_FHealerProfile, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHealerProfile), 843039954U) },
		{ FPlayerHealingHistory::StaticStruct, Z_Construct_UScriptStruct_FPlayerHealingHistory_Statics::NewStructOps, TEXT("PlayerHealingHistory"), &Z_Registration_Info_UScriptStruct_FPlayerHealingHistory, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerHealingHistory), 2240332005U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UCommunityHealingManager, UCommunityHealingManager::StaticClass, TEXT("UCommunityHealingManager"), &Z_Registration_Info_UClass_UCommunityHealingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UCommunityHealingManager), 2871649244U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_1120311592(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_CommunityHealingManager_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
