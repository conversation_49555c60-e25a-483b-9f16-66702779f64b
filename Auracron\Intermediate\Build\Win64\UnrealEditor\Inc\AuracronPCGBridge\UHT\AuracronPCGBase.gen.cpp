// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGBase.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGBase() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomePCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomePCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronResourcePCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronResourcePCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronStructurePCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronStructurePCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
PCG_API UClass* Z_Construct_UClass_UPCGSettings();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
void UAuracronPCGSettingsBase::StaticRegisterNativesUAuracronPCGSettingsBase()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSettingsBase;
UClass* UAuracronPCGSettingsBase::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSettingsBase;
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSettingsBase"),
			Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSettingsBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister()
{
	return UAuracronPCGSettingsBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base class for all Auracron PCG Settings\n * Provides common functionality and interface for all PCG nodes\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base class for all Auracron PCG Settings\nProvides common functionality and interface for all PCG nodes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeDescription_MetaData[] = {
		{ "Category", "Auracron Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auracron specific settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron specific settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeDescription;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSettingsBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_NodeDescription = { "NodeDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, NodeDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeDescription_MetaData), NewProp_NodeDescription_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize = { "BatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, BatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchSize_MetaData), NewProp_BatchSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_NodeDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPCGSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams = {
	&UAuracronPCGSettingsBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton, Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSettingsBase);
UAuracronPCGSettingsBase::~UAuracronPCGSettingsBase() {}
// ********** End Class UAuracronPCGSettingsBase ***************************************************

// ********** Begin Class UAuracronBiomePCGSettings ************************************************
void UAuracronBiomePCGSettings::StaticRegisterNativesUAuracronBiomePCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronBiomePCGSettings;
UClass* UAuracronBiomePCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronBiomePCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronBiomePCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronBiomePCGSettings"),
			Z_Registration_Info_UClass_UAuracronBiomePCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronBiomePCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronBiomePCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronBiomePCGSettings_NoRegister()
{
	return UAuracronBiomePCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronBiomePCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Biome PCG Settings\n * Generates biome-specific content based on environmental parameters\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Biome PCG Settings\nGenerates biome-specific content based on environmental parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome type identifier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome type identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureRange_MetaData[] = {
		{ "Category", "Biome" },
		{ "ClampMax", "50.0" },
		{ "ClampMin", "-50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temperature range for this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temperature range for this biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HumidityRange_MetaData[] = {
		{ "Category", "Biome" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Humidity range for this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Humidity range for this biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElevationRange_MetaData[] = {
		{ "Category", "Biome" },
		{ "ClampMax", "8000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elevation range for this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elevation range for this biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationDensity_MetaData[] = {
		{ "Category", "Vegetation" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vegetation density multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vegetation density multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceSpawnRate_MetaData[] = {
		{ "Category", "Resources" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource spawn rate multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource spawn rate multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseFoliageBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Foliage Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Foliage Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicRealm_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Dynamic Realm Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Dynamic Realm Bridge" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TemperatureRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HumidityRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElevationRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VegetationDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceSpawnRate;
	static void NewProp_bUseFoliageBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseFoliageBridge;
	static void NewProp_bUseDynamicRealm_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicRealm;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronBiomePCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, BiomeType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_TemperatureRange = { "TemperatureRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, TemperatureRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureRange_MetaData), NewProp_TemperatureRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_HumidityRange = { "HumidityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, HumidityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HumidityRange_MetaData), NewProp_HumidityRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_ElevationRange = { "ElevationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, ElevationRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElevationRange_MetaData), NewProp_ElevationRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_VegetationDensity = { "VegetationDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, VegetationDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationDensity_MetaData), NewProp_VegetationDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_ResourceSpawnRate = { "ResourceSpawnRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomePCGSettings, ResourceSpawnRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceSpawnRate_MetaData), NewProp_ResourceSpawnRate_MetaData) };
void Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseFoliageBridge_SetBit(void* Obj)
{
	((UAuracronBiomePCGSettings*)Obj)->bUseFoliageBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseFoliageBridge = { "bUseFoliageBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronBiomePCGSettings), &Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseFoliageBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseFoliageBridge_MetaData), NewProp_bUseFoliageBridge_MetaData) };
void Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseDynamicRealm_SetBit(void* Obj)
{
	((UAuracronBiomePCGSettings*)Obj)->bUseDynamicRealm = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseDynamicRealm = { "bUseDynamicRealm", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronBiomePCGSettings), &Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseDynamicRealm_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicRealm_MetaData), NewProp_bUseDynamicRealm_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_TemperatureRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_HumidityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_ElevationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_VegetationDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_ResourceSpawnRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseFoliageBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::NewProp_bUseDynamicRealm,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::ClassParams = {
	&UAuracronBiomePCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronBiomePCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronBiomePCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronBiomePCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronBiomePCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronBiomePCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronBiomePCGSettings);
UAuracronBiomePCGSettings::~UAuracronBiomePCGSettings() {}
// ********** End Class UAuracronBiomePCGSettings **************************************************

// ********** Begin Class UAuracronTerrainPCGSettings **********************************************
void UAuracronTerrainPCGSettings::StaticRegisterNativesUAuracronTerrainPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronTerrainPCGSettings;
UClass* UAuracronTerrainPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronTerrainPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronTerrainPCGSettings"),
			Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronTerrainPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings_NoRegister()
{
	return UAuracronTerrainPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Terrain PCG Settings\n * Generates terrain features, heightmaps, and landscape modifications\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Terrain PCG Settings\nGenerates terrain features, heightmaps, and landscape modifications" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainSeed_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Terrain generation seed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain generation seed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "Generation" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Noise scale for terrain generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise scale for terrain generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightMultiplier_MetaData[] = {
		{ "Category", "Generation" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Height multiplier for terrain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height multiplier for terrain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "Generation" },
		{ "ClampMax", "8" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of octaves for noise generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of octaves for noise generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoisePersistence_MetaData[] = {
		{ "Category", "Generation" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Persistence for noise generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Persistence for noise generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWorldPartition_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with World Partition Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with World Partition Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLumenIntegration_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Lumen Bridge for lighting */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Lumen Bridge for lighting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TerrainSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoisePersistence;
	static void NewProp_bUseWorldPartition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWorldPartition;
	static void NewProp_bUseLumenIntegration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLumenIntegration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronTerrainPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_TerrainSeed = { "TerrainSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTerrainPCGSettings, TerrainSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainSeed_MetaData), NewProp_TerrainSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTerrainPCGSettings, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_HeightMultiplier = { "HeightMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTerrainPCGSettings, HeightMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightMultiplier_MetaData), NewProp_HeightMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTerrainPCGSettings, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoisePersistence = { "NoisePersistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronTerrainPCGSettings, NoisePersistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoisePersistence_MetaData), NewProp_NoisePersistence_MetaData) };
void Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseWorldPartition_SetBit(void* Obj)
{
	((UAuracronTerrainPCGSettings*)Obj)->bUseWorldPartition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseWorldPartition = { "bUseWorldPartition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronTerrainPCGSettings), &Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseWorldPartition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWorldPartition_MetaData), NewProp_bUseWorldPartition_MetaData) };
void Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseLumenIntegration_SetBit(void* Obj)
{
	((UAuracronTerrainPCGSettings*)Obj)->bUseLumenIntegration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseLumenIntegration = { "bUseLumenIntegration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronTerrainPCGSettings), &Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseLumenIntegration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLumenIntegration_MetaData), NewProp_bUseLumenIntegration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_TerrainSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_HeightMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_NoisePersistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseWorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::NewProp_bUseLumenIntegration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::ClassParams = {
	&UAuracronTerrainPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronTerrainPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronTerrainPCGSettings);
UAuracronTerrainPCGSettings::~UAuracronTerrainPCGSettings() {}
// ********** End Class UAuracronTerrainPCGSettings ************************************************

// ********** Begin Class UAuracronStructurePCGSettings ********************************************
void UAuracronStructurePCGSettings::StaticRegisterNativesUAuracronStructurePCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronStructurePCGSettings;
UClass* UAuracronStructurePCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronStructurePCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronStructurePCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronStructurePCGSettings"),
			Z_Registration_Info_UClass_UAuracronStructurePCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronStructurePCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronStructurePCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronStructurePCGSettings_NoRegister()
{
	return UAuracronStructurePCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronStructurePCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Structure" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Structure PCG Settings\n * Generates buildings, ruins, and architectural elements\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Structure PCG Settings\nGenerates buildings, ruins, and architectural elements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureType_MetaData[] = {
		{ "Category", "Structure" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Structure type to generate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure type to generate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinStructureDistance_MetaData[] = {
		{ "Category", "Placement" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum distance between structures */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum distance between structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxStructuresPerArea_MetaData[] = {
		{ "Category", "Placement" },
		{ "ClampMax", "50" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum structures per area */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum structures per area" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleRange_MetaData[] = {
		{ "Category", "Variation" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Structure scale variation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure scale variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationVariation_MetaData[] = {
		{ "Category", "Variation" },
		{ "ClampMax", "360.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rotation variation in degrees */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rotation variation in degrees" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StructureType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinStructureDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxStructuresPerArea;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ScaleRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationVariation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronStructurePCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_StructureType = { "StructureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronStructurePCGSettings, StructureType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureType_MetaData), NewProp_StructureType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_MinStructureDistance = { "MinStructureDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronStructurePCGSettings, MinStructureDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinStructureDistance_MetaData), NewProp_MinStructureDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_MaxStructuresPerArea = { "MaxStructuresPerArea", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronStructurePCGSettings, MaxStructuresPerArea), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxStructuresPerArea_MetaData), NewProp_MaxStructuresPerArea_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_ScaleRange = { "ScaleRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronStructurePCGSettings, ScaleRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleRange_MetaData), NewProp_ScaleRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_RotationVariation = { "RotationVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronStructurePCGSettings, RotationVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationVariation_MetaData), NewProp_RotationVariation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_StructureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_MinStructureDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_MaxStructuresPerArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_ScaleRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::NewProp_RotationVariation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::ClassParams = {
	&UAuracronStructurePCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronStructurePCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronStructurePCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronStructurePCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronStructurePCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronStructurePCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronStructurePCGSettings);
UAuracronStructurePCGSettings::~UAuracronStructurePCGSettings() {}
// ********** End Class UAuracronStructurePCGSettings **********************************************

// ********** Begin Class UAuracronVegetationPCGSettings *******************************************
void UAuracronVegetationPCGSettings::StaticRegisterNativesUAuracronVegetationPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronVegetationPCGSettings;
UClass* UAuracronVegetationPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronVegetationPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronVegetationPCGSettings"),
			Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronVegetationPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings_NoRegister()
{
	return UAuracronVegetationPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Vegetation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Vegetation PCG Settings\n * Generates trees, plants, and foliage with integration to Foliage Bridge\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Vegetation PCG Settings\nGenerates trees, plants, and foliage with integration to Foliage Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationType_MetaData[] = {
		{ "Category", "Vegetation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vegetation type to spawn */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vegetation type to spawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDensity_MetaData[] = {
		{ "Category", "Density" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base density of vegetation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base density of vegetation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Placement" },
		{ "ClampMax", "90.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Slope threshold for vegetation placement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slope threshold for vegetation placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinAltitude_MetaData[] = {
		{ "Category", "Placement" },
		{ "ClampMax", "8000.0" },
		{ "ClampMin", "-1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum altitude for vegetation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum altitude for vegetation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAltitude_MetaData[] = {
		{ "Category", "Placement" },
		{ "ClampMax", "8000.0" },
		{ "ClampMin", "-1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum altitude for vegetation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum altitude for vegetation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SizeRange_MetaData[] = {
		{ "Category", "Variation" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Size variation range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Size variation range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSeasonalVariation_MetaData[] = {
		{ "Category", "Seasonal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seasonal variation support */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seasonal variation support" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWindInteraction_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Wind interaction support */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Wind interaction support" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VegetationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinAltitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAltitude;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SizeRange;
	static void NewProp_bUseSeasonalVariation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSeasonalVariation;
	static void NewProp_bUseWindInteraction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWindInteraction;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronVegetationPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_VegetationType = { "VegetationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, VegetationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationType_MetaData), NewProp_VegetationType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_BaseDensity = { "BaseDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, BaseDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDensity_MetaData), NewProp_BaseDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MinAltitude = { "MinAltitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, MinAltitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinAltitude_MetaData), NewProp_MinAltitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MaxAltitude = { "MaxAltitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, MaxAltitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAltitude_MetaData), NewProp_MaxAltitude_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_SizeRange = { "SizeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVegetationPCGSettings, SizeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SizeRange_MetaData), NewProp_SizeRange_MetaData) };
void Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseSeasonalVariation_SetBit(void* Obj)
{
	((UAuracronVegetationPCGSettings*)Obj)->bUseSeasonalVariation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseSeasonalVariation = { "bUseSeasonalVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronVegetationPCGSettings), &Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseSeasonalVariation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSeasonalVariation_MetaData), NewProp_bUseSeasonalVariation_MetaData) };
void Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseWindInteraction_SetBit(void* Obj)
{
	((UAuracronVegetationPCGSettings*)Obj)->bUseWindInteraction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseWindInteraction = { "bUseWindInteraction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronVegetationPCGSettings), &Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseWindInteraction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWindInteraction_MetaData), NewProp_bUseWindInteraction_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_VegetationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_BaseDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MinAltitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_MaxAltitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_SizeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseSeasonalVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::NewProp_bUseWindInteraction,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::ClassParams = {
	&UAuracronVegetationPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronVegetationPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronVegetationPCGSettings);
UAuracronVegetationPCGSettings::~UAuracronVegetationPCGSettings() {}
// ********** End Class UAuracronVegetationPCGSettings *********************************************

// ********** Begin Class UAuracronResourcePCGSettings *********************************************
void UAuracronResourcePCGSettings::StaticRegisterNativesUAuracronResourcePCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronResourcePCGSettings;
UClass* UAuracronResourcePCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronResourcePCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronResourcePCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronResourcePCGSettings"),
			Z_Registration_Info_UClass_UAuracronResourcePCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronResourcePCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronResourcePCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronResourcePCGSettings_NoRegister()
{
	return UAuracronResourcePCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronResourcePCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Resource PCG Settings\n * Generates collectible resources, minerals, and materials\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Resource PCG Settings\nGenerates collectible resources, minerals, and materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceType_MetaData[] = {
		{ "Category", "Resource" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource type to generate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource type to generate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceRarity_MetaData[] = {
		{ "Category", "Rarity" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.01" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource rarity (affects spawn rate) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource rarity (affects spawn rate)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterSize_MetaData[] = {
		{ "Category", "Clustering" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cluster size for resource nodes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cluster size for resource nodes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Clustering" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cluster radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cluster radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Gameplay" },
		{ "ClampMax", "3600.0" },
		{ "ClampMin", "60.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Respawn time in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Respawn time in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityRange_MetaData[] = {
		{ "Category", "Quality" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality variation range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality variation range" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResourceRarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ClusterSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QualityRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronResourcePCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, ResourceType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceType_MetaData), NewProp_ResourceType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ResourceRarity = { "ResourceRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, ResourceRarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceRarity_MetaData), NewProp_ResourceRarity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ClusterSize = { "ClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, ClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterSize_MetaData), NewProp_ClusterSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_QualityRange = { "QualityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronResourcePCGSettings, QualityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityRange_MetaData), NewProp_QualityRange_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ResourceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ResourceRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::NewProp_QualityRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::ClassParams = {
	&UAuracronResourcePCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronResourcePCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronResourcePCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronResourcePCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronResourcePCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronResourcePCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronResourcePCGSettings);
UAuracronResourcePCGSettings::~UAuracronResourcePCGSettings() {}
// ********** End Class UAuracronResourcePCGSettings ***********************************************

// ********** Begin Class UAuracronEnemySpawnPCGSettings *******************************************
void UAuracronEnemySpawnPCGSettings::StaticRegisterNativesUAuracronEnemySpawnPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings;
UClass* UAuracronEnemySpawnPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronEnemySpawnPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronEnemySpawnPCGSettings"),
			Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronEnemySpawnPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_NoRegister()
{
	return UAuracronEnemySpawnPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Enemy Spawn PCG Settings\n * Generates enemy spawn points and patrol routes\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Enemy Spawn PCG Settings\nGenerates enemy spawn points and patrol routes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyType_MetaData[] = {
		{ "Category", "Enemy" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enemy type to spawn */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enemy type to spawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnDensity_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.01" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spawn density per area */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawn density per area" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistanceFromStructures_MetaData[] = {
		{ "Category", "Safety" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum distance from player structures */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum distance from player structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolRadius_MetaData[] = {
		{ "Category", "AI" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Patrol radius for spawned enemies */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Patrol radius for spawned enemies" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLevelScaling_MetaData[] = {
		{ "Category", "Scaling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Level scaling based on distance from spawn */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level scaling based on distance from spawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCombatBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Combat Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Combat Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAdaptiveCreatures_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Adaptive Creatures Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Adaptive Creatures Bridge" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistanceFromStructures;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolRadius;
	static void NewProp_bUseLevelScaling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLevelScaling;
	static void NewProp_bUseCombatBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCombatBridge;
	static void NewProp_bUseAdaptiveCreatures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAdaptiveCreatures;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronEnemySpawnPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_EnemyType = { "EnemyType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEnemySpawnPCGSettings, EnemyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyType_MetaData), NewProp_EnemyType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_SpawnDensity = { "SpawnDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEnemySpawnPCGSettings, SpawnDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnDensity_MetaData), NewProp_SpawnDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_MinDistanceFromStructures = { "MinDistanceFromStructures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEnemySpawnPCGSettings, MinDistanceFromStructures), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistanceFromStructures_MetaData), NewProp_MinDistanceFromStructures_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_PatrolRadius = { "PatrolRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronEnemySpawnPCGSettings, PatrolRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolRadius_MetaData), NewProp_PatrolRadius_MetaData) };
void Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseLevelScaling_SetBit(void* Obj)
{
	((UAuracronEnemySpawnPCGSettings*)Obj)->bUseLevelScaling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseLevelScaling = { "bUseLevelScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronEnemySpawnPCGSettings), &Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseLevelScaling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLevelScaling_MetaData), NewProp_bUseLevelScaling_MetaData) };
void Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseCombatBridge_SetBit(void* Obj)
{
	((UAuracronEnemySpawnPCGSettings*)Obj)->bUseCombatBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseCombatBridge = { "bUseCombatBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronEnemySpawnPCGSettings), &Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseCombatBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCombatBridge_MetaData), NewProp_bUseCombatBridge_MetaData) };
void Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseAdaptiveCreatures_SetBit(void* Obj)
{
	((UAuracronEnemySpawnPCGSettings*)Obj)->bUseAdaptiveCreatures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseAdaptiveCreatures = { "bUseAdaptiveCreatures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronEnemySpawnPCGSettings), &Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseAdaptiveCreatures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAdaptiveCreatures_MetaData), NewProp_bUseAdaptiveCreatures_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_EnemyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_SpawnDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_MinDistanceFromStructures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_PatrolRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseLevelScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseCombatBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::NewProp_bUseAdaptiveCreatures,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::ClassParams = {
	&UAuracronEnemySpawnPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronEnemySpawnPCGSettings);
UAuracronEnemySpawnPCGSettings::~UAuracronEnemySpawnPCGSettings() {}
// ********** End Class UAuracronEnemySpawnPCGSettings *********************************************

// ********** Begin Class UAuracronDungeonPCGSettings **********************************************
void UAuracronDungeonPCGSettings::StaticRegisterNativesUAuracronDungeonPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronDungeonPCGSettings;
UClass* UAuracronDungeonPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronDungeonPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronDungeonPCGSettings"),
			Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronDungeonPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings_NoRegister()
{
	return UAuracronDungeonPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Dungeon" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Dungeon PCG Settings\n * Generates procedural dungeons, caves, and underground structures\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Dungeon PCG Settings\nGenerates procedural dungeons, caves, and underground structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DungeonType_MetaData[] = {
		{ "Category", "Dungeon" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dungeon type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dungeon type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoomCount_MetaData[] = {
		{ "Category", "Layout" },
		{ "ClampMax", "50" },
		{ "ClampMin", "3" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of rooms to generate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of rooms to generate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RoomSizeRange_MetaData[] = {
		{ "Category", "Layout" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "200.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Room size range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Room size range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CorridorWidth_MetaData[] = {
		{ "Category", "Layout" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Corridor width */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Corridor width" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DepthLevels_MetaData[] = {
		{ "Category", "Depth" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dungeon depth levels */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dungeon depth levels" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreasureRoomProbability_MetaData[] = {
		{ "Category", "Loot" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Treasure room probability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Treasure room probability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateBossRoom_MetaData[] = {
		{ "Category", "Boss" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Boss room generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Boss room generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DungeonType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RoomCount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RoomSizeRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CorridorWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DepthLevels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TreasureRoomProbability;
	static void NewProp_bGenerateBossRoom_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateBossRoom;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronDungeonPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_DungeonType = { "DungeonType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, DungeonType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DungeonType_MetaData), NewProp_DungeonType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_RoomCount = { "RoomCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, RoomCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoomCount_MetaData), NewProp_RoomCount_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_RoomSizeRange = { "RoomSizeRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, RoomSizeRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RoomSizeRange_MetaData), NewProp_RoomSizeRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_CorridorWidth = { "CorridorWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, CorridorWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CorridorWidth_MetaData), NewProp_CorridorWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_DepthLevels = { "DepthLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, DepthLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DepthLevels_MetaData), NewProp_DepthLevels_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_TreasureRoomProbability = { "TreasureRoomProbability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDungeonPCGSettings, TreasureRoomProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreasureRoomProbability_MetaData), NewProp_TreasureRoomProbability_MetaData) };
void Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_bGenerateBossRoom_SetBit(void* Obj)
{
	((UAuracronDungeonPCGSettings*)Obj)->bGenerateBossRoom = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_bGenerateBossRoom = { "bGenerateBossRoom", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDungeonPCGSettings), &Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_bGenerateBossRoom_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateBossRoom_MetaData), NewProp_bGenerateBossRoom_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_DungeonType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_RoomCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_RoomSizeRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_CorridorWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_DepthLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_TreasureRoomProbability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::NewProp_bGenerateBossRoom,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::ClassParams = {
	&UAuracronDungeonPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronDungeonPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronDungeonPCGSettings);
UAuracronDungeonPCGSettings::~UAuracronDungeonPCGSettings() {}
// ********** End Class UAuracronDungeonPCGSettings ************************************************

// ********** Begin Class UAuracronWeatherPCGSettings **********************************************
void UAuracronWeatherPCGSettings::StaticRegisterNativesUAuracronWeatherPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWeatherPCGSettings;
UClass* UAuracronWeatherPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronWeatherPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWeatherPCGSettings"),
			Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronWeatherPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings_NoRegister()
{
	return UAuracronWeatherPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Weather PCG Settings\n * Generates weather-based environmental modifications\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Weather PCG Settings\nGenerates weather-based environmental modifications" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherType_MetaData[] = {
		{ "Category", "Weather" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherIntensity_MetaData[] = {
		{ "Category", "Intensity" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsVegetation_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Affects vegetation growth */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Affects vegetation growth" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsResources_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Affects resource spawning */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Affects resource spawning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAffectsEnemies_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Affects enemy behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Affects enemy behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherDuration_MetaData[] = {
		{ "Category", "Duration" },
		{ "ClampMax", "3600.0" },
		{ "ClampMin", "60.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weather duration in seconds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weather duration in seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVFXBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with VFX Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with VFX Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAudioBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Audio Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Audio Bridge" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeatherType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherIntensity;
	static void NewProp_bAffectsVegetation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsVegetation;
	static void NewProp_bAffectsResources_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsResources;
	static void NewProp_bAffectsEnemies_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAffectsEnemies;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherDuration;
	static void NewProp_bUseVFXBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVFXBridge;
	static void NewProp_bUseAudioBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAudioBridge;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWeatherPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherType = { "WeatherType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWeatherPCGSettings, WeatherType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherType_MetaData), NewProp_WeatherType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherIntensity = { "WeatherIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWeatherPCGSettings, WeatherIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherIntensity_MetaData), NewProp_WeatherIntensity_MetaData) };
void Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsVegetation_SetBit(void* Obj)
{
	((UAuracronWeatherPCGSettings*)Obj)->bAffectsVegetation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsVegetation = { "bAffectsVegetation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWeatherPCGSettings), &Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsVegetation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsVegetation_MetaData), NewProp_bAffectsVegetation_MetaData) };
void Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsResources_SetBit(void* Obj)
{
	((UAuracronWeatherPCGSettings*)Obj)->bAffectsResources = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsResources = { "bAffectsResources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWeatherPCGSettings), &Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsResources_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsResources_MetaData), NewProp_bAffectsResources_MetaData) };
void Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsEnemies_SetBit(void* Obj)
{
	((UAuracronWeatherPCGSettings*)Obj)->bAffectsEnemies = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsEnemies = { "bAffectsEnemies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWeatherPCGSettings), &Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsEnemies_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAffectsEnemies_MetaData), NewProp_bAffectsEnemies_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherDuration = { "WeatherDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWeatherPCGSettings, WeatherDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherDuration_MetaData), NewProp_WeatherDuration_MetaData) };
void Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseVFXBridge_SetBit(void* Obj)
{
	((UAuracronWeatherPCGSettings*)Obj)->bUseVFXBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseVFXBridge = { "bUseVFXBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWeatherPCGSettings), &Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseVFXBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVFXBridge_MetaData), NewProp_bUseVFXBridge_MetaData) };
void Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseAudioBridge_SetBit(void* Obj)
{
	((UAuracronWeatherPCGSettings*)Obj)->bUseAudioBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseAudioBridge = { "bUseAudioBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWeatherPCGSettings), &Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseAudioBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAudioBridge_MetaData), NewProp_bUseAudioBridge_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsVegetation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsResources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bAffectsEnemies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_WeatherDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseVFXBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::NewProp_bUseAudioBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::ClassParams = {
	&UAuracronWeatherPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWeatherPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWeatherPCGSettings);
UAuracronWeatherPCGSettings::~UAuracronWeatherPCGSettings() {}
// ********** End Class UAuracronWeatherPCGSettings ************************************************

// ********** Begin Class UAuracronPCGBridgeAPI Function ApplyWeatherEffects ***********************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics
{
	struct AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms
	{
		FString WeatherType;
		float Intensity;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply weather effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply weather effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeatherType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_WeatherType = { "WeatherType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms, WeatherType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherType_MetaData), NewProp_WeatherType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_WeatherType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "ApplyWeatherEffects", Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::AuracronPCGBridgeAPI_eventApplyWeatherEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execApplyWeatherEffects)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeatherType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyWeatherEffects(Z_Param_WeatherType,Z_Param_Intensity,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function ApplyWeatherEffects *************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateBiomeContent **********************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms
	{
		FString BiomeType;
		FVector Location;
		float Radius;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate biome content */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate biome content" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_BiomeType = { "BiomeType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms, BiomeType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeType_MetaData), NewProp_BiomeType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms, Radius), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_BiomeType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateBiomeContent", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::AuracronPCGBridgeAPI_eventGenerateBiomeContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateBiomeContent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateBiomeContent(Z_Param_BiomeType,Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateBiomeContent ************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateDungeon ***************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateDungeon_Parms
	{
		FString DungeonType;
		FVector Location;
		int32 RoomCount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate dungeon */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate dungeon" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DungeonType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DungeonType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RoomCount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_DungeonType = { "DungeonType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateDungeon_Parms, DungeonType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DungeonType_MetaData), NewProp_DungeonType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateDungeon_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_RoomCount = { "RoomCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateDungeon_Parms, RoomCount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateDungeon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateDungeon_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_DungeonType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_RoomCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateDungeon", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::AuracronPCGBridgeAPI_eventGenerateDungeon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::AuracronPCGBridgeAPI_eventGenerateDungeon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateDungeon)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DungeonType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_RoomCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateDungeon(Z_Param_DungeonType,Z_Param_Out_Location,Z_Param_RoomCount);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateDungeon *****************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateEnemySpawns ***********************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms
	{
		FString EnemyType;
		FVector Location;
		float Density;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate enemy spawns */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate enemy spawns" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_EnemyType = { "EnemyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms, EnemyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyType_MetaData), NewProp_EnemyType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms, Density), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_EnemyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateEnemySpawns", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::AuracronPCGBridgeAPI_eventGenerateEnemySpawns_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateEnemySpawns)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EnemyType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateEnemySpawns(Z_Param_EnemyType,Z_Param_Out_Location,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateEnemySpawns *************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateResources *************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateResources_Parms
	{
		FString ResourceType;
		FVector Location;
		float Rarity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate resources */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate resources" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Rarity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateResources_Parms, ResourceType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceType_MetaData), NewProp_ResourceType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateResources_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateResources_Parms, Rarity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateResources_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateResources_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ResourceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_Rarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateResources", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::AuracronPCGBridgeAPI_eventGenerateResources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::AuracronPCGBridgeAPI_eventGenerateResources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateResources)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ResourceType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateResources(Z_Param_ResourceType,Z_Param_Out_Location,Z_Param_Rarity);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateResources ***************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateStructures ************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateStructures_Parms
	{
		FString StructureType;
		FVector Location;
		int32 Count;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate structures */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StructureType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_StructureType = { "StructureType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateStructures_Parms, StructureType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureType_MetaData), NewProp_StructureType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateStructures_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateStructures_Parms, Count), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateStructures_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateStructures_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_StructureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_Count,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateStructures", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::AuracronPCGBridgeAPI_eventGenerateStructures_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::AuracronPCGBridgeAPI_eventGenerateStructures_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateStructures)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_StructureType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateStructures(Z_Param_StructureType,Z_Param_Out_Location,Z_Param_Count);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateStructures **************************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateTerrainFeatures *******************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms
	{
		FVector Location;
		float Radius;
		int32 Seed;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate terrain features */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate terrain features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms, Seed), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_Seed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateTerrainFeatures", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::AuracronPCGBridgeAPI_eventGenerateTerrainFeatures_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateTerrainFeatures)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_GET_PROPERTY(FIntProperty,Z_Param_Seed);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateTerrainFeatures(Z_Param_Out_Location,Z_Param_Radius,Z_Param_Seed);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateTerrainFeatures *********************

// ********** Begin Class UAuracronPCGBridgeAPI Function GenerateVegetation ************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics
{
	struct AuracronPCGBridgeAPI_eventGenerateVegetation_Parms
	{
		FString VegetationType;
		FVector Location;
		float Density;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate vegetation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate vegetation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_VegetationType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_VegetationType = { "VegetationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateVegetation_Parms, VegetationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationType_MetaData), NewProp_VegetationType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateVegetation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPCGBridgeAPI_eventGenerateVegetation_Parms, Density), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventGenerateVegetation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventGenerateVegetation_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_VegetationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "GenerateVegetation", Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::AuracronPCGBridgeAPI_eventGenerateVegetation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::AuracronPCGBridgeAPI_eventGenerateVegetation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execGenerateVegetation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_VegetationType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateVegetation(Z_Param_VegetationType,Z_Param_Out_Location,Z_Param_Density);
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function GenerateVegetation **************************

// ********** Begin Class UAuracronPCGBridgeAPI Function InitializePCGSystem ***********************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics
{
	struct AuracronPCGBridgeAPI_eventInitializePCGSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize the PCG system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize the PCG system" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventInitializePCGSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventInitializePCGSystem_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "InitializePCGSystem", Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::AuracronPCGBridgeAPI_eventInitializePCGSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::AuracronPCGBridgeAPI_eventInitializePCGSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execInitializePCGSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializePCGSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function InitializePCGSystem *************************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithAudioBridge ******************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithAudioBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Audio Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Audio Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithAudioBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithAudioBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithAudioBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithAudioBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithAudioBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithAudioBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithAudioBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithAudioBridge ********************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithCombatBridge *****************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithCombatBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Combat Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Combat Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithCombatBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithCombatBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithCombatBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithCombatBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithCombatBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithCombatBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithCombatBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithCombatBridge *******************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithDynamicRealmBridge ***********
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithDynamicRealmBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Dynamic Realm Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Dynamic Realm Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithDynamicRealmBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithDynamicRealmBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithDynamicRealmBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithDynamicRealmBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithDynamicRealmBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithDynamicRealmBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithDynamicRealmBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithDynamicRealmBridge *************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithFoliageBridge ****************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithFoliageBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Foliage Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Foliage Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithFoliageBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithFoliageBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithFoliageBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithFoliageBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithFoliageBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithFoliageBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithFoliageBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithFoliageBridge ******************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithVFXBridge ********************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithVFXBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with VFX Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with VFX Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithVFXBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithVFXBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithVFXBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithVFXBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithVFXBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithVFXBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithVFXBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithVFXBridge **********************

// ********** Begin Class UAuracronPCGBridgeAPI Function IntegrateWithWorldPartitionBridge *********
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics
{
	struct AuracronPCGBridgeAPI_eventIntegrateWithWorldPartitionBridge_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with World Partition Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with World Partition Bridge" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIntegrateWithWorldPartitionBridge_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIntegrateWithWorldPartitionBridge_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IntegrateWithWorldPartitionBridge", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithWorldPartitionBridge_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::AuracronPCGBridgeAPI_eventIntegrateWithWorldPartitionBridge_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIntegrateWithWorldPartitionBridge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IntegrateWithWorldPartitionBridge();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IntegrateWithWorldPartitionBridge ***********

// ********** Begin Class UAuracronPCGBridgeAPI Function IsSystemReady *****************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics
{
	struct AuracronPCGBridgeAPI_eventIsSystemReady_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if system is ready */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if system is ready" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPCGBridgeAPI_eventIsSystemReady_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPCGBridgeAPI_eventIsSystemReady_Parms), &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "IsSystemReady", Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::AuracronPCGBridgeAPI_eventIsSystemReady_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::AuracronPCGBridgeAPI_eventIsSystemReady_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execIsSystemReady)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSystemReady();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function IsSystemReady *******************************

// ********** Begin Class UAuracronPCGBridgeAPI Function ShutdownPCGSystem *************************
struct Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Shutdown the PCG system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shutdown the PCG system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronPCGBridgeAPI, nullptr, "ShutdownPCGSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronPCGBridgeAPI::execShutdownPCGSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShutdownPCGSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronPCGBridgeAPI Function ShutdownPCGSystem ***************************

// ********** Begin Class UAuracronPCGBridgeAPI ****************************************************
void UAuracronPCGBridgeAPI::StaticRegisterNativesUAuracronPCGBridgeAPI()
{
	UClass* Class = UAuracronPCGBridgeAPI::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyWeatherEffects", &UAuracronPCGBridgeAPI::execApplyWeatherEffects },
		{ "GenerateBiomeContent", &UAuracronPCGBridgeAPI::execGenerateBiomeContent },
		{ "GenerateDungeon", &UAuracronPCGBridgeAPI::execGenerateDungeon },
		{ "GenerateEnemySpawns", &UAuracronPCGBridgeAPI::execGenerateEnemySpawns },
		{ "GenerateResources", &UAuracronPCGBridgeAPI::execGenerateResources },
		{ "GenerateStructures", &UAuracronPCGBridgeAPI::execGenerateStructures },
		{ "GenerateTerrainFeatures", &UAuracronPCGBridgeAPI::execGenerateTerrainFeatures },
		{ "GenerateVegetation", &UAuracronPCGBridgeAPI::execGenerateVegetation },
		{ "InitializePCGSystem", &UAuracronPCGBridgeAPI::execInitializePCGSystem },
		{ "IntegrateWithAudioBridge", &UAuracronPCGBridgeAPI::execIntegrateWithAudioBridge },
		{ "IntegrateWithCombatBridge", &UAuracronPCGBridgeAPI::execIntegrateWithCombatBridge },
		{ "IntegrateWithDynamicRealmBridge", &UAuracronPCGBridgeAPI::execIntegrateWithDynamicRealmBridge },
		{ "IntegrateWithFoliageBridge", &UAuracronPCGBridgeAPI::execIntegrateWithFoliageBridge },
		{ "IntegrateWithVFXBridge", &UAuracronPCGBridgeAPI::execIntegrateWithVFXBridge },
		{ "IntegrateWithWorldPartitionBridge", &UAuracronPCGBridgeAPI::execIntegrateWithWorldPartitionBridge },
		{ "IsSystemReady", &UAuracronPCGBridgeAPI::execIsSystemReady },
		{ "ShutdownPCGSystem", &UAuracronPCGBridgeAPI::execShutdownPCGSystem },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGBridgeAPI;
UClass* UAuracronPCGBridgeAPI::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGBridgeAPI;
	if (!Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGBridgeAPI"),
			Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.InnerSingleton,
			StaticRegisterNativesUAuracronPCGBridgeAPI,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI_NoRegister()
{
	return UAuracronPCGBridgeAPI::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron PCG Bridge API\n * Main interface for integrating with other Auracron bridges\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron PCG Bridge API\nMain interface for integrating with other Auracron bridges" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemReady_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** System ready state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System ready state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntegratedBridges_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integrated bridges */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integrated bridges" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance monitoring */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality settings" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSystemReady_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemReady;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IntegratedBridges_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IntegratedBridges;
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_ApplyWeatherEffects, "ApplyWeatherEffects" }, // 922613450
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateBiomeContent, "GenerateBiomeContent" }, // 3291767363
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateDungeon, "GenerateDungeon" }, // 1771435676
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateEnemySpawns, "GenerateEnemySpawns" }, // 1661143242
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateResources, "GenerateResources" }, // 2630068414
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateStructures, "GenerateStructures" }, // 2405704341
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateTerrainFeatures, "GenerateTerrainFeatures" }, // 2029018705
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_GenerateVegetation, "GenerateVegetation" }, // 3718605588
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_InitializePCGSystem, "InitializePCGSystem" }, // 3572530064
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithAudioBridge, "IntegrateWithAudioBridge" }, // 4105395143
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithCombatBridge, "IntegrateWithCombatBridge" }, // 1249373529
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithDynamicRealmBridge, "IntegrateWithDynamicRealmBridge" }, // 107376619
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithFoliageBridge, "IntegrateWithFoliageBridge" }, // 1620041318
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithVFXBridge, "IntegrateWithVFXBridge" }, // 2565317958
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IntegrateWithWorldPartitionBridge, "IntegrateWithWorldPartitionBridge" }, // 1725618494
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_IsSystemReady, "IsSystemReady" }, // 4096645661
		{ &Z_Construct_UFunction_UAuracronPCGBridgeAPI_ShutdownPCGSystem, "ShutdownPCGSystem" }, // 2017645648
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGBridgeAPI>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bSystemReady_SetBit(void* Obj)
{
	((UAuracronPCGBridgeAPI*)Obj)->bSystemReady = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bSystemReady = { "bSystemReady", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGBridgeAPI), &Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bSystemReady_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemReady_MetaData), NewProp_bSystemReady_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_IntegratedBridges_Inner = { "IntegratedBridges", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_IntegratedBridges = { "IntegratedBridges", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBridgeAPI, IntegratedBridges), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntegratedBridges_MetaData), NewProp_IntegratedBridges_MetaData) };
void Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((UAuracronPCGBridgeAPI*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPCGBridgeAPI), &Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGBridgeAPI, QualityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bSystemReady,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_IntegratedBridges_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_IntegratedBridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::ClassParams = {
	&UAuracronPCGBridgeAPI::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.OuterSingleton, Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGBridgeAPI.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGBridgeAPI);
UAuracronPCGBridgeAPI::~UAuracronPCGBridgeAPI() {}
// ********** End Class UAuracronPCGBridgeAPI ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGSettingsBase, UAuracronPCGSettingsBase::StaticClass, TEXT("UAuracronPCGSettingsBase"), &Z_Registration_Info_UClass_UAuracronPCGSettingsBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSettingsBase), 972174545U) },
		{ Z_Construct_UClass_UAuracronBiomePCGSettings, UAuracronBiomePCGSettings::StaticClass, TEXT("UAuracronBiomePCGSettings"), &Z_Registration_Info_UClass_UAuracronBiomePCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronBiomePCGSettings), 19423261U) },
		{ Z_Construct_UClass_UAuracronTerrainPCGSettings, UAuracronTerrainPCGSettings::StaticClass, TEXT("UAuracronTerrainPCGSettings"), &Z_Registration_Info_UClass_UAuracronTerrainPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronTerrainPCGSettings), 4082042997U) },
		{ Z_Construct_UClass_UAuracronStructurePCGSettings, UAuracronStructurePCGSettings::StaticClass, TEXT("UAuracronStructurePCGSettings"), &Z_Registration_Info_UClass_UAuracronStructurePCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronStructurePCGSettings), 577687727U) },
		{ Z_Construct_UClass_UAuracronVegetationPCGSettings, UAuracronVegetationPCGSettings::StaticClass, TEXT("UAuracronVegetationPCGSettings"), &Z_Registration_Info_UClass_UAuracronVegetationPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronVegetationPCGSettings), 3149110579U) },
		{ Z_Construct_UClass_UAuracronResourcePCGSettings, UAuracronResourcePCGSettings::StaticClass, TEXT("UAuracronResourcePCGSettings"), &Z_Registration_Info_UClass_UAuracronResourcePCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronResourcePCGSettings), 1018868109U) },
		{ Z_Construct_UClass_UAuracronEnemySpawnPCGSettings, UAuracronEnemySpawnPCGSettings::StaticClass, TEXT("UAuracronEnemySpawnPCGSettings"), &Z_Registration_Info_UClass_UAuracronEnemySpawnPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronEnemySpawnPCGSettings), 1651154441U) },
		{ Z_Construct_UClass_UAuracronDungeonPCGSettings, UAuracronDungeonPCGSettings::StaticClass, TEXT("UAuracronDungeonPCGSettings"), &Z_Registration_Info_UClass_UAuracronDungeonPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronDungeonPCGSettings), 1476860880U) },
		{ Z_Construct_UClass_UAuracronWeatherPCGSettings, UAuracronWeatherPCGSettings::StaticClass, TEXT("UAuracronWeatherPCGSettings"), &Z_Registration_Info_UClass_UAuracronWeatherPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWeatherPCGSettings), 1380382096U) },
		{ Z_Construct_UClass_UAuracronPCGBridgeAPI, UAuracronPCGBridgeAPI::StaticClass, TEXT("UAuracronPCGBridgeAPI"), &Z_Registration_Info_UClass_UAuracronPCGBridgeAPI, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGBridgeAPI), 2782923801U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_1016718916(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
