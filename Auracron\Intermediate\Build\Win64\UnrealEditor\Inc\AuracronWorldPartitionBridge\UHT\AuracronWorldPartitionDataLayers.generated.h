// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionDataLayers.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionDataLayers_generated_h
#error "AuracronWorldPartitionDataLayers.generated.h already included, missing '#pragma once' in AuracronWorldPartitionDataLayers.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionDataLayers_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronDataLayerManager;
class UWorld;
enum class EAuracronDataLayerState : uint8;
enum class EAuracronDataLayerType : uint8;
enum class EAuracronDataLayerVisibility : uint8;
struct FAuracronDataLayerCondition;
struct FAuracronDataLayerConfiguration;
struct FAuracronDataLayerInfo;
struct FAuracronDataLayerStatistics;

// ********** Begin ScriptStruct FAuracronDataLayerConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_96_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDataLayerConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDataLayerConfiguration;
// ********** End ScriptStruct FAuracronDataLayerConfiguration *************************************

// ********** Begin ScriptStruct FAuracronDataLayerInfo ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_154_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDataLayerInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDataLayerInfo;
// ********** End ScriptStruct FAuracronDataLayerInfo **********************************************

// ********** Begin ScriptStruct FAuracronDataLayerCondition ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_227_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDataLayerCondition_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDataLayerCondition;
// ********** End ScriptStruct FAuracronDataLayerCondition *****************************************

// ********** Begin ScriptStruct FAuracronDataLayerStatistics **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_265_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDataLayerStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDataLayerStatistics;
// ********** End ScriptStruct FAuracronDataLayerStatistics ****************************************

// ********** Begin Delegate FOnDataLayerStateChanged **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_476_DELEGATE \
static void FOnDataLayerStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerStateChanged, const FString& LayerId, EAuracronDataLayerState NewState);


// ********** End Delegate FOnDataLayerStateChanged ************************************************

// ********** Begin Delegate FOnDataLayerVisibilityChanged *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_477_DELEGATE \
static void FOnDataLayerVisibilityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerVisibilityChanged, const FString& LayerId, EAuracronDataLayerVisibility NewVisibility);


// ********** End Delegate FOnDataLayerVisibilityChanged *******************************************

// ********** Begin Delegate FOnDataLayerTransitionStarted *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_478_DELEGATE \
static void FOnDataLayerTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerTransitionStarted, const FString& FromLayerId, const FString& ToLayerId, float TransitionTime);


// ********** End Delegate FOnDataLayerTransitionStarted *******************************************

// ********** Begin Delegate FOnDataLayerTransitionCompleted ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_479_DELEGATE \
static void FOnDataLayerTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnDataLayerTransitionCompleted, const FString& FromLayerId, const FString& ToLayerId);


// ********** End Delegate FOnDataLayerTransitionCompleted *****************************************

// ********** Begin Class UAuracronDataLayerManager ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugLayerInfo); \
	DECLARE_FUNCTION(execLogLayerState); \
	DECLARE_FUNCTION(execIsLayerDebugEnabled); \
	DECLARE_FUNCTION(execEnableLayerDebug); \
	DECLARE_FUNCTION(execGetTotalLayerCount); \
	DECLARE_FUNCTION(execGetLoadedLayerCount); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetDataLayerStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetLayersByTag); \
	DECLARE_FUNCTION(execGetLayersByType); \
	DECLARE_FUNCTION(execGetVisibleLayers); \
	DECLARE_FUNCTION(execGetLoadedLayers); \
	DECLARE_FUNCTION(execValidateLayerDependencies); \
	DECLARE_FUNCTION(execGetLayerDependencies); \
	DECLARE_FUNCTION(execRemoveLayerDependency); \
	DECLARE_FUNCTION(execAddLayerDependency); \
	DECLARE_FUNCTION(execEvaluateAllConditions); \
	DECLARE_FUNCTION(execGetLayerConditions); \
	DECLARE_FUNCTION(execRemoveLayerCondition); \
	DECLARE_FUNCTION(execAddLayerCondition); \
	DECLARE_FUNCTION(execTransitionToDataLayer); \
	DECLARE_FUNCTION(execBlendDataLayers); \
	DECLARE_FUNCTION(execSwitchToDataLayerSet); \
	DECLARE_FUNCTION(execHideDataLayer); \
	DECLARE_FUNCTION(execShowDataLayer); \
	DECLARE_FUNCTION(execGetDataLayerVisibility); \
	DECLARE_FUNCTION(execSetDataLayerVisibility); \
	DECLARE_FUNCTION(execToggleDataLayer); \
	DECLARE_FUNCTION(execUnloadDataLayer); \
	DECLARE_FUNCTION(execLoadDataLayer); \
	DECLARE_FUNCTION(execGetDataLayerState); \
	DECLARE_FUNCTION(execSetDataLayerState); \
	DECLARE_FUNCTION(execDoesDataLayerExist); \
	DECLARE_FUNCTION(execGetDataLayerNames); \
	DECLARE_FUNCTION(execGetAllDataLayers); \
	DECLARE_FUNCTION(execGetDataLayerInfo); \
	DECLARE_FUNCTION(execRemoveDataLayer); \
	DECLARE_FUNCTION(execCreateDataLayer); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronDataLayerManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronDataLayerManager(); \
	friend struct Z_Construct_UClass_UAuracronDataLayerManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronDataLayerManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronDataLayerManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronDataLayerManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronDataLayerManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronDataLayerManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronDataLayerManager(UAuracronDataLayerManager&&) = delete; \
	UAuracronDataLayerManager(const UAuracronDataLayerManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronDataLayerManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronDataLayerManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronDataLayerManager) \
	NO_API virtual ~UAuracronDataLayerManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_319_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h_322_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronDataLayerManager;

// ********** End Class UAuracronDataLayerManager **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDataLayers_h

// ********** Begin Enum EAuracronDataLayerState ***************************************************
#define FOREACH_ENUM_EAURACRONDATALAYERSTATE(op) \
	op(EAuracronDataLayerState::Unloaded) \
	op(EAuracronDataLayerState::Loading) \
	op(EAuracronDataLayerState::Loaded) \
	op(EAuracronDataLayerState::Unloading) \
	op(EAuracronDataLayerState::Failed) 

enum class EAuracronDataLayerState : uint8;
template<> struct TIsUEnumClass<EAuracronDataLayerState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerState>();
// ********** End Enum EAuracronDataLayerState *****************************************************

// ********** Begin Enum EAuracronDataLayerType ****************************************************
#define FOREACH_ENUM_EAURACRONDATALAYERTYPE(op) \
	op(EAuracronDataLayerType::Runtime) \
	op(EAuracronDataLayerType::Editor) \
	op(EAuracronDataLayerType::Streaming) \
	op(EAuracronDataLayerType::Static) \
	op(EAuracronDataLayerType::Dynamic) 

enum class EAuracronDataLayerType : uint8;
template<> struct TIsUEnumClass<EAuracronDataLayerType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerType>();
// ********** End Enum EAuracronDataLayerType ******************************************************

// ********** Begin Enum EAuracronDataLayerVisibility **********************************************
#define FOREACH_ENUM_EAURACRONDATALAYERVISIBILITY(op) \
	op(EAuracronDataLayerVisibility::Hidden) \
	op(EAuracronDataLayerVisibility::Visible) \
	op(EAuracronDataLayerVisibility::PartiallyVisible) \
	op(EAuracronDataLayerVisibility::Transitioning) 

enum class EAuracronDataLayerVisibility : uint8;
template<> struct TIsUEnumClass<EAuracronDataLayerVisibility> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerVisibility>();
// ********** End Enum EAuracronDataLayerVisibility ************************************************

// ********** Begin Enum EAuracronDataLayerPriority ************************************************
#define FOREACH_ENUM_EAURACRONDATALAYERPRIORITY(op) \
	op(EAuracronDataLayerPriority::Lowest) \
	op(EAuracronDataLayerPriority::Low) \
	op(EAuracronDataLayerPriority::Normal) \
	op(EAuracronDataLayerPriority::High) \
	op(EAuracronDataLayerPriority::Highest) \
	op(EAuracronDataLayerPriority::Critical) 

enum class EAuracronDataLayerPriority : uint8;
template<> struct TIsUEnumClass<EAuracronDataLayerPriority> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDataLayerPriority>();
// ********** End Enum EAuracronDataLayerPriority **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
