// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAudioBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAudioBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONAUDIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAudioBridge();
AURACRONAUDIOBRIDGE_API UClass* Z_Construct_UClass_UAuracronAudioBridge_NoRegister();
AURACRONAUDIOBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer();
AURACRONAUDIOBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType();
AURACRONAUDIOBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature();
AURACRONAUDIOBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature();
AURACRONAUDIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioConfiguration();
AURACRONAUDIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration();
AURACRONAUDIOBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSFXConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UReverbEffect_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundCue_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronAudioBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronAudioType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAudioType;
static UEnum* EAuracronAudioType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAudioType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType, (UObject*)Z_Construct_UPackage__Script_AuracronAudioBridge(), TEXT("EAuracronAudioType"));
	}
	return Z_Registration_Info_UEnum_EAuracronAudioType.OuterSingleton;
}
template<> AURACRONAUDIOBRIDGE_API UEnum* StaticEnum<EAuracronAudioType>()
{
	return EAuracronAudioType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ability.DisplayName", "Ability" },
		{ "Ability.Name", "EAuracronAudioType::Ability" },
		{ "Ambient.DisplayName", "Ambient" },
		{ "Ambient.Name", "EAuracronAudioType::Ambient" },
		{ "BlueprintType", "true" },
		{ "Champion.DisplayName", "Champion" },
		{ "Champion.Name", "EAuracronAudioType::Champion" },
		{ "Combat.DisplayName", "Combat" },
		{ "Combat.Name", "EAuracronAudioType::Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de \xc3\x83\xc2\xa1udio\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
		{ "Music.DisplayName", "Music" },
		{ "Music.Name", "EAuracronAudioType::Music" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronAudioType::None" },
		{ "Notification.DisplayName", "Notification" },
		{ "Notification.Name", "EAuracronAudioType::Notification" },
		{ "Realm.DisplayName", "Realm" },
		{ "Realm.Name", "EAuracronAudioType::Realm" },
		{ "SFX.DisplayName", "Sound Effects" },
		{ "SFX.Name", "EAuracronAudioType::SFX" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de \xc3\x83\xc2\xa1udio" },
#endif
		{ "UI.DisplayName", "UI" },
		{ "UI.Name", "EAuracronAudioType::UI" },
		{ "Voice.DisplayName", "Voice" },
		{ "Voice.Name", "EAuracronAudioType::Voice" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAudioType::None", (int64)EAuracronAudioType::None },
		{ "EAuracronAudioType::Music", (int64)EAuracronAudioType::Music },
		{ "EAuracronAudioType::SFX", (int64)EAuracronAudioType::SFX },
		{ "EAuracronAudioType::Voice", (int64)EAuracronAudioType::Voice },
		{ "EAuracronAudioType::Ambient", (int64)EAuracronAudioType::Ambient },
		{ "EAuracronAudioType::UI", (int64)EAuracronAudioType::UI },
		{ "EAuracronAudioType::Ability", (int64)EAuracronAudioType::Ability },
		{ "EAuracronAudioType::Champion", (int64)EAuracronAudioType::Champion },
		{ "EAuracronAudioType::Realm", (int64)EAuracronAudioType::Realm },
		{ "EAuracronAudioType::Combat", (int64)EAuracronAudioType::Combat },
		{ "EAuracronAudioType::Notification", (int64)EAuracronAudioType::Notification },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
	nullptr,
	"EAuracronAudioType",
	"EAuracronAudioType",
	Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAudioType.InnerSingleton, Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAudioType.InnerSingleton;
}
// ********** End Enum EAuracronAudioType **********************************************************

// ********** Begin Enum EAuracronAudioLayer *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAudioLayer;
static UEnum* EAuracronAudioLayer_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioLayer.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAudioLayer.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer, (UObject*)Z_Construct_UPackage__Script_AuracronAudioBridge(), TEXT("EAuracronAudioLayer"));
	}
	return Z_Registration_Info_UEnum_EAuracronAudioLayer.OuterSingleton;
}
template<> AURACRONAUDIOBRIDGE_API UEnum* StaticEnum<EAuracronAudioLayer>()
{
	return EAuracronAudioLayer_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All Layers" },
		{ "All.Name", "EAuracronAudioLayer::All" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de \xc3\x83\xc2\xa1udio 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
		{ "Sky.DisplayName", "Sky Layer" },
		{ "Sky.Name", "EAuracronAudioLayer::Sky" },
		{ "Surface.DisplayName", "Surface Layer" },
		{ "Surface.Name", "EAuracronAudioLayer::Surface" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para camadas de \xc3\x83\xc2\xa1udio 3D" },
#endif
		{ "Underground.DisplayName", "Underground Layer" },
		{ "Underground.Name", "EAuracronAudioLayer::Underground" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAudioLayer::Surface", (int64)EAuracronAudioLayer::Surface },
		{ "EAuracronAudioLayer::Sky", (int64)EAuracronAudioLayer::Sky },
		{ "EAuracronAudioLayer::Underground", (int64)EAuracronAudioLayer::Underground },
		{ "EAuracronAudioLayer::All", (int64)EAuracronAudioLayer::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
	nullptr,
	"EAuracronAudioLayer",
	"EAuracronAudioLayer",
	Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioLayer.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAudioLayer.InnerSingleton, Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAudioLayer.InnerSingleton;
}
// ********** End Enum EAuracronAudioLayer *********************************************************

// ********** Begin ScriptStruct FAuracronAudioConfiguration ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration;
class UScriptStruct* FAuracronAudioConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAudioConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronAudioBridge(), TEXT("AuracronAudioConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio 3D\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasterVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume master */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume master" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume da m\xc3\x83\xc2\xbasica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume da m\xc3\x83\xc2\xbasica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SFXVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume dos efeitos sonoros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume dos efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume das vozes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume das vozes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume do ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UIVolume_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume da UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume da UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUse3DAudio_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar \xc3\x83\xc2\xa1udio 3D */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar \xc3\x83\xc2\xa1udio 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicReverb_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar reverb din\xc3\x83\xc2\xa2mico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar reverb din\xc3\x83\xc2\xa2mico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAudioOcclusion_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar oclus\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar oclus\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicRangeCompression_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar compress\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar compress\xc3\x83\xc2\xa3o din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioQuality_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade do \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade do \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAudioDistance_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseBinauralAudio_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar \xc3\x83\xc2\xa1udio binaural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar \xc3\x83\xc2\xa1udio binaural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHRTF_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar HRTF */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar HRTF" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAudioLayer_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camada de \xc3\x83\xc2\xa1udio atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camada de \xc3\x83\xc2\xa1udio atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSmoothLayerTransitions_MetaData[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es suaves entre camadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es suaves entre camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerTransitionTime_MetaData[] = {
		{ "Category", "Audio Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre camadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre camadas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MasterVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MusicVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SFXVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VoiceVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UIVolume;
	static void NewProp_bUse3DAudio_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUse3DAudio;
	static void NewProp_bUseDynamicReverb_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicReverb;
	static void NewProp_bUseAudioOcclusion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAudioOcclusion;
	static void NewProp_bUseDynamicRangeCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicRangeCompression;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioQuality;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAudioDistance;
	static void NewProp_bUseBinauralAudio_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseBinauralAudio;
	static void NewProp_bUseHRTF_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHRTF;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentAudioLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentAudioLayer;
	static void NewProp_bUseSmoothLayerTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSmoothLayerTransitions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerTransitionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAudioConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MasterVolume = { "MasterVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, MasterVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasterVolume_MetaData), NewProp_MasterVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MusicVolume = { "MusicVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, MusicVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicVolume_MetaData), NewProp_MusicVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_SFXVolume = { "SFXVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, SFXVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SFXVolume_MetaData), NewProp_SFXVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_VoiceVolume = { "VoiceVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, VoiceVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceVolume_MetaData), NewProp_VoiceVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_AmbientVolume = { "AmbientVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, AmbientVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientVolume_MetaData), NewProp_AmbientVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_UIVolume = { "UIVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, UIVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UIVolume_MetaData), NewProp_UIVolume_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUse3DAudio_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUse3DAudio = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUse3DAudio = { "bUse3DAudio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUse3DAudio_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUse3DAudio_MetaData), NewProp_bUse3DAudio_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicReverb_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseDynamicReverb = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicReverb = { "bUseDynamicReverb", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicReverb_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicReverb_MetaData), NewProp_bUseDynamicReverb_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseAudioOcclusion_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseAudioOcclusion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseAudioOcclusion = { "bUseAudioOcclusion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseAudioOcclusion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAudioOcclusion_MetaData), NewProp_bUseAudioOcclusion_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicRangeCompression_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseDynamicRangeCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicRangeCompression = { "bUseDynamicRangeCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicRangeCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicRangeCompression_MetaData), NewProp_bUseDynamicRangeCompression_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_AudioQuality = { "AudioQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, AudioQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioQuality_MetaData), NewProp_AudioQuality_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MaxAudioDistance = { "MaxAudioDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, MaxAudioDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAudioDistance_MetaData), NewProp_MaxAudioDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseBinauralAudio_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseBinauralAudio = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseBinauralAudio = { "bUseBinauralAudio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseBinauralAudio_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseBinauralAudio_MetaData), NewProp_bUseBinauralAudio_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseHRTF_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseHRTF = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseHRTF = { "bUseHRTF", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseHRTF_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHRTF_MetaData), NewProp_bUseHRTF_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_CurrentAudioLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_CurrentAudioLayer = { "CurrentAudioLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, CurrentAudioLayer), Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAudioLayer_MetaData), NewProp_CurrentAudioLayer_MetaData) }; // 648783982
void Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseSmoothLayerTransitions_SetBit(void* Obj)
{
	((FAuracronAudioConfiguration*)Obj)->bUseSmoothLayerTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseSmoothLayerTransitions = { "bUseSmoothLayerTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseSmoothLayerTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSmoothLayerTransitions_MetaData), NewProp_bUseSmoothLayerTransitions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_LayerTransitionTime = { "LayerTransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioConfiguration, LayerTransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerTransitionTime_MetaData), NewProp_LayerTransitionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MasterVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MusicVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_SFXVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_VoiceVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_AmbientVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_UIVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUse3DAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicReverb,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseAudioOcclusion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseDynamicRangeCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_AudioQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_MaxAudioDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseBinauralAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseHRTF,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_CurrentAudioLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_CurrentAudioLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_bUseSmoothLayerTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewProp_LayerTransitionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
	nullptr,
	&NewStructOps,
	"AuracronAudioConfiguration",
	Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::PropPointers),
	sizeof(FAuracronAudioConfiguration),
	alignof(FAuracronAudioConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAudioConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDynamicMusicConfiguration ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration;
class UScriptStruct* FAuracronDynamicMusicConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronAudioBridge(), TEXT("AuracronDynamicMusicConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainMenuMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de menu principal */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de menu principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionSelectMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de campe\xc3\x83\xc2\xb5""es" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceRealmMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de jogo - Plan\xc3\x83\xc2\xad""cie */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de jogo - Plan\xc3\x83\xc2\xad""cie" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyRealmMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de jogo - Firmamento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de jogo - Firmamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UndergroundRealmMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de jogo - Abismo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de jogo - Abismo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de combate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictoryMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de vit\xc3\x83\xc2\xb3ria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de vit\xc3\x83\xc2\xb3ria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefeatMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica de derrota */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica de derrota" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAdaptiveTransitions_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es adaptativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es adaptativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCombatBasedIntensity_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da m\xc3\x83\xc2\xbasica baseada no combate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da m\xc3\x83\xc2\xbasica baseada no combate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicFadeTime_MetaData[] = {
		{ "Category", "Dynamic Music" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de fade entre m\xc3\x83\xc2\xbasicas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de fade entre m\xc3\x83\xc2\xbasicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicMusicVolume_MetaData[] = {
		{ "Category", "Dynamic Music" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Volume da m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volume da m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDynamicMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilitar m\xc3\xbasica din\xc3\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar m\xc3\xbasica din\xc3\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTime_MetaData[] = {
		{ "Category", "Dynamic Music" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de transi\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de transi\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrossfadeTime_MetaData[] = {
		{ "Category", "Dynamic Music" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de crossfade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de crossfade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IntensityLevels_MetaData[] = {
		{ "Category", "Dynamic Music" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadveis de intensidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadveis de intensidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveMusic_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilitar m\xc3\xbasica adaptativa */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar m\xc3\xbasica adaptativa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicTracks_MetaData[] = {
		{ "Category", "Dynamic Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tracks de m\xc3\xbasica por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tracks de m\xc3\xbasica por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MainMenuMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChampionSelectMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SurfaceRealmMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SkyRealmMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UndergroundRealmMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CombatMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_VictoryMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DefeatMusic;
	static void NewProp_bUseAdaptiveTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAdaptiveTransitions;
	static void NewProp_bCombatBasedIntensity_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCombatBasedIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MusicFadeTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DynamicMusicVolume;
	static void NewProp_bEnableDynamicMusic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDynamicMusic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CrossfadeTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_IntensityLevels;
	static void NewProp_bEnableAdaptiveMusic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveMusic;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MusicTracks_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MusicTracks_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_MusicTracks;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDynamicMusicConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MainMenuMusic = { "MainMenuMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, MainMenuMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainMenuMusic_MetaData), NewProp_MainMenuMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_ChampionSelectMusic = { "ChampionSelectMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, ChampionSelectMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionSelectMusic_MetaData), NewProp_ChampionSelectMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_SurfaceRealmMusic = { "SurfaceRealmMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, SurfaceRealmMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceRealmMusic_MetaData), NewProp_SurfaceRealmMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_SkyRealmMusic = { "SkyRealmMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, SkyRealmMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyRealmMusic_MetaData), NewProp_SkyRealmMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_UndergroundRealmMusic = { "UndergroundRealmMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, UndergroundRealmMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UndergroundRealmMusic_MetaData), NewProp_UndergroundRealmMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_CombatMusic = { "CombatMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, CombatMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatMusic_MetaData), NewProp_CombatMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_VictoryMusic = { "VictoryMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, VictoryMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictoryMusic_MetaData), NewProp_VictoryMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_DefeatMusic = { "DefeatMusic", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, DefeatMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefeatMusic_MetaData), NewProp_DefeatMusic_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bUseAdaptiveTransitions_SetBit(void* Obj)
{
	((FAuracronDynamicMusicConfiguration*)Obj)->bUseAdaptiveTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bUseAdaptiveTransitions = { "bUseAdaptiveTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDynamicMusicConfiguration), &Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bUseAdaptiveTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAdaptiveTransitions_MetaData), NewProp_bUseAdaptiveTransitions_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bCombatBasedIntensity_SetBit(void* Obj)
{
	((FAuracronDynamicMusicConfiguration*)Obj)->bCombatBasedIntensity = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bCombatBasedIntensity = { "bCombatBasedIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDynamicMusicConfiguration), &Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bCombatBasedIntensity_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCombatBasedIntensity_MetaData), NewProp_bCombatBasedIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicFadeTime = { "MusicFadeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, MusicFadeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicFadeTime_MetaData), NewProp_MusicFadeTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_DynamicMusicVolume = { "DynamicMusicVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, DynamicMusicVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicMusicVolume_MetaData), NewProp_DynamicMusicVolume_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableDynamicMusic_SetBit(void* Obj)
{
	((FAuracronDynamicMusicConfiguration*)Obj)->bEnableDynamicMusic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableDynamicMusic = { "bEnableDynamicMusic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDynamicMusicConfiguration), &Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableDynamicMusic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDynamicMusic_MetaData), NewProp_bEnableDynamicMusic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, TransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTime_MetaData), NewProp_TransitionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_CrossfadeTime = { "CrossfadeTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, CrossfadeTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrossfadeTime_MetaData), NewProp_CrossfadeTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_IntensityLevels = { "IntensityLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, IntensityLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IntensityLevels_MetaData), NewProp_IntensityLevels_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableAdaptiveMusic_SetBit(void* Obj)
{
	((FAuracronDynamicMusicConfiguration*)Obj)->bEnableAdaptiveMusic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableAdaptiveMusic = { "bEnableAdaptiveMusic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDynamicMusicConfiguration), &Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableAdaptiveMusic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveMusic_MetaData), NewProp_bEnableAdaptiveMusic_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks_ValueProp = { "MusicTracks", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks_Key_KeyProp = { "MusicTracks_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks = { "MusicTracks", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDynamicMusicConfiguration, MusicTracks), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicTracks_MetaData), NewProp_MusicTracks_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MainMenuMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_ChampionSelectMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_SurfaceRealmMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_SkyRealmMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_UndergroundRealmMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_CombatMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_VictoryMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_DefeatMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bUseAdaptiveTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bCombatBasedIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicFadeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_DynamicMusicVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableDynamicMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_CrossfadeTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_IntensityLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_bEnableAdaptiveMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewProp_MusicTracks,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
	nullptr,
	&NewStructOps,
	"AuracronDynamicMusicConfiguration",
	Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::PropPointers),
	sizeof(FAuracronDynamicMusicConfiguration),
	alignof(FAuracronDynamicMusicConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDynamicMusicConfiguration **********************************

// ********** Begin ScriptStruct FAuracronSFXConfiguration *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration;
class UScriptStruct* FAuracronSFXConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronSFXConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronAudioBridge(), TEXT("AuracronSFXConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos sonoros\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionAbilitySounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons de habilidades por campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons de habilidades por campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactSounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons de impacto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UISounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons de UI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons de UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmAmbientSounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons ambientes por realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons ambientes por realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NotificationSounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sons de notifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sons de notifica\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRandomVariations_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es aleat\xc3\x83\xc2\xb3rias */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar varia\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es aleat\xc3\x83\xc2\xb3rias" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSimultaneousSounds_MetaData[] = {
		{ "Category", "SFX Configuration" },
		{ "ClampMax", "100" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de sons simult\xc3\x83\xc2\xa2neos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero m\xc3\x83\xc2\xa1ximo de sons simult\xc3\x83\xc2\xa2neos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAudioComponentPooling_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar pooling de componentes de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar pooling de componentes de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponentPoolSize_MetaData[] = {
		{ "Category", "SFX Configuration" },
		{ "ClampMax", "50" },
		{ "ClampMin", "5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do pool de componentes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do pool de componentes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRandomVariations_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilitar varia\xc3\xa7\xc3\xb5""es aleat\xc3\xb3rias */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar varia\xc3\xa7\xc3\xb5""es aleat\xc3\xb3rias" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioComponentPooling_MetaData[] = {
		{ "Category", "SFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilitar pooling de componentes de \xc3\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar pooling de componentes de \xc3\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolSize_MetaData[] = {
		{ "Category", "SFX Configuration" },
		{ "ClampMax", "100" },
		{ "ClampMin", "5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do pool */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do pool" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChampionAbilitySounds_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionAbilitySounds_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ChampionAbilitySounds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ImpactSounds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MovementSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MovementSounds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UISounds_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UISounds_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_UISounds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RealmAmbientSounds_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmAmbientSounds;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NotificationSounds_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NotificationSounds_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NotificationSounds;
	static void NewProp_bUseRandomVariations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRandomVariations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSimultaneousSounds;
	static void NewProp_bUseAudioComponentPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAudioComponentPooling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AudioComponentPoolSize;
	static void NewProp_bEnableRandomVariations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRandomVariations;
	static void NewProp_bEnableAudioComponentPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioComponentPooling;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronSFXConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds_ValueProp = { "ChampionAbilitySounds", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds_Key_KeyProp = { "ChampionAbilitySounds_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds = { "ChampionAbilitySounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, ChampionAbilitySounds), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionAbilitySounds_MetaData), NewProp_ChampionAbilitySounds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ImpactSounds_Inner = { "ImpactSounds", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ImpactSounds = { "ImpactSounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, ImpactSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactSounds_MetaData), NewProp_ImpactSounds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MovementSounds_Inner = { "MovementSounds", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MovementSounds = { "MovementSounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, MovementSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSounds_MetaData), NewProp_MovementSounds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds_ValueProp = { "UISounds", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds_Key_KeyProp = { "UISounds_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds = { "UISounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, UISounds), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UISounds_MetaData), NewProp_UISounds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_RealmAmbientSounds_Inner = { "RealmAmbientSounds", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_RealmAmbientSounds = { "RealmAmbientSounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, RealmAmbientSounds), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmAmbientSounds_MetaData), NewProp_RealmAmbientSounds_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds_ValueProp = { "NotificationSounds", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USoundCue_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds_Key_KeyProp = { "NotificationSounds_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds = { "NotificationSounds", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, NotificationSounds), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NotificationSounds_MetaData), NewProp_NotificationSounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseRandomVariations_SetBit(void* Obj)
{
	((FAuracronSFXConfiguration*)Obj)->bUseRandomVariations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseRandomVariations = { "bUseRandomVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSFXConfiguration), &Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseRandomVariations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRandomVariations_MetaData), NewProp_bUseRandomVariations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MaxSimultaneousSounds = { "MaxSimultaneousSounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, MaxSimultaneousSounds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSimultaneousSounds_MetaData), NewProp_MaxSimultaneousSounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseAudioComponentPooling_SetBit(void* Obj)
{
	((FAuracronSFXConfiguration*)Obj)->bUseAudioComponentPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseAudioComponentPooling = { "bUseAudioComponentPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSFXConfiguration), &Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseAudioComponentPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAudioComponentPooling_MetaData), NewProp_bUseAudioComponentPooling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_AudioComponentPoolSize = { "AudioComponentPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, AudioComponentPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponentPoolSize_MetaData), NewProp_AudioComponentPoolSize_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableRandomVariations_SetBit(void* Obj)
{
	((FAuracronSFXConfiguration*)Obj)->bEnableRandomVariations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableRandomVariations = { "bEnableRandomVariations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSFXConfiguration), &Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableRandomVariations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRandomVariations_MetaData), NewProp_bEnableRandomVariations_MetaData) };
void Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableAudioComponentPooling_SetBit(void* Obj)
{
	((FAuracronSFXConfiguration*)Obj)->bEnableAudioComponentPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableAudioComponentPooling = { "bEnableAudioComponentPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronSFXConfiguration), &Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableAudioComponentPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioComponentPooling_MetaData), NewProp_bEnableAudioComponentPooling_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_PoolSize = { "PoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronSFXConfiguration, PoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolSize_MetaData), NewProp_PoolSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ChampionAbilitySounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ImpactSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_ImpactSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MovementSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MovementSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_UISounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_RealmAmbientSounds_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_RealmAmbientSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_NotificationSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseRandomVariations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_MaxSimultaneousSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bUseAudioComponentPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_AudioComponentPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableRandomVariations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_bEnableAudioComponentPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewProp_PoolSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
	nullptr,
	&NewStructOps,
	"AuracronSFXConfiguration",
	Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::PropPointers),
	sizeof(FAuracronSFXConfiguration),
	alignof(FAuracronSFXConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronSFXConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronSFXConfiguration *******************************************

// ********** Begin Delegate FOnMusicChanged *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics
{
	struct AuracronAudioBridge_eventOnMusicChanged_Parms
	{
		FString OldMusic;
		FString NewMusic;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando m\xc3\x83\xc2\xbasica muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando m\xc3\x83\xc2\xbasica muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OldMusic;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewMusic;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::NewProp_OldMusic = { "OldMusic", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventOnMusicChanged_Parms, OldMusic), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::NewProp_NewMusic = { "NewMusic", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventOnMusicChanged_Parms, NewMusic), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::NewProp_OldMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::NewProp_NewMusic,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnMusicChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::AuracronAudioBridge_eventOnMusicChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::AuracronAudioBridge_eventOnMusicChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAudioBridge::FOnMusicChanged_DelegateWrapper(const FMulticastScriptDelegate& OnMusicChanged, const FString& OldMusic, const FString& NewMusic)
{
	struct AuracronAudioBridge_eventOnMusicChanged_Parms
	{
		FString OldMusic;
		FString NewMusic;
	};
	AuracronAudioBridge_eventOnMusicChanged_Parms Parms;
	Parms.OldMusic=OldMusic;
	Parms.NewMusic=NewMusic;
	OnMusicChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnMusicChanged *********************************************************

// ********** Begin Delegate FOnAudioLayerChanged **************************************************
struct Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics
{
	struct AuracronAudioBridge_eventOnAudioLayerChanged_Parms
	{
		EAuracronAudioLayer OldLayer;
		EAuracronAudioLayer NewLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando camada de \xc3\x83\xc2\xa1udio muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando camada de \xc3\x83\xc2\xa1udio muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_OldLayer = { "OldLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventOnAudioLayerChanged_Parms, OldLayer), Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer, METADATA_PARAMS(0, nullptr) }; // 648783982
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventOnAudioLayerChanged_Parms, NewLayer), Z_Construct_UEnum_AuracronAudioBridge_EAuracronAudioLayer, METADATA_PARAMS(0, nullptr) }; // 648783982
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_OldLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnAudioLayerChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::AuracronAudioBridge_eventOnAudioLayerChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::AuracronAudioBridge_eventOnAudioLayerChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAudioBridge::FOnAudioLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLayerChanged, EAuracronAudioLayer OldLayer, EAuracronAudioLayer NewLayer)
{
	struct AuracronAudioBridge_eventOnAudioLayerChanged_Parms
	{
		EAuracronAudioLayer OldLayer;
		EAuracronAudioLayer NewLayer;
	};
	AuracronAudioBridge_eventOnAudioLayerChanged_Parms Parms;
	Parms.OldLayer=OldLayer;
	Parms.NewLayer=NewLayer;
	OnAudioLayerChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioLayerChanged ****************************************************

// ********** Begin Class UAuracronAudioBridge Function ApplyAudioConfiguration ********************
struct Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics
{
	struct AuracronAudioBridge_eventApplyAudioConfiguration_Parms
	{
		FAuracronAudioConfiguration Configuration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyAudioConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2689084610
void Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventApplyAudioConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventApplyAudioConfiguration_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ApplyAudioConfiguration", Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::AuracronAudioBridge_eventApplyAudioConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::AuracronAudioBridge_eventApplyAudioConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execApplyAudioConfiguration)
{
	P_GET_STRUCT_REF(FAuracronAudioConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyAudioConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ApplyAudioConfiguration **********************

// ********** Begin Class UAuracronAudioBridge Function ApplyDelayEffect ***************************
struct Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics
{
	struct AuracronAudioBridge_eventApplyDelayEffect_Parms
	{
		float DelayTime;
		float Feedback;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeito de delay\n     */" },
#endif
		{ "CPP_Default_Feedback", "0.300000" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeito de delay" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DelayTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Feedback;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_DelayTime = { "DelayTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyDelayEffect_Parms, DelayTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_Feedback = { "Feedback", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyDelayEffect_Parms, Feedback), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventApplyDelayEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventApplyDelayEffect_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_DelayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_Feedback,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ApplyDelayEffect", Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::AuracronAudioBridge_eventApplyDelayEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::AuracronAudioBridge_eventApplyDelayEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execApplyDelayEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DelayTime);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Feedback);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyDelayEffect(Z_Param_DelayTime,Z_Param_Feedback);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ApplyDelayEffect *****************************

// ********** Begin Class UAuracronAudioBridge Function ApplyFrequencyFilter ***********************
struct Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics
{
	struct AuracronAudioBridge_eventApplyFrequencyFilter_Parms
	{
		float LowPassFreq;
		float HighPassFreq;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar filtro de frequ\xc3\x83\xc2\xaancia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar filtro de frequ\xc3\x83\xc2\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LowPassFreq;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HighPassFreq;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_LowPassFreq = { "LowPassFreq", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyFrequencyFilter_Parms, LowPassFreq), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_HighPassFreq = { "HighPassFreq", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyFrequencyFilter_Parms, HighPassFreq), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventApplyFrequencyFilter_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventApplyFrequencyFilter_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_LowPassFreq,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_HighPassFreq,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ApplyFrequencyFilter", Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::AuracronAudioBridge_eventApplyFrequencyFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::AuracronAudioBridge_eventApplyFrequencyFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execApplyFrequencyFilter)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_LowPassFreq);
	P_GET_PROPERTY(FFloatProperty,Z_Param_HighPassFreq);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyFrequencyFilter(Z_Param_LowPassFreq,Z_Param_HighPassFreq);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ApplyFrequencyFilter *************************

// ********** Begin Class UAuracronAudioBridge Function ApplyRealmAudioEffects *********************
struct Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics
{
	struct AuracronAudioBridge_eventApplyRealmAudioEffects_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeitos de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos de realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyRealmAudioEffects_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventApplyRealmAudioEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventApplyRealmAudioEffects_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ApplyRealmAudioEffects", Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::AuracronAudioBridge_eventApplyRealmAudioEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::AuracronAudioBridge_eventApplyRealmAudioEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execApplyRealmAudioEffects)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyRealmAudioEffects(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ApplyRealmAudioEffects ***********************

// ********** Begin Class UAuracronAudioBridge Function ApplyReverbEffect **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics
{
	struct AuracronAudioBridge_eventApplyReverbEffect_Parms
	{
		FString ReverbType;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeito de reverb\n     */" },
#endif
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeito de reverb" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReverbType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReverbType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReverbType = { "ReverbType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyReverbEffect_Parms, ReverbType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReverbType_MetaData), NewProp_ReverbType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventApplyReverbEffect_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventApplyReverbEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventApplyReverbEffect_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReverbType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ApplyReverbEffect", Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::AuracronAudioBridge_eventApplyReverbEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::AuracronAudioBridge_eventApplyReverbEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execApplyReverbEffect)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ReverbType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyReverbEffect(Z_Param_ReverbType,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ApplyReverbEffect ****************************

// ********** Begin Class UAuracronAudioBridge Function ChangeToRealmAudio *************************
struct Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics
{
	struct AuracronAudioBridge_eventChangeToRealmAudio_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Mudar \xc3\x83\xc2\xa1udio para realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mudar \xc3\x83\xc2\xa1udio para realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventChangeToRealmAudio_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventChangeToRealmAudio_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventChangeToRealmAudio_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ChangeToRealmAudio", Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::AuracronAudioBridge_eventChangeToRealmAudio_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::AuracronAudioBridge_eventChangeToRealmAudio_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execChangeToRealmAudio)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ChangeToRealmAudio(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ChangeToRealmAudio ***************************

// ********** Begin Class UAuracronAudioBridge Function LoadAudioSettings **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics
{
	struct AuracronAudioBridge_eventLoadAudioSettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de \xc3\x83\xc2\xa1udio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de \xc3\x83\xc2\xa1udio" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventLoadAudioSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventLoadAudioSettings_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "LoadAudioSettings", Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::AuracronAudioBridge_eventLoadAudioSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::AuracronAudioBridge_eventLoadAudioSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execLoadAudioSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadAudioSettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function LoadAudioSettings ****************************

// ********** Begin Class UAuracronAudioBridge Function OnAudioComponentFinished *******************
struct Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics
{
	struct AuracronAudioBridge_eventOnAudioComponentFinished_Parms
	{
		UAudioComponent* AudioComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para quando um componente de \xc3\xa1udio termina de tocar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para quando um componente de \xc3\xa1udio termina de tocar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AudioComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::NewProp_AudioComponent = { "AudioComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventOnAudioComponentFinished_Parms, AudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponent_MetaData), NewProp_AudioComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::NewProp_AudioComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnAudioComponentFinished", Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::AuracronAudioBridge_eventOnAudioComponentFinished_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::AuracronAudioBridge_eventOnAudioComponentFinished_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execOnAudioComponentFinished)
{
	P_GET_OBJECT(UAudioComponent,Z_Param_AudioComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAudioComponentFinished(Z_Param_AudioComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function OnAudioComponentFinished *********************

// ********** Begin Class UAuracronAudioBridge Function OnAudioFinishedModern **********************
struct Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback moderno UE5.6 para quando \xc3\xa1udio termina (sem par\xc3\xa2metros) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback moderno UE5.6 para quando \xc3\xa1udio termina (sem par\xc3\xa2metros)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnAudioFinishedModern", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execOnAudioFinishedModern)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAudioFinishedModern();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function OnAudioFinishedModern ************************

// ********** Begin Class UAuracronAudioBridge Function OnRep_CurrentMusic *************************
struct Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnRep_CurrentMusic", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execOnRep_CurrentMusic)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentMusic();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function OnRep_CurrentMusic ***************************

// ********** Begin Class UAuracronAudioBridge Function OnRep_MusicIntensity ***********************
struct Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "OnRep_MusicIntensity", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execOnRep_MusicIntensity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_MusicIntensity();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function OnRep_MusicIntensity *************************

// ********** Begin Class UAuracronAudioBridge Function PauseAllSounds *****************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics
{
	struct AuracronAudioBridge_eventPauseAllSounds_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Pausar todos os sons\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pausar todos os sons" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPauseAllSounds_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPauseAllSounds_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PauseAllSounds", Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::AuracronAudioBridge_eventPauseAllSounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::AuracronAudioBridge_eventPauseAllSounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPauseAllSounds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PauseAllSounds();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PauseAllSounds *******************************

// ********** Begin Class UAuracronAudioBridge Function PlayAbilitySound ***************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics
{
	struct AuracronAudioBridge_eventPlayAbilitySound_Parms
	{
		FString ChampionID;
		FString AbilitySlot;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir som de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir som de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayAbilitySound_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayAbilitySound_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayAbilitySound_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayAbilitySound_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayAbilitySound_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayAbilitySound", Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::AuracronAudioBridge_eventPlayAbilitySound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::AuracronAudioBridge_eventPlayAbilitySound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayAbilitySound)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayAbilitySound(Z_Param_ChampionID,Z_Param_AbilitySlot,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayAbilitySound *****************************

// ********** Begin Class UAuracronAudioBridge Function PlayChampionVoice **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics
{
	struct AuracronAudioBridge_eventPlayChampionVoice_Parms
	{
		FString ChampionID;
		FString VoiceLineType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir voz de campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir voz de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoiceLineType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VoiceLineType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayChampionVoice_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_VoiceLineType = { "VoiceLineType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayChampionVoice_Parms, VoiceLineType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoiceLineType_MetaData), NewProp_VoiceLineType_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayChampionVoice_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayChampionVoice_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_VoiceLineType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayChampionVoice", Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::AuracronAudioBridge_eventPlayChampionVoice_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::AuracronAudioBridge_eventPlayChampionVoice_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayChampionVoice)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_VoiceLineType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayChampionVoice(Z_Param_ChampionID,Z_Param_VoiceLineType);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayChampionVoice ****************************

// ********** Begin Class UAuracronAudioBridge Function PlayDynamicMusic ***************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics
{
	struct AuracronAudioBridge_eventPlayDynamicMusic_Parms
	{
		FString MusicType;
		bool bLoop;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica\n     */" },
#endif
		{ "CPP_Default_bLoop", "true" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MusicType;
	static void NewProp_bLoop_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLoop;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_MusicType = { "MusicType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayDynamicMusic_Parms, MusicType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicType_MetaData), NewProp_MusicType_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_bLoop_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayDynamicMusic_Parms*)Obj)->bLoop = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_bLoop = { "bLoop", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayDynamicMusic_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_bLoop_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayDynamicMusic_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayDynamicMusic_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_MusicType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_bLoop,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayDynamicMusic", Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::AuracronAudioBridge_eventPlayDynamicMusic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::AuracronAudioBridge_eventPlayDynamicMusic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayDynamicMusic)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MusicType);
	P_GET_UBOOL(Z_Param_bLoop);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayDynamicMusic(Z_Param_MusicType,Z_Param_bLoop);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayDynamicMusic *****************************

// ********** Begin Class UAuracronAudioBridge Function PlayMetaSound ******************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics
{
	struct AuracronAudioBridge_eventPlayMetaSound_Parms
	{
		USoundBase* MetaSound;
		FVector Location;
		TMap<FString,float> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|MetaSounds" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir MetaSound\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir MetaSound" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MetaSound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_MetaSound = { "MetaSound", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayMetaSound_Parms, MetaSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayMetaSound_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayMetaSound_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayMetaSound_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayMetaSound_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_MetaSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayMetaSound", Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::AuracronAudioBridge_eventPlayMetaSound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::AuracronAudioBridge_eventPlayMetaSound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayMetaSound)
{
	P_GET_OBJECT(USoundBase,Z_Param_MetaSound);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayMetaSound(Z_Param_MetaSound,Z_Param_Out_Location,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayMetaSound ********************************

// ********** Begin Class UAuracronAudioBridge Function PlayMovementSound **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics
{
	struct AuracronAudioBridge_eventPlayMovementSound_Parms
	{
		FString MovementType;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Champion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir som de movimento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir som de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MovementType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_MovementType = { "MovementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayMovementSound_Parms, MovementType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementType_MetaData), NewProp_MovementType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayMovementSound_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayMovementSound_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayMovementSound_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_MovementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayMovementSound", Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::AuracronAudioBridge_eventPlayMovementSound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::AuracronAudioBridge_eventPlayMovementSound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayMovementSound)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MovementType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayMovementSound(Z_Param_MovementType,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayMovementSound ****************************

// ********** Begin Class UAuracronAudioBridge Function PlayRealmAmbientSound **********************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics
{
	struct AuracronAudioBridge_eventPlayRealmAmbientSound_Parms
	{
		int32 RealmIndex;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir som ambiente de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir som ambiente de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayRealmAmbientSound_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlayRealmAmbientSound_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlayRealmAmbientSound_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlayRealmAmbientSound_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlayRealmAmbientSound", Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::AuracronAudioBridge_eventPlayRealmAmbientSound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::AuracronAudioBridge_eventPlayRealmAmbientSound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlayRealmAmbientSound)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayRealmAmbientSound(Z_Param_RealmIndex,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlayRealmAmbientSound ************************

// ********** Begin Class UAuracronAudioBridge Function PlaySound3D ********************************
struct Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics
{
	struct AuracronAudioBridge_eventPlaySound3D_Parms
	{
		USoundBase* Sound;
		FVector Location;
		float Volume;
		float Pitch;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|3D" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir som 3D\n     */" },
#endif
		{ "CPP_Default_Pitch", "1.000000" },
		{ "CPP_Default_Volume", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir som 3D" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Sound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Pitch;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Sound = { "Sound", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlaySound3D_Parms, Sound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlaySound3D_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlaySound3D_Parms, Volume), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Pitch = { "Pitch", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventPlaySound3D_Parms, Pitch), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventPlaySound3D_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventPlaySound3D_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Sound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Volume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_Pitch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "PlaySound3D", Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::AuracronAudioBridge_eventPlaySound3D_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::AuracronAudioBridge_eventPlaySound3D_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execPlaySound3D)
{
	P_GET_OBJECT(USoundBase,Z_Param_Sound);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Volume);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Pitch);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlaySound3D(Z_Param_Sound,Z_Param_Out_Location,Z_Param_Volume,Z_Param_Pitch);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function PlaySound3D **********************************

// ********** Begin Class UAuracronAudioBridge Function RemoveDelayEffect **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics
{
	struct AuracronAudioBridge_eventRemoveDelayEffect_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Remover efeito de delay\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remover efeito de delay" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventRemoveDelayEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventRemoveDelayEffect_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "RemoveDelayEffect", Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::AuracronAudioBridge_eventRemoveDelayEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::AuracronAudioBridge_eventRemoveDelayEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execRemoveDelayEffect)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveDelayEffect();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function RemoveDelayEffect ****************************

// ********** Begin Class UAuracronAudioBridge Function ResumeAllSounds ****************************
struct Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics
{
	struct AuracronAudioBridge_eventResumeAllSounds_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Retomar todos os sons\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retomar todos os sons" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventResumeAllSounds_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventResumeAllSounds_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ResumeAllSounds", Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::AuracronAudioBridge_eventResumeAllSounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::AuracronAudioBridge_eventResumeAllSounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execResumeAllSounds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResumeAllSounds();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ResumeAllSounds ******************************

// ********** Begin Class UAuracronAudioBridge Function SaveAudioSettings **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics
{
	struct AuracronAudioBridge_eventSaveAudioSettings_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Salvar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de \xc3\x83\xc2\xa1udio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Salvar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es de \xc3\x83\xc2\xa1udio" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventSaveAudioSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventSaveAudioSettings_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "SaveAudioSettings", Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::AuracronAudioBridge_eventSaveAudioSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::AuracronAudioBridge_eventSaveAudioSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execSaveAudioSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveAudioSettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function SaveAudioSettings ****************************

// ********** Begin Class UAuracronAudioBridge Function SetMusicIntensity **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics
{
	struct AuracronAudioBridge_eventSetMusicIntensity_Parms
	{
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir intensidade da m\xc3\x83\xc2\xbasica\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir intensidade da m\xc3\x83\xc2\xbasica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventSetMusicIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventSetMusicIntensity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventSetMusicIntensity_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "SetMusicIntensity", Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::AuracronAudioBridge_eventSetMusicIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::AuracronAudioBridge_eventSetMusicIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execSetMusicIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetMusicIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function SetMusicIntensity ****************************

// ********** Begin Class UAuracronAudioBridge Function StopCurrentMusic ***************************
struct Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics
{
	struct AuracronAudioBridge_eventStopCurrentMusic_Parms
	{
		bool bFadeOut;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar m\xc3\x83\xc2\xbasica atual\n     */" },
#endif
		{ "CPP_Default_bFadeOut", "true" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar m\xc3\x83\xc2\xbasica atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bFadeOut_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFadeOut;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_bFadeOut_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventStopCurrentMusic_Parms*)Obj)->bFadeOut = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_bFadeOut = { "bFadeOut", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventStopCurrentMusic_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_bFadeOut_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventStopCurrentMusic_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventStopCurrentMusic_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_bFadeOut,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "StopCurrentMusic", Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::AuracronAudioBridge_eventStopCurrentMusic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::AuracronAudioBridge_eventStopCurrentMusic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execStopCurrentMusic)
{
	P_GET_UBOOL(Z_Param_bFadeOut);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopCurrentMusic(Z_Param_bFadeOut);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function StopCurrentMusic *****************************

// ********** Begin Class UAuracronAudioBridge Function StopSound **********************************
struct Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics
{
	struct AuracronAudioBridge_eventStopSound_Parms
	{
		UAudioComponent* AudioComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar som\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar som" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AudioComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_AudioComponent = { "AudioComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventStopSound_Parms, AudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponent_MetaData), NewProp_AudioComponent_MetaData) };
void Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventStopSound_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventStopSound_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_AudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "StopSound", Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::AuracronAudioBridge_eventStopSound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::AuracronAudioBridge_eventStopSound_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_StopSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_StopSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execStopSound)
{
	P_GET_OBJECT(UAudioComponent,Z_Param_AudioComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopSound(Z_Param_AudioComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function StopSound ************************************

// ********** Begin Class UAuracronAudioBridge Function TransitionToMusic **************************
struct Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics
{
	struct AuracronAudioBridge_eventTransitionToMusic_Parms
	{
		FString NewMusicType;
		float TransitionTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Audio|Music" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Fazer transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para nova m\xc3\x83\xc2\xbasica\n     */" },
#endif
		{ "CPP_Default_TransitionTime", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fazer transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para nova m\xc3\x83\xc2\xbasica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewMusicType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewMusicType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_NewMusicType = { "NewMusicType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventTransitionToMusic_Parms, NewMusicType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewMusicType_MetaData), NewProp_NewMusicType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_TransitionTime = { "TransitionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventTransitionToMusic_Parms, TransitionTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventTransitionToMusic_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventTransitionToMusic_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_NewMusicType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_TransitionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "TransitionToMusic", Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::AuracronAudioBridge_eventTransitionToMusic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::AuracronAudioBridge_eventTransitionToMusic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execTransitionToMusic)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NewMusicType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TransitionToMusic(Z_Param_NewMusicType,Z_Param_TransitionTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function TransitionToMusic ****************************

// ********** Begin Class UAuracronAudioBridge Function ValidateAudioConfiguration *****************
struct Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics
{
	struct AuracronAudioBridge_eventValidateAudioConfiguration_Parms
	{
		FAuracronAudioConfiguration Configuration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAudioBridge_eventValidateAudioConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2689084610
void Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAudioBridge_eventValidateAudioConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAudioBridge_eventValidateAudioConfiguration_Parms), &Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAudioBridge, nullptr, "ValidateAudioConfiguration", Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::AuracronAudioBridge_eventValidateAudioConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::AuracronAudioBridge_eventValidateAudioConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAudioBridge::execValidateAudioConfiguration)
{
	P_GET_STRUCT_REF(FAuracronAudioConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAudioConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronAudioBridge Function ValidateAudioConfiguration *******************

// ********** Begin Class UAuracronAudioBridge *****************************************************
void UAuracronAudioBridge::StaticRegisterNativesUAuracronAudioBridge()
{
	UClass* Class = UAuracronAudioBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAudioConfiguration", &UAuracronAudioBridge::execApplyAudioConfiguration },
		{ "ApplyDelayEffect", &UAuracronAudioBridge::execApplyDelayEffect },
		{ "ApplyFrequencyFilter", &UAuracronAudioBridge::execApplyFrequencyFilter },
		{ "ApplyRealmAudioEffects", &UAuracronAudioBridge::execApplyRealmAudioEffects },
		{ "ApplyReverbEffect", &UAuracronAudioBridge::execApplyReverbEffect },
		{ "ChangeToRealmAudio", &UAuracronAudioBridge::execChangeToRealmAudio },
		{ "LoadAudioSettings", &UAuracronAudioBridge::execLoadAudioSettings },
		{ "OnAudioComponentFinished", &UAuracronAudioBridge::execOnAudioComponentFinished },
		{ "OnAudioFinishedModern", &UAuracronAudioBridge::execOnAudioFinishedModern },
		{ "OnRep_CurrentMusic", &UAuracronAudioBridge::execOnRep_CurrentMusic },
		{ "OnRep_MusicIntensity", &UAuracronAudioBridge::execOnRep_MusicIntensity },
		{ "PauseAllSounds", &UAuracronAudioBridge::execPauseAllSounds },
		{ "PlayAbilitySound", &UAuracronAudioBridge::execPlayAbilitySound },
		{ "PlayChampionVoice", &UAuracronAudioBridge::execPlayChampionVoice },
		{ "PlayDynamicMusic", &UAuracronAudioBridge::execPlayDynamicMusic },
		{ "PlayMetaSound", &UAuracronAudioBridge::execPlayMetaSound },
		{ "PlayMovementSound", &UAuracronAudioBridge::execPlayMovementSound },
		{ "PlayRealmAmbientSound", &UAuracronAudioBridge::execPlayRealmAmbientSound },
		{ "PlaySound3D", &UAuracronAudioBridge::execPlaySound3D },
		{ "RemoveDelayEffect", &UAuracronAudioBridge::execRemoveDelayEffect },
		{ "ResumeAllSounds", &UAuracronAudioBridge::execResumeAllSounds },
		{ "SaveAudioSettings", &UAuracronAudioBridge::execSaveAudioSettings },
		{ "SetMusicIntensity", &UAuracronAudioBridge::execSetMusicIntensity },
		{ "StopCurrentMusic", &UAuracronAudioBridge::execStopCurrentMusic },
		{ "StopSound", &UAuracronAudioBridge::execStopSound },
		{ "TransitionToMusic", &UAuracronAudioBridge::execTransitionToMusic },
		{ "ValidateAudioConfiguration", &UAuracronAudioBridge::execValidateAudioConfiguration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAudioBridge;
UClass* UAuracronAudioBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronAudioBridge;
	if (!Z_Registration_Info_UClass_UAuracronAudioBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAudioBridge"),
			Z_Registration_Info_UClass_UAuracronAudioBridge.InnerSingleton,
			StaticRegisterNativesUAuracronAudioBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAudioBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAudioBridge_NoRegister()
{
	return UAuracronAudioBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAudioBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de \xc3\x83\xc2\x81udio MetaSounds\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de \xc3\x83\xc2\xa1udio 3D e m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica\n */" },
#endif
		{ "DisplayName", "AURACRON Audio Bridge" },
		{ "IncludePath", "AuracronAudioBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de \xc3\x83\xc2\x81udio MetaSounds\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de \xc3\x83\xc2\xa1udio 3D e m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o geral de \xc3\x83\xc2\xa1udio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o geral de \xc3\x83\xc2\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicMusicConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de m\xc3\x83\xc2\xbasica din\xc3\x83\xc2\xa2mica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SFXConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos sonoros */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos sonoros" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveAudioComponents_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de \xc3\x83\xc2\xa1udio ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de \xc3\x83\xc2\xa1udio ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMusicType_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica atual tocando */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica atual tocando" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMusicIntensity_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade da m\xc3\x83\xc2\xbasica atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade da m\xc3\x83\xc2\xbasica atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMusic_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xbasica atual (objeto) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xbasica atual (objeto)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMusicComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de m\xc3\x83\xc2\xbasica atual */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de m\xc3\x83\xc2\xbasica atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentReverbEffect_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito de reverb atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito de reverb atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnMusicChanged_MetaData[] = {
		{ "Category", "AURACRON Audio|Events" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioLayerChanged_MetaData[] = {
		{ "Category", "AURACRON Audio|Events" },
		{ "ModuleRelativePath", "Public/AuracronAudioBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AudioConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DynamicMusicConfiguration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SFXConfiguration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveAudioComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveAudioComponents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentMusicType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentMusicIntensity;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentMusic;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentMusicComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentReverbEffect;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnMusicChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioLayerChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ApplyAudioConfiguration, "ApplyAudioConfiguration" }, // 645299736
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ApplyDelayEffect, "ApplyDelayEffect" }, // 489225785
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ApplyFrequencyFilter, "ApplyFrequencyFilter" }, // 2347011945
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ApplyRealmAudioEffects, "ApplyRealmAudioEffects" }, // 1765774827
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ApplyReverbEffect, "ApplyReverbEffect" }, // 597390770
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ChangeToRealmAudio, "ChangeToRealmAudio" }, // 2662635681
		{ &Z_Construct_UFunction_UAuracronAudioBridge_LoadAudioSettings, "LoadAudioSettings" }, // 1923485945
		{ &Z_Construct_UFunction_UAuracronAudioBridge_OnAudioComponentFinished, "OnAudioComponentFinished" }, // 346762397
		{ &Z_Construct_UFunction_UAuracronAudioBridge_OnAudioFinishedModern, "OnAudioFinishedModern" }, // 4068226850
		{ &Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature, "OnAudioLayerChanged__DelegateSignature" }, // 1161809722
		{ &Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature, "OnMusicChanged__DelegateSignature" }, // 3311271498
		{ &Z_Construct_UFunction_UAuracronAudioBridge_OnRep_CurrentMusic, "OnRep_CurrentMusic" }, // 1365191472
		{ &Z_Construct_UFunction_UAuracronAudioBridge_OnRep_MusicIntensity, "OnRep_MusicIntensity" }, // 3248758114
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PauseAllSounds, "PauseAllSounds" }, // 1474237625
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayAbilitySound, "PlayAbilitySound" }, // 2427241905
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayChampionVoice, "PlayChampionVoice" }, // 3281777305
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayDynamicMusic, "PlayDynamicMusic" }, // 2587223497
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayMetaSound, "PlayMetaSound" }, // 1683784370
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayMovementSound, "PlayMovementSound" }, // 3114521756
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlayRealmAmbientSound, "PlayRealmAmbientSound" }, // 3090864873
		{ &Z_Construct_UFunction_UAuracronAudioBridge_PlaySound3D, "PlaySound3D" }, // 1899648128
		{ &Z_Construct_UFunction_UAuracronAudioBridge_RemoveDelayEffect, "RemoveDelayEffect" }, // 3835123644
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ResumeAllSounds, "ResumeAllSounds" }, // 3132225798
		{ &Z_Construct_UFunction_UAuracronAudioBridge_SaveAudioSettings, "SaveAudioSettings" }, // 826008255
		{ &Z_Construct_UFunction_UAuracronAudioBridge_SetMusicIntensity, "SetMusicIntensity" }, // 2149606497
		{ &Z_Construct_UFunction_UAuracronAudioBridge_StopCurrentMusic, "StopCurrentMusic" }, // 3618730159
		{ &Z_Construct_UFunction_UAuracronAudioBridge_StopSound, "StopSound" }, // 1163727758
		{ &Z_Construct_UFunction_UAuracronAudioBridge_TransitionToMusic, "TransitionToMusic" }, // 755153838
		{ &Z_Construct_UFunction_UAuracronAudioBridge_ValidateAudioConfiguration, "ValidateAudioConfiguration" }, // 135830475
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAudioBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_AudioConfiguration = { "AudioConfiguration", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, AudioConfiguration), Z_Construct_UScriptStruct_FAuracronAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioConfiguration_MetaData), NewProp_AudioConfiguration_MetaData) }; // 2689084610
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_DynamicMusicConfiguration = { "DynamicMusicConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, DynamicMusicConfiguration), Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicMusicConfiguration_MetaData), NewProp_DynamicMusicConfiguration_MetaData) }; // 3028694550
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_SFXConfiguration = { "SFXConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, SFXConfiguration), Z_Construct_UScriptStruct_FAuracronSFXConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SFXConfiguration_MetaData), NewProp_SFXConfiguration_MetaData) }; // 3455543038
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_ActiveAudioComponents_Inner = { "ActiveAudioComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_ActiveAudioComponents = { "ActiveAudioComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, ActiveAudioComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveAudioComponents_MetaData), NewProp_ActiveAudioComponents_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicType = { "CurrentMusicType", "OnRep_CurrentMusic", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, CurrentMusicType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMusicType_MetaData), NewProp_CurrentMusicType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicIntensity = { "CurrentMusicIntensity", "OnRep_MusicIntensity", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, CurrentMusicIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMusicIntensity_MetaData), NewProp_CurrentMusicIntensity_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusic = { "CurrentMusic", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, CurrentMusic), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMusic_MetaData), NewProp_CurrentMusic_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicComponent = { "CurrentMusicComponent", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, CurrentMusicComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMusicComponent_MetaData), NewProp_CurrentMusicComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentReverbEffect = { "CurrentReverbEffect", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, CurrentReverbEffect), Z_Construct_UClass_UReverbEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentReverbEffect_MetaData), NewProp_CurrentReverbEffect_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_OnMusicChanged = { "OnMusicChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, OnMusicChanged), Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnMusicChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnMusicChanged_MetaData), NewProp_OnMusicChanged_MetaData) }; // 3311271498
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_OnAudioLayerChanged = { "OnAudioLayerChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAudioBridge, OnAudioLayerChanged), Z_Construct_UDelegateFunction_UAuracronAudioBridge_OnAudioLayerChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioLayerChanged_MetaData), NewProp_OnAudioLayerChanged_MetaData) }; // 1161809722
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAudioBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_AudioConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_DynamicMusicConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_SFXConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_ActiveAudioComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_ActiveAudioComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentMusicComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_CurrentReverbEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_OnMusicChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAudioBridge_Statics::NewProp_OnAudioLayerChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAudioBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAudioBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAudioBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAudioBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAudioBridge_Statics::ClassParams = {
	&UAuracronAudioBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAudioBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAudioBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAudioBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAudioBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAudioBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronAudioBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAudioBridge.OuterSingleton, Z_Construct_UClass_UAuracronAudioBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAudioBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronAudioBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_AudioConfiguration(TEXT("AudioConfiguration"));
	static FName Name_CurrentMusicType(TEXT("CurrentMusicType"));
	static FName Name_CurrentMusicIntensity(TEXT("CurrentMusicIntensity"));
	const bool bIsValid = true
		&& Name_AudioConfiguration == ClassReps[(int32)ENetFields_Private::AudioConfiguration].Property->GetFName()
		&& Name_CurrentMusicType == ClassReps[(int32)ENetFields_Private::CurrentMusicType].Property->GetFName()
		&& Name_CurrentMusicIntensity == ClassReps[(int32)ENetFields_Private::CurrentMusicIntensity].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronAudioBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAudioBridge);
UAuracronAudioBridge::~UAuracronAudioBridge() {}
// ********** End Class UAuracronAudioBridge *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronAudioType_StaticEnum, TEXT("EAuracronAudioType"), &Z_Registration_Info_UEnum_EAuracronAudioType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3947060078U) },
		{ EAuracronAudioLayer_StaticEnum, TEXT("EAuracronAudioLayer"), &Z_Registration_Info_UEnum_EAuracronAudioLayer, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 648783982U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronAudioConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronAudioConfiguration_Statics::NewStructOps, TEXT("AuracronAudioConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronAudioConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAudioConfiguration), 2689084610U) },
		{ FAuracronDynamicMusicConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDynamicMusicConfiguration_Statics::NewStructOps, TEXT("AuracronDynamicMusicConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDynamicMusicConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDynamicMusicConfiguration), 3028694550U) },
		{ FAuracronSFXConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronSFXConfiguration_Statics::NewStructOps, TEXT("AuracronSFXConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronSFXConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronSFXConfiguration), 3455543038U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAudioBridge, UAuracronAudioBridge::StaticClass, TEXT("UAuracronAudioBridge"), &Z_Registration_Info_UClass_UAuracronAudioBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAudioBridge), 2092989797U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_2318617277(TEXT("/Script/AuracronAudioBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAudioBridge_Public_AuracronAudioBridge_h__Script_AuracronAudioBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
