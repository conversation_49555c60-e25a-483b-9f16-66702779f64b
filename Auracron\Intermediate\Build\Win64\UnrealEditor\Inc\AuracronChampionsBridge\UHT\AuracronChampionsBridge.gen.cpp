// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronChampionsBridge.h"
#include "InputActionValue.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronChampionsBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONCHAMPIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronChampionsBridge();
AURACRONCHAMPIONSBRIDGE_API UClass* Z_Construct_UClass_UAuracronChampionsBridge_NoRegister();
AURACRONCHAMPIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity();
AURACRONCHAMPIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState();
AURACRONCHAMPIONSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionAbilities();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionConfiguration();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionInputConfig();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMovementConfig();
AURACRONCHAMPIONSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionVisualConfig();
CONTROLRIG_API UClass* Z_Construct_UClass_UControlRig_NoRegister();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UAnimInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCharacterMovementComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UEnhancedInputComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
ENHANCEDINPUT_API UScriptStruct* Z_Construct_UScriptStruct_FInputActionValue();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayAbility_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronChampionsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronChampionType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronChampionType;
static UEnum* EAuracronChampionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronChampionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("EAuracronChampionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronChampionType.OuterSingleton;
}
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionType>()
{
	return EAuracronChampionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Assassin.DisplayName", "Assassin" },
		{ "Assassin.Name", "EAuracronChampionType::Assassin" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de Campe\xc3\x83\xc2\xb5""es\n */" },
#endif
		{ "Damage.DisplayName", "Damage" },
		{ "Damage.Name", "EAuracronChampionType::Damage" },
		{ "Mage.DisplayName", "Mage" },
		{ "Mage.Name", "EAuracronChampionType::Mage" },
		{ "Marksman.DisplayName", "Marksman" },
		{ "Marksman.Name", "EAuracronChampionType::Marksman" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronChampionType::None" },
		{ "Support.DisplayName", "Support" },
		{ "Support.Name", "EAuracronChampionType::Support" },
		{ "Tank.DisplayName", "Tank" },
		{ "Tank.Name", "EAuracronChampionType::Tank" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de Campe\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronChampionType::None", (int64)EAuracronChampionType::None },
		{ "EAuracronChampionType::Tank", (int64)EAuracronChampionType::Tank },
		{ "EAuracronChampionType::Damage", (int64)EAuracronChampionType::Damage },
		{ "EAuracronChampionType::Support", (int64)EAuracronChampionType::Support },
		{ "EAuracronChampionType::Assassin", (int64)EAuracronChampionType::Assassin },
		{ "EAuracronChampionType::Mage", (int64)EAuracronChampionType::Mage },
		{ "EAuracronChampionType::Marksman", (int64)EAuracronChampionType::Marksman },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	"EAuracronChampionType",
	"EAuracronChampionType",
	Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronChampionType.InnerSingleton, Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronChampionType.InnerSingleton;
}
// ********** End Enum EAuracronChampionType *******************************************************

// ********** Begin Enum EAuracronChampionRarity ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronChampionRarity;
static UEnum* EAuracronChampionRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronChampionRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("EAuracronChampionRarity"));
	}
	return Z_Registration_Info_UEnum_EAuracronChampionRarity.OuterSingleton;
}
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionRarity>()
{
	return EAuracronChampionRarity_StaticEnum();
}
struct Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de Campe\xc3\x83\xc2\xb5""es\n */" },
#endif
		{ "Common.DisplayName", "Common" },
		{ "Common.Name", "EAuracronChampionRarity::Common" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronChampionRarity::Epic" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EAuracronChampionRarity::Legendary" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
		{ "Mythic.DisplayName", "Mythic" },
		{ "Mythic.Name", "EAuracronChampionRarity::Mythic" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EAuracronChampionRarity::Rare" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para raridade de Campe\xc3\x83\xc2\xb5""es" },
#endif
		{ "Uncommon.DisplayName", "Uncommon" },
		{ "Uncommon.Name", "EAuracronChampionRarity::Uncommon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronChampionRarity::Common", (int64)EAuracronChampionRarity::Common },
		{ "EAuracronChampionRarity::Uncommon", (int64)EAuracronChampionRarity::Uncommon },
		{ "EAuracronChampionRarity::Rare", (int64)EAuracronChampionRarity::Rare },
		{ "EAuracronChampionRarity::Epic", (int64)EAuracronChampionRarity::Epic },
		{ "EAuracronChampionRarity::Legendary", (int64)EAuracronChampionRarity::Legendary },
		{ "EAuracronChampionRarity::Mythic", (int64)EAuracronChampionRarity::Mythic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	"EAuracronChampionRarity",
	"EAuracronChampionRarity",
	Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronChampionRarity.InnerSingleton, Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronChampionRarity.InnerSingleton;
}
// ********** End Enum EAuracronChampionRarity *****************************************************

// ********** Begin Enum EAuracronChampionState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronChampionState;
static UEnum* EAuracronChampionState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronChampionState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("EAuracronChampionState"));
	}
	return Z_Registration_Info_UEnum_EAuracronChampionState.OuterSingleton;
}
template<> AURACRONCHAMPIONSBRIDGE_API UEnum* StaticEnum<EAuracronChampionState>()
{
	return EAuracronChampionState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Attacking.DisplayName", "Attacking" },
		{ "Attacking.Name", "EAuracronChampionState::Attacking" },
		{ "BlueprintType", "true" },
		{ "CastingAbility.DisplayName", "Casting Ability" },
		{ "CastingAbility.Name", "EAuracronChampionState::CastingAbility" },
		{ "Channeling.DisplayName", "Channeling" },
		{ "Channeling.Name", "EAuracronChampionState::Channeling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados do Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "Dead.DisplayName", "Dead" },
		{ "Dead.Name", "EAuracronChampionState::Dead" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "EAuracronChampionState::Idle" },
		{ "Invisible.DisplayName", "Invisible" },
		{ "Invisible.Name", "EAuracronChampionState::Invisible" },
		{ "Invulnerable.DisplayName", "Invulnerable" },
		{ "Invulnerable.Name", "EAuracronChampionState::Invulnerable" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
		{ "Moving.DisplayName", "Moving" },
		{ "Moving.Name", "EAuracronChampionState::Moving" },
		{ "Respawning.DisplayName", "Respawning" },
		{ "Respawning.Name", "EAuracronChampionState::Respawning" },
		{ "Stunned.DisplayName", "Stunned" },
		{ "Stunned.Name", "EAuracronChampionState::Stunned" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para estados do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronChampionState::Idle", (int64)EAuracronChampionState::Idle },
		{ "EAuracronChampionState::Moving", (int64)EAuracronChampionState::Moving },
		{ "EAuracronChampionState::Attacking", (int64)EAuracronChampionState::Attacking },
		{ "EAuracronChampionState::CastingAbility", (int64)EAuracronChampionState::CastingAbility },
		{ "EAuracronChampionState::Stunned", (int64)EAuracronChampionState::Stunned },
		{ "EAuracronChampionState::Dead", (int64)EAuracronChampionState::Dead },
		{ "EAuracronChampionState::Respawning", (int64)EAuracronChampionState::Respawning },
		{ "EAuracronChampionState::Channeling", (int64)EAuracronChampionState::Channeling },
		{ "EAuracronChampionState::Invisible", (int64)EAuracronChampionState::Invisible },
		{ "EAuracronChampionState::Invulnerable", (int64)EAuracronChampionState::Invulnerable },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	"EAuracronChampionState",
	"EAuracronChampionState",
	Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState()
{
	if (!Z_Registration_Info_UEnum_EAuracronChampionState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronChampionState.InnerSingleton, Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronChampionState.InnerSingleton;
}
// ********** End Enum EAuracronChampionState ******************************************************

// ********** Begin ScriptStruct FAuracronChampionBaseAttributes ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes;
class UScriptStruct* FAuracronChampionBaseAttributes::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionBaseAttributes"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para atributos base de um Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para atributos base de um Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de Vida m\xc3\x83\xc2\xa1ximos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de Vida m\xc3\x83\xc2\xa1ximos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de Vida atuais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de Vida atuais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxMana_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana m\xc3\x83\xc2\xa1xima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana m\xc3\x83\xc2\xa1xima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMana_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano de Ataque */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano de Ataque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityPower_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "500.0" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Poder de Habilidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Poder de Habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Armor_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Armadura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armadura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagicResistance_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "300.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resist\xc3\x83\xc2\xaancia M\xc3\x83\xc2\xa1gica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resist\xc3\x83\xc2\xaancia M\xc3\x83\xc2\xa1gica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de Movimento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de Movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackSpeed_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de Ataque */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de Ataque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "800.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alcance de Ataque */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alcance de Ataque" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChance_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Chance de Cr\xc3\x83\xc2\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Chance de Cr\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalDamage_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano Cr\xc3\x83\xc2\xadtico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano Cr\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegeneration_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "50.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de HP por segundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de HP por segundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegeneration_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Mana por segundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regenera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Mana por segundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReduction_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "0.8" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Redu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Cooldown */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Redu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Cooldown" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArmorPenetration_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Penetra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Armadura */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Penetra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de Armadura" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagicPenetration_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Penetra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o M\xc3\x83\xc2\xa1gica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Penetra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o M\xc3\x83\xc2\xa1gica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LifeSteal_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Roubo de Vida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Roubo de Vida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaSteal_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Roubo de Mana */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Roubo de Mana" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tenacity_MetaData[] = {
		{ "Category", "Base Attributes" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tenacidade (resist\xc3\x83\xc2\xaancia a CC) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tenacidade (resist\xc3\x83\xc2\xaancia a CC)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxMana;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentMana;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityPower;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Armor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MagicResistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ArmorPenetration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MagicPenetration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LifeSteal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaSteal;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tenacity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionBaseAttributes>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MaxMana = { "MaxMana", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, MaxMana), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxMana_MetaData), NewProp_MaxMana_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CurrentMana = { "CurrentMana", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, CurrentMana), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMana_MetaData), NewProp_CurrentMana_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackDamage = { "AttackDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, AttackDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AbilityPower = { "AbilityPower", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, AbilityPower), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityPower_MetaData), NewProp_AbilityPower_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_Armor = { "Armor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, Armor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Armor_MetaData), NewProp_Armor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MagicResistance = { "MagicResistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, MagicResistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagicResistance_MetaData), NewProp_MagicResistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackSpeed = { "AttackSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, AttackSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackSpeed_MetaData), NewProp_AttackSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CriticalChance = { "CriticalChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, CriticalChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChance_MetaData), NewProp_CriticalChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CriticalDamage = { "CriticalDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, CriticalDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalDamage_MetaData), NewProp_CriticalDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_HealthRegeneration = { "HealthRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, HealthRegeneration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegeneration_MetaData), NewProp_HealthRegeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ManaRegeneration = { "ManaRegeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, ManaRegeneration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegeneration_MetaData), NewProp_ManaRegeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CooldownReduction = { "CooldownReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, CooldownReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReduction_MetaData), NewProp_CooldownReduction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ArmorPenetration = { "ArmorPenetration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, ArmorPenetration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArmorPenetration_MetaData), NewProp_ArmorPenetration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MagicPenetration = { "MagicPenetration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, MagicPenetration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagicPenetration_MetaData), NewProp_MagicPenetration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_LifeSteal = { "LifeSteal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, LifeSteal), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LifeSteal_MetaData), NewProp_LifeSteal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ManaSteal = { "ManaSteal", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, ManaSteal), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaSteal_MetaData), NewProp_ManaSteal_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_Tenacity = { "Tenacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionBaseAttributes, Tenacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tenacity_MetaData), NewProp_Tenacity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MaxMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CurrentMana,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AbilityPower,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_Armor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MagicResistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CriticalChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CriticalDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_HealthRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ManaRegeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_CooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ArmorPenetration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_MagicPenetration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_LifeSteal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_ManaSteal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewProp_Tenacity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionBaseAttributes",
	Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::PropPointers),
	sizeof(FAuracronChampionBaseAttributes),
	alignof(FAuracronChampionBaseAttributes),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionBaseAttributes *************************************

// ********** Begin ScriptStruct FAuracronChampionAbilities ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities;
class UScriptStruct* FAuracronChampionAbilities::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionAbilities, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionAbilities"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para habilidades do Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para habilidades do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PassiveAbility_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade Passiva */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade Passiva" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QAbility_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade Q */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade Q" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WAbility_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade W */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade W" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EAbility_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade E */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade E" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RAbility_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidade R (Ultimate) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidade R (Ultimate)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloAbilities_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades de Sigilo (quando fusionado) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades de Sigilo (quando fusionado)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityCooldowns_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldowns das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldowns das habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityManaCosts_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custos de mana das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custos de mana das habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityLevels_MetaData[] = {
		{ "Category", "Champion Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadveis das habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadveis das habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableAbilityPoints_MetaData[] = {
		{ "Category", "Champion Abilities" },
		{ "ClampMax", "18" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pontos de habilidade dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pontos de habilidade dispon\xc3\x83\xc2\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_PassiveAbility;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_QAbility;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_WAbility;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_EAbility;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_RAbility;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_SigiloAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SigiloAbilities;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityCooldowns_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityCooldowns_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AbilityCooldowns;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityManaCosts_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityManaCosts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AbilityManaCosts;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AbilityLevels_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilityLevels_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AbilityLevels;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AvailableAbilityPoints;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionAbilities>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_PassiveAbility = { "PassiveAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, PassiveAbility), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PassiveAbility_MetaData), NewProp_PassiveAbility_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_QAbility = { "QAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, QAbility), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QAbility_MetaData), NewProp_QAbility_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_WAbility = { "WAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, WAbility), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WAbility_MetaData), NewProp_WAbility_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_EAbility = { "EAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, EAbility), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EAbility_MetaData), NewProp_EAbility_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_RAbility = { "RAbility", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, RAbility), Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RAbility_MetaData), NewProp_RAbility_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_SigiloAbilities_Inner = { "SigiloAbilities", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UGameplayAbility_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_SigiloAbilities = { "SigiloAbilities", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, SigiloAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloAbilities_MetaData), NewProp_SigiloAbilities_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns_ValueProp = { "AbilityCooldowns", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns_Key_KeyProp = { "AbilityCooldowns_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns = { "AbilityCooldowns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, AbilityCooldowns), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityCooldowns_MetaData), NewProp_AbilityCooldowns_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts_ValueProp = { "AbilityManaCosts", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts_Key_KeyProp = { "AbilityManaCosts_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts = { "AbilityManaCosts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, AbilityManaCosts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityManaCosts_MetaData), NewProp_AbilityManaCosts_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels_ValueProp = { "AbilityLevels", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels_Key_KeyProp = { "AbilityLevels_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels = { "AbilityLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, AbilityLevels), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityLevels_MetaData), NewProp_AbilityLevels_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AvailableAbilityPoints = { "AvailableAbilityPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionAbilities, AvailableAbilityPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableAbilityPoints_MetaData), NewProp_AvailableAbilityPoints_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_PassiveAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_QAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_WAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_EAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_RAbility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_SigiloAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_SigiloAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityCooldowns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityManaCosts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AbilityLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewProp_AvailableAbilityPoints,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionAbilities",
	Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::PropPointers),
	sizeof(FAuracronChampionAbilities),
	alignof(FAuracronChampionAbilities),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionAbilities()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionAbilities ******************************************

// ********** Begin ScriptStruct FAuracronChampionVisualConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig;
class UScriptStruct* FAuracronChampionVisualConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionVisualConfig, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionVisualConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o visual do Campe\xc3\x83\xc2\xa3o (MetaHuman)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o visual do Campe\xc3\x83\xc2\xa3o (MetaHuman)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetaHumanMesh_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh do MetaHuman */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh do MetaHuman" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationBlueprint_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Animation Blueprint */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Blueprint" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FacialControlRig_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Control Rig para customiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o facial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Control Rig para customiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o facial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IKRigDefinition_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** IK Rig para movimento procedural */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "IK Rig para movimento procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionMaterials_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Materiais do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materiais do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlternativeSkins_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Texturas de skin alternativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Texturas de skin alternativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionParticleEffects_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos de part\xc3\x83\xc2\xad""culas do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de part\xc3\x83\xc2\xad""culas do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionScale_MetaData[] = {
		{ "Category", "Visual Configuration" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.5" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryColor_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor prim\xc3\x83\xc2\xa1ria do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor prim\xc3\x83\xc2\xa1ria do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryColor_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor secund\xc3\x83\xc2\xa1ria do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor secund\xc3\x83\xc2\xa1ria do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EyeColor_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor dos olhos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor dos olhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HairColor_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do cabelo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do cabelo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkinColor_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor da pele */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor da pele" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMetaHuman_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usa MetaHuman */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usa MetaHuman" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowFacialCustomization_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite customiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o facial */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite customiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o facial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSupportsAlternativeSkins_MetaData[] = {
		{ "Category", "Visual Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Suporta skins alternativas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Suporta skins alternativas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MetaHumanMesh;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_AnimationBlueprint;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FacialControlRig;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_IKRigDefinition;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChampionMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChampionMaterials;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AlternativeSkins_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AlternativeSkins;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ChampionParticleEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChampionParticleEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChampionScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrimaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SecondaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EyeColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HairColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SkinColor;
	static void NewProp_bUseMetaHuman_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMetaHuman;
	static void NewProp_bAllowFacialCustomization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowFacialCustomization;
	static void NewProp_bSupportsAlternativeSkins_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSupportsAlternativeSkins;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionVisualConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_MetaHumanMesh = { "MetaHumanMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, MetaHumanMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetaHumanMesh_MetaData), NewProp_MetaHumanMesh_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AnimationBlueprint = { "AnimationBlueprint", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, AnimationBlueprint), Z_Construct_UClass_UAnimInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationBlueprint_MetaData), NewProp_AnimationBlueprint_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_FacialControlRig = { "FacialControlRig", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, FacialControlRig), Z_Construct_UClass_UControlRig_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FacialControlRig_MetaData), NewProp_FacialControlRig_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_IKRigDefinition = { "IKRigDefinition", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, IKRigDefinition), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IKRigDefinition_MetaData), NewProp_IKRigDefinition_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionMaterials_Inner = { "ChampionMaterials", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionMaterials = { "ChampionMaterials", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, ChampionMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionMaterials_MetaData), NewProp_ChampionMaterials_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AlternativeSkins_Inner = { "AlternativeSkins", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AlternativeSkins = { "AlternativeSkins", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, AlternativeSkins), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlternativeSkins_MetaData), NewProp_AlternativeSkins_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionParticleEffects_Inner = { "ChampionParticleEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionParticleEffects = { "ChampionParticleEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, ChampionParticleEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionParticleEffects_MetaData), NewProp_ChampionParticleEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionScale = { "ChampionScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, ChampionScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionScale_MetaData), NewProp_ChampionScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_PrimaryColor = { "PrimaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, PrimaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryColor_MetaData), NewProp_PrimaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_SecondaryColor = { "SecondaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, SecondaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryColor_MetaData), NewProp_SecondaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_EyeColor = { "EyeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, EyeColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EyeColor_MetaData), NewProp_EyeColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_HairColor = { "HairColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, HairColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HairColor_MetaData), NewProp_HairColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_SkinColor = { "SkinColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionVisualConfig, SkinColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkinColor_MetaData), NewProp_SkinColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bUseMetaHuman_SetBit(void* Obj)
{
	((FAuracronChampionVisualConfig*)Obj)->bUseMetaHuman = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bUseMetaHuman = { "bUseMetaHuman", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionVisualConfig), &Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bUseMetaHuman_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMetaHuman_MetaData), NewProp_bUseMetaHuman_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bAllowFacialCustomization_SetBit(void* Obj)
{
	((FAuracronChampionVisualConfig*)Obj)->bAllowFacialCustomization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bAllowFacialCustomization = { "bAllowFacialCustomization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionVisualConfig), &Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bAllowFacialCustomization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowFacialCustomization_MetaData), NewProp_bAllowFacialCustomization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bSupportsAlternativeSkins_SetBit(void* Obj)
{
	((FAuracronChampionVisualConfig*)Obj)->bSupportsAlternativeSkins = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bSupportsAlternativeSkins = { "bSupportsAlternativeSkins", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionVisualConfig), &Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bSupportsAlternativeSkins_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSupportsAlternativeSkins_MetaData), NewProp_bSupportsAlternativeSkins_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_MetaHumanMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AnimationBlueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_FacialControlRig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_IKRigDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AlternativeSkins_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_AlternativeSkins,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionParticleEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionParticleEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_ChampionScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_PrimaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_SecondaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_EyeColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_HairColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_SkinColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bUseMetaHuman,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bAllowFacialCustomization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewProp_bSupportsAlternativeSkins,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionVisualConfig",
	Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::PropPointers),
	sizeof(FAuracronChampionVisualConfig),
	alignof(FAuracronChampionVisualConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionVisualConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionVisualConfig ***************************************

// ********** Begin ScriptStruct FAuracronChampionMovementConfig ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig;
class UScriptStruct* FAuracronChampionMovementConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionMovementConfig, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionMovementConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento do Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWalkSpeed_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade m\xc3\x83\xc2\xa1xima de caminhada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade m\xc3\x83\xc2\xa1xima de caminhada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRunSpeed_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "1500.0" },
		{ "ClampMin", "200.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade m\xc3\x83\xc2\xa1xima de corrida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade m\xc3\x83\xc2\xa1xima de corrida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Acceleration_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Acelera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Acelera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Deceleration_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desacelera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desacelera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpZVelocity_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "200.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\x83\xc2\xa7""a de pulo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\x83\xc2\xa7""a de pulo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirControl_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Controle a\xc3\x83\xc2\xa9reo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Controle a\xc3\x83\xc2\xa9reo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GravityScale_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "3.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplicador de gravidade */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplicador de gravidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroundFriction_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "20.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atrito no solo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atrito no solo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrakingFriction_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "20.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atrito de frenagem */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atrito de frenagem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanFly_MetaData[] = {
		{ "Category", "Movement Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode voar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode voar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanSwim_MetaData[] = {
		{ "Category", "Movement Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode nadar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode nadar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanClimbWalls_MetaData[] = {
		{ "Category", "Movement Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pode escalar paredes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pode escalar paredes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowDash_MetaData[] = {
		{ "Category", "Movement Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Permite dash */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Permite dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDistance_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "1000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia do dash */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia do dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCooldown_MetaData[] = {
		{ "Category", "Movement Configuration" },
		{ "ClampMax", "30.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown do dash */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown do dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWalkSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRunSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Acceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Deceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_JumpZVelocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirControl;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GravityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GroundFriction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BrakingFriction;
	static void NewProp_bCanFly_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanFly;
	static void NewProp_bCanSwim_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanSwim;
	static void NewProp_bCanClimbWalls_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanClimbWalls;
	static void NewProp_bAllowDash_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowDash;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashCooldown;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionMovementConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_MaxWalkSpeed = { "MaxWalkSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, MaxWalkSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWalkSpeed_MetaData), NewProp_MaxWalkSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_MaxRunSpeed = { "MaxRunSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, MaxRunSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRunSpeed_MetaData), NewProp_MaxRunSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_Acceleration = { "Acceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, Acceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Acceleration_MetaData), NewProp_Acceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_Deceleration = { "Deceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, Deceleration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Deceleration_MetaData), NewProp_Deceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_JumpZVelocity = { "JumpZVelocity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, JumpZVelocity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpZVelocity_MetaData), NewProp_JumpZVelocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_AirControl = { "AirControl", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, AirControl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirControl_MetaData), NewProp_AirControl_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_GravityScale = { "GravityScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, GravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GravityScale_MetaData), NewProp_GravityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_GroundFriction = { "GroundFriction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, GroundFriction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroundFriction_MetaData), NewProp_GroundFriction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_BrakingFriction = { "BrakingFriction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, BrakingFriction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrakingFriction_MetaData), NewProp_BrakingFriction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanFly_SetBit(void* Obj)
{
	((FAuracronChampionMovementConfig*)Obj)->bCanFly = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanFly = { "bCanFly", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionMovementConfig), &Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanFly_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanFly_MetaData), NewProp_bCanFly_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanSwim_SetBit(void* Obj)
{
	((FAuracronChampionMovementConfig*)Obj)->bCanSwim = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanSwim = { "bCanSwim", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionMovementConfig), &Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanSwim_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanSwim_MetaData), NewProp_bCanSwim_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanClimbWalls_SetBit(void* Obj)
{
	((FAuracronChampionMovementConfig*)Obj)->bCanClimbWalls = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanClimbWalls = { "bCanClimbWalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionMovementConfig), &Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanClimbWalls_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanClimbWalls_MetaData), NewProp_bCanClimbWalls_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bAllowDash_SetBit(void* Obj)
{
	((FAuracronChampionMovementConfig*)Obj)->bAllowDash = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bAllowDash = { "bAllowDash", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionMovementConfig), &Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bAllowDash_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowDash_MetaData), NewProp_bAllowDash_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_DashDistance = { "DashDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, DashDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDistance_MetaData), NewProp_DashDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_DashCooldown = { "DashCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionMovementConfig, DashCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCooldown_MetaData), NewProp_DashCooldown_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_MaxWalkSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_MaxRunSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_Acceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_Deceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_JumpZVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_AirControl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_GravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_GroundFriction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_BrakingFriction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanFly,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanSwim,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bCanClimbWalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_bAllowDash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_DashDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewProp_DashCooldown,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionMovementConfig",
	Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::PropPointers),
	sizeof(FAuracronChampionMovementConfig),
	alignof(FAuracronChampionMovementConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionMovementConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionMovementConfig *************************************

// ********** Begin ScriptStruct FAuracronChampionInputConfig **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig;
class UScriptStruct* FAuracronChampionInputConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionInputConfig, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionInputConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input do Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputMappingContext_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contexto de mapeamento de input */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contexto de mapeamento de input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoveAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de olhar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de olhar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de pulo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de pulo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BasicAttackAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ataque b\xc3\x83\xc2\xa1sico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de ataque b\xc3\x83\xc2\xa1sico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QAbilityAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade Q */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade Q" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WAbilityAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade W */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade W" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EAbilityAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade E */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade E" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RAbilityAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade R */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de habilidade R" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dash */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de dash" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecallAction_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de recall */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "A\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de recall" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MouseSensitivity_MetaData[] = {
		{ "Category", "Input Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade do mouse */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade do mouse" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GamepadSensitivity_MetaData[] = {
		{ "Category", "Input Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sensibilidade do gamepad */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensibilidade do gamepad" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInvertYAxis_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inverter eixo Y */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inverter eixo Y" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSupportTouch_MetaData[] = {
		{ "Category", "Input Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Suporte a touch (mobile) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Suporte a touch (mobile)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InputMappingContext;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MoveAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LookAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_JumpAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BasicAttackAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_QAbilityAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WAbilityAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EAbilityAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RAbilityAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DashAction;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RecallAction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MouseSensitivity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GamepadSensitivity;
	static void NewProp_bInvertYAxis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInvertYAxis;
	static void NewProp_bSupportTouch_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSupportTouch;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionInputConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_InputMappingContext = { "InputMappingContext", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, InputMappingContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputMappingContext_MetaData), NewProp_InputMappingContext_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_MoveAction = { "MoveAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, MoveAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoveAction_MetaData), NewProp_MoveAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_LookAction = { "LookAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, LookAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAction_MetaData), NewProp_LookAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_JumpAction = { "JumpAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, JumpAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpAction_MetaData), NewProp_JumpAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_BasicAttackAction = { "BasicAttackAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, BasicAttackAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BasicAttackAction_MetaData), NewProp_BasicAttackAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_QAbilityAction = { "QAbilityAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, QAbilityAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QAbilityAction_MetaData), NewProp_QAbilityAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_WAbilityAction = { "WAbilityAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, WAbilityAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WAbilityAction_MetaData), NewProp_WAbilityAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_EAbilityAction = { "EAbilityAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, EAbilityAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EAbilityAction_MetaData), NewProp_EAbilityAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_RAbilityAction = { "RAbilityAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, RAbilityAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RAbilityAction_MetaData), NewProp_RAbilityAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_DashAction = { "DashAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, DashAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashAction_MetaData), NewProp_DashAction_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_RecallAction = { "RecallAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, RecallAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecallAction_MetaData), NewProp_RecallAction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_MouseSensitivity = { "MouseSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, MouseSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MouseSensitivity_MetaData), NewProp_MouseSensitivity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_GamepadSensitivity = { "GamepadSensitivity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionInputConfig, GamepadSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GamepadSensitivity_MetaData), NewProp_GamepadSensitivity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bInvertYAxis_SetBit(void* Obj)
{
	((FAuracronChampionInputConfig*)Obj)->bInvertYAxis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bInvertYAxis = { "bInvertYAxis", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionInputConfig), &Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bInvertYAxis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInvertYAxis_MetaData), NewProp_bInvertYAxis_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bSupportTouch_SetBit(void* Obj)
{
	((FAuracronChampionInputConfig*)Obj)->bSupportTouch = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bSupportTouch = { "bSupportTouch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionInputConfig), &Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bSupportTouch_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSupportTouch_MetaData), NewProp_bSupportTouch_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_InputMappingContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_MoveAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_LookAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_JumpAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_BasicAttackAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_QAbilityAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_WAbilityAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_EAbilityAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_RAbilityAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_DashAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_RecallAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_MouseSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_GamepadSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bInvertYAxis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewProp_bSupportTouch,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionInputConfig",
	Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::PropPointers),
	sizeof(FAuracronChampionInputConfig),
	alignof(FAuracronChampionInputConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionInputConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionInputConfig ****************************************

// ********** Begin ScriptStruct FAuracronChampionConfiguration ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration;
class UScriptStruct* FAuracronChampionConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura completa de configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura completa de configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionName_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionDescription_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionType_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionRarity_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raridade do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raridade do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseAttributes_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atributos base */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos base" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Abilities_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilidades */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilidades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualConfig_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o visual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementConfig_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputConfig_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionLevel_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ClampMax", "18" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentExperience_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceToNextLevel_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Experi\xc3\x83\xc2\xaancia para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Experi\xc3\x83\xc2\xaancia para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockCost_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custo para desbloquear (em moedas do jogo) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custo para desbloquear (em moedas do jogo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUnlocked_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Campe\xc3\x83\xc2\xa3o desbloqueado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Campe\xc3\x83\xc2\xa3o desbloqueado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAvailableForSelection_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dispon\xc3\x83\xc2\xadvel para sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dispon\xc3\x83\xc2\xadvel para sele\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSigiloCompatible_MetaData[] = {
		{ "Category", "Champion Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Compat\xc3\x83\xc2\xadvel com S\xc3\x83\xc2\xadgilos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Compat\xc3\x83\xc2\xadvel com S\xc3\x83\xc2\xadgilos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ChampionName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ChampionDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ChampionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ChampionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ChampionRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ChampionRarity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseAttributes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Abilities;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InputConfig;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ChampionLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentExperience;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceToNextLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnlockCost;
	static void NewProp_bUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUnlocked;
	static void NewProp_bAvailableForSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAvailableForSelection;
	static void NewProp_bSigiloCompatible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSigiloCompatible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionName = { "ChampionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionName_MetaData), NewProp_ChampionName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionDescription = { "ChampionDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionDescription_MetaData), NewProp_ChampionDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionType = { "ChampionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionType), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionType_MetaData), NewProp_ChampionType_MetaData) }; // 2803438190
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionRarity = { "ChampionRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionRarity), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionRarity_MetaData), NewProp_ChampionRarity_MetaData) }; // 3311160883
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_BaseAttributes = { "BaseAttributes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, BaseAttributes), Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseAttributes_MetaData), NewProp_BaseAttributes_MetaData) }; // 2148992779
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_Abilities = { "Abilities", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, Abilities), Z_Construct_UScriptStruct_FAuracronChampionAbilities, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Abilities_MetaData), NewProp_Abilities_MetaData) }; // 2538249185
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_VisualConfig = { "VisualConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, VisualConfig), Z_Construct_UScriptStruct_FAuracronChampionVisualConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualConfig_MetaData), NewProp_VisualConfig_MetaData) }; // 3663511635
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_MovementConfig = { "MovementConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, MovementConfig), Z_Construct_UScriptStruct_FAuracronChampionMovementConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementConfig_MetaData), NewProp_MovementConfig_MetaData) }; // 2847685713
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_InputConfig = { "InputConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, InputConfig), Z_Construct_UScriptStruct_FAuracronChampionInputConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputConfig_MetaData), NewProp_InputConfig_MetaData) }; // 3019990260
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionLevel = { "ChampionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ChampionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionLevel_MetaData), NewProp_ChampionLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_CurrentExperience = { "CurrentExperience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, CurrentExperience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentExperience_MetaData), NewProp_CurrentExperience_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ExperienceToNextLevel = { "ExperienceToNextLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, ExperienceToNextLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceToNextLevel_MetaData), NewProp_ExperienceToNextLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_UnlockCost = { "UnlockCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfiguration, UnlockCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockCost_MetaData), NewProp_UnlockCost_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bUnlocked_SetBit(void* Obj)
{
	((FAuracronChampionConfiguration*)Obj)->bUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bUnlocked = { "bUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionConfiguration), &Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUnlocked_MetaData), NewProp_bUnlocked_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bAvailableForSelection_SetBit(void* Obj)
{
	((FAuracronChampionConfiguration*)Obj)->bAvailableForSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bAvailableForSelection = { "bAvailableForSelection", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionConfiguration), &Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bAvailableForSelection_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAvailableForSelection_MetaData), NewProp_bAvailableForSelection_MetaData) };
void Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bSigiloCompatible_SetBit(void* Obj)
{
	((FAuracronChampionConfiguration*)Obj)->bSigiloCompatible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bSigiloCompatible = { "bSigiloCompatible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronChampionConfiguration), &Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bSigiloCompatible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSigiloCompatible_MetaData), NewProp_bSigiloCompatible_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_BaseAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_Abilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_VisualConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_MovementConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_InputConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ChampionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_CurrentExperience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_ExperienceToNextLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_UnlockCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bAvailableForSelection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewProp_bSigiloCompatible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionConfiguration",
	Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::PropPointers),
	sizeof(FAuracronChampionConfiguration),
	alignof(FAuracronChampionConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionConfiguration **************************************

// ********** Begin ScriptStruct FAuracronChampionConfigurationEntry *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry;
class UScriptStruct* FAuracronChampionConfigurationEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry, (UObject*)Z_Construct_UPackage__Script_AuracronChampionsBridge(), TEXT("AuracronChampionConfigurationEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de campe\xc3\xa3o (substitui TMap para replica\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para entrada de configura\xc3\xa7\xc3\xa3o de campe\xc3\xa3o (substitui TMap para replica\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "Category", "Champion Configuration" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronChampionConfigurationEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfigurationEntry, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronChampionConfigurationEntry, Configuration), Z_Construct_UScriptStruct_FAuracronChampionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4002447047
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
	nullptr,
	&NewStructOps,
	"AuracronChampionConfigurationEntry",
	Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::PropPointers),
	sizeof(FAuracronChampionConfigurationEntry),
	alignof(FAuracronChampionConfigurationEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.InnerSingleton, Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry.InnerSingleton;
}
// ********** End ScriptStruct FAuracronChampionConfigurationEntry *********************************

// ********** Begin Delegate FOnChampionSelected ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnChampionSelected_Parms
	{
		FString ChampionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Campe\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 selecionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Campe\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 selecionado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnChampionSelected_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::NewProp_ChampionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnChampionSelected__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnChampionSelected_DelegateWrapper(const FMulticastScriptDelegate& OnChampionSelected, const FString& ChampionID)
{
	struct AuracronChampionsBridge_eventOnChampionSelected_Parms
	{
		FString ChampionID;
	};
	AuracronChampionsBridge_eventOnChampionSelected_Parms Parms;
	Parms.ChampionID=ChampionID;
	OnChampionSelected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChampionSelected *****************************************************

// ********** Begin Delegate FOnChampionSpawned ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnChampionSpawned_Parms
	{
		FString ChampionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Campe\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 spawnado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Campe\xc3\x83\xc2\xa3o \xc3\x83\xc2\xa9 spawnado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnChampionSpawned_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::NewProp_ChampionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnChampionSpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnChampionSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnChampionSpawned, const FString& ChampionID)
{
	struct AuracronChampionsBridge_eventOnChampionSpawned_Parms
	{
		FString ChampionID;
	};
	AuracronChampionsBridge_eventOnChampionSpawned_Parms Parms;
	Parms.ChampionID=ChampionID;
	OnChampionSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChampionSpawned ******************************************************

// ********** Begin Delegate FOnChampionDied *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnChampionDied_Parms
	{
		FString ChampionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Campe\xc3\x83\xc2\xa3o morre */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Campe\xc3\x83\xc2\xa3o morre" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnChampionDied_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::NewProp_ChampionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnChampionDied__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionDied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionDied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnChampionDied_DelegateWrapper(const FMulticastScriptDelegate& OnChampionDied, const FString& ChampionID)
{
	struct AuracronChampionsBridge_eventOnChampionDied_Parms
	{
		FString ChampionID;
	};
	AuracronChampionsBridge_eventOnChampionDied_Parms Parms;
	Parms.ChampionID=ChampionID;
	OnChampionDied.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChampionDied *********************************************************

// ********** Begin Delegate FOnChampionLevelUp ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnChampionLevelUp_Parms
	{
		FString ChampionID;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando Campe\xc3\x83\xc2\xa3o sobe de n\xc3\x83\xc2\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando Campe\xc3\x83\xc2\xa3o sobe de n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnChampionLevelUp_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnChampionLevelUp_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnChampionLevelUp__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::AuracronChampionsBridge_eventOnChampionLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnChampionLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnChampionLevelUp, const FString& ChampionID, int32 NewLevel)
{
	struct AuracronChampionsBridge_eventOnChampionLevelUp_Parms
	{
		FString ChampionID;
		int32 NewLevel;
	};
	AuracronChampionsBridge_eventOnChampionLevelUp_Parms Parms;
	Parms.ChampionID=ChampionID;
	Parms.NewLevel=NewLevel;
	OnChampionLevelUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChampionLevelUp ******************************************************

// ********** Begin Delegate FOnAbilityActivated ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnAbilityActivated_Parms
	{
		FString ChampionID;
		FString AbilitySlot;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando habilidade \xc3\x83\xc2\xa9 ativada */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando habilidade \xc3\x83\xc2\xa9 ativada" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnAbilityActivated_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnAbilityActivated_Parms, AbilitySlot), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::NewProp_AbilitySlot,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnAbilityActivated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::AuracronChampionsBridge_eventOnAbilityActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::AuracronChampionsBridge_eventOnAbilityActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnAbilityActivated_DelegateWrapper(const FMulticastScriptDelegate& OnAbilityActivated, const FString& ChampionID, const FString& AbilitySlot)
{
	struct AuracronChampionsBridge_eventOnAbilityActivated_Parms
	{
		FString ChampionID;
		FString AbilitySlot;
	};
	AuracronChampionsBridge_eventOnAbilityActivated_Parms Parms;
	Parms.ChampionID=ChampionID;
	Parms.AbilitySlot=AbilitySlot;
	OnAbilityActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAbilityActivated *****************************************************

// ********** Begin Delegate FOnAttributesUpdated **************************************************
struct Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics
{
	struct AuracronChampionsBridge_eventOnAttributesUpdated_Parms
	{
		FString ChampionID;
		FAuracronChampionBaseAttributes NewAttributes;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando atributos s\xc3\x83\xc2\xa3o atualizados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando atributos s\xc3\x83\xc2\xa3o atualizados" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewAttributes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnAttributesUpdated_Parms, ChampionID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::NewProp_NewAttributes = { "NewAttributes", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventOnAttributesUpdated_Parms, NewAttributes), Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes, METADATA_PARAMS(0, nullptr) }; // 2148992779
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::NewProp_NewAttributes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnAttributesUpdated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::AuracronChampionsBridge_eventOnAttributesUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::AuracronChampionsBridge_eventOnAttributesUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronChampionsBridge::FOnAttributesUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnAttributesUpdated, const FString& ChampionID, FAuracronChampionBaseAttributes NewAttributes)
{
	struct AuracronChampionsBridge_eventOnAttributesUpdated_Parms
	{
		FString ChampionID;
		FAuracronChampionBaseAttributes NewAttributes;
	};
	AuracronChampionsBridge_eventOnAttributesUpdated_Parms Parms;
	Parms.ChampionID=ChampionID;
	Parms.NewAttributes=NewAttributes;
	OnAttributesUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAttributesUpdated ****************************************************

// ********** Begin Class UAuracronChampionsBridge Function ActivateAbility ************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics
{
	struct AuracronChampionsBridge_eventActivateAbility_Parms
	{
		FString AbilitySlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar habilidade por slot\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar habilidade por slot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventActivateAbility_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventActivateAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventActivateAbility_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ActivateAbility", Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::AuracronChampionsBridge_eventActivateAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::AuracronChampionsBridge_eventActivateAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execActivateAbility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateAbility(Z_Param_AbilitySlot);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ActivateAbility **************************

// ********** Begin Class UAuracronChampionsBridge Function ActivateQAbility ***********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar habilidade Q\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar habilidade Q" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ActivateQAbility", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execActivateQAbility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateQAbility();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ActivateQAbility *************************

// ********** Begin Class UAuracronChampionsBridge Function ApplyAlternativeSkin *******************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics
{
	struct AuracronChampionsBridge_eventApplyAlternativeSkin_Parms
	{
		int32 SkinIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|MetaHuman" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar skin alternativa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar skin alternativa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SkinIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_SkinIndex = { "SkinIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventApplyAlternativeSkin_Parms, SkinIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventApplyAlternativeSkin_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventApplyAlternativeSkin_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_SkinIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ApplyAlternativeSkin", Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::AuracronChampionsBridge_eventApplyAlternativeSkin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::AuracronChampionsBridge_eventApplyAlternativeSkin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execApplyAlternativeSkin)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SkinIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyAlternativeSkin(Z_Param_SkinIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ApplyAlternativeSkin *********************

// ********** Begin Class UAuracronChampionsBridge Function ApplyDamage ****************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics
{
	struct AuracronChampionsBridge_eventApplyDamage_Parms
	{
		float DamageAmount;
		bool bIsMagicDamage;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar dano ao Campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "CPP_Default_bIsMagicDamage", "false" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar dano ao Campe\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static void NewProp_bIsMagicDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMagicDamage;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventApplyDamage_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_bIsMagicDamage_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventApplyDamage_Parms*)Obj)->bIsMagicDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_bIsMagicDamage = { "bIsMagicDamage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventApplyDamage_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_bIsMagicDamage_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventApplyDamage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventApplyDamage_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_bIsMagicDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ApplyDamage", Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::AuracronChampionsBridge_eventApplyDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::AuracronChampionsBridge_eventApplyDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execApplyDamage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_UBOOL(Z_Param_bIsMagicDamage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyDamage(Z_Param_DamageAmount,Z_Param_bIsMagicDamage);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ApplyDamage ******************************

// ********** Begin Class UAuracronChampionsBridge Function ApplyMetaHumanConfiguration ************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics
{
	struct AuracronChampionsBridge_eventApplyMetaHumanConfiguration_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|MetaHuman" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o MetaHuman\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o MetaHuman" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventApplyMetaHumanConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventApplyMetaHumanConfiguration_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ApplyMetaHumanConfiguration", Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::AuracronChampionsBridge_eventApplyMetaHumanConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::AuracronChampionsBridge_eventApplyMetaHumanConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execApplyMetaHumanConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyMetaHumanConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ApplyMetaHumanConfiguration **************

// ********** Begin Class UAuracronChampionsBridge Function CalculateAttributesForLevel ************
struct Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics
{
	struct AuracronChampionsBridge_eventCalculateAttributesForLevel_Parms
	{
		int32 Level;
		FAuracronChampionBaseAttributes ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Calcular atributos baseados no n\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calcular atributos baseados no n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventCalculateAttributesForLevel_Parms, Level), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventCalculateAttributesForLevel_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes, METADATA_PARAMS(0, nullptr) }; // 2148992779
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "CalculateAttributesForLevel", Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::AuracronChampionsBridge_eventCalculateAttributesForLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::AuracronChampionsBridge_eventCalculateAttributesForLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execCalculateAttributesForLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronChampionBaseAttributes*)Z_Param__Result=P_THIS->CalculateAttributesForLevel(Z_Param_Level);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function CalculateAttributesForLevel **************

// ********** Begin Class UAuracronChampionsBridge Function CancelActiveAbility ********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics
{
	struct AuracronChampionsBridge_eventCancelActiveAbility_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Cancelar habilidade ativa\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancelar habilidade ativa" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventCancelActiveAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventCancelActiveAbility_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "CancelActiveAbility", Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::AuracronChampionsBridge_eventCancelActiveAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::AuracronChampionsBridge_eventCancelActiveAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execCancelActiveAbility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CancelActiveAbility();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function CancelActiveAbility **********************

// ********** Begin Class UAuracronChampionsBridge Function CustomizeFacialAppearance **************
struct Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics
{
	struct AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms
	{
		TMap<FString,float> FacialParameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|MetaHuman" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Customizar apar\xc3\x83\xc2\xaancia facial\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Customizar apar\xc3\x83\xc2\xaancia facial" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FacialParameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FacialParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FacialParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_FacialParameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters_ValueProp = { "FacialParameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters_Key_KeyProp = { "FacialParameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters = { "FacialParameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms, FacialParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FacialParameters_MetaData), NewProp_FacialParameters_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_FacialParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "CustomizeFacialAppearance", Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::AuracronChampionsBridge_eventCustomizeFacialAppearance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execCustomizeFacialAppearance)
{
	P_GET_TMAP_REF(FString,float,Z_Param_Out_FacialParameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CustomizeFacialAppearance(Z_Param_Out_FacialParameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function CustomizeFacialAppearance ****************

// ********** Begin Class UAuracronChampionsBridge Function DespawnChampion ************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics
{
	struct AuracronChampionsBridge_eventDespawnChampion_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Despawnar Campe\xc3\x83\xc2\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Despawnar Campe\xc3\x83\xc2\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventDespawnChampion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventDespawnChampion_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "DespawnChampion", Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::AuracronChampionsBridge_eventDespawnChampion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::AuracronChampionsBridge_eventDespawnChampion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execDespawnChampion)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DespawnChampion();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function DespawnChampion **************************

// ********** Begin Class UAuracronChampionsBridge Function GainExperience *************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics
{
	struct AuracronChampionsBridge_eventGainExperience_Parms
	{
		int32 ExperienceAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ganhar experi\xc3\x83\xc2\xaancia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ganhar experi\xc3\x83\xc2\xaancia" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ExperienceAmount = { "ExperienceAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGainExperience_Parms, ExperienceAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventGainExperience_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventGainExperience_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ExperienceAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GainExperience", Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::AuracronChampionsBridge_eventGainExperience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::AuracronChampionsBridge_eventGainExperience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGainExperience)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ExperienceAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GainExperience(Z_Param_ExperienceAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GainExperience ***************************

// ********** Begin Class UAuracronChampionsBridge Function GetAbilityCooldown *********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics
{
	struct AuracronChampionsBridge_eventGetAbilityCooldown_Parms
	{
		FString AbilitySlot;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter cooldown de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter cooldown de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetAbilityCooldown_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetAbilityCooldown_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetAbilityCooldown", Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::AuracronChampionsBridge_eventGetAbilityCooldown_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::AuracronChampionsBridge_eventGetAbilityCooldown_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetAbilityCooldown)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAbilityCooldown(Z_Param_AbilitySlot);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetAbilityCooldown ***********************

// ********** Begin Class UAuracronChampionsBridge Function GetAbilityLevel ************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics
{
	struct AuracronChampionsBridge_eventGetAbilityLevel_Parms
	{
		FString AbilitySlot;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter n\xc3\x83\xc2\xadvel de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter n\xc3\x83\xc2\xadvel de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetAbilityLevel_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetAbilityLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetAbilityLevel", Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::AuracronChampionsBridge_eventGetAbilityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::AuracronChampionsBridge_eventGetAbilityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetAbilityLevel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAbilityLevel(Z_Param_AbilitySlot);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetAbilityLevel **************************

// ********** Begin Class UAuracronChampionsBridge Function GetAvailableChampions ******************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics
{
	struct AuracronChampionsBridge_eventGetAvailableChampions_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter lista de todos os Campe\xc3\x83\xc2\xb5""es dispon\xc3\x83\xc2\xadveis\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter lista de todos os Campe\xc3\x83\xc2\xb5""es dispon\xc3\x83\xc2\xadveis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetAvailableChampions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetAvailableChampions", Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::AuracronChampionsBridge_eventGetAvailableChampions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::AuracronChampionsBridge_eventGetAvailableChampions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetAvailableChampions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAvailableChampions();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetAvailableChampions ********************

// ********** Begin Class UAuracronChampionsBridge Function GetChampionConfiguration ***************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics
{
	struct AuracronChampionsBridge_eventGetChampionConfiguration_Parms
	{
		FString ChampionID;
		FAuracronChampionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o espec\xc3\x83\xc2\xad""fico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o espec\xc3\x83\xc2\xad""fico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetChampionConfiguration_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetChampionConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronChampionConfiguration, METADATA_PARAMS(0, nullptr) }; // 4002447047
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetChampionConfiguration", Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::AuracronChampionsBridge_eventGetChampionConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::AuracronChampionsBridge_eventGetChampionConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetChampionConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronChampionConfiguration*)Z_Param__Result=P_THIS->GetChampionConfiguration(Z_Param_ChampionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetChampionConfiguration *****************

// ********** Begin Class UAuracronChampionsBridge Function GetChampionState ***********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics
{
	struct AuracronChampionsBridge_eventGetChampionState_Parms
	{
		EAuracronChampionState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter estado atual do Campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter estado atual do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetChampionState_Parms, ReturnValue), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState, METADATA_PARAMS(0, nullptr) }; // 755786022
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetChampionState", Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::AuracronChampionsBridge_eventGetChampionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::AuracronChampionsBridge_eventGetChampionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetChampionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronChampionState*)Z_Param__Result=P_THIS->GetChampionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetChampionState *************************

// ********** Begin Class UAuracronChampionsBridge Function GetCurrentChampionConfiguration ********
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics
{
	struct AuracronChampionsBridge_eventGetCurrentChampionConfiguration_Parms
	{
		FAuracronChampionConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Campe\xc3\x83\xc2\xa3o atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do Campe\xc3\x83\xc2\xa3o atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetCurrentChampionConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronChampionConfiguration, METADATA_PARAMS(0, nullptr) }; // 4002447047
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetCurrentChampionConfiguration", Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::AuracronChampionsBridge_eventGetCurrentChampionConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::AuracronChampionsBridge_eventGetCurrentChampionConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetCurrentChampionConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronChampionConfiguration*)Z_Param__Result=P_THIS->GetCurrentChampionConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetCurrentChampionConfiguration **********

// ********** Begin Class UAuracronChampionsBridge Function GetExperienceToNextLevel ***************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics
{
	struct AuracronChampionsBridge_eventGetExperienceToNextLevel_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter experi\xc3\x83\xc2\xaancia necess\xc3\x83\xc2\xa1ria para pr\xc3\x83\xc2\xb3ximo n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetExperienceToNextLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetExperienceToNextLevel", Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::AuracronChampionsBridge_eventGetExperienceToNextLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::AuracronChampionsBridge_eventGetExperienceToNextLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetExperienceToNextLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetExperienceToNextLevel();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetExperienceToNextLevel *****************

// ********** Begin Class UAuracronChampionsBridge Function GetHealthPercentage ********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics
{
	struct AuracronChampionsBridge_eventGetHealthPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter HP atual em percentual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter HP atual em percentual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetHealthPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetHealthPercentage", Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::AuracronChampionsBridge_eventGetHealthPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::AuracronChampionsBridge_eventGetHealthPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetHealthPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealthPercentage();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetHealthPercentage **********************

// ********** Begin Class UAuracronChampionsBridge Function GetManaPercentage **********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics
{
	struct AuracronChampionsBridge_eventGetManaPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter Mana atual em percentual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter Mana atual em percentual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetManaPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetManaPercentage", Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::AuracronChampionsBridge_eventGetManaPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::AuracronChampionsBridge_eventGetManaPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetManaPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetManaPercentage();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetManaPercentage ************************

// ********** Begin Class UAuracronChampionsBridge Function GetSelectedChampionID ******************
struct Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics
{
	struct AuracronChampionsBridge_eventGetSelectedChampionID_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter Campe\xc3\x83\xc2\xa3o atualmente selecionado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter Campe\xc3\x83\xc2\xa3o atualmente selecionado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventGetSelectedChampionID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "GetSelectedChampionID", Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::AuracronChampionsBridge_eventGetSelectedChampionID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::AuracronChampionsBridge_eventGetSelectedChampionID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execGetSelectedChampionID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSelectedChampionID();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function GetSelectedChampionID ********************

// ********** Begin Class UAuracronChampionsBridge Function HealChampion ***************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics
{
	struct AuracronChampionsBridge_eventHealChampion_Parms
	{
		float HealAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Curar Campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Curar Campe\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_HealAmount = { "HealAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventHealChampion_Parms, HealAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventHealChampion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventHealChampion_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_HealAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "HealChampion", Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::AuracronChampionsBridge_eventHealChampion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::AuracronChampionsBridge_eventHealChampion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execHealChampion)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HealChampion(Z_Param_HealAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function HealChampion *****************************

// ********** Begin Class UAuracronChampionsBridge Function IsAbilityAvailable *********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics
{
	struct AuracronChampionsBridge_eventIsAbilityAvailable_Parms
	{
		FString AbilitySlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se habilidade est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se habilidade est\xc3\x83\xc2\xa1 dispon\xc3\x83\xc2\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventIsAbilityAvailable_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventIsAbilityAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventIsAbilityAvailable_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "IsAbilityAvailable", Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::AuracronChampionsBridge_eventIsAbilityAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::AuracronChampionsBridge_eventIsAbilityAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execIsAbilityAvailable)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAbilityAvailable(Z_Param_AbilitySlot);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function IsAbilityAvailable ***********************

// ********** Begin Class UAuracronChampionsBridge Function IsChampionAlive ************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics
{
	struct AuracronChampionsBridge_eventIsChampionAlive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verificar se Campe\xc3\x83\xc2\xa3o est\xc3\x83\xc2\xa1 vivo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se Campe\xc3\x83\xc2\xa3o est\xc3\x83\xc2\xa1 vivo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventIsChampionAlive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventIsChampionAlive_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "IsChampionAlive", Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::AuracronChampionsBridge_eventIsChampionAlive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::AuracronChampionsBridge_eventIsChampionAlive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execIsChampionAlive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsChampionAlive();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function IsChampionAlive **************************

// ********** Begin Class UAuracronChampionsBridge Function LevelUp ********************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics
{
	struct AuracronChampionsBridge_eventLevelUp_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Subir de n\xc3\x83\xc2\xadvel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subir de n\xc3\x83\xc2\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventLevelUp_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventLevelUp_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "LevelUp", Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::AuracronChampionsBridge_eventLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::AuracronChampionsBridge_eventLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execLevelUp)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LevelUp();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function LevelUp **********************************

// ********** Begin Class UAuracronChampionsBridge Function LevelUpAbility *************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics
{
	struct AuracronChampionsBridge_eventLevelUpAbility_Parms
	{
		FString AbilitySlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Upar habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upar habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventLevelUpAbility_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventLevelUpAbility_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventLevelUpAbility_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "LevelUpAbility", Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::AuracronChampionsBridge_eventLevelUpAbility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::AuracronChampionsBridge_eventLevelUpAbility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execLevelUpAbility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LevelUpAbility(Z_Param_AbilitySlot);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function LevelUpAbility ***************************

// ********** Begin Class UAuracronChampionsBridge Function LoadDefaultChampionConfigurations ******
struct Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics
{
	struct AuracronChampionsBridge_eventLoadDefaultChampionConfigurations_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos Campe\xc3\x83\xc2\xb5""es\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Carregar configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es padr\xc3\x83\xc2\xa3o dos Campe\xc3\x83\xc2\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventLoadDefaultChampionConfigurations_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventLoadDefaultChampionConfigurations_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "LoadDefaultChampionConfigurations", Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::AuracronChampionsBridge_eventLoadDefaultChampionConfigurations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::AuracronChampionsBridge_eventLoadDefaultChampionConfigurations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execLoadDefaultChampionConfigurations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadDefaultChampionConfigurations();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function LoadDefaultChampionConfigurations ********

// ********** Begin Class UAuracronChampionsBridge Function ModifyAttribute ************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics
{
	struct AuracronChampionsBridge_eventModifyAttribute_Parms
	{
		FString AttributeName;
		float ModifierValue;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Modificar atributo temporariamente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modificar atributo temporariamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttributeName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ModifierValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_AttributeName = { "AttributeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventModifyAttribute_Parms, AttributeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeName_MetaData), NewProp_AttributeName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ModifierValue = { "ModifierValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventModifyAttribute_Parms, ModifierValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventModifyAttribute_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventModifyAttribute_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventModifyAttribute_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_AttributeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ModifierValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ModifyAttribute", Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::AuracronChampionsBridge_eventModifyAttribute_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::AuracronChampionsBridge_eventModifyAttribute_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execModifyAttribute)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AttributeName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ModifierValue);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ModifyAttribute(Z_Param_AttributeName,Z_Param_ModifierValue,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ModifyAttribute **************************

// ********** Begin Class UAuracronChampionsBridge Function OnRep_ChampionState ********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnRep_ChampionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execOnRep_ChampionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_ChampionState();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function OnRep_ChampionState **********************

// ********** Begin Class UAuracronChampionsBridge Function OnRep_CurrentAttributes ****************
struct Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnRep_CurrentAttributes", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execOnRep_CurrentAttributes)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_CurrentAttributes();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function OnRep_CurrentAttributes ******************

// ********** Begin Class UAuracronChampionsBridge Function OnRep_SelectedChampionID ***************
struct Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "OnRep_SelectedChampionID", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execOnRep_SelectedChampionID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_SelectedChampionID();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function OnRep_SelectedChampionID *****************

// ********** Begin Class UAuracronChampionsBridge Function ProcessLookInput ***********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics
{
	struct AuracronChampionsBridge_eventProcessLookInput_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar input de c\xc3\xa2mera\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar input de c\xc3\xa2mera" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventProcessLookInput_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ProcessLookInput", Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::AuracronChampionsBridge_eventProcessLookInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::AuracronChampionsBridge_eventProcessLookInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execProcessLookInput)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessLookInput(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ProcessLookInput *************************

// ********** Begin Class UAuracronChampionsBridge Function ProcessMovementInput *******************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics
{
	struct AuracronChampionsBridge_eventProcessMovementInput_Parms
	{
		FInputActionValue Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Processar input de movimento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Processar input de movimento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventProcessMovementInput_Parms, Value), Z_Construct_UScriptStruct_FInputActionValue, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) }; // 203218767
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ProcessMovementInput", Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::AuracronChampionsBridge_eventProcessMovementInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::AuracronChampionsBridge_eventProcessMovementInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execProcessMovementInput)
{
	P_GET_STRUCT_REF(FInputActionValue,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ProcessMovementInput(Z_Param_Out_Value);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ProcessMovementInput *********************

// ********** Begin Class UAuracronChampionsBridge Function ResetToDefaultAppearance ***************
struct Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics
{
	struct AuracronChampionsBridge_eventResetToDefaultAppearance_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|MetaHuman" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Resetar para apar\xc3\x83\xc2\xaancia padr\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resetar para apar\xc3\x83\xc2\xaancia padr\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventResetToDefaultAppearance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventResetToDefaultAppearance_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "ResetToDefaultAppearance", Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::AuracronChampionsBridge_eventResetToDefaultAppearance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::AuracronChampionsBridge_eventResetToDefaultAppearance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execResetToDefaultAppearance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResetToDefaultAppearance();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function ResetToDefaultAppearance *****************

// ********** Begin Class UAuracronChampionsBridge Function RestoreMana ****************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics
{
	struct AuracronChampionsBridge_eventRestoreMana_Parms
	{
		float ManaAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Attributes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Restaurar mana\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Restaurar mana" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ManaAmount = { "ManaAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventRestoreMana_Parms, ManaAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventRestoreMana_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventRestoreMana_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ManaAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "RestoreMana", Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::AuracronChampionsBridge_eventRestoreMana_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::AuracronChampionsBridge_eventRestoreMana_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execRestoreMana)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ManaAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RestoreMana(Z_Param_ManaAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function RestoreMana ******************************

// ********** Begin Class UAuracronChampionsBridge Function SelectChampion *************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics
{
	struct AuracronChampionsBridge_eventSelectChampion_Parms
	{
		FString ChampionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Selecionar Campe\xc3\x83\xc2\xa3o para a partida\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selecionar Campe\xc3\x83\xc2\xa3o para a partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventSelectChampion_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventSelectChampion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventSelectChampion_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "SelectChampion", Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::AuracronChampionsBridge_eventSelectChampion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::AuracronChampionsBridge_eventSelectChampion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execSelectChampion)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SelectChampion(Z_Param_ChampionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function SelectChampion ***************************

// ********** Begin Class UAuracronChampionsBridge Function SetChampionConfiguration ***************
struct Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics
{
	struct AuracronChampionsBridge_eventSetChampionConfiguration_Parms
	{
		FString ChampionID;
		FAuracronChampionConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de um Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventSetChampionConfiguration_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventSetChampionConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronChampionConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 4002447047
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "SetChampionConfiguration", Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::AuracronChampionsBridge_eventSetChampionConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::AuracronChampionsBridge_eventSetChampionConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execSetChampionConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_STRUCT_REF(FAuracronChampionConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetChampionConfiguration(Z_Param_ChampionID,Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function SetChampionConfiguration *****************

// ********** Begin Class UAuracronChampionsBridge Function SetupChampionInput *********************
struct Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics
{
	struct AuracronChampionsBridge_eventSetupChampionInput_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Configurar input do Campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar input do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventSetupChampionInput_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventSetupChampionInput_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "SetupChampionInput", Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::AuracronChampionsBridge_eventSetupChampionInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::AuracronChampionsBridge_eventSetupChampionInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execSetupChampionInput)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetupChampionInput();
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function SetupChampionInput ***********************

// ********** Begin Class UAuracronChampionsBridge Function SpawnChampion **************************
struct Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics
{
	struct AuracronChampionsBridge_eventSpawnChampion_Parms
	{
		FVector SpawnLocation;
		FRotator SpawnRotation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Champions|Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar Campe\xc3\x83\xc2\xa3o no mundo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar Campe\xc3\x83\xc2\xa3o no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnRotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnRotation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_SpawnLocation = { "SpawnLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventSpawnChampion_Parms, SpawnLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnLocation_MetaData), NewProp_SpawnLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_SpawnRotation = { "SpawnRotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronChampionsBridge_eventSpawnChampion_Parms, SpawnRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnRotation_MetaData), NewProp_SpawnRotation_MetaData) };
void Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronChampionsBridge_eventSpawnChampion_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronChampionsBridge_eventSpawnChampion_Parms), &Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_SpawnLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_SpawnRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronChampionsBridge, nullptr, "SpawnChampion", Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::AuracronChampionsBridge_eventSpawnChampion_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::AuracronChampionsBridge_eventSpawnChampion_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronChampionsBridge::execSpawnChampion)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_SpawnLocation);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_SpawnRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpawnChampion(Z_Param_Out_SpawnLocation,Z_Param_Out_SpawnRotation);
	P_NATIVE_END;
}
// ********** End Class UAuracronChampionsBridge Function SpawnChampion ****************************

// ********** Begin Class UAuracronChampionsBridge *************************************************
void UAuracronChampionsBridge::StaticRegisterNativesUAuracronChampionsBridge()
{
	UClass* Class = UAuracronChampionsBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateAbility", &UAuracronChampionsBridge::execActivateAbility },
		{ "ActivateQAbility", &UAuracronChampionsBridge::execActivateQAbility },
		{ "ApplyAlternativeSkin", &UAuracronChampionsBridge::execApplyAlternativeSkin },
		{ "ApplyDamage", &UAuracronChampionsBridge::execApplyDamage },
		{ "ApplyMetaHumanConfiguration", &UAuracronChampionsBridge::execApplyMetaHumanConfiguration },
		{ "CalculateAttributesForLevel", &UAuracronChampionsBridge::execCalculateAttributesForLevel },
		{ "CancelActiveAbility", &UAuracronChampionsBridge::execCancelActiveAbility },
		{ "CustomizeFacialAppearance", &UAuracronChampionsBridge::execCustomizeFacialAppearance },
		{ "DespawnChampion", &UAuracronChampionsBridge::execDespawnChampion },
		{ "GainExperience", &UAuracronChampionsBridge::execGainExperience },
		{ "GetAbilityCooldown", &UAuracronChampionsBridge::execGetAbilityCooldown },
		{ "GetAbilityLevel", &UAuracronChampionsBridge::execGetAbilityLevel },
		{ "GetAvailableChampions", &UAuracronChampionsBridge::execGetAvailableChampions },
		{ "GetChampionConfiguration", &UAuracronChampionsBridge::execGetChampionConfiguration },
		{ "GetChampionState", &UAuracronChampionsBridge::execGetChampionState },
		{ "GetCurrentChampionConfiguration", &UAuracronChampionsBridge::execGetCurrentChampionConfiguration },
		{ "GetExperienceToNextLevel", &UAuracronChampionsBridge::execGetExperienceToNextLevel },
		{ "GetHealthPercentage", &UAuracronChampionsBridge::execGetHealthPercentage },
		{ "GetManaPercentage", &UAuracronChampionsBridge::execGetManaPercentage },
		{ "GetSelectedChampionID", &UAuracronChampionsBridge::execGetSelectedChampionID },
		{ "HealChampion", &UAuracronChampionsBridge::execHealChampion },
		{ "IsAbilityAvailable", &UAuracronChampionsBridge::execIsAbilityAvailable },
		{ "IsChampionAlive", &UAuracronChampionsBridge::execIsChampionAlive },
		{ "LevelUp", &UAuracronChampionsBridge::execLevelUp },
		{ "LevelUpAbility", &UAuracronChampionsBridge::execLevelUpAbility },
		{ "LoadDefaultChampionConfigurations", &UAuracronChampionsBridge::execLoadDefaultChampionConfigurations },
		{ "ModifyAttribute", &UAuracronChampionsBridge::execModifyAttribute },
		{ "OnRep_ChampionState", &UAuracronChampionsBridge::execOnRep_ChampionState },
		{ "OnRep_CurrentAttributes", &UAuracronChampionsBridge::execOnRep_CurrentAttributes },
		{ "OnRep_SelectedChampionID", &UAuracronChampionsBridge::execOnRep_SelectedChampionID },
		{ "ProcessLookInput", &UAuracronChampionsBridge::execProcessLookInput },
		{ "ProcessMovementInput", &UAuracronChampionsBridge::execProcessMovementInput },
		{ "ResetToDefaultAppearance", &UAuracronChampionsBridge::execResetToDefaultAppearance },
		{ "RestoreMana", &UAuracronChampionsBridge::execRestoreMana },
		{ "SelectChampion", &UAuracronChampionsBridge::execSelectChampion },
		{ "SetChampionConfiguration", &UAuracronChampionsBridge::execSetChampionConfiguration },
		{ "SetupChampionInput", &UAuracronChampionsBridge::execSetupChampionInput },
		{ "SpawnChampion", &UAuracronChampionsBridge::execSpawnChampion },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronChampionsBridge;
UClass* UAuracronChampionsBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronChampionsBridge;
	if (!Z_Registration_Info_UClass_UAuracronChampionsBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronChampionsBridge"),
			Z_Registration_Info_UClass_UAuracronChampionsBridge.InnerSingleton,
			StaticRegisterNativesUAuracronChampionsBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronChampionsBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronChampionsBridge_NoRegister()
{
	return UAuracronChampionsBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronChampionsBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Champions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Campe\xc3\x83\xc2\xb5""es\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de campe\xc3\x83\xc2\xb5""es com integra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o MetaHuman\n */" },
#endif
		{ "DisplayName", "AURACRON Champions Bridge" },
		{ "IncludePath", "AuracronChampionsBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Campe\xc3\x83\xc2\xb5""es\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de campe\xc3\x83\xc2\xb5""es com integra\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o MetaHuman" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionConfigurations_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos Campe\xc3\x83\xc2\xb5""es dispon\xc3\x83\xc2\xadveis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xb5""es dos Campe\xc3\x83\xc2\xb5""es dispon\xc3\x83\xc2\xadveis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedChampionID_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do Campe\xc3\x83\xc2\xa3o atualmente selecionado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do Campe\xc3\x83\xc2\xa3o atualmente selecionado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentChampionState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAttributes_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atributos atuais do Campe\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atributos atuais do Campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao AbilitySystemComponent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnhancedInputComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Enhanced Input Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Enhanced Input Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Character Movement Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Character Movement Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionMesh_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\x83\xc2\xaancia ao Skeletal Mesh Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\x83\xc2\xaancia ao Skeletal Mesh Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChampionSelected_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChampionSpawned_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChampionDied_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChampionLevelUp_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAbilityActivated_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAttributesUpdated_MetaData[] = {
		{ "Category", "AURACRON Champions|Events" },
		{ "ModuleRelativePath", "Public/AuracronChampionsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChampionConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ChampionConfigurations;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SelectedChampionID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentChampionState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentChampionState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentAttributes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnhancedInputComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MovementComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChampionMesh;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChampionSelected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChampionSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChampionDied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChampionLevelUp;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAbilityActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAttributesUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ActivateAbility, "ActivateAbility" }, // 2122394850
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ActivateQAbility, "ActivateQAbility" }, // 822467821
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyAlternativeSkin, "ApplyAlternativeSkin" }, // 3726339343
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyDamage, "ApplyDamage" }, // 3313014261
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ApplyMetaHumanConfiguration, "ApplyMetaHumanConfiguration" }, // 1894260390
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_CalculateAttributesForLevel, "CalculateAttributesForLevel" }, // 2600894641
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_CancelActiveAbility, "CancelActiveAbility" }, // 3585533091
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_CustomizeFacialAppearance, "CustomizeFacialAppearance" }, // 2982932228
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_DespawnChampion, "DespawnChampion" }, // 3411086444
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GainExperience, "GainExperience" }, // 3632998442
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityCooldown, "GetAbilityCooldown" }, // 3636427611
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetAbilityLevel, "GetAbilityLevel" }, // 3318425357
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetAvailableChampions, "GetAvailableChampions" }, // 1677359913
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionConfiguration, "GetChampionConfiguration" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetChampionState, "GetChampionState" }, // 107629417
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetCurrentChampionConfiguration, "GetCurrentChampionConfiguration" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetExperienceToNextLevel, "GetExperienceToNextLevel" }, // 311511510
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetHealthPercentage, "GetHealthPercentage" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetManaPercentage, "GetManaPercentage" }, // 534106826
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_GetSelectedChampionID, "GetSelectedChampionID" }, // 259526607
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_HealChampion, "HealChampion" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_IsAbilityAvailable, "IsAbilityAvailable" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_IsChampionAlive, "IsChampionAlive" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_LevelUp, "LevelUp" }, // 968723023
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_LevelUpAbility, "LevelUpAbility" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_LoadDefaultChampionConfigurations, "LoadDefaultChampionConfigurations" }, // **********
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ModifyAttribute, "ModifyAttribute" }, // 1114297993
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature, "OnAbilityActivated__DelegateSignature" }, // 1789004718
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature, "OnAttributesUpdated__DelegateSignature" }, // 924155476
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature, "OnChampionDied__DelegateSignature" }, // 4143641574
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature, "OnChampionLevelUp__DelegateSignature" }, // 2726906731
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature, "OnChampionSelected__DelegateSignature" }, // 4230666546
		{ &Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature, "OnChampionSpawned__DelegateSignature" }, // 1886963461
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_ChampionState, "OnRep_ChampionState" }, // 95598146
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_CurrentAttributes, "OnRep_CurrentAttributes" }, // 1211902007
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_OnRep_SelectedChampionID, "OnRep_SelectedChampionID" }, // 759961696
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ProcessLookInput, "ProcessLookInput" }, // 2261802555
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ProcessMovementInput, "ProcessMovementInput" }, // 3520082342
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_ResetToDefaultAppearance, "ResetToDefaultAppearance" }, // 1323124499
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_RestoreMana, "RestoreMana" }, // 2763912810
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_SelectChampion, "SelectChampion" }, // 324478877
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_SetChampionConfiguration, "SetChampionConfiguration" }, // 3710199883
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_SetupChampionInput, "SetupChampionInput" }, // 4229721700
		{ &Z_Construct_UFunction_UAuracronChampionsBridge_SpawnChampion, "SpawnChampion" }, // 3398360263
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronChampionsBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionConfigurations_Inner = { "ChampionConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry, METADATA_PARAMS(0, nullptr) }; // 1667978875
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionConfigurations = { "ChampionConfigurations", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, ChampionConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionConfigurations_MetaData), NewProp_ChampionConfigurations_MetaData) }; // 1667978875
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_SelectedChampionID = { "SelectedChampionID", "OnRep_SelectedChampionID", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, SelectedChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedChampionID_MetaData), NewProp_SelectedChampionID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentChampionState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentChampionState = { "CurrentChampionState", "OnRep_ChampionState", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, CurrentChampionState), Z_Construct_UEnum_AuracronChampionsBridge_EAuracronChampionState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentChampionState_MetaData), NewProp_CurrentChampionState_MetaData) }; // 755786022
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentAttributes = { "CurrentAttributes", "OnRep_CurrentAttributes", (EPropertyFlags)0x0010000100020035, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, CurrentAttributes), Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAttributes_MetaData), NewProp_CurrentAttributes_MetaData) }; // 2148992779
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_EnhancedInputComponent = { "EnhancedInputComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, EnhancedInputComponent), Z_Construct_UClass_UEnhancedInputComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnhancedInputComponent_MetaData), NewProp_EnhancedInputComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_MovementComponent = { "MovementComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, MovementComponent), Z_Construct_UClass_UCharacterMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementComponent_MetaData), NewProp_MovementComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionMesh = { "ChampionMesh", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, ChampionMesh), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionMesh_MetaData), NewProp_ChampionMesh_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionSelected = { "OnChampionSelected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnChampionSelected), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChampionSelected_MetaData), NewProp_OnChampionSelected_MetaData) }; // 4230666546
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionSpawned = { "OnChampionSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnChampionSpawned), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChampionSpawned_MetaData), NewProp_OnChampionSpawned_MetaData) }; // 1886963461
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionDied = { "OnChampionDied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnChampionDied), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChampionDied_MetaData), NewProp_OnChampionDied_MetaData) }; // 4143641574
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionLevelUp = { "OnChampionLevelUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnChampionLevelUp), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChampionLevelUp_MetaData), NewProp_OnChampionLevelUp_MetaData) }; // 2726906731
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnAbilityActivated = { "OnAbilityActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnAbilityActivated), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAbilityActivated_MetaData), NewProp_OnAbilityActivated_MetaData) }; // 1789004718
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnAttributesUpdated = { "OnAttributesUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronChampionsBridge, OnAttributesUpdated), Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAttributesUpdated_MetaData), NewProp_OnAttributesUpdated_MetaData) }; // 924155476
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronChampionsBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_SelectedChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentChampionState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentChampionState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_CurrentAttributes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_EnhancedInputComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_MovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_ChampionMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionSelected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionDied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnChampionLevelUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnAbilityActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronChampionsBridge_Statics::NewProp_OnAttributesUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronChampionsBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronChampionsBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronChampionsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronChampionsBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronChampionsBridge_Statics::ClassParams = {
	&UAuracronChampionsBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronChampionsBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronChampionsBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronChampionsBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronChampionsBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronChampionsBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronChampionsBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronChampionsBridge.OuterSingleton, Z_Construct_UClass_UAuracronChampionsBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronChampionsBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronChampionsBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ChampionConfigurations(TEXT("ChampionConfigurations"));
	static FName Name_SelectedChampionID(TEXT("SelectedChampionID"));
	static FName Name_CurrentChampionState(TEXT("CurrentChampionState"));
	static FName Name_CurrentAttributes(TEXT("CurrentAttributes"));
	const bool bIsValid = true
		&& Name_ChampionConfigurations == ClassReps[(int32)ENetFields_Private::ChampionConfigurations].Property->GetFName()
		&& Name_SelectedChampionID == ClassReps[(int32)ENetFields_Private::SelectedChampionID].Property->GetFName()
		&& Name_CurrentChampionState == ClassReps[(int32)ENetFields_Private::CurrentChampionState].Property->GetFName()
		&& Name_CurrentAttributes == ClassReps[(int32)ENetFields_Private::CurrentAttributes].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronChampionsBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronChampionsBridge);
UAuracronChampionsBridge::~UAuracronChampionsBridge() {}
// ********** End Class UAuracronChampionsBridge ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronChampionType_StaticEnum, TEXT("EAuracronChampionType"), &Z_Registration_Info_UEnum_EAuracronChampionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2803438190U) },
		{ EAuracronChampionRarity_StaticEnum, TEXT("EAuracronChampionRarity"), &Z_Registration_Info_UEnum_EAuracronChampionRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3311160883U) },
		{ EAuracronChampionState_StaticEnum, TEXT("EAuracronChampionState"), &Z_Registration_Info_UEnum_EAuracronChampionState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 755786022U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronChampionBaseAttributes::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionBaseAttributes_Statics::NewStructOps, TEXT("AuracronChampionBaseAttributes"), &Z_Registration_Info_UScriptStruct_FAuracronChampionBaseAttributes, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionBaseAttributes), 2148992779U) },
		{ FAuracronChampionAbilities::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionAbilities_Statics::NewStructOps, TEXT("AuracronChampionAbilities"), &Z_Registration_Info_UScriptStruct_FAuracronChampionAbilities, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionAbilities), 2538249185U) },
		{ FAuracronChampionVisualConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionVisualConfig_Statics::NewStructOps, TEXT("AuracronChampionVisualConfig"), &Z_Registration_Info_UScriptStruct_FAuracronChampionVisualConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionVisualConfig), 3663511635U) },
		{ FAuracronChampionMovementConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionMovementConfig_Statics::NewStructOps, TEXT("AuracronChampionMovementConfig"), &Z_Registration_Info_UScriptStruct_FAuracronChampionMovementConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionMovementConfig), 2847685713U) },
		{ FAuracronChampionInputConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionInputConfig_Statics::NewStructOps, TEXT("AuracronChampionInputConfig"), &Z_Registration_Info_UScriptStruct_FAuracronChampionInputConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionInputConfig), 3019990260U) },
		{ FAuracronChampionConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionConfiguration_Statics::NewStructOps, TEXT("AuracronChampionConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronChampionConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionConfiguration), 4002447047U) },
		{ FAuracronChampionConfigurationEntry::StaticStruct, Z_Construct_UScriptStruct_FAuracronChampionConfigurationEntry_Statics::NewStructOps, TEXT("AuracronChampionConfigurationEntry"), &Z_Registration_Info_UScriptStruct_FAuracronChampionConfigurationEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronChampionConfigurationEntry), 1667978875U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronChampionsBridge, UAuracronChampionsBridge::StaticClass, TEXT("UAuracronChampionsBridge"), &Z_Registration_Info_UClass_UAuracronChampionsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronChampionsBridge), 2036040928U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_1294599253(TEXT("/Script/AuracronChampionsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronChampionsBridge_Public_AuracronChampionsBridge_h__Script_AuracronChampionsBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
