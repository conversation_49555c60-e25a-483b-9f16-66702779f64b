// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartition.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartition_generated_h
#error "AuracronWorldPartition.generated.h already included, missing '#pragma once' in AuracronWorldPartition.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartition_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronWorldPartitionLogger;
class UAuracronWorldPartitionManager;
class UDataLayerSubsystem;
class UWorld;
class UWorldPartition;
class UWorldPartitionSubsystem;
enum class EAuracronStreamingState : uint8;
enum class EAuracronWorldPartitionLogLevel : uint8;
enum class EAuracronWorldPartitionState : uint8;
struct FAuracronCellInfo;
struct FAuracronStreamingStatistics;
struct FAuracronWorldPartitionConfiguration;

// ********** Begin ScriptStruct FAuracronWorldPartitionConfiguration ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_107_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWorldPartitionConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWorldPartitionConfiguration;
// ********** End ScriptStruct FAuracronWorldPartitionConfiguration ********************************

// ********** Begin ScriptStruct FAuracronCellInfo *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_185_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCellInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCellInfo;
// ********** End ScriptStruct FAuracronCellInfo ***************************************************

// ********** Begin ScriptStruct FAuracronStreamingStatistics **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_239_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStreamingStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStreamingStatistics;
// ********** End ScriptStruct FAuracronStreamingStatistics ****************************************

// ********** Begin Class UAuracronWorldPartitionLogger ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSaveLogsToFile); \
	DECLARE_FUNCTION(execGetRecentLogs); \
	DECLARE_FUNCTION(execClearLogs); \
	DECLARE_FUNCTION(execIsCategoryEnabled); \
	DECLARE_FUNCTION(execEnableCategoryLogging); \
	DECLARE_FUNCTION(execGetLogLevel); \
	DECLARE_FUNCTION(execSetLogLevel); \
	DECLARE_FUNCTION(execLogVerbose); \
	DECLARE_FUNCTION(execLogInfo); \
	DECLARE_FUNCTION(execLogWarning); \
	DECLARE_FUNCTION(execLogError); \
	DECLARE_FUNCTION(execLogMessage); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionLogger(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionLogger_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionLogger, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionLogger_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionLogger)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionLogger(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionLogger(UAuracronWorldPartitionLogger&&) = delete; \
	UAuracronWorldPartitionLogger(const UAuracronWorldPartitionLogger&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionLogger); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionLogger); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionLogger) \
	NO_API virtual ~UAuracronWorldPartitionLogger();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_293_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_296_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionLogger;

// ********** End Class UAuracronWorldPartitionLogger **********************************************

// ********** Begin Delegate FOnCellLoaded *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_511_DELEGATE \
static void FOnCellLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnCellLoaded, const FString& CellId, float LoadingTime);


// ********** End Delegate FOnCellLoaded ***********************************************************

// ********** Begin Delegate FOnCellUnloaded *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_512_DELEGATE \
static void FOnCellUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnCellUnloaded, const FString& CellId, float UnloadingTime);


// ********** End Delegate FOnCellUnloaded *********************************************************

// ********** Begin Delegate FOnStreamingStateChanged **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_513_DELEGATE \
static void FOnStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnStreamingStateChanged, EAuracronStreamingState OldState, EAuracronStreamingState NewState);


// ********** End Delegate FOnStreamingStateChanged ************************************************

// ********** Begin Class UAuracronWorldPartitionManager *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execLogCurrentState); \
	DECLARE_FUNCTION(execDrawDebugCells); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execGetTotalCellCount); \
	DECLARE_FUNCTION(execGetLoadedCellCount); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetStreamingStatistics); \
	DECLARE_FUNCTION(execGetStreamingDistance); \
	DECLARE_FUNCTION(execSetStreamingDistance); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execGetLoadedDataLayers); \
	DECLARE_FUNCTION(execGetAvailableDataLayers); \
	DECLARE_FUNCTION(execGetDataLayerState); \
	DECLARE_FUNCTION(execSetDataLayerState); \
	DECLARE_FUNCTION(execUpdateStreamingState); \
	DECLARE_FUNCTION(execSetStreamingSource); \
	DECLARE_FUNCTION(execRequestCellsInRadius); \
	DECLARE_FUNCTION(execRequestCellUnloading); \
	DECLARE_FUNCTION(execRequestCellLoading); \
	DECLARE_FUNCTION(execGetStreamingCells); \
	DECLARE_FUNCTION(execGetLoadedCells); \
	DECLARE_FUNCTION(execGetCellsInRadius); \
	DECLARE_FUNCTION(execGetCellInfo); \
	DECLARE_FUNCTION(execGetAllCells); \
	DECLARE_FUNCTION(execGetWorldPartitionSubsystem); \
	DECLARE_FUNCTION(execGetDataLayerSubsystem); \
	DECLARE_FUNCTION(execGetWorldPartition); \
	DECLARE_FUNCTION(execSetupWorldPartition); \
	DECLARE_FUNCTION(execGetState); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdownWorldPartition); \
	DECLARE_FUNCTION(execInitializeWorldPartition); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionManager, UEngineSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionManager(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionManager(UAuracronWorldPartitionManager&&) = delete; \
	UAuracronWorldPartitionManager(const UAuracronWorldPartitionManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronWorldPartitionManager) \
	NO_API virtual ~UAuracronWorldPartitionManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_383_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h_386_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionManager;

// ********** End Class UAuracronWorldPartitionManager *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartition_h

// ********** Begin Enum EAuracronWorldPartitionState **********************************************
#define FOREACH_ENUM_EAURACRONWORLDPARTITIONSTATE(op) \
	op(EAuracronWorldPartitionState::Uninitialized) \
	op(EAuracronWorldPartitionState::Initializing) \
	op(EAuracronWorldPartitionState::Ready) \
	op(EAuracronWorldPartitionState::Streaming) \
	op(EAuracronWorldPartitionState::Error) \
	op(EAuracronWorldPartitionState::Shutdown) 

enum class EAuracronWorldPartitionState : uint8;
template<> struct TIsUEnumClass<EAuracronWorldPartitionState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionState>();
// ********** End Enum EAuracronWorldPartitionState ************************************************

// ********** Begin Enum EAuracronStreamingState ***************************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGSTATE(op) \
	op(EAuracronStreamingState::Unloaded) \
	op(EAuracronStreamingState::Loading) \
	op(EAuracronStreamingState::Loaded) \
	op(EAuracronStreamingState::Unloading) \
	op(EAuracronStreamingState::Failed) 

enum class EAuracronStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronStreamingState>();
// ********** End Enum EAuracronStreamingState *****************************************************

// ********** Begin Enum EAuracronCellType *********************************************************
#define FOREACH_ENUM_EAURACRONCELLTYPE(op) \
	op(EAuracronCellType::Static) \
	op(EAuracronCellType::Dynamic) \
	op(EAuracronCellType::Streaming) \
	op(EAuracronCellType::AlwaysLoaded) \
	op(EAuracronCellType::RuntimeGenerated) 

enum class EAuracronCellType : uint8;
template<> struct TIsUEnumClass<EAuracronCellType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCellType>();
// ********** End Enum EAuracronCellType ***********************************************************

// ********** Begin Enum EAuracronWorldPartitionLogLevel *******************************************
#define FOREACH_ENUM_EAURACRONWORLDPARTITIONLOGLEVEL(op) \
	op(EAuracronWorldPartitionLogLevel::None) \
	op(EAuracronWorldPartitionLogLevel::Error) \
	op(EAuracronWorldPartitionLogLevel::Warning) \
	op(EAuracronWorldPartitionLogLevel::Log) \
	op(EAuracronWorldPartitionLogLevel::Verbose) \
	op(EAuracronWorldPartitionLogLevel::VeryVerbose) 

enum class EAuracronWorldPartitionLogLevel : uint8;
template<> struct TIsUEnumClass<EAuracronWorldPartitionLogLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionLogLevel>();
// ********** End Enum EAuracronWorldPartitionLogLevel *********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
