{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\source\\auracronpcgbridge\\private\\auracronpcgbridgemodule.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronpcgbridge\\definitions.auracronpcgbridge.h", "c:\\aura\\projeto\\source\\auracronpcgbridge\\public\\auracronpcgbridgemodule.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}