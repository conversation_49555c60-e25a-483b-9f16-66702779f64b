// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for AuracronPCGBridge
#pragma once
#include "SharedDefinitions.UnrealEd.Project.NonOptimized.RTTI.ValApi.ValExpApi.Cpp20.h"
#undef AURACRONPCGBRIDGE_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Auracron_PCG_Test
#define UE_TARGET_NAME AuracronEditor
#define WITH_PCG_ADVANCED_FEATURES 1
#define WITH_PCG_ASYNC_PROCESSING 1
#define WITH_PCG_PERFORMANCE_MONITORING 1
#define WITH_PCG_MEMORY_OPTIMIZATION 1
#define WITH_PCG_CUSTOM_ELEMENTS 1
#define AURACRON_PCG_BRIDGE_PLATFORM_WINDOWS 1
#define WITH_PCG_MULTITHREADING 1
#define WITH_PCG_SIMD_OPTIMIZATIONS 1
#define WITH_PCG 1
#define AURACRON_PCG_BRIDGE_VERSION_MAJOR 1
#define AURACRON_PCG_BRIDGE_VERSION_MINOR 0
#define AURACRON_PCG_BRIDGE_VERSION_PATCH 0
#define UE_MODULE_NAME "AuracronPCGBridge"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define SERIALIZATION_API DLLIMPORT
#define CBOR_API DLLIMPORT
#define COMPONENTVISUALIZERS_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define AURACRONPCGBRIDGE_API DLLEXPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define PCGGEOMETRYSCRIPTINTEROP_API DLLIMPORT
#define GEOMETRYSCRIPTINGCORE_API DLLIMPORT
#define GEOMETRYFRAMEWORK_API DLLIMPORT
#define MESHCONVERSION_API DLLIMPORT
#define DYNAMICMESH_API DLLIMPORT
#define GEOMETRYALGORITHMS_API DLLIMPORT
#define MODELINGOPERATORS_API DLLIMPORT
#define TEXTUREUTILITIESCOMMON_API DLLIMPORT
#define MODELINGCOMPONENTS_API DLLIMPORT
