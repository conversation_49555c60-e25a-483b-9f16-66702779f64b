// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronHarmonyEngineBridge_init() {}
	AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature();
	AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature();
	AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature();
	AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature();
	AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronHarmonyEngineBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronHarmonyEngineBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronHarmonyEngineBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x8F024857,
				0x7BC00AD3,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronHarmonyEngineBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronHarmonyEngineBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronHarmonyEngineBridge(Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Registration_Info_UPackage__Script_AuracronHarmonyEngineBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x8F024857, 0x7BC00AD3));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
