// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAnalyticsBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAnalyticsBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONANALYTICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronAnalyticsBridge();
AURACRONANALYTICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronAnalyticsBridge_NoRegister();
AURACRONANALYTICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType();
AURACRONANALYTICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory();
AURACRONANALYTICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature();
AURACRONANALYTICSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature();
AURACRONANALYTICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronABTestConfiguration();
AURACRONANALYTICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnalyticsEvent();
AURACRONANALYTICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBalanceMetrics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronAnalyticsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronAnalyticsEventType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAnalyticsEventType;
static UEnum* EAuracronAnalyticsEventType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType, (UObject*)Z_Construct_UPackage__Script_AuracronAnalyticsBridge(), TEXT("EAuracronAnalyticsEventType"));
	}
	return Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.OuterSingleton;
}
template<> AURACRONANALYTICSBRIDGE_API UEnum* StaticEnum<EAuracronAnalyticsEventType>()
{
	return EAuracronAnalyticsEventType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "ABTestEvent.DisplayName", "A/B Test Event" },
		{ "ABTestEvent.Name", "EAuracronAnalyticsEventType::ABTestEvent" },
		{ "BalanceData.DisplayName", "Balance Data" },
		{ "BalanceData.Name", "EAuracronAnalyticsEventType::BalanceData" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de eventos anal\xc3\x83\xc2\xadticos\n */" },
#endif
		{ "CustomEvent.DisplayName", "Custom Event" },
		{ "CustomEvent.Name", "EAuracronAnalyticsEventType::CustomEvent" },
		{ "GameplayEvent.DisplayName", "Gameplay Event" },
		{ "GameplayEvent.Name", "EAuracronAnalyticsEventType::GameplayEvent" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
		{ "MonetizationEvent.DisplayName", "Monetization Event" },
		{ "MonetizationEvent.Name", "EAuracronAnalyticsEventType::MonetizationEvent" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronAnalyticsEventType::None" },
		{ "PerformanceMetric.DisplayName", "Performance Metric" },
		{ "PerformanceMetric.Name", "EAuracronAnalyticsEventType::PerformanceMetric" },
		{ "SecurityEvent.DisplayName", "Security Event" },
		{ "SecurityEvent.Name", "EAuracronAnalyticsEventType::SecurityEvent" },
		{ "TechnicalMetric.DisplayName", "Technical Metric" },
		{ "TechnicalMetric.Name", "EAuracronAnalyticsEventType::TechnicalMetric" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de eventos anal\xc3\x83\xc2\xadticos" },
#endif
		{ "UserBehavior.DisplayName", "User Behavior" },
		{ "UserBehavior.Name", "EAuracronAnalyticsEventType::UserBehavior" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAnalyticsEventType::None", (int64)EAuracronAnalyticsEventType::None },
		{ "EAuracronAnalyticsEventType::GameplayEvent", (int64)EAuracronAnalyticsEventType::GameplayEvent },
		{ "EAuracronAnalyticsEventType::PerformanceMetric", (int64)EAuracronAnalyticsEventType::PerformanceMetric },
		{ "EAuracronAnalyticsEventType::UserBehavior", (int64)EAuracronAnalyticsEventType::UserBehavior },
		{ "EAuracronAnalyticsEventType::BalanceData", (int64)EAuracronAnalyticsEventType::BalanceData },
		{ "EAuracronAnalyticsEventType::MonetizationEvent", (int64)EAuracronAnalyticsEventType::MonetizationEvent },
		{ "EAuracronAnalyticsEventType::TechnicalMetric", (int64)EAuracronAnalyticsEventType::TechnicalMetric },
		{ "EAuracronAnalyticsEventType::SecurityEvent", (int64)EAuracronAnalyticsEventType::SecurityEvent },
		{ "EAuracronAnalyticsEventType::ABTestEvent", (int64)EAuracronAnalyticsEventType::ABTestEvent },
		{ "EAuracronAnalyticsEventType::CustomEvent", (int64)EAuracronAnalyticsEventType::CustomEvent },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
	nullptr,
	"EAuracronAnalyticsEventType",
	"EAuracronAnalyticsEventType",
	Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType()
{
	if (!Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.InnerSingleton, Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAnalyticsEventType.InnerSingleton;
}
// ********** End Enum EAuracronAnalyticsEventType *************************************************

// ********** Begin Enum EAuracronBalanceMetricCategory ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory;
static UEnum* EAuracronBalanceMetricCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory, (UObject*)Z_Construct_UPackage__Script_AuracronAnalyticsBridge(), TEXT("EAuracronBalanceMetricCategory"));
	}
	return Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.OuterSingleton;
}
template<> AURACRONANALYTICSBRIDGE_API UEnum* StaticEnum<EAuracronBalanceMetricCategory>()
{
	return EAuracronBalanceMetricCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AbilityUsage.DisplayName", "Ability Usage" },
		{ "AbilityUsage.Name", "EAuracronBalanceMetricCategory::AbilityUsage" },
		{ "BlueprintType", "true" },
		{ "ChampionPerformance.DisplayName", "Champion Performance" },
		{ "ChampionPerformance.Name", "EAuracronBalanceMetricCategory::ChampionPerformance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para categorias de m\xc3\x83\xc2\xa9tricas de balanceamento\n */" },
#endif
		{ "EconomyBalance.DisplayName", "Economy Balance" },
		{ "EconomyBalance.Name", "EAuracronBalanceMetricCategory::EconomyBalance" },
		{ "ItemEffectiveness.DisplayName", "Item Effectiveness" },
		{ "ItemEffectiveness.Name", "EAuracronBalanceMetricCategory::ItemEffectiveness" },
		{ "MatchDuration.DisplayName", "Match Duration" },
		{ "MatchDuration.Name", "EAuracronBalanceMetricCategory::MatchDuration" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
		{ "ObjectiveControl.DisplayName", "Objective Control" },
		{ "ObjectiveControl.Name", "EAuracronBalanceMetricCategory::ObjectiveControl" },
		{ "PlayerProgression.DisplayName", "Player Progression" },
		{ "PlayerProgression.Name", "EAuracronBalanceMetricCategory::PlayerProgression" },
		{ "RealmBalance.DisplayName", "Realm Balance" },
		{ "RealmBalance.Name", "EAuracronBalanceMetricCategory::RealmBalance" },
		{ "SigiloImpact.DisplayName", "Sigilo Impact" },
		{ "SigiloImpact.Name", "EAuracronBalanceMetricCategory::SigiloImpact" },
		{ "TeamComposition.DisplayName", "Team Composition" },
		{ "TeamComposition.Name", "EAuracronBalanceMetricCategory::TeamComposition" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para categorias de m\xc3\x83\xc2\xa9tricas de balanceamento" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronBalanceMetricCategory::ChampionPerformance", (int64)EAuracronBalanceMetricCategory::ChampionPerformance },
		{ "EAuracronBalanceMetricCategory::AbilityUsage", (int64)EAuracronBalanceMetricCategory::AbilityUsage },
		{ "EAuracronBalanceMetricCategory::ItemEffectiveness", (int64)EAuracronBalanceMetricCategory::ItemEffectiveness },
		{ "EAuracronBalanceMetricCategory::SigiloImpact", (int64)EAuracronBalanceMetricCategory::SigiloImpact },
		{ "EAuracronBalanceMetricCategory::RealmBalance", (int64)EAuracronBalanceMetricCategory::RealmBalance },
		{ "EAuracronBalanceMetricCategory::MatchDuration", (int64)EAuracronBalanceMetricCategory::MatchDuration },
		{ "EAuracronBalanceMetricCategory::PlayerProgression", (int64)EAuracronBalanceMetricCategory::PlayerProgression },
		{ "EAuracronBalanceMetricCategory::EconomyBalance", (int64)EAuracronBalanceMetricCategory::EconomyBalance },
		{ "EAuracronBalanceMetricCategory::TeamComposition", (int64)EAuracronBalanceMetricCategory::TeamComposition },
		{ "EAuracronBalanceMetricCategory::ObjectiveControl", (int64)EAuracronBalanceMetricCategory::ObjectiveControl },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
	nullptr,
	"EAuracronBalanceMetricCategory",
	"EAuracronBalanceMetricCategory",
	Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory()
{
	if (!Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.InnerSingleton, Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory.InnerSingleton;
}
// ********** End Enum EAuracronBalanceMetricCategory **********************************************

// ********** Begin ScriptStruct FAuracronAnalyticsEvent *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent;
class UScriptStruct* FAuracronAnalyticsEvent::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAnalyticsEvent, (UObject*)Z_Construct_UPackage__Script_AuracronAnalyticsBridge(), TEXT("AuracronAnalyticsEvent"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para evento anal\xc3\x83\xc2\xadtico\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para evento anal\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventID_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\x83\xc2\xbanico do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\x83\xc2\xbanico do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventName_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventCategory_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Categoria do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTimestamp_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da sess\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da sess\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchID_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da partida */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da partida" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventParameters_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\x83\xc2\xa2metros do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\x83\xc2\xa2metros do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumericValues_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valores num\xc3\x83\xc2\xa9ricos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valores num\xc3\x83\xc2\xa9ricos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldLocation_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmIndex_MetaData[] = {
		{ "Category", "Analytics Event" },
		{ "ClampMax", "2" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Realm onde ocorreu */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm onde ocorreu" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Analytics Event" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade do evento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTags_MetaData[] = {
		{ "Category", "Analytics Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do evento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do evento" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EventType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventCategory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTimestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MatchID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventParameters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NumericValues_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NumericValues_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NumericValues;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldLocation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAnalyticsEvent>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventID = { "EventID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventID_MetaData), NewProp_EventID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventName = { "EventName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventName_MetaData), NewProp_EventName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventType), Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronAnalyticsEventType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) }; // 1981641102
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventCategory = { "EventCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventCategory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventCategory_MetaData), NewProp_EventCategory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventTimestamp = { "EventTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTimestamp_MetaData), NewProp_EventTimestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_MatchID = { "MatchID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, MatchID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchID_MetaData), NewProp_MatchID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters_ValueProp = { "EventParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters_Key_KeyProp = { "EventParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters = { "EventParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventParameters_MetaData), NewProp_EventParameters_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues_ValueProp = { "NumericValues", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues_Key_KeyProp = { "NumericValues_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues = { "NumericValues", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, NumericValues), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumericValues_MetaData), NewProp_NumericValues_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_WorldLocation = { "WorldLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, WorldLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldLocation_MetaData), NewProp_WorldLocation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, RealmIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmIndex_MetaData), NewProp_RealmIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventTags = { "EventTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAnalyticsEvent, EventTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTags_MetaData), NewProp_EventTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventTimestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_MatchID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_NumericValues,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_WorldLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewProp_EventTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
	nullptr,
	&NewStructOps,
	"AuracronAnalyticsEvent",
	Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::PropPointers),
	sizeof(FAuracronAnalyticsEvent),
	alignof(FAuracronAnalyticsEvent),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAnalyticsEvent()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAnalyticsEvent *********************************************

// ********** Begin ScriptStruct FAuracronBalanceMetrics *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics;
class UScriptStruct* FAuracronBalanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBalanceMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronAnalyticsBridge(), TEXT("AuracronBalanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para m\xc3\x83\xc2\xa9tricas de balanceamento\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para m\xc3\x83\xc2\xa9tricas de balanceamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Balance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Categoria da m\xc3\x83\xc2\xa9trica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria da m\xc3\x83\xc2\xa9trica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectID_MetaData[] = {
		{ "Category", "Balance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do objeto sendo analisado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do objeto sendo analisado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WinRate_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de vit\xc3\x83\xc2\xb3ria */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de vit\xc3\x83\xc2\xb3ria" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PickRate_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de pick */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de pick" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BanRate_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Taxa de ban */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Taxa de ban" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageKDA_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** KDA m\xc3\x83\xc2\xa9""dio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "KDA m\xc3\x83\xc2\xa9""dio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageDamagePerMinute_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dano m\xc3\x83\xc2\xa9""dio por minuto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dano m\xc3\x83\xc2\xa9""dio por minuto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageGoldPerMinute_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gold m\xc3\x83\xc2\xa9""dio por minuto */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gold m\xc3\x83\xc2\xa9""dio por minuto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageTimeToFirstDeath_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo m\xc3\x83\xc2\xa9""dio de primeira morte */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo m\xc3\x83\xc2\xa9""dio de primeira morte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamFightParticipation_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Participa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em team fights */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Participa\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o em team fights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveEffectiveness_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efetividade em objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efetividade em objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SampleSize_MetaData[] = {
		{ "Category", "Balance Metrics" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xbamero de amostras */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xbamero de amostras" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdated_MetaData[] = {
		{ "Category", "Balance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x83\xc5\xa1ltima atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x83\xc5\xa1ltima atualiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WinRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PickRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BanRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageKDA;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageDamagePerMinute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageGoldPerMinute;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageTimeToFirstDeath;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamFightParticipation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveEffectiveness;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SampleSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdated;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBalanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, Category), Z_Construct_UEnum_AuracronAnalyticsBridge_EAuracronBalanceMetricCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 2188752279
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_ObjectID = { "ObjectID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, ObjectID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectID_MetaData), NewProp_ObjectID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_WinRate = { "WinRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, WinRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WinRate_MetaData), NewProp_WinRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_PickRate = { "PickRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, PickRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PickRate_MetaData), NewProp_PickRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_BanRate = { "BanRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, BanRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BanRate_MetaData), NewProp_BanRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageKDA = { "AverageKDA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, AverageKDA), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageKDA_MetaData), NewProp_AverageKDA_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageDamagePerMinute = { "AverageDamagePerMinute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, AverageDamagePerMinute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageDamagePerMinute_MetaData), NewProp_AverageDamagePerMinute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageGoldPerMinute = { "AverageGoldPerMinute", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, AverageGoldPerMinute), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageGoldPerMinute_MetaData), NewProp_AverageGoldPerMinute_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageTimeToFirstDeath = { "AverageTimeToFirstDeath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, AverageTimeToFirstDeath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageTimeToFirstDeath_MetaData), NewProp_AverageTimeToFirstDeath_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_TeamFightParticipation = { "TeamFightParticipation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, TeamFightParticipation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamFightParticipation_MetaData), NewProp_TeamFightParticipation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_ObjectiveEffectiveness = { "ObjectiveEffectiveness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, ObjectiveEffectiveness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveEffectiveness_MetaData), NewProp_ObjectiveEffectiveness_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_SampleSize = { "SampleSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, SampleSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SampleSize_MetaData), NewProp_SampleSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_LastUpdated = { "LastUpdated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBalanceMetrics, LastUpdated), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdated_MetaData), NewProp_LastUpdated_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_ObjectID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_WinRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_PickRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_BanRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageKDA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageDamagePerMinute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageGoldPerMinute,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_AverageTimeToFirstDeath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_TeamFightParticipation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_ObjectiveEffectiveness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_SampleSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewProp_LastUpdated,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
	nullptr,
	&NewStructOps,
	"AuracronBalanceMetrics",
	Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::PropPointers),
	sizeof(FAuracronBalanceMetrics),
	alignof(FAuracronBalanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBalanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBalanceMetrics *********************************************

// ********** Begin ScriptStruct FAuracronABTestConfiguration **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration;
class UScriptStruct* FAuracronABTestConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronABTestConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronAnalyticsBridge(), TEXT("AuracronABTestConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de A/B Testing\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de A/B Testing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestID_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID do teste A/B */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID do teste A/B" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestName_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome do teste */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome do teste" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestDescription_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do teste */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Descri\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do teste" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariantA_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Variante A (controle) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variante A (controle)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariantB_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Variante B (teste) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variante B (teste)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariantBPercentage_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Porcentagem de jogadores na variante B */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Porcentagem de jogadores na variante B" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Teste est\xc3\x83\xc2\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Teste est\xc3\x83\xc2\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartDate_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de in\xc3\x83\xc2\xad""cio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de in\xc3\x83\xc2\xad""cio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndDate_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data de fim */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data de fim" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricsToTrack_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xa9tricas a serem coletadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xa9tricas a serem coletadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessCriteria_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Crit\xc3\x83\xc2\xa9rio de sucesso */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Crit\xc3\x83\xc2\xa9rio de sucesso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumSampleSize_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho m\xc3\x83\xc2\xadnimo da amostra */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho m\xc3\x83\xc2\xadnimo da amostra" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConfidenceLevel_MetaData[] = {
		{ "Category", "A/B Test Configuration" },
		{ "ClampMax", "0.99" },
		{ "ClampMin", "0.8" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\x83\xc2\xadvel de confian\xc3\x83\xc2\xa7""a estat\xc3\x83\xc2\xadstica */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\x83\xc2\xadvel de confian\xc3\x83\xc2\xa7""a estat\xc3\x83\xc2\xadstica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariantA_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariantA_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VariantA;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariantB_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VariantB_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VariantB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VariantBPercentage;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartDate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndDate;
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricsToTrack_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MetricsToTrack;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuccessCriteria;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinimumSampleSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConfidenceLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronABTestConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestID = { "TestID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, TestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestID_MetaData), NewProp_TestID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestName = { "TestName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, TestName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestName_MetaData), NewProp_TestName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestDescription = { "TestDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, TestDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestDescription_MetaData), NewProp_TestDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA_ValueProp = { "VariantA", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA_Key_KeyProp = { "VariantA_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA = { "VariantA", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, VariantA), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariantA_MetaData), NewProp_VariantA_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB_ValueProp = { "VariantB", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB_Key_KeyProp = { "VariantB_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB = { "VariantB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, VariantB), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariantB_MetaData), NewProp_VariantB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantBPercentage = { "VariantBPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, VariantBPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariantBPercentage_MetaData), NewProp_VariantBPercentage_MetaData) };
void Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronABTestConfiguration*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronABTestConfiguration), &Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_StartDate = { "StartDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, StartDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartDate_MetaData), NewProp_StartDate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_EndDate = { "EndDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, EndDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndDate_MetaData), NewProp_EndDate_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MetricsToTrack_Inner = { "MetricsToTrack", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MetricsToTrack = { "MetricsToTrack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, MetricsToTrack), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricsToTrack_MetaData), NewProp_MetricsToTrack_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_SuccessCriteria = { "SuccessCriteria", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, SuccessCriteria), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessCriteria_MetaData), NewProp_SuccessCriteria_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MinimumSampleSize = { "MinimumSampleSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, MinimumSampleSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumSampleSize_MetaData), NewProp_MinimumSampleSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_ConfidenceLevel = { "ConfidenceLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronABTestConfiguration, ConfidenceLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConfidenceLevel_MetaData), NewProp_ConfidenceLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_TestDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantA,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_VariantBPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_StartDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_EndDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MetricsToTrack_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MetricsToTrack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_SuccessCriteria,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_MinimumSampleSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewProp_ConfidenceLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
	nullptr,
	&NewStructOps,
	"AuracronABTestConfiguration",
	Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::PropPointers),
	sizeof(FAuracronABTestConfiguration),
	alignof(FAuracronABTestConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronABTestConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronABTestConfiguration ****************************************

// ********** Begin Delegate FOnAnalyticsEventRecorded *********************************************
struct Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics
{
	struct AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms
	{
		FAuracronAnalyticsEvent Event;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando evento \xc3\x83\xc2\xa9 registrado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando evento \xc3\x83\xc2\xa9 registrado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Event;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::NewProp_Event = { "Event", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms, Event), Z_Construct_UScriptStruct_FAuracronAnalyticsEvent, METADATA_PARAMS(0, nullptr) }; // 807406157
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::NewProp_Event,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "OnAnalyticsEventRecorded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAnalyticsBridge::FOnAnalyticsEventRecorded_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsEventRecorded, FAuracronAnalyticsEvent Event)
{
	struct AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms
	{
		FAuracronAnalyticsEvent Event;
	};
	AuracronAnalyticsBridge_eventOnAnalyticsEventRecorded_Parms Parms;
	Parms.Event=Event;
	OnAnalyticsEventRecorded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAnalyticsEventRecorded ***********************************************

// ********** Begin Delegate FOnAnalyticsDataSynced ************************************************
struct Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics
{
	struct AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando dados s\xc3\x83\xc2\xa3o sincronizados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando dados s\xc3\x83\xc2\xa3o sincronizados" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms), &Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "OnAnalyticsDataSynced__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronAnalyticsBridge::FOnAnalyticsDataSynced_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsDataSynced, bool bSuccess)
{
	struct AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms
	{
		bool bSuccess;
	};
	AuracronAnalyticsBridge_eventOnAnalyticsDataSynced_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnAnalyticsDataSynced.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAnalyticsDataSynced **************************************************

// ********** Begin Class UAuracronAnalyticsBridge Function CollectAbilityMetrics ******************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics
{
	struct AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms
	{
		FString ChampionID;
		FString AbilitySlot;
		TMap<FString,float> Metrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Balance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar m\xc3\x83\xc2\xa9tricas de habilidade\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar m\xc3\x83\xc2\xa9tricas de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Metrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics_ValueProp = { "Metrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics_Key_KeyProp = { "Metrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms, Metrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_Metrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "CollectAbilityMetrics", Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::AuracronAnalyticsBridge_eventCollectAbilityMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execCollectAbilityMetrics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Metrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CollectAbilityMetrics(Z_Param_ChampionID,Z_Param_AbilitySlot,Z_Param_Out_Metrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function CollectAbilityMetrics ********************

// ********** Begin Class UAuracronAnalyticsBridge Function CollectChampionMetrics *****************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics
{
	struct AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms
	{
		FString ChampionID;
		TMap<FString,float> Metrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Balance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar m\xc3\x83\xc2\xa9tricas de campe\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar m\xc3\x83\xc2\xa9tricas de campe\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Metrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics_ValueProp = { "Metrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics_Key_KeyProp = { "Metrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms, Metrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_Metrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "CollectChampionMetrics", Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::AuracronAnalyticsBridge_eventCollectChampionMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execCollectChampionMetrics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Metrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CollectChampionMetrics(Z_Param_ChampionID,Z_Param_Out_Metrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function CollectChampionMetrics *******************

// ********** Begin Class UAuracronAnalyticsBridge Function CollectRealmMetrics ********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics
{
	struct AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms
	{
		int32 RealmIndex;
		TMap<FString,float> Metrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Balance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar m\xc3\x83\xc2\xa9tricas de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar m\xc3\x83\xc2\xa9tricas de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Metrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics_ValueProp = { "Metrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics_Key_KeyProp = { "Metrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms, Metrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_Metrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "CollectRealmMetrics", Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::AuracronAnalyticsBridge_eventCollectRealmMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execCollectRealmMetrics)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Metrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CollectRealmMetrics(Z_Param_RealmIndex,Z_Param_Out_Metrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function CollectRealmMetrics **********************

// ********** Begin Class UAuracronAnalyticsBridge Function CollectSigiloMetrics *******************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics
{
	struct AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms
	{
		FString SigiloType;
		TMap<FString,float> Metrics;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Balance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Coletar m\xc3\x83\xc2\xa9tricas de Sigilo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coletar m\xc3\x83\xc2\xa9tricas de Sigilo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SigiloType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SigiloType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Metrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Metrics;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_SigiloType = { "SigiloType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms, SigiloType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SigiloType_MetaData), NewProp_SigiloType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics_ValueProp = { "Metrics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics_Key_KeyProp = { "Metrics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics = { "Metrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms, Metrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metrics_MetaData), NewProp_Metrics_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_SigiloType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_Metrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "CollectSigiloMetrics", Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::AuracronAnalyticsBridge_eventCollectSigiloMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execCollectSigiloMetrics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SigiloType);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Metrics);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CollectSigiloMetrics(Z_Param_SigiloType,Z_Param_Out_Metrics);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function CollectSigiloMetrics *********************

// ********** Begin Class UAuracronAnalyticsBridge Function ExportAnalyticsData ********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics
{
	struct AuracronAnalyticsBridge_eventExportAnalyticsData_Parms
	{
		FString ExportFormat;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Export" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Exportar dados anal\xc3\x83\xc2\xadticos\n     */" },
#endif
		{ "CPP_Default_ExportFormat", "JSON" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Exportar dados anal\xc3\x83\xc2\xadticos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExportFormat_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ExportFormat;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ExportFormat = { "ExportFormat", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventExportAnalyticsData_Parms, ExportFormat), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExportFormat_MetaData), NewProp_ExportFormat_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventExportAnalyticsData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventExportAnalyticsData_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ExportFormat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "ExportAnalyticsData", Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::AuracronAnalyticsBridge_eventExportAnalyticsData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::AuracronAnalyticsBridge_eventExportAnalyticsData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execExportAnalyticsData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ExportFormat);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ExportAnalyticsData(Z_Param_ExportFormat);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function ExportAnalyticsData **********************

// ********** Begin Class UAuracronAnalyticsBridge Function GetPlayerVariant ***********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics
{
	struct AuracronAnalyticsBridge_eventGetPlayerVariant_Parms
	{
		FString TestID;
		FString PlayerID;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|ABTesting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Obter variante do jogador para teste\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter variante do jogador para teste" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_TestID = { "TestID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventGetPlayerVariant_Parms, TestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestID_MetaData), NewProp_TestID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventGetPlayerVariant_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventGetPlayerVariant_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_TestID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "GetPlayerVariant", Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::AuracronAnalyticsBridge_eventGetPlayerVariant_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::AuracronAnalyticsBridge_eventGetPlayerVariant_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execGetPlayerVariant)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TestID);
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetPlayerVariant(Z_Param_TestID,Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function GetPlayerVariant *************************

// ********** Begin Class UAuracronAnalyticsBridge Function MonitorFrameRate ***********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics
{
	struct AuracronAnalyticsBridge_eventMonitorFrameRate_Parms
	{
		float AverageFPS;
		float MinFPS;
		float MaxFPS;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar FPS\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar FPS" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFPS;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_AverageFPS = { "AverageFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorFrameRate_Parms, AverageFPS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_MinFPS = { "MinFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorFrameRate_Parms, MinFPS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_MaxFPS = { "MaxFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorFrameRate_Parms, MaxFPS), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventMonitorFrameRate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventMonitorFrameRate_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_AverageFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_MinFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_MaxFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "MonitorFrameRate", Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::AuracronAnalyticsBridge_eventMonitorFrameRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::AuracronAnalyticsBridge_eventMonitorFrameRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execMonitorFrameRate)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_AverageFPS);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MinFPS);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MaxFPS);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorFrameRate(Z_Param_AverageFPS,Z_Param_MinFPS,Z_Param_MaxFPS);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function MonitorFrameRate *************************

// ********** Begin Class UAuracronAnalyticsBridge Function MonitorLoadingTimes ********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics
{
	struct AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms
	{
		FString LoadingStage;
		float LoadingTimeSeconds;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar tempo de carregamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar tempo de carregamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingStage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoadingStage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingTimeSeconds;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_LoadingStage = { "LoadingStage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms, LoadingStage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingStage_MetaData), NewProp_LoadingStage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_LoadingTimeSeconds = { "LoadingTimeSeconds", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms, LoadingTimeSeconds), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_LoadingStage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_LoadingTimeSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "MonitorLoadingTimes", Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::AuracronAnalyticsBridge_eventMonitorLoadingTimes_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execMonitorLoadingTimes)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoadingStage);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LoadingTimeSeconds);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorLoadingTimes(Z_Param_LoadingStage,Z_Param_LoadingTimeSeconds);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function MonitorLoadingTimes **********************

// ********** Begin Class UAuracronAnalyticsBridge Function MonitorMemoryUsage *********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics
{
	struct AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms
	{
		float UsedMemoryMB;
		float AvailableMemoryMB;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar uso de mem\xc3\x83\xc2\xb3ria\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar uso de mem\xc3\x83\xc2\xb3ria" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UsedMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvailableMemoryMB;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_UsedMemoryMB = { "UsedMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms, UsedMemoryMB), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_AvailableMemoryMB = { "AvailableMemoryMB", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms, AvailableMemoryMB), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_UsedMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_AvailableMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "MonitorMemoryUsage", Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::AuracronAnalyticsBridge_eventMonitorMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execMonitorMemoryUsage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_UsedMemoryMB);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AvailableMemoryMB);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorMemoryUsage(Z_Param_UsedMemoryMB,Z_Param_AvailableMemoryMB);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function MonitorMemoryUsage ***********************

// ********** Begin Class UAuracronAnalyticsBridge Function MonitorNetworkLatency ******************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics
{
	struct AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms
	{
		float PingMS;
		float PacketLossPercentage;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Monitorar lat\xc3\x83\xc2\xaancia de rede\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitorar lat\xc3\x83\xc2\xaancia de rede" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PingMS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PacketLossPercentage;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_PingMS = { "PingMS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms, PingMS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_PacketLossPercentage = { "PacketLossPercentage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms, PacketLossPercentage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_PingMS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_PacketLossPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "MonitorNetworkLatency", Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::AuracronAnalyticsBridge_eventMonitorNetworkLatency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execMonitorNetworkLatency)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_PingMS);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PacketLossPercentage);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MonitorNetworkLatency(Z_Param_PingMS,Z_Param_PacketLossPercentage);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function MonitorNetworkLatency ********************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordABTestResult *********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics
{
	struct AuracronAnalyticsBridge_eventRecordABTestResult_Parms
	{
		FString TestID;
		FString PlayerID;
		FString Variant;
		TMap<FString,float> Results;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|ABTesting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar resultado de teste A/B\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar resultado de teste A/B" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Variant_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Results_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Variant;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Results_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Results_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Results;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_TestID = { "TestID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordABTestResult_Parms, TestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestID_MetaData), NewProp_TestID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordABTestResult_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Variant = { "Variant", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordABTestResult_Parms, Variant), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Variant_MetaData), NewProp_Variant_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results_ValueProp = { "Results", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results_Key_KeyProp = { "Results_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results = { "Results", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordABTestResult_Parms, Results), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Results_MetaData), NewProp_Results_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordABTestResult_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordABTestResult_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_TestID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Variant,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_Results,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordABTestResult", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::AuracronAnalyticsBridge_eventRecordABTestResult_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::AuracronAnalyticsBridge_eventRecordABTestResult_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordABTestResult)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TestID);
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Variant);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Results);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordABTestResult(Z_Param_TestID,Z_Param_PlayerID,Z_Param_Variant,Z_Param_Out_Results);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordABTestResult ***********************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordAnalyticsEvent *******************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics
{
	struct AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms
	{
		FAuracronAnalyticsEvent Event;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar evento anal\xc3\x83\xc2\xadtico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar evento anal\xc3\x83\xc2\xadtico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Event_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Event;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_Event = { "Event", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms, Event), Z_Construct_UScriptStruct_FAuracronAnalyticsEvent, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Event_MetaData), NewProp_Event_MetaData) }; // 807406157
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_Event,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordAnalyticsEvent", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::AuracronAnalyticsBridge_eventRecordAnalyticsEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordAnalyticsEvent)
{
	P_GET_STRUCT_REF(FAuracronAnalyticsEvent,Z_Param_Out_Event);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordAnalyticsEvent(Z_Param_Out_Event);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordAnalyticsEvent *********************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordBalanceData **********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics
{
	struct AuracronAnalyticsBridge_eventRecordBalanceData_Parms
	{
		FAuracronBalanceMetrics BalanceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Balance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar dados de balanceamento\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar dados de balanceamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BalanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BalanceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_BalanceData = { "BalanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordBalanceData_Parms, BalanceData), Z_Construct_UScriptStruct_FAuracronBalanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BalanceData_MetaData), NewProp_BalanceData_MetaData) }; // 2100346441
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordBalanceData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordBalanceData_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_BalanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordBalanceData", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::AuracronAnalyticsBridge_eventRecordBalanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::AuracronAnalyticsBridge_eventRecordBalanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordBalanceData)
{
	P_GET_STRUCT_REF(FAuracronBalanceMetrics,Z_Param_Out_BalanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordBalanceData(Z_Param_Out_BalanceData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordBalanceData ************************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordGameplayEvent ********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics
{
	struct AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms
	{
		FString EventName;
		TMap<FString,FString> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar evento de gameplay\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar evento de gameplay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_EventName = { "EventName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms, EventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventName_MetaData), NewProp_EventName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_EventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordGameplayEvent", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::AuracronAnalyticsBridge_eventRecordGameplayEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordGameplayEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordGameplayEvent(Z_Param_EventName,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordGameplayEvent **********************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordGameSession **********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics
{
	struct AuracronAnalyticsBridge_eventRecordGameSession_Parms
	{
		int32 DurationMinutes;
		FString GameMode;
		TMap<FString,FString> SessionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar sess\xc3\x83\xc2\xa3o de jogo\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar sess\xc3\x83\xc2\xa3o de jogo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameMode_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_DurationMinutes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameMode;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SessionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_DurationMinutes = { "DurationMinutes", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordGameSession_Parms, DurationMinutes), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_GameMode = { "GameMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordGameSession_Parms, GameMode), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameMode_MetaData), NewProp_GameMode_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData_ValueProp = { "SessionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData_Key_KeyProp = { "SessionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData = { "SessionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordGameSession_Parms, SessionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionData_MetaData), NewProp_SessionData_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordGameSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordGameSession_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_DurationMinutes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_GameMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_SessionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordGameSession", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::AuracronAnalyticsBridge_eventRecordGameSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::AuracronAnalyticsBridge_eventRecordGameSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordGameSession)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_DurationMinutes);
	P_GET_PROPERTY(FStrProperty,Z_Param_GameMode);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_SessionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordGameSession(Z_Param_DurationMinutes,Z_Param_GameMode,Z_Param_Out_SessionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordGameSession ************************

// ********** Begin Class UAuracronAnalyticsBridge Function RecordPerformanceMetric ****************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics
{
	struct AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms
	{
		FString MetricName;
		float Value;
		FString Context;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Registrar m\xc3\x83\xc2\xa9trica de performance\n     */" },
#endif
		{ "CPP_Default_Context", "" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar m\xc3\x83\xc2\xa9trica de performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MetricName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MetricName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Context;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_MetricName = { "MetricName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms, MetricName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MetricName_MetaData), NewProp_MetricName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms, Context), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_MetricName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "RecordPerformanceMetric", Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::AuracronAnalyticsBridge_eventRecordPerformanceMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execRecordPerformanceMetric)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_MetricName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_GET_PROPERTY(FStrProperty,Z_Param_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RecordPerformanceMetric(Z_Param_MetricName,Z_Param_Value,Z_Param_Context);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function RecordPerformanceMetric ******************

// ********** Begin Class UAuracronAnalyticsBridge Function SendDataToAnalyticsServer **************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics
{
	struct AuracronAnalyticsBridge_eventSendDataToAnalyticsServer_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Export" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enviar dados para servidor de analytics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enviar dados para servidor de analytics" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventSendDataToAnalyticsServer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventSendDataToAnalyticsServer_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "SendDataToAnalyticsServer", Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::AuracronAnalyticsBridge_eventSendDataToAnalyticsServer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::AuracronAnalyticsBridge_eventSendDataToAnalyticsServer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execSendDataToAnalyticsServer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SendDataToAnalyticsServer();
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function SendDataToAnalyticsServer ****************

// ********** Begin Class UAuracronAnalyticsBridge Function StartABTest ****************************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics
{
	struct AuracronAnalyticsBridge_eventStartABTest_Parms
	{
		FAuracronABTestConfiguration TestConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|ABTesting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Iniciar teste A/B\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar teste A/B" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TestConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_TestConfig = { "TestConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventStartABTest_Parms, TestConfig), Z_Construct_UScriptStruct_FAuracronABTestConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestConfig_MetaData), NewProp_TestConfig_MetaData) }; // 2358259459
void Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventStartABTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventStartABTest_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_TestConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "StartABTest", Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::AuracronAnalyticsBridge_eventStartABTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::AuracronAnalyticsBridge_eventStartABTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execStartABTest)
{
	P_GET_STRUCT_REF(FAuracronABTestConfiguration,Z_Param_Out_TestConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartABTest(Z_Param_Out_TestConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function StartABTest ******************************

// ********** Begin Class UAuracronAnalyticsBridge Function StopABTest *****************************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics
{
	struct AuracronAnalyticsBridge_eventStopABTest_Parms
	{
		FString TestID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|ABTesting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar teste A/B\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar teste A/B" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TestID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TestID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_TestID = { "TestID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventStopABTest_Parms, TestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TestID_MetaData), NewProp_TestID_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventStopABTest_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventStopABTest_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_TestID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "StopABTest", Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::AuracronAnalyticsBridge_eventStopABTest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::AuracronAnalyticsBridge_eventStopABTest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execStopABTest)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TestID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopABTest(Z_Param_TestID);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function StopABTest *******************************

// ********** Begin Class UAuracronAnalyticsBridge Function SyncWithFirebaseAnalytics **************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics
{
	struct AuracronAnalyticsBridge_eventSyncWithFirebaseAnalytics_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Export" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Sincronizar com Firebase Analytics\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sincronizar com Firebase Analytics" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventSyncWithFirebaseAnalytics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventSyncWithFirebaseAnalytics_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "SyncWithFirebaseAnalytics", Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::AuracronAnalyticsBridge_eventSyncWithFirebaseAnalytics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::AuracronAnalyticsBridge_eventSyncWithFirebaseAnalytics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execSyncWithFirebaseAnalytics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SyncWithFirebaseAnalytics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function SyncWithFirebaseAnalytics ****************

// ********** Begin Class UAuracronAnalyticsBridge Function TrackEngagement ************************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics
{
	struct AuracronAnalyticsBridge_eventTrackEngagement_Parms
	{
		FString EngagementType;
		float Value;
		FString Context;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear engajamento\n     */" },
#endif
		{ "CPP_Default_Context", "" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear engajamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EngagementType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EngagementType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Context;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_EngagementType = { "EngagementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventTrackEngagement_Parms, EngagementType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EngagementType_MetaData), NewProp_EngagementType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventTrackEngagement_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventTrackEngagement_Parms, Context), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventTrackEngagement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventTrackEngagement_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_EngagementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_Context,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "TrackEngagement", Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::AuracronAnalyticsBridge_eventTrackEngagement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::AuracronAnalyticsBridge_eventTrackEngagement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execTrackEngagement)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EngagementType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_GET_PROPERTY(FStrProperty,Z_Param_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackEngagement(Z_Param_EngagementType,Z_Param_Value,Z_Param_Context);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function TrackEngagement **************************

// ********** Begin Class UAuracronAnalyticsBridge Function TrackPlayerBehavior ********************
struct Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics
{
	struct AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms
	{
		FString BehaviorType;
		TMap<FString,FString> BehaviorData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON Analytics|Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Rastrear comportamento do jogador\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rastrear comportamento do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BehaviorData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BehaviorData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BehaviorData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms, BehaviorType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorType_MetaData), NewProp_BehaviorType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData_ValueProp = { "BehaviorData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData_Key_KeyProp = { "BehaviorData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData = { "BehaviorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms, BehaviorData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorData_MetaData), NewProp_BehaviorData_MetaData) };
void Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms), &Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_BehaviorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAnalyticsBridge, nullptr, "TrackPlayerBehavior", Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::AuracronAnalyticsBridge_eventTrackPlayerBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAnalyticsBridge::execTrackPlayerBehavior)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BehaviorType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_BehaviorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrackPlayerBehavior(Z_Param_BehaviorType,Z_Param_Out_BehaviorData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAnalyticsBridge Function TrackPlayerBehavior **********************

// ********** Begin Class UAuracronAnalyticsBridge *************************************************
void UAuracronAnalyticsBridge::StaticRegisterNativesUAuracronAnalyticsBridge()
{
	UClass* Class = UAuracronAnalyticsBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CollectAbilityMetrics", &UAuracronAnalyticsBridge::execCollectAbilityMetrics },
		{ "CollectChampionMetrics", &UAuracronAnalyticsBridge::execCollectChampionMetrics },
		{ "CollectRealmMetrics", &UAuracronAnalyticsBridge::execCollectRealmMetrics },
		{ "CollectSigiloMetrics", &UAuracronAnalyticsBridge::execCollectSigiloMetrics },
		{ "ExportAnalyticsData", &UAuracronAnalyticsBridge::execExportAnalyticsData },
		{ "GetPlayerVariant", &UAuracronAnalyticsBridge::execGetPlayerVariant },
		{ "MonitorFrameRate", &UAuracronAnalyticsBridge::execMonitorFrameRate },
		{ "MonitorLoadingTimes", &UAuracronAnalyticsBridge::execMonitorLoadingTimes },
		{ "MonitorMemoryUsage", &UAuracronAnalyticsBridge::execMonitorMemoryUsage },
		{ "MonitorNetworkLatency", &UAuracronAnalyticsBridge::execMonitorNetworkLatency },
		{ "RecordABTestResult", &UAuracronAnalyticsBridge::execRecordABTestResult },
		{ "RecordAnalyticsEvent", &UAuracronAnalyticsBridge::execRecordAnalyticsEvent },
		{ "RecordBalanceData", &UAuracronAnalyticsBridge::execRecordBalanceData },
		{ "RecordGameplayEvent", &UAuracronAnalyticsBridge::execRecordGameplayEvent },
		{ "RecordGameSession", &UAuracronAnalyticsBridge::execRecordGameSession },
		{ "RecordPerformanceMetric", &UAuracronAnalyticsBridge::execRecordPerformanceMetric },
		{ "SendDataToAnalyticsServer", &UAuracronAnalyticsBridge::execSendDataToAnalyticsServer },
		{ "StartABTest", &UAuracronAnalyticsBridge::execStartABTest },
		{ "StopABTest", &UAuracronAnalyticsBridge::execStopABTest },
		{ "SyncWithFirebaseAnalytics", &UAuracronAnalyticsBridge::execSyncWithFirebaseAnalytics },
		{ "TrackEngagement", &UAuracronAnalyticsBridge::execTrackEngagement },
		{ "TrackPlayerBehavior", &UAuracronAnalyticsBridge::execTrackPlayerBehavior },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAnalyticsBridge;
UClass* UAuracronAnalyticsBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronAnalyticsBridge;
	if (!Z_Registration_Info_UClass_UAuracronAnalyticsBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAnalyticsBridge"),
			Z_Registration_Info_UClass_UAuracronAnalyticsBridge.InnerSingleton,
			StaticRegisterNativesUAuracronAnalyticsBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAnalyticsBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAnalyticsBridge_NoRegister()
{
	return UAuracronAnalyticsBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAnalyticsBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Telemetria e Analytics\n * Respons\xc3\x83\xc2\xa1vel pela coleta completa de dados para balanceamento e otimiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n */" },
#endif
		{ "DisplayName", "AURACRON Analytics Bridge" },
		{ "IncludePath", "AuracronAnalyticsBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Telemetria e Analytics\nRespons\xc3\x83\xc2\xa1vel pela coleta completa de dados para balanceamento e otimiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventQueue_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Eventos na fila para envio */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos na fila para envio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BalanceMetrics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** M\xc3\x83\xc2\xa9tricas de balanceamento coletadas */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "M\xc3\x83\xc2\xa9tricas de balanceamento coletadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveABTests_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Testes A/B ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Testes A/B ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSessionID_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID da sess\xc3\x83\xc2\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID da sess\xc3\x83\xc2\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionStatistics_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estat\xc3\xadsticas da sess\xc3\xa3o atual */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estat\xc3\xadsticas da sess\xc3\xa3o atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAnalyticsEventRecorded_MetaData[] = {
		{ "Category", "AURACRON Analytics|Events" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAnalyticsDataSynced_MetaData[] = {
		{ "Category", "AURACRON Analytics|Events" },
		{ "ModuleRelativePath", "Public/AuracronAnalyticsBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventQueue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EventQueue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BalanceMetrics_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BalanceMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveABTests_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveABTests;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentSessionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionStatistics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SessionStatistics;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAnalyticsEventRecorded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAnalyticsDataSynced;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectAbilityMetrics, "CollectAbilityMetrics" }, // 3198715068
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectChampionMetrics, "CollectChampionMetrics" }, // 935743478
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectRealmMetrics, "CollectRealmMetrics" }, // 1337020941
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_CollectSigiloMetrics, "CollectSigiloMetrics" }, // 410304512
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_ExportAnalyticsData, "ExportAnalyticsData" }, // 3293930455
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_GetPlayerVariant, "GetPlayerVariant" }, // 218759183
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorFrameRate, "MonitorFrameRate" }, // 2252098144
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorLoadingTimes, "MonitorLoadingTimes" }, // 1019450899
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorMemoryUsage, "MonitorMemoryUsage" }, // 1078738396
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_MonitorNetworkLatency, "MonitorNetworkLatency" }, // 2113615355
		{ &Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature, "OnAnalyticsDataSynced__DelegateSignature" }, // 2336602461
		{ &Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature, "OnAnalyticsEventRecorded__DelegateSignature" }, // 1811026075
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordABTestResult, "RecordABTestResult" }, // 1911485127
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordAnalyticsEvent, "RecordAnalyticsEvent" }, // 1890546885
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordBalanceData, "RecordBalanceData" }, // 3882900674
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameplayEvent, "RecordGameplayEvent" }, // 3188177936
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordGameSession, "RecordGameSession" }, // 3082100838
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_RecordPerformanceMetric, "RecordPerformanceMetric" }, // 2160251663
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_SendDataToAnalyticsServer, "SendDataToAnalyticsServer" }, // 2820696755
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_StartABTest, "StartABTest" }, // 3473977927
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_StopABTest, "StopABTest" }, // 55234933
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_SyncWithFirebaseAnalytics, "SyncWithFirebaseAnalytics" }, // 3151197250
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackEngagement, "TrackEngagement" }, // 337735292
		{ &Z_Construct_UFunction_UAuracronAnalyticsBridge_TrackPlayerBehavior, "TrackPlayerBehavior" }, // 1128595851
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAnalyticsBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_EventQueue_Inner = { "EventQueue", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAnalyticsEvent, METADATA_PARAMS(0, nullptr) }; // 807406157
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_EventQueue = { "EventQueue", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, EventQueue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventQueue_MetaData), NewProp_EventQueue_MetaData) }; // 807406157
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_BalanceMetrics_Inner = { "BalanceMetrics", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronBalanceMetrics, METADATA_PARAMS(0, nullptr) }; // 2100346441
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_BalanceMetrics = { "BalanceMetrics", nullptr, (EPropertyFlags)0x0010000000020035, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, BalanceMetrics), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BalanceMetrics_MetaData), NewProp_BalanceMetrics_MetaData) }; // 2100346441
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_ActiveABTests_Inner = { "ActiveABTests", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronABTestConfiguration, METADATA_PARAMS(0, nullptr) }; // 2358259459
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_ActiveABTests = { "ActiveABTests", nullptr, (EPropertyFlags)0x0010000000000025, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, ActiveABTests), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveABTests_MetaData), NewProp_ActiveABTests_MetaData) }; // 2358259459
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_CurrentSessionID = { "CurrentSessionID", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, CurrentSessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSessionID_MetaData), NewProp_CurrentSessionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics_ValueProp = { "SessionStatistics", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics_Key_KeyProp = { "SessionStatistics_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics = { "SessionStatistics", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, SessionStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionStatistics_MetaData), NewProp_SessionStatistics_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_OnAnalyticsEventRecorded = { "OnAnalyticsEventRecorded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, OnAnalyticsEventRecorded), Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsEventRecorded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAnalyticsEventRecorded_MetaData), NewProp_OnAnalyticsEventRecorded_MetaData) }; // 1811026075
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_OnAnalyticsDataSynced = { "OnAnalyticsDataSynced", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAnalyticsBridge, OnAnalyticsDataSynced), Z_Construct_UDelegateFunction_UAuracronAnalyticsBridge_OnAnalyticsDataSynced__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAnalyticsDataSynced_MetaData), NewProp_OnAnalyticsDataSynced_MetaData) }; // 2336602461
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_EventQueue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_EventQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_BalanceMetrics_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_BalanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_ActiveABTests_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_ActiveABTests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_CurrentSessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_SessionStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_OnAnalyticsEventRecorded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::NewProp_OnAnalyticsDataSynced,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronAnalyticsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::ClassParams = {
	&UAuracronAnalyticsBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAnalyticsBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronAnalyticsBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAnalyticsBridge.OuterSingleton, Z_Construct_UClass_UAuracronAnalyticsBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAnalyticsBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronAnalyticsBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_BalanceMetrics(TEXT("BalanceMetrics"));
	static FName Name_ActiveABTests(TEXT("ActiveABTests"));
	const bool bIsValid = true
		&& Name_BalanceMetrics == ClassReps[(int32)ENetFields_Private::BalanceMetrics].Property->GetFName()
		&& Name_ActiveABTests == ClassReps[(int32)ENetFields_Private::ActiveABTests].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronAnalyticsBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAnalyticsBridge);
UAuracronAnalyticsBridge::~UAuracronAnalyticsBridge() {}
// ********** End Class UAuracronAnalyticsBridge ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronAnalyticsEventType_StaticEnum, TEXT("EAuracronAnalyticsEventType"), &Z_Registration_Info_UEnum_EAuracronAnalyticsEventType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1981641102U) },
		{ EAuracronBalanceMetricCategory_StaticEnum, TEXT("EAuracronBalanceMetricCategory"), &Z_Registration_Info_UEnum_EAuracronBalanceMetricCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2188752279U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronAnalyticsEvent::StaticStruct, Z_Construct_UScriptStruct_FAuracronAnalyticsEvent_Statics::NewStructOps, TEXT("AuracronAnalyticsEvent"), &Z_Registration_Info_UScriptStruct_FAuracronAnalyticsEvent, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAnalyticsEvent), 807406157U) },
		{ FAuracronBalanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronBalanceMetrics_Statics::NewStructOps, TEXT("AuracronBalanceMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronBalanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBalanceMetrics), 2100346441U) },
		{ FAuracronABTestConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronABTestConfiguration_Statics::NewStructOps, TEXT("AuracronABTestConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronABTestConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronABTestConfiguration), 2358259459U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAnalyticsBridge, UAuracronAnalyticsBridge::StaticClass, TEXT("UAuracronAnalyticsBridge"), &Z_Registration_Info_UClass_UAuracronAnalyticsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAnalyticsBridge), 3852350695U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_1937351436(TEXT("/Script/AuracronAnalyticsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronAnalyticsBridge_Public_AuracronAnalyticsBridge_h__Script_AuracronAnalyticsBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
