// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Main Bridge Implementation
// Bridge 2.2: PCG Framework - Core Integration

#include "AuracronPCGBridgeModule.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"

#define LOCTEXT_NAMESPACE "FAuracronPCGBridgeModule"

void FAuracronPCGBridgeModule::StartupModule()
{
    // This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file
    UE_LOG(LogTemp, Warning, TEXT("AuracronPCGBridge module started"));
    
    RegisterPCGNodes();
}

void FAuracronPCGBridgeModule::ShutdownModule()
{
    // This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
    // we call this function before unloading the module.
    UE_LOG(LogTemp, Warning, TEXT("AuracronPCGBridge module shutdown"));
    
    UnregisterPCGNodes();
}

void FAuracronPCGBridgeModule::RegisterPCGNodes()
{
    // Register custom PCG nodes here
    UE_LOG(LogTemp, Log, TEXT("Registering Auracron PCG nodes"));
}

void FAuracronPCGBridgeModule::UnregisterPCGNodes()
{
    // Unregister custom PCG nodes here
    UE_LOG(LogTemp, Log, TEXT("Unregistering Auracron PCG nodes"));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronPCGBridgeModule, AuracronPCGBridge)
