﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node Examples Header
// Bridge 2.13: PCG Framework - Custom Node Creation

#pragma once

#include "CoreMinimal.h"
#include "AuracronPCGNodeSystem.h"
#include "AuracronPCGCustomNodeSystem.h"
#include "AuracronPCGElementBase.h"

// PCG Framework includes
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGPointData.h"

#include "AuracronPCGCustomNodeExamples.generated.h"

// =============================================================================
// EXAMPLE CUSTOM POINT PROCESSOR
// =============================================================================

/**
 * Example Custom Point Processor
 * Demonstrates how to create a custom node using the custom node system
 */

public:
    UAuracronPCGExampleCustomPointProcessorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGExampleCustomPointProcessorSettings();

    // Custom parameters defined through the custom node system
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    float ProcessingStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    int32 ProcessingIterations = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    bool bEnableAdvancedProcessing = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    FString ProcessingMode = TEXT("Default");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    FVector ProcessingOffset = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Processing")
    FLinearColor ProcessingColor = FLinearColor::White;

    // Custom validation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bValidateInputs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    float MinValidValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    float MaxValidValue = 1.0f;

    // Custom execution settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    bool bUseAsyncExecution = false;

    // BatchSize is inherited from UAuracronPCGSettingsBase, no need to redeclare

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;

public:
    // Custom node template creation
    static FAuracronPCGCustomNodeTemplate CreateCustomNodeTemplate();
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGExampleCustomPointProcessorElement, UAuracronPCGExampleCustomPointProcessorSettings)

// =============================================================================
// EXAMPLE CUSTOM GENERATOR
// =============================================================================

/**
 * Example Custom Generator
 * Demonstrates how to create a custom generator node
 */

public:
    UAuracronPCGExampleCustomGeneratorSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGExampleCustomGeneratorSettings();

    // Generation parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 PointCount = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FVector GenerationBounds = FVector(1000.0f, 1000.0f, 1000.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    bool bUseRandomSeed = true;

    // Seed is inherited from UPCGSettings, no need to redeclare

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    TArray<FString> GenerationModes = {TEXT("Random"), TEXT("Grid"), TEXT("Spiral")};

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    FString SelectedMode = TEXT("Random");

    // Pattern parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pattern")
    bool bUsePattern = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pattern", meta = (EditCondition = "bUsePattern"))
    float PatternScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pattern", meta = (EditCondition = "bUsePattern"))
    FVector PatternOffset = FVector::ZeroVector;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;

public:
    // Custom node template creation
    static FAuracronPCGCustomNodeTemplate CreateCustomNodeTemplate();
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGExampleCustomGeneratorElement, UAuracronPCGExampleCustomGeneratorSettings)

// =============================================================================
// EXAMPLE CUSTOM FILTER
// =============================================================================

/**
 * Example Custom Filter
 * Demonstrates how to create a custom filter node
 */

public:
    UAuracronPCGExampleCustomFilterSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

public:
    UAuracronPCGExampleCustomFilterSettings();

    // Filter criteria
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter")
    bool bFilterByPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByPosition"))
    FVector FilterCenter = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByPosition"))
    float FilterRadius = 500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter")
    bool bFilterByDensity = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByDensity"))
    float MinDensity = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByDensity"))
    float MaxDensity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter")
    bool bFilterByColor = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByColor"))
    FLinearColor TargetColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filter", meta = (EditCondition = "bFilterByColor"))
    float ColorTolerance = 0.1f;

    // Filter behavior
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bInvertFilter = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
    bool bSoftFilter = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior", meta = (EditCondition = "bSoftFilter"))
    float SoftFilterFalloff = 0.5f;

protected:
    virtual void ConfigureInputPins(TArray<FPCGPinProperties>& InputPins) const override;
    virtual void ConfigureOutputPins(TArray<FPCGPinProperties>& OutputPins) const override;

public:
    // Custom node template creation
    static FAuracronPCGCustomNodeTemplate CreateCustomNodeTemplate();
};

AURACRON_PCG_NODE_ELEMENT(FAuracronPCGExampleCustomFilterElement, UAuracronPCGExampleCustomFilterSettings)

// =============================================================================
// CUSTOM NODE EXAMPLES MANAGER
// =============================================================================

/**
 * Custom Node Examples Manager
 * Manages and registers example custom nodes
 */

public:
    // Registration functions
    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static void RegisterAllExampleNodes();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static void UnregisterAllExampleNodes();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool RegisterExamplePointProcessor();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool RegisterExampleGenerator();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool RegisterExampleFilter();

    // Template creation functions
    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static TArray<FAuracronPCGCustomNodeTemplate> GetAllExampleTemplates();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static FAuracronPCGCustomNodeTemplate CreateExamplePointProcessorTemplate();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static FAuracronPCGCustomNodeTemplate CreateExampleGeneratorTemplate();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static FAuracronPCGCustomNodeTemplate CreateExampleFilterTemplate();

    // Utility functions
    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool ValidateAllExampleTemplates(TArray<FString>& OutErrors);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static void SaveExampleTemplatesToFile(const FString& Directory);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static void LoadExampleTemplatesFromFile(const FString& Directory);

    // Testing functions
    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool TestExampleNodeCreation();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool TestExampleNodeExecution();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Examples")
    static bool TestExampleNodeValidation();

private:
    static TArray<FString> RegisteredExampleNodes;
};

// =============================================================================
// CUSTOM NODE TUTORIAL HELPER
// =============================================================================

/**
 * Custom Node Tutorial Helper
 * Provides step-by-step guidance for creating custom nodes
 */

public:
    // Tutorial steps
    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate CreateBasicCustomNode(const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate AddParametersToNode(const FAuracronPCGCustomNodeTemplate& BaseTemplate);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate AddPinsToNode(const FAuracronPCGCustomNodeTemplate& BaseTemplate);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate ConfigureNodeExecution(const FAuracronPCGCustomNodeTemplate& BaseTemplate);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate FinalizeCustomNode(const FAuracronPCGCustomNodeTemplate& BaseTemplate);

    // Tutorial validation
    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static bool ValidateTutorialStep(const FAuracronPCGCustomNodeTemplate& Template, int32 StepNumber, FString& OutFeedback);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static TArray<FString> GetTutorialStepInstructions(int32 StepNumber);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static int32 GetTotalTutorialSteps();

    // Tutorial examples
    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static TArray<FAuracronPCGCustomNodeTemplate> GetTutorialExamples();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FAuracronPCGCustomNodeTemplate GetTutorialExample(int32 ExampleIndex);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Tutorial")
    static FString GetTutorialExampleDescription(int32 ExampleIndex);
};
