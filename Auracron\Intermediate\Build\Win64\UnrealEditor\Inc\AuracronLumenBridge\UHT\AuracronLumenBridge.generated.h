// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronLumenBridge.h"

#ifdef AURACRONLUMENBRIDGE_AuracronLumenBridge_generated_h
#error "AuracronLumenBridge.generated.h already included, missing '#pragma once' in AuracronLumenBridge.h"
#endif
#define AURACRONLUMENBRIDGE_AuracronLumenBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UWorld;
enum class EAuracronLumenGIState : uint8;
enum class EAuracronLumenQuality : uint8;
struct FAuracronLumenLightingScenario;
struct FAuracronLumenMetrics;
struct FAuracronLumenQualitySettings;

// ********** Begin ScriptStruct FAuracronLumenMetrics *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_194_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLumenMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLumenMetrics;
// ********** End ScriptStruct FAuracronLumenMetrics ***********************************************

// ********** Begin ScriptStruct FAuracronLumenQualitySettings *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_239_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLumenQualitySettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLumenQualitySettings;
// ********** End ScriptStruct FAuracronLumenQualitySettings ***************************************

// ********** Begin ScriptStruct FAuracronLumenLightingScenario ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_284_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLumenLightingScenario_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLumenLightingScenario;
// ********** End ScriptStruct FAuracronLumenLightingScenario **************************************

// ********** Begin Delegate FOnLumenStateChanged **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_332_DELEGATE \
AURACRONLUMENBRIDGE_API void FOnLumenStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenStateChanged, EAuracronLumenGIState NewState);


// ********** End Delegate FOnLumenStateChanged ****************************************************

// ********** Begin Delegate FOnLumenQualityChanged ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_333_DELEGATE \
AURACRONLUMENBRIDGE_API void FOnLumenQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenQualityChanged, FAuracronLumenQualitySettings const& NewSettings);


// ********** End Delegate FOnLumenQualityChanged **************************************************

// ********** Begin Delegate FOnLumenMetricsUpdated ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_334_DELEGATE \
AURACRONLUMENBRIDGE_API void FOnLumenMetricsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLumenMetricsUpdated, FAuracronLumenMetrics const& Metrics);


// ********** End Delegate FOnLumenMetricsUpdated **************************************************

// ********** Begin Delegate FOnLumenScenarioChanged ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_335_DELEGATE \
AURACRONLUMENBRIDGE_API void FOnLumenScenarioChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLumenScenarioChanged, const FString& ScenarioName, bool bSuccess);


// ********** End Delegate FOnLumenScenarioChanged *************************************************

// ********** Begin Class UAuracronLumenBridgeAPI **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetDetailedLumenStatistics); \
	DECLARE_FUNCTION(execUpdateLumenSceneCapture); \
	DECLARE_FUNCTION(execConfigureMeshCards); \
	DECLARE_FUNCTION(execSetTwoSidedFoliageEnabled); \
	DECLARE_FUNCTION(execConfigureFinalGather); \
	DECLARE_FUNCTION(execSetLumenSceneViewDistance); \
	DECLARE_FUNCTION(execConfigureTranslucency); \
	DECLARE_FUNCTION(execSetScreenProbeGatherEnabled); \
	DECLARE_FUNCTION(execConfigureRadianceCache); \
	DECLARE_FUNCTION(execConfigureSurfaceCache); \
	DECLARE_FUNCTION(execSetHardwareRayTracingEnabled); \
	DECLARE_FUNCTION(execGetRadianceCacheMemoryUsage); \
	DECLARE_FUNCTION(execGetSurfaceCacheMemoryUsage); \
	DECLARE_FUNCTION(execEnablePerformanceMonitoring); \
	DECLARE_FUNCTION(execGetLumenMetrics); \
	DECLARE_FUNCTION(execRemoveLightingScenario); \
	DECLARE_FUNCTION(execGetAvailableLightingScenarios); \
	DECLARE_FUNCTION(execApplyLightingScenario); \
	DECLARE_FUNCTION(execCreateLightingScenario); \
	DECLARE_FUNCTION(execSetReflectionIntensity); \
	DECLARE_FUNCTION(execSetGlobalIlluminationIntensity); \
	DECLARE_FUNCTION(execApplyQualityPreset); \
	DECLARE_FUNCTION(execGetLumenQualitySettings); \
	DECLARE_FUNCTION(execSetLumenQualitySettings); \
	DECLARE_FUNCTION(execUpdateLumenScene); \
	DECLARE_FUNCTION(execGetLumenState); \
	DECLARE_FUNCTION(execIsHardwareRayTracingAvailable); \
	DECLARE_FUNCTION(execIsLumenSupported); \
	DECLARE_FUNCTION(execShutdownLumenSystem); \
	DECLARE_FUNCTION(execInitializeLumenSystem);


AURACRONLUMENBRIDGE_API UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronLumenBridgeAPI(); \
	friend struct Z_Construct_UClass_UAuracronLumenBridgeAPI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONLUMENBRIDGE_API UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronLumenBridgeAPI, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronLumenBridge"), Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister) \
	DECLARE_SERIALIZER(UAuracronLumenBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronLumenBridgeAPI(UAuracronLumenBridgeAPI&&) = delete; \
	UAuracronLumenBridgeAPI(const UAuracronLumenBridgeAPI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronLumenBridgeAPI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronLumenBridgeAPI); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronLumenBridgeAPI) \
	NO_API virtual ~UAuracronLumenBridgeAPI();


#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_341_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h_344_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronLumenBridgeAPI;

// ********** End Class UAuracronLumenBridgeAPI ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronLumenBridge_Public_AuracronLumenBridge_h

// ********** Begin Enum EAuracronLumenQuality *****************************************************
#define FOREACH_ENUM_EAURACRONLUMENQUALITY(op) \
	op(EAuracronLumenQuality::Low) \
	op(EAuracronLumenQuality::Medium) \
	op(EAuracronLumenQuality::High) \
	op(EAuracronLumenQuality::Epic) \
	op(EAuracronLumenQuality::Cinematic) 

enum class EAuracronLumenQuality : uint8;
template<> struct TIsUEnumClass<EAuracronLumenQuality> { enum { Value = true }; };
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenQuality>();
// ********** End Enum EAuracronLumenQuality *******************************************************

// ********** Begin Enum EAuracronLumenRayTracingMode **********************************************
#define FOREACH_ENUM_EAURACRONLUMENRAYTRACINGMODE(op) \
	op(EAuracronLumenRayTracingMode::Software) \
	op(EAuracronLumenRayTracingMode::Hardware) \
	op(EAuracronLumenRayTracingMode::Hybrid) 

enum class EAuracronLumenRayTracingMode : uint8;
template<> struct TIsUEnumClass<EAuracronLumenRayTracingMode> { enum { Value = true }; };
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenRayTracingMode>();
// ********** End Enum EAuracronLumenRayTracingMode ************************************************

// ********** Begin Enum EAuracronLumenSceneMode ***************************************************
#define FOREACH_ENUM_EAURACRONLUMENSCENEMODE(op) \
	op(EAuracronLumenSceneMode::SurfaceCache) \
	op(EAuracronLumenSceneMode::GlobalSDF) \
	op(EAuracronLumenSceneMode::MeshCards) \
	op(EAuracronLumenSceneMode::Hybrid) 

enum class EAuracronLumenSceneMode : uint8;
template<> struct TIsUEnumClass<EAuracronLumenSceneMode> { enum { Value = true }; };
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenSceneMode>();
// ********** End Enum EAuracronLumenSceneMode *****************************************************

// ********** Begin Enum EAuracronLumenReflectionQuality *******************************************
#define FOREACH_ENUM_EAURACRONLUMENREFLECTIONQUALITY(op) \
	op(EAuracronLumenReflectionQuality::Disabled) \
	op(EAuracronLumenReflectionQuality::Low) \
	op(EAuracronLumenReflectionQuality::Medium) \
	op(EAuracronLumenReflectionQuality::High) \
	op(EAuracronLumenReflectionQuality::Epic) 

enum class EAuracronLumenReflectionQuality : uint8;
template<> struct TIsUEnumClass<EAuracronLumenReflectionQuality> { enum { Value = true }; };
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenReflectionQuality>();
// ********** End Enum EAuracronLumenReflectionQuality *********************************************

// ********** Begin Enum EAuracronLumenGIState *****************************************************
#define FOREACH_ENUM_EAURACRONLUMENGISTATE(op) \
	op(EAuracronLumenGIState::Disabled) \
	op(EAuracronLumenGIState::Initializing) \
	op(EAuracronLumenGIState::Active) \
	op(EAuracronLumenGIState::Updating) \
	op(EAuracronLumenGIState::Error) 

enum class EAuracronLumenGIState : uint8;
template<> struct TIsUEnumClass<EAuracronLumenGIState> { enum { Value = true }; };
template<> AURACRONLUMENBRIDGE_API UEnum* StaticEnum<EAuracronLumenGIState>();
// ********** End Enum EAuracronLumenGIState *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
