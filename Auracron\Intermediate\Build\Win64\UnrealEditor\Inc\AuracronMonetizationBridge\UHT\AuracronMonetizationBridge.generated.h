// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMonetizationBridge.h"

#ifdef AURACRONMONETIZATIONBRIDGE_AuracronMonetizationBridge_generated_h
#error "AuracronMonetizationBridge.generated.h already included, missing '#pragma once' in AuracronMonetizationBridge.h"
#endif
#define AURACRONMONETIZATIONBRIDGE_AuracronMonetizationBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronCurrencyType : uint8;
struct FAuracronBattlePass;
struct FAuracronStoreProduct;
struct FAuracronTransaction;

// ********** Begin ScriptStruct FAuracronStoreProduct *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_83_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronStoreProduct_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronStoreProduct;
// ********** End ScriptStruct FAuracronStoreProduct ***********************************************

// ********** Begin ScriptStruct FAuracronBattlePass ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_184_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBattlePass_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBattlePass;
// ********** End ScriptStruct FAuracronBattlePass *************************************************

// ********** Begin ScriptStruct FAuracronPurchaseAnalytics ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_253_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPurchaseAnalytics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPurchaseAnalytics;
// ********** End ScriptStruct FAuracronPurchaseAnalytics ******************************************

// ********** Begin ScriptStruct FAuracronCurrencyBalanceEntry *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_310_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCurrencyBalanceEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCurrencyBalanceEntry;
// ********** End ScriptStruct FAuracronCurrencyBalanceEntry ***************************************

// ********** Begin ScriptStruct FAuracronTransaction **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_336_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTransaction_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTransaction;
// ********** End ScriptStruct FAuracronTransaction ************************************************

// ********** Begin Delegate FOnPurchaseCompleted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_641_DELEGATE \
static void FOnPurchaseCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPurchaseCompleted, FAuracronTransaction Transaction);


// ********** End Delegate FOnPurchaseCompleted ****************************************************

// ********** Begin Delegate FOnBattlePassLevelUp **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_646_DELEGATE \
static void FOnBattlePassLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnBattlePassLevelUp, int32 OldLevel, int32 NewLevel);


// ********** End Delegate FOnBattlePassLevelUp ****************************************************

// ********** Begin Class UAuracronMonetizationBridge **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetMonetizationStatistics); \
	DECLARE_FUNCTION(execTrackPurchase); \
	DECLARE_FUNCTION(execIsSubscriptionActive); \
	DECLARE_FUNCTION(execCancelSubscription); \
	DECLARE_FUNCTION(execActivateSubscription); \
	DECLARE_FUNCTION(execGetPendingGifts); \
	DECLARE_FUNCTION(execReceiveGift); \
	DECLARE_FUNCTION(execSendGift); \
	DECLARE_FUNCTION(execGetBattlePassProgress); \
	DECLARE_FUNCTION(execClaimBattlePassReward); \
	DECLARE_FUNCTION(execAddBattlePassXP); \
	DECLARE_FUNCTION(execPurchaseBattlePass); \
	DECLARE_FUNCTION(execTransferCurrency); \
	DECLARE_FUNCTION(execSpendCurrency); \
	DECLARE_FUNCTION(execAddCurrency); \
	DECLARE_FUNCTION(execGetCurrencyBalance); \
	DECLARE_FUNCTION(execCancelPurchase); \
	DECLARE_FUNCTION(execFinalizePurchase); \
	DECLARE_FUNCTION(execValidatePurchase); \
	DECLARE_FUNCTION(execProcessPurchase); \
	DECLARE_FUNCTION(execInitiatePurchase); \
	DECLARE_FUNCTION(execGetProductPrice); \
	DECLARE_FUNCTION(execIsProductAvailable); \
	DECLARE_FUNCTION(execGetFeaturedProducts); \
	DECLARE_FUNCTION(execGetProductsByCategory); \
	DECLARE_FUNCTION(execLoadStoreProducts);


AURACRONMONETIZATIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronMonetizationBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMonetizationBridge(); \
	friend struct Z_Construct_UClass_UAuracronMonetizationBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMONETIZATIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronMonetizationBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMonetizationBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronMonetizationBridge"), Z_Construct_UClass_UAuracronMonetizationBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMonetizationBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentBattlePass=NETFIELD_REP_START, \
		CurrencyBalances, \
		OwnedProducts, \
		NETFIELD_REP_END=OwnedProducts	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMonetizationBridge(UAuracronMonetizationBridge&&) = delete; \
	UAuracronMonetizationBridge(const UAuracronMonetizationBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMonetizationBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMonetizationBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMonetizationBridge) \
	NO_API virtual ~UAuracronMonetizationBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_395_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h_398_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMonetizationBridge;

// ********** End Class UAuracronMonetizationBridge ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMonetizationBridge_Public_AuracronMonetizationBridge_h

// ********** Begin Enum EAuracronCurrencyType *****************************************************
#define FOREACH_ENUM_EAURACRONCURRENCYTYPE(op) \
	op(EAuracronCurrencyType::None) \
	op(EAuracronCurrencyType::AuraCoins) \
	op(EAuracronCurrencyType::ChronosShards) \
	op(EAuracronCurrencyType::RealmEssence) \
	op(EAuracronCurrencyType::SigiloFragments) \
	op(EAuracronCurrencyType::SeasonTokens) 

enum class EAuracronCurrencyType : uint8;
template<> struct TIsUEnumClass<EAuracronCurrencyType> { enum { Value = true }; };
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronCurrencyType>();
// ********** End Enum EAuracronCurrencyType *******************************************************

// ********** Begin Enum EAuracronProductType ******************************************************
#define FOREACH_ENUM_EAURACRONPRODUCTTYPE(op) \
	op(EAuracronProductType::None) \
	op(EAuracronProductType::Champion) \
	op(EAuracronProductType::Skin) \
	op(EAuracronProductType::BattlePass) \
	op(EAuracronProductType::CurrencyPack) \
	op(EAuracronProductType::Subscription) \
	op(EAuracronProductType::Cosmetic) \
	op(EAuracronProductType::Emote) \
	op(EAuracronProductType::VoicePack) \
	op(EAuracronProductType::Bundle) \
	op(EAuracronProductType::SeasonalItem) 

enum class EAuracronProductType : uint8;
template<> struct TIsUEnumClass<EAuracronProductType> { enum { Value = true }; };
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronProductType>();
// ********** End Enum EAuracronProductType ********************************************************

// ********** Begin Enum EAuracronItemRarity *******************************************************
#define FOREACH_ENUM_EAURACRONITEMRARITY(op) \
	op(EAuracronItemRarity::Common) \
	op(EAuracronItemRarity::Uncommon) \
	op(EAuracronItemRarity::Rare) \
	op(EAuracronItemRarity::Epic) \
	op(EAuracronItemRarity::Legendary) \
	op(EAuracronItemRarity::Mythic) \
	op(EAuracronItemRarity::Limited) \
	op(EAuracronItemRarity::Exclusive) 

enum class EAuracronItemRarity : uint8;
template<> struct TIsUEnumClass<EAuracronItemRarity> { enum { Value = true }; };
template<> AURACRONMONETIZATIONBRIDGE_API UEnum* StaticEnum<EAuracronItemRarity>();
// ********** End Enum EAuracronItemRarity *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
