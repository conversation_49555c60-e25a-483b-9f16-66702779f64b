// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGAdvanced.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGAdvanced() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronMasterPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronMasterPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronQuestPCGSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronQuestPCGSettings_NoRegister();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeConfig();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronResourceDistribution();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronStructurePlacementRule();
AURACRONPCGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTerrainParams();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronBiomeConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig;
class UScriptStruct* FAuracronBiomeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBiomeConfig, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronBiomeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Biome configuration data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome configuration data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeID_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome identifier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeName_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome display name */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome display name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemperatureRange_MetaData[] = {
		{ "Category", "Climate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temperature range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temperature range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HumidityRange_MetaData[] = {
		{ "Category", "Climate" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Humidity range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Humidity range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElevationRange_MetaData[] = {
		{ "Category", "Geography" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elevation range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elevation range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationTypes_MetaData[] = {
		{ "Category", "Vegetation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dominant vegetation types */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dominant vegetation types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceTypes_MetaData[] = {
		{ "Category", "Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource types found in this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource types found in this biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyTypes_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enemy types that spawn in this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enemy types that spawn in this biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedTransitions_MetaData[] = {
		{ "Category", "Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome transition rules */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome transition rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gameplay tags for this biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tags for this biome" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TemperatureRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HumidityRange;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElevationRange;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VegetationTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_VegetationTypes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResourceTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ResourceTypes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnemyTypes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AllowedTransitions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedTransitions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBiomeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeID = { "BiomeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, BiomeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeID_MetaData), NewProp_BiomeID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeName = { "BiomeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, BiomeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeName_MetaData), NewProp_BiomeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_TemperatureRange = { "TemperatureRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, TemperatureRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemperatureRange_MetaData), NewProp_TemperatureRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_HumidityRange = { "HumidityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, HumidityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HumidityRange_MetaData), NewProp_HumidityRange_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ElevationRange = { "ElevationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, ElevationRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElevationRange_MetaData), NewProp_ElevationRange_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_VegetationTypes_Inner = { "VegetationTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_VegetationTypes = { "VegetationTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, VegetationTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationTypes_MetaData), NewProp_VegetationTypes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ResourceTypes_Inner = { "ResourceTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ResourceTypes = { "ResourceTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, ResourceTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceTypes_MetaData), NewProp_ResourceTypes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_EnemyTypes_Inner = { "EnemyTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_EnemyTypes = { "EnemyTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, EnemyTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyTypes_MetaData), NewProp_EnemyTypes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_AllowedTransitions_Inner = { "AllowedTransitions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_AllowedTransitions = { "AllowedTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, AllowedTransitions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedTransitions_MetaData), NewProp_AllowedTransitions_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeTags = { "BiomeTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBiomeConfig, BiomeTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeTags_MetaData), NewProp_BiomeTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_TemperatureRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_HumidityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ElevationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_VegetationTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_VegetationTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ResourceTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_ResourceTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_EnemyTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_EnemyTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_AllowedTransitions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_AllowedTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewProp_BiomeTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronBiomeConfig",
	Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::PropPointers),
	sizeof(FAuracronBiomeConfig),
	alignof(FAuracronBiomeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBiomeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBiomeConfig ************************************************

// ********** Begin ScriptStruct FAuracronTerrainParams ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTerrainParams;
class UScriptStruct* FAuracronTerrainParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTerrainParams, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronTerrainParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Terrain generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseHeight_MetaData[] = {
		{ "Category", "Height" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base height */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base height" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightVariation_MetaData[] = {
		{ "Category", "Height" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Height variation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Height variation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseFrequency_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Noise frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseAmplitude_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Noise amplitude */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Noise amplitude" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Octaves_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of octaves */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of octaves" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Persistence_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Persistence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Persistence" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Lacunarity_MetaData[] = {
		{ "Category", "Noise" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lacunarity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lacunarity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Seed_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Terrain seed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain seed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseAmplitude;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Octaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Persistence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Lacunarity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Seed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTerrainParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_BaseHeight = { "BaseHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, BaseHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseHeight_MetaData), NewProp_BaseHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_HeightVariation = { "HeightVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, HeightVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightVariation_MetaData), NewProp_HeightVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_NoiseFrequency = { "NoiseFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, NoiseFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseFrequency_MetaData), NewProp_NoiseFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_NoiseAmplitude = { "NoiseAmplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, NoiseAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseAmplitude_MetaData), NewProp_NoiseAmplitude_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Octaves = { "Octaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, Octaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Octaves_MetaData), NewProp_Octaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Persistence = { "Persistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, Persistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Persistence_MetaData), NewProp_Persistence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Lacunarity = { "Lacunarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, Lacunarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Lacunarity_MetaData), NewProp_Lacunarity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Seed = { "Seed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTerrainParams, Seed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Seed_MetaData), NewProp_Seed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_BaseHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_HeightVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_NoiseFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_NoiseAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Octaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Persistence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Lacunarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewProp_Seed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronTerrainParams",
	Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::PropPointers),
	sizeof(FAuracronTerrainParams),
	alignof(FAuracronTerrainParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTerrainParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTerrainParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTerrainParams **********************************************

// ********** Begin ScriptStruct FAuracronStructurePlacementRule ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule;
class UScriptStruct* FAuracronStructurePlacementRule::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronStructurePlacementRule, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronStructurePlacementRule"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Structure placement rules\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure placement rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureType_MetaData[] = {
		{ "Category", "Structure" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Structure type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSlope_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum slope for placement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum slope for placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSlope_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum slope for placement */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum slope for placement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinWaterDistance_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum distance from water */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum distance from water" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWaterDistance_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum distance from water */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum distance from water" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredBiomes_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required biome types */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required biome types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ForbiddenBiomes_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Forbidden biome types */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Forbidden biome types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinStructureDistance_MetaData[] = {
		{ "Category", "Spacing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum distance from other structures */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum distance from other structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementProbability_MetaData[] = {
		{ "Category", "Probability" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Placement probability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement probability" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StructureType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxSlope;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinWaterDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWaterDistance;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredBiomes;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ForbiddenBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ForbiddenBiomes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinStructureDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlacementProbability;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronStructurePlacementRule>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_StructureType = { "StructureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, StructureType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureType_MetaData), NewProp_StructureType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinSlope = { "MinSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, MinSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSlope_MetaData), NewProp_MinSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MaxSlope = { "MaxSlope", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, MaxSlope), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSlope_MetaData), NewProp_MaxSlope_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinWaterDistance = { "MinWaterDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, MinWaterDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinWaterDistance_MetaData), NewProp_MinWaterDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MaxWaterDistance = { "MaxWaterDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, MaxWaterDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWaterDistance_MetaData), NewProp_MaxWaterDistance_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_RequiredBiomes_Inner = { "RequiredBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_RequiredBiomes = { "RequiredBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, RequiredBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredBiomes_MetaData), NewProp_RequiredBiomes_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_ForbiddenBiomes_Inner = { "ForbiddenBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_ForbiddenBiomes = { "ForbiddenBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, ForbiddenBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ForbiddenBiomes_MetaData), NewProp_ForbiddenBiomes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinStructureDistance = { "MinStructureDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, MinStructureDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinStructureDistance_MetaData), NewProp_MinStructureDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_PlacementProbability = { "PlacementProbability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronStructurePlacementRule, PlacementProbability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementProbability_MetaData), NewProp_PlacementProbability_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_StructureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MaxSlope,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinWaterDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MaxWaterDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_RequiredBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_RequiredBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_ForbiddenBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_ForbiddenBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_MinStructureDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewProp_PlacementProbability,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronStructurePlacementRule",
	Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::PropPointers),
	sizeof(FAuracronStructurePlacementRule),
	alignof(FAuracronStructurePlacementRule),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronStructurePlacementRule()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.InnerSingleton, Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule.InnerSingleton;
}
// ********** End ScriptStruct FAuracronStructurePlacementRule *************************************

// ********** Begin ScriptStruct FAuracronResourceDistribution *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution;
class UScriptStruct* FAuracronResourceDistribution::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronResourceDistribution, (UObject*)Z_Construct_UPackage__Script_AuracronPCGBridge(), TEXT("AuracronResourceDistribution"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Resource distribution data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource distribution data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceType_MetaData[] = {
		{ "Category", "Resource" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseRarity_MetaData[] = {
		{ "Category", "Rarity" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.001" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base rarity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base rarity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DepthRange_MetaData[] = {
		{ "Category", "Depth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Depth preference */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Depth preference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeModifiers_MetaData[] = {
		{ "Category", "Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome modifiers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome modifiers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterSize_MetaData[] = {
		{ "Category", "Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cluster parameters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cluster parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "Clustering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cluster radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cluster radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityRange_MetaData[] = {
		{ "Category", "Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "Gameplay" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Respawn time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Respawn time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseRarity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DepthRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BiomeModifiers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeModifiers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BiomeModifiers;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ClusterSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_QualityRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronResourceDistribution>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, ResourceType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceType_MetaData), NewProp_ResourceType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BaseRarity = { "BaseRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, BaseRarity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseRarity_MetaData), NewProp_BaseRarity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_DepthRange = { "DepthRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, DepthRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DepthRange_MetaData), NewProp_DepthRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers_ValueProp = { "BiomeModifiers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers_Key_KeyProp = { "BiomeModifiers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers = { "BiomeModifiers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, BiomeModifiers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeModifiers_MetaData), NewProp_BiomeModifiers_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ClusterSize = { "ClusterSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, ClusterSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterSize_MetaData), NewProp_ClusterSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_QualityRange = { "QualityRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, QualityRange), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityRange_MetaData), NewProp_QualityRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronResourceDistribution, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ResourceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BaseRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_DepthRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_BiomeModifiers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ClusterSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_QualityRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewProp_RespawnTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
	nullptr,
	&NewStructOps,
	"AuracronResourceDistribution",
	Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::PropPointers),
	sizeof(FAuracronResourceDistribution),
	alignof(FAuracronResourceDistribution),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronResourceDistribution()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.InnerSingleton, Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution.InnerSingleton;
}
// ********** End ScriptStruct FAuracronResourceDistribution ***************************************

// ********** Begin Class UAuracronMasterPCGSettings ***********************************************
void UAuracronMasterPCGSettings::StaticRegisterNativesUAuracronMasterPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronMasterPCGSettings;
UClass* UAuracronMasterPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronMasterPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronMasterPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMasterPCGSettings"),
			Z_Registration_Info_UClass_UAuracronMasterPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronMasterPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronMasterPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronMasterPCGSettings_NoRegister()
{
	return UAuracronMasterPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronMasterPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Master" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Master PCG Settings\n * Coordinates all PCG generation systems\n */" },
#endif
		{ "IncludePath", "AuracronPCGAdvanced.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Master PCG Settings\nCoordinates all PCG generation systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldSeed_MetaData[] = {
		{ "Category", "World Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** World generation seed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World generation seed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldSize_MetaData[] = {
		{ "Category", "World Generation" },
		{ "ClampMax", "100.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** World size in kilometers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World size in kilometers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeConfigs_MetaData[] = {
		{ "Category", "Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Biome configurations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerrainParams_MetaData[] = {
		{ "Category", "Terrain" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Terrain generation parameters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Terrain generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureRules_MetaData[] = {
		{ "Category", "Structures" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Structure placement rules */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Structure placement rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceDistributions_MetaData[] = {
		{ "Category", "Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resource distributions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource distributions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableOptimization_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLODSystem_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** LOD system integration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD system integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseStreaming_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Streaming integration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "Quality" },
		{ "ClampMax", "5" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality level (1-5) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality level (1-5)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIntegrateAllBridges_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with all bridges */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with all bridges" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_WorldSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WorldSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeConfigs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BiomeConfigs;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TerrainParams;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StructureRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StructureRules;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResourceDistributions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ResourceDistributions;
	static void NewProp_bEnableOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableOptimization;
	static void NewProp_bUseLODSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLODSystem;
	static void NewProp_bUseStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseStreaming;
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static void NewProp_bIntegrateAllBridges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIntegrateAllBridges;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronMasterPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_WorldSeed = { "WorldSeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, WorldSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldSeed_MetaData), NewProp_WorldSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_WorldSize = { "WorldSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, WorldSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldSize_MetaData), NewProp_WorldSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_BiomeConfigs_Inner = { "BiomeConfigs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronBiomeConfig, METADATA_PARAMS(0, nullptr) }; // 2898563277
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_BiomeConfigs = { "BiomeConfigs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, BiomeConfigs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeConfigs_MetaData), NewProp_BiomeConfigs_MetaData) }; // 2898563277
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_TerrainParams = { "TerrainParams", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, TerrainParams), Z_Construct_UScriptStruct_FAuracronTerrainParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerrainParams_MetaData), NewProp_TerrainParams_MetaData) }; // 1090193251
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_StructureRules_Inner = { "StructureRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronStructurePlacementRule, METADATA_PARAMS(0, nullptr) }; // 3277351513
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_StructureRules = { "StructureRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, StructureRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureRules_MetaData), NewProp_StructureRules_MetaData) }; // 3277351513
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_ResourceDistributions_Inner = { "ResourceDistributions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronResourceDistribution, METADATA_PARAMS(0, nullptr) }; // 2974603928
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_ResourceDistributions = { "ResourceDistributions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, ResourceDistributions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceDistributions_MetaData), NewProp_ResourceDistributions_MetaData) }; // 2974603928
void Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bEnableOptimization_SetBit(void* Obj)
{
	((UAuracronMasterPCGSettings*)Obj)->bEnableOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bEnableOptimization = { "bEnableOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMasterPCGSettings), &Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bEnableOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableOptimization_MetaData), NewProp_bEnableOptimization_MetaData) };
void Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseLODSystem_SetBit(void* Obj)
{
	((UAuracronMasterPCGSettings*)Obj)->bUseLODSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseLODSystem = { "bUseLODSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMasterPCGSettings), &Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseLODSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLODSystem_MetaData), NewProp_bUseLODSystem_MetaData) };
void Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseStreaming_SetBit(void* Obj)
{
	((UAuracronMasterPCGSettings*)Obj)->bUseStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseStreaming = { "bUseStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMasterPCGSettings), &Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseStreaming_MetaData), NewProp_bUseStreaming_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronMasterPCGSettings, QualityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) };
void Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bIntegrateAllBridges_SetBit(void* Obj)
{
	((UAuracronMasterPCGSettings*)Obj)->bIntegrateAllBridges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bIntegrateAllBridges = { "bIntegrateAllBridges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronMasterPCGSettings), &Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bIntegrateAllBridges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIntegrateAllBridges_MetaData), NewProp_bIntegrateAllBridges_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_WorldSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_WorldSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_BiomeConfigs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_BiomeConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_TerrainParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_StructureRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_StructureRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_ResourceDistributions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_ResourceDistributions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bEnableOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseLODSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bUseStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::NewProp_bIntegrateAllBridges,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::ClassParams = {
	&UAuracronMasterPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronMasterPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronMasterPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronMasterPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronMasterPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronMasterPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronMasterPCGSettings);
UAuracronMasterPCGSettings::~UAuracronMasterPCGSettings() {}
// ********** End Class UAuracronMasterPCGSettings *************************************************

// ********** Begin Class UAuracronBiomeTransitionPCGSettings **************************************
void UAuracronBiomeTransitionPCGSettings::StaticRegisterNativesUAuracronBiomeTransitionPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings;
UClass* UAuracronBiomeTransitionPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronBiomeTransitionPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronBiomeTransitionPCGSettings"),
			Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronBiomeTransitionPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_NoRegister()
{
	return UAuracronBiomeTransitionPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Biome" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Biome Transition PCG Settings\n * Handles smooth transitions between different biomes\n */" },
#endif
		{ "IncludePath", "AuracronPCGAdvanced.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Biome Transition PCG Settings\nHandles smooth transitions between different biomes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionWidth_MetaData[] = {
		{ "Category", "Transition" },
		{ "ClampMax", "2000.0" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition width in meters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition width in meters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSmoothness_MetaData[] = {
		{ "Category", "Transition" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition smoothness */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition smoothness" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceBiome_MetaData[] = {
		{ "Category", "Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Source biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Source biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetBiome_MetaData[] = {
		{ "Category", "Biomes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target biome */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target biome" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlendVegetation_MetaData[] = {
		{ "Category", "Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blend vegetation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend vegetation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlendTerrain_MetaData[] = {
		{ "Category", "Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blend terrain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend terrain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBlendResources_MetaData[] = {
		{ "Category", "Blending" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blend resources */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend resources" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSmoothness;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SourceBiome;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TargetBiome;
	static void NewProp_bBlendVegetation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlendVegetation;
	static void NewProp_bBlendTerrain_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlendTerrain;
	static void NewProp_bBlendResources_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBlendResources;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronBiomeTransitionPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TransitionWidth = { "TransitionWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomeTransitionPCGSettings, TransitionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionWidth_MetaData), NewProp_TransitionWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TransitionSmoothness = { "TransitionSmoothness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomeTransitionPCGSettings, TransitionSmoothness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSmoothness_MetaData), NewProp_TransitionSmoothness_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_SourceBiome = { "SourceBiome", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomeTransitionPCGSettings, SourceBiome), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceBiome_MetaData), NewProp_SourceBiome_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TargetBiome = { "TargetBiome", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronBiomeTransitionPCGSettings, TargetBiome), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetBiome_MetaData), NewProp_TargetBiome_MetaData) };
void Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendVegetation_SetBit(void* Obj)
{
	((UAuracronBiomeTransitionPCGSettings*)Obj)->bBlendVegetation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendVegetation = { "bBlendVegetation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronBiomeTransitionPCGSettings), &Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendVegetation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlendVegetation_MetaData), NewProp_bBlendVegetation_MetaData) };
void Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendTerrain_SetBit(void* Obj)
{
	((UAuracronBiomeTransitionPCGSettings*)Obj)->bBlendTerrain = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendTerrain = { "bBlendTerrain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronBiomeTransitionPCGSettings), &Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendTerrain_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlendTerrain_MetaData), NewProp_bBlendTerrain_MetaData) };
void Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendResources_SetBit(void* Obj)
{
	((UAuracronBiomeTransitionPCGSettings*)Obj)->bBlendResources = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendResources = { "bBlendResources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronBiomeTransitionPCGSettings), &Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendResources_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBlendResources_MetaData), NewProp_bBlendResources_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TransitionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TransitionSmoothness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_SourceBiome,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_TargetBiome,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendVegetation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendTerrain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::NewProp_bBlendResources,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::ClassParams = {
	&UAuracronBiomeTransitionPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronBiomeTransitionPCGSettings);
UAuracronBiomeTransitionPCGSettings::~UAuracronBiomeTransitionPCGSettings() {}
// ********** End Class UAuracronBiomeTransitionPCGSettings ****************************************

// ********** Begin Class UAuracronQuestPCGSettings ************************************************
void UAuracronQuestPCGSettings::StaticRegisterNativesUAuracronQuestPCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronQuestPCGSettings;
UClass* UAuracronQuestPCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronQuestPCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronQuestPCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronQuestPCGSettings"),
			Z_Registration_Info_UClass_UAuracronQuestPCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronQuestPCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronQuestPCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronQuestPCGSettings_NoRegister()
{
	return UAuracronQuestPCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronQuestPCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Quest" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Quest Integration PCG Settings\n * Generates quest-related content and objectives\n */" },
#endif
		{ "IncludePath", "AuracronPCGAdvanced.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Quest Integration PCG Settings\nGenerates quest-related content and objectives" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QuestType_MetaData[] = {
		{ "Category", "Quest" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quest type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quest type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "Quest" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Difficulty level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Difficulty level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCount_MetaData[] = {
		{ "Category", "Objectives" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective count */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective count" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardTier_MetaData[] = {
		{ "Category", "Rewards" },
		{ "ClampMax", "5" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reward tier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reward tier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredBiomes_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required biomes for quest */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required biomes for quest" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QuestDuration_MetaData[] = {
		{ "Category", "Duration" },
		{ "ClampMax", "180.0" },
		{ "ClampMin", "5.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quest duration in minutes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quest duration in minutes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseProgressionBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Progression Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Progression Bridge" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLoreBridge_MetaData[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Lore Bridge */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Lore Bridge" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_QuestType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DifficultyLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewardTier;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredBiomes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredBiomes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QuestDuration;
	static void NewProp_bUseProgressionBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseProgressionBridge;
	static void NewProp_bUseLoreBridge_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLoreBridge;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronQuestPCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_QuestType = { "QuestType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, QuestType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QuestType_MetaData), NewProp_QuestType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, DifficultyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_ObjectiveCount = { "ObjectiveCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, ObjectiveCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCount_MetaData), NewProp_ObjectiveCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RewardTier = { "RewardTier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, RewardTier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardTier_MetaData), NewProp_RewardTier_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RequiredBiomes_Inner = { "RequiredBiomes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RequiredBiomes = { "RequiredBiomes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, RequiredBiomes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredBiomes_MetaData), NewProp_RequiredBiomes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_QuestDuration = { "QuestDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronQuestPCGSettings, QuestDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QuestDuration_MetaData), NewProp_QuestDuration_MetaData) };
void Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseProgressionBridge_SetBit(void* Obj)
{
	((UAuracronQuestPCGSettings*)Obj)->bUseProgressionBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseProgressionBridge = { "bUseProgressionBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronQuestPCGSettings), &Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseProgressionBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseProgressionBridge_MetaData), NewProp_bUseProgressionBridge_MetaData) };
void Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseLoreBridge_SetBit(void* Obj)
{
	((UAuracronQuestPCGSettings*)Obj)->bUseLoreBridge = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseLoreBridge = { "bUseLoreBridge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronQuestPCGSettings), &Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseLoreBridge_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLoreBridge_MetaData), NewProp_bUseLoreBridge_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_QuestType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_ObjectiveCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RewardTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RequiredBiomes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_RequiredBiomes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_QuestDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseProgressionBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::NewProp_bUseLoreBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::ClassParams = {
	&UAuracronQuestPCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronQuestPCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronQuestPCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronQuestPCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronQuestPCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronQuestPCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronQuestPCGSettings);
UAuracronQuestPCGSettings::~UAuracronQuestPCGSettings() {}
// ********** End Class UAuracronQuestPCGSettings **************************************************

// ********** Begin Class UAuracronPerformancePCGSettings ******************************************
void UAuracronPerformancePCGSettings::StaticRegisterNativesUAuracronPerformancePCGSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPerformancePCGSettings;
UClass* UAuracronPerformancePCGSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPerformancePCGSettings;
	if (!Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPerformancePCGSettings"),
			Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPerformancePCGSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings_NoRegister()
{
	return UAuracronPerformancePCGSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Performance Optimization PCG Settings\n * Optimizes PCG generation for performance\n */" },
#endif
		{ "IncludePath", "AuracronPCGAdvanced.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Performance Optimization PCG Settings\nOptimizes PCG generation for performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGPUAcceleration_MetaData[] = {
		{ "Category", "GPU" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable GPU acceleration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable GPU acceleration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGenerationTimePerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "33.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum generation time per frame (ms) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum generation time per frame (ms)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistances_MetaData[] = {
		{ "Category", "LOD" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** LOD distance multipliers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD distance multipliers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingDistance_MetaData[] = {
		{ "Category", "Culling" },
		{ "ClampMax", "50000.0" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Culling distance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling distance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingChunkSize_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ClampMax", "5000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Streaming chunk size */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming chunk size" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheSizeMB_MetaData[] = {
		{ "Category", "Cache" },
		{ "ClampMax", "2048" },
		{ "ClampMin", "100" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache size in MB */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache size in MB" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMultithreading_MetaData[] = {
		{ "Category", "Threading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable multithreading */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable multithreading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThreadCount_MetaData[] = {
		{ "Category", "Threading" },
		{ "ClampMax", "32" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Thread count (0 = auto) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGAdvanced.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Thread count (0 = auto)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bUseGPUAcceleration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGPUAcceleration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxGenerationTimePerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistances_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LODDistances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingChunkSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheSizeMB;
	static void NewProp_bUseMultithreading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMultithreading;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ThreadCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPerformancePCGSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseGPUAcceleration_SetBit(void* Obj)
{
	((UAuracronPerformancePCGSettings*)Obj)->bUseGPUAcceleration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseGPUAcceleration = { "bUseGPUAcceleration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPerformancePCGSettings), &Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseGPUAcceleration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGPUAcceleration_MetaData), NewProp_bUseGPUAcceleration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_MaxGenerationTimePerFrame = { "MaxGenerationTimePerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, MaxGenerationTimePerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGenerationTimePerFrame_MetaData), NewProp_MaxGenerationTimePerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_LODDistances_Inner = { "LODDistances", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_LODDistances = { "LODDistances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, LODDistances), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistances_MetaData), NewProp_LODDistances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_CullingDistance = { "CullingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, CullingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingDistance_MetaData), NewProp_CullingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_StreamingChunkSize = { "StreamingChunkSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, StreamingChunkSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingChunkSize_MetaData), NewProp_StreamingChunkSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_CacheSizeMB = { "CacheSizeMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, CacheSizeMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheSizeMB_MetaData), NewProp_CacheSizeMB_MetaData) };
void Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseMultithreading_SetBit(void* Obj)
{
	((UAuracronPerformancePCGSettings*)Obj)->bUseMultithreading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseMultithreading = { "bUseMultithreading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronPerformancePCGSettings), &Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseMultithreading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMultithreading_MetaData), NewProp_bUseMultithreading_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_ThreadCount = { "ThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPerformancePCGSettings, ThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThreadCount_MetaData), NewProp_ThreadCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseGPUAcceleration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_MaxGenerationTimePerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_LODDistances_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_LODDistances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_CullingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_StreamingChunkSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_CacheSizeMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_bUseMultithreading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::NewProp_ThreadCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::ClassParams = {
	&UAuracronPerformancePCGSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPerformancePCGSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.OuterSingleton, Z_Construct_UClass_UAuracronPerformancePCGSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPerformancePCGSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPerformancePCGSettings);
UAuracronPerformancePCGSettings::~UAuracronPerformancePCGSettings() {}
// ********** End Class UAuracronPerformancePCGSettings ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronBiomeConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronBiomeConfig_Statics::NewStructOps, TEXT("AuracronBiomeConfig"), &Z_Registration_Info_UScriptStruct_FAuracronBiomeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBiomeConfig), 2898563277U) },
		{ FAuracronTerrainParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronTerrainParams_Statics::NewStructOps, TEXT("AuracronTerrainParams"), &Z_Registration_Info_UScriptStruct_FAuracronTerrainParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTerrainParams), 1090193251U) },
		{ FAuracronStructurePlacementRule::StaticStruct, Z_Construct_UScriptStruct_FAuracronStructurePlacementRule_Statics::NewStructOps, TEXT("AuracronStructurePlacementRule"), &Z_Registration_Info_UScriptStruct_FAuracronStructurePlacementRule, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronStructurePlacementRule), 3277351513U) },
		{ FAuracronResourceDistribution::StaticStruct, Z_Construct_UScriptStruct_FAuracronResourceDistribution_Statics::NewStructOps, TEXT("AuracronResourceDistribution"), &Z_Registration_Info_UScriptStruct_FAuracronResourceDistribution, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronResourceDistribution), 2974603928U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronMasterPCGSettings, UAuracronMasterPCGSettings::StaticClass, TEXT("UAuracronMasterPCGSettings"), &Z_Registration_Info_UClass_UAuracronMasterPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronMasterPCGSettings), 1861474285U) },
		{ Z_Construct_UClass_UAuracronBiomeTransitionPCGSettings, UAuracronBiomeTransitionPCGSettings::StaticClass, TEXT("UAuracronBiomeTransitionPCGSettings"), &Z_Registration_Info_UClass_UAuracronBiomeTransitionPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronBiomeTransitionPCGSettings), 1701597624U) },
		{ Z_Construct_UClass_UAuracronQuestPCGSettings, UAuracronQuestPCGSettings::StaticClass, TEXT("UAuracronQuestPCGSettings"), &Z_Registration_Info_UClass_UAuracronQuestPCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronQuestPCGSettings), 4109917318U) },
		{ Z_Construct_UClass_UAuracronPerformancePCGSettings, UAuracronPerformancePCGSettings::StaticClass, TEXT("UAuracronPerformancePCGSettings"), &Z_Registration_Info_UClass_UAuracronPerformancePCGSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPerformancePCGSettings), 3197605931U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_1676784798(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGAdvanced_h__Script_AuracronPCGBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
