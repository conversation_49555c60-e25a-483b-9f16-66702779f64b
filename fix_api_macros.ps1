# Script para corrigir todos os AURACRONPCGFRAMEWORK_API para AURACRONPCGBRIDGE_API
# Executa em todos os arquivos .h e .cpp do projeto AuracronPCGBridge

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao de AURACRONPCGFRAMEWORK_API para AURACRONPCGBRIDGE_API..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Encontra todos os arquivos .h e .cpp
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h", "*.cpp" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalReplacements = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Lê o conteúdo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    if ($content -match "AURACRONPCGFRAMEWORK_API") {
        # Conta quantas ocorrências existem
        $matches = [regex]::Matches($content, "AURACRONPCGFRAMEWORK_API")
        $count = $matches.Count
        
        Write-Host "  Encontradas $count ocorrencias de AURACRONPCGFRAMEWORK_API" -ForegroundColor Yellow

        # Substitui todas as ocorrencias
        $newContent = $content -replace "AURACRONPCGFRAMEWORK_API", "AURACRONPCGBRIDGE_API"

        # Salva o arquivo com a codificacao original
        Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8 -NoNewline

        $totalReplacements += $count
        $filesModified++

        Write-Host "  OK Substituidas $count ocorrencias" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma ocorrencia encontrada" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de substituicoes: $totalReplacements" -ForegroundColor Green

if ($totalReplacements -gt 0) {
    Write-Host "`nCorrecao concluida com sucesso!" -ForegroundColor Green
    Write-Host "Todos os AURACRONPCGFRAMEWORK_API foram substituidos por AURACRONPCGBRIDGE_API" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma correcao necessaria." -ForegroundColor Yellow
}

# Lista os arquivos modificados
if ($filesModified -gt 0) {
    Write-Host "`nArquivos modificados:" -ForegroundColor Cyan
    foreach ($file in $files) {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        if ($content -match "AURACRONPCGBRIDGE_API" -and $content -notmatch "AURACRONPCGFRAMEWORK_API") {
            $relativePath = $file.FullName.Replace($ProjectPath, "").TrimStart('\')
            Write-Host "  - $relativePath" -ForegroundColor White
        }
    }
}

Write-Host "`nScript concluido!" -ForegroundColor Green
