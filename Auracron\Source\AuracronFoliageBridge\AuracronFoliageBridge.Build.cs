using UnrealBuildTool;
public class AuracronFoliageBridge : ModuleRules
{
    public AuracronFoliageBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "Foliage",
                "Landscape",
                "RenderCore",
                "RHI",
                "StaticMeshDescription",
                "MeshDescription",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework",
                "InteractiveToolsFramework",
                "ModelingComponents",
                "ModelingOperators",
                "MeshConversion",
                "PCG",
                "PCGGeometryScriptInterop",
                "AuracronPCGBridge",

                "Foliage",
                "ChaosCore",
                "PhysicsCore",
                "AudioMixer",
                "NiagaraCore",
                "NiagaraShader",
                "ImageWrapper",
                "ImageCore"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "Slate",
                "SlateCore",
                "EngineSettings",
                "GameplayTags",
                "GameplayTasks",
                "GameplayAbilities",
                "NavigationSystem",
                "Engine",
                "DeveloperSettings",
                "MeshUtilitiesCommon",
                "RawMesh",

                "SkeletalMeshUtilitiesCommon",
                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeCommon",
                "PhysicsCore",
                "ChaosCore"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "AssetTools",
                    "ComponentVisualizers",
                    "ContentBrowser",
                    "DetailCustomizations",
                    "EditorStyle",
                    "EditorSubsystem",
                    "EditorWidgets",
                    "LevelEditor",
                    "MeshBuilder",
                    "PropertyEditor",
                    "SourceControl",
                    "ToolMenus",
                    "UnrealEd"
                }
            );
        }
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "LandscapeEditor",
                "EditorInteractiveToolsFramework",
                "MaterialUtilities",
                "MainFrame",
                "SourceControlWindows",
                "ContentBrowserData"
            });
        }
        // Enable RTTI for Python integration
        bUseRTTI = true;
        // Enable exceptions for Python integration
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Platform specific settings
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_WINDOWS=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Android)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_ANDROID=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_PLATFORM_IOS=1");
        }
        // Foliage specific definitions
        PublicDefinitions.Add("WITH_FOLIAGE=1");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_FOLIAGE_BRIDGE_VERSION_PATCH=0");
    }
}




