// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronAdvancedPerformanceAnalyzer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdvancedPerformanceAnalyzer() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPerformanceAnalysisCategory **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPerformanceAnalysisCategory;
static UEnum* EPerformanceAnalysisCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPerformanceAnalysisCategory"));
	}
	return Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPerformanceAnalysisCategory>()
{
	return EPerformanceAnalysisCategory_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AI.DisplayName", "AI Performance" },
		{ "AI.Name", "EPerformanceAnalysisCategory::AI" },
		{ "Audio.DisplayName", "Audio Performance" },
		{ "Audio.Name", "EPerformanceAnalysisCategory::Audio" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance analysis categories\n */" },
#endif
		{ "CPU.DisplayName", "CPU Performance" },
		{ "CPU.Name", "EPerformanceAnalysisCategory::CPU" },
		{ "GameThread.DisplayName", "Game Thread Performance" },
		{ "GameThread.Name", "EPerformanceAnalysisCategory::GameThread" },
		{ "GPU.DisplayName", "GPU Performance" },
		{ "GPU.Name", "EPerformanceAnalysisCategory::GPU" },
		{ "Memory.DisplayName", "Memory Usage" },
		{ "Memory.Name", "EPerformanceAnalysisCategory::Memory" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
		{ "Network.DisplayName", "Network Performance" },
		{ "Network.Name", "EPerformanceAnalysisCategory::Network" },
		{ "Overall.DisplayName", "Overall Performance" },
		{ "Overall.Name", "EPerformanceAnalysisCategory::Overall" },
		{ "Physics.DisplayName", "Physics Performance" },
		{ "Physics.Name", "EPerformanceAnalysisCategory::Physics" },
		{ "Rendering.DisplayName", "Rendering Performance" },
		{ "Rendering.Name", "EPerformanceAnalysisCategory::Rendering" },
		{ "RenderThread.DisplayName", "Render Thread Performance" },
		{ "RenderThread.Name", "EPerformanceAnalysisCategory::RenderThread" },
		{ "Streaming.DisplayName", "Streaming Performance" },
		{ "Streaming.Name", "EPerformanceAnalysisCategory::Streaming" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance analysis categories" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPerformanceAnalysisCategory::CPU", (int64)EPerformanceAnalysisCategory::CPU },
		{ "EPerformanceAnalysisCategory::GPU", (int64)EPerformanceAnalysisCategory::GPU },
		{ "EPerformanceAnalysisCategory::Memory", (int64)EPerformanceAnalysisCategory::Memory },
		{ "EPerformanceAnalysisCategory::Network", (int64)EPerformanceAnalysisCategory::Network },
		{ "EPerformanceAnalysisCategory::Rendering", (int64)EPerformanceAnalysisCategory::Rendering },
		{ "EPerformanceAnalysisCategory::Audio", (int64)EPerformanceAnalysisCategory::Audio },
		{ "EPerformanceAnalysisCategory::Physics", (int64)EPerformanceAnalysisCategory::Physics },
		{ "EPerformanceAnalysisCategory::AI", (int64)EPerformanceAnalysisCategory::AI },
		{ "EPerformanceAnalysisCategory::Streaming", (int64)EPerformanceAnalysisCategory::Streaming },
		{ "EPerformanceAnalysisCategory::GameThread", (int64)EPerformanceAnalysisCategory::GameThread },
		{ "EPerformanceAnalysisCategory::RenderThread", (int64)EPerformanceAnalysisCategory::RenderThread },
		{ "EPerformanceAnalysisCategory::Overall", (int64)EPerformanceAnalysisCategory::Overall },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPerformanceAnalysisCategory",
	"EPerformanceAnalysisCategory",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory()
{
	if (!Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPerformanceAnalysisCategory.InnerSingleton;
}
// ********** End Enum EPerformanceAnalysisCategory ************************************************

// ********** Begin Enum EPerformanceBottleneckType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPerformanceBottleneckType;
static UEnum* EPerformanceBottleneckType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPerformanceBottleneckType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPerformanceBottleneckType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPerformanceBottleneckType"));
	}
	return Z_Registration_Info_UEnum_EPerformanceBottleneckType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPerformanceBottleneckType>()
{
	return EPerformanceBottleneckType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AssetLoading.DisplayName", "Asset Loading" },
		{ "AssetLoading.Name", "EPerformanceBottleneckType::AssetLoading" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance bottleneck types\n */" },
#endif
		{ "CPUBound.DisplayName", "CPU Bound" },
		{ "CPUBound.Name", "EPerformanceBottleneckType::CPUBound" },
		{ "GarbageCollection.DisplayName", "Garbage Collection" },
		{ "GarbageCollection.Name", "EPerformanceBottleneckType::GarbageCollection" },
		{ "GPUBound.DisplayName", "GPU Bound" },
		{ "GPUBound.Name", "EPerformanceBottleneckType::GPUBound" },
		{ "IOBound.DisplayName", "I/O Bound" },
		{ "IOBound.Name", "EPerformanceBottleneckType::IOBound" },
		{ "MemoryBound.DisplayName", "Memory Bound" },
		{ "MemoryBound.Name", "EPerformanceBottleneckType::MemoryBound" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
		{ "NetworkBound.DisplayName", "Network Bound" },
		{ "NetworkBound.Name", "EPerformanceBottleneckType::NetworkBound" },
		{ "ShaderCompilation.DisplayName", "Shader Compilation" },
		{ "ShaderCompilation.Name", "EPerformanceBottleneckType::ShaderCompilation" },
		{ "ThreadContention.DisplayName", "Thread Contention" },
		{ "ThreadContention.Name", "EPerformanceBottleneckType::ThreadContention" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance bottleneck types" },
#endif
		{ "Unknown.DisplayName", "Unknown" },
		{ "Unknown.Name", "EPerformanceBottleneckType::Unknown" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPerformanceBottleneckType::CPUBound", (int64)EPerformanceBottleneckType::CPUBound },
		{ "EPerformanceBottleneckType::GPUBound", (int64)EPerformanceBottleneckType::GPUBound },
		{ "EPerformanceBottleneckType::MemoryBound", (int64)EPerformanceBottleneckType::MemoryBound },
		{ "EPerformanceBottleneckType::NetworkBound", (int64)EPerformanceBottleneckType::NetworkBound },
		{ "EPerformanceBottleneckType::IOBound", (int64)EPerformanceBottleneckType::IOBound },
		{ "EPerformanceBottleneckType::ThreadContention", (int64)EPerformanceBottleneckType::ThreadContention },
		{ "EPerformanceBottleneckType::GarbageCollection", (int64)EPerformanceBottleneckType::GarbageCollection },
		{ "EPerformanceBottleneckType::AssetLoading", (int64)EPerformanceBottleneckType::AssetLoading },
		{ "EPerformanceBottleneckType::ShaderCompilation", (int64)EPerformanceBottleneckType::ShaderCompilation },
		{ "EPerformanceBottleneckType::Unknown", (int64)EPerformanceBottleneckType::Unknown },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPerformanceBottleneckType",
	"EPerformanceBottleneckType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType()
{
	if (!Z_Registration_Info_UEnum_EPerformanceBottleneckType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPerformanceBottleneckType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPerformanceBottleneckType.InnerSingleton;
}
// ********** End Enum EPerformanceBottleneckType **************************************************

// ********** Begin Enum EPerformanceOptimizationStrategy ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy;
static UEnum* EPerformanceOptimizationStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPerformanceOptimizationStrategy"));
	}
	return Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPerformanceOptimizationStrategy>()
{
	return EPerformanceOptimizationStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EPerformanceOptimizationStrategy::Adaptive" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EPerformanceOptimizationStrategy::Aggressive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "EPerformanceOptimizationStrategy::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance optimization strategies\n */" },
#endif
		{ "Conservative.DisplayName", "Conservative" },
		{ "Conservative.Name", "EPerformanceOptimizationStrategy::Conservative" },
		{ "MachineLearning.DisplayName", "Machine Learning" },
		{ "MachineLearning.Name", "EPerformanceOptimizationStrategy::MachineLearning" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance optimization strategies" },
#endif
		{ "UserDefined.DisplayName", "User Defined" },
		{ "UserDefined.Name", "EPerformanceOptimizationStrategy::UserDefined" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPerformanceOptimizationStrategy::Conservative", (int64)EPerformanceOptimizationStrategy::Conservative },
		{ "EPerformanceOptimizationStrategy::Balanced", (int64)EPerformanceOptimizationStrategy::Balanced },
		{ "EPerformanceOptimizationStrategy::Aggressive", (int64)EPerformanceOptimizationStrategy::Aggressive },
		{ "EPerformanceOptimizationStrategy::Adaptive", (int64)EPerformanceOptimizationStrategy::Adaptive },
		{ "EPerformanceOptimizationStrategy::MachineLearning", (int64)EPerformanceOptimizationStrategy::MachineLearning },
		{ "EPerformanceOptimizationStrategy::UserDefined", (int64)EPerformanceOptimizationStrategy::UserDefined },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPerformanceOptimizationStrategy",
	"EPerformanceOptimizationStrategy",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy()
{
	if (!Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy.InnerSingleton;
}
// ********** End Enum EPerformanceOptimizationStrategy ********************************************

// ********** Begin ScriptStruct FAuracronAdvancedPerformanceMetrics *******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics;
class UScriptStruct* FAuracronAdvancedPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronAdvancedPerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Advanced performance metrics\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFPS_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame rate metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame rate metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFPS_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFPS_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFPS_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameThreadTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frame time metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frame time metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderThreadTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsedMemoryMB_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableMemoryMB_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CPUUsagePercent_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** CPU metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "CPU metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveThreadCount_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkLatency_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Network metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PacketLoss_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rendering metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SetPassCalls_MetaData[] = {
		{ "Category", "Performance Metrics" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameThreadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderThreadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UsedMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AvailableMemoryMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CPUUsagePercent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveThreadCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NetworkLatency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PacketLoss;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SetPassCalls;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAdvancedPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, CurrentFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFPS_MetaData), NewProp_CurrentFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_AverageFPS = { "AverageFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, AverageFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFPS_MetaData), NewProp_AverageFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_MinFPS = { "MinFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, MinFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFPS_MetaData), NewProp_MinFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_MaxFPS = { "MaxFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, MaxFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFPS_MetaData), NewProp_MaxFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GameThreadTime = { "GameThreadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, GameThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameThreadTime_MetaData), NewProp_GameThreadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_RenderThreadTime = { "RenderThreadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, RenderThreadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderThreadTime_MetaData), NewProp_RenderThreadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GPUTime = { "GPUTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, GPUTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUTime_MetaData), NewProp_GPUTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_UsedMemoryMB = { "UsedMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, UsedMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsedMemoryMB_MetaData), NewProp_UsedMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_AvailableMemoryMB = { "AvailableMemoryMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, AvailableMemoryMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableMemoryMB_MetaData), NewProp_AvailableMemoryMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GPUMemoryUsageMB = { "GPUMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, GPUMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsageMB_MetaData), NewProp_GPUMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_CPUUsagePercent = { "CPUUsagePercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, CPUUsagePercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CPUUsagePercent_MetaData), NewProp_CPUUsagePercent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_ActiveThreadCount = { "ActiveThreadCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, ActiveThreadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveThreadCount_MetaData), NewProp_ActiveThreadCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_NetworkLatency = { "NetworkLatency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, NetworkLatency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkLatency_MetaData), NewProp_NetworkLatency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_PacketLoss = { "PacketLoss", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, PacketLoss), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PacketLoss_MetaData), NewProp_PacketLoss_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, Triangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_SetPassCalls = { "SetPassCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, SetPassCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SetPassCalls_MetaData), NewProp_SetPassCalls_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedPerformanceMetrics, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_CurrentFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_AverageFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_MinFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_MaxFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GameThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_RenderThreadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GPUTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_UsedMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_AvailableMemoryMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_GPUMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_CPUUsagePercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_ActiveThreadCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_NetworkLatency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_PacketLoss,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_SetPassCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronAdvancedPerformanceMetrics",
	Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::PropPointers),
	sizeof(FAuracronAdvancedPerformanceMetrics),
	alignof(FAuracronAdvancedPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAdvancedPerformanceMetrics *********************************

// ********** Begin ScriptStruct FAuracronPerformanceBottleneckAnalysis ****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis;
class UScriptStruct* FAuracronPerformanceBottleneckAnalysis::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronPerformanceBottleneckAnalysis"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance bottleneck analysis\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance bottleneck analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BottleneckType_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detected bottleneck type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detected bottleneck type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Severity_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bottleneck severity (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bottleneck severity (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectedCategory_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Affected performance category */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Affected performance category" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bottleneck description */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bottleneck description" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecommendedOptimizations_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recommended optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recommended optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedImpact_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estimated performance impact */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estimated performance impact" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionTime_MetaData[] = {
		{ "Category", "Bottleneck Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detection time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detection time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Severity;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectedCategory_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectedCategory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecommendedOptimizations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecommendedOptimizations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedImpact;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DetectionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceBottleneckAnalysis>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, BottleneckType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BottleneckType_MetaData), NewProp_BottleneckType_MetaData) }; // 234980083
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_Severity = { "Severity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, Severity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Severity_MetaData), NewProp_Severity_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_AffectedCategory_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_AffectedCategory = { "AffectedCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, AffectedCategory), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectedCategory_MetaData), NewProp_AffectedCategory_MetaData) }; // 3501850380
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_RecommendedOptimizations_Inner = { "RecommendedOptimizations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_RecommendedOptimizations = { "RecommendedOptimizations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, RecommendedOptimizations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecommendedOptimizations_MetaData), NewProp_RecommendedOptimizations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_EstimatedImpact = { "EstimatedImpact", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, EstimatedImpact), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedImpact_MetaData), NewProp_EstimatedImpact_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_DetectionTime = { "DetectionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceBottleneckAnalysis, DetectionTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionTime_MetaData), NewProp_DetectionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_BottleneckType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_Severity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_AffectedCategory_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_AffectedCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_RecommendedOptimizations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_RecommendedOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_EstimatedImpact,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewProp_DetectionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceBottleneckAnalysis",
	Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::PropPointers),
	sizeof(FAuracronPerformanceBottleneckAnalysis),
	alignof(FAuracronPerformanceBottleneckAnalysis),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceBottleneckAnalysis ******************************

// ********** Begin ScriptStruct FAuracronPerformanceOptimizationRecommendation ********************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation;
class UScriptStruct* FAuracronPerformanceOptimizationRecommendation::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronPerformanceOptimizationRecommendation"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Performance optimization recommendation\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance optimization recommendation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationID_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization ID */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strategy_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization strategy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetCategory_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target category */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target category" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization description */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization description" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpectedGain_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Expected performance gain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Expected performance gain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImplementationComplexity_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Implementation complexity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Implementation complexity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PriorityScore_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Priority score */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority score" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanAutoApply_MetaData[] = {
		{ "Category", "Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Auto-apply enabled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-apply enabled" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OptimizationID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Strategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Strategy;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetCategory_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetCategory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Description;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpectedGain;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ImplementationComplexity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PriorityScore;
	static void NewProp_bCanAutoApply_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanAutoApply;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPerformanceOptimizationRecommendation>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_OptimizationID = { "OptimizationID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, OptimizationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationID_MetaData), NewProp_OptimizationID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Strategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Strategy = { "Strategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, Strategy), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strategy_MetaData), NewProp_Strategy_MetaData) }; // 3314976456
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_TargetCategory_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_TargetCategory = { "TargetCategory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, TargetCategory), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetCategory_MetaData), NewProp_TargetCategory_MetaData) }; // 3501850380
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_ExpectedGain = { "ExpectedGain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, ExpectedGain), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpectedGain_MetaData), NewProp_ExpectedGain_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_ImplementationComplexity = { "ImplementationComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, ImplementationComplexity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImplementationComplexity_MetaData), NewProp_ImplementationComplexity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_PriorityScore = { "PriorityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPerformanceOptimizationRecommendation, PriorityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PriorityScore_MetaData), NewProp_PriorityScore_MetaData) };
void Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_bCanAutoApply_SetBit(void* Obj)
{
	((FAuracronPerformanceOptimizationRecommendation*)Obj)->bCanAutoApply = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_bCanAutoApply = { "bCanAutoApply", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronPerformanceOptimizationRecommendation), &Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_bCanAutoApply_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanAutoApply_MetaData), NewProp_bCanAutoApply_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_OptimizationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Strategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Strategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_TargetCategory_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_TargetCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_ExpectedGain,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_ImplementationComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_PriorityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewProp_bCanAutoApply,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronPerformanceOptimizationRecommendation",
	Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::PropPointers),
	sizeof(FAuracronPerformanceOptimizationRecommendation),
	alignof(FAuracronPerformanceOptimizationRecommendation),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPerformanceOptimizationRecommendation **********************

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function AnalyzePerformanceBottlenecks 
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventAnalyzePerformanceBottlenecks_Parms
	{
		TArray<FAuracronPerformanceBottleneckAnalysis> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bottleneck Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analyze performance bottlenecks */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze performance bottlenecks" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, METADATA_PARAMS(0, nullptr) }; // 965090622
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventAnalyzePerformanceBottlenecks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 965090622
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "AnalyzePerformanceBottlenecks", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::AuracronAdvancedPerformanceAnalyzer_eventAnalyzePerformanceBottlenecks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::AuracronAdvancedPerformanceAnalyzer_eventAnalyzePerformanceBottlenecks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execAnalyzePerformanceBottlenecks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceBottleneckAnalysis>*)Z_Param__Result=P_THIS->AnalyzePerformanceBottlenecks();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function AnalyzePerformanceBottlenecks 

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function ApplyOptimizationRecommendation 
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms
	{
		FString OptimizationID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply optimization recommendation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply optimization recommendation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OptimizationID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_OptimizationID = { "OptimizationID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms, OptimizationID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationID_MetaData), NewProp_OptimizationID_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms), &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_OptimizationID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "ApplyOptimizationRecommendation", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::AuracronAdvancedPerformanceAnalyzer_eventApplyOptimizationRecommendation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execApplyOptimizationRecommendation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OptimizationID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyOptimizationRecommendation(Z_Param_OptimizationID);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function ApplyOptimizationRecommendation 

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function AutoApplySafeOptimizations *
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventAutoApplySafeOptimizations_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Auto-apply safe optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-apply safe optimizations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventAutoApplySafeOptimizations_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "AutoApplySafeOptimizations", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::AuracronAdvancedPerformanceAnalyzer_eventAutoApplySafeOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::AuracronAdvancedPerformanceAnalyzer_eventAutoApplySafeOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execAutoApplySafeOptimizations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->AutoApplySafeOptimizations();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function AutoApplySafeOptimizations ***

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function GenerateOptimizationRecommendations 
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventGenerateOptimizationRecommendations_Parms
	{
		TArray<FAuracronPerformanceOptimizationRecommendation> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate optimization recommendations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate optimization recommendations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation, METADATA_PARAMS(0, nullptr) }; // 3796584243
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventGenerateOptimizationRecommendations_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3796584243
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "GenerateOptimizationRecommendations", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::AuracronAdvancedPerformanceAnalyzer_eventGenerateOptimizationRecommendations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::AuracronAdvancedPerformanceAnalyzer_eventGenerateOptimizationRecommendations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execGenerateOptimizationRecommendations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceOptimizationRecommendation>*)Z_Param__Result=P_THIS->GenerateOptimizationRecommendations();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function GenerateOptimizationRecommendations 

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function GetCurrentBottlenecks ******
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventGetCurrentBottlenecks_Parms
	{
		TArray<FAuracronPerformanceBottleneckAnalysis> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bottleneck Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current bottlenecks */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current bottlenecks" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, METADATA_PARAMS(0, nullptr) }; // 965090622
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventGetCurrentBottlenecks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 965090622
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "GetCurrentBottlenecks", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetCurrentBottlenecks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetCurrentBottlenecks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execGetCurrentBottlenecks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronPerformanceBottleneckAnalysis>*)Z_Param__Result=P_THIS->GetCurrentBottlenecks();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function GetCurrentBottlenecks ********

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function GetCurrentPerformanceMetrics 
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventGetCurrentPerformanceMetrics_Parms
	{
		FAuracronAdvancedPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventGetCurrentPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 2666824745
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "GetCurrentPerformanceMetrics", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetCurrentPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetCurrentPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execGetCurrentPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAdvancedPerformanceMetrics*)Z_Param__Result=P_THIS->GetCurrentPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function GetCurrentPerformanceMetrics *

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function GetPerformanceTrend ********
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventGetPerformanceTrend_Parms
	{
		EPerformanceAnalysisCategory Category;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Predictive Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get performance trend */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get performance trend" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventGetPerformanceTrend_Parms, Category), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory, METADATA_PARAMS(0, nullptr) }; // 3501850380
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventGetPerformanceTrend_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "GetPerformanceTrend", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetPerformanceTrend_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::AuracronAdvancedPerformanceAnalyzer_eventGetPerformanceTrend_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execGetPerformanceTrend)
{
	P_GET_ENUM(EPerformanceAnalysisCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPerformanceTrend(EPerformanceAnalysisCategory(Z_Param_Category));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function GetPerformanceTrend **********

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function HasBottleneckType **********
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms
	{
		EPerformanceBottleneckType BottleneckType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bottleneck Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check for specific bottleneck type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check for specific bottleneck type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BottleneckType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BottleneckType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_BottleneckType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_BottleneckType = { "BottleneckType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms, BottleneckType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceBottleneckType, METADATA_PARAMS(0, nullptr) }; // 234980083
void Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms), &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_BottleneckType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_BottleneckType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "HasBottleneckType", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::AuracronAdvancedPerformanceAnalyzer_eventHasBottleneckType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execHasBottleneckType)
{
	P_GET_ENUM(EPerformanceBottleneckType,Z_Param_BottleneckType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasBottleneckType(EPerformanceBottleneckType(Z_Param_BottleneckType));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function HasBottleneckType ************

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function InitializePerformanceAnalyzer 
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize performance analyzer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize performance analyzer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "InitializePerformanceAnalyzer", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execInitializePerformanceAnalyzer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePerformanceAnalyzer();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function InitializePerformanceAnalyzer 

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function OnBottleneckDetected *******
struct AuracronAdvancedPerformanceAnalyzer_eventOnBottleneckDetected_Parms
{
	FAuracronPerformanceBottleneckAnalysis Bottleneck;
};
static FName NAME_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected = FName(TEXT("OnBottleneckDetected"));
void UAuracronAdvancedPerformanceAnalyzer::OnBottleneckDetected(FAuracronPerformanceBottleneckAnalysis const& Bottleneck)
{
	AuracronAdvancedPerformanceAnalyzer_eventOnBottleneckDetected_Parms Parms;
	Parms.Bottleneck=Bottleneck;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when bottleneck is detected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when bottleneck is detected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Bottleneck_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Bottleneck;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::NewProp_Bottleneck = { "Bottleneck", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventOnBottleneckDetected_Parms, Bottleneck), Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Bottleneck_MetaData), NewProp_Bottleneck_MetaData) }; // 965090622
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::NewProp_Bottleneck,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "OnBottleneckDetected", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::PropPointers), sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnBottleneckDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnBottleneckDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function OnBottleneckDetected *********

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function OnOptimizationApplied ******
struct AuracronAdvancedPerformanceAnalyzer_eventOnOptimizationApplied_Parms
{
	FAuracronPerformanceOptimizationRecommendation Optimization;
};
static FName NAME_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied = FName(TEXT("OnOptimizationApplied"));
void UAuracronAdvancedPerformanceAnalyzer::OnOptimizationApplied(FAuracronPerformanceOptimizationRecommendation const& Optimization)
{
	AuracronAdvancedPerformanceAnalyzer_eventOnOptimizationApplied_Parms Parms;
	Parms.Optimization=Optimization;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when optimization is applied */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when optimization is applied" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Optimization_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Optimization;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::NewProp_Optimization = { "Optimization", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventOnOptimizationApplied_Parms, Optimization), Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Optimization_MetaData), NewProp_Optimization_MetaData) }; // 3796584243
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::NewProp_Optimization,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "OnOptimizationApplied", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::PropPointers), sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnOptimizationApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnOptimizationApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function OnOptimizationApplied ********

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function OnPerformanceThresholdExceeded 
struct AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms
{
	EPerformanceAnalysisCategory Category;
	float Value;
	float Threshold;
};
static FName NAME_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded = FName(TEXT("OnPerformanceThresholdExceeded"));
void UAuracronAdvancedPerformanceAnalyzer::OnPerformanceThresholdExceeded(EPerformanceAnalysisCategory Category, float Value, float Threshold)
{
	AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms Parms;
	Parms.Category=Category;
	Parms.Value=Value;
	Parms.Threshold=Threshold;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when performance threshold is exceeded */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when performance threshold is exceeded" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Threshold;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms, Category), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceAnalysisCategory, METADATA_PARAMS(0, nullptr) }; // 3501850380
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Threshold = { "Threshold", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms, Threshold), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::NewProp_Threshold,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "OnPerformanceThresholdExceeded", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::PropPointers), sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedPerformanceAnalyzer_eventOnPerformanceThresholdExceeded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function OnPerformanceThresholdExceeded 

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function PredictFuturePerformance ***
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventPredictFuturePerformance_Parms
	{
		float TimeHorizon;
		FAuracronAdvancedPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Predictive Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Predict future performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Predict future performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeHorizon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::NewProp_TimeHorizon = { "TimeHorizon", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventPredictFuturePerformance_Parms, TimeHorizon), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventPredictFuturePerformance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 2666824745
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::NewProp_TimeHorizon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "PredictFuturePerformance", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::AuracronAdvancedPerformanceAnalyzer_eventPredictFuturePerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::AuracronAdvancedPerformanceAnalyzer_eventPredictFuturePerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execPredictFuturePerformance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_TimeHorizon);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAdvancedPerformanceMetrics*)Z_Param__Result=P_THIS->PredictFuturePerformance(Z_Param_TimeHorizon);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function PredictFuturePerformance *****

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function PredictPerformanceImpact ***
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventPredictPerformanceImpact_Parms
	{
		FString ChangeDescription;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Predictive Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Predict performance impact of change */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Predict performance impact of change" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChangeDescription_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChangeDescription;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::NewProp_ChangeDescription = { "ChangeDescription", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventPredictPerformanceImpact_Parms, ChangeDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChangeDescription_MetaData), NewProp_ChangeDescription_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventPredictPerformanceImpact_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::NewProp_ChangeDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "PredictPerformanceImpact", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::AuracronAdvancedPerformanceAnalyzer_eventPredictPerformanceImpact_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::AuracronAdvancedPerformanceAnalyzer_eventPredictPerformanceImpact_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execPredictPerformanceImpact)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChangeDescription);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->PredictPerformanceImpact(Z_Param_ChangeDescription);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function PredictPerformanceImpact *****

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function SetAutoOptimizationEnabled *
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventSetAutoOptimizationEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable/disable auto-optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/disable auto-optimization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronAdvancedPerformanceAnalyzer_eventSetAutoOptimizationEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedPerformanceAnalyzer_eventSetAutoOptimizationEnabled_Parms), &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "SetAutoOptimizationEnabled", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetAutoOptimizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetAutoOptimizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execSetAutoOptimizationEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAutoOptimizationEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function SetAutoOptimizationEnabled ***

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function SetMonitoringFrequency *****
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventSetMonitoringFrequency_Parms
	{
		float Frequency;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set monitoring frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set monitoring frequency" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Frequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::NewProp_Frequency = { "Frequency", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventSetMonitoringFrequency_Parms, Frequency), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::NewProp_Frequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "SetMonitoringFrequency", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetMonitoringFrequency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetMonitoringFrequency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execSetMonitoringFrequency)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Frequency);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMonitoringFrequency(Z_Param_Frequency);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function SetMonitoringFrequency *******

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function SetOptimizationStrategy ****
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventSetOptimizationStrategy_Parms
	{
		EPerformanceOptimizationStrategy Strategy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set optimization strategy" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Strategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Strategy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::NewProp_Strategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::NewProp_Strategy = { "Strategy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventSetOptimizationStrategy_Parms, Strategy), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy, METADATA_PARAMS(0, nullptr) }; // 3314976456
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::NewProp_Strategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::NewProp_Strategy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "SetOptimizationStrategy", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetOptimizationStrategy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::AuracronAdvancedPerformanceAnalyzer_eventSetOptimizationStrategy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execSetOptimizationStrategy)
{
	P_GET_ENUM(EPerformanceOptimizationStrategy,Z_Param_Strategy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetOptimizationStrategy(EPerformanceOptimizationStrategy(Z_Param_Strategy));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function SetOptimizationStrategy ******

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function StartPerformanceMonitoring *
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Start performance monitoring */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "StartPerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execStartPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function StartPerformanceMonitoring ***

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function StopPerformanceMonitoring **
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Stop performance monitoring */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "StopPerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execStopPerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function StopPerformanceMonitoring ****

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer Function UpdatePerformanceAnalysis **
struct Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics
{
	struct AuracronAdvancedPerformanceAnalyzer_eventUpdatePerformanceAnalysis_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update performance analysis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update performance analysis" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPerformanceAnalyzer_eventUpdatePerformanceAnalysis_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, nullptr, "UpdatePerformanceAnalysis", Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::AuracronAdvancedPerformanceAnalyzer_eventUpdatePerformanceAnalysis_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::AuracronAdvancedPerformanceAnalyzer_eventUpdatePerformanceAnalysis_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedPerformanceAnalyzer::execUpdatePerformanceAnalysis)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceAnalysis(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer Function UpdatePerformanceAnalysis ****

// ********** Begin Class UAuracronAdvancedPerformanceAnalyzer *************************************
void UAuracronAdvancedPerformanceAnalyzer::StaticRegisterNativesUAuracronAdvancedPerformanceAnalyzer()
{
	UClass* Class = UAuracronAdvancedPerformanceAnalyzer::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzePerformanceBottlenecks", &UAuracronAdvancedPerformanceAnalyzer::execAnalyzePerformanceBottlenecks },
		{ "ApplyOptimizationRecommendation", &UAuracronAdvancedPerformanceAnalyzer::execApplyOptimizationRecommendation },
		{ "AutoApplySafeOptimizations", &UAuracronAdvancedPerformanceAnalyzer::execAutoApplySafeOptimizations },
		{ "GenerateOptimizationRecommendations", &UAuracronAdvancedPerformanceAnalyzer::execGenerateOptimizationRecommendations },
		{ "GetCurrentBottlenecks", &UAuracronAdvancedPerformanceAnalyzer::execGetCurrentBottlenecks },
		{ "GetCurrentPerformanceMetrics", &UAuracronAdvancedPerformanceAnalyzer::execGetCurrentPerformanceMetrics },
		{ "GetPerformanceTrend", &UAuracronAdvancedPerformanceAnalyzer::execGetPerformanceTrend },
		{ "HasBottleneckType", &UAuracronAdvancedPerformanceAnalyzer::execHasBottleneckType },
		{ "InitializePerformanceAnalyzer", &UAuracronAdvancedPerformanceAnalyzer::execInitializePerformanceAnalyzer },
		{ "PredictFuturePerformance", &UAuracronAdvancedPerformanceAnalyzer::execPredictFuturePerformance },
		{ "PredictPerformanceImpact", &UAuracronAdvancedPerformanceAnalyzer::execPredictPerformanceImpact },
		{ "SetAutoOptimizationEnabled", &UAuracronAdvancedPerformanceAnalyzer::execSetAutoOptimizationEnabled },
		{ "SetMonitoringFrequency", &UAuracronAdvancedPerformanceAnalyzer::execSetMonitoringFrequency },
		{ "SetOptimizationStrategy", &UAuracronAdvancedPerformanceAnalyzer::execSetOptimizationStrategy },
		{ "StartPerformanceMonitoring", &UAuracronAdvancedPerformanceAnalyzer::execStartPerformanceMonitoring },
		{ "StopPerformanceMonitoring", &UAuracronAdvancedPerformanceAnalyzer::execStopPerformanceMonitoring },
		{ "UpdatePerformanceAnalysis", &UAuracronAdvancedPerformanceAnalyzer::execUpdatePerformanceAnalysis },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer;
UClass* UAuracronAdvancedPerformanceAnalyzer::GetPrivateStaticClass()
{
	using TClass = UAuracronAdvancedPerformanceAnalyzer;
	if (!Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdvancedPerformanceAnalyzer"),
			Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.InnerSingleton,
			StaticRegisterNativesUAuracronAdvancedPerformanceAnalyzer,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister()
{
	return UAuracronAdvancedPerformanceAnalyzer::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Advanced Performance Analyzer\n * \n * Comprehensive performance analysis system that provides real-time\n * monitoring, bottleneck detection, predictive optimization, and\n * automated performance management for optimal game experience.\n */" },
#endif
		{ "IncludePath", "AuracronAdvancedPerformanceAnalyzer.h" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Advanced Performance Analyzer\n\nComprehensive performance analysis system that provides real-time\nmonitoring, bottleneck detection, predictive optimization, and\nautomated performance management for optimal game experience." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPerformanceAnalyzerEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance analyzer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance analyzer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringFrequency_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Monitoring frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitoring frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationStrategy_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization strategy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoOptimizationEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable auto-optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable auto-optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePredictiveAnalysis_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable predictive analysis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable predictive analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMetrics_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceHistory_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance history */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance history" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBottlenecks_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current bottlenecks */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current bottlenecks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveOptimizations_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active optimizations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active optimizations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPerformanceAnalyzer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bPerformanceAnalyzerEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPerformanceAnalyzerEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MonitoringFrequency;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationStrategy;
	static void NewProp_bAutoOptimizationEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoOptimizationEnabled;
	static void NewProp_bEnablePredictiveAnalysis_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePredictiveAnalysis;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PerformanceHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentBottlenecks_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CurrentBottlenecks;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveOptimizations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveOptimizations;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AnalyzePerformanceBottlenecks, "AnalyzePerformanceBottlenecks" }, // 989564935
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_ApplyOptimizationRecommendation, "ApplyOptimizationRecommendation" }, // 63632304
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_AutoApplySafeOptimizations, "AutoApplySafeOptimizations" }, // 2534879111
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GenerateOptimizationRecommendations, "GenerateOptimizationRecommendations" }, // 2682047672
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentBottlenecks, "GetCurrentBottlenecks" }, // 3757736765
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetCurrentPerformanceMetrics, "GetCurrentPerformanceMetrics" }, // 1519351132
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_GetPerformanceTrend, "GetPerformanceTrend" }, // 4126873487
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_HasBottleneckType, "HasBottleneckType" }, // 2992765718
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_InitializePerformanceAnalyzer, "InitializePerformanceAnalyzer" }, // 3650813671
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnBottleneckDetected, "OnBottleneckDetected" }, // 1327530178
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnOptimizationApplied, "OnOptimizationApplied" }, // 242655780
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_OnPerformanceThresholdExceeded, "OnPerformanceThresholdExceeded" }, // 3758408711
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictFuturePerformance, "PredictFuturePerformance" }, // 2035906700
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_PredictPerformanceImpact, "PredictPerformanceImpact" }, // 449706781
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetAutoOptimizationEnabled, "SetAutoOptimizationEnabled" }, // 92083578
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetMonitoringFrequency, "SetMonitoringFrequency" }, // 1565994678
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_SetOptimizationStrategy, "SetOptimizationStrategy" }, // 3280066659
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StartPerformanceMonitoring, "StartPerformanceMonitoring" }, // 1165688675
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_StopPerformanceMonitoring, "StopPerformanceMonitoring" }, // 207421471
		{ &Z_Construct_UFunction_UAuracronAdvancedPerformanceAnalyzer_UpdatePerformanceAnalysis, "UpdatePerformanceAnalysis" }, // 2738074192
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAdvancedPerformanceAnalyzer>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bPerformanceAnalyzerEnabled_SetBit(void* Obj)
{
	((UAuracronAdvancedPerformanceAnalyzer*)Obj)->bPerformanceAnalyzerEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bPerformanceAnalyzerEnabled = { "bPerformanceAnalyzerEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedPerformanceAnalyzer), &Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bPerformanceAnalyzerEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPerformanceAnalyzerEnabled_MetaData), NewProp_bPerformanceAnalyzerEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_MonitoringFrequency = { "MonitoringFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, MonitoringFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringFrequency_MetaData), NewProp_MonitoringFrequency_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_OptimizationStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_OptimizationStrategy = { "OptimizationStrategy", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, OptimizationStrategy), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPerformanceOptimizationStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationStrategy_MetaData), NewProp_OptimizationStrategy_MetaData) }; // 3314976456
void Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bAutoOptimizationEnabled_SetBit(void* Obj)
{
	((UAuracronAdvancedPerformanceAnalyzer*)Obj)->bAutoOptimizationEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bAutoOptimizationEnabled = { "bAutoOptimizationEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedPerformanceAnalyzer), &Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bAutoOptimizationEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoOptimizationEnabled_MetaData), NewProp_bAutoOptimizationEnabled_MetaData) };
void Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bEnablePredictiveAnalysis_SetBit(void* Obj)
{
	((UAuracronAdvancedPerformanceAnalyzer*)Obj)->bEnablePredictiveAnalysis = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bEnablePredictiveAnalysis = { "bEnablePredictiveAnalysis", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedPerformanceAnalyzer), &Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bEnablePredictiveAnalysis_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePredictiveAnalysis_MetaData), NewProp_bEnablePredictiveAnalysis_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentMetrics = { "CurrentMetrics", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, CurrentMetrics), Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMetrics_MetaData), NewProp_CurrentMetrics_MetaData) }; // 2666824745
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_PerformanceHistory_Inner = { "PerformanceHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 2666824745
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_PerformanceHistory = { "PerformanceHistory", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, PerformanceHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceHistory_MetaData), NewProp_PerformanceHistory_MetaData) }; // 2666824745
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentBottlenecks_Inner = { "CurrentBottlenecks", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, METADATA_PARAMS(0, nullptr) }; // 965090622
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentBottlenecks = { "CurrentBottlenecks", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, CurrentBottlenecks), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBottlenecks_MetaData), NewProp_CurrentBottlenecks_MetaData) }; // 965090622
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_ActiveOptimizations_Inner = { "ActiveOptimizations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation, METADATA_PARAMS(0, nullptr) }; // 3796584243
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_ActiveOptimizations = { "ActiveOptimizations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, ActiveOptimizations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveOptimizations_MetaData), NewProp_ActiveOptimizations_MetaData) }; // 3796584243
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedPerformanceAnalyzer, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bPerformanceAnalyzerEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_MonitoringFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_OptimizationStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_OptimizationStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bAutoOptimizationEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_bEnablePredictiveAnalysis,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_PerformanceHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_PerformanceHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentBottlenecks_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CurrentBottlenecks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_ActiveOptimizations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_ActiveOptimizations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::NewProp_CachedRealmSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::ClassParams = {
	&UAuracronAdvancedPerformanceAnalyzer::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer()
{
	if (!Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.OuterSingleton, Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer.OuterSingleton;
}
UAuracronAdvancedPerformanceAnalyzer::UAuracronAdvancedPerformanceAnalyzer() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAdvancedPerformanceAnalyzer);
UAuracronAdvancedPerformanceAnalyzer::~UAuracronAdvancedPerformanceAnalyzer() {}
// ********** End Class UAuracronAdvancedPerformanceAnalyzer ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPerformanceAnalysisCategory_StaticEnum, TEXT("EPerformanceAnalysisCategory"), &Z_Registration_Info_UEnum_EPerformanceAnalysisCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3501850380U) },
		{ EPerformanceBottleneckType_StaticEnum, TEXT("EPerformanceBottleneckType"), &Z_Registration_Info_UEnum_EPerformanceBottleneckType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 234980083U) },
		{ EPerformanceOptimizationStrategy_StaticEnum, TEXT("EPerformanceOptimizationStrategy"), &Z_Registration_Info_UEnum_EPerformanceOptimizationStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3314976456U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronAdvancedPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronAdvancedPerformanceMetrics_Statics::NewStructOps, TEXT("AuracronAdvancedPerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronAdvancedPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAdvancedPerformanceMetrics), 2666824745U) },
		{ FAuracronPerformanceBottleneckAnalysis::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceBottleneckAnalysis_Statics::NewStructOps, TEXT("AuracronPerformanceBottleneckAnalysis"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceBottleneckAnalysis, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceBottleneckAnalysis), 965090622U) },
		{ FAuracronPerformanceOptimizationRecommendation::StaticStruct, Z_Construct_UScriptStruct_FAuracronPerformanceOptimizationRecommendation_Statics::NewStructOps, TEXT("AuracronPerformanceOptimizationRecommendation"), &Z_Registration_Info_UScriptStruct_FAuracronPerformanceOptimizationRecommendation, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPerformanceOptimizationRecommendation), 3796584243U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer, UAuracronAdvancedPerformanceAnalyzer::StaticClass, TEXT("UAuracronAdvancedPerformanceAnalyzer"), &Z_Registration_Info_UClass_UAuracronAdvancedPerformanceAnalyzer, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAdvancedPerformanceAnalyzer), 645287939U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_2682252490(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedPerformanceAnalyzer_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
