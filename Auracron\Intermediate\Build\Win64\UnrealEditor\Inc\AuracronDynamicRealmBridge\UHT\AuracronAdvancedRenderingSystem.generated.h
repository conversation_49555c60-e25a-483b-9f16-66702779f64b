// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdvancedRenderingSystem.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronAdvancedRenderingSystem_generated_h
#error "AuracronAdvancedRenderingSystem.generated.h already included, missing '#pragma once' in AuracronAdvancedRenderingSystem.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronAdvancedRenderingSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EPlatformRenderingProfile : uint8;
enum class ERenderingQualityLevel : uint8;
struct FAuracronAdvancedRenderingConfig;
struct FAuracronRenderingPerformanceMetrics;

// ********** Begin ScriptStruct FAuracronAdvancedRenderingConfig **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_98_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAdvancedRenderingConfig;
// ********** End ScriptStruct FAuracronAdvancedRenderingConfig ************************************

// ********** Begin ScriptStruct FAuracronRenderingPerformanceMetrics ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_166_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRenderingPerformanceMetrics;
// ********** End ScriptStruct FAuracronRenderingPerformanceMetrics ********************************

// ********** Begin Class UAuracronAdvancedRenderingSystem *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCoordinateRenderingSubsystems); \
	DECLARE_FUNCTION(execInitializeVFXIntegration); \
	DECLARE_FUNCTION(execInitializeNaniteIntegration); \
	DECLARE_FUNCTION(execInitializeLumenIntegration); \
	DECLARE_FUNCTION(execOptimizeRenderingPerformance); \
	DECLARE_FUNCTION(execMonitorRenderingPerformance); \
	DECLARE_FUNCTION(execGetRenderingPerformanceMetrics); \
	DECLARE_FUNCTION(execApplyPlatformSpecificSettings); \
	DECLARE_FUNCTION(execOptimizeForCurrentPlatform); \
	DECLARE_FUNCTION(execSetPlatformRenderingProfile); \
	DECLARE_FUNCTION(execUpdateAdaptiveQuality); \
	DECLARE_FUNCTION(execSetAdaptiveQualityEnabled); \
	DECLARE_FUNCTION(execGetCurrentQualityLevel); \
	DECLARE_FUNCTION(execSetRenderingQualityLevel); \
	DECLARE_FUNCTION(execGetCurrentRenderingConfiguration); \
	DECLARE_FUNCTION(execApplyRenderingConfiguration); \
	DECLARE_FUNCTION(execUpdateRenderingSystem); \
	DECLARE_FUNCTION(execInitializeRenderingSystem);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAdvancedRenderingSystem(); \
	friend struct Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAdvancedRenderingSystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronAdvancedRenderingSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAdvancedRenderingSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronAdvancedRenderingSystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAdvancedRenderingSystem(UAuracronAdvancedRenderingSystem&&) = delete; \
	UAuracronAdvancedRenderingSystem(const UAuracronAdvancedRenderingSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAdvancedRenderingSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAdvancedRenderingSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAdvancedRenderingSystem) \
	NO_API virtual ~UAuracronAdvancedRenderingSystem();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_225_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h_228_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAdvancedRenderingSystem;

// ********** End Class UAuracronAdvancedRenderingSystem *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h

// ********** Begin Enum ERenderingQualityLevel ****************************************************
#define FOREACH_ENUM_ERENDERINGQUALITYLEVEL(op) \
	op(ERenderingQualityLevel::Low) \
	op(ERenderingQualityLevel::Medium) \
	op(ERenderingQualityLevel::High) \
	op(ERenderingQualityLevel::Ultra) \
	op(ERenderingQualityLevel::Cinematic) \
	op(ERenderingQualityLevel::Adaptive) 

enum class ERenderingQualityLevel : uint8;
template<> struct TIsUEnumClass<ERenderingQualityLevel> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERenderingQualityLevel>();
// ********** End Enum ERenderingQualityLevel ******************************************************

// ********** Begin Enum ERenderingOptimizationStrategy ********************************************
#define FOREACH_ENUM_ERENDERINGOPTIMIZATIONSTRATEGY(op) \
	op(ERenderingOptimizationStrategy::Performance) \
	op(ERenderingOptimizationStrategy::Quality) \
	op(ERenderingOptimizationStrategy::Balanced) \
	op(ERenderingOptimizationStrategy::Adaptive) \
	op(ERenderingOptimizationStrategy::Platform) \
	op(ERenderingOptimizationStrategy::Custom) 

enum class ERenderingOptimizationStrategy : uint8;
template<> struct TIsUEnumClass<ERenderingOptimizationStrategy> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERenderingOptimizationStrategy>();
// ********** End Enum ERenderingOptimizationStrategy **********************************************

// ********** Begin Enum EPlatformRenderingProfile *************************************************
#define FOREACH_ENUM_EPLATFORMRENDERINGPROFILE(op) \
	op(EPlatformRenderingProfile::Desktop_High) \
	op(EPlatformRenderingProfile::Desktop_Mid) \
	op(EPlatformRenderingProfile::Desktop_Low) \
	op(EPlatformRenderingProfile::Console_Next) \
	op(EPlatformRenderingProfile::Console_Current) \
	op(EPlatformRenderingProfile::Mobile_High) \
	op(EPlatformRenderingProfile::Mobile_Mid) \
	op(EPlatformRenderingProfile::Mobile_Low) \
	op(EPlatformRenderingProfile::VR_High) \
	op(EPlatformRenderingProfile::VR_Standard) 

enum class EPlatformRenderingProfile : uint8;
template<> struct TIsUEnumClass<EPlatformRenderingProfile> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPlatformRenderingProfile>();
// ********** End Enum EPlatformRenderingProfile ***************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
