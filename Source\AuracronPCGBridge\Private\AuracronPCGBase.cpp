// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Base Implementation
// Bridge 2.2: PCG Framework - Base Classes

#include "AuracronPCGBase.h"
#include "PCGComponent.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"

// UAuracronPCGSettingsBase Implementation
UAuracronPCGSettingsBase::UAuracronPCGSettingsBase(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Default settings
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::InputPinProperties() const
{
    TArray<FPCGPinProperties> PinProperties;
    PinProperties.Emplace(PCGPinConstants::DefaultInputLabel, EPCGDataType::Spatial);
    return PinProperties;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::OutputPinProperties() const
{
    TArray<FPCGPinProperties> PinProperties;
    PinProperties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Spatial);
    return PinProperties;
}

FPCGElementPtr UAuracronPCGSettingsBase::CreateElement() const
{
    return MakeShared<FAuracronPCGElementBase>();
}

// FAuracronPCGElementBase Implementation
FPCGContext* FAuracronPCGElementBase::Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node)
{
    FPCGContext* Context = new FPCGContext();
    Context->InputData = InputData;
    Context->Node = Node;
    return Context;
}

bool FAuracronPCGElementBase::ExecuteInternal(FPCGContext* Context) const
{
    // Default implementation - just pass through the input data
    Context->OutputData = Context->InputData;
    return true;
}

void FAuracronPCGElementBase::ProcessPoints(FPCGContext* Context, const FPCGDataCollection& InputData, FPCGDataCollection& OutputData) const
{
    // Default implementation - copy input to output
    OutputData = InputData;
}

// UAuracronPCGExampleSettings Implementation
UAuracronPCGExampleSettings::UAuracronPCGExampleSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Default settings
}

FPCGElementPtr UAuracronPCGExampleSettings::CreateElement() const
{
    return MakeShared<FAuracronPCGExampleElement>();
}

// FAuracronPCGExampleElement Implementation
bool FAuracronPCGExampleElement::ExecuteInternal(FPCGContext* Context) const
{
    if (!Context || !Context->Node)
    {
        return false;
    }

    const UAuracronPCGExampleSettings* Settings = Context->GetInputSettings<UAuracronPCGExampleSettings>();
    if (!Settings)
    {
        return false;
    }

    // Process input data
    for (const FPCGTaggedData& TaggedData : Context->InputData.TaggedData)
    {
        if (const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data))
        {
            // Create output point data
            UPCGPointData* OutputPointData = NewObject<UPCGPointData>();
            OutputPointData->InitializeFromData(PointData);
            
            // Transform points
            TArray<FPCGPoint>& OutputPoints = OutputPointData->GetMutablePoints();
            for (FPCGPoint& Point : OutputPoints)
            {
                Point.Transform.SetLocation(Point.Transform.GetLocation() + Settings->Offset);
                Point.Transform.SetScale3D(Point.Transform.GetScale3D() * Settings->Scale);
            }

            // Add to output
            FPCGTaggedData& OutputTaggedData = Context->OutputData.TaggedData.Emplace_GetRef();
            OutputTaggedData.Data = OutputPointData;
            OutputTaggedData.Tags = TaggedData.Tags;
        }
    }

    return true;
}
