{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronworldpartitionbridge\\module.auracronworldpartitionbridge.gen.7.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.rtti.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronworldpartitionbridge\\definitions.auracronworldpartitionbridge.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitionstreaming.gen.cpp", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartitionstreaming.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordesc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorcontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorcontainerid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\filter\\worldpartitionactorfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorfilter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstancecollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancenames.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancenames.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesclist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackagegenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionstreaminggeneration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinitparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceviewinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorloaderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorloaderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitioneditorloaderadapter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitioneditorloaderadapter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecelltransformer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecelltransformer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\actorreferencesutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externaldirtyactorstracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\packagesourcecontrolhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangelist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangeliststate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontroloperation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolresultinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolrevision.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\sourcecontrol\\uht\\sourcecontrolhelpers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\cookpackagesplitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\streaming\\streamingworldsubsysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\streamingworldsubsysteminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecell.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayertype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayertype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\errorhandling\\worldpartitionstreaminggenerationerrorhandler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecelldata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\stringdev.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\hierarchicallogarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecelldata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecellinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecellinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackageinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitioncookpackageinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\anyof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\noneof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecell.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionstreaminggenerationcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecontainerresolving.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecontainerresolving.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstanceproviderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstanceproviderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\worlddatalayers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\actordatalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordatalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayeruid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayeruid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externalpackagehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\archivemd5.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancewithasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancewithasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\coreredirects.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\internal\\uobject\\coreredirects\\pm-k.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worlddatalayers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimespatialhash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayersid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshmergingsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshmergingsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshproxysettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshproxysettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshapproximationsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshapproximationsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodbuilder.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimespatialhashgridpreviewer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimespatialhashgridpreviewer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimespatialhash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionstreamingpolicy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionstreamingpolicy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayersubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayermanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayereditorcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayermanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayersubsystem.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartition.generated.h", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartitiongrid.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitiongrid.generated.h", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartitiondebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\canvas.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\canvasitem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\font.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\fontimportoptions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\fontimportoptions.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slatecore\\public\\fonts\\fontproviderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slatecore\\uht\\fontproviderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\font.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\canvastypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\canvastypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\canvas.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\debug\\debugdrawservice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\debugdrawservice.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hud.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\hudhitbox.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\debugtextinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\debugtextinfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hud.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitiondebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlevelstreamingdynamic.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\levelstreamingdynamic.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\levelstreamingdynamic.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimelevelstreamingcell.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlevelhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionpropertyoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionpropertyoverride.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimelevelstreamingcell.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionlevelstreamingdynamic.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitionstreaming.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitiontests.gen.cpp", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartitiontests.h", "c:\\aura\\projeto\\auracron\\source\\auracronworldpartitionbridge\\public\\auracronworldpartitionperformance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\stats\\statsdata.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitionperformance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\tests\\automationcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadheartbeat.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronworldpartitionbridge\\uht\\auracronworldpartitiontests.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}