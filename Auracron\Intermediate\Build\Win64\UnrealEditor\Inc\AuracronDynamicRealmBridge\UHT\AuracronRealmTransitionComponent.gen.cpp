// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronRealmTransitionComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronRealmTransitionComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransitionEffects();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransitionPath();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APlayerController_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronTransitionEffects ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects;
class UScriptStruct* FAuracronTransitionEffects::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTransitionEffects, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronTransitionEffects"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Transition effect configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition effect configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartEffect_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual effect for transition start */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual effect for transition start" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEffect_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual effect during transition */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual effect during transition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndEffect_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual effect for transition end */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual effect for transition end" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartSound_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio for transition start */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio for transition start" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSound_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio during transition */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio during transition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndSound_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio for transition end */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio for transition end" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScreenEffectIntensity_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Screen effect intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Screen effect intensity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraShakeIntensity_MetaData[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camera shake intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera shake intensity" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StartEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EndEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StartSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EndSound;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScreenEffectIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraShakeIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTransitionEffects>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_StartEffect = { "StartEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, StartEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartEffect_MetaData), NewProp_StartEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_TransitionEffect = { "TransitionEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, TransitionEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEffect_MetaData), NewProp_TransitionEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_EndEffect = { "EndEffect", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, EndEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndEffect_MetaData), NewProp_EndEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_StartSound = { "StartSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, StartSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartSound_MetaData), NewProp_StartSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_TransitionSound = { "TransitionSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, TransitionSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSound_MetaData), NewProp_TransitionSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_EndSound = { "EndSound", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, EndSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndSound_MetaData), NewProp_EndSound_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_ScreenEffectIntensity = { "ScreenEffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, ScreenEffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScreenEffectIntensity_MetaData), NewProp_ScreenEffectIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_CameraShakeIntensity = { "CameraShakeIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionEffects, CameraShakeIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraShakeIntensity_MetaData), NewProp_CameraShakeIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_StartEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_TransitionEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_EndEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_StartSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_TransitionSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_EndSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_ScreenEffectIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewProp_CameraShakeIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronTransitionEffects",
	Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::PropPointers),
	sizeof(FAuracronTransitionEffects),
	alignof(FAuracronTransitionEffects),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransitionEffects()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTransitionEffects ******************************************

// ********** Begin ScriptStruct FAuracronTransitionPath *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTransitionPath;
class UScriptStruct* FAuracronTransitionPath::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTransitionPath, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronTransitionPath"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Transition path data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition path data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLayer_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Source layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Source layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLayer_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionType_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Waypoints_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Path waypoints */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path waypoints" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionCurve_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition curve */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition curve" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Effects_MetaData[] = {
		{ "Category", "Transition Path" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Effects configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effects configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Waypoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Waypoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionCurve;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Effects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTransitionPath>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLayer_MetaData), NewProp_SourceLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLayer_MetaData), NewProp_TargetLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, TransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionType_MetaData), NewProp_TransitionType_MetaData) }; // 2122775647
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Waypoints_Inner = { "Waypoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Waypoints = { "Waypoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, Waypoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Waypoints_MetaData), NewProp_Waypoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionCurve = { "TransitionCurve", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, TransitionCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionCurve_MetaData), NewProp_TransitionCurve_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Effects = { "Effects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTransitionPath, Effects), Z_Construct_UScriptStruct_FAuracronTransitionEffects, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Effects_MetaData), NewProp_Effects_MetaData) }; // 268295719
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Waypoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Waypoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_TransitionCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewProp_Effects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronTransitionPath",
	Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::PropPointers),
	sizeof(FAuracronTransitionPath),
	alignof(FAuracronTransitionPath),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTransitionPath()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTransitionPath.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTransitionPath *********************************************

// ********** Begin Class UAuracronRealmTransitionComponent Function ApplyLayerPhysics *************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics
{
	struct AuracronRealmTransitionComponent_eventApplyLayerPhysics_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Physics" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventApplyLayerPhysics_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "ApplyLayerPhysics", Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::AuracronRealmTransitionComponent_eventApplyLayerPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::AuracronRealmTransitionComponent_eventApplyLayerPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execApplyLayerPhysics)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyLayerPhysics(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function ApplyLayerPhysics ***************

// ********** Begin Class UAuracronRealmTransitionComponent Function CancelTransition **************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Transition" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "CancelTransition", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execCancelTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelTransition();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function CancelTransition ****************

// ********** Begin Class UAuracronRealmTransitionComponent Function GetTransitionPath *************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics
{
	struct AuracronRealmTransitionComponent_eventGetTransitionPath_Parms
	{
		FAuracronTransitionPath ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventGetTransitionPath_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronTransitionPath, METADATA_PARAMS(0, nullptr) }; // 3073015388
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "GetTransitionPath", Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::AuracronRealmTransitionComponent_eventGetTransitionPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::AuracronRealmTransitionComponent_eventGetTransitionPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execGetTransitionPath)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronTransitionPath*)Z_Param__Result=P_THIS->GetTransitionPath();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function GetTransitionPath ***************

// ********** Begin Class UAuracronRealmTransitionComponent Function GetTransitionProgress *********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics
{
	struct AuracronRealmTransitionComponent_eventGetTransitionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Transition" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "GetTransitionProgress", Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::AuracronRealmTransitionComponent_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::AuracronRealmTransitionComponent_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execGetTransitionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function GetTransitionProgress ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function GetTransitionTarget ***********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics
{
	struct AuracronRealmTransitionComponent_eventGetTransitionTarget_Parms
	{
		EAuracronRealmLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Transition" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventGetTransitionTarget_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "GetTransitionTarget", Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::AuracronRealmTransitionComponent_eventGetTransitionTarget_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::AuracronRealmTransitionComponent_eventGetTransitionTarget_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execGetTransitionTarget)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmLayer*)Z_Param__Result=P_THIS->GetTransitionTarget();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function GetTransitionTarget *************

// ********** Begin Class UAuracronRealmTransitionComponent Function IsTransitioning ***************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics
{
	struct AuracronRealmTransitionComponent_eventIsTransitioning_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Transition" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmTransitionComponent_eventIsTransitioning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmTransitionComponent_eventIsTransitioning_Parms), &Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "IsTransitioning", Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::AuracronRealmTransitionComponent_eventIsTransitioning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::AuracronRealmTransitionComponent_eventIsTransitioning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execIsTransitioning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsTransitioning();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function IsTransitioning *****************

// ********** Begin Class UAuracronRealmTransitionComponent Function OnTransitionCancelled *********
static FName NAME_UAuracronRealmTransitionComponent_OnTransitionCancelled = FName(TEXT("OnTransitionCancelled"));
void UAuracronRealmTransitionComponent::OnTransitionCancelled()
{
	UFunction* Func = FindFunctionChecked(NAME_UAuracronRealmTransitionComponent_OnTransitionCancelled);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "OnTransitionCancelled", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronRealmTransitionComponent Function OnTransitionCancelled ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function OnTransitionCompleted *********
struct AuracronRealmTransitionComponent_eventOnTransitionCompleted_Parms
{
	EAuracronRealmLayer NewLayer;
};
static FName NAME_UAuracronRealmTransitionComponent_OnTransitionCompleted = FName(TEXT("OnTransitionCompleted"));
void UAuracronRealmTransitionComponent::OnTransitionCompleted(EAuracronRealmLayer NewLayer)
{
	AuracronRealmTransitionComponent_eventOnTransitionCompleted_Parms Parms;
	Parms.NewLayer=NewLayer;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronRealmTransitionComponent_OnTransitionCompleted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventOnTransitionCompleted_Parms, NewLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "OnTransitionCompleted", Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::PropPointers), sizeof(AuracronRealmTransitionComponent_eventOnTransitionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronRealmTransitionComponent_eventOnTransitionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronRealmTransitionComponent Function OnTransitionCompleted ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function OnTransitionProgress **********
struct AuracronRealmTransitionComponent_eventOnTransitionProgress_Parms
{
	float Progress;
};
static FName NAME_UAuracronRealmTransitionComponent_OnTransitionProgress = FName(TEXT("OnTransitionProgress"));
void UAuracronRealmTransitionComponent::OnTransitionProgress(float Progress)
{
	AuracronRealmTransitionComponent_eventOnTransitionProgress_Parms Parms;
	Parms.Progress=Progress;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronRealmTransitionComponent_OnTransitionProgress);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Events" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventOnTransitionProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "OnTransitionProgress", Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::PropPointers), sizeof(AuracronRealmTransitionComponent_eventOnTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronRealmTransitionComponent_eventOnTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronRealmTransitionComponent Function OnTransitionProgress ************

// ********** Begin Class UAuracronRealmTransitionComponent Function OnTransitionStarted ***********
struct AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms
{
	EAuracronRealmLayer SourceLayer;
	EAuracronRealmLayer TargetLayer;
};
static FName NAME_UAuracronRealmTransitionComponent_OnTransitionStarted = FName(TEXT("OnTransitionStarted"));
void UAuracronRealmTransitionComponent::OnTransitionStarted(EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer)
{
	AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms Parms;
	Parms.SourceLayer=SourceLayer;
	Parms.TargetLayer=TargetLayer;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronRealmTransitionComponent_OnTransitionStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::NewProp_TargetLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "OnTransitionStarted", Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::PropPointers), sizeof(AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronRealmTransitionComponent_eventOnTransitionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronRealmTransitionComponent Function OnTransitionStarted *************

// ********** Begin Class UAuracronRealmTransitionComponent Function PlayTransitionEffects *********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Effect management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effect management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "PlayTransitionEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execPlayTransitionEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayTransitionEffects();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function PlayTransitionEffects ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function RestoreOriginalCamera *********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Camera" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "RestoreOriginalCamera", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execRestoreOriginalCamera)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RestoreOriginalCamera();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function RestoreOriginalCamera ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function SetTransitionEffects **********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics
{
	struct AuracronRealmTransitionComponent_eventSetTransitionEffects_Parms
	{
		FAuracronTransitionEffects NewEffects;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewEffects_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewEffects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::NewProp_NewEffects = { "NewEffects", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventSetTransitionEffects_Parms, NewEffects), Z_Construct_UScriptStruct_FAuracronTransitionEffects, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewEffects_MetaData), NewProp_NewEffects_MetaData) }; // 268295719
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::NewProp_NewEffects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "SetTransitionEffects", Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::AuracronRealmTransitionComponent_eventSetTransitionEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::AuracronRealmTransitionComponent_eventSetTransitionEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execSetTransitionEffects)
{
	P_GET_STRUCT_REF(FAuracronTransitionEffects,Z_Param_Out_NewEffects);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTransitionEffects(Z_Param_Out_NewEffects);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function SetTransitionEffects ************

// ********** Begin Class UAuracronRealmTransitionComponent Function SetTransitionPath *************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics
{
	struct AuracronRealmTransitionComponent_eventSetTransitionPath_Parms
	{
		FAuracronTransitionPath NewPath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewPath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::NewProp_NewPath = { "NewPath", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventSetTransitionPath_Parms, NewPath), Z_Construct_UScriptStruct_FAuracronTransitionPath, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewPath_MetaData), NewProp_NewPath_MetaData) }; // 3073015388
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::NewProp_NewPath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "SetTransitionPath", Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::AuracronRealmTransitionComponent_eventSetTransitionPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::AuracronRealmTransitionComponent_eventSetTransitionPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execSetTransitionPath)
{
	P_GET_STRUCT_REF(FAuracronTransitionPath,Z_Param_Out_NewPath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTransitionPath(Z_Param_Out_NewPath);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function SetTransitionPath ***************

// ********** Begin Class UAuracronRealmTransitionComponent Function SetupTransitionCamera *********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Camera and view management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera and view management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "SetupTransitionCamera", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execSetupTransitionCamera)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupTransitionCamera();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function SetupTransitionCamera ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function StartTransition ***************
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics
{
	struct AuracronRealmTransitionComponent_eventStartTransition_Parms
	{
		EAuracronRealmLayer TargetLayer;
		ERealmTransitionType TransitionType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Realm Transition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition control\n" },
#endif
		{ "CPP_Default_TransitionType", "Gradual" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition control" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventStartTransition_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventStartTransition_Parms, TransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(0, nullptr) }; // 2122775647
void Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronRealmTransitionComponent_eventStartTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronRealmTransitionComponent_eventStartTransition_Parms), &Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "StartTransition", Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::AuracronRealmTransitionComponent_eventStartTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::AuracronRealmTransitionComponent_eventStartTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execStartTransition)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_GET_ENUM(ERealmTransitionType,Z_Param_TransitionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StartTransition(EAuracronRealmLayer(Z_Param_TargetLayer),ERealmTransitionType(Z_Param_TransitionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function StartTransition *****************

// ********** Begin Class UAuracronRealmTransitionComponent Function StopTransitionEffects *********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Effects" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "StopTransitionEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execStopTransitionEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopTransitionEffects();
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function StopTransitionEffects ***********

// ********** Begin Class UAuracronRealmTransitionComponent Function UpdateTransitionCamera ********
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics
{
	struct AuracronRealmTransitionComponent_eventUpdateTransitionCamera_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Camera" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventUpdateTransitionCamera_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "UpdateTransitionCamera", Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionCamera_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionCamera_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execUpdateTransitionCamera)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTransitionCamera(Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function UpdateTransitionCamera **********

// ********** Begin Class UAuracronRealmTransitionComponent Function UpdateTransitionEffects *******
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics
{
	struct AuracronRealmTransitionComponent_eventUpdateTransitionEffects_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Effects" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventUpdateTransitionEffects_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "UpdateTransitionEffects", Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execUpdateTransitionEffects)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTransitionEffects(Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function UpdateTransitionEffects *********

// ********** Begin Class UAuracronRealmTransitionComponent Function UpdateTransitionPhysics *******
struct Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics
{
	struct AuracronRealmTransitionComponent_eventUpdateTransitionPhysics_Parms
	{
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transition Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Physics and collision\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics and collision" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronRealmTransitionComponent_eventUpdateTransitionPhysics_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronRealmTransitionComponent, nullptr, "UpdateTransitionPhysics", Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionPhysics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::AuracronRealmTransitionComponent_eventUpdateTransitionPhysics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronRealmTransitionComponent::execUpdateTransitionPhysics)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTransitionPhysics(Z_Param_Progress);
	P_NATIVE_END;
}
// ********** End Class UAuracronRealmTransitionComponent Function UpdateTransitionPhysics *********

// ********** Begin Class UAuracronRealmTransitionComponent ****************************************
void UAuracronRealmTransitionComponent::StaticRegisterNativesUAuracronRealmTransitionComponent()
{
	UClass* Class = UAuracronRealmTransitionComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyLayerPhysics", &UAuracronRealmTransitionComponent::execApplyLayerPhysics },
		{ "CancelTransition", &UAuracronRealmTransitionComponent::execCancelTransition },
		{ "GetTransitionPath", &UAuracronRealmTransitionComponent::execGetTransitionPath },
		{ "GetTransitionProgress", &UAuracronRealmTransitionComponent::execGetTransitionProgress },
		{ "GetTransitionTarget", &UAuracronRealmTransitionComponent::execGetTransitionTarget },
		{ "IsTransitioning", &UAuracronRealmTransitionComponent::execIsTransitioning },
		{ "PlayTransitionEffects", &UAuracronRealmTransitionComponent::execPlayTransitionEffects },
		{ "RestoreOriginalCamera", &UAuracronRealmTransitionComponent::execRestoreOriginalCamera },
		{ "SetTransitionEffects", &UAuracronRealmTransitionComponent::execSetTransitionEffects },
		{ "SetTransitionPath", &UAuracronRealmTransitionComponent::execSetTransitionPath },
		{ "SetupTransitionCamera", &UAuracronRealmTransitionComponent::execSetupTransitionCamera },
		{ "StartTransition", &UAuracronRealmTransitionComponent::execStartTransition },
		{ "StopTransitionEffects", &UAuracronRealmTransitionComponent::execStopTransitionEffects },
		{ "UpdateTransitionCamera", &UAuracronRealmTransitionComponent::execUpdateTransitionCamera },
		{ "UpdateTransitionEffects", &UAuracronRealmTransitionComponent::execUpdateTransitionEffects },
		{ "UpdateTransitionPhysics", &UAuracronRealmTransitionComponent::execUpdateTransitionPhysics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronRealmTransitionComponent;
UClass* UAuracronRealmTransitionComponent::GetPrivateStaticClass()
{
	using TClass = UAuracronRealmTransitionComponent;
	if (!Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronRealmTransitionComponent"),
			Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.InnerSingleton,
			StaticRegisterNativesUAuracronRealmTransitionComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister()
{
	return UAuracronRealmTransitionComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Auracron" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Realm Transition Component\n * \n * Handles smooth transitions between realm layers:\n * - Manages transition animations and effects\n * - Controls camera movement during transitions\n * - Applies layer-specific physics changes\n * - Handles collision and interaction updates\n * - Optimizes performance during transitions\n */" },
#endif
		{ "IncludePath", "AuracronRealmTransitionComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Realm Transition Component\n\nHandles smooth transitions between realm layers:\n- Manages transition animations and effects\n- Controls camera movement during transitions\n- Applies layer-specific physics changes\n- Handles collision and interaction updates\n- Optimizes performance during transitions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionPaths_MetaData[] = {
		{ "Category", "Transition Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoConfigureTransitions_MetaData[] = {
		{ "Category", "Transition Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTransitionDuration_MetaData[] = {
		{ "Category", "Transition Configuration" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTransitioning_MetaData[] = {
		{ "Category", "Transition State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current transition state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current transition state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSourceLayer_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTargetLayer_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionType_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionStartTime_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionPath_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectComponents_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Effect components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effect components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponents_MetaData[] = {
		{ "Category", "Effects" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalCameraLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Camera data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalCameraRotation_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalCameraFOV_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached references\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedLayerComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerPawn_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnerController_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronRealmTransitionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionPaths_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionPaths_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionPaths_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TransitionPaths;
	static void NewProp_bAutoConfigureTransitions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoConfigureTransitions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultTransitionDuration;
	static void NewProp_bIsTransitioning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTransitioning;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionSourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionSourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionTargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionTargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTransitionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionStartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentTransitionPath;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EffectComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AudioComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AudioComponents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalCameraLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalCameraRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OriginalCameraFOV;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedLayerComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerPawn;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerController;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_ApplyLayerPhysics, "ApplyLayerPhysics" }, // 667867596
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_CancelTransition, "CancelTransition" }, // 2779725868
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionPath, "GetTransitionPath" }, // 4063766681
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionProgress, "GetTransitionProgress" }, // 1555018948
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_GetTransitionTarget, "GetTransitionTarget" }, // 1317139805
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_IsTransitioning, "IsTransitioning" }, // 4279266418
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCancelled, "OnTransitionCancelled" }, // 2164495451
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionCompleted, "OnTransitionCompleted" }, // 3838177537
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionProgress, "OnTransitionProgress" }, // 1734504598
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_OnTransitionStarted, "OnTransitionStarted" }, // 674679762
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_PlayTransitionEffects, "PlayTransitionEffects" }, // 596925522
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_RestoreOriginalCamera, "RestoreOriginalCamera" }, // 3738229195
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionEffects, "SetTransitionEffects" }, // 2902409036
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetTransitionPath, "SetTransitionPath" }, // 808682755
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_SetupTransitionCamera, "SetupTransitionCamera" }, // 1407098868
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_StartTransition, "StartTransition" }, // 2709844419
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_StopTransitionEffects, "StopTransitionEffects" }, // 3699894786
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionCamera, "UpdateTransitionCamera" }, // 2902525243
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionEffects, "UpdateTransitionEffects" }, // 698365864
		{ &Z_Construct_UFunction_UAuracronRealmTransitionComponent_UpdateTransitionPhysics, "UpdateTransitionPhysics" }, // 2796631695
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronRealmTransitionComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_ValueProp = { "TransitionPaths", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronTransitionPath, METADATA_PARAMS(0, nullptr) }; // 3073015388
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_Key_KeyProp = { "TransitionPaths_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(0, nullptr) }; // 2122775647
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths = { "TransitionPaths", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, TransitionPaths), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionPaths_MetaData), NewProp_TransitionPaths_MetaData) }; // 2122775647 3073015388
void Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bAutoConfigureTransitions_SetBit(void* Obj)
{
	((UAuracronRealmTransitionComponent*)Obj)->bAutoConfigureTransitions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bAutoConfigureTransitions = { "bAutoConfigureTransitions", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronRealmTransitionComponent), &Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bAutoConfigureTransitions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoConfigureTransitions_MetaData), NewProp_bAutoConfigureTransitions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_DefaultTransitionDuration = { "DefaultTransitionDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, DefaultTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTransitionDuration_MetaData), NewProp_DefaultTransitionDuration_MetaData) };
void Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bIsTransitioning_SetBit(void* Obj)
{
	((UAuracronRealmTransitionComponent*)Obj)->bIsTransitioning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bIsTransitioning = { "bIsTransitioning", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronRealmTransitionComponent), &Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bIsTransitioning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTransitioning_MetaData), NewProp_bIsTransitioning_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionSourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionSourceLayer = { "TransitionSourceLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, TransitionSourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSourceLayer_MetaData), NewProp_TransitionSourceLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionTargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionTargetLayer = { "TransitionTargetLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, TransitionTargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTargetLayer_MetaData), NewProp_TransitionTargetLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionType = { "CurrentTransitionType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, CurrentTransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionType_MetaData), NewProp_CurrentTransitionType_MetaData) }; // 2122775647
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionStartTime = { "TransitionStartTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, TransitionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionStartTime_MetaData), NewProp_TransitionStartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionPath = { "CurrentTransitionPath", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, CurrentTransitionPath), Z_Construct_UScriptStruct_FAuracronTransitionPath, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionPath_MetaData), NewProp_CurrentTransitionPath_MetaData) }; // 3073015388
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_EffectComponents_Inner = { "EffectComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_EffectComponents = { "EffectComponents", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, EffectComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectComponents_MetaData), NewProp_EffectComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_AudioComponents_Inner = { "AudioComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_AudioComponents = { "AudioComponents", nullptr, (EPropertyFlags)0x012408800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, AudioComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponents_MetaData), NewProp_AudioComponents_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraLocation = { "OriginalCameraLocation", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, OriginalCameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalCameraLocation_MetaData), NewProp_OriginalCameraLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraRotation = { "OriginalCameraRotation", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, OriginalCameraRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalCameraRotation_MetaData), NewProp_OriginalCameraRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraFOV = { "OriginalCameraFOV", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, OriginalCameraFOV), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalCameraFOV_MetaData), NewProp_OriginalCameraFOV_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CachedLayerComponent = { "CachedLayerComponent", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, CachedLayerComponent), Z_Construct_UClass_UAuracronLayerComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedLayerComponent_MetaData), NewProp_CachedLayerComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OwnerPawn = { "OwnerPawn", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, OwnerPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerPawn_MetaData), NewProp_OwnerPawn_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OwnerController = { "OwnerController", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronRealmTransitionComponent, OwnerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnerController_MetaData), NewProp_OwnerController_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bAutoConfigureTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_DefaultTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_bIsTransitioning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionSourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionSourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionTargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionTargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_TransitionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CurrentTransitionPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_EffectComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_EffectComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_AudioComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_AudioComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OriginalCameraFOV,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CachedRealmSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_CachedLayerComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OwnerPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::NewProp_OwnerController,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::ClassParams = {
	&UAuracronRealmTransitionComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent()
{
	if (!Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.OuterSingleton, Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronRealmTransitionComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronRealmTransitionComponent);
UAuracronRealmTransitionComponent::~UAuracronRealmTransitionComponent() {}
// ********** End Class UAuracronRealmTransitionComponent ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTransitionEffects::StaticStruct, Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics::NewStructOps, TEXT("AuracronTransitionEffects"), &Z_Registration_Info_UScriptStruct_FAuracronTransitionEffects, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTransitionEffects), 268295719U) },
		{ FAuracronTransitionPath::StaticStruct, Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics::NewStructOps, TEXT("AuracronTransitionPath"), &Z_Registration_Info_UScriptStruct_FAuracronTransitionPath, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTransitionPath), 3073015388U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronRealmTransitionComponent, UAuracronRealmTransitionComponent::StaticClass, TEXT("UAuracronRealmTransitionComponent"), &Z_Registration_Info_UClass_UAuracronRealmTransitionComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronRealmTransitionComponent), 1036618667U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_1970871488(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
