// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAdvancedNetworkingCoordinator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdvancedNetworkingCoordinator() {}

// ********** Begin Cross Module References ********************************************************
AURACRONANTICHEATBRIDGE_API UClass* Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAntiCheatValidation();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APlayerController_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
IRISCORE_API UClass* Z_Construct_UClass_UNetObjectFactory_NoRegister();
IRISCORE_API UClass* Z_Construct_UClass_UNetObjectFilter_NoRegister();
IRISCORE_API UClass* Z_Construct_UClass_UNetObjectPrioritizer_NoRegister();
IRISCORE_API UClass* Z_Construct_UClass_UReplicationBridge_NoRegister();
IRISCORE_API UClass* Z_Construct_UClass_UReplicationSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronNetworkingBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ENetworkOptimizationStrategy **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ENetworkOptimizationStrategy;
static UEnum* ENetworkOptimizationStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("ENetworkOptimizationStrategy"));
	}
	return Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<ENetworkOptimizationStrategy>()
{
	return ENetworkOptimizationStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "ENetworkOptimizationStrategy::Adaptive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "ENetworkOptimizationStrategy::Balanced" },
		{ "Bandwidth.DisplayName", "Bandwidth Optimized" },
		{ "Bandwidth.Name", "ENetworkOptimizationStrategy::Bandwidth" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Network optimization strategies\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "ENetworkOptimizationStrategy::Custom" },
		{ "Latency.DisplayName", "Latency Optimized" },
		{ "Latency.Name", "ENetworkOptimizationStrategy::Latency" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
		{ "Reliability.DisplayName", "Reliability Optimized" },
		{ "Reliability.Name", "ENetworkOptimizationStrategy::Reliability" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network optimization strategies" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ENetworkOptimizationStrategy::Latency", (int64)ENetworkOptimizationStrategy::Latency },
		{ "ENetworkOptimizationStrategy::Bandwidth", (int64)ENetworkOptimizationStrategy::Bandwidth },
		{ "ENetworkOptimizationStrategy::Reliability", (int64)ENetworkOptimizationStrategy::Reliability },
		{ "ENetworkOptimizationStrategy::Balanced", (int64)ENetworkOptimizationStrategy::Balanced },
		{ "ENetworkOptimizationStrategy::Adaptive", (int64)ENetworkOptimizationStrategy::Adaptive },
		{ "ENetworkOptimizationStrategy::Custom", (int64)ENetworkOptimizationStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"ENetworkOptimizationStrategy",
	"ENetworkOptimizationStrategy",
	Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy()
{
	if (!Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ENetworkOptimizationStrategy.InnerSingleton;
}
// ********** End Enum ENetworkOptimizationStrategy ************************************************

// ********** Begin Enum EAuracronIrisReplicationMode **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronIrisReplicationMode;
static UEnum* EAuracronIrisReplicationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronIrisReplicationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronIrisReplicationMode>()
{
	return EAuracronIrisReplicationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Iris replication modes\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronIrisReplicationMode::Custom" },
		{ "HighFrequency.DisplayName", "High Frequency" },
		{ "HighFrequency.Name", "EAuracronIrisReplicationMode::HighFrequency" },
		{ "LowLatency.DisplayName", "Low Latency" },
		{ "LowLatency.Name", "EAuracronIrisReplicationMode::LowLatency" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
		{ "Optimized.DisplayName", "Optimized" },
		{ "Optimized.Name", "EAuracronIrisReplicationMode::Optimized" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EAuracronIrisReplicationMode::Standard" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronIrisReplicationMode::Standard", (int64)EAuracronIrisReplicationMode::Standard },
		{ "EAuracronIrisReplicationMode::HighFrequency", (int64)EAuracronIrisReplicationMode::HighFrequency },
		{ "EAuracronIrisReplicationMode::LowLatency", (int64)EAuracronIrisReplicationMode::LowLatency },
		{ "EAuracronIrisReplicationMode::Optimized", (int64)EAuracronIrisReplicationMode::Optimized },
		{ "EAuracronIrisReplicationMode::Custom", (int64)EAuracronIrisReplicationMode::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronIrisReplicationMode",
	"EAuracronIrisReplicationMode",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronIrisReplicationMode.InnerSingleton;
}
// ********** End Enum EAuracronIrisReplicationMode ************************************************

// ********** Begin Enum EAuracronAntiCheatLevel ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAntiCheatLevel;
static UEnum* EAuracronAntiCheatLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronAntiCheatLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronAntiCheatLevel>()
{
	return EAuracronAntiCheatLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EAuracronAntiCheatLevel::Advanced" },
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EAuracronAntiCheatLevel::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Anti-cheat validation levels\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
		{ "Paranoid.DisplayName", "Paranoid" },
		{ "Paranoid.Name", "EAuracronAntiCheatLevel::Paranoid" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EAuracronAntiCheatLevel::Standard" },
		{ "Strict.DisplayName", "Strict" },
		{ "Strict.Name", "EAuracronAntiCheatLevel::Strict" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat validation levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAntiCheatLevel::Basic", (int64)EAuracronAntiCheatLevel::Basic },
		{ "EAuracronAntiCheatLevel::Standard", (int64)EAuracronAntiCheatLevel::Standard },
		{ "EAuracronAntiCheatLevel::Advanced", (int64)EAuracronAntiCheatLevel::Advanced },
		{ "EAuracronAntiCheatLevel::Strict", (int64)EAuracronAntiCheatLevel::Strict },
		{ "EAuracronAntiCheatLevel::Paranoid", (int64)EAuracronAntiCheatLevel::Paranoid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronAntiCheatLevel",
	"EAuracronAntiCheatLevel",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAntiCheatLevel.InnerSingleton;
}
// ********** End Enum EAuracronAntiCheatLevel *****************************************************

// ********** Begin Enum EAuracronSessionType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSessionType;
static UEnum* EAuracronSessionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("EAuracronSessionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSessionType.OuterSingleton;
}
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronSessionType>()
{
	return EAuracronSessionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Multiplayer session types\n */" },
#endif
		{ "Competitive.DisplayName", "Competitive" },
		{ "Competitive.Name", "EAuracronSessionType::Competitive" },
		{ "Cooperative.DisplayName", "Cooperative" },
		{ "Cooperative.Name", "EAuracronSessionType::Cooperative" },
		{ "Event.DisplayName", "Event" },
		{ "Event.Name", "EAuracronSessionType::Event" },
		{ "Guild.DisplayName", "Guild" },
		{ "Guild.Name", "EAuracronSessionType::Guild" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
		{ "Realm.DisplayName", "Realm" },
		{ "Realm.Name", "EAuracronSessionType::Realm" },
		{ "Solo.DisplayName", "Solo" },
		{ "Solo.Name", "EAuracronSessionType::Solo" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplayer session types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSessionType::Solo", (int64)EAuracronSessionType::Solo },
		{ "EAuracronSessionType::Cooperative", (int64)EAuracronSessionType::Cooperative },
		{ "EAuracronSessionType::Competitive", (int64)EAuracronSessionType::Competitive },
		{ "EAuracronSessionType::Realm", (int64)EAuracronSessionType::Realm },
		{ "EAuracronSessionType::Guild", (int64)EAuracronSessionType::Guild },
		{ "EAuracronSessionType::Event", (int64)EAuracronSessionType::Event },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	"EAuracronSessionType",
	"EAuracronSessionType",
	Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton, Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSessionType.InnerSingleton;
}
// ********** End Enum EAuracronSessionType ********************************************************

// ********** Begin ScriptStruct FAuracronNetworkQualityMetrics ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics;
class UScriptStruct* FAuracronNetworkQualityMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronNetworkQualityMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Network quality metrics\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network quality metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLatency_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Average latency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Average latency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PacketLossPercent_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Packet loss percentage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Packet loss percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Jitter_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jitter (latency variance) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jitter (latency variance)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BandwidthUtilization_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bandwidth utilization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bandwidth utilization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionStability_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Connection stability */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connection stability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationEfficiency_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Replication efficiency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replication efficiency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionAccuracy_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prediction accuracy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prediction accuracy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatConfidence_MetaData[] = {
		{ "Category", "Network Quality" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Anti-cheat confidence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat confidence" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLatency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PacketLossPercent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Jitter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BandwidthUtilization;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionStability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReplicationEfficiency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PredictionAccuracy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AntiCheatConfidence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronNetworkQualityMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_AverageLatency = { "AverageLatency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, AverageLatency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLatency_MetaData), NewProp_AverageLatency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_PacketLossPercent = { "PacketLossPercent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, PacketLossPercent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PacketLossPercent_MetaData), NewProp_PacketLossPercent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_Jitter = { "Jitter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, Jitter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Jitter_MetaData), NewProp_Jitter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_BandwidthUtilization = { "BandwidthUtilization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, BandwidthUtilization), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BandwidthUtilization_MetaData), NewProp_BandwidthUtilization_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_ConnectionStability = { "ConnectionStability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, ConnectionStability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionStability_MetaData), NewProp_ConnectionStability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_ReplicationEfficiency = { "ReplicationEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, ReplicationEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationEfficiency_MetaData), NewProp_ReplicationEfficiency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_PredictionAccuracy = { "PredictionAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, PredictionAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionAccuracy_MetaData), NewProp_PredictionAccuracy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_AntiCheatConfidence = { "AntiCheatConfidence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkQualityMetrics, AntiCheatConfidence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatConfidence_MetaData), NewProp_AntiCheatConfidence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_AverageLatency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_PacketLossPercent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_Jitter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_BandwidthUtilization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_ConnectionStability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_ReplicationEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_PredictionAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewProp_AntiCheatConfidence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronNetworkQualityMetrics",
	Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::PropPointers),
	sizeof(FAuracronNetworkQualityMetrics),
	alignof(FAuracronNetworkQualityMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronNetworkQualityMetrics **************************************

// ********** Begin ScriptStruct FAuracronAdvancedReplicationPriority ******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority;
class UScriptStruct* FAuracronAdvancedReplicationPriority::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronAdvancedReplicationPriority"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Advanced replication priority configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced replication priority configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Actor class */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor class" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BasePriority_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base priority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base priority" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceFactor_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Distance factor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance factor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelevancyFactor_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Relevancy factor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Relevancy factor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateFrequencyMultiplier_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update frequency multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update frequency multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptivePriority_MetaData[] = {
		{ "Category", "Replication Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable adaptive priority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable adaptive priority" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BasePriority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RelevancyFactor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateFrequencyMultiplier;
	static void NewProp_bEnableAdaptivePriority_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptivePriority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAdvancedReplicationPriority>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedReplicationPriority, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_BasePriority = { "BasePriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedReplicationPriority, BasePriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BasePriority_MetaData), NewProp_BasePriority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_DistanceFactor = { "DistanceFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedReplicationPriority, DistanceFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceFactor_MetaData), NewProp_DistanceFactor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_RelevancyFactor = { "RelevancyFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedReplicationPriority, RelevancyFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelevancyFactor_MetaData), NewProp_RelevancyFactor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_UpdateFrequencyMultiplier = { "UpdateFrequencyMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedReplicationPriority, UpdateFrequencyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateFrequencyMultiplier_MetaData), NewProp_UpdateFrequencyMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_bEnableAdaptivePriority_SetBit(void* Obj)
{
	((FAuracronAdvancedReplicationPriority*)Obj)->bEnableAdaptivePriority = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_bEnableAdaptivePriority = { "bEnableAdaptivePriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedReplicationPriority), &Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_bEnableAdaptivePriority_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptivePriority_MetaData), NewProp_bEnableAdaptivePriority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_BasePriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_DistanceFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_RelevancyFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_UpdateFrequencyMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewProp_bEnableAdaptivePriority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronAdvancedReplicationPriority",
	Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::PropPointers),
	sizeof(FAuracronAdvancedReplicationPriority),
	alignof(FAuracronAdvancedReplicationPriority),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAdvancedReplicationPriority ********************************

// ********** Begin ScriptStruct FAuracronNetworkPredictionConfig **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig;
class UScriptStruct* FAuracronNetworkPredictionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronNetworkPredictionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Network prediction configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network prediction configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableClientPrediction_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable client-side prediction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable client-side prediction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableServerReconciliation_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable server reconciliation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable server reconciliation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionBufferSize_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prediction buffer size */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prediction buffer size" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReconciliationThreshold_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reconciliation threshold */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reconciliation threshold" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LagCompensationWindow_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lag compensation window */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lag compensation window" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRollbackNetworking_MetaData[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable rollback networking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable rollback networking" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableClientPrediction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableClientPrediction;
	static void NewProp_bEnableServerReconciliation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableServerReconciliation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PredictionBufferSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReconciliationThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LagCompensationWindow;
	static void NewProp_bEnableRollbackNetworking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRollbackNetworking;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronNetworkPredictionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableClientPrediction_SetBit(void* Obj)
{
	((FAuracronNetworkPredictionConfig*)Obj)->bEnableClientPrediction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableClientPrediction = { "bEnableClientPrediction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkPredictionConfig), &Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableClientPrediction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableClientPrediction_MetaData), NewProp_bEnableClientPrediction_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableServerReconciliation_SetBit(void* Obj)
{
	((FAuracronNetworkPredictionConfig*)Obj)->bEnableServerReconciliation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableServerReconciliation = { "bEnableServerReconciliation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkPredictionConfig), &Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableServerReconciliation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableServerReconciliation_MetaData), NewProp_bEnableServerReconciliation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_PredictionBufferSize = { "PredictionBufferSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkPredictionConfig, PredictionBufferSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionBufferSize_MetaData), NewProp_PredictionBufferSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_ReconciliationThreshold = { "ReconciliationThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkPredictionConfig, ReconciliationThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReconciliationThreshold_MetaData), NewProp_ReconciliationThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_LagCompensationWindow = { "LagCompensationWindow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronNetworkPredictionConfig, LagCompensationWindow), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LagCompensationWindow_MetaData), NewProp_LagCompensationWindow_MetaData) };
void Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableRollbackNetworking_SetBit(void* Obj)
{
	((FAuracronNetworkPredictionConfig*)Obj)->bEnableRollbackNetworking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableRollbackNetworking = { "bEnableRollbackNetworking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronNetworkPredictionConfig), &Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableRollbackNetworking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRollbackNetworking_MetaData), NewProp_bEnableRollbackNetworking_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableClientPrediction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableServerReconciliation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_PredictionBufferSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_ReconciliationThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_LagCompensationWindow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewProp_bEnableRollbackNetworking,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronNetworkPredictionConfig",
	Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::PropPointers),
	sizeof(FAuracronNetworkPredictionConfig),
	alignof(FAuracronNetworkPredictionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronNetworkPredictionConfig ************************************

// ********** Begin ScriptStruct FAuracronIrisReplicationConfig ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig;
class UScriptStruct* FAuracronIrisReplicationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronIrisReplicationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Iris replication configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableIrisReplication_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Iris replication system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Iris replication system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationMode_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iris replication mode */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication mode" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxReplicationFrequency_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum replication frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum replication frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDeltaCompression_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delta compression enabled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delta compression enabled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableQuantization_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quantization enabled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quantization enabled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableConditionalReplication_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Conditional replication */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Conditional replication" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeReplicationGraph_MetaData[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Replication graph optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replication graph optimization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableIrisReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableIrisReplication;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReplicationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReplicationMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxReplicationFrequency;
	static void NewProp_bEnableDeltaCompression_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDeltaCompression;
	static void NewProp_bEnableQuantization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableQuantization;
	static void NewProp_bEnableConditionalReplication_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableConditionalReplication;
	static void NewProp_bOptimizeReplicationGraph_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeReplicationGraph;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronIrisReplicationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableIrisReplication_SetBit(void* Obj)
{
	((FAuracronIrisReplicationConfig*)Obj)->bEnableIrisReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableIrisReplication = { "bEnableIrisReplication", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronIrisReplicationConfig), &Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableIrisReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableIrisReplication_MetaData), NewProp_bEnableIrisReplication_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_ReplicationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_ReplicationMode = { "ReplicationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIrisReplicationConfig, ReplicationMode), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronIrisReplicationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationMode_MetaData), NewProp_ReplicationMode_MetaData) }; // 3713434061
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_MaxReplicationFrequency = { "MaxReplicationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIrisReplicationConfig, MaxReplicationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxReplicationFrequency_MetaData), NewProp_MaxReplicationFrequency_MetaData) };
void Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableDeltaCompression_SetBit(void* Obj)
{
	((FAuracronIrisReplicationConfig*)Obj)->bEnableDeltaCompression = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableDeltaCompression = { "bEnableDeltaCompression", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronIrisReplicationConfig), &Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableDeltaCompression_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDeltaCompression_MetaData), NewProp_bEnableDeltaCompression_MetaData) };
void Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableQuantization_SetBit(void* Obj)
{
	((FAuracronIrisReplicationConfig*)Obj)->bEnableQuantization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableQuantization = { "bEnableQuantization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronIrisReplicationConfig), &Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableQuantization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableQuantization_MetaData), NewProp_bEnableQuantization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableConditionalReplication_SetBit(void* Obj)
{
	((FAuracronIrisReplicationConfig*)Obj)->bEnableConditionalReplication = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableConditionalReplication = { "bEnableConditionalReplication", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronIrisReplicationConfig), &Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableConditionalReplication_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableConditionalReplication_MetaData), NewProp_bEnableConditionalReplication_MetaData) };
void Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bOptimizeReplicationGraph_SetBit(void* Obj)
{
	((FAuracronIrisReplicationConfig*)Obj)->bOptimizeReplicationGraph = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bOptimizeReplicationGraph = { "bOptimizeReplicationGraph", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronIrisReplicationConfig), &Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bOptimizeReplicationGraph_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeReplicationGraph_MetaData), NewProp_bOptimizeReplicationGraph_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableIrisReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_ReplicationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_ReplicationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_MaxReplicationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableDeltaCompression,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableQuantization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bEnableConditionalReplication,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewProp_bOptimizeReplicationGraph,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronIrisReplicationConfig",
	Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::PropPointers),
	sizeof(FAuracronIrisReplicationConfig),
	alignof(FAuracronIrisReplicationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronIrisReplicationConfig **************************************

// ********** Begin ScriptStruct FAuracronMultiplayerSessionConfig *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig;
class UScriptStruct* FAuracronMultiplayerSessionConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronMultiplayerSessionConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Multiplayer session configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplayer session configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionName_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Session name */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionType_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Session type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayers_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum players */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum players" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDedicatedServer_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable dedicated server */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable dedicated server" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAntiCheat_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable anti-cheat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatLevel_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Anti-cheat level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCrossPlatform_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable cross-platform */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable cross-platform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionSettings_MetaData[] = {
		{ "Category", "Session Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Session settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session settings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SessionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SessionType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayers;
	static void NewProp_bUseDedicatedServer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDedicatedServer;
	static void NewProp_bEnableAntiCheat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAntiCheat;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AntiCheatLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AntiCheatLevel;
	static void NewProp_bEnableCrossPlatform_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCrossPlatform;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionSettings_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SessionSettings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMultiplayerSessionConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionName = { "SessionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMultiplayerSessionConfig, SessionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionName_MetaData), NewProp_SessionName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionType = { "SessionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMultiplayerSessionConfig, SessionType), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronSessionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionType_MetaData), NewProp_SessionType_MetaData) }; // 2168713918
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_MaxPlayers = { "MaxPlayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMultiplayerSessionConfig, MaxPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayers_MetaData), NewProp_MaxPlayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bUseDedicatedServer_SetBit(void* Obj)
{
	((FAuracronMultiplayerSessionConfig*)Obj)->bUseDedicatedServer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bUseDedicatedServer = { "bUseDedicatedServer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMultiplayerSessionConfig), &Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bUseDedicatedServer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDedicatedServer_MetaData), NewProp_bUseDedicatedServer_MetaData) };
void Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableAntiCheat_SetBit(void* Obj)
{
	((FAuracronMultiplayerSessionConfig*)Obj)->bEnableAntiCheat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableAntiCheat = { "bEnableAntiCheat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMultiplayerSessionConfig), &Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableAntiCheat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAntiCheat_MetaData), NewProp_bEnableAntiCheat_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_AntiCheatLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_AntiCheatLevel = { "AntiCheatLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMultiplayerSessionConfig, AntiCheatLevel), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatLevel_MetaData), NewProp_AntiCheatLevel_MetaData) }; // 1227517794
void Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableCrossPlatform_SetBit(void* Obj)
{
	((FAuracronMultiplayerSessionConfig*)Obj)->bEnableCrossPlatform = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableCrossPlatform = { "bEnableCrossPlatform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronMultiplayerSessionConfig), &Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableCrossPlatform_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCrossPlatform_MetaData), NewProp_bEnableCrossPlatform_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings_ValueProp = { "SessionSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings_Key_KeyProp = { "SessionSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings = { "SessionSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMultiplayerSessionConfig, SessionSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionSettings_MetaData), NewProp_SessionSettings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_MaxPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bUseDedicatedServer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableAntiCheat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_AntiCheatLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_AntiCheatLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_bEnableCrossPlatform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewProp_SessionSettings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronMultiplayerSessionConfig",
	Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::PropPointers),
	sizeof(FAuracronMultiplayerSessionConfig),
	alignof(FAuracronMultiplayerSessionConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMultiplayerSessionConfig ***********************************

// ********** Begin ScriptStruct FAuracronAntiCheatValidation **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation;
class UScriptStruct* FAuracronAntiCheatValidation::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAntiCheatValidation, (UObject*)Z_Construct_UPackage__Script_AuracronNetworkingBridge(), TEXT("AuracronAntiCheatValidation"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Anti-cheat validation data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat validation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player ID */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationType_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validation type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bValidationPassed_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validation result */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConfidenceScore_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Confidence score */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Confidence score" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationData_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validation data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationTimestamp_MetaData[] = {
		{ "Category", "Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timestamp */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timestamp" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationType;
	static void NewProp_bValidationPassed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bValidationPassed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConfidenceScore;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ValidationData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ValidationData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidationTimestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAntiCheatValidation>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatValidation, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationType = { "ValidationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatValidation, ValidationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationType_MetaData), NewProp_ValidationType_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_bValidationPassed_SetBit(void* Obj)
{
	((FAuracronAntiCheatValidation*)Obj)->bValidationPassed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_bValidationPassed = { "bValidationPassed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAntiCheatValidation), &Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_bValidationPassed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bValidationPassed_MetaData), NewProp_bValidationPassed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ConfidenceScore = { "ConfidenceScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatValidation, ConfidenceScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConfidenceScore_MetaData), NewProp_ConfidenceScore_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData_ValueProp = { "ValidationData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData_Key_KeyProp = { "ValidationData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData = { "ValidationData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatValidation, ValidationData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationData_MetaData), NewProp_ValidationData_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationTimestamp = { "ValidationTimestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAntiCheatValidation, ValidationTimestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationTimestamp_MetaData), NewProp_ValidationTimestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_bValidationPassed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ConfidenceScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewProp_ValidationTimestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
	nullptr,
	&NewStructOps,
	"AuracronAntiCheatValidation",
	Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::PropPointers),
	sizeof(FAuracronAntiCheatValidation),
	alignof(FAuracronAntiCheatValidation),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAntiCheatValidation()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAntiCheatValidation ****************************************

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ApplyLagCompensationForAction 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms
	{
		APlayerController* PlayerController;
		FString ActionType;
		float Timestamp;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lag Compensation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply lag compensation for action */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply lag compensation for action" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ApplyLagCompensationForAction", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::AuracronAdvancedNetworkingCoordinator_eventApplyLagCompensationForAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execApplyLagCompensationForAction)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyLagCompensationForAction(Z_Param_PlayerController,Z_Param_ActionType,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ApplyLagCompensationForAction 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function BanPlayerWithEvidence ****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms
	{
		FString PlayerID;
		FString Reason;
		TArray<FString> Evidence;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ban player with evidence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ban player with evidence" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Evidence_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Evidence_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Evidence;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Evidence_Inner = { "Evidence", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Evidence = { "Evidence", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms, Evidence), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Evidence_MetaData), NewProp_Evidence_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Reason,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Evidence_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_Evidence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "BanPlayerWithEvidence", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::AuracronAdvancedNetworkingCoordinator_eventBanPlayerWithEvidence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execBanPlayerWithEvidence)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_GET_TARRAY_REF(FString,Z_Param_Out_Evidence);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BanPlayerWithEvidence(Z_Param_PlayerID,Z_Param_Reason,Z_Param_Out_Evidence);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function BanPlayerWithEvidence ******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ConfigureIrisReplication *
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventConfigureIrisReplication_Parms
	{
		FAuracronIrisReplicationConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configure Iris replication */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure Iris replication" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventConfigureIrisReplication_Parms, Config), Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1594044851
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ConfigureIrisReplication", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::AuracronAdvancedNetworkingCoordinator_eventConfigureIrisReplication_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::AuracronAdvancedNetworkingCoordinator_eventConfigureIrisReplication_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execConfigureIrisReplication)
{
	P_GET_STRUCT_REF(FAuracronIrisReplicationConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureIrisReplication(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ConfigureIrisReplication ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ConfigureLagCompensation *
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventConfigureLagCompensation_Parms
	{
		FAuracronNetworkPredictionConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lag Compensation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configure lag compensation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configure lag compensation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventConfigureLagCompensation_Parms, Config), Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 2465755286
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ConfigureLagCompensation", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::AuracronAdvancedNetworkingCoordinator_eventConfigureLagCompensation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::AuracronAdvancedNetworkingCoordinator_eventConfigureLagCompensation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execConfigureLagCompensation)
{
	P_GET_STRUCT_REF(FAuracronNetworkPredictionConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConfigureLagCompensation(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ConfigureLagCompensation ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function CoordinateAntiCheatSystems 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Anti-Cheat Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Coordinate anti-cheat systems */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coordinate anti-cheat systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "CoordinateAntiCheatSystems", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execCoordinateAntiCheatSystems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CoordinateAntiCheatSystems();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function CoordinateAntiCheatSystems *

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function CreateMultiplayerSession *
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms
	{
		FAuracronMultiplayerSessionConfig SessionConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create multiplayer session" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms, SessionConfig), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 729078944
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_SessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "CreateMultiplayerSession", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::AuracronAdvancedNetworkingCoordinator_eventCreateMultiplayerSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execCreateMultiplayerSession)
{
	P_GET_STRUCT_REF(FAuracronMultiplayerSessionConfig,Z_Param_Out_SessionConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateMultiplayerSession(Z_Param_Out_SessionConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function CreateMultiplayerSession ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function EnableAuthoritativeModeForSystem 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventEnableAuthoritativeModeForSystem_Parms
	{
		FString SystemName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Authoritative Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable authoritative mode for system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable authoritative mode for system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SystemName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::NewProp_SystemName = { "SystemName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventEnableAuthoritativeModeForSystem_Parms, SystemName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemName_MetaData), NewProp_SystemName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::NewProp_SystemName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "EnableAuthoritativeModeForSystem", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::AuracronAdvancedNetworkingCoordinator_eventEnableAuthoritativeModeForSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::AuracronAdvancedNetworkingCoordinator_eventEnableAuthoritativeModeForSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execEnableAuthoritativeModeForSystem)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SystemName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableAuthoritativeModeForSystem(Z_Param_SystemName);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function EnableAuthoritativeModeForSystem 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function EnableNetworkPredictionForActor 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventEnableNetworkPredictionForActor_Parms
	{
		AActor* Actor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable network prediction for actor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable network prediction for actor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventEnableNetworkPredictionForActor_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::NewProp_Actor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "EnableNetworkPredictionForActor", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::AuracronAdvancedNetworkingCoordinator_eventEnableNetworkPredictionForActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::AuracronAdvancedNetworkingCoordinator_eventEnableNetworkPredictionForActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execEnableNetworkPredictionForActor)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableNetworkPredictionForActor(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function EnableNetworkPredictionForActor 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ForceServerReconciliation 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventForceServerReconciliation_Parms
	{
		FString PlayerID;
		FString SystemName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Authoritative Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Force server reconciliation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Force server reconciliation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SystemName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventForceServerReconciliation_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::NewProp_SystemName = { "SystemName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventForceServerReconciliation_Parms, SystemName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemName_MetaData), NewProp_SystemName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::NewProp_SystemName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ForceServerReconciliation", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::AuracronAdvancedNetworkingCoordinator_eventForceServerReconciliation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::AuracronAdvancedNetworkingCoordinator_eventForceServerReconciliation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execForceServerReconciliation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_SystemName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceServerReconciliation(Z_Param_PlayerID,Z_Param_SystemName);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ForceServerReconciliation **

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function GetActiveSessionInfo *****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventGetActiveSessionInfo_Parms
	{
		FAuracronMultiplayerSessionConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get active session info */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get active session info" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventGetActiveSessionInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(0, nullptr) }; // 729078944
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "GetActiveSessionInfo", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::AuracronAdvancedNetworkingCoordinator_eventGetActiveSessionInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::AuracronAdvancedNetworkingCoordinator_eventGetActiveSessionInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execGetActiveSessionInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMultiplayerSessionConfig*)Z_Param__Result=P_THIS->GetActiveSessionInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function GetActiveSessionInfo *******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function GetIrisReplicationMetrics 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventGetIrisReplicationMetrics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get Iris replication metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get Iris replication metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventGetIrisReplicationMetrics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "GetIrisReplicationMetrics", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::AuracronAdvancedNetworkingCoordinator_eventGetIrisReplicationMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::AuracronAdvancedNetworkingCoordinator_eventGetIrisReplicationMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execGetIrisReplicationMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetIrisReplicationMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function GetIrisReplicationMetrics **

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function GetNetworkQualityMetrics *
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventGetNetworkQualityMetrics_Parms
	{
		FAuracronNetworkQualityMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Coordination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get network quality metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get network quality metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventGetNetworkQualityMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(0, nullptr) }; // 1513915643
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "GetNetworkQualityMetrics", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::AuracronAdvancedNetworkingCoordinator_eventGetNetworkQualityMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::AuracronAdvancedNetworkingCoordinator_eventGetNetworkQualityMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execGetNetworkQualityMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronNetworkQualityMetrics*)Z_Param__Result=P_THIS->GetNetworkQualityMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function GetNetworkQualityMetrics ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function InitializeAdvancedAntiCheat 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms
	{
		EAuracronAntiCheatLevel Level;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize advanced anti-cheat */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize advanced anti-cheat" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Level_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Level;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_Level_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms, Level), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel, METADATA_PARAMS(0, nullptr) }; // 1227517794
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_Level_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "InitializeAdvancedAntiCheat", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::AuracronAdvancedNetworkingCoordinator_eventInitializeAdvancedAntiCheat_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execInitializeAdvancedAntiCheat)
{
	P_GET_ENUM(EAuracronAntiCheatLevel,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeAdvancedAntiCheat(EAuracronAntiCheatLevel(Z_Param_Level));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function InitializeAdvancedAntiCheat 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function InitializeIrisReplicationSystem 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventInitializeIrisReplicationSystem_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize Iris replication system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Iris replication system" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventInitializeIrisReplicationSystem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventInitializeIrisReplicationSystem_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "InitializeIrisReplicationSystem", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::AuracronAdvancedNetworkingCoordinator_eventInitializeIrisReplicationSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::AuracronAdvancedNetworkingCoordinator_eventInitializeIrisReplicationSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execInitializeIrisReplicationSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeIrisReplicationSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function InitializeIrisReplicationSystem 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function InitializeNetworkingCoordinator 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Coordination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize networking coordinator */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize networking coordinator" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "InitializeNetworkingCoordinator", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execInitializeNetworkingCoordinator)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeNetworkingCoordinator();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function InitializeNetworkingCoordinator 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function JoinMultiplayerSession ***
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms
	{
		FString SessionID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Join multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Join multiplayer session" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "JoinMultiplayerSession", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::AuracronAdvancedNetworkingCoordinator_eventJoinMultiplayerSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execJoinMultiplayerSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->JoinMultiplayerSession(Z_Param_SessionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function JoinMultiplayerSession *****

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function LeaveMultiplayerSession **
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Leave multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Leave multiplayer session" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "LeaveMultiplayerSession", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execLeaveMultiplayerSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LeaveMultiplayerSession();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function LeaveMultiplayerSession ****

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function MonitorBandwidthUsage ****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bandwidth Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Monitor bandwidth usage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitor bandwidth usage" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "MonitorBandwidthUsage", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execMonitorBandwidthUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MonitorBandwidthUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function MonitorBandwidthUsage ******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnAntiCheatViolationDetected 
struct AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms
{
	FString PlayerID;
	FString ViolationType;
	float SeverityLevel;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected = FName(TEXT("OnAntiCheatViolationDetected"));
void UAuracronAdvancedNetworkingCoordinator::OnAntiCheatViolationDetected(const FString& PlayerID, const FString& ViolationType, float SeverityLevel)
{
	AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.ViolationType=ViolationType;
	Parms.SeverityLevel=SeverityLevel;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when anti-cheat violation is detected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when anti-cheat violation is detected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViolationType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ViolationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SeverityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_ViolationType = { "ViolationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms, ViolationType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViolationType_MetaData), NewProp_ViolationType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_SeverityLevel = { "SeverityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms, SeverityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_ViolationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::NewProp_SeverityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnAntiCheatViolationDetected", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnAntiCheatViolationDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnAntiCheatViolationDetected 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnIrisReplicationOptimized 
struct AuracronAdvancedNetworkingCoordinator_eventOnIrisReplicationOptimized_Parms
{
	float PerformanceImprovement;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized = FName(TEXT("OnIrisReplicationOptimized"));
void UAuracronAdvancedNetworkingCoordinator::OnIrisReplicationOptimized(float PerformanceImprovement)
{
	AuracronAdvancedNetworkingCoordinator_eventOnIrisReplicationOptimized_Parms Parms;
	Parms.PerformanceImprovement=PerformanceImprovement;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when Iris replication is optimized */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when Iris replication is optimized" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceImprovement;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::NewProp_PerformanceImprovement = { "PerformanceImprovement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnIrisReplicationOptimized_Parms, PerformanceImprovement), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::NewProp_PerformanceImprovement,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnIrisReplicationOptimized", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnIrisReplicationOptimized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnIrisReplicationOptimized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnIrisReplicationOptimized *

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnMultiplayerSessionCreated 
struct AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms
{
	FString SessionID;
	FAuracronMultiplayerSessionConfig SessionConfig;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated = FName(TEXT("OnMultiplayerSessionCreated"));
void UAuracronAdvancedNetworkingCoordinator::OnMultiplayerSessionCreated(const FString& SessionID, FAuracronMultiplayerSessionConfig const& SessionConfig)
{
	AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms Parms;
	Parms.SessionID=SessionID;
	Parms.SessionConfig=SessionConfig;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when multiplayer session is created */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when multiplayer session is created" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms, SessionConfig), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 729078944
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::NewProp_SessionConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnMultiplayerSessionCreated", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnMultiplayerSessionCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnMultiplayerSessionCreated 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkAnomalyDetected *
struct AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms
{
	APlayerController* PlayerController;
	FString AnomalyType;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected = FName(TEXT("OnNetworkAnomalyDetected"));
void UAuracronAdvancedNetworkingCoordinator::OnNetworkAnomalyDetected(APlayerController* PlayerController, const FString& AnomalyType)
{
	AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms Parms;
	Parms.PlayerController=PlayerController;
	Parms.AnomalyType=AnomalyType;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when network anomaly is detected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when network anomaly is detected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnomalyType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnomalyType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::NewProp_AnomalyType = { "AnomalyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms, AnomalyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnomalyType_MetaData), NewProp_AnomalyType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::NewProp_AnomalyType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnNetworkAnomalyDetected", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkAnomalyDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkAnomalyDetected ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkOptimizationApplied 
struct AuracronAdvancedNetworkingCoordinator_eventOnNetworkOptimizationApplied_Parms
{
	ENetworkOptimizationStrategy Strategy;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied = FName(TEXT("OnNetworkOptimizationApplied"));
void UAuracronAdvancedNetworkingCoordinator::OnNetworkOptimizationApplied(ENetworkOptimizationStrategy Strategy)
{
	AuracronAdvancedNetworkingCoordinator_eventOnNetworkOptimizationApplied_Parms Parms;
	Parms.Strategy=Strategy;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when optimization is applied */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when optimization is applied" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Strategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Strategy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::NewProp_Strategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::NewProp_Strategy = { "Strategy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnNetworkOptimizationApplied_Parms, Strategy), Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy, METADATA_PARAMS(0, nullptr) }; // 3255578726
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::NewProp_Strategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::NewProp_Strategy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnNetworkOptimizationApplied", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkOptimizationApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkOptimizationApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkOptimizationApplied 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkQualityChanged **
struct AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms
{
	FAuracronNetworkQualityMetrics OldMetrics;
	FAuracronNetworkQualityMetrics NewMetrics;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged = FName(TEXT("OnNetworkQualityChanged"));
void UAuracronAdvancedNetworkingCoordinator::OnNetworkQualityChanged(FAuracronNetworkQualityMetrics const& OldMetrics, FAuracronNetworkQualityMetrics const& NewMetrics)
{
	AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms Parms;
	Parms.OldMetrics=OldMetrics;
	Parms.NewMetrics=NewMetrics;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when network quality changes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when network quality changes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OldMetrics_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewMetrics_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OldMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::NewProp_OldMetrics = { "OldMetrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms, OldMetrics), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OldMetrics_MetaData), NewProp_OldMetrics_MetaData) }; // 1513915643
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::NewProp_NewMetrics = { "NewMetrics", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms, NewMetrics), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewMetrics_MetaData), NewProp_NewMetrics_MetaData) }; // 1513915643
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::NewProp_OldMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::NewProp_NewMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnNetworkQualityChanged", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnNetworkQualityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnNetworkQualityChanged ****

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnPlayerJoinedSession ****
struct AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms
{
	FString PlayerID;
	FString SessionID;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession = FName(TEXT("OnPlayerJoinedSession"));
void UAuracronAdvancedNetworkingCoordinator::OnPlayerJoinedSession(const FString& PlayerID, const FString& SessionID)
{
	AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SessionID=SessionID;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when player joins session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when player joins session" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::NewProp_SessionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnPlayerJoinedSession", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnPlayerJoinedSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnPlayerJoinedSession ******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OnServerReconciliationTriggered 
struct AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms
{
	FString PlayerID;
	FString SystemName;
};
static FName NAME_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered = FName(TEXT("OnServerReconciliationTriggered"));
void UAuracronAdvancedNetworkingCoordinator::OnServerReconciliationTriggered(const FString& PlayerID, const FString& SystemName)
{
	AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.SystemName=SystemName;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when server reconciliation occurs */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when server reconciliation occurs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SystemName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::NewProp_SystemName = { "SystemName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms, SystemName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemName_MetaData), NewProp_SystemName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::NewProp_SystemName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OnServerReconciliationTriggered", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::PropPointers), sizeof(AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedNetworkingCoordinator_eventOnServerReconciliationTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OnServerReconciliationTriggered 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OptimizeBandwidthAllocation 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bandwidth Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize bandwidth allocation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize bandwidth allocation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OptimizeBandwidthAllocation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execOptimizeBandwidthAllocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeBandwidthAllocation();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OptimizeBandwidthAllocation 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OptimizeIrisReplicationGraph 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize Iris replication graph */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize Iris replication graph" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OptimizeIrisReplicationGraph", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execOptimizeIrisReplicationGraph)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeIrisReplicationGraph();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OptimizeIrisReplicationGraph 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OptimizeNetworkPerformance 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Coordination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize network performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize network performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OptimizeNetworkPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execOptimizeNetworkPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeNetworkPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OptimizeNetworkPerformance *

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function OptimizeReplicationForPlayer 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventOptimizeReplicationForPlayer_Parms
	{
		APlayerController* PlayerController;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Replication Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize replication for player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize replication for player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventOptimizeReplicationForPlayer_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::NewProp_PlayerController,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "OptimizeReplicationForPlayer", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::AuracronAdvancedNetworkingCoordinator_eventOptimizeReplicationForPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::AuracronAdvancedNetworkingCoordinator_eventOptimizeReplicationForPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execOptimizeReplicationForPlayer)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeReplicationForPlayer(Z_Param_PlayerController);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function OptimizeReplicationForPlayer 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ReconcileClientPrediction 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms
	{
		APlayerController* PlayerController;
		FVector ServerLocation;
		float Timestamp;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reconcile client prediction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reconcile client prediction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ServerLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_ServerLocation = { "ServerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms, ServerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerLocation_MetaData), NewProp_ServerLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_ServerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ReconcileClientPrediction", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::AuracronAdvancedNetworkingCoordinator_eventReconcileClientPrediction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execReconcileClientPrediction)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ServerLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReconcileClientPrediction(Z_Param_PlayerController,Z_Param_Out_ServerLocation,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ReconcileClientPrediction **

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function RegisterObjectForIrisReplication 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms
	{
		UObject* Object;
		FString ObjectName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Iris Replication" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Register object for Iris replication */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Register object for Iris replication" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms, Object), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ObjectName = { "ObjectName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms, ObjectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectName_MetaData), NewProp_ObjectName_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_Object,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ObjectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "RegisterObjectForIrisReplication", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::AuracronAdvancedNetworkingCoordinator_eventRegisterObjectForIrisReplication_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execRegisterObjectForIrisReplication)
{
	P_GET_OBJECT(UObject,Z_Param_Object);
	P_GET_PROPERTY(FStrProperty,Z_Param_ObjectName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterObjectForIrisReplication(Z_Param_Object,Z_Param_ObjectName);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function RegisterObjectForIrisReplication 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ReportNetworkAnomaly *****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms
	{
		APlayerController* PlayerController;
		FString AnomalyType;
		TMap<FString,float> AnomalyData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Anti-Cheat Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Report network anomaly */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Report network anomaly" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnomalyType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnomalyData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnomalyType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnomalyData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnomalyData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AnomalyData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyType = { "AnomalyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms, AnomalyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnomalyType_MetaData), NewProp_AnomalyType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData_ValueProp = { "AnomalyData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData_Key_KeyProp = { "AnomalyData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData = { "AnomalyData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms, AnomalyData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnomalyData_MetaData), NewProp_AnomalyData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::NewProp_AnomalyData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ReportNetworkAnomaly", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::AuracronAdvancedNetworkingCoordinator_eventReportNetworkAnomaly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execReportNetworkAnomaly)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_PROPERTY(FStrProperty,Z_Param_AnomalyType);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_AnomalyData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportNetworkAnomaly(Z_Param_PlayerController,Z_Param_AnomalyType,Z_Param_Out_AnomalyData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ReportNetworkAnomaly *******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ReportSuspiciousActivity *
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms
	{
		FString PlayerID;
		FString ActivityType;
		float SuspicionLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Report suspicious activity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Report suspicious activity" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActivityType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuspicionLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_ActivityType = { "ActivityType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms, ActivityType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityType_MetaData), NewProp_ActivityType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_SuspicionLevel = { "SuspicionLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms, SuspicionLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_ActivityType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::NewProp_SuspicionLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ReportSuspiciousActivity", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::AuracronAdvancedNetworkingCoordinator_eventReportSuspiciousActivity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execReportSuspiciousActivity)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActivityType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_SuspicionLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportSuspiciousActivity(Z_Param_PlayerID,Z_Param_ActivityType,Z_Param_SuspicionLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ReportSuspiciousActivity ***

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function SetActorReplicationPriority 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventSetActorReplicationPriority_Parms
	{
		AActor* Actor;
		float Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Replication Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set actor replication priority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set actor replication priority" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSetActorReplicationPriority_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSetActorReplicationPriority_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "SetActorReplicationPriority", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::AuracronAdvancedNetworkingCoordinator_eventSetActorReplicationPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::AuracronAdvancedNetworkingCoordinator_eventSetActorReplicationPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execSetActorReplicationPriority)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActorReplicationPriority(Z_Param_Actor,Z_Param_Priority);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function SetActorReplicationPriority 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function SetPlayerBandwidthLimit **
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventSetPlayerBandwidthLimit_Parms
	{
		APlayerController* PlayerController;
		float BandwidthKbps;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bandwidth Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set bandwidth limit for player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set bandwidth limit for player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BandwidthKbps;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSetPlayerBandwidthLimit_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::NewProp_BandwidthKbps = { "BandwidthKbps", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSetPlayerBandwidthLimit_Parms, BandwidthKbps), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::NewProp_BandwidthKbps,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "SetPlayerBandwidthLimit", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::AuracronAdvancedNetworkingCoordinator_eventSetPlayerBandwidthLimit_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::AuracronAdvancedNetworkingCoordinator_eventSetPlayerBandwidthLimit_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execSetPlayerBandwidthLimit)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BandwidthKbps);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerBandwidthLimit(Z_Param_PlayerController,Z_Param_BandwidthKbps);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function SetPlayerBandwidthLimit ****

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function SetReplicationOptimizationStrategy 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventSetReplicationOptimizationStrategy_Parms
	{
		ENetworkOptimizationStrategy Strategy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Replication Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set replication optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set replication optimization strategy" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Strategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Strategy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::NewProp_Strategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::NewProp_Strategy = { "Strategy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSetReplicationOptimizationStrategy_Parms, Strategy), Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy, METADATA_PARAMS(0, nullptr) }; // 3255578726
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::NewProp_Strategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::NewProp_Strategy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "SetReplicationOptimizationStrategy", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::AuracronAdvancedNetworkingCoordinator_eventSetReplicationOptimizationStrategy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::AuracronAdvancedNetworkingCoordinator_eventSetReplicationOptimizationStrategy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execSetReplicationOptimizationStrategy)
{
	P_GET_ENUM(ENetworkOptimizationStrategy,Z_Param_Strategy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetReplicationOptimizationStrategy(ENetworkOptimizationStrategy(Z_Param_Strategy));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function SetReplicationOptimizationStrategy 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function SyncBridgeStateAcrossNetwork 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventSyncBridgeStateAcrossNetwork_Parms
	{
		FString BridgeName;
		TMap<FString,FString> BridgeState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Authoritative Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sync bridge state across network */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sync bridge state across network" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BridgeState_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BridgeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BridgeState_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BridgeState_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BridgeState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeName = { "BridgeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSyncBridgeStateAcrossNetwork_Parms, BridgeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeName_MetaData), NewProp_BridgeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState_ValueProp = { "BridgeState", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState_Key_KeyProp = { "BridgeState_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState = { "BridgeState", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventSyncBridgeStateAcrossNetwork_Parms, BridgeState), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BridgeState_MetaData), NewProp_BridgeState_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::NewProp_BridgeState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "SyncBridgeStateAcrossNetwork", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::AuracronAdvancedNetworkingCoordinator_eventSyncBridgeStateAcrossNetwork_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::AuracronAdvancedNetworkingCoordinator_eventSyncBridgeStateAcrossNetwork_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execSyncBridgeStateAcrossNetwork)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BridgeName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_BridgeState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SyncBridgeStateAcrossNetwork(Z_Param_BridgeName,Z_Param_Out_BridgeState);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function SyncBridgeStateAcrossNetwork 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function UpdateNetworkingCoordination 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventUpdateNetworkingCoordination_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Networking Coordination" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update networking coordination */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update networking coordination" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventUpdateNetworkingCoordination_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "UpdateNetworkingCoordination", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdateNetworkingCoordination_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdateNetworkingCoordination_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execUpdateNetworkingCoordination)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateNetworkingCoordination(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function UpdateNetworkingCoordination 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function UpdatePredictionBuffer ***
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms
	{
		APlayerController* PlayerController;
		FVector PredictedLocation;
		float Timestamp;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Network Prediction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update prediction buffer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update prediction buffer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictedLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredictedLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_PredictedLocation = { "PredictedLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms, PredictedLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictedLocation_MetaData), NewProp_PredictedLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_PredictedLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "UpdatePredictionBuffer", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdatePredictionBuffer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execUpdatePredictionBuffer)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PredictedLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePredictionBuffer(Z_Param_PlayerController,Z_Param_Out_PredictedLocation,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function UpdatePredictionBuffer *****

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function UpdateReplicationPriorities 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Replication Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update replication priorities */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update replication priorities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "UpdateReplicationPriorities", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execUpdateReplicationPriorities)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateReplicationPriorities();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function UpdateReplicationPriorities 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function UpdateSessionSettings ****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms
	{
		TMap<FString,FString> NewSettings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Sessions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update session settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update session settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewSettings_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_NewSettings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings_ValueProp = { "NewSettings", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings_Key_KeyProp = { "NewSettings_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings = { "NewSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms, NewSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewSettings_MetaData), NewProp_NewSettings_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_NewSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "UpdateSessionSettings", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::AuracronAdvancedNetworkingCoordinator_eventUpdateSessionSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execUpdateSessionSettings)
{
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_NewSettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateSessionSettings(Z_Param_Out_NewSettings);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function UpdateSessionSettings ******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ValidateClientActionWithLagCompensation 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms
	{
		APlayerController* PlayerController;
		FVector ActionLocation;
		float Timestamp;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lag Compensation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate client action with lag compensation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate client action with lag compensation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ActionLocation = { "ActionLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms, ActionLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionLocation_MetaData), NewProp_ActionLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ActionLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ValidateClientActionWithLagCompensation", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateClientActionWithLagCompensation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execValidateClientActionWithLagCompensation)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ActionLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateClientActionWithLagCompensation(Z_Param_PlayerController,Z_Param_Out_ActionLocation,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ValidateClientActionWithLagCompensation 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ValidateClientStateAgainstServer 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms
	{
		FString PlayerID;
		TMap<FString,FString> ClientState;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Authoritative Networking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate client state against server */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate client state against server" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClientState_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClientState_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClientState_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ClientState;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState_ValueProp = { "ClientState", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState_Key_KeyProp = { "ClientState_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState = { "ClientState", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms, ClientState), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClientState_MetaData), NewProp_ClientState_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ClientState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ValidateClientStateAgainstServer", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateClientStateAgainstServer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execValidateClientStateAgainstServer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ClientState);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateClientStateAgainstServer(Z_Param_PlayerID,Z_Param_Out_ClientState);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ValidateClientStateAgainstServer 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ValidateNetworkAction ****
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms
	{
		APlayerController* PlayerController;
		FString ActionType;
		TMap<FString,FString> ActionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Anti-Cheat Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate network action */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate network action" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms, PlayerController), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
void Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms), &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ActionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ValidateNetworkAction", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::AuracronAdvancedNetworkingCoordinator_eventValidateNetworkAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execValidateNetworkAction)
{
	P_GET_OBJECT(APlayerController,Z_Param_PlayerController);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateNetworkAction(Z_Param_PlayerController,Z_Param_ActionType,Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ValidateNetworkAction ******

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator Function ValidatePlayerActionServerSide 
struct Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics
{
	struct AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms
	{
		FString PlayerID;
		FString ActionType;
		TMap<FString,FString> ActionData;
		FAuracronAntiCheatValidation ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Anti-Cheat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate player action server-side */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate player action server-side" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAntiCheatValidation, METADATA_PARAMS(0, nullptr) }; // 3063857892
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ActionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, nullptr, "ValidatePlayerActionServerSide", Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::AuracronAdvancedNetworkingCoordinator_eventValidatePlayerActionServerSide_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedNetworkingCoordinator::execValidatePlayerActionServerSide)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAntiCheatValidation*)Z_Param__Result=P_THIS->ValidatePlayerActionServerSide(Z_Param_PlayerID,Z_Param_ActionType,Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedNetworkingCoordinator Function ValidatePlayerActionServerSide 

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator ***********************************
void UAuracronAdvancedNetworkingCoordinator::StaticRegisterNativesUAuracronAdvancedNetworkingCoordinator()
{
	UClass* Class = UAuracronAdvancedNetworkingCoordinator::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyLagCompensationForAction", &UAuracronAdvancedNetworkingCoordinator::execApplyLagCompensationForAction },
		{ "BanPlayerWithEvidence", &UAuracronAdvancedNetworkingCoordinator::execBanPlayerWithEvidence },
		{ "ConfigureIrisReplication", &UAuracronAdvancedNetworkingCoordinator::execConfigureIrisReplication },
		{ "ConfigureLagCompensation", &UAuracronAdvancedNetworkingCoordinator::execConfigureLagCompensation },
		{ "CoordinateAntiCheatSystems", &UAuracronAdvancedNetworkingCoordinator::execCoordinateAntiCheatSystems },
		{ "CreateMultiplayerSession", &UAuracronAdvancedNetworkingCoordinator::execCreateMultiplayerSession },
		{ "EnableAuthoritativeModeForSystem", &UAuracronAdvancedNetworkingCoordinator::execEnableAuthoritativeModeForSystem },
		{ "EnableNetworkPredictionForActor", &UAuracronAdvancedNetworkingCoordinator::execEnableNetworkPredictionForActor },
		{ "ForceServerReconciliation", &UAuracronAdvancedNetworkingCoordinator::execForceServerReconciliation },
		{ "GetActiveSessionInfo", &UAuracronAdvancedNetworkingCoordinator::execGetActiveSessionInfo },
		{ "GetIrisReplicationMetrics", &UAuracronAdvancedNetworkingCoordinator::execGetIrisReplicationMetrics },
		{ "GetNetworkQualityMetrics", &UAuracronAdvancedNetworkingCoordinator::execGetNetworkQualityMetrics },
		{ "InitializeAdvancedAntiCheat", &UAuracronAdvancedNetworkingCoordinator::execInitializeAdvancedAntiCheat },
		{ "InitializeIrisReplicationSystem", &UAuracronAdvancedNetworkingCoordinator::execInitializeIrisReplicationSystem },
		{ "InitializeNetworkingCoordinator", &UAuracronAdvancedNetworkingCoordinator::execInitializeNetworkingCoordinator },
		{ "JoinMultiplayerSession", &UAuracronAdvancedNetworkingCoordinator::execJoinMultiplayerSession },
		{ "LeaveMultiplayerSession", &UAuracronAdvancedNetworkingCoordinator::execLeaveMultiplayerSession },
		{ "MonitorBandwidthUsage", &UAuracronAdvancedNetworkingCoordinator::execMonitorBandwidthUsage },
		{ "OptimizeBandwidthAllocation", &UAuracronAdvancedNetworkingCoordinator::execOptimizeBandwidthAllocation },
		{ "OptimizeIrisReplicationGraph", &UAuracronAdvancedNetworkingCoordinator::execOptimizeIrisReplicationGraph },
		{ "OptimizeNetworkPerformance", &UAuracronAdvancedNetworkingCoordinator::execOptimizeNetworkPerformance },
		{ "OptimizeReplicationForPlayer", &UAuracronAdvancedNetworkingCoordinator::execOptimizeReplicationForPlayer },
		{ "ReconcileClientPrediction", &UAuracronAdvancedNetworkingCoordinator::execReconcileClientPrediction },
		{ "RegisterObjectForIrisReplication", &UAuracronAdvancedNetworkingCoordinator::execRegisterObjectForIrisReplication },
		{ "ReportNetworkAnomaly", &UAuracronAdvancedNetworkingCoordinator::execReportNetworkAnomaly },
		{ "ReportSuspiciousActivity", &UAuracronAdvancedNetworkingCoordinator::execReportSuspiciousActivity },
		{ "SetActorReplicationPriority", &UAuracronAdvancedNetworkingCoordinator::execSetActorReplicationPriority },
		{ "SetPlayerBandwidthLimit", &UAuracronAdvancedNetworkingCoordinator::execSetPlayerBandwidthLimit },
		{ "SetReplicationOptimizationStrategy", &UAuracronAdvancedNetworkingCoordinator::execSetReplicationOptimizationStrategy },
		{ "SyncBridgeStateAcrossNetwork", &UAuracronAdvancedNetworkingCoordinator::execSyncBridgeStateAcrossNetwork },
		{ "UpdateNetworkingCoordination", &UAuracronAdvancedNetworkingCoordinator::execUpdateNetworkingCoordination },
		{ "UpdatePredictionBuffer", &UAuracronAdvancedNetworkingCoordinator::execUpdatePredictionBuffer },
		{ "UpdateReplicationPriorities", &UAuracronAdvancedNetworkingCoordinator::execUpdateReplicationPriorities },
		{ "UpdateSessionSettings", &UAuracronAdvancedNetworkingCoordinator::execUpdateSessionSettings },
		{ "ValidateClientActionWithLagCompensation", &UAuracronAdvancedNetworkingCoordinator::execValidateClientActionWithLagCompensation },
		{ "ValidateClientStateAgainstServer", &UAuracronAdvancedNetworkingCoordinator::execValidateClientStateAgainstServer },
		{ "ValidateNetworkAction", &UAuracronAdvancedNetworkingCoordinator::execValidateNetworkAction },
		{ "ValidatePlayerActionServerSide", &UAuracronAdvancedNetworkingCoordinator::execValidatePlayerActionServerSide },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator;
UClass* UAuracronAdvancedNetworkingCoordinator::GetPrivateStaticClass()
{
	using TClass = UAuracronAdvancedNetworkingCoordinator;
	if (!Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdvancedNetworkingCoordinator"),
			Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.InnerSingleton,
			StaticRegisterNativesUAuracronAdvancedNetworkingCoordinator,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister()
{
	return UAuracronAdvancedNetworkingCoordinator::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Advanced Networking Coordinator\n * \n * Comprehensive networking coordination system that manages and optimizes\n * all networking subsystems for optimal multiplayer performance, security,\n * and player experience.\n */" },
#endif
		{ "IncludePath", "AuracronAdvancedNetworkingCoordinator.h" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Advanced Networking Coordinator\n\nComprehensive networking coordination system that manages and optimizes\nall networking subsystems for optimal multiplayer performance, security,\nand player experience." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bNetworkingCoordinatorEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable networking coordinator */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable networking coordinator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationStrategy_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization strategy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictionConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Network prediction configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network prediction configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReplicationPriorities_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Replication priorities */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replication priorities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveOptimization_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable adaptive optimization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable adaptive optimization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisReplicationConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iris replication configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Multiplayer session configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multiplayer session configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatLevel_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Anti-cheat level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableServerAuthority_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable server authority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable server authority" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCrossPlatformNetworking_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable cross-platform networking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable cross-platform networking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentNetworkMetrics_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current network quality metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current network quality metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkQualityHistory_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Network quality history */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network quality history" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerNetworkMetrics_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player connection metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player connection metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveSession_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active multiplayer session" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectedPlayers_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Connected players */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connected players" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatValidationHistory_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Anti-cheat validation history */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat validation history" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisReplicationMetrics_MetaData[] = {
		{ "Category", "Network State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iris replication metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedNetworkingBridge_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedAntiCheatBridge_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedPerformanceAnalyzer_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisReplicationSystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Iris System References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Iris System References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisNetObjectFactory_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisReplicationBridge_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisNetObjectFilter_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisNetObjectPrioritizer_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedNetworkingCoordinator.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bNetworkingCoordinatorEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNetworkingCoordinatorEnabled;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationStrategy;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PredictionConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReplicationPriorities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReplicationPriorities;
	static void NewProp_bEnableAdaptiveOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveOptimization;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IrisReplicationConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AntiCheatLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AntiCheatLevel;
	static void NewProp_bEnableServerAuthority_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableServerAuthority;
	static void NewProp_bEnableCrossPlatformNetworking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCrossPlatformNetworking;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentNetworkMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NetworkQualityHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NetworkQualityHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerNetworkMetrics_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerNetworkMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerNetworkMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveSession;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConnectedPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectedPlayers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AntiCheatValidationHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AntiCheatValidationHistory;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IrisReplicationMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_IrisReplicationMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_IrisReplicationMetrics;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedNetworkingBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedAntiCheatBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedPerformanceAnalyzer;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisReplicationSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisNetObjectFactory;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisReplicationBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisNetObjectFilter;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IrisNetObjectPrioritizer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ApplyLagCompensationForAction, "ApplyLagCompensationForAction" }, // 2613334002
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_BanPlayerWithEvidence, "BanPlayerWithEvidence" }, // 3987872947
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureIrisReplication, "ConfigureIrisReplication" }, // 4086533058
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ConfigureLagCompensation, "ConfigureLagCompensation" }, // 1021823924
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CoordinateAntiCheatSystems, "CoordinateAntiCheatSystems" }, // 2376776265
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_CreateMultiplayerSession, "CreateMultiplayerSession" }, // 3747966957
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableAuthoritativeModeForSystem, "EnableAuthoritativeModeForSystem" }, // 2784194044
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_EnableNetworkPredictionForActor, "EnableNetworkPredictionForActor" }, // 3936599332
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ForceServerReconciliation, "ForceServerReconciliation" }, // 119142235
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetActiveSessionInfo, "GetActiveSessionInfo" }, // 3311453194
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetIrisReplicationMetrics, "GetIrisReplicationMetrics" }, // 1009570880
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_GetNetworkQualityMetrics, "GetNetworkQualityMetrics" }, // 785507479
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeAdvancedAntiCheat, "InitializeAdvancedAntiCheat" }, // 1518124141
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeIrisReplicationSystem, "InitializeIrisReplicationSystem" }, // 2962257716
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_InitializeNetworkingCoordinator, "InitializeNetworkingCoordinator" }, // 2064859678
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_JoinMultiplayerSession, "JoinMultiplayerSession" }, // 578766653
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_LeaveMultiplayerSession, "LeaveMultiplayerSession" }, // 2817420047
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_MonitorBandwidthUsage, "MonitorBandwidthUsage" }, // 4211823981
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnAntiCheatViolationDetected, "OnAntiCheatViolationDetected" }, // 2268132261
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnIrisReplicationOptimized, "OnIrisReplicationOptimized" }, // 5767354
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnMultiplayerSessionCreated, "OnMultiplayerSessionCreated" }, // 2122689528
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkAnomalyDetected, "OnNetworkAnomalyDetected" }, // 1885005571
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkOptimizationApplied, "OnNetworkOptimizationApplied" }, // 3335109663
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnNetworkQualityChanged, "OnNetworkQualityChanged" }, // 3195466925
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnPlayerJoinedSession, "OnPlayerJoinedSession" }, // 1905736868
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OnServerReconciliationTriggered, "OnServerReconciliationTriggered" }, // 2017695386
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeBandwidthAllocation, "OptimizeBandwidthAllocation" }, // 2498985442
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeIrisReplicationGraph, "OptimizeIrisReplicationGraph" }, // 488554281
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeNetworkPerformance, "OptimizeNetworkPerformance" }, // 229279259
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_OptimizeReplicationForPlayer, "OptimizeReplicationForPlayer" }, // 1407962090
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReconcileClientPrediction, "ReconcileClientPrediction" }, // 269841173
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_RegisterObjectForIrisReplication, "RegisterObjectForIrisReplication" }, // 2354026761
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportNetworkAnomaly, "ReportNetworkAnomaly" }, // 977130009
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ReportSuspiciousActivity, "ReportSuspiciousActivity" }, // 3731520269
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetActorReplicationPriority, "SetActorReplicationPriority" }, // 4242180494
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetPlayerBandwidthLimit, "SetPlayerBandwidthLimit" }, // 2144460389
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SetReplicationOptimizationStrategy, "SetReplicationOptimizationStrategy" }, // 3882313606
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_SyncBridgeStateAcrossNetwork, "SyncBridgeStateAcrossNetwork" }, // 2135023548
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateNetworkingCoordination, "UpdateNetworkingCoordination" }, // 4185912990
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdatePredictionBuffer, "UpdatePredictionBuffer" }, // 989233596
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateReplicationPriorities, "UpdateReplicationPriorities" }, // 3738269209
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_UpdateSessionSettings, "UpdateSessionSettings" }, // 1553756026
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientActionWithLagCompensation, "ValidateClientActionWithLagCompensation" }, // 3318354055
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateClientStateAgainstServer, "ValidateClientStateAgainstServer" }, // 3322951462
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidateNetworkAction, "ValidateNetworkAction" }, // 824976389
		{ &Z_Construct_UFunction_UAuracronAdvancedNetworkingCoordinator_ValidatePlayerActionServerSide, "ValidatePlayerActionServerSide" }, // 446357851
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAdvancedNetworkingCoordinator>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bNetworkingCoordinatorEnabled_SetBit(void* Obj)
{
	((UAuracronAdvancedNetworkingCoordinator*)Obj)->bNetworkingCoordinatorEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bNetworkingCoordinatorEnabled = { "bNetworkingCoordinatorEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedNetworkingCoordinator), &Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bNetworkingCoordinatorEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bNetworkingCoordinatorEnabled_MetaData), NewProp_bNetworkingCoordinatorEnabled_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_OptimizationStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_OptimizationStrategy = { "OptimizationStrategy", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, OptimizationStrategy), Z_Construct_UEnum_AuracronNetworkingBridge_ENetworkOptimizationStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationStrategy_MetaData), NewProp_OptimizationStrategy_MetaData) }; // 3255578726
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PredictionConfig = { "PredictionConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, PredictionConfig), Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictionConfig_MetaData), NewProp_PredictionConfig_MetaData) }; // 2465755286
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ReplicationPriorities_Inner = { "ReplicationPriorities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority, METADATA_PARAMS(0, nullptr) }; // 3470695875
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ReplicationPriorities = { "ReplicationPriorities", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, ReplicationPriorities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReplicationPriorities_MetaData), NewProp_ReplicationPriorities_MetaData) }; // 3470695875
void Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableAdaptiveOptimization_SetBit(void* Obj)
{
	((UAuracronAdvancedNetworkingCoordinator*)Obj)->bEnableAdaptiveOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableAdaptiveOptimization = { "bEnableAdaptiveOptimization", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedNetworkingCoordinator), &Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableAdaptiveOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveOptimization_MetaData), NewProp_bEnableAdaptiveOptimization_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationConfig = { "IrisReplicationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisReplicationConfig), Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisReplicationConfig_MetaData), NewProp_IrisReplicationConfig_MetaData) }; // 1594044851
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, SessionConfig), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 729078944
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatLevel = { "AntiCheatLevel", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, AntiCheatLevel), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatLevel_MetaData), NewProp_AntiCheatLevel_MetaData) }; // 1227517794
void Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableServerAuthority_SetBit(void* Obj)
{
	((UAuracronAdvancedNetworkingCoordinator*)Obj)->bEnableServerAuthority = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableServerAuthority = { "bEnableServerAuthority", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedNetworkingCoordinator), &Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableServerAuthority_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableServerAuthority_MetaData), NewProp_bEnableServerAuthority_MetaData) };
void Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableCrossPlatformNetworking_SetBit(void* Obj)
{
	((UAuracronAdvancedNetworkingCoordinator*)Obj)->bEnableCrossPlatformNetworking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableCrossPlatformNetworking = { "bEnableCrossPlatformNetworking", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedNetworkingCoordinator), &Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableCrossPlatformNetworking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCrossPlatformNetworking_MetaData), NewProp_bEnableCrossPlatformNetworking_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CurrentNetworkMetrics = { "CurrentNetworkMetrics", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, CurrentNetworkMetrics), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentNetworkMetrics_MetaData), NewProp_CurrentNetworkMetrics_MetaData) }; // 1513915643
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_NetworkQualityHistory_Inner = { "NetworkQualityHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(0, nullptr) }; // 1513915643
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_NetworkQualityHistory = { "NetworkQualityHistory", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, NetworkQualityHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkQualityHistory_MetaData), NewProp_NetworkQualityHistory_MetaData) }; // 1513915643
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics_ValueProp = { "PlayerNetworkMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(0, nullptr) }; // 1513915643
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics_Key_KeyProp = { "PlayerNetworkMetrics_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics = { "PlayerNetworkMetrics", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, PlayerNetworkMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerNetworkMetrics_MetaData), NewProp_PlayerNetworkMetrics_MetaData) }; // 1513915643
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ActiveSession = { "ActiveSession", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, ActiveSession), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveSession_MetaData), NewProp_ActiveSession_MetaData) }; // 729078944
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ConnectedPlayers_Inner = { "ConnectedPlayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ConnectedPlayers = { "ConnectedPlayers", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, ConnectedPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectedPlayers_MetaData), NewProp_ConnectedPlayers_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatValidationHistory_Inner = { "AntiCheatValidationHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAntiCheatValidation, METADATA_PARAMS(0, nullptr) }; // 3063857892
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatValidationHistory = { "AntiCheatValidationHistory", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, AntiCheatValidationHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatValidationHistory_MetaData), NewProp_AntiCheatValidationHistory_MetaData) }; // 3063857892
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics_ValueProp = { "IrisReplicationMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics_Key_KeyProp = { "IrisReplicationMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics = { "IrisReplicationMetrics", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisReplicationMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisReplicationMetrics_MetaData), NewProp_IrisReplicationMetrics_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedNetworkingBridge = { "CachedNetworkingBridge", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, CachedNetworkingBridge), Z_Construct_UClass_UAuracronNetworkingBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedNetworkingBridge_MetaData), NewProp_CachedNetworkingBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedAntiCheatBridge = { "CachedAntiCheatBridge", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, CachedAntiCheatBridge), Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedAntiCheatBridge_MetaData), NewProp_CachedAntiCheatBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedPerformanceAnalyzer = { "CachedPerformanceAnalyzer", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, CachedPerformanceAnalyzer), Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedPerformanceAnalyzer_MetaData), NewProp_CachedPerformanceAnalyzer_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationSystem = { "IrisReplicationSystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisReplicationSystem), Z_Construct_UClass_UReplicationSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisReplicationSystem_MetaData), NewProp_IrisReplicationSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectFactory = { "IrisNetObjectFactory", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisNetObjectFactory), Z_Construct_UClass_UNetObjectFactory_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisNetObjectFactory_MetaData), NewProp_IrisNetObjectFactory_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationBridge = { "IrisReplicationBridge", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisReplicationBridge), Z_Construct_UClass_UReplicationBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisReplicationBridge_MetaData), NewProp_IrisReplicationBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectFilter = { "IrisNetObjectFilter", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisNetObjectFilter), Z_Construct_UClass_UNetObjectFilter_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisNetObjectFilter_MetaData), NewProp_IrisNetObjectFilter_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectPrioritizer = { "IrisNetObjectPrioritizer", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedNetworkingCoordinator, IrisNetObjectPrioritizer), Z_Construct_UClass_UNetObjectPrioritizer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisNetObjectPrioritizer_MetaData), NewProp_IrisNetObjectPrioritizer_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bNetworkingCoordinatorEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_OptimizationStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_OptimizationStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PredictionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ReplicationPriorities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ReplicationPriorities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableAdaptiveOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_SessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableServerAuthority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_bEnableCrossPlatformNetworking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CurrentNetworkMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_NetworkQualityHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_NetworkQualityHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_PlayerNetworkMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ActiveSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ConnectedPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_ConnectedPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatValidationHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_AntiCheatValidationHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedNetworkingBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedAntiCheatBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_CachedPerformanceAnalyzer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectFactory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisReplicationBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::NewProp_IrisNetObjectPrioritizer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::ClassParams = {
	&UAuracronAdvancedNetworkingCoordinator::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator()
{
	if (!Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.OuterSingleton, Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator.OuterSingleton;
}
UAuracronAdvancedNetworkingCoordinator::UAuracronAdvancedNetworkingCoordinator() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAdvancedNetworkingCoordinator);
UAuracronAdvancedNetworkingCoordinator::~UAuracronAdvancedNetworkingCoordinator() {}
// ********** End Class UAuracronAdvancedNetworkingCoordinator *************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ENetworkOptimizationStrategy_StaticEnum, TEXT("ENetworkOptimizationStrategy"), &Z_Registration_Info_UEnum_ENetworkOptimizationStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3255578726U) },
		{ EAuracronIrisReplicationMode_StaticEnum, TEXT("EAuracronIrisReplicationMode"), &Z_Registration_Info_UEnum_EAuracronIrisReplicationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3713434061U) },
		{ EAuracronAntiCheatLevel_StaticEnum, TEXT("EAuracronAntiCheatLevel"), &Z_Registration_Info_UEnum_EAuracronAntiCheatLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1227517794U) },
		{ EAuracronSessionType_StaticEnum, TEXT("EAuracronSessionType"), &Z_Registration_Info_UEnum_EAuracronSessionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2168713918U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronNetworkQualityMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics::NewStructOps, TEXT("AuracronNetworkQualityMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronNetworkQualityMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronNetworkQualityMetrics), 1513915643U) },
		{ FAuracronAdvancedReplicationPriority::StaticStruct, Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics::NewStructOps, TEXT("AuracronAdvancedReplicationPriority"), &Z_Registration_Info_UScriptStruct_FAuracronAdvancedReplicationPriority, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAdvancedReplicationPriority), 3470695875U) },
		{ FAuracronNetworkPredictionConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics::NewStructOps, TEXT("AuracronNetworkPredictionConfig"), &Z_Registration_Info_UScriptStruct_FAuracronNetworkPredictionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronNetworkPredictionConfig), 2465755286U) },
		{ FAuracronIrisReplicationConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics::NewStructOps, TEXT("AuracronIrisReplicationConfig"), &Z_Registration_Info_UScriptStruct_FAuracronIrisReplicationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronIrisReplicationConfig), 1594044851U) },
		{ FAuracronMultiplayerSessionConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics::NewStructOps, TEXT("AuracronMultiplayerSessionConfig"), &Z_Registration_Info_UScriptStruct_FAuracronMultiplayerSessionConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMultiplayerSessionConfig), 729078944U) },
		{ FAuracronAntiCheatValidation::StaticStruct, Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics::NewStructOps, TEXT("AuracronAntiCheatValidation"), &Z_Registration_Info_UScriptStruct_FAuracronAntiCheatValidation, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAntiCheatValidation), 3063857892U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator, UAuracronAdvancedNetworkingCoordinator::StaticClass, TEXT("UAuracronAdvancedNetworkingCoordinator"), &Z_Registration_Info_UClass_UAuracronAdvancedNetworkingCoordinator, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAdvancedNetworkingCoordinator), 1053910680U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_3278693888(TEXT("/Script/AuracronNetworkingBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h__Script_AuracronNetworkingBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
