{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\auracron\\source\\auracronchampionsbridge\\private\\auracronchampionsbridge_methods.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracronchampionsbridge\\definitions.auracronchampionsbridge.h", "c:\\aura\\projeto\\auracron\\source\\auracronchampionsbridge\\public\\auracronchampionsbridge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationavoidancetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationavoidancetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ai\\rvoavoidanceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rvoavoidanceinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\pawnmovementcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\pathfollowingagentinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pathfollowingagentinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pawnmovementcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\interfaces\\networkpredictioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\networkpredictioninterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\charactermovementcomponentasync.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponentasync.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\attributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\attributeset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayprediction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\classes\\net\\serialization\\fastarrayserializer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\netcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\netcore\\uht\\fastarrayserializer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayprediction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplaycueinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffecttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffecthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\activegameplayeffecthandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectattributecapturedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffectattributecapturedefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffecttypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplaycueinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagassetinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagassetinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspec.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspechandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspechandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\scalablefloat.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\source\\dataregistry\\public\\dataregistryid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\intermediate\\build\\win64\\unrealeditor\\inc\\dataregistry\\uht\\dataregistryid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\scalablefloat.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspec.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffect.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectaggregator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffectiterator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffect.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskscomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytasks\\classes\\gameplaytaskresource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskresource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytasks\\uht\\gameplaytaskscomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilityrepanimmontage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityrepanimmontage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytargettypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytargettypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayability.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilities\\gameplayabilitytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilitytypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayability.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\abilitysystemreplicationproxyinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemreplicationproxyinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\abilitysystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\timelinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\timelinecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagarasystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraassettagdefinitions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraassettagdefinitions.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetcompileddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstats.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponentpoolmethodenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponentpoolmethodenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaradefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarascalabilitystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarascalabilitystate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratickbehaviorenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratickbehaviorenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaratypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaratyperegistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaradatasetcompileddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaradatasetaccessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraeffecttype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ingameperformancetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraplatformset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraplatformset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraperfbaseline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstatsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraperfbaseline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationrule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationrule.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaravalidationruleset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravalidationruleset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraeffecttype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraemitterhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraemitterhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaramessagestore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaramessagestore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagaracore\\public\\niagaracompilehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagaracore\\uht\\niagaracompilehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\classes\\niagaraparameterdefinitionssubscriber.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaraparameterdefinitionsdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaraparameterdefinitionssubscriber.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagarauserredirectionparameterstore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarauserredirectionparameterstore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\fxbudget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagarasystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaracomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\source\\niagara\\public\\niagaravariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaravariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\emitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\emitter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\particlesystemstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\fx\\niagara\\intermediate\\build\\win64\\unrealeditor\\inc\\niagara\\uht\\niagaracomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundcue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundcue.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputaction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmodifiers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmodifiers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputtriggers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputtriggers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputaction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsystems.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedplayerinput.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\keystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\public\\nativegameplaytags.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagsmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedplayerinput.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsysteminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsystems.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmappingcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedactionkeymapping.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedactionkeymapping.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmappingcontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlrig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlrigdefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\propertypath\\public\\propertypathhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\propertypath\\public\\propertytypecompatibility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\propertypath\\uht\\propertypathhelpers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmexecutecontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmdefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmdebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmdebuginfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmprofilinginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmprofilinginfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmnamecache.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmmemorystoragestruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmmemorystorage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmmemorycommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmtraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmmemorycommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmpropertypath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmstatistics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmstatistics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmstringutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmmemorystorage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\propertybag.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\sharedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutilstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\sharedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\propertybag.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmmemorystoragestruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmtraitscope.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmdrawinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmdrawcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmdrawinstruction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\dynamicmeshbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmdrawinstruction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmdrawcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmdrawinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmexecutecontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\mttransactionallysafeaccessdetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmbytecode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmmemorydeprecated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmmemorydeprecated.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmdispatchfactory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmfunction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmmemory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmtypeindex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmfunction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmstructupgradeinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmtemplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmtypeutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmunknowntype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmunknowntype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\userdefinedenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\userdefinedenum.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\userdefinedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\userdefinedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\coreredirects.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\internal\\uobject\\coreredirects\\pm-k.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmtemplate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmdispatchfactory.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmbytecode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmexternalvariable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmpropertyutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmexternalvariable.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvm.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigname.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchyelements.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchycomponents.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchydefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animationcore\\public\\transformnoscale.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animationcore\\uht\\transformnoscale.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animationcore\\public\\eulertransform.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animationcore\\uht\\eulertransform.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlrigobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchydefines.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchycomponents.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchymetadata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchymetadata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigconnectionrules.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigconnectionrules.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigreusableelementstorage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchyelements.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchycache.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchycache.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchypose.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchypose.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchyposeadapter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmpythonutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmdevelopermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigdependency.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\controlrigdefines.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlriggizmolibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\controlriggizmolibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlrigoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\controlrigoverride.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\units\\rigunitcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\righierarchycontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigbonehierarchy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigbonehierarchy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigspacehierarchy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigspacehierarchy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigcontrolhierarchy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigcontrolhierarchy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigcurvecontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigcurvecontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\riginfluencemap.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\riginfluencemap.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\righierarchycontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animationcore\\public\\animationdatasource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animationcore\\uht\\animationdatasource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\controlrigassetuserdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmassetuserdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmassetuserdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\controlrigassetuserdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigunitcontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\units\\rigunit.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmuserworkflow.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmuserworkflow.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmfunctions\\rigvmfunctiondefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmfunctiondefines.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\nodes\\rigvmunitnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\nodes\\rigvmtemplatenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\rigvmnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\rigvmpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmcompiler\\rigvmastproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmtrait.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmtrait.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\rigvmmodelcachedvalue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvmdeveloper\\uht\\rigvmpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvmdeveloper\\public\\rigvmmodel\\rigvmtraitdefaultvaluestruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvmdeveloper\\uht\\rigvmtraitdefaultvaluestruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmgraphfunctiondefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmobjectarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmobjectarchive.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmvariant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmvariant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmnodelayout.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmnodelayout.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmgraphfunctiondefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvmdeveloper\\uht\\rigvmnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvmdeveloper\\uht\\rigvmtemplatenode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvmdeveloper\\uht\\rigvmunitnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigunit.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\units\\control\\rigunit_control.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animationcore\\public\\constraint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animationcore\\public\\commonanimtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animationcore\\uht\\commonanimtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animationcore\\uht\\constraint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigunit_control.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmhost.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\source\\rigvm\\public\\rigvmcore\\rigvmgraphfunctionhost.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmgraphfunctionhost.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\rigvm\\intermediate\\build\\win64\\unrealeditor\\inc\\rigvm\\uht\\rigvmhost.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\animation\\animinstanceproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animinstanceproxy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\meshdeformerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshdeformerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\meshdeformerproducer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshdeformerproducer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\source\\controlrig\\public\\rigs\\rigmoduledefines.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\rigmoduledefines.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\overriddenpropertyset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\overriddenpropertyset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\animgraph\\public\\animpreviewinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\bonecontrollers\\animnode_modifybone.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\bonecontrollers\\animnode_skeletalcontrolbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\inputscalebias.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\inputscalebias.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_skeletalcontrolbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_modifybone.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\animsinglenodeinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animsinglenodeinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\animation\\animsinglenodeinstanceproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animsinglenodeinstanceproxy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\animnodes\\animnode_curvesource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\curvesourceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\curvesourceinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_curvesource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\animnodes\\animnode_poseblendnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\animnodes\\animnode_posehandler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\poseasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\poseasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\animnode_assetplayerbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\animnode_relevantassetplayerbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animnode_relevantassetplayerbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animnode_assetplayerbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_posehandler.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_poseblendnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\animgraphruntime\\public\\animnodes\\animnode_copyposefrommesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_copyposefrommesh.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraph\\uht\\animpreviewinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\animation\\controlrig\\intermediate\\build\\win64\\unrealeditor\\inc\\controlrig\\uht\\controlrig.generated.h", "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracronchampionsbridge\\uht\\auracronchampionsbridge.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}