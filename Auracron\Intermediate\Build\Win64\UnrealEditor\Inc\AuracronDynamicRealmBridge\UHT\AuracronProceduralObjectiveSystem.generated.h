// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronProceduralObjectiveSystem.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronProceduralObjectiveSystem_generated_h
#error "AuracronProceduralObjectiveSystem.generated.h already included, missing '#pragma once' in AuracronProceduralObjectiveSystem.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronProceduralObjectiveSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EObjectiveGenerationContext : uint8;
enum class EProceduralObjectiveType : uint8;
struct FAuracronMatchStateAnalysis;
struct FAuracronObjectiveGenerationParams;
struct FAuracronProceduralObjective;

// ********** Begin ScriptStruct FAuracronMatchStateAnalysis ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_94_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMatchStateAnalysis;
// ********** End ScriptStruct FAuracronMatchStateAnalysis *****************************************

// ********** Begin ScriptStruct FAuracronProceduralObjective **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralObjective;
// ********** End ScriptStruct FAuracronProceduralObjective ****************************************

// ********** Begin ScriptStruct FAuracronObjectiveGenerationParams ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_229_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronObjectiveGenerationParams;
// ********** End ScriptStruct FAuracronObjectiveGenerationParams **********************************

// ********** Begin Class UAuracronProceduralObjectiveSystem ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetAdaptiveGeneration); \
	DECLARE_FUNCTION(execGetGenerationParameters); \
	DECLARE_FUNCTION(execSetGenerationParameters); \
	DECLARE_FUNCTION(execCalculateActionIntensity); \
	DECLARE_FUNCTION(execCalculateTeamBalance); \
	DECLARE_FUNCTION(execGetCurrentMatchPhase); \
	DECLARE_FUNCTION(execAnalyzeMatchState); \
	DECLARE_FUNCTION(execGetObjectiveByID); \
	DECLARE_FUNCTION(execGetObjectivesByType); \
	DECLARE_FUNCTION(execGetObjectivesForTeam); \
	DECLARE_FUNCTION(execGetActiveObjectives); \
	DECLARE_FUNCTION(execCancelObjective); \
	DECLARE_FUNCTION(execCompleteObjective); \
	DECLARE_FUNCTION(execUpdateObjectives); \
	DECLARE_FUNCTION(execGenerateObjectives); \
	DECLARE_FUNCTION(execInitializeObjectiveSystem);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronProceduralObjectiveSystem(); \
	friend struct Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronProceduralObjectiveSystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronProceduralObjectiveSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronProceduralObjectiveSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronProceduralObjectiveSystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronProceduralObjectiveSystem(UAuracronProceduralObjectiveSystem&&) = delete; \
	UAuracronProceduralObjectiveSystem(const UAuracronProceduralObjectiveSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronProceduralObjectiveSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronProceduralObjectiveSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronProceduralObjectiveSystem) \
	NO_API virtual ~UAuracronProceduralObjectiveSystem();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_276_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h_279_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronProceduralObjectiveSystem;

// ********** End Class UAuracronProceduralObjectiveSystem *****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h

// ********** Begin Enum EProceduralObjectiveType **************************************************
#define FOREACH_ENUM_EPROCEDURALOBJECTIVETYPE(op) \
	op(EProceduralObjectiveType::Capture) \
	op(EProceduralObjectiveType::Defend) \
	op(EProceduralObjectiveType::Eliminate) \
	op(EProceduralObjectiveType::Collect) \
	op(EProceduralObjectiveType::Escort) \
	op(EProceduralObjectiveType::Survive) \
	op(EProceduralObjectiveType::Activate) \
	op(EProceduralObjectiveType::Destroy) \
	op(EProceduralObjectiveType::Control) \
	op(EProceduralObjectiveType::Explore) \
	op(EProceduralObjectiveType::Coordinate) \
	op(EProceduralObjectiveType::Adapt) 

enum class EProceduralObjectiveType : uint8;
template<> struct TIsUEnumClass<EProceduralObjectiveType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EProceduralObjectiveType>();
// ********** End Enum EProceduralObjectiveType ****************************************************

// ********** Begin Enum EObjectivePriority ********************************************************
#define FOREACH_ENUM_EOBJECTIVEPRIORITY(op) \
	op(EObjectivePriority::Low) \
	op(EObjectivePriority::Medium) \
	op(EObjectivePriority::High) \
	op(EObjectivePriority::Critical) \
	op(EObjectivePriority::Emergency) 

enum class EObjectivePriority : uint8;
template<> struct TIsUEnumClass<EObjectivePriority> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EObjectivePriority>();
// ********** End Enum EObjectivePriority **********************************************************

// ********** Begin Enum EObjectiveGenerationContext ***********************************************
#define FOREACH_ENUM_EOBJECTIVEGENERATIONCONTEXT(op) \
	op(EObjectiveGenerationContext::MatchStart) \
	op(EObjectiveGenerationContext::EarlyGame) \
	op(EObjectiveGenerationContext::MidGame) \
	op(EObjectiveGenerationContext::LateGame) \
	op(EObjectiveGenerationContext::TeamFight) \
	op(EObjectiveGenerationContext::Stalemate) \
	op(EObjectiveGenerationContext::Comeback) \
	op(EObjectiveGenerationContext::Domination) 

enum class EObjectiveGenerationContext : uint8;
template<> struct TIsUEnumClass<EObjectiveGenerationContext> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EObjectiveGenerationContext>();
// ********** End Enum EObjectiveGenerationContext *************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
