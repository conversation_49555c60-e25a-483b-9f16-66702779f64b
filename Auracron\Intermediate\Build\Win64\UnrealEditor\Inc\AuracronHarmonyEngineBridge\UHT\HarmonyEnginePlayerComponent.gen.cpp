// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "HarmonyEnginePlayerComponent.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyRewardsSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeHarmonyEnginePlayerComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSubsystem_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyInterventionData();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FKindnessReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ClaimReward ***********************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics
{
	struct HarmonyEnginePlayerComponent_eventClaimReward_Parms
	{
		FString RewardID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_RewardID = { "RewardID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClaimReward_Parms, RewardID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardID_MetaData), NewProp_RewardID_MetaData) };
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventClaimReward_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventClaimReward_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_RewardID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ClaimReward", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::HarmonyEnginePlayerComponent_eventClaimReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::HarmonyEnginePlayerComponent_eventClaimReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execClaimReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_RewardID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ClaimReward(Z_Param_RewardID);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ClaimReward *************************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ClientShowIntervention ************
struct HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms
{
	FString InterventionMessage;
	EInterventionType InterventionType;
};
static FName NAME_UHarmonyEnginePlayerComponent_ClientShowIntervention = FName(TEXT("ClientShowIntervention"));
void UHarmonyEnginePlayerComponent::ClientShowIntervention(const FString& InterventionMessage, EInterventionType InterventionType)
{
	HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms Parms;
	Parms.InterventionMessage=InterventionMessage;
	Parms.InterventionType=InterventionType;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ClientShowIntervention);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionMessage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionMessage = { "InterventionMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms, InterventionMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionMessage_MetaData), NewProp_InterventionMessage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionType = { "InterventionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms, InterventionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::NewProp_InterventionType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ClientShowIntervention", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x01040CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventClientShowIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execClientShowIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionMessage);
	P_GET_ENUM(EInterventionType,Z_Param_InterventionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientShowIntervention_Implementation(Z_Param_InterventionMessage,EInterventionType(Z_Param_InterventionType));
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ClientShowIntervention **************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ClientShowRewardNotification ******
struct HarmonyEnginePlayerComponent_eventClientShowRewardNotification_Parms
{
	FKindnessReward Reward;
};
static FName NAME_UHarmonyEnginePlayerComponent_ClientShowRewardNotification = FName(TEXT("ClientShowRewardNotification"));
void UHarmonyEnginePlayerComponent::ClientShowRewardNotification(FKindnessReward const& Reward)
{
	HarmonyEnginePlayerComponent_eventClientShowRewardNotification_Parms Parms;
	Parms.Reward=Reward;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ClientShowRewardNotification);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000082, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientShowRewardNotification_Parms, Reward), Z_Construct_UScriptStruct_FKindnessReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4200945998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ClientShowRewardNotification", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventClientShowRewardNotification_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x01040CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventClientShowRewardNotification_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execClientShowRewardNotification)
{
	P_GET_STRUCT(FKindnessReward,Z_Param_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientShowRewardNotification_Implementation(Z_Param_Reward);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ClientShowRewardNotification ********

// ********** Begin Class UHarmonyEnginePlayerComponent Function ClientUpdateHarmonyData ***********
struct HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms
{
	float ToxicityScore;
	float PositivityScore;
	EEmotionalState EmotionalState;
	int32 KindnessPoints;
};
static FName NAME_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData = FName(TEXT("ClientUpdateHarmonyData"));
void UHarmonyEnginePlayerComponent::ClientUpdateHarmonyData(float ToxicityScore, float PositivityScore, EEmotionalState EmotionalState, int32 KindnessPoints)
{
	HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms Parms;
	Parms.ToxicityScore=ToxicityScore;
	Parms.PositivityScore=PositivityScore;
	Parms.EmotionalState=EmotionalState;
	Parms.KindnessPoints=KindnessPoints;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ToxicityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PositivityScore;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EmotionalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EmotionalState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_KindnessPoints;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_ToxicityScore = { "ToxicityScore", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms, ToxicityScore), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_PositivityScore = { "PositivityScore", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms, PositivityScore), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_EmotionalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_EmotionalState = { "EmotionalState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms, EmotionalState), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(0, nullptr) }; // 3787130921
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_KindnessPoints = { "KindnessPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms, KindnessPoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_ToxicityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_PositivityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_EmotionalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_EmotionalState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::NewProp_KindnessPoints,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ClientUpdateHarmonyData", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x01040CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventClientUpdateHarmonyData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execClientUpdateHarmonyData)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_ToxicityScore);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PositivityScore);
	P_GET_ENUM(EEmotionalState,Z_Param_EmotionalState);
	P_GET_PROPERTY(FIntProperty,Z_Param_KindnessPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientUpdateHarmonyData_Implementation(Z_Param_ToxicityScore,Z_Param_PositivityScore,EEmotionalState(Z_Param_EmotionalState),Z_Param_KindnessPoints);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ClientUpdateHarmonyData *************

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetAvailableRewards ***************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetAvailableRewards_Parms
	{
		TArray<FHarmonyReward> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rewards and Progression\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rewards and Progression" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHarmonyReward, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetAvailableRewards_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4013152750
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetAvailableRewards", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::HarmonyEnginePlayerComponent_eventGetAvailableRewards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::HarmonyEnginePlayerComponent_eventGetAvailableRewards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetAvailableRewards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FHarmonyReward>*)Z_Param__Result=P_THIS->GetAvailableRewards();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetAvailableRewards *****************

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetCurrentEmotionalState **********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetCurrentEmotionalState_Parms
	{
		EEmotionalState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetCurrentEmotionalState_Parms, ReturnValue), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(0, nullptr) }; // 3787130921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetCurrentEmotionalState", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::HarmonyEnginePlayerComponent_eventGetCurrentEmotionalState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::HarmonyEnginePlayerComponent_eventGetCurrentEmotionalState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetCurrentEmotionalState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EEmotionalState*)Z_Param__Result=P_THIS->GetCurrentEmotionalState();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetCurrentEmotionalState ************

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetCurrentPositivityScore *********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetCurrentPositivityScore_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetCurrentPositivityScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetCurrentPositivityScore", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::HarmonyEnginePlayerComponent_eventGetCurrentPositivityScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::HarmonyEnginePlayerComponent_eventGetCurrentPositivityScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetCurrentPositivityScore)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentPositivityScore();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetCurrentPositivityScore ***********

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetCurrentToxicityScore ***********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetCurrentToxicityScore_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player Status Queries\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Status Queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetCurrentToxicityScore_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetCurrentToxicityScore", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::HarmonyEnginePlayerComponent_eventGetCurrentToxicityScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::HarmonyEnginePlayerComponent_eventGetCurrentToxicityScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetCurrentToxicityScore)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentToxicityScore();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetCurrentToxicityScore *************

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetKindnessPoints *****************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetKindnessPoints_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetKindnessPoints_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetKindnessPoints", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::HarmonyEnginePlayerComponent_eventGetKindnessPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::HarmonyEnginePlayerComponent_eventGetKindnessPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetKindnessPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetKindnessPoints();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetKindnessPoints *******************

// ********** Begin Class UHarmonyEnginePlayerComponent Function GetProgressToNextTier *************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics
{
	struct HarmonyEnginePlayerComponent_eventGetProgressToNextTier_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventGetProgressToNextTier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "GetProgressToNextTier", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::HarmonyEnginePlayerComponent_eventGetProgressToNextTier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::HarmonyEnginePlayerComponent_eventGetProgressToNextTier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execGetProgressToNextTier)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetProgressToNextTier();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function GetProgressToNextTier ***************

// ********** Begin Class UHarmonyEnginePlayerComponent Function IsInHealingSession ****************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics
{
	struct HarmonyEnginePlayerComponent_eventIsInHealingSession_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventIsInHealingSession_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventIsInHealingSession_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "IsInHealingSession", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::HarmonyEnginePlayerComponent_eventIsInHealingSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::HarmonyEnginePlayerComponent_eventIsInHealingSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execIsInHealingSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInHealingSession();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function IsInHealingSession ******************

// ********** Begin Class UHarmonyEnginePlayerComponent Function IsUnderIntervention ***************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics
{
	struct HarmonyEnginePlayerComponent_eventIsUnderIntervention_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventIsUnderIntervention_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventIsUnderIntervention_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "IsUnderIntervention", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::HarmonyEnginePlayerComponent_eventIsUnderIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::HarmonyEnginePlayerComponent_eventIsUnderIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execIsUnderIntervention)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnderIntervention();
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function IsUnderIntervention *****************

// ********** Begin Class UHarmonyEnginePlayerComponent Function OnBehaviorDetectedInternal ********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics
{
	struct HarmonyEnginePlayerComponent_eventOnBehaviorDetectedInternal_Parms
	{
		FString DetectedPlayerID;
		FPlayerBehaviorSnapshot BehaviorData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event handlers\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event handlers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectedPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DetectedPlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::NewProp_DetectedPlayerID = { "DetectedPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnBehaviorDetectedInternal_Parms, DetectedPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectedPlayerID_MetaData), NewProp_DetectedPlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::NewProp_BehaviorData = { "BehaviorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnBehaviorDetectedInternal_Parms, BehaviorData), Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorData_MetaData), NewProp_BehaviorData_MetaData) }; // 3446946508
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::NewProp_DetectedPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::NewProp_BehaviorData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "OnBehaviorDetectedInternal", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::HarmonyEnginePlayerComponent_eventOnBehaviorDetectedInternal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::HarmonyEnginePlayerComponent_eventOnBehaviorDetectedInternal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execOnBehaviorDetectedInternal)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DetectedPlayerID);
	P_GET_STRUCT_REF(FPlayerBehaviorSnapshot,Z_Param_Out_BehaviorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBehaviorDetectedInternal(Z_Param_DetectedPlayerID,Z_Param_Out_BehaviorData);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function OnBehaviorDetectedInternal **********

// ********** Begin Class UHarmonyEnginePlayerComponent Function OnInterventionTriggeredInternal ***
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics
{
	struct HarmonyEnginePlayerComponent_eventOnInterventionTriggeredInternal_Parms
	{
		FString DetectedPlayerID;
		FHarmonyInterventionData InterventionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectedPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DetectedPlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::NewProp_DetectedPlayerID = { "DetectedPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnInterventionTriggeredInternal_Parms, DetectedPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectedPlayerID_MetaData), NewProp_DetectedPlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::NewProp_InterventionData = { "InterventionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnInterventionTriggeredInternal_Parms, InterventionData), Z_Construct_UScriptStruct_FHarmonyInterventionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionData_MetaData), NewProp_InterventionData_MetaData) }; // 1406652677
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::NewProp_DetectedPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::NewProp_InterventionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "OnInterventionTriggeredInternal", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::HarmonyEnginePlayerComponent_eventOnInterventionTriggeredInternal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::HarmonyEnginePlayerComponent_eventOnInterventionTriggeredInternal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execOnInterventionTriggeredInternal)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DetectedPlayerID);
	P_GET_STRUCT_REF(FHarmonyInterventionData,Z_Param_Out_InterventionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInterventionTriggeredInternal(Z_Param_DetectedPlayerID,Z_Param_Out_InterventionData);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function OnInterventionTriggeredInternal *****

// ********** Begin Class UHarmonyEnginePlayerComponent Function OnKindnessRewardInternal **********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics
{
	struct HarmonyEnginePlayerComponent_eventOnKindnessRewardInternal_Parms
	{
		FString DetectedPlayerID;
		FKindnessReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectedPlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_DetectedPlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::NewProp_DetectedPlayerID = { "DetectedPlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnKindnessRewardInternal_Parms, DetectedPlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectedPlayerID_MetaData), NewProp_DetectedPlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventOnKindnessRewardInternal_Parms, Reward), Z_Construct_UScriptStruct_FKindnessReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4200945998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::NewProp_DetectedPlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "OnKindnessRewardInternal", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::HarmonyEnginePlayerComponent_eventOnKindnessRewardInternal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::HarmonyEnginePlayerComponent_eventOnKindnessRewardInternal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execOnKindnessRewardInternal)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_DetectedPlayerID);
	P_GET_STRUCT_REF(FKindnessReward,Z_Param_Out_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnKindnessRewardInternal(Z_Param_DetectedPlayerID,Z_Param_Out_Reward);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function OnKindnessRewardInternal ************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ReportChatMessage *****************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics
{
	struct HarmonyEnginePlayerComponent_eventReportChatMessage_Parms
	{
		FString Message;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportChatMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ReportChatMessage", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::HarmonyEnginePlayerComponent_eventReportChatMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::HarmonyEnginePlayerComponent_eventReportChatMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execReportChatMessage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportChatMessage(Z_Param_Message);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ReportChatMessage *******************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ReportEmotionalState **************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics
{
	struct HarmonyEnginePlayerComponent_eventReportEmotionalState_Parms
	{
		EEmotionalState NewEmotionalState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEmotionalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEmotionalState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::NewProp_NewEmotionalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::NewProp_NewEmotionalState = { "NewEmotionalState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportEmotionalState_Parms, NewEmotionalState), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(0, nullptr) }; // 3787130921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::NewProp_NewEmotionalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::NewProp_NewEmotionalState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ReportEmotionalState", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::HarmonyEnginePlayerComponent_eventReportEmotionalState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::HarmonyEnginePlayerComponent_eventReportEmotionalState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execReportEmotionalState)
{
	P_GET_ENUM(EEmotionalState,Z_Param_NewEmotionalState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportEmotionalState(EEmotionalState(Z_Param_NewEmotionalState));
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ReportEmotionalState ****************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ReportPlayerAction ****************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics
{
	struct HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms
	{
		FString ActionType;
		bool bIsPositive;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player Behavior Tracking\n" },
#endif
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Behavior Tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static void NewProp_bIsPositive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPositive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_bIsPositive_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms*)Obj)->bIsPositive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_bIsPositive = { "bIsPositive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_bIsPositive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_bIsPositive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ReportPlayerAction", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::HarmonyEnginePlayerComponent_eventReportPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execReportPlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_UBOOL(Z_Param_bIsPositive);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportPlayerAction(Z_Param_ActionType,Z_Param_bIsPositive,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ReportPlayerAction ******************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ReportTeamworkAction **************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics
{
	struct HarmonyEnginePlayerComponent_eventReportTeamworkAction_Parms
	{
		FString TeamworkType;
		TArray<FString> InvolvedPlayerIDs;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamworkType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvolvedPlayerIDs_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TeamworkType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InvolvedPlayerIDs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InvolvedPlayerIDs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_TeamworkType = { "TeamworkType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportTeamworkAction_Parms, TeamworkType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamworkType_MetaData), NewProp_TeamworkType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_InvolvedPlayerIDs_Inner = { "InvolvedPlayerIDs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_InvolvedPlayerIDs = { "InvolvedPlayerIDs", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventReportTeamworkAction_Parms, InvolvedPlayerIDs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvolvedPlayerIDs_MetaData), NewProp_InvolvedPlayerIDs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_TeamworkType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_InvolvedPlayerIDs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::NewProp_InvolvedPlayerIDs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ReportTeamworkAction", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::HarmonyEnginePlayerComponent_eventReportTeamworkAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::HarmonyEnginePlayerComponent_eventReportTeamworkAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execReportTeamworkAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_TeamworkType);
	P_GET_TARRAY_REF(FString,Z_Param_Out_InvolvedPlayerIDs);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReportTeamworkAction(Z_Param_TeamworkType,Z_Param_Out_InvolvedPlayerIDs);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ReportTeamworkAction ****************

// ********** Begin Class UHarmonyEnginePlayerComponent Function RequestCommunitySupport ***********
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics
{
	struct HarmonyEnginePlayerComponent_eventRequestCommunitySupport_Parms
	{
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventRequestCommunitySupport_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "RequestCommunitySupport", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::HarmonyEnginePlayerComponent_eventRequestCommunitySupport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::HarmonyEnginePlayerComponent_eventRequestCommunitySupport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execRequestCommunitySupport)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RequestCommunitySupport(Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function RequestCommunitySupport *************

// ********** Begin Class UHarmonyEnginePlayerComponent Function RespondToIntervention *************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics
{
	struct HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms
	{
		FString InterventionID;
		bool bAccepted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervention Response\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervention Response" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static void NewProp_bAccepted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAccepted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_bAccepted_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms*)Obj)->bAccepted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_bAccepted = { "bAccepted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_bAccepted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::NewProp_bAccepted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "RespondToIntervention", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::HarmonyEnginePlayerComponent_eventRespondToIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execRespondToIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_GET_UBOOL(Z_Param_bAccepted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RespondToIntervention(Z_Param_InterventionID,Z_Param_bAccepted);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function RespondToIntervention ***************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ServerReportChatMessage ***********
struct HarmonyEnginePlayerComponent_eventServerReportChatMessage_Parms
{
	FString Message;
};
static FName NAME_UHarmonyEnginePlayerComponent_ServerReportChatMessage = FName(TEXT("ServerReportChatMessage"));
void UHarmonyEnginePlayerComponent::ServerReportChatMessage(const FString& Message)
{
	HarmonyEnginePlayerComponent_eventServerReportChatMessage_Parms Parms;
	Parms.Message=Message;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ServerReportChatMessage);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventServerReportChatMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ServerReportChatMessage", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventServerReportChatMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventServerReportChatMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execServerReportChatMessage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerReportChatMessage_Implementation(Z_Param_Message);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ServerReportChatMessage *************

// ********** Begin Class UHarmonyEnginePlayerComponent Function ServerReportEmotionalState ********
struct HarmonyEnginePlayerComponent_eventServerReportEmotionalState_Parms
{
	EEmotionalState NewEmotionalState;
};
static FName NAME_UHarmonyEnginePlayerComponent_ServerReportEmotionalState = FName(TEXT("ServerReportEmotionalState"));
void UHarmonyEnginePlayerComponent::ServerReportEmotionalState(EEmotionalState NewEmotionalState)
{
	HarmonyEnginePlayerComponent_eventServerReportEmotionalState_Parms Parms;
	Parms.NewEmotionalState=NewEmotionalState;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ServerReportEmotionalState);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEmotionalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEmotionalState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::NewProp_NewEmotionalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::NewProp_NewEmotionalState = { "NewEmotionalState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventServerReportEmotionalState_Parms, NewEmotionalState), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(0, nullptr) }; // 3787130921
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::NewProp_NewEmotionalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::NewProp_NewEmotionalState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ServerReportEmotionalState", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventServerReportEmotionalState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventServerReportEmotionalState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execServerReportEmotionalState)
{
	P_GET_ENUM(EEmotionalState,Z_Param_NewEmotionalState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerReportEmotionalState_Implementation(EEmotionalState(Z_Param_NewEmotionalState));
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ServerReportEmotionalState **********

// ********** Begin Class UHarmonyEnginePlayerComponent Function ServerReportPlayerAction **********
struct HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms
{
	FString ActionType;
	bool bIsPositive;
	float Intensity;
};
static FName NAME_UHarmonyEnginePlayerComponent_ServerReportPlayerAction = FName(TEXT("ServerReportPlayerAction"));
void UHarmonyEnginePlayerComponent::ServerReportPlayerAction(const FString& ActionType, bool bIsPositive, float Intensity)
{
	HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms Parms;
	Parms.ActionType=ActionType;
	Parms.bIsPositive=bIsPositive ? true : false;
	Parms.Intensity=Intensity;
	UFunction* Func = FindFunctionChecked(NAME_UHarmonyEnginePlayerComponent_ServerReportPlayerAction);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Network functions\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Network functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static void NewProp_bIsPositive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPositive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
void Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_bIsPositive_SetBit(void* Obj)
{
	((HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms*)Obj)->bIsPositive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_bIsPositive = { "bIsPositive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms), &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_bIsPositive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_bIsPositive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "ServerReportPlayerAction", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::PropPointers), sizeof(HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEnginePlayerComponent_eventServerReportPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execServerReportPlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_UBOOL(Z_Param_bIsPositive);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerReportPlayerAction_Implementation(Z_Param_ActionType,Z_Param_bIsPositive,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function ServerReportPlayerAction ************

// ********** Begin Class UHarmonyEnginePlayerComponent Function VolunteerAsHealer *****************
struct Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics
{
	struct HarmonyEnginePlayerComponent_eventVolunteerAsHealer_Parms
	{
		TArray<EHealingSessionType> Specializations;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specializations_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Specializations_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Specializations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Specializations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations_Inner = { "Specializations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHealingSessionType, METADATA_PARAMS(0, nullptr) }; // 2683699308
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations = { "Specializations", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEnginePlayerComponent_eventVolunteerAsHealer_Parms, Specializations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specializations_MetaData), NewProp_Specializations_MetaData) }; // 2683699308
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::NewProp_Specializations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UHarmonyEnginePlayerComponent, nullptr, "VolunteerAsHealer", Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::HarmonyEnginePlayerComponent_eventVolunteerAsHealer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::HarmonyEnginePlayerComponent_eventVolunteerAsHealer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UHarmonyEnginePlayerComponent::execVolunteerAsHealer)
{
	P_GET_TARRAY_REF(EHealingSessionType,Z_Param_Out_Specializations);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->VolunteerAsHealer(Z_Param_Out_Specializations);
	P_NATIVE_END;
}
// ********** End Class UHarmonyEnginePlayerComponent Function VolunteerAsHealer *******************

// ********** Begin Class UHarmonyEnginePlayerComponent ********************************************
void UHarmonyEnginePlayerComponent::StaticRegisterNativesUHarmonyEnginePlayerComponent()
{
	UClass* Class = UHarmonyEnginePlayerComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClaimReward", &UHarmonyEnginePlayerComponent::execClaimReward },
		{ "ClientShowIntervention", &UHarmonyEnginePlayerComponent::execClientShowIntervention },
		{ "ClientShowRewardNotification", &UHarmonyEnginePlayerComponent::execClientShowRewardNotification },
		{ "ClientUpdateHarmonyData", &UHarmonyEnginePlayerComponent::execClientUpdateHarmonyData },
		{ "GetAvailableRewards", &UHarmonyEnginePlayerComponent::execGetAvailableRewards },
		{ "GetCurrentEmotionalState", &UHarmonyEnginePlayerComponent::execGetCurrentEmotionalState },
		{ "GetCurrentPositivityScore", &UHarmonyEnginePlayerComponent::execGetCurrentPositivityScore },
		{ "GetCurrentToxicityScore", &UHarmonyEnginePlayerComponent::execGetCurrentToxicityScore },
		{ "GetKindnessPoints", &UHarmonyEnginePlayerComponent::execGetKindnessPoints },
		{ "GetProgressToNextTier", &UHarmonyEnginePlayerComponent::execGetProgressToNextTier },
		{ "IsInHealingSession", &UHarmonyEnginePlayerComponent::execIsInHealingSession },
		{ "IsUnderIntervention", &UHarmonyEnginePlayerComponent::execIsUnderIntervention },
		{ "OnBehaviorDetectedInternal", &UHarmonyEnginePlayerComponent::execOnBehaviorDetectedInternal },
		{ "OnInterventionTriggeredInternal", &UHarmonyEnginePlayerComponent::execOnInterventionTriggeredInternal },
		{ "OnKindnessRewardInternal", &UHarmonyEnginePlayerComponent::execOnKindnessRewardInternal },
		{ "ReportChatMessage", &UHarmonyEnginePlayerComponent::execReportChatMessage },
		{ "ReportEmotionalState", &UHarmonyEnginePlayerComponent::execReportEmotionalState },
		{ "ReportPlayerAction", &UHarmonyEnginePlayerComponent::execReportPlayerAction },
		{ "ReportTeamworkAction", &UHarmonyEnginePlayerComponent::execReportTeamworkAction },
		{ "RequestCommunitySupport", &UHarmonyEnginePlayerComponent::execRequestCommunitySupport },
		{ "RespondToIntervention", &UHarmonyEnginePlayerComponent::execRespondToIntervention },
		{ "ServerReportChatMessage", &UHarmonyEnginePlayerComponent::execServerReportChatMessage },
		{ "ServerReportEmotionalState", &UHarmonyEnginePlayerComponent::execServerReportEmotionalState },
		{ "ServerReportPlayerAction", &UHarmonyEnginePlayerComponent::execServerReportPlayerAction },
		{ "VolunteerAsHealer", &UHarmonyEnginePlayerComponent::execVolunteerAsHealer },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent;
UClass* UHarmonyEnginePlayerComponent::GetPrivateStaticClass()
{
	using TClass = UHarmonyEnginePlayerComponent;
	if (!Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("HarmonyEnginePlayerComponent"),
			Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.InnerSingleton,
			StaticRegisterNativesUHarmonyEnginePlayerComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent_NoRegister()
{
	return UHarmonyEnginePlayerComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "HarmonyEngine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Player Component for Harmony Engine Integration\n * Attach this to PlayerState or PlayerController for automatic Harmony Engine integration\n */" },
#endif
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "HarmonyEnginePlayerComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Component for Harmony Engine Integration\nAttach this to PlayerState or PlayerController for automatic Harmony Engine integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerBehaviorDetected_MetaData[] = {
		{ "Category", "Harmony Engine Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerInterventionTriggered_MetaData[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerKindnessReward_MetaData[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentToxicityScore_MetaData[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Replicated Properties\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replicated Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPositivityScore_MetaData[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEmotionalState_MetaData[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalKindnessPoints_MetaData[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnderIntervention_MetaData[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInHealingSession_MetaData[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticReporting_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorUpdateInterval_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEmotionalTracking_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRewardNotifications_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HarmonyEngineSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Runtime Data\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Runtime Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentActions_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentChatMessages_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastBehaviorUpdate_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionsThisSession_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEnginePlayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerBehaviorDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerInterventionTriggered;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerKindnessReward;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentToxicityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentPositivityScore;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEmotionalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEmotionalState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalKindnessPoints;
	static void NewProp_bIsUnderIntervention_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnderIntervention;
	static void NewProp_bIsInHealingSession_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInHealingSession;
	static void NewProp_bEnableAutomaticReporting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticReporting;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BehaviorUpdateInterval;
	static void NewProp_bEnableEmotionalTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEmotionalTracking;
	static void NewProp_bEnableRewardNotifications_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRewardNotifications;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HarmonyEngineSubsystem;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecentActions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentActions;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecentChatMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentChatMessages;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastBehaviorUpdate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActionsThisSession;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClaimReward, "ClaimReward" }, // 1293957977
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowIntervention, "ClientShowIntervention" }, // 2730927690
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientShowRewardNotification, "ClientShowRewardNotification" }, // 3454397138
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ClientUpdateHarmonyData, "ClientUpdateHarmonyData" }, // 3465049348
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetAvailableRewards, "GetAvailableRewards" }, // 3949577223
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentEmotionalState, "GetCurrentEmotionalState" }, // 558015616
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentPositivityScore, "GetCurrentPositivityScore" }, // 2076815875
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetCurrentToxicityScore, "GetCurrentToxicityScore" }, // 4210177556
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetKindnessPoints, "GetKindnessPoints" }, // 1465884823
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_GetProgressToNextTier, "GetProgressToNextTier" }, // 157079980
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsInHealingSession, "IsInHealingSession" }, // 2996503288
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_IsUnderIntervention, "IsUnderIntervention" }, // 3803238837
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnBehaviorDetectedInternal, "OnBehaviorDetectedInternal" }, // 850988376
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnInterventionTriggeredInternal, "OnInterventionTriggeredInternal" }, // 2443347165
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_OnKindnessRewardInternal, "OnKindnessRewardInternal" }, // 2218683050
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportChatMessage, "ReportChatMessage" }, // 2419454073
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportEmotionalState, "ReportEmotionalState" }, // 1233100074
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportPlayerAction, "ReportPlayerAction" }, // 2375620588
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ReportTeamworkAction, "ReportTeamworkAction" }, // 3975398608
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RequestCommunitySupport, "RequestCommunitySupport" }, // 3999213450
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_RespondToIntervention, "RespondToIntervention" }, // 1262879869
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportChatMessage, "ServerReportChatMessage" }, // 1901811623
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportEmotionalState, "ServerReportEmotionalState" }, // 4202365553
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_ServerReportPlayerAction, "ServerReportPlayerAction" }, // 4076252111
		{ &Z_Construct_UFunction_UHarmonyEnginePlayerComponent_VolunteerAsHealer, "VolunteerAsHealer" }, // 922400176
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UHarmonyEnginePlayerComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerBehaviorDetected = { "OnPlayerBehaviorDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, OnPlayerBehaviorDetected), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerBehaviorDetected_MetaData), NewProp_OnPlayerBehaviorDetected_MetaData) }; // 495710177
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerInterventionTriggered = { "OnPlayerInterventionTriggered", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, OnPlayerInterventionTriggered), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerInterventionTriggered_MetaData), NewProp_OnPlayerInterventionTriggered_MetaData) }; // 2654442320
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerKindnessReward = { "OnPlayerKindnessReward", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, OnPlayerKindnessReward), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerKindnessReward_MetaData), NewProp_OnPlayerKindnessReward_MetaData) }; // 3905834585
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentToxicityScore = { "CurrentToxicityScore", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, CurrentToxicityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentToxicityScore_MetaData), NewProp_CurrentToxicityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentPositivityScore = { "CurrentPositivityScore", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, CurrentPositivityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPositivityScore_MetaData), NewProp_CurrentPositivityScore_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentEmotionalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentEmotionalState = { "CurrentEmotionalState", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, CurrentEmotionalState), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEmotionalState_MetaData), NewProp_CurrentEmotionalState_MetaData) }; // 3787130921
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_TotalKindnessPoints = { "TotalKindnessPoints", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, TotalKindnessPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalKindnessPoints_MetaData), NewProp_TotalKindnessPoints_MetaData) };
void Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsUnderIntervention_SetBit(void* Obj)
{
	((UHarmonyEnginePlayerComponent*)Obj)->bIsUnderIntervention = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsUnderIntervention = { "bIsUnderIntervention", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEnginePlayerComponent), &Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsUnderIntervention_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnderIntervention_MetaData), NewProp_bIsUnderIntervention_MetaData) };
void Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsInHealingSession_SetBit(void* Obj)
{
	((UHarmonyEnginePlayerComponent*)Obj)->bIsInHealingSession = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsInHealingSession = { "bIsInHealingSession", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEnginePlayerComponent), &Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsInHealingSession_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInHealingSession_MetaData), NewProp_bIsInHealingSession_MetaData) };
void Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableAutomaticReporting_SetBit(void* Obj)
{
	((UHarmonyEnginePlayerComponent*)Obj)->bEnableAutomaticReporting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableAutomaticReporting = { "bEnableAutomaticReporting", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEnginePlayerComponent), &Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableAutomaticReporting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticReporting_MetaData), NewProp_bEnableAutomaticReporting_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_BehaviorUpdateInterval = { "BehaviorUpdateInterval", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, BehaviorUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorUpdateInterval_MetaData), NewProp_BehaviorUpdateInterval_MetaData) };
void Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableEmotionalTracking_SetBit(void* Obj)
{
	((UHarmonyEnginePlayerComponent*)Obj)->bEnableEmotionalTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableEmotionalTracking = { "bEnableEmotionalTracking", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEnginePlayerComponent), &Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableEmotionalTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEmotionalTracking_MetaData), NewProp_bEnableEmotionalTracking_MetaData) };
void Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableRewardNotifications_SetBit(void* Obj)
{
	((UHarmonyEnginePlayerComponent*)Obj)->bEnableRewardNotifications = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableRewardNotifications = { "bEnableRewardNotifications", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UHarmonyEnginePlayerComponent), &Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableRewardNotifications_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRewardNotifications_MetaData), NewProp_bEnableRewardNotifications_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_HarmonyEngineSubsystem = { "HarmonyEngineSubsystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, HarmonyEngineSubsystem), Z_Construct_UClass_UHarmonyEngineSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HarmonyEngineSubsystem_MetaData), NewProp_HarmonyEngineSubsystem_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentActions_Inner = { "RecentActions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentActions = { "RecentActions", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, RecentActions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentActions_MetaData), NewProp_RecentActions_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentChatMessages_Inner = { "RecentChatMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentChatMessages = { "RecentChatMessages", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, RecentChatMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentChatMessages_MetaData), NewProp_RecentChatMessages_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_LastBehaviorUpdate = { "LastBehaviorUpdate", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, LastBehaviorUpdate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastBehaviorUpdate_MetaData), NewProp_LastBehaviorUpdate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_ActionsThisSession = { "ActionsThisSession", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UHarmonyEnginePlayerComponent, ActionsThisSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionsThisSession_MetaData), NewProp_ActionsThisSession_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerBehaviorDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerInterventionTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_OnPlayerKindnessReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentToxicityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentPositivityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentEmotionalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_CurrentEmotionalState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_TotalKindnessPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsUnderIntervention,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bIsInHealingSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableAutomaticReporting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_BehaviorUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableEmotionalTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_bEnableRewardNotifications,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_HarmonyEngineSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentActions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentActions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentChatMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_RecentChatMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_LastBehaviorUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::NewProp_ActionsThisSession,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::ClassParams = {
	&UHarmonyEnginePlayerComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UHarmonyEnginePlayerComponent()
{
	if (!Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.OuterSingleton, Z_Construct_UClass_UHarmonyEnginePlayerComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UHarmonyEnginePlayerComponent::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentToxicityScore(TEXT("CurrentToxicityScore"));
	static FName Name_CurrentPositivityScore(TEXT("CurrentPositivityScore"));
	static FName Name_CurrentEmotionalState(TEXT("CurrentEmotionalState"));
	static FName Name_TotalKindnessPoints(TEXT("TotalKindnessPoints"));
	static FName Name_bIsUnderIntervention(TEXT("bIsUnderIntervention"));
	static FName Name_bIsInHealingSession(TEXT("bIsInHealingSession"));
	const bool bIsValid = true
		&& Name_CurrentToxicityScore == ClassReps[(int32)ENetFields_Private::CurrentToxicityScore].Property->GetFName()
		&& Name_CurrentPositivityScore == ClassReps[(int32)ENetFields_Private::CurrentPositivityScore].Property->GetFName()
		&& Name_CurrentEmotionalState == ClassReps[(int32)ENetFields_Private::CurrentEmotionalState].Property->GetFName()
		&& Name_TotalKindnessPoints == ClassReps[(int32)ENetFields_Private::TotalKindnessPoints].Property->GetFName()
		&& Name_bIsUnderIntervention == ClassReps[(int32)ENetFields_Private::bIsUnderIntervention].Property->GetFName()
		&& Name_bIsInHealingSession == ClassReps[(int32)ENetFields_Private::bIsInHealingSession].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UHarmonyEnginePlayerComponent"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UHarmonyEnginePlayerComponent);
UHarmonyEnginePlayerComponent::~UHarmonyEnginePlayerComponent() {}
// ********** End Class UHarmonyEnginePlayerComponent **********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UHarmonyEnginePlayerComponent, UHarmonyEnginePlayerComponent::StaticClass, TEXT("UHarmonyEnginePlayerComponent"), &Z_Registration_Info_UClass_UHarmonyEnginePlayerComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UHarmonyEnginePlayerComponent), 1871707294U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h__Script_AuracronHarmonyEngineBridge_4081382685(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEnginePlayerComponent_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
