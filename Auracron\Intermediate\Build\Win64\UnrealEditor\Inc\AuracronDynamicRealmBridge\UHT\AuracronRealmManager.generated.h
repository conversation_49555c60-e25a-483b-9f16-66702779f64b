// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronRealmManager.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronRealmManager_generated_h
#error "AuracronRealmManager.generated.h already included, missing '#pragma once' in AuracronRealmManager.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronRealmManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class ERealmEvolutionPhase : uint8;
struct FAuracronRealmEnvironment;

// ********** Begin ScriptStruct FAuracronRealmContentConfig ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmContentConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmContentConfig;
// ********** End ScriptStruct FAuracronRealmContentConfig *****************************************

// ********** Begin ScriptStruct FAuracronRealmEnvironment *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmEnvironment_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmEnvironment;
// ********** End ScriptStruct FAuracronRealmEnvironment *******************************************

// ********** Begin Class AAuracronRealmManager ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDebugShowPerformanceStats); \
	DECLARE_FUNCTION(execDebugRegenerateContent); \
	DECLARE_FUNCTION(execDebugShowLayerBounds); \
	DECLARE_FUNCTION(execCullDistantContent); \
	DECLARE_FUNCTION(execGetCurrentPerformanceMetric); \
	DECLARE_FUNCTION(execOptimizeLayerPerformance); \
	DECLARE_FUNCTION(execSetEnvironmentSettings); \
	DECLARE_FUNCTION(execUpdateWeatherEffects); \
	DECLARE_FUNCTION(execApplyEnvironmentalEffects); \
	DECLARE_FUNCTION(execUpdateContentBasedOnPhase); \
	DECLARE_FUNCTION(execDespawnRealmContent); \
	DECLARE_FUNCTION(execSpawnRealmContent); \
	DECLARE_FUNCTION(execSetLayerLODLevel); \
	DECLARE_FUNCTION(execSetLayerVisibility); \
	DECLARE_FUNCTION(execUpdateLayerEvolution); \
	DECLARE_FUNCTION(execGenerateLayerContent); \
	DECLARE_FUNCTION(execInitializeLayer);


AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronRealmManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronRealmManager(); \
	friend struct Z_Construct_UClass_AAuracronRealmManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronRealmManager_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronRealmManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronRealmManager_NoRegister) \
	DECLARE_SERIALIZER(AAuracronRealmManager)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronRealmManager(AAuracronRealmManager&&) = delete; \
	AAuracronRealmManager(const AAuracronRealmManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronRealmManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronRealmManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronRealmManager) \
	NO_API virtual ~AAuracronRealmManager();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_129_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h_132_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronRealmManager;

// ********** End Class AAuracronRealmManager ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmManager_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
