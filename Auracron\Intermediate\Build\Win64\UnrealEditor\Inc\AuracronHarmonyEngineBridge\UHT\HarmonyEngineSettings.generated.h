// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "HarmonyEngineSettings.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_HarmonyEngineSettings_generated_h
#error "HarmonyEngineSettings.generated.h already included, missing '#pragma once' in HarmonyEngineSettings.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_HarmonyEngineSettings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UHarmonyEngineSettings;

// ********** Begin Class UHarmonyEngineSettings ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetHarmonyEngineSettings); \
	DECLARE_FUNCTION(execApplySettings); \
	DECLARE_FUNCTION(execResetToDefaults); \
	DECLARE_FUNCTION(execValidateSettings);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUHarmonyEngineSettings(); \
	friend struct Z_Construct_UClass_UHarmonyEngineSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UHarmonyEngineSettings, UDeveloperSettings, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UHarmonyEngineSettings_NoRegister) \
	DECLARE_SERIALIZER(UHarmonyEngineSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UHarmonyEngineSettings(UHarmonyEngineSettings&&) = delete; \
	UHarmonyEngineSettings(const UHarmonyEngineSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UHarmonyEngineSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UHarmonyEngineSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UHarmonyEngineSettings) \
	NO_API virtual ~UHarmonyEngineSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_13_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UHarmonyEngineSettings;

// ********** End Class UHarmonyEngineSettings *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineSettings_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
