// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdvancedNetworkingCoordinator.h"

#ifdef AURACRONNETWORKINGBRIDGE_AuracronAdvancedNetworkingCoordinator_generated_h
#error "AuracronAdvancedNetworkingCoordinator.generated.h already included, missing '#pragma once' in AuracronAdvancedNetworkingCoordinator.h"
#endif
#define AURACRONNETWORKINGBRIDGE_AuracronAdvancedNetworkingCoordinator_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class AActor;
class APlayerController;
class UObject;
enum class EAuracronAntiCheatLevel : uint8;
enum class ENetworkOptimizationStrategy : uint8;
struct FAuracronAntiCheatValidation;
struct FAuracronIrisReplicationConfig;
struct FAuracronMultiplayerSessionConfig;
struct FAuracronNetworkPredictionConfig;
struct FAuracronNetworkQualityMetrics;

// ********** Begin ScriptStruct FAuracronNetworkQualityMetrics ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_119_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronNetworkQualityMetrics;
// ********** End ScriptStruct FAuracronNetworkQualityMetrics **************************************

// ********** Begin ScriptStruct FAuracronAdvancedReplicationPriority ******************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_172_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAdvancedReplicationPriority_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAdvancedReplicationPriority;
// ********** End ScriptStruct FAuracronAdvancedReplicationPriority ********************************

// ********** Begin ScriptStruct FAuracronNetworkPredictionConfig **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_215_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronNetworkPredictionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronNetworkPredictionConfig;
// ********** End ScriptStruct FAuracronNetworkPredictionConfig ************************************

// ********** Begin ScriptStruct FAuracronIrisReplicationConfig ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_258_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronIrisReplicationConfig;
// ********** End ScriptStruct FAuracronIrisReplicationConfig **************************************

// ********** Begin ScriptStruct FAuracronMultiplayerSessionConfig *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_306_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMultiplayerSessionConfig;
// ********** End ScriptStruct FAuracronMultiplayerSessionConfig ***********************************

// ********** Begin ScriptStruct FAuracronAntiCheatValidation **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_358_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAntiCheatValidation_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAntiCheatValidation;
// ********** End ScriptStruct FAuracronAntiCheatValidation ****************************************

// ********** Begin Class UAuracronAdvancedNetworkingCoordinator ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execReportNetworkAnomaly); \
	DECLARE_FUNCTION(execValidateNetworkAction); \
	DECLARE_FUNCTION(execCoordinateAntiCheatSystems); \
	DECLARE_FUNCTION(execSetPlayerBandwidthLimit); \
	DECLARE_FUNCTION(execOptimizeBandwidthAllocation); \
	DECLARE_FUNCTION(execMonitorBandwidthUsage); \
	DECLARE_FUNCTION(execReconcileClientPrediction); \
	DECLARE_FUNCTION(execUpdatePredictionBuffer); \
	DECLARE_FUNCTION(execEnableNetworkPredictionForActor); \
	DECLARE_FUNCTION(execValidateClientActionWithLagCompensation); \
	DECLARE_FUNCTION(execApplyLagCompensationForAction); \
	DECLARE_FUNCTION(execConfigureLagCompensation); \
	DECLARE_FUNCTION(execSetActorReplicationPriority); \
	DECLARE_FUNCTION(execOptimizeReplicationForPlayer); \
	DECLARE_FUNCTION(execUpdateReplicationPriorities); \
	DECLARE_FUNCTION(execSetReplicationOptimizationStrategy); \
	DECLARE_FUNCTION(execSyncBridgeStateAcrossNetwork); \
	DECLARE_FUNCTION(execForceServerReconciliation); \
	DECLARE_FUNCTION(execValidateClientStateAgainstServer); \
	DECLARE_FUNCTION(execEnableAuthoritativeModeForSystem); \
	DECLARE_FUNCTION(execBanPlayerWithEvidence); \
	DECLARE_FUNCTION(execReportSuspiciousActivity); \
	DECLARE_FUNCTION(execValidatePlayerActionServerSide); \
	DECLARE_FUNCTION(execInitializeAdvancedAntiCheat); \
	DECLARE_FUNCTION(execUpdateSessionSettings); \
	DECLARE_FUNCTION(execGetActiveSessionInfo); \
	DECLARE_FUNCTION(execLeaveMultiplayerSession); \
	DECLARE_FUNCTION(execJoinMultiplayerSession); \
	DECLARE_FUNCTION(execCreateMultiplayerSession); \
	DECLARE_FUNCTION(execGetIrisReplicationMetrics); \
	DECLARE_FUNCTION(execOptimizeIrisReplicationGraph); \
	DECLARE_FUNCTION(execRegisterObjectForIrisReplication); \
	DECLARE_FUNCTION(execConfigureIrisReplication); \
	DECLARE_FUNCTION(execInitializeIrisReplicationSystem); \
	DECLARE_FUNCTION(execGetNetworkQualityMetrics); \
	DECLARE_FUNCTION(execOptimizeNetworkPerformance); \
	DECLARE_FUNCTION(execUpdateNetworkingCoordination); \
	DECLARE_FUNCTION(execInitializeNetworkingCoordinator);


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_CALLBACK_WRAPPERS
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAdvancedNetworkingCoordinator(); \
	friend struct Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAdvancedNetworkingCoordinator, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronNetworkingBridge"), Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAdvancedNetworkingCoordinator)


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronAdvancedNetworkingCoordinator(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAdvancedNetworkingCoordinator(UAuracronAdvancedNetworkingCoordinator&&) = delete; \
	UAuracronAdvancedNetworkingCoordinator(const UAuracronAdvancedNetworkingCoordinator&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAdvancedNetworkingCoordinator); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAdvancedNetworkingCoordinator); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAdvancedNetworkingCoordinator) \
	NO_API virtual ~UAuracronAdvancedNetworkingCoordinator();


#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_401_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h_404_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAdvancedNetworkingCoordinator;

// ********** End Class UAuracronAdvancedNetworkingCoordinator *************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronAdvancedNetworkingCoordinator_h

// ********** Begin Enum ENetworkOptimizationStrategy **********************************************
#define FOREACH_ENUM_ENETWORKOPTIMIZATIONSTRATEGY(op) \
	op(ENetworkOptimizationStrategy::Latency) \
	op(ENetworkOptimizationStrategy::Bandwidth) \
	op(ENetworkOptimizationStrategy::Reliability) \
	op(ENetworkOptimizationStrategy::Balanced) \
	op(ENetworkOptimizationStrategy::Adaptive) \
	op(ENetworkOptimizationStrategy::Custom) 

enum class ENetworkOptimizationStrategy : uint8;
template<> struct TIsUEnumClass<ENetworkOptimizationStrategy> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<ENetworkOptimizationStrategy>();
// ********** End Enum ENetworkOptimizationStrategy ************************************************

// ********** Begin Enum EAuracronIrisReplicationMode **********************************************
#define FOREACH_ENUM_EAURACRONIRISREPLICATIONMODE(op) \
	op(EAuracronIrisReplicationMode::Standard) \
	op(EAuracronIrisReplicationMode::HighFrequency) \
	op(EAuracronIrisReplicationMode::LowLatency) \
	op(EAuracronIrisReplicationMode::Optimized) \
	op(EAuracronIrisReplicationMode::Custom) 

enum class EAuracronIrisReplicationMode : uint8;
template<> struct TIsUEnumClass<EAuracronIrisReplicationMode> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronIrisReplicationMode>();
// ********** End Enum EAuracronIrisReplicationMode ************************************************

// ********** Begin Enum EAuracronAntiCheatLevel ***************************************************
#define FOREACH_ENUM_EAURACRONANTICHEATLEVEL(op) \
	op(EAuracronAntiCheatLevel::Basic) \
	op(EAuracronAntiCheatLevel::Standard) \
	op(EAuracronAntiCheatLevel::Advanced) \
	op(EAuracronAntiCheatLevel::Strict) \
	op(EAuracronAntiCheatLevel::Paranoid) 

enum class EAuracronAntiCheatLevel : uint8;
template<> struct TIsUEnumClass<EAuracronAntiCheatLevel> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronAntiCheatLevel>();
// ********** End Enum EAuracronAntiCheatLevel *****************************************************

// ********** Begin Enum EAuracronSessionType ******************************************************
#define FOREACH_ENUM_EAURACRONSESSIONTYPE(op) \
	op(EAuracronSessionType::Solo) \
	op(EAuracronSessionType::Cooperative) \
	op(EAuracronSessionType::Competitive) \
	op(EAuracronSessionType::Realm) \
	op(EAuracronSessionType::Guild) \
	op(EAuracronSessionType::Event) 

enum class EAuracronSessionType : uint8;
template<> struct TIsUEnumClass<EAuracronSessionType> { enum { Value = true }; };
template<> AURACRONNETWORKINGBRIDGE_API UEnum* StaticEnum<EAuracronSessionType>();
// ********** End Enum EAuracronSessionType ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
