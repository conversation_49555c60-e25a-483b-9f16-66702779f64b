// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronDynamicRail.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRail_generated_h
#error "AuracronDynamicRail.generated.h already included, missing '#pragma once' in AuracronDynamicRail.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRail_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class APawn;
class UPrimitiveComponent;
struct FHitResult;

// ********** Begin ScriptStruct FRailMovementData *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRailMovementData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRailMovementData;
// ********** End ScriptStruct FRailMovementData ***************************************************

// ********** Begin ScriptStruct FRailVisualConfig *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_76_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRailVisualConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRailVisualConfig;
// ********** End ScriptStruct FRailVisualConfig ***************************************************

// ********** Begin Class AAuracronDynamicRail *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnExitPointBeginOverlap); \
	DECLARE_FUNCTION(execOnEntryPointBeginOverlap); \
	DECLARE_FUNCTION(execPlayRailDeactivationEffects); \
	DECLARE_FUNCTION(execPlayRailActivationEffects); \
	DECLARE_FUNCTION(execUpdateRailVisibility); \
	DECLARE_FUNCTION(execUpdateRailVisuals); \
	DECLARE_FUNCTION(execUpdateLunarRailBehavior); \
	DECLARE_FUNCTION(execUpdateAxisRailBehavior); \
	DECLARE_FUNCTION(execUpdateSolarRailBehavior); \
	DECLARE_FUNCTION(execGetPlayerRailPosition); \
	DECLARE_FUNCTION(execGetPlayerRailProgress); \
	DECLARE_FUNCTION(execSetPlayerRailSpeed); \
	DECLARE_FUNCTION(execUpdatePlayerMovement); \
	DECLARE_FUNCTION(execGetPlayersOnRail); \
	DECLARE_FUNCTION(execIsPlayerOnRail); \
	DECLARE_FUNCTION(execStopPlayerMovement); \
	DECLARE_FUNCTION(execStartPlayerMovement); \
	DECLARE_FUNCTION(execCanPlayerUseRail); \
	DECLARE_FUNCTION(execGetRailPath); \
	DECLARE_FUNCTION(execSetRailPath); \
	DECLARE_FUNCTION(execIsRailActive); \
	DECLARE_FUNCTION(execDeactivateRail); \
	DECLARE_FUNCTION(execActivateRail);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronDynamicRail(); \
	friend struct Z_Construct_UClass_AAuracronDynamicRail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronDynamicRail, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronDynamicRail_NoRegister) \
	DECLARE_SERIALIZER(AAuracronDynamicRail)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronDynamicRail(AAuracronDynamicRail&&) = delete; \
	AAuracronDynamicRail(const AAuracronDynamicRail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronDynamicRail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronDynamicRail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronDynamicRail) \
	NO_API virtual ~AAuracronDynamicRail();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_143_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h_146_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronDynamicRail;

// ********** End Class AAuracronDynamicRail *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRail_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
