// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanPythonBindings.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanPythonBindings_generated_h
#error "AuracronMetaHumanPythonBindings.generated.h already included, missing '#pragma once' in AuracronMetaHumanPythonBindings.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanPythonBindings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UAuracronMetaHumanFramework;
class UObject;
enum class EAuracronPythonAPICategory : uint8;
struct FAuracronPythonAPIDoc;
struct FAuracronPythonResult;
struct FAuracronPythonScriptConfig;

// ********** Begin ScriptStruct FAuracronPythonResult *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_56_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonResult;
// ********** End ScriptStruct FAuracronPythonResult ***********************************************

// ********** Begin ScriptStruct FAuracronPythonScriptConfig ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_100_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonScriptConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonScriptConfig;
// ********** End ScriptStruct FAuracronPythonScriptConfig *****************************************

// ********** Begin ScriptStruct FAuracronPythonAPIDoc *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_138_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPythonAPIDoc_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPythonAPIDoc;
// ********** End ScriptStruct FAuracronPythonAPIDoc ***********************************************

// ********** Begin Delegate FAuracronPythonScriptComplete *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_174_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronPythonScriptComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronPythonScriptComplete, FAuracronPythonResult const& Result, const FString& ScriptID);


// ********** End Delegate FAuracronPythonScriptComplete *******************************************

// ********** Begin Class UAuracronMetaHumanPythonBindings *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetPythonSystemInfo); \
	DECLARE_FUNCTION(execGetPythonVersion); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetPythonExecutionStatistics); \
	DECLARE_FUNCTION(execProfilePythonScript); \
	DECLARE_FUNCTION(execSetPythonBreakpoint); \
	DECLARE_FUNCTION(execEnablePythonDebugging); \
	DECLARE_FUNCTION(execGetPythonScriptTemplates); \
	DECLARE_FUNCTION(execValidatePythonScript); \
	DECLARE_FUNCTION(execSavePythonScriptToFile); \
	DECLARE_FUNCTION(execLoadPythonScriptFromFile); \
	DECLARE_FUNCTION(execConvertPythonObjectToUE); \
	DECLARE_FUNCTION(execConvertUEObjectToPython); \
	DECLARE_FUNCTION(execGetPythonVariable); \
	DECLARE_FUNCTION(execSetPythonVariable); \
	DECLARE_FUNCTION(execGetAPIDocumentation); \
	DECLARE_FUNCTION(execGetAvailableAPIFunctions); \
	DECLARE_FUNCTION(execUnregisterMetaHumanAPIs); \
	DECLARE_FUNCTION(execRegisterMetaHumanAPIs); \
	DECLARE_FUNCTION(execListInstalledPythonPackages); \
	DECLARE_FUNCTION(execInstallPythonPackage); \
	DECLARE_FUNCTION(execImportPythonModule); \
	DECLARE_FUNCTION(execShutdownPythonEnvironment); \
	DECLARE_FUNCTION(execInitializePythonEnvironment); \
	DECLARE_FUNCTION(execGetAsyncExecutionResult); \
	DECLARE_FUNCTION(execIsAsyncExecutionComplete); \
	DECLARE_FUNCTION(execExecutePythonCodeAsync); \
	DECLARE_FUNCTION(execExecutePythonScriptAsync); \
	DECLARE_FUNCTION(execEvaluatePythonExpression); \
	DECLARE_FUNCTION(execExecutePythonFile); \
	DECLARE_FUNCTION(execExecutePythonCode); \
	DECLARE_FUNCTION(execExecutePythonScript);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanPythonBindings(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanPythonBindings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanPythonBindings, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanPythonBindings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanPythonBindings)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanPythonBindings(UAuracronMetaHumanPythonBindings&&) = delete; \
	UAuracronMetaHumanPythonBindings(const UAuracronMetaHumanPythonBindings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanPythonBindings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanPythonBindings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanPythonBindings)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_180_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h_183_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanPythonBindings;

// ********** End Class UAuracronMetaHumanPythonBindings *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanPythonBindings_h

// ********** Begin Enum EAuracronPythonAPICategory ************************************************
#define FOREACH_ENUM_EAURACRONPYTHONAPICATEGORY(op) \
	op(EAuracronPythonAPICategory::Core) \
	op(EAuracronPythonAPICategory::DNA) \
	op(EAuracronPythonAPICategory::Animation) \
	op(EAuracronPythonAPICategory::Texture) \
	op(EAuracronPythonAPICategory::Performance) \
	op(EAuracronPythonAPICategory::Clothing) \
	op(EAuracronPythonAPICategory::Utility) \
	op(EAuracronPythonAPICategory::All) 

enum class EAuracronPythonAPICategory : uint8;
template<> struct TIsUEnumClass<EAuracronPythonAPICategory> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronPythonAPICategory>();
// ********** End Enum EAuracronPythonAPICategory **************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
