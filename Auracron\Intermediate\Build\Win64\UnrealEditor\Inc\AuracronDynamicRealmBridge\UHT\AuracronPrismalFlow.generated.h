// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPrismalFlow.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronPrismalFlow_generated_h
#error "AuracronPrismalFlow.generated.h already included, missing '#pragma once' in AuracronPrismalFlow.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronPrismalFlow_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AAuracronPrismalIsland;
enum class EPrismalIslandType : uint8;

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPrismalFlowSegment;
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin ScriptStruct FPrismalFlowConfig ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_82_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPrismalFlowConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPrismalFlowConfig;
// ********** End ScriptStruct FPrismalFlowConfig **************************************************

// ********** Begin Class AAuracronPrismalFlow *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDebugRegenerateFlow); \
	DECLARE_FUNCTION(execDebugShowIslandLocations); \
	DECLARE_FUNCTION(execDebugShowFlowPath); \
	DECLARE_FUNCTION(execPlayFlowTransitionEffect); \
	DECLARE_FUNCTION(execSetFlowIntensity); \
	DECLARE_FUNCTION(execUpdateFlowVisuals); \
	DECLARE_FUNCTION(execApplyFlowEffectToActor); \
	DECLARE_FUNCTION(execGetFlowIntensityAtLocation); \
	DECLARE_FUNCTION(execGetFlowVelocityAtLocation); \
	DECLARE_FUNCTION(execIsLocationInFlow); \
	DECLARE_FUNCTION(execGetNearestIsland); \
	DECLARE_FUNCTION(execGetIslandsByType); \
	DECLARE_FUNCTION(execUpdateIslandStates); \
	DECLARE_FUNCTION(execSpawnPrismalIslands); \
	DECLARE_FUNCTION(execUpdateFlowColors); \
	DECLARE_FUNCTION(execGetSegmentControl); \
	DECLARE_FUNCTION(execSetSegmentControl); \
	DECLARE_FUNCTION(execGetFlowSpeed); \
	DECLARE_FUNCTION(execSetFlowSpeed); \
	DECLARE_FUNCTION(execUpdateFlowDynamics); \
	DECLARE_FUNCTION(execRegenerateFlowPattern); \
	DECLARE_FUNCTION(execInitializeFlow);


AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronPrismalFlow(); \
	friend struct Z_Construct_UClass_AAuracronPrismalFlow_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronPrismalFlow, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronPrismalFlow_NoRegister) \
	DECLARE_SERIALIZER(AAuracronPrismalFlow)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronPrismalFlow(AAuracronPrismalFlow&&) = delete; \
	AAuracronPrismalFlow(const AAuracronPrismalFlow&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronPrismalFlow); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronPrismalFlow); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronPrismalFlow) \
	NO_API virtual ~AAuracronPrismalFlow();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_134_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h_137_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronPrismalFlow;

// ********** End Class AAuracronPrismalFlow *******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalFlow_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
