// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronChampionsBridge_init() {}
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature();
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature();
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature();
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature();
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature();
	AURACRONCHAMPIONSBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronChampionsBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronChampionsBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronChampionsBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAbilityActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnAttributesUpdated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionDied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSelected__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UAuracronChampionsBridge_OnChampionSpawned__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronChampionsBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x0F238FA5,
				0xDEC62D15,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronChampionsBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronChampionsBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronChampionsBridge(Z_Construct_UPackage__Script_AuracronChampionsBridge, TEXT("/Script/AuracronChampionsBridge"), Z_Registration_Info_UPackage__Script_AuracronChampionsBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x0F238FA5, 0xDEC62D15));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
