﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Specialized Logger Header
// Bridge 2.1: PCG Framework - Core Infrastructure

#pragma once

#include "CoreMinimal.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"

// Forward declarations
struct FAuracronPCGErrorInfo;
struct FAuracronPCGPerformanceMetrics;

// Log entry structure
USTRUCT()
struct AURACRONPCGBRIDGE_API FAuracronPCGLogEntry
{
    GENERATED_BODY()

    UPROPERTY()
    FDateTime Timestamp;

    UPROPERTY()
    FString LogLevel;

    UPROPERTY()
    FString Category;

    UPROPERTY()
    FString Message;

    UPROPERTY()
    FString Context;

    UPROPERTY()
    FString ThreadId;

    UPROPERTY()
    int32 LineNumber = 0;

    UPROPERTY()
    FString SourceFile;

    FAuracronPCGLogEntry()
    {
        Timestamp = FDateTime::Now();
        ThreadId = FString::Printf(TEXT("%d"), FPlatformTLS::GetCurrentThreadId());
    }

    FAuracronPCGLogEntry(const FString& InLogLevel, const FString& InCategory, const FString& InMessage, const FString& InContext = TEXT(""))
        : LogLevel(InLogLevel)
        , Category(InCategory)
        , Message(InMessage)
        , Context(InContext)
    {
        Timestamp = FDateTime::Now();
        ThreadId = FString::Printf(TEXT("%d"), FPlatformTLS::GetCurrentThreadId());
    }
};

// Log configuration
USTRUCT()
struct AURACRONPCGBRIDGE_API FAuracronPCGLogConfig
{
    GENERATED_BODY()

    UPROPERTY()
    bool bEnableFileLogging = true;

    UPROPERTY()
    bool bEnableConsoleLogging = true;

    UPROPERTY()
    bool bEnablePerformanceLogging = true;

    UPROPERTY()
    bool bEnableDetailedLogging = false;

    UPROPERTY()
    FString LogFilePath = TEXT("Saved/Logs/AuracronPCG");

    UPROPERTY()
    int32 MaxLogFileSize = 10 * 1024 * 1024; // 10MB

    UPROPERTY()
    int32 MaxLogFiles = 10;

    UPROPERTY()
    int32 MaxMemoryEntries = 1000;

    UPROPERTY()
    bool bAutoFlush = true;

    UPROPERTY()
    float FlushIntervalSeconds = 5.0f;
};

/**
 * Specialized logger for AURACRON PCG Framework
 * Provides advanced logging capabilities with performance monitoring,
 * file rotation, and thread-safe operation
 */
class AURACRONPCGBRIDGE_API FAuracronPCGLogger
{
public:
    FAuracronPCGLogger();
    ~FAuracronPCGLogger();

    // Initialization and shutdown
    bool Initialize(const FAuracronPCGLogConfig& Config = FAuracronPCGLogConfig());
    void Shutdown();
    bool IsInitialized() const { return bIsInitialized; }

    // Configuration
    void SetConfiguration(const FAuracronPCGLogConfig& NewConfig);
    const FAuracronPCGLogConfig& GetConfiguration() const { return Configuration; }

    // Core logging methods
    void Log(const FString& LogLevel, const FString& Category, const FString& Message, const FString& Context = TEXT(""));
    void LogError(const FAuracronPCGErrorInfo& ErrorInfo);
    void LogPerformance(const FAuracronPCGPerformanceMetrics& Metrics);

    // Convenience logging methods
    void LogInfo(const FString& Category, const FString& Message, const FString& Context = TEXT(""));
    void LogWarning(const FString& Category, const FString& Message, const FString& Context = TEXT(""));
    void LogError(const FString& Category, const FString& Message, const FString& Context = TEXT(""));
    void LogVerbose(const FString& Category, const FString& Message, const FString& Context = TEXT(""));

    // Log retrieval
    TArray<FAuracronPCGLogEntry> GetRecentLogs(int32 MaxEntries = 100) const;
    TArray<FAuracronPCGLogEntry> GetLogsByCategory(const FString& Category, int32 MaxEntries = 100) const;
    TArray<FAuracronPCGLogEntry> GetLogsByLevel(const FString& LogLevel, int32 MaxEntries = 100) const;
    TArray<FAuracronPCGLogEntry> GetLogsInTimeRange(const FDateTime& StartTime, const FDateTime& EndTime) const;

    // Log management
    void ClearLogs();
    void FlushLogs();
    void RotateLogFiles();

    // Statistics
    int32 GetTotalLogCount() const;
    int32 GetLogCountByLevel(const FString& LogLevel) const;
    int32 GetLogCountByCategory(const FString& Category) const;
    TMap<FString, int32> GetLogStatistics() const;

    // File operations
    bool SaveLogsToFile(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const;
    bool LoadLogsFromFile(const FString& FilePath, TArray<FAuracronPCGLogEntry>& OutLogs) const;

    // Export functionality
    bool ExportLogsToCSV(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const;
    bool ExportLogsToJSON(const FString& FilePath, const TArray<FAuracronPCGLogEntry>& Logs) const;

private:
    // Configuration
    FAuracronPCGLogConfig Configuration;
    bool bIsInitialized = false;

    // Log storage
    TArray<FAuracronPCGLogEntry> LogEntries;
    mutable FCriticalSection LogEntriesLock;

    // File handling
    FString CurrentLogFilePath;
    TUniquePtr<FArchive> LogFileArchive;
    mutable FCriticalSection FileOperationLock;

    // Statistics
    TMap<FString, int32> LogCountByLevel;
    TMap<FString, int32> LogCountByCategory;
    mutable FCriticalSection StatisticsLock;

    // Timer for auto-flush
    FTimerHandle FlushTimer;

    // Internal methods
    void WriteToFile(const FAuracronPCGLogEntry& Entry);
    void WriteToConsole(const FAuracronPCGLogEntry& Entry);
    void AddToMemory(const FAuracronPCGLogEntry& Entry);
    void UpdateStatistics(const FAuracronPCGLogEntry& Entry);

    FString FormatLogEntry(const FAuracronPCGLogEntry& Entry) const;
    FString GetLogLevelColor(const FString& LogLevel) const;
    
    bool CreateLogDirectory();
    FString GenerateLogFileName() const;
    bool ShouldRotateLogFile() const;
    void PerformLogRotation();

    void StartAutoFlushTimer();
    void StopAutoFlushTimer();
    void OnAutoFlushTimer();

    // Utility methods
    static FString GetCurrentThreadName();
    static FString SanitizeLogMessage(const FString& Message);
};

// Global logger instance access
AURACRONPCGBRIDGE_API FAuracronPCGLogger* GetAuracronPCGLogger();

// Convenience macros for logging with automatic context
#define AURACRON_PCG_LOG_WITH_CONTEXT(Level, Category, Format, ...) \
    do { \
        if (FAuracronPCGLogger* Logger = GetAuracronPCGLogger()) \
        { \
            FString Message = FString::Printf(Format, ##__VA_ARGS__); \
            FString Context = FString::Printf(TEXT("%s:%d"), TEXT(__FILE__), __LINE__); \
            Logger->Log(TEXT(#Level), TEXT(#Category), Message, Context); \
        } \
    } while(0)

#define AURACRON_PCG_LOG_INFO_CTX(Category, Format, ...) \
    AURACRON_PCG_LOG_WITH_CONTEXT(Info, Category, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_WARNING_CTX(Category, Format, ...) \
    AURACRON_PCG_LOG_WITH_CONTEXT(Warning, Category, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_ERROR_CTX(Category, Format, ...) \
    AURACRON_PCG_LOG_WITH_CONTEXT(Error, Category, Format, ##__VA_ARGS__)

#define AURACRON_PCG_LOG_VERBOSE_CTX(Category, Format, ...) \
    AURACRON_PCG_LOG_WITH_CONTEXT(Verbose, Category, Format, ##__VA_ARGS__)

// Performance logging macros
#define AURACRON_PCG_LOG_PERFORMANCE_START(OperationName) \
    double AURACRON_PCG_PerfStart_##OperationName = FPlatformTime::Seconds()

#define AURACRON_PCG_LOG_PERFORMANCE_END(OperationName) \
    do { \
        double Duration = FPlatformTime::Seconds() - AURACRON_PCG_PerfStart_##OperationName; \
        AURACRON_PCG_LOG_INFO_CTX(Performance, TEXT("Operation '%s' took %.6f seconds"), TEXT(#OperationName), Duration); \
    } while(0)

// Scoped performance logging
class AURACRONPCGBRIDGE_API FAuracronPCGScopedPerformanceLogger
{
public:
    FAuracronPCGScopedPerformanceLogger(const FString& InOperationName);
    ~FAuracronPCGScopedPerformanceLogger();

private:
    FString OperationName;
    double StartTime;
};

#define AURACRON_PCG_SCOPED_PERFORMANCE_LOG(OperationName) \
    FAuracronPCGScopedPerformanceLogger AURACRON_PCG_ScopedPerfLogger_##OperationName(TEXT(#OperationName))
