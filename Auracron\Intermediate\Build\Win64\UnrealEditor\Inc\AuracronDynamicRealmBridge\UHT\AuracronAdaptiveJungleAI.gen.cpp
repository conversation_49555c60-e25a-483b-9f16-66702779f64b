// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronAdaptiveJungleAI.h"
#include "AuracronAdaptiveCreaturesBridge/Public/AuracronAdaptiveCreaturesBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdaptiveJungleAI() {}

// ********** Begin Cross Module References ********************************************************
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTree_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UBlackboardData_NoRegister();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCreatureAdaptation();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronJungleCreature_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronJungleAILearningData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPlayerBehaviorPattern ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPlayerBehaviorPattern;
static UEnum* EPlayerBehaviorPattern_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPlayerBehaviorPattern.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPlayerBehaviorPattern.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPlayerBehaviorPattern"));
	}
	return Z_Registration_Info_UEnum_EPlayerBehaviorPattern.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPlayerBehaviorPattern>()
{
	return EPlayerBehaviorPattern_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EPlayerBehaviorPattern::Adaptive" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EPlayerBehaviorPattern::Aggressive" },
		{ "BlueprintType", "true" },
		{ "Cautious.DisplayName", "Cautious" },
		{ "Cautious.Name", "EPlayerBehaviorPattern::Cautious" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Player behavior pattern types for ML analysis\n */" },
#endif
		{ "Efficient.DisplayName", "Efficient" },
		{ "Efficient.Name", "EPlayerBehaviorPattern::Efficient" },
		{ "Exploratory.DisplayName", "Exploratory" },
		{ "Exploratory.Name", "EPlayerBehaviorPattern::Exploratory" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
		{ "Social.DisplayName", "Social" },
		{ "Social.Name", "EPlayerBehaviorPattern::Social" },
		{ "Stealth.DisplayName", "Stealth" },
		{ "Stealth.Name", "EPlayerBehaviorPattern::Stealth" },
		{ "Territorial.DisplayName", "Territorial" },
		{ "Territorial.Name", "EPlayerBehaviorPattern::Territorial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player behavior pattern types for ML analysis" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPlayerBehaviorPattern::Aggressive", (int64)EPlayerBehaviorPattern::Aggressive },
		{ "EPlayerBehaviorPattern::Cautious", (int64)EPlayerBehaviorPattern::Cautious },
		{ "EPlayerBehaviorPattern::Exploratory", (int64)EPlayerBehaviorPattern::Exploratory },
		{ "EPlayerBehaviorPattern::Efficient", (int64)EPlayerBehaviorPattern::Efficient },
		{ "EPlayerBehaviorPattern::Social", (int64)EPlayerBehaviorPattern::Social },
		{ "EPlayerBehaviorPattern::Stealth", (int64)EPlayerBehaviorPattern::Stealth },
		{ "EPlayerBehaviorPattern::Territorial", (int64)EPlayerBehaviorPattern::Territorial },
		{ "EPlayerBehaviorPattern::Adaptive", (int64)EPlayerBehaviorPattern::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPlayerBehaviorPattern",
	"EPlayerBehaviorPattern",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern()
{
	if (!Z_Registration_Info_UEnum_EPlayerBehaviorPattern.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPlayerBehaviorPattern.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPlayerBehaviorPattern.InnerSingleton;
}
// ********** End Enum EPlayerBehaviorPattern ******************************************************

// ********** Begin Enum ECreatureAdaptationType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECreatureAdaptationType;
static UEnum* ECreatureAdaptationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECreatureAdaptationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECreatureAdaptationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ECreatureAdaptationType"));
	}
	return Z_Registration_Info_UEnum_ECreatureAdaptationType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ECreatureAdaptationType>()
{
	return ECreatureAdaptationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Behavioral.DisplayName", "Behavioral" },
		{ "Behavioral.Name", "ECreatureAdaptationType::Behavioral" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Creature adaptation types\n */" },
#endif
		{ "Environmental.DisplayName", "Environmental" },
		{ "Environmental.Name", "ECreatureAdaptationType::Environmental" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
		{ "Social.DisplayName", "Social" },
		{ "Social.Name", "ECreatureAdaptationType::Social" },
		{ "Statistical.DisplayName", "Statistical" },
		{ "Statistical.Name", "ECreatureAdaptationType::Statistical" },
		{ "Tactical.DisplayName", "Tactical" },
		{ "Tactical.Name", "ECreatureAdaptationType::Tactical" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creature adaptation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECreatureAdaptationType::Behavioral", (int64)ECreatureAdaptationType::Behavioral },
		{ "ECreatureAdaptationType::Statistical", (int64)ECreatureAdaptationType::Statistical },
		{ "ECreatureAdaptationType::Environmental", (int64)ECreatureAdaptationType::Environmental },
		{ "ECreatureAdaptationType::Social", (int64)ECreatureAdaptationType::Social },
		{ "ECreatureAdaptationType::Tactical", (int64)ECreatureAdaptationType::Tactical },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ECreatureAdaptationType",
	"ECreatureAdaptationType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType()
{
	if (!Z_Registration_Info_UEnum_ECreatureAdaptationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECreatureAdaptationType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECreatureAdaptationType.InnerSingleton;
}
// ********** End Enum ECreatureAdaptationType *****************************************************

// ********** Begin Enum EJungleAIDifficulty *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EJungleAIDifficulty;
static UEnum* EJungleAIDifficulty_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EJungleAIDifficulty.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EJungleAIDifficulty.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EJungleAIDifficulty"));
	}
	return Z_Registration_Info_UEnum_EJungleAIDifficulty.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleAIDifficulty>()
{
	return EJungleAIDifficulty_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EJungleAIDifficulty::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Jungle AI difficulty scaling modes\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
		{ "Progressive.DisplayName", "Progressive" },
		{ "Progressive.Name", "EJungleAIDifficulty::Progressive" },
		{ "Reactive.DisplayName", "Reactive" },
		{ "Reactive.Name", "EJungleAIDifficulty::Reactive" },
		{ "Static.DisplayName", "Static" },
		{ "Static.Name", "EJungleAIDifficulty::Static" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle AI difficulty scaling modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EJungleAIDifficulty::Adaptive", (int64)EJungleAIDifficulty::Adaptive },
		{ "EJungleAIDifficulty::Static", (int64)EJungleAIDifficulty::Static },
		{ "EJungleAIDifficulty::Progressive", (int64)EJungleAIDifficulty::Progressive },
		{ "EJungleAIDifficulty::Reactive", (int64)EJungleAIDifficulty::Reactive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EJungleAIDifficulty",
	"EJungleAIDifficulty",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty()
{
	if (!Z_Registration_Info_UEnum_EJungleAIDifficulty.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EJungleAIDifficulty.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EJungleAIDifficulty.InnerSingleton;
}
// ********** End Enum EJungleAIDifficulty *********************************************************

// ********** Begin ScriptStruct FAuracronPlayerBehaviorAnalysis ***********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis;
class UScriptStruct* FAuracronPlayerBehaviorAnalysis::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronPlayerBehaviorAnalysis"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Player behavior analysis data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player behavior analysis data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player identifier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DominantPattern_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dominant behavior pattern */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dominant behavior pattern" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryPatterns_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Secondary behavior patterns */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Secondary behavior patterns" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AggressionLevel_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aggression level (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aggression level (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExplorationTendency_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Exploration tendency (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Exploration tendency (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RiskTolerance_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Risk tolerance (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Risk tolerance (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SocialFrequency_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Social interaction frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Social interaction frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkillLevel_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Skill level estimation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Skill level estimation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PredictabilityScore_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Predictability score */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Predictability score" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalysisConfidence_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analysis confidence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analysis confidence" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAnalysisTime_MetaData[] = {
		{ "Category", "Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last analysis time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last analysis time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DominantPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DominantPattern;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SecondaryPatterns_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SecondaryPatterns_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SecondaryPatterns;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AggressionLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExplorationTendency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RiskTolerance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SocialFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SkillLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PredictabilityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnalysisConfidence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastAnalysisTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronPlayerBehaviorAnalysis>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_DominantPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_DominantPattern = { "DominantPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, DominantPattern), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DominantPattern_MetaData), NewProp_DominantPattern_MetaData) }; // 1949688724
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns_Inner = { "SecondaryPatterns", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(0, nullptr) }; // 1949688724
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns = { "SecondaryPatterns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, SecondaryPatterns), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryPatterns_MetaData), NewProp_SecondaryPatterns_MetaData) }; // 1949688724
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_AggressionLevel = { "AggressionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, AggressionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AggressionLevel_MetaData), NewProp_AggressionLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_ExplorationTendency = { "ExplorationTendency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, ExplorationTendency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExplorationTendency_MetaData), NewProp_ExplorationTendency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_RiskTolerance = { "RiskTolerance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, RiskTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RiskTolerance_MetaData), NewProp_RiskTolerance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SocialFrequency = { "SocialFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, SocialFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SocialFrequency_MetaData), NewProp_SocialFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SkillLevel = { "SkillLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, SkillLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkillLevel_MetaData), NewProp_SkillLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_PredictabilityScore = { "PredictabilityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, PredictabilityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PredictabilityScore_MetaData), NewProp_PredictabilityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_AnalysisConfidence = { "AnalysisConfidence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, AnalysisConfidence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalysisConfidence_MetaData), NewProp_AnalysisConfidence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_LastAnalysisTime = { "LastAnalysisTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronPlayerBehaviorAnalysis, LastAnalysisTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAnalysisTime_MetaData), NewProp_LastAnalysisTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_DominantPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_DominantPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SecondaryPatterns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_AggressionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_ExplorationTendency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_RiskTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SocialFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_SkillLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_PredictabilityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_AnalysisConfidence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewProp_LastAnalysisTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronPlayerBehaviorAnalysis",
	Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::PropPointers),
	sizeof(FAuracronPlayerBehaviorAnalysis),
	alignof(FAuracronPlayerBehaviorAnalysis),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.InnerSingleton, Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis.InnerSingleton;
}
// ********** End ScriptStruct FAuracronPlayerBehaviorAnalysis *************************************

// ********** Begin ScriptStruct FAuracronJungleCreatureAdaptation *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation;
class UScriptStruct* FAuracronJungleCreatureAdaptation::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronJungleCreatureAdaptation"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Creature adaptation configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creature adaptation configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationType_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptation type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptation type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPattern_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target player behavior pattern */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target player behavior pattern" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationStrength_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptation strength (0.0 to 2.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptation strength (0.0 to 2.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationDuration_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptation duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptation duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptedBehaviorTree_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Behavior tree to use */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior tree to use" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlackboardOverrides_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blackboard overrides */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard overrides" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationEffects_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gameplay effects to apply */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay effects to apply" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptationType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetPattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetPattern;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AdaptedBehaviorTree;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlackboardOverrides_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlackboardOverrides_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BlackboardOverrides;
	static const UECodeGen_Private::FClassPropertyParams NewProp_AdaptationEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdaptationEffects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronJungleCreatureAdaptation>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationType = { "AdaptationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, AdaptationType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationType_MetaData), NewProp_AdaptationType_MetaData) }; // 1378412969
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_TargetPattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_TargetPattern = { "TargetPattern", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, TargetPattern), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPattern_MetaData), NewProp_TargetPattern_MetaData) }; // 1949688724
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationStrength = { "AdaptationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, AdaptationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationStrength_MetaData), NewProp_AdaptationStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationDuration = { "AdaptationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, AdaptationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationDuration_MetaData), NewProp_AdaptationDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptedBehaviorTree = { "AdaptedBehaviorTree", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, AdaptedBehaviorTree), Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptedBehaviorTree_MetaData), NewProp_AdaptedBehaviorTree_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides_ValueProp = { "BlackboardOverrides", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides_Key_KeyProp = { "BlackboardOverrides_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides = { "BlackboardOverrides", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, BlackboardOverrides), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlackboardOverrides_MetaData), NewProp_BlackboardOverrides_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationEffects_Inner = { "AdaptationEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationEffects = { "AdaptationEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleCreatureAdaptation, AdaptationEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationEffects_MetaData), NewProp_AdaptationEffects_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_TargetPattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_TargetPattern,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptedBehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_BlackboardOverrides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewProp_AdaptationEffects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronJungleCreatureAdaptation",
	Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::PropPointers),
	sizeof(FAuracronJungleCreatureAdaptation),
	alignof(FAuracronJungleCreatureAdaptation),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.InnerSingleton, Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation.InnerSingleton;
}
// ********** End ScriptStruct FAuracronJungleCreatureAdaptation ***********************************

// ********** Begin ScriptStruct FAuracronJungleAILearningData *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData;
class UScriptStruct* FAuracronJungleAILearningData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronJungleAILearningData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronJungleAILearningData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Jungle AI learning data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle AI learning data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLearningSessions_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total learning sessions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total learning sessions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModelAccuracy_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Model accuracy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Model accuracy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrainingIterations_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Training iterations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Training iterations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DataPointsCollected_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data points collected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data points collected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTrainingTime_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last training time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last training time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LearningRate_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Learning rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Learning rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationSuccessRate_MetaData[] = {
		{ "Category", "Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptation success rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptation success rate" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalLearningSessions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ModelAccuracy;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TrainingIterations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DataPointsCollected;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastTrainingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LearningRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationSuccessRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronJungleAILearningData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_TotalLearningSessions = { "TotalLearningSessions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, TotalLearningSessions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLearningSessions_MetaData), NewProp_TotalLearningSessions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_ModelAccuracy = { "ModelAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, ModelAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModelAccuracy_MetaData), NewProp_ModelAccuracy_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_TrainingIterations = { "TrainingIterations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, TrainingIterations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrainingIterations_MetaData), NewProp_TrainingIterations_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_DataPointsCollected = { "DataPointsCollected", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, DataPointsCollected), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DataPointsCollected_MetaData), NewProp_DataPointsCollected_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_LastTrainingTime = { "LastTrainingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, LastTrainingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTrainingTime_MetaData), NewProp_LastTrainingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_LearningRate = { "LearningRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, LearningRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LearningRate_MetaData), NewProp_LearningRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_AdaptationSuccessRate = { "AdaptationSuccessRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronJungleAILearningData, AdaptationSuccessRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationSuccessRate_MetaData), NewProp_AdaptationSuccessRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_TotalLearningSessions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_ModelAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_TrainingIterations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_DataPointsCollected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_LastTrainingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_LearningRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewProp_AdaptationSuccessRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronJungleAILearningData",
	Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::PropPointers),
	sizeof(FAuracronJungleAILearningData),
	alignof(FAuracronJungleAILearningData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronJungleAILearningData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronJungleAILearningData ***************************************

// ********** Begin Class AAuracronAdaptiveJungleAI Function AdaptCreatureToPlayer *****************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics
{
	struct AuracronAdaptiveJungleAI_eventAdaptCreatureToPlayer_Parms
	{
		AAuracronJungleCreature* Creature;
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adapt creature to player behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adapt creature to player behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Creature;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::NewProp_Creature = { "Creature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAdaptCreatureToPlayer_Parms, Creature), Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAdaptCreatureToPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::NewProp_Creature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "AdaptCreatureToPlayer", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::AuracronAdaptiveJungleAI_eventAdaptCreatureToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::AuracronAdaptiveJungleAI_eventAdaptCreatureToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execAdaptCreatureToPlayer)
{
	P_GET_OBJECT(AAuracronJungleCreature,Z_Param_Creature);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdaptCreatureToPlayer(Z_Param_Creature,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function AdaptCreatureToPlayer *******************

// ********** Begin Class AAuracronAdaptiveJungleAI Function AdaptEnvironmentToPlayer **************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics
{
	struct AuracronAdaptiveJungleAI_eventAdaptEnvironmentToPlayer_Parms
	{
		APawn* Player;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Environmental Response" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adapt jungle environment to player behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adapt jungle environment to player behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAdaptEnvironmentToPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAdaptEnvironmentToPlayer_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "AdaptEnvironmentToPlayer", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::AuracronAdaptiveJungleAI_eventAdaptEnvironmentToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::AuracronAdaptiveJungleAI_eventAdaptEnvironmentToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execAdaptEnvironmentToPlayer)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdaptEnvironmentToPlayer(Z_Param_Player,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function AdaptEnvironmentToPlayer ****************

// ********** Begin Class AAuracronAdaptiveJungleAI Function AnalyzePlayerBehavior *****************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics
{
	struct AuracronAdaptiveJungleAI_eventAnalyzePlayerBehavior_Parms
	{
		APawn* Player;
		FAuracronPlayerBehaviorAnalysis ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Adaptive AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analyze player behavior patterns */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze player behavior patterns" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAnalyzePlayerBehavior_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventAnalyzePlayerBehavior_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis, METADATA_PARAMS(0, nullptr) }; // 3144763003
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "AnalyzePlayerBehavior", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::AuracronAdaptiveJungleAI_eventAnalyzePlayerBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::AuracronAdaptiveJungleAI_eventAnalyzePlayerBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execAnalyzePlayerBehavior)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronPlayerBehaviorAnalysis*)Z_Param__Result=P_THIS->AnalyzePlayerBehavior(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function AnalyzePlayerBehavior *******************

// ********** Begin Class AAuracronAdaptiveJungleAI Function ApplyCreatureAdaptations **************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics
{
	struct AuracronAdaptiveJungleAI_eventApplyCreatureAdaptations_Parms
	{
		FAuracronPlayerBehaviorAnalysis PlayerAnalysis;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Adaptive AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply creature adaptations based on player behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply creature adaptations based on player behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerAnalysis_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerAnalysis;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::NewProp_PlayerAnalysis = { "PlayerAnalysis", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventApplyCreatureAdaptations_Parms, PlayerAnalysis), Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerAnalysis_MetaData), NewProp_PlayerAnalysis_MetaData) }; // 3144763003
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::NewProp_PlayerAnalysis,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "ApplyCreatureAdaptations", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::AuracronAdaptiveJungleAI_eventApplyCreatureAdaptations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::AuracronAdaptiveJungleAI_eventApplyCreatureAdaptations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execApplyCreatureAdaptations)
{
	P_GET_STRUCT_REF(FAuracronPlayerBehaviorAnalysis,Z_Param_Out_PlayerAnalysis);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyCreatureAdaptations(Z_Param_Out_PlayerAnalysis);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function ApplyCreatureAdaptations ****************

// ********** Begin Class AAuracronAdaptiveJungleAI Function GetAILearningData *********************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics
{
	struct AuracronAdaptiveJungleAI_eventGetAILearningData_Parms
	{
		FAuracronJungleAILearningData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Adaptive AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current AI learning data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current AI learning data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventGetAILearningData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronJungleAILearningData, METADATA_PARAMS(0, nullptr) }; // 72641269
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "GetAILearningData", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::AuracronAdaptiveJungleAI_eventGetAILearningData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::AuracronAdaptiveJungleAI_eventGetAILearningData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execGetAILearningData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronJungleAILearningData*)Z_Param__Result=P_THIS->GetAILearningData();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function GetAILearningData ***********************

// ********** Begin Class AAuracronAdaptiveJungleAI Function GetPlayerBehaviorPattern **************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics
{
	struct AuracronAdaptiveJungleAI_eventGetPlayerBehaviorPattern_Parms
	{
		APawn* Player;
		EPlayerBehaviorPattern ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get player behavior pattern */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get player behavior pattern" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventGetPlayerBehaviorPattern_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventGetPlayerBehaviorPattern_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(0, nullptr) }; // 1949688724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "GetPlayerBehaviorPattern", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::AuracronAdaptiveJungleAI_eventGetPlayerBehaviorPattern_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::AuracronAdaptiveJungleAI_eventGetPlayerBehaviorPattern_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execGetPlayerBehaviorPattern)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPlayerBehaviorPattern*)Z_Param__Result=P_THIS->GetPlayerBehaviorPattern(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function GetPlayerBehaviorPattern ****************

// ********** Begin Class AAuracronAdaptiveJungleAI Function InitializeAdaptiveAI ******************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Adaptive AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize the adaptive AI system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize the adaptive AI system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "InitializeAdaptiveAI", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execInitializeAdaptiveAI)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAdaptiveAI();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function InitializeAdaptiveAI ********************

// ********** Begin Class AAuracronAdaptiveJungleAI Function OnAIModelRetrained ********************
struct AuracronAdaptiveJungleAI_eventOnAIModelRetrained_Parms
{
	float NewAccuracy;
};
static FName NAME_AAuracronAdaptiveJungleAI_OnAIModelRetrained = FName(TEXT("OnAIModelRetrained"));
void AAuracronAdaptiveJungleAI::OnAIModelRetrained(float NewAccuracy)
{
	AuracronAdaptiveJungleAI_eventOnAIModelRetrained_Parms Parms;
	Parms.NewAccuracy=NewAccuracy;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronAdaptiveJungleAI_OnAIModelRetrained);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when AI model is retrained */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when AI model is retrained" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewAccuracy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::NewProp_NewAccuracy = { "NewAccuracy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventOnAIModelRetrained_Parms, NewAccuracy), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::NewProp_NewAccuracy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "OnAIModelRetrained", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::PropPointers), sizeof(AuracronAdaptiveJungleAI_eventOnAIModelRetrained_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdaptiveJungleAI_eventOnAIModelRetrained_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronAdaptiveJungleAI Function OnAIModelRetrained **********************

// ********** Begin Class AAuracronAdaptiveJungleAI Function OnCreatureAdaptationApplied ***********
struct AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms
{
	AAuracronJungleCreature* Creature;
	ECreatureAdaptationType AdaptationType;
};
static FName NAME_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied = FName(TEXT("OnCreatureAdaptationApplied"));
void AAuracronAdaptiveJungleAI::OnCreatureAdaptationApplied(AAuracronJungleCreature* Creature, ECreatureAdaptationType AdaptationType)
{
	AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms Parms;
	Parms.Creature=Creature;
	Parms.AdaptationType=AdaptationType;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when creature adaptation is applied */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when creature adaptation is applied" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Creature;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptationType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_Creature = { "Creature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms, Creature), Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_AdaptationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_AdaptationType = { "AdaptationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms, AdaptationType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType, METADATA_PARAMS(0, nullptr) }; // 1378412969
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_Creature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_AdaptationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::NewProp_AdaptationType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "OnCreatureAdaptationApplied", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::PropPointers), sizeof(AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdaptiveJungleAI_eventOnCreatureAdaptationApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronAdaptiveJungleAI Function OnCreatureAdaptationApplied *************

// ********** Begin Class AAuracronAdaptiveJungleAI Function OnPlayerPatternLearned ****************
struct AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms
{
	APawn* Player;
	EPlayerBehaviorPattern Pattern;
};
static FName NAME_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned = FName(TEXT("OnPlayerPatternLearned"));
void AAuracronAdaptiveJungleAI::OnPlayerPatternLearned(APawn* Player, EPlayerBehaviorPattern Pattern)
{
	AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms Parms;
	Parms.Player=Player;
	Parms.Pattern=Pattern;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when AI learns new player pattern */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when AI learns new player pattern" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Pattern_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Pattern;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Pattern_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Pattern = { "Pattern", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms, Pattern), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(0, nullptr) }; // 1949688724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Pattern_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::NewProp_Pattern,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "OnPlayerPatternLearned", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::PropPointers), sizeof(AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdaptiveJungleAI_eventOnPlayerPatternLearned_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronAdaptiveJungleAI Function OnPlayerPatternLearned ******************

// ********** Begin Class AAuracronAdaptiveJungleAI Function RemoveCreatureAdaptations *************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics
{
	struct AuracronAdaptiveJungleAI_eventRemoveCreatureAdaptations_Parms
	{
		AAuracronJungleCreature* Creature;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove creature adaptations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove creature adaptations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Creature;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::NewProp_Creature = { "Creature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventRemoveCreatureAdaptations_Parms, Creature), Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::NewProp_Creature,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "RemoveCreatureAdaptations", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::AuracronAdaptiveJungleAI_eventRemoveCreatureAdaptations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::AuracronAdaptiveJungleAI_eventRemoveCreatureAdaptations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execRemoveCreatureAdaptations)
{
	P_GET_OBJECT(AAuracronJungleCreature,Z_Param_Creature);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveCreatureAdaptations(Z_Param_Creature);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function RemoveCreatureAdaptations ***************

// ********** Begin Class AAuracronAdaptiveJungleAI Function ResetAILearning ***********************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reset AI learning data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reset AI learning data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "ResetAILearning", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execResetAILearning)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAILearning();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function ResetAILearning *************************

// ********** Begin Class AAuracronAdaptiveJungleAI Function StartPlayerMonitoring *****************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics
{
	struct AuracronAdaptiveJungleAI_eventStartPlayerMonitoring_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Start monitoring player behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Start monitoring player behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventStartPlayerMonitoring_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "StartPlayerMonitoring", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::AuracronAdaptiveJungleAI_eventStartPlayerMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::AuracronAdaptiveJungleAI_eventStartPlayerMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execStartPlayerMonitoring)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPlayerMonitoring(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function StartPlayerMonitoring *******************

// ********** Begin Class AAuracronAdaptiveJungleAI Function StopPlayerMonitoring ******************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics
{
	struct AuracronAdaptiveJungleAI_eventStopPlayerMonitoring_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Stop monitoring player behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stop monitoring player behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventStopPlayerMonitoring_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "StopPlayerMonitoring", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::AuracronAdaptiveJungleAI_eventStopPlayerMonitoring_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::AuracronAdaptiveJungleAI_eventStopPlayerMonitoring_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execStopPlayerMonitoring)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPlayerMonitoring(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function StopPlayerMonitoring ********************

// ********** Begin Class AAuracronAdaptiveJungleAI Function TrainAIModel **************************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Train AI model with collected data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Train AI model with collected data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "TrainAIModel", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execTrainAIModel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrainAIModel();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function TrainAIModel ****************************

// ********** Begin Class AAuracronAdaptiveJungleAI Function UpdateAILearning **********************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics
{
	struct AuracronAdaptiveJungleAI_eventUpdateAILearning_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Adaptive AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update AI learning and adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update AI learning and adaptation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdateAILearning_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "UpdateAILearning", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::AuracronAdaptiveJungleAI_eventUpdateAILearning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::AuracronAdaptiveJungleAI_eventUpdateAILearning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execUpdateAILearning)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAILearning(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function UpdateAILearning ************************

// ********** Begin Class AAuracronAdaptiveJungleAI Function UpdateCreatureAIBehavior **************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics
{
	struct AuracronAdaptiveJungleAI_eventUpdateCreatureAIBehavior_Parms
	{
		AAuracronJungleCreature* Creature;
		FAuracronCreatureAdaptation Adaptation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update creature AI behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update creature AI behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Adaptation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Creature;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Adaptation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::NewProp_Creature = { "Creature", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdateCreatureAIBehavior_Parms, Creature), Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::NewProp_Adaptation = { "Adaptation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdateCreatureAIBehavior_Parms, Adaptation), Z_Construct_UScriptStruct_FAuracronCreatureAdaptation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Adaptation_MetaData), NewProp_Adaptation_MetaData) }; // 3328928115
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::NewProp_Creature,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::NewProp_Adaptation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "UpdateCreatureAIBehavior", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::AuracronAdaptiveJungleAI_eventUpdateCreatureAIBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::AuracronAdaptiveJungleAI_eventUpdateCreatureAIBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execUpdateCreatureAIBehavior)
{
	P_GET_OBJECT(AAuracronJungleCreature,Z_Param_Creature);
	P_GET_STRUCT_REF(FAuracronCreatureAdaptation,Z_Param_Out_Adaptation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCreatureAIBehavior(Z_Param_Creature,Z_Param_Out_Adaptation);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function UpdateCreatureAIBehavior ****************

// ********** Begin Class AAuracronAdaptiveJungleAI Function UpdateEnvironmentalResponses **********
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics
{
	struct AuracronAdaptiveJungleAI_eventUpdateEnvironmentalResponses_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Environmental Response" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update environmental AI responses */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update environmental AI responses" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdateEnvironmentalResponses_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "UpdateEnvironmentalResponses", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::AuracronAdaptiveJungleAI_eventUpdateEnvironmentalResponses_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::AuracronAdaptiveJungleAI_eventUpdateEnvironmentalResponses_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execUpdateEnvironmentalResponses)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEnvironmentalResponses(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function UpdateEnvironmentalResponses ************

// ********** Begin Class AAuracronAdaptiveJungleAI Function UpdatePlayerTracking ******************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics
{
	struct AuracronAdaptiveJungleAI_eventUpdatePlayerTracking_Parms
	{
		APawn* Player;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update player behavior tracking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update player behavior tracking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdatePlayerTracking_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdaptiveJungleAI_eventUpdatePlayerTracking_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "UpdatePlayerTracking", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::AuracronAdaptiveJungleAI_eventUpdatePlayerTracking_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::AuracronAdaptiveJungleAI_eventUpdatePlayerTracking_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execUpdatePlayerTracking)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerTracking(Z_Param_Player,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function UpdatePlayerTracking ********************

// ********** Begin Class AAuracronAdaptiveJungleAI Function ValidateAIModel ***********************
struct Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics
{
	struct AuracronAdaptiveJungleAI_eventValidateAIModel_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Machine Learning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate AI model performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate AI model performance" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronAdaptiveJungleAI_eventValidateAIModel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdaptiveJungleAI_eventValidateAIModel_Parms), &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdaptiveJungleAI, nullptr, "ValidateAIModel", Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::AuracronAdaptiveJungleAI_eventValidateAIModel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::AuracronAdaptiveJungleAI_eventValidateAIModel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdaptiveJungleAI::execValidateAIModel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAIModel();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdaptiveJungleAI Function ValidateAIModel *************************

// ********** Begin Class AAuracronAdaptiveJungleAI ************************************************
void AAuracronAdaptiveJungleAI::StaticRegisterNativesAAuracronAdaptiveJungleAI()
{
	UClass* Class = AAuracronAdaptiveJungleAI::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdaptCreatureToPlayer", &AAuracronAdaptiveJungleAI::execAdaptCreatureToPlayer },
		{ "AdaptEnvironmentToPlayer", &AAuracronAdaptiveJungleAI::execAdaptEnvironmentToPlayer },
		{ "AnalyzePlayerBehavior", &AAuracronAdaptiveJungleAI::execAnalyzePlayerBehavior },
		{ "ApplyCreatureAdaptations", &AAuracronAdaptiveJungleAI::execApplyCreatureAdaptations },
		{ "GetAILearningData", &AAuracronAdaptiveJungleAI::execGetAILearningData },
		{ "GetPlayerBehaviorPattern", &AAuracronAdaptiveJungleAI::execGetPlayerBehaviorPattern },
		{ "InitializeAdaptiveAI", &AAuracronAdaptiveJungleAI::execInitializeAdaptiveAI },
		{ "RemoveCreatureAdaptations", &AAuracronAdaptiveJungleAI::execRemoveCreatureAdaptations },
		{ "ResetAILearning", &AAuracronAdaptiveJungleAI::execResetAILearning },
		{ "StartPlayerMonitoring", &AAuracronAdaptiveJungleAI::execStartPlayerMonitoring },
		{ "StopPlayerMonitoring", &AAuracronAdaptiveJungleAI::execStopPlayerMonitoring },
		{ "TrainAIModel", &AAuracronAdaptiveJungleAI::execTrainAIModel },
		{ "UpdateAILearning", &AAuracronAdaptiveJungleAI::execUpdateAILearning },
		{ "UpdateCreatureAIBehavior", &AAuracronAdaptiveJungleAI::execUpdateCreatureAIBehavior },
		{ "UpdateEnvironmentalResponses", &AAuracronAdaptiveJungleAI::execUpdateEnvironmentalResponses },
		{ "UpdatePlayerTracking", &AAuracronAdaptiveJungleAI::execUpdatePlayerTracking },
		{ "ValidateAIModel", &AAuracronAdaptiveJungleAI::execValidateAIModel },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI;
UClass* AAuracronAdaptiveJungleAI::GetPrivateStaticClass()
{
	using TClass = AAuracronAdaptiveJungleAI;
	if (!Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdaptiveJungleAI"),
			Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.InnerSingleton,
			StaticRegisterNativesAAuracronAdaptiveJungleAI,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister()
{
	return AAuracronAdaptiveJungleAI::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Adaptive Jungle AI\n * \n * Advanced AI system that learns from player behavior and adapts jungle creatures\n * and environmental responses to create dynamic, engaging gameplay experiences.\n * \n * Key Features:\n * - Real-time player behavior analysis\n * - Machine learning for pattern recognition\n * - Dynamic creature behavior adaptation\n * - Environmental response system\n * - Performance-aware scaling\n */" },
#endif
		{ "IncludePath", "AuracronAdaptiveJungleAI.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Adaptive Jungle AI\n\nAdvanced AI system that learns from player behavior and adapts jungle creatures\nand environmental responses to create dynamic, engaging gameplay experiences.\n\nKey Features:\n- Real-time player behavior analysis\n- Machine learning for pattern recognition\n- Dynamic creature behavior adaptation\n- Environmental response system\n- Performance-aware scaling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyMode_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** AI difficulty scaling mode */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI difficulty scaling mode" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLearningRate_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base AI learning rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base AI learning rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAdaptationStrength_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum adaptation strength */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum adaptation strength" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoringRadius_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player monitoring radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player monitoring radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIUpdateFrequency_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** AI update frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI update frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMachineLearning_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable machine learning */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable machine learning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEnvironmentalAdaptation_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable environmental adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable environmental adaptation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceScaling_MetaData[] = {
		{ "Category", "AI Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable performance scaling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable performance scaling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationTemplates_MetaData[] = {
		{ "Category", "Creature Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Available creature adaptation templates */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Available creature adaptation templates" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveBehaviorTrees_MetaData[] = {
		{ "Category", "Creature Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Behavior trees for different adaptations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior trees for different adaptations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptiveBlackboard_MetaData[] = {
		{ "Category", "Creature Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blackboard data for AI */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard data for AI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Root scene component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Root scene component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AILearningData_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current AI learning data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current AI learning data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitoredPlayers_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Monitored players */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitored players" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerAnalyses_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player behavior analyses */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player behavior analyses" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveAdaptations_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active creature adaptations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active creature adaptations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedCreatures_MetaData[] = {
		{ "Category", "AI State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Jungle creatures under AI control */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle creatures under AI control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdaptiveJungleAI.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_DifficultyMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DifficultyMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLearningRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAdaptationStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MonitoringRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AIUpdateFrequency;
	static void NewProp_bEnableMachineLearning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMachineLearning;
	static void NewProp_bEnableEnvironmentalAdaptation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEnvironmentalAdaptation;
	static void NewProp_bEnablePerformanceScaling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceScaling;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdaptationTemplates_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptationTemplates_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptationTemplates_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AdaptationTemplates;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AdaptiveBehaviorTrees_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptiveBehaviorTrees_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptiveBehaviorTrees_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AdaptiveBehaviorTrees;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AdaptiveBlackboard;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AILearningData;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MonitoredPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MonitoredPlayers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerAnalyses_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerAnalyses_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerAnalyses;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveAdaptations_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveAdaptations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveAdaptations;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ManagedCreatures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ManagedCreatures;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptCreatureToPlayer, "AdaptCreatureToPlayer" }, // 3054416343
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AdaptEnvironmentToPlayer, "AdaptEnvironmentToPlayer" }, // 536921692
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_AnalyzePlayerBehavior, "AnalyzePlayerBehavior" }, // 2361170476
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ApplyCreatureAdaptations, "ApplyCreatureAdaptations" }, // 3604713308
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetAILearningData, "GetAILearningData" }, // 1606802716
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_GetPlayerBehaviorPattern, "GetPlayerBehaviorPattern" }, // 1618078673
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_InitializeAdaptiveAI, "InitializeAdaptiveAI" }, // 1718926618
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnAIModelRetrained, "OnAIModelRetrained" }, // 3308257151
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnCreatureAdaptationApplied, "OnCreatureAdaptationApplied" }, // 3653475609
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_OnPlayerPatternLearned, "OnPlayerPatternLearned" }, // 1297682884
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_RemoveCreatureAdaptations, "RemoveCreatureAdaptations" }, // 1033902055
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ResetAILearning, "ResetAILearning" }, // 2508791806
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StartPlayerMonitoring, "StartPlayerMonitoring" }, // 4086687189
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_StopPlayerMonitoring, "StopPlayerMonitoring" }, // 3901645328
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_TrainAIModel, "TrainAIModel" }, // 3270651238
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateAILearning, "UpdateAILearning" }, // 379794087
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateCreatureAIBehavior, "UpdateCreatureAIBehavior" }, // 2217837331
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdateEnvironmentalResponses, "UpdateEnvironmentalResponses" }, // 3020962174
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_UpdatePlayerTracking, "UpdatePlayerTracking" }, // 3656454135
		{ &Z_Construct_UFunction_AAuracronAdaptiveJungleAI_ValidateAIModel, "ValidateAIModel" }, // 198386863
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronAdaptiveJungleAI>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_DifficultyMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_DifficultyMode = { "DifficultyMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, DifficultyMode), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleAIDifficulty, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyMode_MetaData), NewProp_DifficultyMode_MetaData) }; // 4193063920
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_BaseLearningRate = { "BaseLearningRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, BaseLearningRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLearningRate_MetaData), NewProp_BaseLearningRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MaxAdaptationStrength = { "MaxAdaptationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, MaxAdaptationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAdaptationStrength_MetaData), NewProp_MaxAdaptationStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoringRadius = { "MonitoringRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, MonitoringRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoringRadius_MetaData), NewProp_MonitoringRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AIUpdateFrequency = { "AIUpdateFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, AIUpdateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIUpdateFrequency_MetaData), NewProp_AIUpdateFrequency_MetaData) };
void Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableMachineLearning_SetBit(void* Obj)
{
	((AAuracronAdaptiveJungleAI*)Obj)->bEnableMachineLearning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableMachineLearning = { "bEnableMachineLearning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronAdaptiveJungleAI), &Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableMachineLearning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMachineLearning_MetaData), NewProp_bEnableMachineLearning_MetaData) };
void Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableEnvironmentalAdaptation_SetBit(void* Obj)
{
	((AAuracronAdaptiveJungleAI*)Obj)->bEnableEnvironmentalAdaptation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableEnvironmentalAdaptation = { "bEnableEnvironmentalAdaptation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronAdaptiveJungleAI), &Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableEnvironmentalAdaptation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEnvironmentalAdaptation_MetaData), NewProp_bEnableEnvironmentalAdaptation_MetaData) };
void Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnablePerformanceScaling_SetBit(void* Obj)
{
	((AAuracronAdaptiveJungleAI*)Obj)->bEnablePerformanceScaling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnablePerformanceScaling = { "bEnablePerformanceScaling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronAdaptiveJungleAI), &Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnablePerformanceScaling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceScaling_MetaData), NewProp_bEnablePerformanceScaling_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_ValueProp = { "AdaptationTemplates", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronCreatureAdaptation, METADATA_PARAMS(0, nullptr) }; // 3328928115
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_Key_KeyProp = { "AdaptationTemplates_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlayerBehaviorPattern, METADATA_PARAMS(0, nullptr) }; // 1949688724
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates = { "AdaptationTemplates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, AdaptationTemplates), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationTemplates_MetaData), NewProp_AdaptationTemplates_MetaData) }; // 1949688724 3328928115
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_ValueProp = { "AdaptiveBehaviorTrees", nullptr, (EPropertyFlags)0x0104000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_Key_KeyProp = { "AdaptiveBehaviorTrees_Key", nullptr, (EPropertyFlags)0x0100000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureAdaptationType, METADATA_PARAMS(0, nullptr) }; // 1378412969
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees = { "AdaptiveBehaviorTrees", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, AdaptiveBehaviorTrees), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveBehaviorTrees_MetaData), NewProp_AdaptiveBehaviorTrees_MetaData) }; // 1378412969
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBlackboard = { "AdaptiveBlackboard", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, AdaptiveBlackboard), Z_Construct_UClass_UBlackboardData_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptiveBlackboard_MetaData), NewProp_AdaptiveBlackboard_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AILearningData = { "AILearningData", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, AILearningData), Z_Construct_UScriptStruct_FAuracronJungleAILearningData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AILearningData_MetaData), NewProp_AILearningData_MetaData) }; // 72641269
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoredPlayers_Inner = { "MonitoredPlayers", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoredPlayers = { "MonitoredPlayers", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, MonitoredPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitoredPlayers_MetaData), NewProp_MonitoredPlayers_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses_ValueProp = { "PlayerAnalyses", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis, METADATA_PARAMS(0, nullptr) }; // 3144763003
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses_Key_KeyProp = { "PlayerAnalyses_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses = { "PlayerAnalyses", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, PlayerAnalyses), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerAnalyses_MetaData), NewProp_PlayerAnalyses_MetaData) }; // 3144763003
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations_ValueProp = { "ActiveAdaptations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronCreatureAdaptation, METADATA_PARAMS(0, nullptr) }; // 3328928115
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations_Key_KeyProp = { "ActiveAdaptations_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations = { "ActiveAdaptations", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, ActiveAdaptations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveAdaptations_MetaData), NewProp_ActiveAdaptations_MetaData) }; // 3328928115
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ManagedCreatures_Inner = { "ManagedCreatures", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronJungleCreature_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ManagedCreatures = { "ManagedCreatures", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, ManagedCreatures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedCreatures_MetaData), NewProp_ManagedCreatures_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdaptiveJungleAI, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_DifficultyMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_DifficultyMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_BaseLearningRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MaxAdaptationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoringRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AIUpdateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableMachineLearning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnableEnvironmentalAdaptation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_bEnablePerformanceScaling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptationTemplates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBehaviorTrees,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AdaptiveBlackboard,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_AILearningData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoredPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_MonitoredPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_PlayerAnalyses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ActiveAdaptations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ManagedCreatures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_ManagedCreatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::NewProp_CachedRealmSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::ClassParams = {
	&AAuracronAdaptiveJungleAI::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI()
{
	if (!Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.OuterSingleton, Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronAdaptiveJungleAI);
AAuracronAdaptiveJungleAI::~AAuracronAdaptiveJungleAI() {}
// ********** End Class AAuracronAdaptiveJungleAI **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPlayerBehaviorPattern_StaticEnum, TEXT("EPlayerBehaviorPattern"), &Z_Registration_Info_UEnum_EPlayerBehaviorPattern, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1949688724U) },
		{ ECreatureAdaptationType_StaticEnum, TEXT("ECreatureAdaptationType"), &Z_Registration_Info_UEnum_ECreatureAdaptationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1378412969U) },
		{ EJungleAIDifficulty_StaticEnum, TEXT("EJungleAIDifficulty"), &Z_Registration_Info_UEnum_EJungleAIDifficulty, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4193063920U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronPlayerBehaviorAnalysis::StaticStruct, Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics::NewStructOps, TEXT("AuracronPlayerBehaviorAnalysis"), &Z_Registration_Info_UScriptStruct_FAuracronPlayerBehaviorAnalysis, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronPlayerBehaviorAnalysis), 3144763003U) },
		{ FAuracronJungleCreatureAdaptation::StaticStruct, Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics::NewStructOps, TEXT("AuracronJungleCreatureAdaptation"), &Z_Registration_Info_UScriptStruct_FAuracronJungleCreatureAdaptation, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronJungleCreatureAdaptation), 762622823U) },
		{ FAuracronJungleAILearningData::StaticStruct, Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics::NewStructOps, TEXT("AuracronJungleAILearningData"), &Z_Registration_Info_UScriptStruct_FAuracronJungleAILearningData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronJungleAILearningData), 72641269U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronAdaptiveJungleAI, AAuracronAdaptiveJungleAI::StaticClass, TEXT("AAuracronAdaptiveJungleAI"), &Z_Registration_Info_UClass_AAuracronAdaptiveJungleAI, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronAdaptiveJungleAI), 3728645207U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_2493141646(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
