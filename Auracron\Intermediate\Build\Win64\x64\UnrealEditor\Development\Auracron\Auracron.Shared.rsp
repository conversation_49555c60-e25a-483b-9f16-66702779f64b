/I "."
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronFoliageBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source"
/I "C:/Aura/projeto/Auracron/Source/AuracronFoliageBridge/Public"
/I "Runtime/Core/Public"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/CorePreciseFP/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Engine/UHT"
/I "Runtime/Engine/Classes"
/I "Runtime/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreOnline/UHT"
/I "Runtime/CoreOnline/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldNotification/UHT"
/I "Runtime/FieldNotification/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NetCore/UHT"
/I "Runtime/Net/Core/Classes"
/I "Runtime/Net/Core/Public"
/I "Runtime/Net/Common/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/JsonUtilities/UHT"
/I "Runtime/JsonUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/RHI/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/ImageWrapper/Public"
/I "Runtime/Messaging/Public"
/I "Runtime/MessagingCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/RenderCore/UHT"
/I "Runtime/RenderCore/Public"
/I "Runtime/OpenGLDrv/Public"
/I "Runtime/Analytics/AnalyticsET/Public"
/I "Runtime/Analytics/Analytics/Public"
/I "Runtime/Sockets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetRegistry/UHT"
/I "Runtime/AssetRegistry/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineMessages/UHT"
/I "Runtime/EngineMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineSettings/UHT"
/I "Runtime/EngineSettings/Classes"
/I "Runtime/EngineSettings/Public"
/I "Runtime/SynthBenchmark/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTags/UHT"
/I "Runtime/GameplayTags/Classes"
/I "Runtime/GameplayTags/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PacketHandler/UHT"
/I "Runtime/PacketHandlers/PacketHandler/Classes"
/I "Runtime/PacketHandlers/PacketHandler/Public"
/I "Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioPlatformConfiguration/UHT"
/I "Runtime/AudioPlatformConfiguration/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MeshDescription/UHT"
/I "Runtime/MeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StaticMeshDescription/UHT"
/I "Runtime/StaticMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletalMeshDescription/UHT"
/I "Runtime/SkeletalMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationCore/UHT"
/I "Runtime/AnimationCore/Public"
/I "Runtime/PakFile/Public"
/I "Runtime/RSA/Public"
/I "Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsCore/UHT"
/I "Runtime/PhysicsCore/Public"
/I "Runtime/Experimental/ChaosCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Chaos/UHT"
/I "Runtime/Experimental/Chaos/Public"
/I "Runtime/Experimental/Voronoi/Public"
/I "Runtime/GeometryCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVDRuntime/UHT"
/I "Runtime/Experimental/ChaosVisualDebugger/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NNE/UHT"
/I "Runtime/NNE/Public"
/I "Runtime/SignalProcessing/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StateStream/UHT"
/I "Runtime/StateStream/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioExtensions/UHT"
/I "Runtime/AudioExtensions/Public"
/I "Runtime/AudioMixerCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioMixer/UHT"
/I "Runtime/AudioMixer/Classes"
/I "Runtime/AudioMixer/Public"
/I "Developer/TargetPlatform/Public"
/I "Developer/TextureFormat/Public"
/I "Developer/DesktopPlatform/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkEngine/UHT"
/I "Runtime/AudioLink/AudioLinkEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkCore/UHT"
/I "Runtime/AudioLink/AudioLinkCore/Public"
/I "Runtime/Networking/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Public"
/I "Developer/TextureBuildUtilities/Public"
/I "Developer/Horde/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothSysRuntimeIntrfc/UHT"
/I "Runtime/ClothingSystemRuntimeInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/IrisCore/UHT"
/I "Runtime/Experimental/Iris/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneCapture/UHT"
/I "Runtime/MovieSceneCapture/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Renderer/UHT"
/I "Runtime/Renderer/Public"
/I "../Shaders/Public"
/I "../Shaders/Shared"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementFramework/UHT"
/I "Runtime/TypedElementFramework/Tests"
/I "Runtime/TypedElementFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementRuntime/UHT"
/I "Runtime/TypedElementRuntime/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationDataController/UHT"
/I "Developer/AnimationDataController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationBlueprintEditor/UHT"
/I "Editor/AnimationBlueprintEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Kismet/UHT"
/I "Editor/Kismet/Classes"
/I "Editor/Kismet/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Persona/UHT"
/I "Editor/Persona/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletonEditor/UHT"
/I "Editor/SkeletonEditor/Public"
/I "Developer/AnimationWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolWidgets/UHT"
/I "Developer/ToolWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenus/UHT"
/I "Developer/ToolMenus/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditor/UHT"
/I "Editor/AnimationEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AdvancedPreviewScene/UHT"
/I "Editor/AdvancedPreviewScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyEditor/UHT"
/I "Editor/PropertyEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorConfig/UHT"
/I "Editor/EditorConfig/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorFramework/UHT"
/I "Editor/EditorFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorSubsystem/UHT"
/I "Editor/EditorSubsystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InteractiveToolsFramework/UHT"
/I "Runtime/InteractiveToolsFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEd/UHT"
/I "Programs/UnrealLightmass/Public"
/I "Editor/UnrealEd/Classes"
/I "Editor/UnrealEd/Public"
/I "Editor/AssetTagsEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CollectionManager/UHT"
/I "Developer/CollectionManager/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowser/UHT"
/I "Editor/ContentBrowser/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetTools/UHT"
/I "Developer/AssetTools/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetDefinition/UHT"
/I "Editor/AssetDefinition/Public"
/I "Developer/Merge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowserData/UHT"
/I "Editor/ContentBrowserData/Public"
/I "Runtime/Projects/Public"
/I "Developer/MeshUtilities/Public"
/I "Developer/MeshMergeUtilities/Public"
/I "Developer/MeshReductionInterface/Public"
/I "Runtime/RawMesh/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialUtilities/UHT"
/I "Developer/MaterialUtilities/Public"
/I "Editor/KismetCompiler/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTasks/UHT"
/I "Runtime/GameplayTasks/Classes"
/I "Runtime/GameplayTasks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClassViewer/UHT"
/I "Editor/ClassViewer/Public"
/I "Developer/DirectoryWatcher/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Documentation/UHT"
/I "Editor/Documentation/Public"
/I "Editor/MainFrame/Public"
/I "Runtime/SandboxFile/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SourceControl/UHT"
/I "Developer/SourceControl/Public"
/I "Developer/UncontrolledChangelists/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEdMessages/UHT"
/I "Editor/UnrealEdMessages/Classes"
/I "Editor/UnrealEdMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/BlueprintGraph/UHT"
/I "Editor/BlueprintGraph/Classes"
/I "Editor/BlueprintGraph/Public"
/I "Runtime/Online/HTTP/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FunctionalTesting/UHT"
/I "Developer/FunctionalTesting/Classes"
/I "Developer/FunctionalTesting/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationController/UHT"
/I "Developer/AutomationController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationTest/UHT"
/I "Runtime/AutomationTest/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Localization/UHT"
/I "Developer/Localization/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioEditor/UHT"
/I "Editor/AudioEditor/Classes"
/I "Editor/AudioEditor/Public"
/I "ThirdParty/libSampleRate/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LevelEditor/UHT"
/I "Editor/LevelEditor/Public"
/I "Editor/CommonMenuExtensions/Public"
/I "Developer/Settings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VREditor/UHT"
/I "Editor/VREditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ViewportInteraction/UHT"
/I "Editor/ViewportInteraction/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/HeadMountedDisplay/UHT"
/I "Runtime/HeadMountedDisplay/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Landscape/UHT"
/I "Runtime/Landscape/Classes"
/I "Runtime/Landscape/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DetailCustomizations/UHT"
/I "Editor/DetailCustomizations/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GraphEditor/UHT"
/I "Editor/GraphEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StructViewer/UHT"
/I "Editor/StructViewer/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialEditor/UHT"
/I "Editor/MaterialEditor/Public"
/I "Runtime/NetworkFileSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UMG/UHT"
/I "Runtime/UMG/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieScene/UHT"
/I "Runtime/MovieScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TimeManagement/UHT"
/I "Runtime/TimeManagement/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UniversalObjectLocator/UHT"
/I "Runtime/UniversalObjectLocator/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneTracks/UHT"
/I "Runtime/MovieSceneTracks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Constraints/UHT"
/I "Runtime/Experimental/Animation/Constraints/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyPath/UHT"
/I "Runtime/PropertyPath/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NavigationSystem/UHT"
/I "Runtime/NavigationSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GeometryCollectionEngine/UHT"
/I "Runtime/Experimental/GeometryCollectionEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosSolverEngine/UHT"
/I "Runtime/Experimental/ChaosSolverEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowCore/UHT"
/I "Runtime/Experimental/Dataflow/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowEngine/UHT"
/I "Runtime/Experimental/Dataflow/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowSimulation/UHT"
/I "Runtime/Experimental/Dataflow/Simulation/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldSystemEngine/UHT"
/I "Runtime/Experimental/FieldSystem/Source/FieldSystemEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ISMPool/UHT"
/I "Runtime/Experimental/ISMPool/Public"
/I "Developer/MeshBuilder/Public"
/I "Runtime/MeshUtilitiesCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MSQS/UHT"
/I "Runtime/MaterialShaderQualitySettings/Classes"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenusEditor/UHT"
/I "Editor/ToolMenusEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StatusBar/UHT"
/I "Editor/StatusBar/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeCore/UHT"
/I "Runtime/Interchange/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeEngine/UHT"
/I "Runtime/Interchange/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperToolSettings/UHT"
/I "Developer/DeveloperToolSettings/Classes"
/I "Developer/DeveloperToolSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectDataInterface/UHT"
/I "Editor/SubobjectDataInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectEditor/UHT"
/I "Editor/SubobjectEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsUtilities/UHT"
/I "Developer/PhysicsUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/WidgetRegistration/UHT"
/I "Developer/WidgetRegistration/Public"
/I "Editor/ActorPickerMode/Public"
/I "Editor/SceneDepthPickerMode/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditMode/UHT"
/I "Editor/AnimationEditMode/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Foliage/UHT"
/I "Runtime/Foliage/Public"
/I "../Plugins/Runtime/GeometryProcessing/Source"
/I "../Plugins/Runtime/GeometryProcessing/Source/DynamicMesh/Public"
/I "../Plugins/Runtime/GeometryProcessing/Source/GeometryAlgorithms/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GeometryFramework/UHT"
/I "Runtime/GeometryFramework/Public"
/I "Runtime/MeshConversion/Public"
/I "../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/UnrealEditor/Inc/ModelingComponents/UHT"
/I "../Plugins/Runtime/MeshModelingToolset/Source"
/I "../Plugins/Runtime/MeshModelingToolset/Source/ModelingComponents/Public"
/I "../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/UnrealEditor/Inc/ModelingOperators/UHT"
/I "../Plugins/Runtime/MeshModelingToolset/Source/ModelingOperators/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TextureUtilitiesCommon/UHT"
/I "Runtime/TextureUtilitiesCommon/Public"
/I "../Plugins/PCG/Intermediate/Build/Win64/UnrealEditor/Inc/PCG/UHT"
/I "../Plugins/PCG/Source"
/I "../Plugins/PCG/Source/PCG/Public"
/I "../Plugins/Runtime/ComputeFramework/Intermediate/Build/Win64/UnrealEditor/Inc/ComputeFramework/UHT"
/I "../Plugins/Runtime/ComputeFramework/Source"
/I "../Plugins/Runtime/ComputeFramework/Source/ComputeFramework/Public"
/I "../Plugins/PCGInterops/PCGGeometryScriptInterop/Intermediate/Build/Win64/UnrealEditor/Inc/PCGGeometryScriptInterop/UHT"
/I "../Plugins/PCGInterops/PCGGeometryScriptInterop/Source"
/I "../Plugins/PCGInterops/PCGGeometryScriptInterop/Source/PCGGeometryScriptInterop/Public"
/I "../Plugins/Runtime/GeometryScripting/Intermediate/Build/Win64/UnrealEditor/Inc/GeometryScriptingCore/UHT"
/I "../Plugins/Runtime/GeometryScripting/Source"
/I "../Plugins/Runtime/GeometryScripting/Source/GeometryScriptingCore/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronPCGBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronPCGBridge/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/NiagaraCore/UHT"
/I "../Plugins/FX/Niagara/Source"
/I "../Plugins/FX/Niagara/Source/NiagaraCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VectorVM/UHT"
/I "Runtime/VectorVM/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/NiagaraShader/UHT"
/I "../Plugins/FX/Niagara/Shaders/Shared"
/I "../Plugins/FX/Niagara/Source/NiagaraShader/Public"
/I "../Plugins/FX/Niagara/Source/NiagaraVertexFactories/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/Niagara/UHT"
/I "../Plugins/FX/Niagara/Source/Niagara/Classes"
/I "../Plugins/FX/Niagara/Source/Niagara/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronLumenBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronLumenBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronWorldPartitionBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronWorldPartitionBridge/Public"
/I "Runtime/Engine/Public/WorldPartition"
/I "Editor/WorldPartitionEditor/Public"
/I "Editor/WorldPartitionEditor/Private"
/I "Runtime/Engine/Private/WorldPartition"
/I "Runtime/Navmesh/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronAbismoUmbrioBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronAbismoUmbrioBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronAdaptiveCreaturesBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronAdaptiveCreaturesBridge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MassEntity/UHT"
/I "Runtime/MassEntity/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MassEntityTestSuite/UHT"
/I "Developer/MassEntityTestSuite/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AITestSuite/UHT"
/I "Developer/AITestSuite/Classes"
/I "Developer/AITestSuite/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AIModule/UHT"
/I "Runtime/AIModule/Classes"
/I "Runtime/AIModule/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassMovement/UHT"
/I "../Plugins/Runtime/MassGameplay/Source"
/I "../Plugins/Runtime/MassGameplay/Source/MassMovement/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassCommon/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassCommon/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassLOD/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassLOD/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassSimulation/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassSimulation/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassSpawner/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassSpawner/Public"
/I "../Plugins/Runtime/ZoneGraph/Intermediate/Build/Win64/UnrealEditor/Inc/ZoneGraph/UHT"
/I "../Plugins/Runtime/ZoneGraph/Source"
/I "../Plugins/Runtime/ZoneGraph/Source/ZoneGraph/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MassEntityEditor/UHT"
/I "Editor/MassEntityEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ComponentVisualizers/UHT"
/I "Editor/ComponentVisualizers/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassSignals/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassSignals/Public"
/I "../Plugins/Runtime/ZoneGraphAnnotations/Intermediate/Build/Win64/UnrealEditor/Inc/ZoneGraphAnnotations/UHT"
/I "../Plugins/Runtime/ZoneGraphAnnotations/Source"
/I "../Plugins/Runtime/ZoneGraphAnnotations/Source/ZoneGraphAnnotations/Public"
/I "../Plugins/Runtime/ZoneGraph/Intermediate/Build/Win64/UnrealEditor/Inc/ZoneGraphDebug/UHT"
/I "../Plugins/Runtime/ZoneGraph/Source/ZoneGraphDebug/Public"
/I "../Plugins/AI/MassAI/Intermediate/Build/Win64/UnrealEditor/Inc/MassNavigation/UHT"
/I "../Plugins/AI/MassAI/Source"
/I "../Plugins/AI/MassAI/Source/MassNavigation/Public"
/I "../Plugins/Runtime/MassGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/MassReplication/UHT"
/I "../Plugins/Runtime/MassGameplay/Source/MassReplication/Public"
/I "../Plugins/Runtime/StateTree/Intermediate/Build/Win64/UnrealEditor/Inc/StateTreeModule/UHT"
/I "../Plugins/Runtime/StateTree/Source"
/I "../Plugins/Runtime/StateTree/Source/StateTreeModule/Public"
/I "../Plugins/Runtime/PropertyBindingUtils/Intermediate/Build/Win64/UnrealEditor/Inc/PropertyBindingUtils/UHT"
/I "../Plugins/Runtime/PropertyBindingUtils/Source"
/I "../Plugins/Runtime/PropertyBindingUtils/Source/PropertyBindingUtils/Public"
/I "Developer/TraceServices/Public"
/I "Runtime/Cbor/Public"
/I "Developer/TraceAnalysis/Public"
/I "../Plugins/Runtime/GameplayStateTree/Intermediate/Build/Win64/UnrealEditor/Inc/GameplayStateTreeModule/UHT"
/I "../Plugins/Runtime/GameplayStateTree/Source"
/I "../Plugins/Runtime/GameplayStateTree/Source/GameplayStateTreeModule/Public"
/I "../Plugins/Experimental/StructUtils/Source"
/I "../Plugins/Experimental/StructUtils/Source/StructUtils/Public"
/I "../Plugins/Runtime/SmartObjects/Intermediate/Build/Win64/UnrealEditor/Inc/SmartObjectsModule/UHT"
/I "../Plugins/Runtime/SmartObjects/Source"
/I "../Plugins/Runtime/SmartObjects/Source/SmartObjectsModule/Public"
/I "../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/UnrealEditor/Inc/GameplayAbilities/UHT"
/I "../Plugins/Runtime/GameplayAbilities/Source"
/I "../Plugins/Runtime/GameplayAbilities/Source/GameplayAbilities/Public"
/I "../Plugins/Runtime/DataRegistry/Intermediate/Build/Win64/UnrealEditor/Inc/DataRegistry/UHT"
/I "../Plugins/Runtime/DataRegistry/Source"
/I "../Plugins/Runtime/DataRegistry/Source/DataRegistry/Public"
/I "../Plugins/Runtime/WorldConditions/Intermediate/Build/Win64/UnrealEditor/Inc/WorldConditions/UHT"
/I "../Plugins/Runtime/WorldConditions/Source"
/I "../Plugins/Runtime/WorldConditions/Source/WorldConditions/Public"
/I "../Plugins/Experimental/GameplayTargetingSystem/Intermediate/Build/Win64/UnrealEditor/Inc/TargetingSystem/UHT"
/I "../Plugins/Experimental/GameplayTargetingSystem/Source"
/I "../Plugins/Experimental/GameplayTargetingSystem/Source/GameplayTargetingSystem/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronVerticalTransitionsBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronVerticalTransitionsBridge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CinematicCamera/UHT"
/I "Runtime/CinematicCamera/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronQABridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronQABridge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationMessages/UHT"
/I "Runtime/AutomationMessages/Public"
/I "Runtime/AutomationWorker/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ScreenShotComparisonTools/UHT"
/I "Developer/ScreenShotComparisonTools/Public"
/I "../Plugins/EnhancedInput/Intermediate/Build/Win64/UnrealEditor/Inc/EnhancedInput/UHT"
/I "../Plugins/EnhancedInput/Source"
/I "../Plugins/EnhancedInput/Source/EnhancedInput/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronMetaHumanBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronMetaHumanBridge/Public"
/I "Editor/EditorStyle/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorWidgets/UHT"
/I "Editor/EditorWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SceneOutliner/UHT"
/I "Editor/SceneOutliner/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/WidgetCarousel/UHT"
/I "Runtime/WidgetCarousel/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LogVisualizer/UHT"
/I "Developer/LogVisualizer/Public"
/I "Editor/WorkspaceMenuStructure/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/OutputLog/UHT"
/I "Developer/OutputLog/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationBlueprintLibrary/UHT"
/I "Editor/AnimationBlueprintLibrary/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimGraph/UHT"
/I "Editor/AnimGraph/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimGraphRuntime/UHT"
/I "Runtime/AnimGraphRuntime/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/KismetWidgets/UHT"
/I "Editor/KismetWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletalMeshEditor/UHT"
/I "Editor/SkeletalMeshEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SequencerCore/UHT"
/I "Editor/SequencerCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CurveEditor/UHT"
/I "Editor/CurveEditor/Public"
/I "Editor/SequencerWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Sequencer/UHT"
/I "Editor/Sequencer/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneTools/UHT"
/I "Editor/MovieSceneTools/Public"
/I "../Plugins/MovieScene/LevelSequenceEditor/Intermediate/Build/Win64/UnrealEditor/Inc/LevelSequenceEditor/UHT"
/I "../Plugins/MovieScene/LevelSequenceEditor/Source"
/I "../Plugins/MovieScene/LevelSequenceEditor/Source/LevelSequenceEditor/Public"
/I "../Plugins/MovieScene/SequencerScripting/Intermediate/Build/Win64/UnrealEditor/Inc/SequencerScripting/UHT"
/I "../Plugins/MovieScene/SequencerScripting/Source"
/I "../Plugins/MovieScene/SequencerScripting/Source/SequencerScripting/Public"
/I "../Plugins/MovieScene/SequencerScripting/Intermediate/Build/Win64/UnrealEditor/Inc/SequencerScriptingEditor/UHT"
/I "../Plugins/MovieScene/SequencerScripting/Source/SequencerScriptingEditor/Public"
/I "Editor/MovieSceneCaptureDialog/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothingSystemEditor/UHT"
/I "Editor/ClothingSystemEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothingSystemRuntimeCommon/UHT"
/I "Runtime/ClothingSystemRuntimeCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothingSystemRuntimeNv/UHT"
/I "Runtime/ClothingSystemRuntimeNv/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothingSystemEditorInterface/UHT"
/I "Editor/ClothingSystemEditorInterface/Public"
/I "../Plugins/ChaosCloth/Source"
/I "../Plugins/ChaosCloth/Source/ChaosClothEditor/Public"
/I "../Plugins/Runtime/HairStrands/Intermediate/Build/Win64/UnrealEditor/Inc/HairStrandsEditor/UHT"
/I "../Plugins/Runtime/HairStrands/Source"
/I "../Plugins/Runtime/HairStrands/Source/HairStrandsEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TextureEditor/UHT"
/I "Editor/TextureEditor/Classes"
/I "Editor/TextureEditor/Public"
/I "../Plugins/Animation/LiveLink/Intermediate/Build/Win64/UnrealEditor/Inc/LiveLinkEditor/UHT"
/I "../Plugins/Animation/LiveLink/Source"
/I "../Plugins/Animation/LiveLink/Source/LiveLinkEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LiveLinkAnimationCore/UHT"
/I "Runtime/LiveLinkAnimationCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LiveLinkInterface/UHT"
/I "Runtime/LiveLinkInterface/Public"
/I "../Plugins/Animation/LiveLink/Intermediate/Build/Win64/UnrealEditor/Inc/LiveLink/UHT"
/I "../Plugins/Animation/LiveLink/Source/LiveLink/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LiveLinkMessageBusFramework/UHT"
/I "Runtime/LiveLinkMessageBusFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Serialization/UHT"
/I "Runtime/Serialization/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronAnalyticsBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronAnalyticsBridge/Public"
/I "../Plugins/Runtime/Analytics/AnalyticsMulticast/Source"
/I "../Plugins/Runtime/Analytics/AnalyticsMulticast/Source/AnalyticsMulticast/Public"
/I "../Plugins/Runtime/ModularGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/ModularGameplay/UHT"
/I "../Plugins/Runtime/ModularGameplay/Source"
/I "../Plugins/Runtime/ModularGameplay/Source/ModularGameplay/Public"
/I "../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/UnrealEditor/Inc/ReplicationGraph/UHT"
/I "../Plugins/Runtime/ReplicationGraph/Source"
/I "../Plugins/Runtime/ReplicationGraph/Source/Public"
/I "../Plugins/Online/OnlineSubsystemUtils/Intermediate/Build/Win64/UnrealEditor/Inc/OnlineSubsystemUtils/UHT"
/I "../Plugins/Online/OnlineSubsystemUtils/Source"
/I "../Plugins/Online/OnlineSubsystemUtils/Source/OnlineSubsystemUtils/Classes"
/I "../Plugins/Online/OnlineSubsystemUtils/Source/OnlineSubsystemUtils/Public"
/I "../Plugins/Online/OnlineSubsystem/Intermediate/Build/Win64/UnrealEditor/Inc/OnlineSubsystem/UHT"
/I "../Plugins/Online/OnlineSubsystem/Source/Test"
/I "../Plugins/Online/OnlineSubsystem/Source"
/I "../Plugins/Online/OnlineSubsystem/Source/Public"
/I "../Plugins/Online/OnlineBase/Source"
/I "../Plugins/Online/OnlineBase/Source/Public"
/I "../Plugins/Online/EOSShared/Source"
/I "../Plugins/Online/EOSShared/Source/EOSShared/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronAudioBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronAudioBridge/Public"
/I "../Plugins/Runtime/Synthesis/Intermediate/Build/Win64/UnrealEditor/Inc/Synthesis/UHT"
/I "../Plugins/Runtime/Synthesis/Source"
/I "../Plugins/Runtime/Synthesis/Source/Synthesis/Classes"
/I "../Plugins/Runtime/Synthesis/Source/Synthesis/Public"
/I "Runtime/SoundFieldRendering/Public"
/I "../Plugins/Runtime/Metasound/Intermediate/Build/Win64/UnrealEditor/Inc/MetasoundFrontend/UHT"
/I "../Plugins/Runtime/Metasound/Source"
/I "../Plugins/Runtime/Metasound/Source/MetasoundFrontend/Public"
/I "../Plugins/Runtime/Metasound/Source/MetasoundGraphCore/Public"
/I "../Plugins/Runtime/Metasound/Source/MetasoundStandardNodes/Public"
/I "../Plugins/Runtime/Metasound/Source/MetasoundGenerator/Public"
/I "../Plugins/Runtime/WaveTable/Intermediate/Build/Win64/UnrealEditor/Inc/WaveTable/UHT"
/I "../Plugins/Runtime/WaveTable/Source"
/I "../Plugins/Runtime/WaveTable/Source/WaveTable/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronVFXBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronVFXBridge/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/NiagaraAnimNotifies/UHT"
/I "../Plugins/FX/Niagara/Source/NiagaraAnimNotifies/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronPhysicsBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronPhysicsBridge/Public"
/I "../Plugins/Experimental/ChaosVehiclesPlugin/Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVehicles/UHT"
/I "../Plugins/Experimental/ChaosVehiclesPlugin/Source"
/I "../Plugins/Experimental/ChaosVehiclesPlugin/Source/ChaosVehicles/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVehiclesCore/UHT"
/I "Runtime/Experimental/ChaosVehicles/ChaosVehiclesCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVehiclesEngine/UHT"
/I "Runtime/Experimental/ChaosVehicles/ChaosVehiclesEngine/Public"
/I "../Plugins/ChaosCloth/Intermediate/Build/Win64/UnrealEditor/Inc/ChaosCloth/UHT"
/I "../Plugins/ChaosCloth/Source/ChaosCloth/Public"
/I "../Plugins/Experimental/ChaosCaching/Intermediate/Build/Win64/UnrealEditor/Inc/ChaosCaching/UHT"
/I "../Plugins/Experimental/ChaosCaching/Source"
/I "../Plugins/Experimental/ChaosCaching/Source/ChaosCaching/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LevelSequence/UHT"
/I "Runtime/LevelSequence/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronNaniteBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronNaniteBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronVoiceBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronVoiceBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronAntiCheatBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronAntiCheatBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronTutorialBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronTutorialBridge/Public"
/I "../Plugins/Runtime/CommonUI/Intermediate/Build/Win64/UnrealEditor/Inc/CommonInput/UHT"
/I "../Plugins/Runtime/CommonUI/Source"
/I "../Plugins/Runtime/CommonUI/Source/CommonInput/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronLoreBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronLoreBridge/Public"
/I "../Plugins/Runtime/CommonUI/Intermediate/Build/Win64/UnrealEditor/Inc/CommonUI/UHT"
/I "../Plugins/Runtime/CommonUI/Source/CommonUI/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MediaAssets/UHT"
/I "Runtime/MediaAssets/Public"
/I "Runtime/Media/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MediaUtils/UHT"
/I "Runtime/MediaUtils/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronMonetizationBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronMonetizationBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronEOSBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronEOSBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronCombatBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronCombatBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronChampionsBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronChampionsBridge/Public"
/I "../Plugins/Animation/ControlRig/Intermediate/Build/Win64/UnrealEditor/Inc/ControlRig/UHT"
/I "../Plugins/Animation/ControlRig/Source"
/I "../Plugins/Animation/ControlRig/Source/ControlRig/Public"
/I "../Plugins/Runtime/RigVM/Intermediate/Build/Win64/UnrealEditor/Inc/RigVM/UHT"
/I "../Plugins/Runtime/RigVM/Source"
/I "../Plugins/Runtime/RigVM/Source/RigVM/Public"
/I "../Plugins/Runtime/RigVM/Intermediate/Build/Win64/UnrealEditor/Inc/RigVMDeveloper/UHT"
/I "../Plugins/Runtime/RigVM/Source/RigVMDeveloper/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VisualGraphUtils/UHT"
/I "Developer/VisualGraphUtils/Public"
/I "../Plugins/Animation/IKRig/Intermediate/Build/Win64/UnrealEditor/Inc/IKRig/UHT"
/I "../Plugins/Animation/IKRig/Source"
/I "../Plugins/Animation/IKRig/Source/IKRig/Public"
/I "../Plugins/Experimental/FullBodyIK/Intermediate/Build/Win64/UnrealEditor/Inc/PBIK/UHT"
/I "../Plugins/Experimental/FullBodyIK/Source"
/I "../Plugins/Experimental/FullBodyIK/Source/PBIK/Public"
/I "../Plugins/Animation/ControlRig/Intermediate/Build/Win64/UnrealEditor/Inc/ControlRigDeveloper/UHT"
/I "../Plugins/Animation/ControlRig/Source/ControlRigDeveloper/Public"
/I "Editor/AnimationEditorWidgets/Public"
/I "../Plugins/Animation/ControlRig/Intermediate/Build/Win64/UnrealEditor/Inc/ControlRigEditor/UHT"
/I "../Plugins/Animation/ControlRig/Source/ControlRigEditor/Public"
/I "Runtime/AppFramework/Public"
/I "../Plugins/Runtime/RigVM/Intermediate/Build/Win64/UnrealEditor/Inc/RigVMEditor/UHT"
/I "../Plugins/Runtime/RigVM/Source/RigVMEditor/Public"
/I "Developer/MessageLog/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StructUtilsEditor/UHT"
/I "Editor/StructUtilsEditor/Public"
/I "../Plugins/Animation/TweeningUtils/Intermediate/Build/Win64/UnrealEditor/Inc/TweeningUtilsEditor/UHT"
/I "../Plugins/Animation/TweeningUtils/Source"
/I "../Plugins/Animation/TweeningUtils/Source/TweeningUtilsEditor/Public"
/I "../Plugins/Animation/TweeningUtils/Source/TweeningUtils/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronNetworkingBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronNetworkingBridge/Public"
/I "../Plugins/Online/OnlineSubsystemEOS/Intermediate/Build/Win64/UnrealEditor/Inc/OnlineSubsystemEOS/UHT"
/I "../Plugins/Online/OnlineSubsystemEOS/Source"
/I "../Plugins/Online/OnlineSubsystemEOS/Source/OnlineSubsystemEOS/Public"
/I "../Plugins/Runtime/NetworkPrediction/Intermediate/Build/Win64/UnrealEditor/Inc/NetworkPrediction/UHT"
/I "../Plugins/Runtime/NetworkPrediction/Source"
/I "../Plugins/Runtime/NetworkPrediction/Source/NetworkPrediction/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronProgressionBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronProgressionBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronRealmsBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronRealmsBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronSigilosBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronSigilosBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronUIBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronUIBridge/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronMetaHumanFramework/UHT"
/I "C:/Aura/projeto/Auracron/Source/AuracronMetaHumanFramework/Public"
/I "../Plugins/Animation/RigLogic/Intermediate/Build/Win64/UnrealEditor/Inc/RigLogicModule/UHT"
/I "../Plugins/Animation/RigLogic/Source"
/I "../Plugins/Animation/RigLogic/Source/RigLogicModule/Public"
/I "../Plugins/Animation/RigLogic/Source/RigLogicLib/Public"
/I "Developer/SkeletalMeshUtilitiesCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorInteractiveToolsFramework/UHT"
/I "Editor/Experimental/EditorInteractiveToolsFramework/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/AuracronEditor/Development/UnrealEd"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "ThirdParty/OpenGL"
/external:I "Runtime/SymsLib"
/external:I "Runtime/SymsLib/syms"
/external:I "ThirdParty/EOSSDK/SDK/Include"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"