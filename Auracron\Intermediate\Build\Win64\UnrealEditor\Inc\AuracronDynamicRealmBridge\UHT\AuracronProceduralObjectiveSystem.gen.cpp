// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronProceduralObjectiveSystem.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronProceduralObjectiveSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralObjective();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EProceduralObjectiveType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EProceduralObjectiveType;
static UEnum* EProceduralObjectiveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EProceduralObjectiveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EProceduralObjectiveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EProceduralObjectiveType"));
	}
	return Z_Registration_Info_UEnum_EProceduralObjectiveType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EProceduralObjectiveType>()
{
	return EProceduralObjectiveType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Activate.DisplayName", "Activate" },
		{ "Activate.Name", "EProceduralObjectiveType::Activate" },
		{ "Adapt.DisplayName", "Adapt" },
		{ "Adapt.Name", "EProceduralObjectiveType::Adapt" },
		{ "BlueprintType", "true" },
		{ "Capture.DisplayName", "Capture" },
		{ "Capture.Name", "EProceduralObjectiveType::Capture" },
		{ "Collect.DisplayName", "Collect" },
		{ "Collect.Name", "EProceduralObjectiveType::Collect" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural objective types\n */" },
#endif
		{ "Control.DisplayName", "Control" },
		{ "Control.Name", "EProceduralObjectiveType::Control" },
		{ "Coordinate.DisplayName", "Coordinate" },
		{ "Coordinate.Name", "EProceduralObjectiveType::Coordinate" },
		{ "Defend.DisplayName", "Defend" },
		{ "Defend.Name", "EProceduralObjectiveType::Defend" },
		{ "Destroy.DisplayName", "Destroy" },
		{ "Destroy.Name", "EProceduralObjectiveType::Destroy" },
		{ "Eliminate.DisplayName", "Eliminate" },
		{ "Eliminate.Name", "EProceduralObjectiveType::Eliminate" },
		{ "Escort.DisplayName", "Escort" },
		{ "Escort.Name", "EProceduralObjectiveType::Escort" },
		{ "Explore.DisplayName", "Explore" },
		{ "Explore.Name", "EProceduralObjectiveType::Explore" },
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
		{ "Survive.DisplayName", "Survive" },
		{ "Survive.Name", "EProceduralObjectiveType::Survive" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural objective types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EProceduralObjectiveType::Capture", (int64)EProceduralObjectiveType::Capture },
		{ "EProceduralObjectiveType::Defend", (int64)EProceduralObjectiveType::Defend },
		{ "EProceduralObjectiveType::Eliminate", (int64)EProceduralObjectiveType::Eliminate },
		{ "EProceduralObjectiveType::Collect", (int64)EProceduralObjectiveType::Collect },
		{ "EProceduralObjectiveType::Escort", (int64)EProceduralObjectiveType::Escort },
		{ "EProceduralObjectiveType::Survive", (int64)EProceduralObjectiveType::Survive },
		{ "EProceduralObjectiveType::Activate", (int64)EProceduralObjectiveType::Activate },
		{ "EProceduralObjectiveType::Destroy", (int64)EProceduralObjectiveType::Destroy },
		{ "EProceduralObjectiveType::Control", (int64)EProceduralObjectiveType::Control },
		{ "EProceduralObjectiveType::Explore", (int64)EProceduralObjectiveType::Explore },
		{ "EProceduralObjectiveType::Coordinate", (int64)EProceduralObjectiveType::Coordinate },
		{ "EProceduralObjectiveType::Adapt", (int64)EProceduralObjectiveType::Adapt },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EProceduralObjectiveType",
	"EProceduralObjectiveType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType()
{
	if (!Z_Registration_Info_UEnum_EProceduralObjectiveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EProceduralObjectiveType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EProceduralObjectiveType.InnerSingleton;
}
// ********** End Enum EProceduralObjectiveType ****************************************************

// ********** Begin Enum EObjectivePriority ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EObjectivePriority;
static UEnum* EObjectivePriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EObjectivePriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EObjectivePriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EObjectivePriority"));
	}
	return Z_Registration_Info_UEnum_EObjectivePriority.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EObjectivePriority>()
{
	return EObjectivePriority_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Objective priority levels\n */" },
#endif
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EObjectivePriority::Critical" },
		{ "Emergency.DisplayName", "Emergency" },
		{ "Emergency.Name", "EObjectivePriority::Emergency" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EObjectivePriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EObjectivePriority::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EObjectivePriority::Medium" },
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective priority levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EObjectivePriority::Low", (int64)EObjectivePriority::Low },
		{ "EObjectivePriority::Medium", (int64)EObjectivePriority::Medium },
		{ "EObjectivePriority::High", (int64)EObjectivePriority::High },
		{ "EObjectivePriority::Critical", (int64)EObjectivePriority::Critical },
		{ "EObjectivePriority::Emergency", (int64)EObjectivePriority::Emergency },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EObjectivePriority",
	"EObjectivePriority",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority()
{
	if (!Z_Registration_Info_UEnum_EObjectivePriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EObjectivePriority.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EObjectivePriority.InnerSingleton;
}
// ********** End Enum EObjectivePriority **********************************************************

// ********** Begin Enum EObjectiveGenerationContext ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EObjectiveGenerationContext;
static UEnum* EObjectiveGenerationContext_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EObjectiveGenerationContext.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EObjectiveGenerationContext.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EObjectiveGenerationContext"));
	}
	return Z_Registration_Info_UEnum_EObjectiveGenerationContext.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EObjectiveGenerationContext>()
{
	return EObjectiveGenerationContext_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Comeback.DisplayName", "Comeback" },
		{ "Comeback.Name", "EObjectiveGenerationContext::Comeback" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Objective generation contexts\n */" },
#endif
		{ "Domination.DisplayName", "Domination" },
		{ "Domination.Name", "EObjectiveGenerationContext::Domination" },
		{ "EarlyGame.DisplayName", "Early Game" },
		{ "EarlyGame.Name", "EObjectiveGenerationContext::EarlyGame" },
		{ "LateGame.DisplayName", "Late Game" },
		{ "LateGame.Name", "EObjectiveGenerationContext::LateGame" },
		{ "MatchStart.DisplayName", "Match Start" },
		{ "MatchStart.Name", "EObjectiveGenerationContext::MatchStart" },
		{ "MidGame.DisplayName", "Mid Game" },
		{ "MidGame.Name", "EObjectiveGenerationContext::MidGame" },
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
		{ "Stalemate.DisplayName", "Stalemate" },
		{ "Stalemate.Name", "EObjectiveGenerationContext::Stalemate" },
		{ "TeamFight.DisplayName", "Team Fight" },
		{ "TeamFight.Name", "EObjectiveGenerationContext::TeamFight" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective generation contexts" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EObjectiveGenerationContext::MatchStart", (int64)EObjectiveGenerationContext::MatchStart },
		{ "EObjectiveGenerationContext::EarlyGame", (int64)EObjectiveGenerationContext::EarlyGame },
		{ "EObjectiveGenerationContext::MidGame", (int64)EObjectiveGenerationContext::MidGame },
		{ "EObjectiveGenerationContext::LateGame", (int64)EObjectiveGenerationContext::LateGame },
		{ "EObjectiveGenerationContext::TeamFight", (int64)EObjectiveGenerationContext::TeamFight },
		{ "EObjectiveGenerationContext::Stalemate", (int64)EObjectiveGenerationContext::Stalemate },
		{ "EObjectiveGenerationContext::Comeback", (int64)EObjectiveGenerationContext::Comeback },
		{ "EObjectiveGenerationContext::Domination", (int64)EObjectiveGenerationContext::Domination },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EObjectiveGenerationContext",
	"EObjectiveGenerationContext",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext()
{
	if (!Z_Registration_Info_UEnum_EObjectiveGenerationContext.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EObjectiveGenerationContext.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EObjectiveGenerationContext.InnerSingleton;
}
// ********** End Enum EObjectiveGenerationContext *************************************************

// ********** Begin ScriptStruct FAuracronMatchStateAnalysis ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis;
class UScriptStruct* FAuracronMatchStateAnalysis::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronMatchStateAnalysis"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Match state analysis data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Match state analysis data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current match phase */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current match phase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MatchDuration_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Match duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Match duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamBalanceScore_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Team balance score (-1.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team balance score (-1.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionIntensity_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Action intensity (0.0 to 2.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Action intensity (0.0 to 2.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCompletionRate_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective completion rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective completion rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerEngagementLevel_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player engagement level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player engagement level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicDiversityScore_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Strategic diversity score */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Strategic diversity score" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastMajorEventTime_MetaData[] = {
		{ "Category", "Match State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last major event time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last major event time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MatchDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TeamBalanceScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActionIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveCompletionRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerEngagementLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicDiversityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastMajorEventTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronMatchStateAnalysis>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, CurrentPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 1028433660
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_MatchDuration = { "MatchDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, MatchDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MatchDuration_MetaData), NewProp_MatchDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_TeamBalanceScore = { "TeamBalanceScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, TeamBalanceScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamBalanceScore_MetaData), NewProp_TeamBalanceScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_ActionIntensity = { "ActionIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, ActionIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionIntensity_MetaData), NewProp_ActionIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_ObjectiveCompletionRate = { "ObjectiveCompletionRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, ObjectiveCompletionRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCompletionRate_MetaData), NewProp_ObjectiveCompletionRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_PlayerEngagementLevel = { "PlayerEngagementLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, PlayerEngagementLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerEngagementLevel_MetaData), NewProp_PlayerEngagementLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_StrategicDiversityScore = { "StrategicDiversityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, StrategicDiversityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicDiversityScore_MetaData), NewProp_StrategicDiversityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_LastMajorEventTime = { "LastMajorEventTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronMatchStateAnalysis, LastMajorEventTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastMajorEventTime_MetaData), NewProp_LastMajorEventTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_MatchDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_TeamBalanceScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_ActionIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_ObjectiveCompletionRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_PlayerEngagementLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_StrategicDiversityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewProp_LastMajorEventTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronMatchStateAnalysis",
	Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::PropPointers),
	sizeof(FAuracronMatchStateAnalysis),
	alignof(FAuracronMatchStateAnalysis),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.InnerSingleton, Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis.InnerSingleton;
}
// ********** End ScriptStruct FAuracronMatchStateAnalysis *****************************************

// ********** Begin ScriptStruct FAuracronProceduralObjective **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective;
class UScriptStruct* FAuracronProceduralObjective::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralObjective, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronProceduralObjective"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural objective configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural objective configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unique objective ID */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique objective ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective priority */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective priority" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRadius_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Duration (0 = permanent) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Duration (0 = permanent)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardMultiplier_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reward multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reward multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTeamSize_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required team size */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required team size" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTeam_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target team (0 = both teams) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target team (0 = both teams)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveDescription_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective description */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective description" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveTags_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective tags */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Creation time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creation time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Completion status */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completion status" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Objective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active status */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active status" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewardMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredTeamSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetTeam;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ObjectiveDescription;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveTags;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CreationTime;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralObjective>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, ObjectiveType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 2895530511
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, Priority), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectivePriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 1994125831
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveRadius = { "ObjectiveRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, ObjectiveRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRadius_MetaData), NewProp_ObjectiveRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_RewardMultiplier = { "RewardMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, RewardMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardMultiplier_MetaData), NewProp_RewardMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_RequiredTeamSize = { "RequiredTeamSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, RequiredTeamSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTeamSize_MetaData), NewProp_RequiredTeamSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_TargetTeam = { "TargetTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, TargetTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTeam_MetaData), NewProp_TargetTeam_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveDescription = { "ObjectiveDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, ObjectiveDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveDescription_MetaData), NewProp_ObjectiveDescription_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveTags = { "ObjectiveTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, ObjectiveTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveTags_MetaData), NewProp_ObjectiveTags_MetaData) }; // 2104890724
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralObjective, CreationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FAuracronProceduralObjective*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralObjective), &Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronProceduralObjective*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralObjective), &Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_RewardMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_RequiredTeamSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_TargetTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_ObjectiveTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronProceduralObjective",
	Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::PropPointers),
	sizeof(FAuracronProceduralObjective),
	alignof(FAuracronProceduralObjective),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralObjective()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralObjective ****************************************

// ********** Begin ScriptStruct FAuracronObjectiveGenerationParams ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams;
class UScriptStruct* FAuracronObjectiveGenerationParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronObjectiveGenerationParams"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Objective generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationContext_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generation context */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredTypes_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Preferred objective types */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Preferred objective types" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeWeights_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generation weights for each type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation weights for each type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinObjectives_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum objectives to maintain */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum objectives to maintain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxObjectives_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum objectives allowed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum objectives allowed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationFrequency_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generation frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAdaptiveGeneration_MetaData[] = {
		{ "Category", "Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable adaptive generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable adaptive generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_GenerationContext_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_GenerationContext;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreferredTypes_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreferredTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreferredTypes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TypeWeights_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TypeWeights_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TypeWeights_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TypeWeights;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinObjectives;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxObjectives;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationFrequency;
	static void NewProp_bEnableAdaptiveGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAdaptiveGeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronObjectiveGenerationParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationContext_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationContext = { "GenerationContext", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, GenerationContext), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationContext_MetaData), NewProp_GenerationContext_MetaData) }; // 1028433660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes_Inner = { "PreferredTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2895530511
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes = { "PreferredTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, PreferredTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredTypes_MetaData), NewProp_PreferredTypes_MetaData) }; // 2895530511
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_ValueProp = { "TypeWeights", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_Key_KeyProp = { "TypeWeights_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2895530511
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights = { "TypeWeights", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, TypeWeights), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeWeights_MetaData), NewProp_TypeWeights_MetaData) }; // 2895530511
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_MinObjectives = { "MinObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, MinObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinObjectives_MetaData), NewProp_MinObjectives_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_MaxObjectives = { "MaxObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, MaxObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxObjectives_MetaData), NewProp_MaxObjectives_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationFrequency = { "GenerationFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronObjectiveGenerationParams, GenerationFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationFrequency_MetaData), NewProp_GenerationFrequency_MetaData) };
void Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_bEnableAdaptiveGeneration_SetBit(void* Obj)
{
	((FAuracronObjectiveGenerationParams*)Obj)->bEnableAdaptiveGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_bEnableAdaptiveGeneration = { "bEnableAdaptiveGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronObjectiveGenerationParams), &Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_bEnableAdaptiveGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAdaptiveGeneration_MetaData), NewProp_bEnableAdaptiveGeneration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationContext_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_PreferredTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_TypeWeights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_MinObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_MaxObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_GenerationFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewProp_bEnableAdaptiveGeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronObjectiveGenerationParams",
	Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::PropPointers),
	sizeof(FAuracronObjectiveGenerationParams),
	alignof(FAuracronObjectiveGenerationParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.InnerSingleton, Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams.InnerSingleton;
}
// ********** End ScriptStruct FAuracronObjectiveGenerationParams **********************************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function AnalyzeMatchState ************
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics
{
	struct AuracronProceduralObjectiveSystem_eventAnalyzeMatchState_Parms
	{
		FAuracronMatchStateAnalysis ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Match Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analyze current match state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analyze current match state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventAnalyzeMatchState_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis, METADATA_PARAMS(0, nullptr) }; // 1432427563
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "AnalyzeMatchState", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::AuracronProceduralObjectiveSystem_eventAnalyzeMatchState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::AuracronProceduralObjectiveSystem_eventAnalyzeMatchState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execAnalyzeMatchState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronMatchStateAnalysis*)Z_Param__Result=P_THIS->AnalyzeMatchState();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function AnalyzeMatchState **************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function CalculateActionIntensity *****
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics
{
	struct AuracronProceduralObjectiveSystem_eventCalculateActionIntensity_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Match Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calculate action intensity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculate action intensity" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventCalculateActionIntensity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "CalculateActionIntensity", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::AuracronProceduralObjectiveSystem_eventCalculateActionIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::AuracronProceduralObjectiveSystem_eventCalculateActionIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execCalculateActionIntensity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateActionIntensity();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function CalculateActionIntensity *******

// ********** Begin Class UAuracronProceduralObjectiveSystem Function CalculateTeamBalance *********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics
{
	struct AuracronProceduralObjectiveSystem_eventCalculateTeamBalance_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Match Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Calculate team balance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Calculate team balance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventCalculateTeamBalance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "CalculateTeamBalance", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::AuracronProceduralObjectiveSystem_eventCalculateTeamBalance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::AuracronProceduralObjectiveSystem_eventCalculateTeamBalance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execCalculateTeamBalance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateTeamBalance();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function CalculateTeamBalance ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function CancelObjective **************
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics
{
	struct AuracronProceduralObjectiveSystem_eventCancelObjective_Parms
	{
		FString ObjectiveID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cancel objective */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cancel objective" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventCancelObjective_Parms, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::NewProp_ObjectiveID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "CancelObjective", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::AuracronProceduralObjectiveSystem_eventCancelObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::AuracronProceduralObjectiveSystem_eventCancelObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execCancelObjective)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ObjectiveID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelObjective(Z_Param_ObjectiveID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function CancelObjective ****************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function CompleteObjective ************
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics
{
	struct AuracronProceduralObjectiveSystem_eventCompleteObjective_Parms
	{
		FString ObjectiveID;
		int32 CompletingTeam;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Complete objective */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Complete objective" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletingTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventCompleteObjective_Parms, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::NewProp_CompletingTeam = { "CompletingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventCompleteObjective_Parms, CompletingTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::NewProp_ObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::NewProp_CompletingTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "CompleteObjective", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::AuracronProceduralObjectiveSystem_eventCompleteObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::AuracronProceduralObjectiveSystem_eventCompleteObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execCompleteObjective)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ObjectiveID);
	P_GET_PROPERTY(FIntProperty,Z_Param_CompletingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteObjective(Z_Param_ObjectiveID,Z_Param_CompletingTeam);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function CompleteObjective **************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GenerateObjectives ***********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generate new objectives based on current match state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generate new objectives based on current match state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GenerateObjectives", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGenerateObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateObjectives();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GenerateObjectives *************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetActiveObjectives **********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetActiveObjectives_Parms
	{
		TArray<FAuracronProceduralObjective> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get all active objectives */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get all active objectives" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetActiveObjectives_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetActiveObjectives", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::AuracronProceduralObjectiveSystem_eventGetActiveObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::AuracronProceduralObjectiveSystem_eventGetActiveObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetActiveObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronProceduralObjective>*)Z_Param__Result=P_THIS->GetActiveObjectives();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetActiveObjectives ************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetCurrentMatchPhase *********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetCurrentMatchPhase_Parms
	{
		EObjectiveGenerationContext ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Match Analysis" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current match phase */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current match phase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetCurrentMatchPhase_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, METADATA_PARAMS(0, nullptr) }; // 1028433660
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetCurrentMatchPhase", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::AuracronProceduralObjectiveSystem_eventGetCurrentMatchPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::AuracronProceduralObjectiveSystem_eventGetCurrentMatchPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetCurrentMatchPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EObjectiveGenerationContext*)Z_Param__Result=P_THIS->GetCurrentMatchPhase();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetCurrentMatchPhase ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetGenerationParameters ******
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetGenerationParameters_Parms
	{
		FAuracronObjectiveGenerationParams ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get generation parameters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get generation parameters" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetGenerationParameters_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams, METADATA_PARAMS(0, nullptr) }; // 3312340170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetGenerationParameters", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::AuracronProceduralObjectiveSystem_eventGetGenerationParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::AuracronProceduralObjectiveSystem_eventGetGenerationParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetGenerationParameters)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronObjectiveGenerationParams*)Z_Param__Result=P_THIS->GetGenerationParameters();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetGenerationParameters ********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetObjectiveByID *************
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetObjectiveByID_Parms
	{
		FString ObjectiveID;
		FAuracronProceduralObjective ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get objective by ID */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get objective by ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectiveByID_Parms, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectiveByID_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::NewProp_ObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetObjectiveByID", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::AuracronProceduralObjectiveSystem_eventGetObjectiveByID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::AuracronProceduralObjectiveSystem_eventGetObjectiveByID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetObjectiveByID)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ObjectiveID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronProceduralObjective*)Z_Param__Result=P_THIS->GetObjectiveByID(Z_Param_ObjectiveID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetObjectiveByID ***************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetObjectivesByType **********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetObjectivesByType_Parms
	{
		EProceduralObjectiveType ObjectiveType;
		TArray<FAuracronProceduralObjective> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get objectives by type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get objectives by type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectivesByType_Parms, ObjectiveType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EProceduralObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2895530511
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectivesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetObjectivesByType", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::AuracronProceduralObjectiveSystem_eventGetObjectivesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::AuracronProceduralObjectiveSystem_eventGetObjectivesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetObjectivesByType)
{
	P_GET_ENUM(EProceduralObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronProceduralObjective>*)Z_Param__Result=P_THIS->GetObjectivesByType(EProceduralObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetObjectivesByType ************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function GetObjectivesForTeam *********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics
{
	struct AuracronProceduralObjectiveSystem_eventGetObjectivesForTeam_Parms
	{
		int32 TeamID;
		TArray<FAuracronProceduralObjective> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get objectives for team */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get objectives for team" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_TeamID = { "TeamID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectivesForTeam_Parms, TeamID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventGetObjectivesForTeam_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_TeamID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "GetObjectivesForTeam", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::AuracronProceduralObjectiveSystem_eventGetObjectivesForTeam_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::AuracronProceduralObjectiveSystem_eventGetObjectivesForTeam_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execGetObjectivesForTeam)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeamID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronProceduralObjective>*)Z_Param__Result=P_THIS->GetObjectivesForTeam(Z_Param_TeamID);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function GetObjectivesForTeam ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function InitializeObjectiveSystem ****
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize procedural objective system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize procedural objective system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "InitializeObjectiveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execInitializeObjectiveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeObjectiveSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function InitializeObjectiveSystem ******

// ********** Begin Class UAuracronProceduralObjectiveSystem Function OnMatchPhaseChanged **********
struct AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms
{
	EObjectiveGenerationContext OldPhase;
	EObjectiveGenerationContext NewPhase;
};
static FName NAME_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged = FName(TEXT("OnMatchPhaseChanged"));
void UAuracronProceduralObjectiveSystem::OnMatchPhaseChanged(EObjectiveGenerationContext OldPhase, EObjectiveGenerationContext NewPhase)
{
	AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms Parms;
	Parms.OldPhase=OldPhase;
	Parms.NewPhase=NewPhase;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when match phase changes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when match phase changes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_OldPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_OldPhase = { "OldPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms, OldPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, METADATA_PARAMS(0, nullptr) }; // 1028433660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_EObjectiveGenerationContext, METADATA_PARAMS(0, nullptr) }; // 1028433660
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_OldPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_OldPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "OnMatchPhaseChanged", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::PropPointers), sizeof(AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronProceduralObjectiveSystem_eventOnMatchPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function OnMatchPhaseChanged ************

// ********** Begin Class UAuracronProceduralObjectiveSystem Function OnObjectiveCancelled *********
struct AuracronProceduralObjectiveSystem_eventOnObjectiveCancelled_Parms
{
	FAuracronProceduralObjective CancelledObjective;
};
static FName NAME_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled = FName(TEXT("OnObjectiveCancelled"));
void UAuracronProceduralObjectiveSystem::OnObjectiveCancelled(FAuracronProceduralObjective const& CancelledObjective)
{
	AuracronProceduralObjectiveSystem_eventOnObjectiveCancelled_Parms Parms;
	Parms.CancelledObjective=CancelledObjective;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when objective is cancelled */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when objective is cancelled" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CancelledObjective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CancelledObjective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::NewProp_CancelledObjective = { "CancelledObjective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnObjectiveCancelled_Parms, CancelledObjective), Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CancelledObjective_MetaData), NewProp_CancelledObjective_MetaData) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::NewProp_CancelledObjective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "OnObjectiveCancelled", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::PropPointers), sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveCancelled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveCancelled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function OnObjectiveCancelled ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function OnObjectiveCompleted *********
struct AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms
{
	FAuracronProceduralObjective CompletedObjective;
	int32 CompletingTeam;
};
static FName NAME_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted = FName(TEXT("OnObjectiveCompleted"));
void UAuracronProceduralObjectiveSystem::OnObjectiveCompleted(FAuracronProceduralObjective const& CompletedObjective, int32 CompletingTeam)
{
	AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms Parms;
	Parms.CompletedObjective=CompletedObjective;
	Parms.CompletingTeam=CompletingTeam;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when objective is completed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when objective is completed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedObjective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedObjective;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletingTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::NewProp_CompletedObjective = { "CompletedObjective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms, CompletedObjective), Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedObjective_MetaData), NewProp_CompletedObjective_MetaData) }; // 4292427082
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::NewProp_CompletingTeam = { "CompletingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms, CompletingTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::NewProp_CompletedObjective,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::NewProp_CompletingTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "OnObjectiveCompleted", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::PropPointers), sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function OnObjectiveCompleted ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function OnObjectiveGenerated *********
struct AuracronProceduralObjectiveSystem_eventOnObjectiveGenerated_Parms
{
	FAuracronProceduralObjective NewObjective;
};
static FName NAME_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated = FName(TEXT("OnObjectiveGenerated"));
void UAuracronProceduralObjectiveSystem::OnObjectiveGenerated(FAuracronProceduralObjective const& NewObjective)
{
	AuracronProceduralObjectiveSystem_eventOnObjectiveGenerated_Parms Parms;
	Parms.NewObjective=NewObjective;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objective Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when new objective is generated */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when new objective is generated" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewObjective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewObjective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::NewProp_NewObjective = { "NewObjective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventOnObjectiveGenerated_Parms, NewObjective), Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewObjective_MetaData), NewProp_NewObjective_MetaData) }; // 4292427082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::NewProp_NewObjective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "OnObjectiveGenerated", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::PropPointers), sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronProceduralObjectiveSystem_eventOnObjectiveGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function OnObjectiveGenerated ***********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function SetAdaptiveGeneration ********
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics
{
	struct AuracronProceduralObjectiveSystem_eventSetAdaptiveGeneration_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable/disable adaptive generation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable/disable adaptive generation" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronProceduralObjectiveSystem_eventSetAdaptiveGeneration_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronProceduralObjectiveSystem_eventSetAdaptiveGeneration_Parms), &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "SetAdaptiveGeneration", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::AuracronProceduralObjectiveSystem_eventSetAdaptiveGeneration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::AuracronProceduralObjectiveSystem_eventSetAdaptiveGeneration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execSetAdaptiveGeneration)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAdaptiveGeneration(Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function SetAdaptiveGeneration **********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function SetGenerationParameters ******
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics
{
	struct AuracronProceduralObjectiveSystem_eventSetGenerationParameters_Parms
	{
		FAuracronObjectiveGenerationParams NewParams;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set generation parameters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewParams_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewParams;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::NewProp_NewParams = { "NewParams", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventSetGenerationParameters_Parms, NewParams), Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewParams_MetaData), NewProp_NewParams_MetaData) }; // 3312340170
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::NewProp_NewParams,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "SetGenerationParameters", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::AuracronProceduralObjectiveSystem_eventSetGenerationParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::AuracronProceduralObjectiveSystem_eventSetGenerationParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execSetGenerationParameters)
{
	P_GET_STRUCT_REF(FAuracronObjectiveGenerationParams,Z_Param_Out_NewParams);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGenerationParameters(Z_Param_Out_NewParams);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function SetGenerationParameters ********

// ********** Begin Class UAuracronProceduralObjectiveSystem Function UpdateObjectives *************
struct Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics
{
	struct AuracronProceduralObjectiveSystem_eventUpdateObjectives_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Procedural Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update existing objectives */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update existing objectives" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronProceduralObjectiveSystem_eventUpdateObjectives_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronProceduralObjectiveSystem, nullptr, "UpdateObjectives", Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::AuracronProceduralObjectiveSystem_eventUpdateObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::AuracronProceduralObjectiveSystem_eventUpdateObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronProceduralObjectiveSystem::execUpdateObjectives)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateObjectives(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronProceduralObjectiveSystem Function UpdateObjectives ***************

// ********** Begin Class UAuracronProceduralObjectiveSystem ***************************************
void UAuracronProceduralObjectiveSystem::StaticRegisterNativesUAuracronProceduralObjectiveSystem()
{
	UClass* Class = UAuracronProceduralObjectiveSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AnalyzeMatchState", &UAuracronProceduralObjectiveSystem::execAnalyzeMatchState },
		{ "CalculateActionIntensity", &UAuracronProceduralObjectiveSystem::execCalculateActionIntensity },
		{ "CalculateTeamBalance", &UAuracronProceduralObjectiveSystem::execCalculateTeamBalance },
		{ "CancelObjective", &UAuracronProceduralObjectiveSystem::execCancelObjective },
		{ "CompleteObjective", &UAuracronProceduralObjectiveSystem::execCompleteObjective },
		{ "GenerateObjectives", &UAuracronProceduralObjectiveSystem::execGenerateObjectives },
		{ "GetActiveObjectives", &UAuracronProceduralObjectiveSystem::execGetActiveObjectives },
		{ "GetCurrentMatchPhase", &UAuracronProceduralObjectiveSystem::execGetCurrentMatchPhase },
		{ "GetGenerationParameters", &UAuracronProceduralObjectiveSystem::execGetGenerationParameters },
		{ "GetObjectiveByID", &UAuracronProceduralObjectiveSystem::execGetObjectiveByID },
		{ "GetObjectivesByType", &UAuracronProceduralObjectiveSystem::execGetObjectivesByType },
		{ "GetObjectivesForTeam", &UAuracronProceduralObjectiveSystem::execGetObjectivesForTeam },
		{ "InitializeObjectiveSystem", &UAuracronProceduralObjectiveSystem::execInitializeObjectiveSystem },
		{ "SetAdaptiveGeneration", &UAuracronProceduralObjectiveSystem::execSetAdaptiveGeneration },
		{ "SetGenerationParameters", &UAuracronProceduralObjectiveSystem::execSetGenerationParameters },
		{ "UpdateObjectives", &UAuracronProceduralObjectiveSystem::execUpdateObjectives },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem;
UClass* UAuracronProceduralObjectiveSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronProceduralObjectiveSystem;
	if (!Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronProceduralObjectiveSystem"),
			Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.InnerSingleton,
			StaticRegisterNativesUAuracronProceduralObjectiveSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem_NoRegister()
{
	return UAuracronProceduralObjectiveSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Procedural Objective System\n * \n * Advanced system for generating dynamic objectives based on match state,\n * player behavior, and strategic requirements. Creates engaging, adaptive\n * gameplay experiences that respond to player actions and match flow.\n */" },
#endif
		{ "IncludePath", "AuracronProceduralObjectiveSystem.h" },
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Procedural Objective System\n\nAdvanced system for generating dynamic objectives based on match state,\nplayer behavior, and strategic requirements. Creates engaging, adaptive\ngameplay experiences that respond to player actions and match flow." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationParams_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective generation parameters */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSystemEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable debug logging */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable debug logging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxObjectivesPerTeam_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum objectives per team */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum objectives per team" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveTimeoutDuration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objective timeout duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective timeout duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveObjectives_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Active objectives */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Active objectives" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedObjectives_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Completed objectives */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completed objectives" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMatchState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current match state analysis */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current match state analysis" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastGenerationTime_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last generation time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last generation time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NextObjectiveID_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Next objective ID counter */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Next objective ID counter" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronProceduralObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationParams;
	static void NewProp_bSystemEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSystemEnabled;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxObjectivesPerTeam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveTimeoutDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveObjectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedObjectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentMatchState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastGenerationTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NextObjectiveID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_AnalyzeMatchState, "AnalyzeMatchState" }, // 2738337122
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateActionIntensity, "CalculateActionIntensity" }, // 3016767078
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CalculateTeamBalance, "CalculateTeamBalance" }, // 13739733
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CancelObjective, "CancelObjective" }, // 2393809955
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_CompleteObjective, "CompleteObjective" }, // 3189800081
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GenerateObjectives, "GenerateObjectives" }, // 248576743
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetActiveObjectives, "GetActiveObjectives" }, // 3239567101
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetCurrentMatchPhase, "GetCurrentMatchPhase" }, // 4284503944
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetGenerationParameters, "GetGenerationParameters" }, // 1078190516
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectiveByID, "GetObjectiveByID" }, // 2663841568
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesByType, "GetObjectivesByType" }, // 3095972036
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_GetObjectivesForTeam, "GetObjectivesForTeam" }, // 3300257247
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_InitializeObjectiveSystem, "InitializeObjectiveSystem" }, // 1883337180
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnMatchPhaseChanged, "OnMatchPhaseChanged" }, // 3332201437
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCancelled, "OnObjectiveCancelled" }, // 1512741524
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveCompleted, "OnObjectiveCompleted" }, // 4255754956
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_OnObjectiveGenerated, "OnObjectiveGenerated" }, // 560363224
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetAdaptiveGeneration, "SetAdaptiveGeneration" }, // 872240725
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_SetGenerationParameters, "SetGenerationParameters" }, // 2819402932
		{ &Z_Construct_UFunction_UAuracronProceduralObjectiveSystem_UpdateObjectives, "UpdateObjectives" }, // 502567509
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronProceduralObjectiveSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_GenerationParams = { "GenerationParams", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, GenerationParams), Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationParams_MetaData), NewProp_GenerationParams_MetaData) }; // 3312340170
void Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bSystemEnabled_SetBit(void* Obj)
{
	((UAuracronProceduralObjectiveSystem*)Obj)->bSystemEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bSystemEnabled = { "bSystemEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronProceduralObjectiveSystem), &Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bSystemEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSystemEnabled_MetaData), NewProp_bSystemEnabled_MetaData) };
void Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((UAuracronProceduralObjectiveSystem*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronProceduralObjectiveSystem), &Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_MaxObjectivesPerTeam = { "MaxObjectivesPerTeam", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, MaxObjectivesPerTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxObjectivesPerTeam_MetaData), NewProp_MaxObjectivesPerTeam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ObjectiveTimeoutDuration = { "ObjectiveTimeoutDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, ObjectiveTimeoutDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveTimeoutDuration_MetaData), NewProp_ObjectiveTimeoutDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ActiveObjectives_Inner = { "ActiveObjectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ActiveObjectives = { "ActiveObjectives", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, ActiveObjectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveObjectives_MetaData), NewProp_ActiveObjectives_MetaData) }; // 4292427082
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CompletedObjectives_Inner = { "CompletedObjectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4292427082
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CompletedObjectives = { "CompletedObjectives", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, CompletedObjectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedObjectives_MetaData), NewProp_CompletedObjectives_MetaData) }; // 4292427082
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CurrentMatchState = { "CurrentMatchState", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, CurrentMatchState), Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMatchState_MetaData), NewProp_CurrentMatchState_MetaData) }; // 1432427563
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_LastGenerationTime = { "LastGenerationTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, LastGenerationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastGenerationTime_MetaData), NewProp_LastGenerationTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_NextObjectiveID = { "NextObjectiveID", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, NextObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NextObjectiveID_MetaData), NewProp_NextObjectiveID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronProceduralObjectiveSystem, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_GenerationParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bSystemEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_MaxObjectivesPerTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ObjectiveTimeoutDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ActiveObjectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_ActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CompletedObjectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CompletedObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CurrentMatchState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_LastGenerationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_NextObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::NewProp_CachedRealmSubsystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::ClassParams = {
	&UAuracronProceduralObjectiveSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronProceduralObjectiveSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.OuterSingleton, Z_Construct_UClass_UAuracronProceduralObjectiveSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem.OuterSingleton;
}
UAuracronProceduralObjectiveSystem::UAuracronProceduralObjectiveSystem() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronProceduralObjectiveSystem);
UAuracronProceduralObjectiveSystem::~UAuracronProceduralObjectiveSystem() {}
// ********** End Class UAuracronProceduralObjectiveSystem *****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EProceduralObjectiveType_StaticEnum, TEXT("EProceduralObjectiveType"), &Z_Registration_Info_UEnum_EProceduralObjectiveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2895530511U) },
		{ EObjectivePriority_StaticEnum, TEXT("EObjectivePriority"), &Z_Registration_Info_UEnum_EObjectivePriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1994125831U) },
		{ EObjectiveGenerationContext_StaticEnum, TEXT("EObjectiveGenerationContext"), &Z_Registration_Info_UEnum_EObjectiveGenerationContext, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1028433660U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronMatchStateAnalysis::StaticStruct, Z_Construct_UScriptStruct_FAuracronMatchStateAnalysis_Statics::NewStructOps, TEXT("AuracronMatchStateAnalysis"), &Z_Registration_Info_UScriptStruct_FAuracronMatchStateAnalysis, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronMatchStateAnalysis), 1432427563U) },
		{ FAuracronProceduralObjective::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralObjective_Statics::NewStructOps, TEXT("AuracronProceduralObjective"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralObjective, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralObjective), 4292427082U) },
		{ FAuracronObjectiveGenerationParams::StaticStruct, Z_Construct_UScriptStruct_FAuracronObjectiveGenerationParams_Statics::NewStructOps, TEXT("AuracronObjectiveGenerationParams"), &Z_Registration_Info_UScriptStruct_FAuracronObjectiveGenerationParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronObjectiveGenerationParams), 3312340170U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronProceduralObjectiveSystem, UAuracronProceduralObjectiveSystem::StaticClass, TEXT("UAuracronProceduralObjectiveSystem"), &Z_Registration_Info_UClass_UAuracronProceduralObjectiveSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronProceduralObjectiveSystem), 102759150U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_1888755424(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronProceduralObjectiveSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
