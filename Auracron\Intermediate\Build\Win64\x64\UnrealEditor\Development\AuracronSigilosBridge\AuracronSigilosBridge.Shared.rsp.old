/I "."
/I "C:/Aura/projeto/Auracron/Source/AuracronSigilosBridge/Private"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/Core/Public"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "Runtime/CorePreciseFP/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Engine/UHT"
/I "Runtime/Engine/Classes"
/I "Runtime/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreOnline/UHT"
/I "Runtime/CoreOnline/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldNotification/UHT"
/I "Runtime/FieldNotification/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NetCore/UHT"
/I "Runtime/Net/Core/Classes"
/I "Runtime/Net/Core/Public"
/I "Runtime/Net/Common/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/JsonUtilities/UHT"
/I "Runtime/JsonUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/RHI/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/ImageWrapper/Public"
/I "Runtime/Messaging/Public"
/I "Runtime/MessagingCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/RenderCore/UHT"
/I "Runtime/RenderCore/Public"
/I "Runtime/OpenGLDrv/Public"
/I "Runtime/Analytics/AnalyticsET/Public"
/I "Runtime/Analytics/Analytics/Public"
/I "Runtime/Sockets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetRegistry/UHT"
/I "Runtime/AssetRegistry/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineMessages/UHT"
/I "Runtime/EngineMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineSettings/UHT"
/I "Runtime/EngineSettings/Classes"
/I "Runtime/EngineSettings/Public"
/I "Runtime/SynthBenchmark/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTags/UHT"
/I "Runtime/GameplayTags/Classes"
/I "Runtime/GameplayTags/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PacketHandler/UHT"
/I "Runtime/PacketHandlers/PacketHandler/Classes"
/I "Runtime/PacketHandlers/PacketHandler/Public"
/I "Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioPlatformConfiguration/UHT"
/I "Runtime/AudioPlatformConfiguration/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MeshDescription/UHT"
/I "Runtime/MeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StaticMeshDescription/UHT"
/I "Runtime/StaticMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletalMeshDescription/UHT"
/I "Runtime/SkeletalMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationCore/UHT"
/I "Runtime/AnimationCore/Public"
/I "Runtime/PakFile/Public"
/I "Runtime/RSA/Public"
/I "Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsCore/UHT"
/I "Runtime/PhysicsCore/Public"
/I "Runtime/Experimental/ChaosCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Chaos/UHT"
/I "Runtime/Experimental/Chaos/Public"
/I "Runtime/Experimental/Voronoi/Public"
/I "Runtime/GeometryCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVDRuntime/UHT"
/I "Runtime/Experimental/ChaosVisualDebugger/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NNE/UHT"
/I "Runtime/NNE/Public"
/I "Runtime/SignalProcessing/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StateStream/UHT"
/I "Runtime/StateStream/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioExtensions/UHT"
/I "Runtime/AudioExtensions/Public"
/I "Runtime/AudioMixerCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioMixer/UHT"
/I "Runtime/AudioMixer/Classes"
/I "Runtime/AudioMixer/Public"
/I "Developer/TargetPlatform/Public"
/I "Developer/TextureFormat/Public"
/I "Developer/DesktopPlatform/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkEngine/UHT"
/I "Runtime/AudioLink/AudioLinkEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkCore/UHT"
/I "Runtime/AudioLink/AudioLinkCore/Public"
/I "Runtime/Networking/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Public"
/I "Developer/TextureBuildUtilities/Public"
/I "Developer/Horde/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothSysRuntimeIntrfc/UHT"
/I "Runtime/ClothingSystemRuntimeInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/IrisCore/UHT"
/I "Runtime/Experimental/Iris/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneCapture/UHT"
/I "Runtime/MovieSceneCapture/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Renderer/UHT"
/I "Runtime/Renderer/Public"
/I "../Shaders/Public"
/I "../Shaders/Shared"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementFramework/UHT"
/I "Runtime/TypedElementFramework/Tests"
/I "Runtime/TypedElementFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementRuntime/UHT"
/I "Runtime/TypedElementRuntime/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationDataController/UHT"
/I "Developer/AnimationDataController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationBlueprintEditor/UHT"
/I "Editor/AnimationBlueprintEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Kismet/UHT"
/I "Editor/Kismet/Classes"
/I "Editor/Kismet/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Persona/UHT"
/I "Editor/Persona/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletonEditor/UHT"
/I "Editor/SkeletonEditor/Public"
/I "Developer/AnimationWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolWidgets/UHT"
/I "Developer/ToolWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenus/UHT"
/I "Developer/ToolMenus/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditor/UHT"
/I "Editor/AnimationEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AdvancedPreviewScene/UHT"
/I "Editor/AdvancedPreviewScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyEditor/UHT"
/I "Editor/PropertyEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorConfig/UHT"
/I "Editor/EditorConfig/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorFramework/UHT"
/I "Editor/EditorFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorSubsystem/UHT"
/I "Editor/EditorSubsystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InteractiveToolsFramework/UHT"
/I "Runtime/InteractiveToolsFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEd/UHT"
/I "Programs/UnrealLightmass/Public"
/I "Editor/UnrealEd/Classes"
/I "Editor/UnrealEd/Public"
/I "Editor/AssetTagsEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CollectionManager/UHT"
/I "Developer/CollectionManager/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowser/UHT"
/I "Editor/ContentBrowser/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetTools/UHT"
/I "Developer/AssetTools/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetDefinition/UHT"
/I "Editor/AssetDefinition/Public"
/I "Developer/Merge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowserData/UHT"
/I "Editor/ContentBrowserData/Public"
/I "Runtime/Projects/Public"
/I "Developer/MeshUtilities/Public"
/I "Developer/MeshMergeUtilities/Public"
/I "Developer/MeshReductionInterface/Public"
/I "Runtime/RawMesh/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialUtilities/UHT"
/I "Developer/MaterialUtilities/Public"
/I "Editor/KismetCompiler/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTasks/UHT"
/I "Runtime/GameplayTasks/Classes"
/I "Runtime/GameplayTasks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClassViewer/UHT"
/I "Editor/ClassViewer/Public"
/I "Developer/DirectoryWatcher/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Documentation/UHT"
/I "Editor/Documentation/Public"
/I "Editor/MainFrame/Public"
/I "Runtime/SandboxFile/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SourceControl/UHT"
/I "Developer/SourceControl/Public"
/I "Developer/UncontrolledChangelists/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEdMessages/UHT"
/I "Editor/UnrealEdMessages/Classes"
/I "Editor/UnrealEdMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/BlueprintGraph/UHT"
/I "Editor/BlueprintGraph/Classes"
/I "Editor/BlueprintGraph/Public"
/I "Runtime/Online/HTTP/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FunctionalTesting/UHT"
/I "Developer/FunctionalTesting/Classes"
/I "Developer/FunctionalTesting/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationController/UHT"
/I "Developer/AutomationController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationTest/UHT"
/I "Runtime/AutomationTest/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Localization/UHT"
/I "Developer/Localization/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioEditor/UHT"
/I "Editor/AudioEditor/Classes"
/I "Editor/AudioEditor/Public"
/I "ThirdParty/libSampleRate/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LevelEditor/UHT"
/I "Editor/LevelEditor/Public"
/I "Editor/CommonMenuExtensions/Public"
/I "Developer/Settings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VREditor/UHT"
/I "Editor/VREditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ViewportInteraction/UHT"
/I "Editor/ViewportInteraction/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/HeadMountedDisplay/UHT"
/I "Runtime/HeadMountedDisplay/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Landscape/UHT"
/I "Runtime/Landscape/Classes"
/I "Runtime/Landscape/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DetailCustomizations/UHT"
/I "Editor/DetailCustomizations/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GraphEditor/UHT"
/I "Editor/GraphEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StructViewer/UHT"
/I "Editor/StructViewer/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialEditor/UHT"
/I "Editor/MaterialEditor/Public"
/I "Runtime/NetworkFileSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UMG/UHT"
/I "Runtime/UMG/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieScene/UHT"
/I "Runtime/MovieScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TimeManagement/UHT"
/I "Runtime/TimeManagement/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UniversalObjectLocator/UHT"
/I "Runtime/UniversalObjectLocator/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneTracks/UHT"
/I "Runtime/MovieSceneTracks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Constraints/UHT"
/I "Runtime/Experimental/Animation/Constraints/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyPath/UHT"
/I "Runtime/PropertyPath/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NavigationSystem/UHT"
/I "Runtime/NavigationSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GeometryCollectionEngine/UHT"
/I "Runtime/Experimental/GeometryCollectionEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosSolverEngine/UHT"
/I "Runtime/Experimental/ChaosSolverEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowCore/UHT"
/I "Runtime/Experimental/Dataflow/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowEngine/UHT"
/I "Runtime/Experimental/Dataflow/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowSimulation/UHT"
/I "Runtime/Experimental/Dataflow/Simulation/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldSystemEngine/UHT"
/I "Runtime/Experimental/FieldSystem/Source/FieldSystemEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ISMPool/UHT"
/I "Runtime/Experimental/ISMPool/Public"
/I "Developer/MeshBuilder/Public"
/I "Runtime/MeshUtilitiesCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MSQS/UHT"
/I "Runtime/MaterialShaderQualitySettings/Classes"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenusEditor/UHT"
/I "Editor/ToolMenusEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StatusBar/UHT"
/I "Editor/StatusBar/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeCore/UHT"
/I "Runtime/Interchange/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeEngine/UHT"
/I "Runtime/Interchange/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperToolSettings/UHT"
/I "Developer/DeveloperToolSettings/Classes"
/I "Developer/DeveloperToolSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectDataInterface/UHT"
/I "Editor/SubobjectDataInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectEditor/UHT"
/I "Editor/SubobjectEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsUtilities/UHT"
/I "Developer/PhysicsUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/WidgetRegistration/UHT"
/I "Developer/WidgetRegistration/Public"
/I "Editor/ActorPickerMode/Public"
/I "Editor/SceneDepthPickerMode/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditMode/UHT"
/I "Editor/AnimationEditMode/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/NiagaraCore/UHT"
/I "../Plugins/FX/Niagara/Source"
/I "../Plugins/FX/Niagara/Source/NiagaraCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VectorVM/UHT"
/I "Runtime/VectorVM/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/NiagaraShader/UHT"
/I "../Plugins/FX/Niagara/Shaders/Shared"
/I "../Plugins/FX/Niagara/Source/NiagaraShader/Public"
/I "../Plugins/FX/Niagara/Source/NiagaraVertexFactories/Public"
/I "../Plugins/FX/Niagara/Intermediate/Build/Win64/UnrealEditor/Inc/Niagara/UHT"
/I "../Plugins/FX/Niagara/Source/Niagara/Classes"
/I "../Plugins/FX/Niagara/Source/Niagara/Public"
/I "../Plugins/Runtime/Metasound/Intermediate/Build/Win64/UnrealEditor/Inc/MetasoundFrontend/UHT"
/I "../Plugins/Runtime/Metasound/Source"
/I "../Plugins/Runtime/Metasound/Source/MetasoundFrontend/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Serialization/UHT"
/I "Runtime/Serialization/Public"
/I "Runtime/Cbor/Public"
/I "../Plugins/Runtime/Metasound/Source/MetasoundGraphCore/Public"
/I "../Plugins/Runtime/Metasound/Intermediate/Build/Win64/UnrealEditor/Inc/MetasoundEngine/UHT"
/I "../Plugins/Runtime/Metasound/Source/MetasoundEngine/Public"
/I "../Plugins/Runtime/Metasound/Source/MetasoundStandardNodes/Public"
/I "../Plugins/Runtime/WaveTable/Intermediate/Build/Win64/UnrealEditor/Inc/WaveTable/UHT"
/I "../Plugins/Runtime/WaveTable/Source"
/I "../Plugins/Runtime/WaveTable/Source/WaveTable/Public"
/I "../Plugins/Online/OnlineSubsystemUtils/Intermediate/Build/Win64/UnrealEditor/Inc/OnlineSubsystemUtils/UHT"
/I "../Plugins/Online/OnlineSubsystemUtils/Source"
/I "../Plugins/Online/OnlineSubsystemUtils/Source/OnlineSubsystemUtils/Classes"
/I "../Plugins/Online/OnlineSubsystemUtils/Source/OnlineSubsystemUtils/Public"
/I "../Plugins/Online/OnlineSubsystem/Intermediate/Build/Win64/UnrealEditor/Inc/OnlineSubsystem/UHT"
/I "../Plugins/Online/OnlineSubsystem/Source/Test"
/I "../Plugins/Online/OnlineSubsystem/Source"
/I "../Plugins/Online/OnlineSubsystem/Source/Public"
/I "../Plugins/Online/OnlineBase/Source"
/I "../Plugins/Online/OnlineBase/Source/Public"
/I "Editor/EditorStyle/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorWidgets/UHT"
/I "Editor/EditorWidgets/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/UnrealEditor/Inc/AuracronSigilosBridge/UHT"
/I "C:/Aura/projeto/Auracron/Source"
/I "C:/Aura/projeto/Auracron/Source/AuracronSigilosBridge/Public"
/I "../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/UnrealEditor/Inc/GameplayAbilities/UHT"
/I "../Plugins/Runtime/GameplayAbilities/Source"
/I "../Plugins/Runtime/GameplayAbilities/Source/GameplayAbilities/Public"
/I "../Plugins/Runtime/DataRegistry/Intermediate/Build/Win64/UnrealEditor/Inc/DataRegistry/UHT"
/I "../Plugins/Runtime/DataRegistry/Source"
/I "../Plugins/Runtime/DataRegistry/Source/DataRegistry/Public"
/I "../Plugins/Runtime/ModularGameplay/Intermediate/Build/Win64/UnrealEditor/Inc/ModularGameplay/UHT"
/I "../Plugins/Runtime/ModularGameplay/Source"
/I "../Plugins/Runtime/ModularGameplay/Source/ModularGameplay/Public"
/I "../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/UnrealEditor/Inc/ReplicationGraph/UHT"
/I "../Plugins/Runtime/ReplicationGraph/Source"
/I "../Plugins/Runtime/ReplicationGraph/Source/Public"
/I "../Plugins/EnhancedInput/Intermediate/Build/Win64/UnrealEditor/Inc/EnhancedInput/UHT"
/I "../Plugins/EnhancedInput/Source"
/I "../Plugins/EnhancedInput/Source/EnhancedInput/Public"
/I "C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/AuracronEditor/Development/UnrealEd"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "ThirdParty/OpenGL"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"