// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAdaptiveJungleAI.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronAdaptiveJungleAI_generated_h
#error "AuracronAdaptiveJungleAI.generated.h already included, missing '#pragma once' in AuracronAdaptiveJungleAI.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronAdaptiveJungleAI_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAuracronJungleCreature;
class APawn;
enum class ECreatureAdaptationType : uint8;
enum class EPlayerBehaviorPattern : uint8;
struct FAuracronCreatureAdaptation;
struct FAuracronJungleAILearningData;
struct FAuracronPlayerBehaviorAnalysis;

// ********** Begin ScriptStruct FAuracronPlayerBehaviorAnalysis ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_92_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronPlayerBehaviorAnalysis_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronPlayerBehaviorAnalysis;
// ********** End ScriptStruct FAuracronPlayerBehaviorAnalysis *************************************

// ********** Begin ScriptStruct FAuracronJungleCreatureAdaptation *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_159_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronJungleCreatureAdaptation_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronJungleCreatureAdaptation;
// ********** End ScriptStruct FAuracronJungleCreatureAdaptation ***********************************

// ********** Begin ScriptStruct FAuracronJungleAILearningData *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_205_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronJungleAILearningData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronJungleAILearningData;
// ********** End ScriptStruct FAuracronJungleAILearningData ***************************************

// ********** Begin Class AAuracronAdaptiveJungleAI ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetAILearning); \
	DECLARE_FUNCTION(execValidateAIModel); \
	DECLARE_FUNCTION(execTrainAIModel); \
	DECLARE_FUNCTION(execUpdateEnvironmentalResponses); \
	DECLARE_FUNCTION(execAdaptEnvironmentToPlayer); \
	DECLARE_FUNCTION(execUpdateCreatureAIBehavior); \
	DECLARE_FUNCTION(execRemoveCreatureAdaptations); \
	DECLARE_FUNCTION(execAdaptCreatureToPlayer); \
	DECLARE_FUNCTION(execGetPlayerBehaviorPattern); \
	DECLARE_FUNCTION(execUpdatePlayerTracking); \
	DECLARE_FUNCTION(execStopPlayerMonitoring); \
	DECLARE_FUNCTION(execStartPlayerMonitoring); \
	DECLARE_FUNCTION(execGetAILearningData); \
	DECLARE_FUNCTION(execApplyCreatureAdaptations); \
	DECLARE_FUNCTION(execAnalyzePlayerBehavior); \
	DECLARE_FUNCTION(execUpdateAILearning); \
	DECLARE_FUNCTION(execInitializeAdaptiveAI);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronAdaptiveJungleAI(); \
	friend struct Z_Construct_UClass_AAuracronAdaptiveJungleAI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronAdaptiveJungleAI, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister) \
	DECLARE_SERIALIZER(AAuracronAdaptiveJungleAI)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronAdaptiveJungleAI(AAuracronAdaptiveJungleAI&&) = delete; \
	AAuracronAdaptiveJungleAI(const AAuracronAdaptiveJungleAI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronAdaptiveJungleAI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronAdaptiveJungleAI); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronAdaptiveJungleAI) \
	NO_API virtual ~AAuracronAdaptiveJungleAI();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_260_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h_263_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronAdaptiveJungleAI;

// ********** End Class AAuracronAdaptiveJungleAI **************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdaptiveJungleAI_h

// ********** Begin Enum EPlayerBehaviorPattern ****************************************************
#define FOREACH_ENUM_EPLAYERBEHAVIORPATTERN(op) \
	op(EPlayerBehaviorPattern::Aggressive) \
	op(EPlayerBehaviorPattern::Cautious) \
	op(EPlayerBehaviorPattern::Exploratory) \
	op(EPlayerBehaviorPattern::Efficient) \
	op(EPlayerBehaviorPattern::Social) \
	op(EPlayerBehaviorPattern::Stealth) \
	op(EPlayerBehaviorPattern::Territorial) \
	op(EPlayerBehaviorPattern::Adaptive) 

enum class EPlayerBehaviorPattern : uint8;
template<> struct TIsUEnumClass<EPlayerBehaviorPattern> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPlayerBehaviorPattern>();
// ********** End Enum EPlayerBehaviorPattern ******************************************************

// ********** Begin Enum ECreatureAdaptationType ***************************************************
#define FOREACH_ENUM_ECREATUREADAPTATIONTYPE(op) \
	op(ECreatureAdaptationType::Behavioral) \
	op(ECreatureAdaptationType::Statistical) \
	op(ECreatureAdaptationType::Environmental) \
	op(ECreatureAdaptationType::Social) \
	op(ECreatureAdaptationType::Tactical) 

enum class ECreatureAdaptationType : uint8;
template<> struct TIsUEnumClass<ECreatureAdaptationType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ECreatureAdaptationType>();
// ********** End Enum ECreatureAdaptationType *****************************************************

// ********** Begin Enum EJungleAIDifficulty *******************************************************
#define FOREACH_ENUM_EJUNGLEAIDIFFICULTY(op) \
	op(EJungleAIDifficulty::Adaptive) \
	op(EJungleAIDifficulty::Static) \
	op(EJungleAIDifficulty::Progressive) \
	op(EJungleAIDifficulty::Reactive) 

enum class EJungleAIDifficulty : uint8;
template<> struct TIsUEnumClass<EJungleAIDifficulty> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleAIDifficulty>();
// ********** End Enum EJungleAIDifficulty *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
