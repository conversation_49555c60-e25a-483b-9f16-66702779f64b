// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "HarmonyEngineGameMode.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_HarmonyEngineGameMode_generated_h
#error "HarmonyEngineGameMode.generated.h already included, missing '#pragma once' in HarmonyEngineGameMode.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_HarmonyEngineGameMode_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APlayerController;
struct FHarmonyInterventionData;
struct FKindnessReward;
struct FPlayerBehaviorSnapshot;

// ********** Begin Class AHarmonyEngineGameMode ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnHarmonyLevelChanged); \
	DECLARE_FUNCTION(execOnHarmonyKindnessReward); \
	DECLARE_FUNCTION(execOnHarmonyCommunityHealing); \
	DECLARE_FUNCTION(execOnHarmonyInterventionTriggered); \
	DECLARE_FUNCTION(execOnHarmonyBehaviorDetected); \
	DECLARE_FUNCTION(execOnTeamworkAction); \
	DECLARE_FUNCTION(execOnPlayerDisconnect); \
	DECLARE_FUNCTION(execOnPlayerDeath); \
	DECLARE_FUNCTION(execOnPlayerKill); \
	DECLARE_FUNCTION(execOnPlayerAction); \
	DECLARE_FUNCTION(execOnPlayerChatMessage);


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_CALLBACK_WRAPPERS
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_AHarmonyEngineGameMode_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAHarmonyEngineGameMode(); \
	friend struct Z_Construct_UClass_AHarmonyEngineGameMode_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_AHarmonyEngineGameMode_NoRegister(); \
public: \
	DECLARE_CLASS2(AHarmonyEngineGameMode, AGameModeBase, COMPILED_IN_FLAGS(0 | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_AHarmonyEngineGameMode_NoRegister) \
	DECLARE_SERIALIZER(AHarmonyEngineGameMode)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AHarmonyEngineGameMode(AHarmonyEngineGameMode&&) = delete; \
	AHarmonyEngineGameMode(const AHarmonyEngineGameMode&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AHarmonyEngineGameMode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AHarmonyEngineGameMode); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AHarmonyEngineGameMode) \
	NO_API virtual ~AHarmonyEngineGameMode();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_16_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h_19_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AHarmonyEngineGameMode;

// ********** End Class AHarmonyEngineGameMode *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
