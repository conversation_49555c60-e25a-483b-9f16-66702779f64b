// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionActorManager.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionActorManager_generated_h
#error "AuracronWorldPartitionActorManager.generated.h already included, missing '#pragma once' in AuracronWorldPartitionActorManager.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionActorManager_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UAuracronWorldPartitionActorManager;
class UWorld;
enum class EAuracronActorLifecycleState : uint8;
enum class EAuracronActorPlacementType : uint8;
enum class EAuracronActorStreamingState : uint8;
struct FAuracronActorDescriptor;
struct FAuracronActorManagementConfiguration;
struct FAuracronActorStatistics;
struct FAuracronSpatialQueryParameters;
struct FAuracronSpatialQueryResult;

// ********** Begin ScriptStruct FAuracronActorManagementConfiguration *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronActorManagementConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronActorManagementConfiguration;
// ********** End ScriptStruct FAuracronActorManagementConfiguration *******************************

// ********** Begin ScriptStruct FAuracronActorDescriptor ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_173_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronActorDescriptor_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronActorDescriptor;
// ********** End ScriptStruct FAuracronActorDescriptor ********************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryParameters ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_259_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSpatialQueryParameters_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSpatialQueryParameters;
// ********** End ScriptStruct FAuracronSpatialQueryParameters *************************************

// ********** Begin ScriptStruct FAuracronSpatialQueryResult ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_319_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronSpatialQueryResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronSpatialQueryResult;
// ********** End ScriptStruct FAuracronSpatialQueryResult *****************************************

// ********** Begin ScriptStruct FAuracronActorStatistics ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_358_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronActorStatistics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronActorStatistics;
// ********** End ScriptStruct FAuracronActorStatistics ********************************************

// ********** Begin Delegate FOnActorPlaced ********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_577_DELEGATE \
static void FOnActorPlaced_DelegateWrapper(const FMulticastScriptDelegate& OnActorPlaced, const FString& ActorId, FVector Location);


// ********** End Delegate FOnActorPlaced **********************************************************

// ********** Begin Delegate FOnActorRemoved *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_578_DELEGATE \
static void FOnActorRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorRemoved, const FString& ActorId);


// ********** End Delegate FOnActorRemoved *********************************************************

// ********** Begin Delegate FOnActorMoved *********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_579_DELEGATE \
static void FOnActorMoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorMoved, const FString& ActorId, FVector NewLocation);


// ********** End Delegate FOnActorMoved ***********************************************************

// ********** Begin Delegate FOnActorStreamingStateChanged *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_580_DELEGATE \
static void FOnActorStreamingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnActorStreamingStateChanged, const FString& ActorId, EAuracronActorStreamingState NewState);


// ********** End Delegate FOnActorStreamingStateChanged *******************************************

// ********** Begin Class UAuracronWorldPartitionActorManager **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDrawDebugActorInfo); \
	DECLARE_FUNCTION(execLogActorState); \
	DECLARE_FUNCTION(execIsActorDebugEnabled); \
	DECLARE_FUNCTION(execEnableActorDebug); \
	DECLARE_FUNCTION(execGetTotalMemoryUsage); \
	DECLARE_FUNCTION(execGetLoadedActorCount); \
	DECLARE_FUNCTION(execGetTotalActorCount); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetActorStatistics); \
	DECLARE_FUNCTION(execGetConfiguration); \
	DECLARE_FUNCTION(execSetConfiguration); \
	DECLARE_FUNCTION(execUpdateActorLifecycles); \
	DECLARE_FUNCTION(execGetActorLifecycleState); \
	DECLARE_FUNCTION(execSetActorLifecycleState); \
	DECLARE_FUNCTION(execGetActorReferencedBy); \
	DECLARE_FUNCTION(execGetActorReferences); \
	DECLARE_FUNCTION(execRemoveActorReference); \
	DECLARE_FUNCTION(execAddActorReference); \
	DECLARE_FUNCTION(execMoveActorToCell); \
	DECLARE_FUNCTION(execGetActorCell); \
	DECLARE_FUNCTION(execGetActorsInCell); \
	DECLARE_FUNCTION(execGetActorsByTag); \
	DECLARE_FUNCTION(execGetActorsByClass); \
	DECLARE_FUNCTION(execGetActorsInBox); \
	DECLARE_FUNCTION(execGetActorsInRadius); \
	DECLARE_FUNCTION(execExecuteSpatialQuery); \
	DECLARE_FUNCTION(execGetStreamingActors); \
	DECLARE_FUNCTION(execGetLoadedActors); \
	DECLARE_FUNCTION(execGetActorStreamingState); \
	DECLARE_FUNCTION(execUnloadActor); \
	DECLARE_FUNCTION(execLoadActor); \
	DECLARE_FUNCTION(execGetActorReference); \
	DECLARE_FUNCTION(execDoesActorExist); \
	DECLARE_FUNCTION(execGetActorIds); \
	DECLARE_FUNCTION(execGetAllActors); \
	DECLARE_FUNCTION(execGetActorDescriptor); \
	DECLARE_FUNCTION(execSetActorScale); \
	DECLARE_FUNCTION(execSetActorRotation); \
	DECLARE_FUNCTION(execMoveActor); \
	DECLARE_FUNCTION(execRemoveActor); \
	DECLARE_FUNCTION(execPlaceActor); \
	DECLARE_FUNCTION(execTick); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetInstance);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionActorManager(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionActorManager_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionActorManager, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionActorManager_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionActorManager)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronWorldPartitionActorManager(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionActorManager(UAuracronWorldPartitionActorManager&&) = delete; \
	UAuracronWorldPartitionActorManager(const UAuracronWorldPartitionActorManager&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionActorManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionActorManager); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWorldPartitionActorManager) \
	NO_API virtual ~UAuracronWorldPartitionActorManager();


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_420_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h_423_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionActorManager;

// ********** End Class UAuracronWorldPartitionActorManager ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionActorManager_h

// ********** Begin Enum EAuracronActorStreamingState **********************************************
#define FOREACH_ENUM_EAURACRONACTORSTREAMINGSTATE(op) \
	op(EAuracronActorStreamingState::Unloaded) \
	op(EAuracronActorStreamingState::Loading) \
	op(EAuracronActorStreamingState::Loaded) \
	op(EAuracronActorStreamingState::Unloading) \
	op(EAuracronActorStreamingState::Failed) \
	op(EAuracronActorStreamingState::Pending) 

enum class EAuracronActorStreamingState : uint8;
template<> struct TIsUEnumClass<EAuracronActorStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorStreamingState>();
// ********** End Enum EAuracronActorStreamingState ************************************************

// ********** Begin Enum EAuracronActorPlacementType ***********************************************
#define FOREACH_ENUM_EAURACRONACTORPLACEMENTTYPE(op) \
	op(EAuracronActorPlacementType::Static) \
	op(EAuracronActorPlacementType::Dynamic) \
	op(EAuracronActorPlacementType::Streaming) \
	op(EAuracronActorPlacementType::Persistent) \
	op(EAuracronActorPlacementType::Temporary) 

enum class EAuracronActorPlacementType : uint8;
template<> struct TIsUEnumClass<EAuracronActorPlacementType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorPlacementType>();
// ********** End Enum EAuracronActorPlacementType *************************************************

// ********** Begin Enum EAuracronSpatialQueryType *************************************************
#define FOREACH_ENUM_EAURACRONSPATIALQUERYTYPE(op) \
	op(EAuracronSpatialQueryType::Point) \
	op(EAuracronSpatialQueryType::Sphere) \
	op(EAuracronSpatialQueryType::Box) \
	op(EAuracronSpatialQueryType::Cylinder) \
	op(EAuracronSpatialQueryType::Cone) \
	op(EAuracronSpatialQueryType::Frustum) 

enum class EAuracronSpatialQueryType : uint8;
template<> struct TIsUEnumClass<EAuracronSpatialQueryType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronSpatialQueryType>();
// ********** End Enum EAuracronSpatialQueryType ***************************************************

// ********** Begin Enum EAuracronActorLifecycleState **********************************************
#define FOREACH_ENUM_EAURACRONACTORLIFECYCLESTATE(op) \
	op(EAuracronActorLifecycleState::Created) \
	op(EAuracronActorLifecycleState::Initialized) \
	op(EAuracronActorLifecycleState::Active) \
	op(EAuracronActorLifecycleState::Inactive) \
	op(EAuracronActorLifecycleState::Destroying) \
	op(EAuracronActorLifecycleState::Destroyed) 

enum class EAuracronActorLifecycleState : uint8;
template<> struct TIsUEnumClass<EAuracronActorLifecycleState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronActorLifecycleState>();
// ********** End Enum EAuracronActorLifecycleState ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
