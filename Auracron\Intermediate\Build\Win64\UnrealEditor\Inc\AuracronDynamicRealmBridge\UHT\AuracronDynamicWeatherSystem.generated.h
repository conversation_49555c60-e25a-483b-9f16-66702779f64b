// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronDynamicWeatherSystem.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronDynamicWeatherSystem_generated_h
#error "AuracronDynamicWeatherSystem.generated.h already included, missing '#pragma once' in AuracronDynamicWeatherSystem.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronDynamicWeatherSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class APawn;
enum class EAuracronRealmLayer : uint8;
enum class EDynamicWeatherType : uint8;
enum class EWeatherIntensity : uint8;
enum class EWeatherTriggerEvent : uint8;
struct FAuracronDynamicWeatherConfig;
struct FAuracronWeatherState;

// ********** Begin ScriptStruct FAuracronDynamicWeatherConfig *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDynamicWeatherConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDynamicWeatherConfig;
// ********** End ScriptStruct FAuracronDynamicWeatherConfig ***************************************

// ********** Begin ScriptStruct FAuracronWeatherState *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_156_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronWeatherState_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronWeatherState;
// ********** End ScriptStruct FAuracronWeatherState ***********************************************

// ********** Begin Class UAuracronDynamicWeatherSystem ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetWeatherTransitionSpeed); \
	DECLARE_FUNCTION(execSetWeatherUpdateFrequency); \
	DECLARE_FUNCTION(execSetWeatherSystemEnabled); \
	DECLARE_FUNCTION(execGetWeatherIntensityAtLocation); \
	DECLARE_FUNCTION(execDoesWeatherAffectLocation); \
	DECLARE_FUNCTION(execRemoveWeatherEffectsFromPlayer); \
	DECLARE_FUNCTION(execApplyWeatherEffectsToPlayer); \
	DECLARE_FUNCTION(execUpdateLayerWeatherEffects); \
	DECLARE_FUNCTION(execGetLayerWeather); \
	DECLARE_FUNCTION(execSetLayerWeather); \
	DECLARE_FUNCTION(execGetCurrentWeatherState); \
	DECLARE_FUNCTION(execTriggerWeatherEvent); \
	DECLARE_FUNCTION(execChangeWeather); \
	DECLARE_FUNCTION(execUpdateWeatherSystem); \
	DECLARE_FUNCTION(execInitializeWeatherSystem);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronDynamicWeatherSystem(); \
	friend struct Z_Construct_UClass_UAuracronDynamicWeatherSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicWeatherSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronDynamicWeatherSystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronDynamicWeatherSystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronDynamicWeatherSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAuracronDynamicWeatherSystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronDynamicWeatherSystem(UAuracronDynamicWeatherSystem&&) = delete; \
	UAuracronDynamicWeatherSystem(const UAuracronDynamicWeatherSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronDynamicWeatherSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronDynamicWeatherSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronDynamicWeatherSystem) \
	NO_API virtual ~UAuracronDynamicWeatherSystem();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_199_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h_202_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronDynamicWeatherSystem;

// ********** End Class UAuracronDynamicWeatherSystem **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicWeatherSystem_h

// ********** Begin Enum EDynamicWeatherType *******************************************************
#define FOREACH_ENUM_EDYNAMICWEATHERTYPE(op) \
	op(EDynamicWeatherType::Clear) \
	op(EDynamicWeatherType::Cloudy) \
	op(EDynamicWeatherType::Rain) \
	op(EDynamicWeatherType::Storm) \
	op(EDynamicWeatherType::Fog) \
	op(EDynamicWeatherType::Wind) \
	op(EDynamicWeatherType::Mystical) \
	op(EDynamicWeatherType::Prismal) \
	op(EDynamicWeatherType::Chaos) \
	op(EDynamicWeatherType::Ethereal) \
	op(EDynamicWeatherType::Abyssal) \
	op(EDynamicWeatherType::Transcendent) 

enum class EDynamicWeatherType : uint8;
template<> struct TIsUEnumClass<EDynamicWeatherType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EDynamicWeatherType>();
// ********** End Enum EDynamicWeatherType *********************************************************

// ********** Begin Enum EWeatherIntensity *********************************************************
#define FOREACH_ENUM_EWEATHERINTENSITY(op) \
	op(EWeatherIntensity::None) \
	op(EWeatherIntensity::Light) \
	op(EWeatherIntensity::Moderate) \
	op(EWeatherIntensity::Heavy) \
	op(EWeatherIntensity::Extreme) \
	op(EWeatherIntensity::Supernatural) 

enum class EWeatherIntensity : uint8;
template<> struct TIsUEnumClass<EWeatherIntensity> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EWeatherIntensity>();
// ********** End Enum EWeatherIntensity ***********************************************************

// ********** Begin Enum EWeatherTriggerEvent ******************************************************
#define FOREACH_ENUM_EWEATHERTRIGGEREVENT(op) \
	op(EWeatherTriggerEvent::MatchStart) \
	op(EWeatherTriggerEvent::TeamFight) \
	op(EWeatherTriggerEvent::IslandActivation) \
	op(EWeatherTriggerEvent::FlowSurge) \
	op(EWeatherTriggerEvent::LayerEvolution) \
	op(EWeatherTriggerEvent::PlayerDeath) \
	op(EWeatherTriggerEvent::ObjectiveComplete) \
	op(EWeatherTriggerEvent::TimeProgression) \
	op(EWeatherTriggerEvent::EnvironmentalShift) 

enum class EWeatherTriggerEvent : uint8;
template<> struct TIsUEnumClass<EWeatherTriggerEvent> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EWeatherTriggerEvent>();
// ********** End Enum EWeatherTriggerEvent ********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
