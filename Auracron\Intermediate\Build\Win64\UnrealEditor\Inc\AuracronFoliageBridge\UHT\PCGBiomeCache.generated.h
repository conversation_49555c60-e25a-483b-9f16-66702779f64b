// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCGBiomeCache.h"

#ifdef AURACRONFOLIAGEBRIDGE_PCGBiomeCache_generated_h
#error "PCGBiomeCache.generated.h already included, missing '#pragma once' in PCGBiomeCache.h"
#endif
#define AURACRONFOLIAGEBRIDGE_PCGBiomeCache_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGComponent;
class UPCGGraph;
enum class EPCGBiomeCacheState : uint8;
struct FPCGBiomeCacheEntry;

// ********** Begin ScriptStruct FPCGBiomeCacheEntry ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPCGBiomeCacheEntry;
// ********** End ScriptStruct FPCGBiomeCacheEntry *************************************************

// ********** Begin Class UPCGBiomeCache ***********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execRefreshCache); \
	DECLARE_FUNCTION(execValidateCache); \
	DECLARE_FUNCTION(execSetBiomeCacheState); \
	DECLARE_FUNCTION(execGetBiomeCacheState); \
	DECLARE_FUNCTION(execGetPCGComponents); \
	DECLARE_FUNCTION(execUnregisterPCGComponent); \
	DECLARE_FUNCTION(execRegisterPCGComponent); \
	DECLARE_FUNCTION(execGetAllBiomeIds); \
	DECLARE_FUNCTION(execGetBiomeEntry); \
	DECLARE_FUNCTION(execRemoveBiomeEntry); \
	DECLARE_FUNCTION(execAddBiomeEntry); \
	DECLARE_FUNCTION(execClearCache); \
	DECLARE_FUNCTION(execInitializeCache);


AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UPCGBiomeCache_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPCGBiomeCache(); \
	friend struct Z_Construct_UClass_UPCGBiomeCache_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UPCGBiomeCache_NoRegister(); \
public: \
	DECLARE_CLASS2(UPCGBiomeCache, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronFoliageBridge"), Z_Construct_UClass_UPCGBiomeCache_NoRegister) \
	DECLARE_SERIALIZER(UPCGBiomeCache)


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UPCGBiomeCache(UPCGBiomeCache&&) = delete; \
	UPCGBiomeCache(const UPCGBiomeCache&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPCGBiomeCache); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPCGBiomeCache); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPCGBiomeCache) \
	NO_API virtual ~UPCGBiomeCache();


#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_70_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h_73_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UPCGBiomeCache;

// ********** End Class UPCGBiomeCache *************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h

// ********** Begin Enum EPCGBiomeCacheState *******************************************************
#define FOREACH_ENUM_EPCGBIOMECACHESTATE(op) \
	op(EPCGBiomeCacheState::Uninitialized) \
	op(EPCGBiomeCacheState::Loading) \
	op(EPCGBiomeCacheState::Ready) \
	op(EPCGBiomeCacheState::Error) 

enum class EPCGBiomeCacheState : uint8;
template<> struct TIsUEnumClass<EPCGBiomeCacheState> { enum { Value = true }; };
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EPCGBiomeCacheState>();
// ********** End Enum EPCGBiomeCacheState *********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
