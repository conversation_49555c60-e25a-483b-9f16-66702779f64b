// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronWorldPartitionBridge.h"

#ifdef AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionBridge_generated_h
#error "AuracronWorldPartitionBridge.generated.h already included, missing '#pragma once' in AuracronWorldPartitionBridge.h"
#endif
#define AURACRONWORLDPARTITIONBRIDGE_AuracronWorldPartitionBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class UWorld;
class UWorldPartition;
class UWorldPartitionSubsystem;
enum class EWorldPartitionActorFilter : uint8;
enum class EWorldPartitionDataLayer : uint8;
enum class EWorldPartitionHLODLevel : uint8;
enum class EWorldPartitionMinimapCaptureMode : uint8;
enum class EWorldPartitionStreamingPolicy : uint8;
enum class EWorldPartitionStreamingSourceShape : uint8;
struct FLinearColor;
struct FWorldPartitionCellInfo;
struct FWorldPartitionDataLayerInfo;
struct FWorldPartitionDetailedStats;
struct FWorldPartitionGridInfo;
struct FWorldPartitionHLODInfo;
struct FWorldPartitionMemoryStats;
struct FWorldPartitionStreamingSourceInfo;

// ********** Begin ScriptStruct FWorldPartitionCellInfo *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_283_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionCellInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionCellInfo;
// ********** End ScriptStruct FWorldPartitionCellInfo *********************************************

// ********** Begin ScriptStruct FWorldPartitionStreamingSourceInfo ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_324_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionStreamingSourceInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionStreamingSourceInfo;
// ********** End ScriptStruct FWorldPartitionStreamingSourceInfo **********************************

// ********** Begin ScriptStruct FWorldPartitionDataLayerInfo **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_370_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionDataLayerInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionDataLayerInfo;
// ********** End ScriptStruct FWorldPartitionDataLayerInfo ****************************************

// ********** Begin ScriptStruct FWorldPartitionHLODInfo *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_415_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionHLODInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionHLODInfo;
// ********** End ScriptStruct FWorldPartitionHLODInfo *********************************************

// ********** Begin ScriptStruct FWorldPartitionGridInfo *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_460_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionGridInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionGridInfo;
// ********** End ScriptStruct FWorldPartitionGridInfo *********************************************

// ********** Begin ScriptStruct FWorldPartitionMemoryStats ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_526_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionMemoryStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionMemoryStats;
// ********** End ScriptStruct FWorldPartitionMemoryStats ******************************************

// ********** Begin ScriptStruct FWorldPartitionDetailedStats **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_563_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldPartitionDetailedStats_Statics; \
	static class UScriptStruct* StaticStruct();


struct FWorldPartitionDetailedStats;
// ********** End ScriptStruct FWorldPartitionDetailedStats ****************************************

// ********** Begin Delegate FOnWorldPartitionCellLoaded *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_606_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionCellLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionCellLoaded, const FString& CellName, bool bSuccess);


// ********** End Delegate FOnWorldPartitionCellLoaded *********************************************

// ********** Begin Delegate FOnWorldPartitionCellUnloaded *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_607_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionCellUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionCellUnloaded, const FString& CellName, bool bSuccess);


// ********** End Delegate FOnWorldPartitionCellUnloaded *******************************************

// ********** Begin Delegate FOnWorldPartitionStreamingCompleted ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_608_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionStreamingCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionStreamingCompleted, FVector const& Location, bool bSuccess);


// ********** End Delegate FOnWorldPartitionStreamingCompleted *************************************

// ********** Begin Delegate FOnWorldPartitionDataLayerChanged *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_609_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionDataLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionDataLayerChanged, const FString& LayerName, bool bLoaded);


// ********** End Delegate FOnWorldPartitionDataLayerChanged ***************************************

// ********** Begin Delegate FOnWorldPartitionHLODGenerated ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_610_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionHLODGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionHLODGenerated, const FString& HLODName, bool bSuccess);


// ********** End Delegate FOnWorldPartitionHLODGenerated ******************************************

// ********** Begin Delegate FOnWorldPartitionMinimapGenerated *************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_611_DELEGATE \
AURACRONWORLDPARTITIONBRIDGE_API void FOnWorldPartitionMinimapGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnWorldPartitionMinimapGenerated, const FString& MinimapPath, bool bSuccess);


// ********** End Delegate FOnWorldPartitionMinimapGenerated ***************************************

// ********** Begin Class UAuracronWorldPartitionBridgeAPI *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetDetailedStatistics); \
	DECLARE_FUNCTION(execConfigureStreamingSources); \
	DECLARE_FUNCTION(execSetAsyncLoadingEnabled); \
	DECLARE_FUNCTION(execConfigureLODSettings); \
	DECLARE_FUNCTION(execGetMemoryStatistics); \
	DECLARE_FUNCTION(execForceGarbageCollection); \
	DECLARE_FUNCTION(execSetDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execConfigureRuntimeHash); \
	DECLARE_FUNCTION(execGetStreamingPolicy); \
	DECLARE_FUNCTION(execSetStreamingPolicy); \
	DECLARE_FUNCTION(execGetWorldPartitionWarnings); \
	DECLARE_FUNCTION(execGetWorldPartitionErrors); \
	DECLARE_FUNCTION(execOptimizeWorldPartition); \
	DECLARE_FUNCTION(execConvertLevelToWorldPartition); \
	DECLARE_FUNCTION(execRepairWorldPartition); \
	DECLARE_FUNCTION(execValidateWorldPartition); \
	DECLARE_FUNCTION(execGetWorldPartitionStats); \
	DECLARE_FUNCTION(execDumpWorldPartitionInfo); \
	DECLARE_FUNCTION(execGetAvailableDebugVisualizationModes); \
	DECLARE_FUNCTION(execSetDebugVisualizationMode); \
	DECLARE_FUNCTION(execIsDebugVisualizationEnabled); \
	DECLARE_FUNCTION(execEnableDebugVisualization); \
	DECLARE_FUNCTION(execIsLevelInstanceLoaded); \
	DECLARE_FUNCTION(execUnloadLevelInstance); \
	DECLARE_FUNCTION(execLoadLevelInstance); \
	DECLARE_FUNCTION(execGetAllLevelInstances); \
	DECLARE_FUNCTION(execDeleteLevelInstance); \
	DECLARE_FUNCTION(execCreateLevelInstance); \
	DECLARE_FUNCTION(execGetActorDataLayers); \
	DECLARE_FUNCTION(execGetActorCountInCell); \
	DECLARE_FUNCTION(execMoveActorToCell); \
	DECLARE_FUNCTION(execIsActorSpatiallyLoaded); \
	DECLARE_FUNCTION(execSetActorSpatiallyLoaded); \
	DECLARE_FUNCTION(execGetActorCell); \
	DECLARE_FUNCTION(execGetActorsInBounds); \
	DECLARE_FUNCTION(execGetActorsInCell); \
	DECLARE_FUNCTION(execGetMinimapGenerationProgress); \
	DECLARE_FUNCTION(execIsMinimapGenerationInProgress); \
	DECLARE_FUNCTION(execSetMinimapCaptureSettings); \
	DECLARE_FUNCTION(execGenerateMinimapForBounds); \
	DECLARE_FUNCTION(execGenerateMinimap); \
	DECLARE_FUNCTION(execGetHLODGenerationProgress); \
	DECLARE_FUNCTION(execIsHLODGenerationInProgress); \
	DECLARE_FUNCTION(execGetHLODScreenSize); \
	DECLARE_FUNCTION(execSetHLODScreenSize); \
	DECLARE_FUNCTION(execDeleteHLODsForLevel); \
	DECLARE_FUNCTION(execDeleteHLODs); \
	DECLARE_FUNCTION(execGenerateHLODsForLevel); \
	DECLARE_FUNCTION(execGenerateHLODs); \
	DECLARE_FUNCTION(execGetHLODInfo); \
	DECLARE_FUNCTION(execGetAllHLODs); \
	DECLARE_FUNCTION(execRemoveActorFromDataLayer); \
	DECLARE_FUNCTION(execAddActorToDataLayer); \
	DECLARE_FUNCTION(execGetActorsInDataLayer); \
	DECLARE_FUNCTION(execDeleteDataLayer); \
	DECLARE_FUNCTION(execCreateDataLayer); \
	DECLARE_FUNCTION(execIsDataLayerLoaded); \
	DECLARE_FUNCTION(execSetDataLayerVisibility); \
	DECLARE_FUNCTION(execUnloadDataLayer); \
	DECLARE_FUNCTION(execLoadDataLayer); \
	DECLARE_FUNCTION(execGetDataLayerInfo); \
	DECLARE_FUNCTION(execGetAllDataLayers); \
	DECLARE_FUNCTION(execSetGridDebugColor); \
	DECLARE_FUNCTION(execEnableGridPreview); \
	DECLARE_FUNCTION(execSetGridLoadingRange); \
	DECLARE_FUNCTION(execSetGridCellSize); \
	DECLARE_FUNCTION(execGetGridInfo); \
	DECLARE_FUNCTION(execGetAllGrids); \
	DECLARE_FUNCTION(execSetStreamingSourcePriority); \
	DECLARE_FUNCTION(execSetStreamingSourceShape); \
	DECLARE_FUNCTION(execGetStreamingSourceInfo); \
	DECLARE_FUNCTION(execGetAllStreamingSources); \
	DECLARE_FUNCTION(execEnableStreamingSource); \
	DECLARE_FUNCTION(execUpdateStreamingSource); \
	DECLARE_FUNCTION(execRemoveStreamingSource); \
	DECLARE_FUNCTION(execAddStreamingSource); \
	DECLARE_FUNCTION(execGetLoadedCellCount); \
	DECLARE_FUNCTION(execGetTotalCellCount); \
	DECLARE_FUNCTION(execGetCellsAtLocation); \
	DECLARE_FUNCTION(execGetCellInfo); \
	DECLARE_FUNCTION(execGetCellsInBounds); \
	DECLARE_FUNCTION(execGetLoadedCells); \
	DECLARE_FUNCTION(execGetAllCells); \
	DECLARE_FUNCTION(execGetStreamingProgress); \
	DECLARE_FUNCTION(execIsStreamingCompleted); \
	DECLARE_FUNCTION(execUnloadCellsByName); \
	DECLARE_FUNCTION(execLoadCellsByName); \
	DECLARE_FUNCTION(execUnloadCellsAtLocation); \
	DECLARE_FUNCTION(execLoadCellsAtLocation); \
	DECLARE_FUNCTION(execIsStreamingEnabled); \
	DECLARE_FUNCTION(execEnableStreaming); \
	DECLARE_FUNCTION(execGetWorldPartitionSubsystem); \
	DECLARE_FUNCTION(execGetWorldPartition); \
	DECLARE_FUNCTION(execIsWorldPartitionEnabled); \
	DECLARE_FUNCTION(execDisableWorldPartition); \
	DECLARE_FUNCTION(execEnableWorldPartition); \
	DECLARE_FUNCTION(execIsWorldPartitionBridgeInitialized); \
	DECLARE_FUNCTION(execShutdownWorldPartitionBridge); \
	DECLARE_FUNCTION(execInitializeWorldPartitionBridge);


AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionBridgeAPI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWorldPartitionBridgeAPI(); \
	friend struct Z_Construct_UClass_UAuracronWorldPartitionBridgeAPI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionBridgeAPI_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWorldPartitionBridgeAPI, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronWorldPartitionBridge"), Z_Construct_UClass_UAuracronWorldPartitionBridgeAPI_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWorldPartitionBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWorldPartitionBridgeAPI(UAuracronWorldPartitionBridgeAPI&&) = delete; \
	UAuracronWorldPartitionBridgeAPI(const UAuracronWorldPartitionBridgeAPI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWorldPartitionBridgeAPI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWorldPartitionBridgeAPI); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronWorldPartitionBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_617_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h_620_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWorldPartitionBridgeAPI;

// ********** End Class UAuracronWorldPartitionBridgeAPI *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionBridge_h

// ********** Begin Enum EWorldPartitionStreamingState *********************************************
#define FOREACH_ENUM_EWORLDPARTITIONSTREAMINGSTATE(op) \
	op(EWorldPartitionStreamingState::Unloaded) \
	op(EWorldPartitionStreamingState::Loading) \
	op(EWorldPartitionStreamingState::Loaded) \
	op(EWorldPartitionStreamingState::Activating) \
	op(EWorldPartitionStreamingState::Activated) \
	op(EWorldPartitionStreamingState::Unloading) 

enum class EWorldPartitionStreamingState : uint8;
template<> struct TIsUEnumClass<EWorldPartitionStreamingState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionStreamingState>();
// ********** End Enum EWorldPartitionStreamingState ***********************************************

// ********** Begin Enum EWorldPartitionDataLayer **************************************************
#define FOREACH_ENUM_EWORLDPARTITIONDATALAYER(op) \
	op(EWorldPartitionDataLayer::Runtime) \
	op(EWorldPartitionDataLayer::Editor) \
	op(EWorldPartitionDataLayer::Both) 

enum class EWorldPartitionDataLayer : uint8;
template<> struct TIsUEnumClass<EWorldPartitionDataLayer> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionDataLayer>();
// ********** End Enum EWorldPartitionDataLayer ****************************************************

// ********** Begin Enum EWorldPartitionHLODLevel **************************************************
#define FOREACH_ENUM_EWORLDPARTITIONHLODLEVEL(op) \
	op(EWorldPartitionHLODLevel::HLOD0) \
	op(EWorldPartitionHLODLevel::HLOD1) \
	op(EWorldPartitionHLODLevel::HLOD2) \
	op(EWorldPartitionHLODLevel::HLOD3) \
	op(EWorldPartitionHLODLevel::HLOD4) \
	op(EWorldPartitionHLODLevel::HLOD5) 

enum class EWorldPartitionHLODLevel : uint8;
template<> struct TIsUEnumClass<EWorldPartitionHLODLevel> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionHLODLevel>();
// ********** End Enum EWorldPartitionHLODLevel ****************************************************

// ********** Begin Enum EWorldPartitionCellState **************************************************
#define FOREACH_ENUM_EWORLDPARTITIONCELLSTATE(op) \
	op(EWorldPartitionCellState::Unloaded) \
	op(EWorldPartitionCellState::Loading) \
	op(EWorldPartitionCellState::Loaded) \
	op(EWorldPartitionCellState::Activated) 

enum class EWorldPartitionCellState : uint8;
template<> struct TIsUEnumClass<EWorldPartitionCellState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionCellState>();
// ********** End Enum EWorldPartitionCellState ****************************************************

// ********** Begin Enum EWorldPartitionGridType ***************************************************
#define FOREACH_ENUM_EWORLDPARTITIONGRIDTYPE(op) \
	op(EWorldPartitionGridType::Runtime2D) \
	op(EWorldPartitionGridType::Runtime3D) \
	op(EWorldPartitionGridType::EditorSpatial) \
	op(EWorldPartitionGridType::Custom) 

enum class EWorldPartitionGridType : uint8;
template<> struct TIsUEnumClass<EWorldPartitionGridType> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionGridType>();
// ********** End Enum EWorldPartitionGridType *****************************************************

// ********** Begin Enum EWorldPartitionStreamingSourceShape ***************************************
#define FOREACH_ENUM_EWORLDPARTITIONSTREAMINGSOURCESHAPE(op) \
	op(EWorldPartitionStreamingSourceShape::Sphere) \
	op(EWorldPartitionStreamingSourceShape::Box) \
	op(EWorldPartitionStreamingSourceShape::Capsule) \
	op(EWorldPartitionStreamingSourceShape::Custom) 

enum class EWorldPartitionStreamingSourceShape : uint8;
template<> struct TIsUEnumClass<EWorldPartitionStreamingSourceShape> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionStreamingSourceShape>();
// ********** End Enum EWorldPartitionStreamingSourceShape *****************************************

// ********** Begin Enum EWorldPartitionStreamingSourceTargetState *********************************
#define FOREACH_ENUM_EWORLDPARTITIONSTREAMINGSOURCETARGETSTATE(op) \
	op(EWorldPartitionStreamingSourceTargetState::Loaded) \
	op(EWorldPartitionStreamingSourceTargetState::Activated) 

enum class EWorldPartitionStreamingSourceTargetState : uint8;
template<> struct TIsUEnumClass<EWorldPartitionStreamingSourceTargetState> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionStreamingSourceTargetState>();
// ********** End Enum EWorldPartitionStreamingSourceTargetState ***********************************

// ********** Begin Enum EWorldPartitionMinimapCaptureMode *****************************************
#define FOREACH_ENUM_EWORLDPARTITIONMINIMAPCAPTUREMODE(op) \
	op(EWorldPartitionMinimapCaptureMode::TopDown) \
	op(EWorldPartitionMinimapCaptureMode::Perspective) \
	op(EWorldPartitionMinimapCaptureMode::Orthographic) 

enum class EWorldPartitionMinimapCaptureMode : uint8;
template<> struct TIsUEnumClass<EWorldPartitionMinimapCaptureMode> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionMinimapCaptureMode>();
// ********** End Enum EWorldPartitionMinimapCaptureMode *******************************************

// ********** Begin Enum EWorldPartitionActorFilter ************************************************
#define FOREACH_ENUM_EWORLDPARTITIONACTORFILTER(op) \
	op(EWorldPartitionActorFilter::All) \
	op(EWorldPartitionActorFilter::SpatiallyLoaded) \
	op(EWorldPartitionActorFilter::AlwaysLoaded) \
	op(EWorldPartitionActorFilter::DataLayerOnly) \
	op(EWorldPartitionActorFilter::HLODRelevant) 

enum class EWorldPartitionActorFilter : uint8;
template<> struct TIsUEnumClass<EWorldPartitionActorFilter> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionActorFilter>();
// ********** End Enum EWorldPartitionActorFilter **************************************************

// ********** Begin Enum EWorldPartitionStreamingPolicy ********************************************
#define FOREACH_ENUM_EWORLDPARTITIONSTREAMINGPOLICY(op) \
	op(EWorldPartitionStreamingPolicy::Default) \
	op(EWorldPartitionStreamingPolicy::Distance) \
	op(EWorldPartitionStreamingPolicy::Priority) \
	op(EWorldPartitionStreamingPolicy::Hybrid) \
	op(EWorldPartitionStreamingPolicy::Custom) 

enum class EWorldPartitionStreamingPolicy : uint8;
template<> struct TIsUEnumClass<EWorldPartitionStreamingPolicy> { enum { Value = true }; };
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EWorldPartitionStreamingPolicy>();
// ********** End Enum EWorldPartitionStreamingPolicy **********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
