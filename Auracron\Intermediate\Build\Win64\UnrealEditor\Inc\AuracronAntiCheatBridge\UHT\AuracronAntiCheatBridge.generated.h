// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronAntiCheatBridge.h"

#ifdef AURACRONANTICHEATBRIDGE_AuracronAntiCheatBridge_generated_h
#error "AuracronAntiCheatBridge.generated.h already included, missing '#pragma once' in AuracronAntiCheatBridge.h"
#endif
#define AURACRONANTICHEATBRIDGE_AuracronAntiCheatBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronAntiCheatAction : uint8;
struct FAuracronCheatDetection;

// ********** Begin ScriptStruct FAuracronBanInfo **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_87_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronBanInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronBanInfo;
// ********** End ScriptStruct FAuracronBanInfo ****************************************************

// ********** Begin ScriptStruct FAuracronKickInfo *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_128_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronKickInfo_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronKickInfo;
// ********** End ScriptStruct FAuracronKickInfo ***************************************************

// ********** Begin ScriptStruct FAuracronCheatDetection *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_153_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCheatDetection_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCheatDetection;
// ********** End ScriptStruct FAuracronCheatDetection *********************************************

// ********** Begin ScriptStruct FAuracronAntiCheatConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_222_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAntiCheatConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAntiCheatConfiguration;
// ********** End ScriptStruct FAuracronAntiCheatConfiguration *************************************

// ********** Begin Delegate FOnCheatDetected ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_530_DELEGATE \
static void FOnCheatDetected_DelegateWrapper(const FMulticastScriptDelegate& OnCheatDetected, FAuracronCheatDetection Detection);


// ********** End Delegate FOnCheatDetected ********************************************************

// ********** Begin Delegate FOnAntiCheatActionTaken ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_535_DELEGATE \
static void FOnAntiCheatActionTaken_DelegateWrapper(const FMulticastScriptDelegate& OnAntiCheatActionTaken, const FString& PlayerID, EAuracronAntiCheatAction Action, const FString& Reason);


// ********** End Delegate FOnAntiCheatActionTaken *************************************************

// ********** Begin Delegate FOnPlayerBanned *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_540_DELEGATE \
static void FOnPlayerBanned_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerBanned, const FString& PlayerID, int32 BanDurationHours, const FString& Reason);


// ********** End Delegate FOnPlayerBanned *********************************************************

// ********** Begin Delegate FOnPlayerKicked *******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_544_DELEGATE \
static void FOnPlayerKicked_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerKicked, const FString& PlayerID, const FString& Reason);


// ********** End Delegate FOnPlayerKicked *********************************************************

// ********** Begin Class UAuracronAntiCheatBridge *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execVerifyIntegrityHash); \
	DECLARE_FUNCTION(execGenerateIntegrityHash); \
	DECLARE_FUNCTION(execDecryptData); \
	DECLARE_FUNCTION(execEncryptData); \
	DECLARE_FUNCTION(execGetSecurityStatistics); \
	DECLARE_FUNCTION(execStopMonitoringPlayer); \
	DECLARE_FUNCTION(execMonitorPlayer); \
	DECLARE_FUNCTION(execKickPlayer); \
	DECLARE_FUNCTION(execBanPlayer); \
	DECLARE_FUNCTION(execTakeAntiCheatAction); \
	DECLARE_FUNCTION(execReportCheatDetection); \
	DECLARE_FUNCTION(execMonitorSuspiciousPerformance); \
	DECLARE_FUNCTION(execVerifyClientIntegrity); \
	DECLARE_FUNCTION(execAnalyzeInputPatterns); \
	DECLARE_FUNCTION(execDetectSuspiciousBehavior); \
	DECLARE_FUNCTION(execValidateActionTiming); \
	DECLARE_FUNCTION(execValidatePlayerPosition); \
	DECLARE_FUNCTION(execValidateDamageDealt); \
	DECLARE_FUNCTION(execValidateAbilityUsage); \
	DECLARE_FUNCTION(execValidatePlayerMovement);


AURACRONANTICHEATBRIDGE_API UClass* Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronAntiCheatBridge(); \
	friend struct Z_Construct_UClass_UAuracronAntiCheatBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONANTICHEATBRIDGE_API UClass* Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronAntiCheatBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronAntiCheatBridge"), Z_Construct_UClass_UAuracronAntiCheatBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronAntiCheatBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		AntiCheatConfiguration=NETFIELD_REP_START, \
		MonitoredPlayers, \
		NETFIELD_REP_END=MonitoredPlayers	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronAntiCheatBridge(UAuracronAntiCheatBridge&&) = delete; \
	UAuracronAntiCheatBridge(const UAuracronAntiCheatBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronAntiCheatBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronAntiCheatBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronAntiCheatBridge) \
	NO_API virtual ~UAuracronAntiCheatBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_325_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h_328_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronAntiCheatBridge;

// ********** End Class UAuracronAntiCheatBridge ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronAntiCheatBridge_Public_AuracronAntiCheatBridge_h

// ********** Begin Enum EAuracronCheatType ********************************************************
#define FOREACH_ENUM_EAURACRONCHEATTYPE(op) \
	op(EAuracronCheatType::None) \
	op(EAuracronCheatType::SpeedHack) \
	op(EAuracronCheatType::TeleportHack) \
	op(EAuracronCheatType::WallHack) \
	op(EAuracronCheatType::AimbotHack) \
	op(EAuracronCheatType::DamageHack) \
	op(EAuracronCheatType::HealthHack) \
	op(EAuracronCheatType::ResourceHack) \
	op(EAuracronCheatType::CooldownHack) \
	op(EAuracronCheatType::PositionHack) \
	op(EAuracronCheatType::InputHack) \
	op(EAuracronCheatType::MemoryHack) \
	op(EAuracronCheatType::NetworkHack) \
	op(EAuracronCheatType::TimingHack) \
	op(EAuracronCheatType::StatHack) \
	op(EAuracronCheatType::AbilityHack) \
	op(EAuracronCheatType::ItemHack) \
	op(EAuracronCheatType::ExperienceHack) \
	op(EAuracronCheatType::UnknownHack) 

enum class EAuracronCheatType : uint8;
template<> struct TIsUEnumClass<EAuracronCheatType> { enum { Value = true }; };
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronCheatType>();
// ********** End Enum EAuracronCheatType **********************************************************

// ********** Begin Enum EAuracronCheatSeverity ****************************************************
#define FOREACH_ENUM_EAURACRONCHEATSEVERITY(op) \
	op(EAuracronCheatSeverity::Low) \
	op(EAuracronCheatSeverity::Medium) \
	op(EAuracronCheatSeverity::High) \
	op(EAuracronCheatSeverity::Critical) \
	op(EAuracronCheatSeverity::Immediate) 

enum class EAuracronCheatSeverity : uint8;
template<> struct TIsUEnumClass<EAuracronCheatSeverity> { enum { Value = true }; };
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronCheatSeverity>();
// ********** End Enum EAuracronCheatSeverity ******************************************************

// ********** Begin Enum EAuracronAntiCheatAction **************************************************
#define FOREACH_ENUM_EAURACRONANTICHEATACTION(op) \
	op(EAuracronAntiCheatAction::None) \
	op(EAuracronAntiCheatAction::Warning) \
	op(EAuracronAntiCheatAction::Correction) \
	op(EAuracronAntiCheatAction::Kick) \
	op(EAuracronAntiCheatAction::TempBan) \
	op(EAuracronAntiCheatAction::PermBan) \
	op(EAuracronAntiCheatAction::Investigation) \
	op(EAuracronAntiCheatAction::Monitoring) 

enum class EAuracronAntiCheatAction : uint8;
template<> struct TIsUEnumClass<EAuracronAntiCheatAction> { enum { Value = true }; };
template<> AURACRONANTICHEATBRIDGE_API UEnum* StaticEnum<EAuracronAntiCheatAction>();
// ********** End Enum EAuracronAntiCheatAction ****************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
