// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronMetaHumanBridge.h"

#ifdef AURACRONMETAHUMANBRIDGE_AuracronMetaHumanBridge_generated_h
#error "AuracronMetaHumanBridge.generated.h already included, missing '#pragma once' in AuracronMetaHumanBridge.h"
#endif
#define AURACRONMETAHUMANBRIDGE_AuracronMetaHumanBridge_generated_h

#include "Templates/IsUEnumClass.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ReflectedTypeAccessors.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h

// ********** Begin Enum EMetaHumanDNADataLayer ****************************************************
#define FOREACH_ENUM_EMETAHUMANDNADATALAYER(op) \
	op(EMetaHumanDNADataLayer::All) \
	op(EMetaHumanDNADataLayer::Descriptor) \
	op(EMetaHumanDNADataLayer::Definition) \
	op(EMetaHumanDNADataLayer::Behavior) \
	op(EMetaHumanDNADataLayer::Geometry) \
	op(EMetaHumanDNADataLayer::GeometryWithoutBlendShapes) \
	op(EMetaHumanDNADataLayer::MachineLearnedBehavior) 

enum class EMetaHumanDNADataLayer : uint8;
template<> struct TIsUEnumClass<EMetaHumanDNADataLayer> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanDNADataLayer>();
// ********** End Enum EMetaHumanDNADataLayer ******************************************************

// ********** Begin Enum EMetaHumanGender **********************************************************
#define FOREACH_ENUM_EMETAHUMANGENDER(op) \
	op(EMetaHumanGender::Male) \
	op(EMetaHumanGender::Female) \
	op(EMetaHumanGender::Other) 

enum class EMetaHumanGender : uint8;
template<> struct TIsUEnumClass<EMetaHumanGender> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanGender>();
// ********** End Enum EMetaHumanGender ************************************************************

// ********** Begin Enum EMetaHumanArchetype *******************************************************
#define FOREACH_ENUM_EMETAHUMANARCHETYPE(op) \
	op(EMetaHumanArchetype::Standard) \
	op(EMetaHumanArchetype::Athletic) \
	op(EMetaHumanArchetype::Heavy) \
	op(EMetaHumanArchetype::Slim) \
	op(EMetaHumanArchetype::Custom) 

enum class EMetaHumanArchetype : uint8;
template<> struct TIsUEnumClass<EMetaHumanArchetype> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanArchetype>();
// ********** End Enum EMetaHumanArchetype *********************************************************

// ********** Begin Enum EMetaHumanCoordinateSystem ************************************************
#define FOREACH_ENUM_EMETAHUMANCOORDINATESYSTEM(op) \
	op(EMetaHumanCoordinateSystem::LeftHanded) \
	op(EMetaHumanCoordinateSystem::RightHanded) 

enum class EMetaHumanCoordinateSystem : uint8;
template<> struct TIsUEnumClass<EMetaHumanCoordinateSystem> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanCoordinateSystem>();
// ********** End Enum EMetaHumanCoordinateSystem **************************************************

// ********** Begin Enum EMetaHumanRotationUnit ****************************************************
#define FOREACH_ENUM_EMETAHUMANROTATIONUNIT(op) \
	op(EMetaHumanRotationUnit::Degrees) \
	op(EMetaHumanRotationUnit::Radians) 

enum class EMetaHumanRotationUnit : uint8;
template<> struct TIsUEnumClass<EMetaHumanRotationUnit> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanRotationUnit>();
// ********** End Enum EMetaHumanRotationUnit ******************************************************

// ********** Begin Enum EMetaHumanTranslationUnit *************************************************
#define FOREACH_ENUM_EMETAHUMANTRANSLATIONUNIT(op) \
	op(EMetaHumanTranslationUnit::Centimeters) \
	op(EMetaHumanTranslationUnit::Meters) 

enum class EMetaHumanTranslationUnit : uint8;
template<> struct TIsUEnumClass<EMetaHumanTranslationUnit> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanTranslationUnit>();
// ********** End Enum EMetaHumanTranslationUnit ***************************************************

// ********** Begin Enum EBlendShapeInterpolationType **********************************************
#define FOREACH_ENUM_EBLENDSHAPEINTERPOLATIONTYPE(op) \
	op(EBlendShapeInterpolationType::Linear) \
	op(EBlendShapeInterpolationType::Cubic) \
	op(EBlendShapeInterpolationType::Smoothstep) \
	op(EBlendShapeInterpolationType::Hermite) \
	op(EBlendShapeInterpolationType::Bezier) 

enum class EBlendShapeInterpolationType : uint8;
template<> struct TIsUEnumClass<EBlendShapeInterpolationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeInterpolationType>();
// ********** End Enum EBlendShapeInterpolationType ************************************************

// ********** Begin Enum EFacialExpressionType *****************************************************
#define FOREACH_ENUM_EFACIALEXPRESSIONTYPE(op) \
	op(EFacialExpressionType::Neutral) \
	op(EFacialExpressionType::Happy) \
	op(EFacialExpressionType::Sad) \
	op(EFacialExpressionType::Angry) \
	op(EFacialExpressionType::Surprised) \
	op(EFacialExpressionType::Disgusted) \
	op(EFacialExpressionType::Fearful) \
	op(EFacialExpressionType::Contempt) \
	op(EFacialExpressionType::Custom) 

enum class EFacialExpressionType : uint8;
template<> struct TIsUEnumClass<EFacialExpressionType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EFacialExpressionType>();
// ********** End Enum EFacialExpressionType *******************************************************

// ********** Begin Enum EBlendShapeValidationType *************************************************
#define FOREACH_ENUM_EBLENDSHAPEVALIDATIONTYPE(op) \
	op(EBlendShapeValidationType::None) \
	op(EBlendShapeValidationType::Basic) \
	op(EBlendShapeValidationType::Comprehensive) \
	op(EBlendShapeValidationType::Performance) 

enum class EBlendShapeValidationType : uint8;
template<> struct TIsUEnumClass<EBlendShapeValidationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeValidationType>();
// ********** End Enum EBlendShapeValidationType ***************************************************

// ********** Begin Enum EJointConstraintType ******************************************************
#define FOREACH_ENUM_EJOINTCONSTRAINTTYPE(op) \
	op(EJointConstraintType::None) \
	op(EJointConstraintType::Position) \
	op(EJointConstraintType::Rotation) \
	op(EJointConstraintType::Scale) \
	op(EJointConstraintType::LookAt) \
	op(EJointConstraintType::Aim) \
	op(EJointConstraintType::Parent) \
	op(EJointConstraintType::Point) \
	op(EJointConstraintType::Orient) 

enum class EJointConstraintType : uint8;
template<> struct TIsUEnumClass<EJointConstraintType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointConstraintType>();
// ********** End Enum EJointConstraintType ********************************************************

// ********** Begin Enum EJointTransformSpace ******************************************************
#define FOREACH_ENUM_EJOINTTRANSFORMSPACE(op) \
	op(EJointTransformSpace::Local) \
	op(EJointTransformSpace::World) \
	op(EJointTransformSpace::Parent) \
	op(EJointTransformSpace::Component) 

enum class EJointTransformSpace : uint8;
template<> struct TIsUEnumClass<EJointTransformSpace> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointTransformSpace>();
// ********** End Enum EJointTransformSpace ********************************************************

// ********** Begin Enum EClothingMaterialType *****************************************************
#define FOREACH_ENUM_ECLOTHINGMATERIALTYPE(op) \
	op(EClothingMaterialType::Cotton) \
	op(EClothingMaterialType::Silk) \
	op(EClothingMaterialType::Leather) \
	op(EClothingMaterialType::Chainmail) \
	op(EClothingMaterialType::Plate) \
	op(EClothingMaterialType::Fabric) \
	op(EClothingMaterialType::Denim) \
	op(EClothingMaterialType::Wool) \
	op(EClothingMaterialType::Linen) \
	op(EClothingMaterialType::Synthetic) 

enum class EClothingMaterialType : uint8;
template<> struct TIsUEnumClass<EClothingMaterialType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaterialType>();
// ********** End Enum EClothingMaterialType *******************************************************

// ********** Begin Enum ELipSyncType **************************************************************
#define FOREACH_ENUM_ELIPSYNCTYPE(op) \
	op(ELipSyncType::Phoneme) \
	op(ELipSyncType::Viseme) \
	op(ELipSyncType::Audio2Face) \
	op(ELipSyncType::Procedural) \
	op(ELipSyncType::MachineLearning) \
	op(ELipSyncType::Hybrid) 

enum class ELipSyncType : uint8;
template<> struct TIsUEnumClass<ELipSyncType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ELipSyncType>();
// ********** End Enum ELipSyncType ****************************************************************

// ********** Begin Enum EEmotionMappingType *******************************************************
#define FOREACH_ENUM_EEMOTIONMAPPINGTYPE(op) \
	op(EEmotionMappingType::Basic) \
	op(EEmotionMappingType::Extended) \
	op(EEmotionMappingType::FACS) \
	op(EEmotionMappingType::Custom) \
	op(EEmotionMappingType::Procedural) \
	op(EEmotionMappingType::MachineLearning) 

enum class EEmotionMappingType : uint8;
template<> struct TIsUEnumClass<EEmotionMappingType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEmotionMappingType>();
// ********** End Enum EEmotionMappingType *********************************************************

// ********** Begin Enum EPoseGenerationMethod *****************************************************
#define FOREACH_ENUM_EPOSEGENERATIONMETHOD(op) \
	op(EPoseGenerationMethod::Manual) \
	op(EPoseGenerationMethod::Procedural) \
	op(EPoseGenerationMethod::MotionCapture) \
	op(EPoseGenerationMethod::MachineLearning) \
	op(EPoseGenerationMethod::Hybrid) \
	op(EPoseGenerationMethod::Template) 

enum class EPoseGenerationMethod : uint8;
template<> struct TIsUEnumClass<EPoseGenerationMethod> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPoseGenerationMethod>();
// ********** End Enum EPoseGenerationMethod *******************************************************

// ********** Begin Enum EAnimationBlueprintQuality ************************************************
#define FOREACH_ENUM_EANIMATIONBLUEPRINTQUALITY(op) \
	op(EAnimationBlueprintQuality::Low) \
	op(EAnimationBlueprintQuality::Medium) \
	op(EAnimationBlueprintQuality::High) \
	op(EAnimationBlueprintQuality::Ultra) \
	op(EAnimationBlueprintQuality::Custom) \
	op(EAnimationBlueprintQuality::Adaptive) \
	op(EAnimationBlueprintQuality::PerformanceBased) \
	op(EAnimationBlueprintQuality::QualityBased) 

enum class EAnimationBlueprintQuality : uint8;
template<> struct TIsUEnumClass<EAnimationBlueprintQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAnimationBlueprintQuality>();
// ********** End Enum EAnimationBlueprintQuality **************************************************

// ********** Begin Enum EPhonemeType **************************************************************
#define FOREACH_ENUM_EPHONEMETYPE(op) \
	op(EPhonemeType::A) \
	op(EPhonemeType::E) \
	op(EPhonemeType::I) \
	op(EPhonemeType::O) \
	op(EPhonemeType::U) \
	op(EPhonemeType::B) \
	op(EPhonemeType::C) \
	op(EPhonemeType::D) \
	op(EPhonemeType::F) \
	op(EPhonemeType::G) \
	op(EPhonemeType::H) \
	op(EPhonemeType::J) \
	op(EPhonemeType::K) \
	op(EPhonemeType::L) \
	op(EPhonemeType::M) \
	op(EPhonemeType::N) \
	op(EPhonemeType::P) \
	op(EPhonemeType::Q) \
	op(EPhonemeType::R) \
	op(EPhonemeType::S) \
	op(EPhonemeType::T) \
	op(EPhonemeType::V) \
	op(EPhonemeType::W) \
	op(EPhonemeType::X) \
	op(EPhonemeType::Y) \
	op(EPhonemeType::Z) \
	op(EPhonemeType::TH) \
	op(EPhonemeType::SH) \
	op(EPhonemeType::CH) \
	op(EPhonemeType::Silent) 

enum class EPhonemeType : uint8;
template<> struct TIsUEnumClass<EPhonemeType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPhonemeType>();
// ********** End Enum EPhonemeType ****************************************************************

// ********** Begin Enum EClothingFittingMethod ****************************************************
#define FOREACH_ENUM_ECLOTHINGFITTINGMETHOD(op) \
	op(EClothingFittingMethod::Automatic) \
	op(EClothingFittingMethod::Manual) \
	op(EClothingFittingMethod::Procedural) \
	op(EClothingFittingMethod::TemplateBase) \
	op(EClothingFittingMethod::MorphTarget) 

enum class EClothingFittingMethod : uint8;
template<> struct TIsUEnumClass<EClothingFittingMethod> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingFittingMethod>();
// ********** End Enum EClothingFittingMethod ******************************************************

// ********** Begin Enum EClothingLODMode **********************************************************
#define FOREACH_ENUM_ECLOTHINGLODMODE(op) \
	op(EClothingLODMode::Automatic) \
	op(EClothingLODMode::Manual) \
	op(EClothingLODMode::DistanceBased) \
	op(EClothingLODMode::PerformanceBased) 

enum class EClothingLODMode : uint8;
template<> struct TIsUEnumClass<EClothingLODMode> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingLODMode>();
// ********** End Enum EClothingLODMode ************************************************************

// ********** Begin Enum EClothingPaintTool ********************************************************
#define FOREACH_ENUM_ECLOTHINGPAINTTOOL(op) \
	op(EClothingPaintTool::Brush) \
	op(EClothingPaintTool::Gradient) \
	op(EClothingPaintTool::Smooth) \
	op(EClothingPaintTool::Fill) \
	op(EClothingPaintTool::Erase) 

enum class EClothingPaintTool : uint8;
template<> struct TIsUEnumClass<EClothingPaintTool> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingPaintTool>();
// ********** End Enum EClothingPaintTool **********************************************************

// ********** Begin Enum EClothingLayerType ********************************************************
#define FOREACH_ENUM_ECLOTHINGLAYERTYPE(op) \
	op(EClothingLayerType::BaseLayer) \
	op(EClothingLayerType::OverLayer) \
	op(EClothingLayerType::UnderLayer) \
	op(EClothingLayerType::AccessoryLayer) 

enum class EClothingLayerType : uint8;
template<> struct TIsUEnumClass<EClothingLayerType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingLayerType>();
// ********** End Enum EClothingLayerType **********************************************************

// ********** Begin Enum EClothingMaskTarget *******************************************************
#define FOREACH_ENUM_ECLOTHINGMASKTARGET(op) \
	op(EClothingMaskTarget::MaxDistance) \
	op(EClothingMaskTarget::BackstopDistance) \
	op(EClothingMaskTarget::BackstopRadius) \
	op(EClothingMaskTarget::AnimDriveStiffness) \
	op(EClothingMaskTarget::AnimDriveDamping) 

enum class EClothingMaskTarget : uint8;
template<> struct TIsUEnumClass<EClothingMaskTarget> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaskTarget>();
// ********** End Enum EClothingMaskTarget *********************************************************

// ********** Begin Enum EMeshDeformationType ******************************************************
#define FOREACH_ENUM_EMESHDEFORMATIONTYPE(op) \
	op(EMeshDeformationType::Vertex) \
	op(EMeshDeformationType::Normal) \
	op(EMeshDeformationType::UV) \
	op(EMeshDeformationType::LOD) \
	op(EMeshDeformationType::Combined) 

enum class EMeshDeformationType : uint8;
template<> struct TIsUEnumClass<EMeshDeformationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMeshDeformationType>();
// ********** End Enum EMeshDeformationType ********************************************************

// ********** Begin Enum EHairCardQuality **********************************************************
#define FOREACH_ENUM_EHAIRCARDQUALITY(op) \
	op(EHairCardQuality::Low) \
	op(EHairCardQuality::Medium) \
	op(EHairCardQuality::High) \
	op(EHairCardQuality::Ultra) 

enum class EHairCardQuality : uint8;
template<> struct TIsUEnumClass<EHairCardQuality> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairCardQuality>();
// ********** End Enum EHairCardQuality ************************************************************

// ********** Begin Enum EClothingCollisionType ****************************************************
#define FOREACH_ENUM_ECLOTHINGCOLLISIONTYPE(op) \
	op(EClothingCollisionType::None) \
	op(EClothingCollisionType::Basic) \
	op(EClothingCollisionType::Precise) \
	op(EClothingCollisionType::Convex) \
	op(EClothingCollisionType::TriangleMesh) 

enum class EClothingCollisionType : uint8;
template<> struct TIsUEnumClass<EClothingCollisionType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingCollisionType>();
// ********** End Enum EClothingCollisionType ******************************************************

// ********** Begin Enum EClothingQualityLevel *****************************************************
#define FOREACH_ENUM_ECLOTHINGQUALITYLEVEL(op) \
	op(EClothingQualityLevel::Low) \
	op(EClothingQualityLevel::Medium) \
	op(EClothingQualityLevel::High) \
	op(EClothingQualityLevel::Ultra) \
	op(EClothingQualityLevel::Cinematic) 

enum class EClothingQualityLevel : uint8;
template<> struct TIsUEnumClass<EClothingQualityLevel> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingQualityLevel>();
// ********** End Enum EClothingQualityLevel *******************************************************

// ********** Begin Enum ERigTransformationType ****************************************************
#define FOREACH_ENUM_ERIGTRANSFORMATIONTYPE(op) \
	op(ERigTransformationType::BoneScaling) \
	op(ERigTransformationType::Constraint) \
	op(ERigTransformationType::IKSetup) \
	op(ERigTransformationType::FKSetup) \
	op(ERigTransformationType::Retargeting) \
	op(ERigTransformationType::Combined) 

enum class ERigTransformationType : uint8;
template<> struct TIsUEnumClass<ERigTransformationType> { enum { Value = true }; };
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERigTransformationType>();
// ********** End Enum ERigTransformationType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
