// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronCombatBridgeSettings.h"

#ifdef AURACRONCOMBATBRIDGE_AuracronCombatBridgeSettings_generated_h
#error "AuracronCombatBridgeSettings.generated.h already included, missing '#pragma once' in AuracronCombatBridgeSettings.h"
#endif
#define AURACRONCOMBATBRIDGE_AuracronCombatBridgeSettings_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronCombatBridgeSettings;
struct FAuracronAdvancedDestructionConfig;
struct FAuracronAICombatConfig;
struct FAuracronElementalDamageConfig;
struct FAuracronEnhancedInputConfig;

// ********** Begin Class UAuracronCombatBridgeSettings ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetDefaultAdvancedDestructionConfig); \
	DECLARE_FUNCTION(execGetDefaultElementalDamageConfig); \
	DECLARE_FUNCTION(execGetDefaultAICombatConfig); \
	DECLARE_FUNCTION(execGetDefaultEnhancedInputConfig); \
	DECLARE_FUNCTION(execGetGlobalCombatTickInterval); \
	DECLARE_FUNCTION(execAreAdvancedCombatFeaturesEnabled); \
	DECLARE_FUNCTION(execGetCombatBridgeSettings);


AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronCombatBridgeSettings(); \
	friend struct Z_Construct_UClass_UAuracronCombatBridgeSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronCombatBridgeSettings, UDeveloperSettings, COMPILED_IN_FLAGS(0 | CLASS_DefaultConfig | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronCombatBridge"), Z_Construct_UClass_UAuracronCombatBridgeSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronCombatBridgeSettings) \
	static const TCHAR* StaticConfigName() {return TEXT("Game");} \



#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronCombatBridgeSettings(UAuracronCombatBridgeSettings&&) = delete; \
	UAuracronCombatBridgeSettings(const UAuracronCombatBridgeSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronCombatBridgeSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronCombatBridgeSettings); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronCombatBridgeSettings) \
	NO_API virtual ~UAuracronCombatBridgeSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_15_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronCombatBridgeSettings;

// ********** End Class UAuracronCombatBridgeSettings **********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridgeSettings_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
