// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAuracronDynamicRealmBridge_init() {}
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature();
	AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_AuracronDynamicRealmBridge;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge()
	{
		if (!Z_Registration_Info_UPackage__Script_AuracronDynamicRealmBridge.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/AuracronDynamicRealmBridge",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x7BB6F4D7,
				0x3CEBEAC5,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_AuracronDynamicRealmBridge.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_AuracronDynamicRealmBridge.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_AuracronDynamicRealmBridge(Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Registration_Info_UPackage__Script_AuracronDynamicRealmBridge, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x7BB6F4D7, 0x3CEBEAC5));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
