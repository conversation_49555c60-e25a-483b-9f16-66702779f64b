// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Systems/AuracronMetaHumanDNASystem.h"

#ifdef AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanDNASystem_generated_h
#error "AuracronMetaHumanDNASystem.generated.h already included, missing '#pragma once' in AuracronMetaHumanDNASystem.h"
#endif
#define AURACRONMETAHUMANFRAMEWORK_AuracronMetaHumanDNASystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UAuracronMetaHumanFramework;
struct FAuracronDNABlendShapeConfig;
struct FAuracronDNAResult;

// ********** Begin ScriptStruct FAuracronDNAResult ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_57_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAResult;
// ********** End ScriptStruct FAuracronDNAResult **************************************************

// ********** Begin ScriptStruct FAuracronDNAMetadata **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_102_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAMetadata_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAMetadata;
// ********** End ScriptStruct FAuracronDNAMetadata ************************************************

// ********** Begin ScriptStruct FAuracronDNABlendShapeConfig **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_152_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNABlendShapeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNABlendShapeConfig;
// ********** End ScriptStruct FAuracronDNABlendShapeConfig ****************************************

// ********** Begin ScriptStruct FAuracronDNAJointConfig *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_186_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDNAJointConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDNAJointConfig;
// ********** End ScriptStruct FAuracronDNAJointConfig *********************************************

// ********** Begin Delegate FAuracronDNAOperationComplete *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_214_DELEGATE \
AURACRONMETAHUMANFRAMEWORK_API void FAuracronDNAOperationComplete_DelegateWrapper(const FMulticastScriptDelegate& AuracronDNAOperationComplete, FAuracronDNAResult const& Result, const FString& OperationID);


// ********** End Delegate FAuracronDNAOperationComplete *******************************************

// ********** Begin Class UAuracronMetaHumanDNASystem **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSystemStatus); \
	DECLARE_FUNCTION(execIsInitialized); \
	DECLARE_FUNCTION(execShutdown); \
	DECLARE_FUNCTION(execInitialize); \
	DECLARE_FUNCTION(execGetVertexCount); \
	DECLARE_FUNCTION(execGetMeshName); \
	DECLARE_FUNCTION(execGetMeshCount); \
	DECLARE_FUNCTION(execGetJointParentIndex); \
	DECLARE_FUNCTION(execGetJointName); \
	DECLARE_FUNCTION(execGetJointCount); \
	DECLARE_FUNCTION(execSetNeutralJointRotations); \
	DECLARE_FUNCTION(execSetNeutralJointTranslations); \
	DECLARE_FUNCTION(execGetBlendShapeName); \
	DECLARE_FUNCTION(execGetBlendShapeCount); \
	DECLARE_FUNCTION(execGetBlendShapeTargetDeltas); \
	DECLARE_FUNCTION(execSetBlendShapeTargetDeltas); \
	DECLARE_FUNCTION(execGetAsyncOperationResult); \
	DECLARE_FUNCTION(execIsAsyncOperationComplete); \
	DECLARE_FUNCTION(execSaveDNAToFileAsync); \
	DECLARE_FUNCTION(execLoadDNAFromFileAsync); \
	DECLARE_FUNCTION(execOptimizeDNA); \
	DECLARE_FUNCTION(execValidateDNA); \
	DECLARE_FUNCTION(execSaveDNAToFile); \
	DECLARE_FUNCTION(execLoadDNAFromFile);


AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronMetaHumanDNASystem(); \
	friend struct Z_Construct_UClass_UAuracronMetaHumanDNASystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONMETAHUMANFRAMEWORK_API UClass* Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronMetaHumanDNASystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronMetaHumanFramework"), Z_Construct_UClass_UAuracronMetaHumanDNASystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronMetaHumanDNASystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronMetaHumanDNASystem(UAuracronMetaHumanDNASystem&&) = delete; \
	UAuracronMetaHumanDNASystem(const UAuracronMetaHumanDNASystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronMetaHumanDNASystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronMetaHumanDNASystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronMetaHumanDNASystem)


#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_220_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h_223_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronMetaHumanDNASystem;

// ********** End Class UAuracronMetaHumanDNASystem ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronMetaHumanFramework_Public_Systems_AuracronMetaHumanDNASystem_h

// ********** Begin Enum EAuracronDNAOperation *****************************************************
#define FOREACH_ENUM_EAURACRONDNAOPERATION(op) \
	op(EAuracronDNAOperation::Load) \
	op(EAuracronDNAOperation::Save) \
	op(EAuracronDNAOperation::Validate) \
	op(EAuracronDNAOperation::Optimize) \
	op(EAuracronDNAOperation::Merge) \
	op(EAuracronDNAOperation::Extract) \
	op(EAuracronDNAOperation::Calibrate) \
	op(EAuracronDNAOperation::Convert) 

enum class EAuracronDNAOperation : uint8;
template<> struct TIsUEnumClass<EAuracronDNAOperation> { enum { Value = true }; };
template<> AURACRONMETAHUMANFRAMEWORK_API UEnum* StaticEnum<EAuracronDNAOperation>();
// ********** End Enum EAuracronDNAOperation *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
