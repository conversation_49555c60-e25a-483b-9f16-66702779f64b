// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronRealmTransitionComponent.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronRealmTransitionComponent_generated_h
#error "AuracronRealmTransitionComponent.generated.h already included, missing '#pragma once' in AuracronRealmTransitionComponent.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronRealmTransitionComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronRealmLayer : uint8;
enum class ERealmTransitionType : uint8;
struct FAuracronTransitionEffects;
struct FAuracronTransitionPath;

// ********** Begin ScriptStruct FAuracronTransitionEffects ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_27_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTransitionEffects_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTransitionEffects;
// ********** End ScriptStruct FAuracronTransitionEffects ******************************************

// ********** Begin ScriptStruct FAuracronTransitionPath *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_74_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTransitionPath_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTransitionPath;
// ********** End ScriptStruct FAuracronTransitionPath *********************************************

// ********** Begin Class UAuracronRealmTransitionComponent ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execApplyLayerPhysics); \
	DECLARE_FUNCTION(execUpdateTransitionPhysics); \
	DECLARE_FUNCTION(execRestoreOriginalCamera); \
	DECLARE_FUNCTION(execUpdateTransitionCamera); \
	DECLARE_FUNCTION(execSetupTransitionCamera); \
	DECLARE_FUNCTION(execUpdateTransitionEffects); \
	DECLARE_FUNCTION(execStopTransitionEffects); \
	DECLARE_FUNCTION(execPlayTransitionEffects); \
	DECLARE_FUNCTION(execSetTransitionEffects); \
	DECLARE_FUNCTION(execGetTransitionPath); \
	DECLARE_FUNCTION(execSetTransitionPath); \
	DECLARE_FUNCTION(execGetTransitionTarget); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execIsTransitioning); \
	DECLARE_FUNCTION(execCancelTransition); \
	DECLARE_FUNCTION(execStartTransition);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronRealmTransitionComponent(); \
	friend struct Z_Construct_UClass_UAuracronRealmTransitionComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronRealmTransitionComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister) \
	DECLARE_SERIALIZER(UAuracronRealmTransitionComponent)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronRealmTransitionComponent(UAuracronRealmTransitionComponent&&) = delete; \
	UAuracronRealmTransitionComponent(const UAuracronRealmTransitionComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronRealmTransitionComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronRealmTransitionComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronRealmTransitionComponent) \
	NO_API virtual ~UAuracronRealmTransitionComponent();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_123_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h_126_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronRealmTransitionComponent;

// ********** End Class UAuracronRealmTransitionComponent ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronRealmTransitionComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
