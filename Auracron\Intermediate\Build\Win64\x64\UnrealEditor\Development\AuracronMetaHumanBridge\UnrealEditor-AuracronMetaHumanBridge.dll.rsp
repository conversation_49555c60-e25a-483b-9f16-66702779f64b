/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieScene/MovieScene.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/GeometryCore.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SequencerCore/SequencerCore.natvis"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/PCH.AuracronMetaHumanBridge.h.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/Module.AuracronMetaHumanBridge.gen.1.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/Module.AuracronMetaHumanBridge.gen.2.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronAnimationBlueprint.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronBehaviorReader.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronClothingGeneration.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronDNACalib.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronDNAReaderWriter.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronErrorHandling.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronEyeGeneration.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronHairGeneration.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronMeshDeformation.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronMetaHumanBridge.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronPerformanceOptimization.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronRigTransformation.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/AuracronTextureGeneration.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/PerModuleInline.gen.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronMetaHumanBridge/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimationCore/UnrealEditor-AnimationCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimGraphRuntime/UnrealEditor-AnimGraphRuntime.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieScene/UnrealEditor-MovieScene.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieSceneTracks/UnrealEditor-MovieSceneTracks.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothSysRuntimeIntrfc/UnrealEditor-ClothingSystemRuntimeInterface.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothingSystemRuntimeCommon/UnrealEditor-ClothingSystemRuntimeCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothingSystemRuntimeNv/UnrealEditor-ClothingSystemRuntimeNv.lib"
"../Plugins/ChaosCloth/Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCloth/UnrealEditor-ChaosCloth.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCore/UnrealEditor-ChaosCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Plugins/Runtime/HairStrands/Intermediate/Build/Win64/x64/UnrealEditor/Development/HairStrandsCore/UnrealEditor-HairStrandsCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageWrapper/UnrealEditor-ImageWrapper.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ImageCore/UnrealEditor-ImageCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshDescription/UnrealEditor-MeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/StaticMeshDescription/UnrealEditor-StaticMeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SkeletalMeshDescription/UnrealEditor-SkeletalMeshDescription.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshConversion/UnrealEditor-MeshConversion.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshUtilitiesCommon/UnrealEditor-MeshUtilitiesCommon.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryCore/UnrealEditor-GeometryCore.lib"
"../Plugins/Runtime/GeometryProcessing/Intermediate/Build/Win64/x64/UnrealEditor/Development/DynamicMesh/UnrealEditor-DynamicMesh.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GeometryFramework/UnrealEditor-GeometryFramework.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingComponents/UnrealEditor-ModelingComponents.lib"
"../Plugins/Runtime/MeshModelingToolset/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModelingOperators/UnrealEditor-ModelingOperators.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LiveLinkInterface/UnrealEditor-LiveLinkInterface.lib"
"../Plugins/Animation/LiveLink/Intermediate/Build/Win64/x64/UnrealEditor/Development/LiveLinkComponents/UnrealEditor-LiveLinkComponents.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LiveLinkAnimationCore/UnrealEditor-LiveLinkAnimationCore.lib"
"../Plugins/Animation/LiveLink/Intermediate/Build/Win64/x64/UnrealEditor/Development/LiveLinkMovieScene/UnrealEditor-LiveLinkMovieScene.lib"
"../Plugins/Editor/FacialAnimation/Intermediate/Build/Win64/x64/UnrealEditor/Development/FacialAnimation/UnrealEditor-FacialAnimation.lib"
"../Plugins/Runtime/AudioSynesthesia/Intermediate/Build/Win64/x64/UnrealEditor/Development/AudioSynesthesia/UnrealEditor-AudioSynesthesia.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AudioAnalyzer/UnrealEditor-AudioAnalyzer.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/TraceLog/UnrealEditor-TraceLog.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MessageLog/UnrealEditor-MessageLog.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Networking/UnrealEditor-Networking.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Sockets/UnrealEditor-Sockets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RSA/UnrealEditor-RSA.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Json/UnrealEditor-Json.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorStyle/UnrealEditor-EditorStyle.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorWidgets/UnrealEditor-EditorWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PropertyEditor/UnrealEditor-PropertyEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DetailCustomizations/UnrealEditor-DetailCustomizations.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ComponentVisualizers/UnrealEditor-ComponentVisualizers.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LevelEditor/UnrealEditor-LevelEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SceneOutliner/UnrealEditor-SceneOutliner.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GraphEditor/UnrealEditor-GraphEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorSubsystem/UnrealEditor-EditorSubsystem.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EngineSettings/UnrealEditor-EngineSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Projects/UnrealEditor-Projects.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ApplicationCore/UnrealEditor-ApplicationCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/WidgetCarousel/UnrealEditor-WidgetCarousel.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/LogVisualizer/UnrealEditor-LogVisualizer.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/OutputLog/UnrealEditor-OutputLog.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimationBlueprintLibrary/UnrealEditor-AnimationBlueprintLibrary.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimGraph/UnrealEditor-AnimGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintGraph/UnrealEditor-BlueprintGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetCompiler/UnrealEditor-KismetCompiler.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Kismet/UnrealEditor-Kismet.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetWidgets/UnrealEditor-KismetWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Persona/UnrealEditor-Persona.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SkeletalMeshEditor/UnrealEditor-SkeletalMeshEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimationBlueprintEditor/UnrealEditor-AnimationBlueprintEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AnimationEditor/UnrealEditor-AnimationEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SequencerCore/UnrealEditor-SequencerCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Sequencer/UnrealEditor-Sequencer.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MovieSceneTools/UnrealEditor-MovieSceneTools.lib"
"../Plugins/MovieScene/LevelSequenceEditor/Intermediate/Build/Win64/x64/UnrealEditor/Development/LevelSequenceEditor/UnrealEditor-LevelSequenceEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ClothingSystemEditor/UnrealEditor-ClothingSystemEditor.lib"
"../Plugins/ChaosCloth/Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosClothEditor/UnrealEditor-ChaosClothEditor.lib"
"../Plugins/Runtime/HairStrands/Intermediate/Build/Win64/x64/UnrealEditor/Development/HairStrandsEditor/UnrealEditor-HairStrandsEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MaterialEditor/UnrealEditor-MaterialEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MaterialUtilities/UnrealEditor-MaterialUtilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/TextureEditor/UnrealEditor-TextureEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperToolSettings/UnrealEditor-DeveloperToolSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/MeshUtilities/UnrealEditor-MeshUtilities.lib"
"../Plugins/Animation/LiveLink/Intermediate/Build/Win64/x64/UnrealEditor/Development/LiveLinkEditor/UnrealEditor-LiveLinkEditor.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"uiautomationcore.lib"
"DXGI.lib"
/OUT:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronMetaHumanBridge.dll"
/PDB:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronMetaHumanBridge.pdb"
/ignore:4078