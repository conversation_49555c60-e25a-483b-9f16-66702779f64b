// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RealTimeInterventionSystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "Engine/TimerHandle.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeRealTimeInterventionSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_URealTimeInterventionSystem();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_URealTimeInterventionSystem_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FActiveIntervention();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyInterventionData();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FInterventionStrategy();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EInterventionMethod *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EInterventionMethod;
static UEnum* EInterventionMethod_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EInterventionMethod.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EInterventionMethod.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EInterventionMethod"));
	}
	return Z_Registration_Info_UEnum_EInterventionMethod.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionMethod>()
{
	return EInterventionMethod_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AudioCue.DisplayName", "Audio Cue" },
		{ "AudioCue.Name", "EInterventionMethod::AudioCue" },
		{ "BlueprintType", "true" },
		{ "BreakSuggestion.DisplayName", "Break Suggestion" },
		{ "BreakSuggestion.Name", "EInterventionMethod::BreakSuggestion" },
		{ "GameplayModifier.DisplayName", "Gameplay Modifier" },
		{ "GameplayModifier.Name", "EInterventionMethod::GameplayModifier" },
		{ "InGameMessage.DisplayName", "In-Game Message" },
		{ "InGameMessage.Name", "EInterventionMethod::InGameMessage" },
		{ "MentorAssignment.DisplayName", "Mentor Assignment" },
		{ "MentorAssignment.Name", "EInterventionMethod::MentorAssignment" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
		{ "PeerConnection.DisplayName", "Peer Connection" },
		{ "PeerConnection.Name", "EInterventionMethod::PeerConnection" },
		{ "UINotification.DisplayName", "UI Notification" },
		{ "UINotification.Name", "EInterventionMethod::UINotification" },
		{ "VisualEffect.DisplayName", "Visual Effect" },
		{ "VisualEffect.Name", "EInterventionMethod::VisualEffect" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EInterventionMethod::InGameMessage", (int64)EInterventionMethod::InGameMessage },
		{ "EInterventionMethod::UINotification", (int64)EInterventionMethod::UINotification },
		{ "EInterventionMethod::AudioCue", (int64)EInterventionMethod::AudioCue },
		{ "EInterventionMethod::VisualEffect", (int64)EInterventionMethod::VisualEffect },
		{ "EInterventionMethod::GameplayModifier", (int64)EInterventionMethod::GameplayModifier },
		{ "EInterventionMethod::PeerConnection", (int64)EInterventionMethod::PeerConnection },
		{ "EInterventionMethod::MentorAssignment", (int64)EInterventionMethod::MentorAssignment },
		{ "EInterventionMethod::BreakSuggestion", (int64)EInterventionMethod::BreakSuggestion },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EInterventionMethod",
	"EInterventionMethod",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod()
{
	if (!Z_Registration_Info_UEnum_EInterventionMethod.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EInterventionMethod.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EInterventionMethod.InnerSingleton;
}
// ********** End Enum EInterventionMethod *********************************************************

// ********** Begin Enum EInterventionResult *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EInterventionResult;
static UEnum* EInterventionResult_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EInterventionResult.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EInterventionResult.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EInterventionResult"));
	}
	return Z_Registration_Info_UEnum_EInterventionResult.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionResult>()
{
	return EInterventionResult_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Accepted.DisplayName", "Accepted" },
		{ "Accepted.Name", "EInterventionResult::Accepted" },
		{ "BlueprintType", "true" },
		{ "Declined.DisplayName", "Declined" },
		{ "Declined.Name", "EInterventionResult::Declined" },
		{ "Escalated.DisplayName", "Escalated" },
		{ "Escalated.Name", "EInterventionResult::Escalated" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EInterventionResult::Failed" },
		{ "Ignored.DisplayName", "Ignored" },
		{ "Ignored.Name", "EInterventionResult::Ignored" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
		{ "Pending.DisplayName", "Pending" },
		{ "Pending.Name", "EInterventionResult::Pending" },
		{ "Successful.DisplayName", "Successful" },
		{ "Successful.Name", "EInterventionResult::Successful" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EInterventionResult::Pending", (int64)EInterventionResult::Pending },
		{ "EInterventionResult::Accepted", (int64)EInterventionResult::Accepted },
		{ "EInterventionResult::Declined", (int64)EInterventionResult::Declined },
		{ "EInterventionResult::Ignored", (int64)EInterventionResult::Ignored },
		{ "EInterventionResult::Successful", (int64)EInterventionResult::Successful },
		{ "EInterventionResult::Failed", (int64)EInterventionResult::Failed },
		{ "EInterventionResult::Escalated", (int64)EInterventionResult::Escalated },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EInterventionResult",
	"EInterventionResult",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult()
{
	if (!Z_Registration_Info_UEnum_EInterventionResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EInterventionResult.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EInterventionResult.InnerSingleton;
}
// ********** End Enum EInterventionResult *********************************************************

// ********** Begin ScriptStruct FActiveIntervention ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FActiveIntervention;
class UScriptStruct* FActiveIntervention::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FActiveIntervention.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FActiveIntervention.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FActiveIntervention, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("ActiveIntervention"));
	}
	return Z_Registration_Info_UScriptStruct_FActiveIntervention.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FActiveIntervention_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionType_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Method_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionMessage_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResponseTime_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectivenessScore_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresFollowUp_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionTags_MetaData[] = {
		{ "Category", "ActiveIntervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Method_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Method;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Result_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Result;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionMessage;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResponseTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectivenessScore;
	static void NewProp_bRequiresFollowUp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresFollowUp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FActiveIntervention>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionType = { "InterventionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, InterventionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionType_MetaData), NewProp_InterventionType_MetaData) }; // 864251872
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Method_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Method = { "Method", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, Method), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Method_MetaData), NewProp_Method_MetaData) }; // 1459886174
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Result_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, Result), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 2085991710
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionMessage = { "InterventionMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, InterventionMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionMessage_MetaData), NewProp_InterventionMessage_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, StartTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_ResponseTime = { "ResponseTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, ResponseTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResponseTime_MetaData), NewProp_ResponseTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_EffectivenessScore = { "EffectivenessScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, EffectivenessScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectivenessScore_MetaData), NewProp_EffectivenessScore_MetaData) };
void Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_bRequiresFollowUp_SetBit(void* Obj)
{
	((FActiveIntervention*)Obj)->bRequiresFollowUp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_bRequiresFollowUp = { "bRequiresFollowUp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FActiveIntervention), &Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_bRequiresFollowUp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresFollowUp_MetaData), NewProp_bRequiresFollowUp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionTags = { "InterventionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActiveIntervention, InterventionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionTags_MetaData), NewProp_InterventionTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FActiveIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Method_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Method,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Result_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_ResponseTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_EffectivenessScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_bRequiresFollowUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewProp_InterventionTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActiveIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FActiveIntervention_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"ActiveIntervention",
	Z_Construct_UScriptStruct_FActiveIntervention_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActiveIntervention_Statics::PropPointers),
	sizeof(FActiveIntervention),
	alignof(FActiveIntervention),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActiveIntervention_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FActiveIntervention_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FActiveIntervention()
{
	if (!Z_Registration_Info_UScriptStruct_FActiveIntervention.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FActiveIntervention.InnerSingleton, Z_Construct_UScriptStruct_FActiveIntervention_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FActiveIntervention.InnerSingleton;
}
// ********** End ScriptStruct FActiveIntervention *************************************************

// ********** Begin ScriptStruct FInterventionStrategy *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FInterventionStrategy;
class UScriptStruct* FInterventionStrategy::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FInterventionStrategy.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FInterventionStrategy.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FInterventionStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("InterventionStrategy"));
	}
	return Z_Registration_Info_UScriptStruct_FInterventionStrategy.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FInterventionStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggerType_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredMethods_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessRate_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownTime_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAttemptsPerSession_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategyTags_MetaData[] = {
		{ "Category", "InterventionStrategy" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TriggerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TriggerType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreferredMethods_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreferredMethods_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PreferredMethods;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SuccessRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAttemptsPerSession;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StrategyTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FInterventionStrategy>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_TriggerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_TriggerType = { "TriggerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, TriggerType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggerType_MetaData), NewProp_TriggerType_MetaData) }; // 864251872
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods_Inner = { "PreferredMethods", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod, METADATA_PARAMS(0, nullptr) }; // 1459886174
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods = { "PreferredMethods", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, PreferredMethods), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredMethods_MetaData), NewProp_PreferredMethods_MetaData) }; // 1459886174
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_SuccessRate = { "SuccessRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, SuccessRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessRate_MetaData), NewProp_SuccessRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_CooldownTime = { "CooldownTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, CooldownTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownTime_MetaData), NewProp_CooldownTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_MaxAttemptsPerSession = { "MaxAttemptsPerSession", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, MaxAttemptsPerSession), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAttemptsPerSession_MetaData), NewProp_MaxAttemptsPerSession_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_StrategyTags = { "StrategyTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInterventionStrategy, StrategyTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategyTags_MetaData), NewProp_StrategyTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FInterventionStrategy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_TriggerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_TriggerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_PreferredMethods,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_SuccessRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_CooldownTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_MaxAttemptsPerSession,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewProp_StrategyTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInterventionStrategy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FInterventionStrategy_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"InterventionStrategy",
	Z_Construct_UScriptStruct_FInterventionStrategy_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInterventionStrategy_Statics::PropPointers),
	sizeof(FInterventionStrategy),
	alignof(FInterventionStrategy),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInterventionStrategy_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FInterventionStrategy_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FInterventionStrategy()
{
	if (!Z_Registration_Info_UScriptStruct_FInterventionStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FInterventionStrategy.InnerSingleton, Z_Construct_UScriptStruct_FInterventionStrategy_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FInterventionStrategy.InnerSingleton;
}
// ********** End ScriptStruct FInterventionStrategy ***********************************************

// ********** Begin Class URealTimeInterventionSystem Function CancelIntervention ******************
struct Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics
{
	struct RealTimeInterventionSystem_eventCancelIntervention_Parms
	{
		FString InterventionID;
		FString Reason;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventCancelIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventCancelIntervention_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "CancelIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::RealTimeInterventionSystem_eventCancelIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::RealTimeInterventionSystem_eventCancelIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execCancelIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_Reason);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelIntervention(Z_Param_InterventionID,Z_Param_Reason);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function CancelIntervention ********************

// ********** Begin Class URealTimeInterventionSystem Function CompleteIntervention ****************
struct Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics
{
	struct RealTimeInterventionSystem_eventCompleteIntervention_Parms
	{
		FString InterventionID;
		float EffectivenessScore;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectivenessScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventCompleteIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::NewProp_EffectivenessScore = { "EffectivenessScore", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventCompleteIntervention_Parms, EffectivenessScore), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::NewProp_EffectivenessScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "CompleteIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::RealTimeInterventionSystem_eventCompleteIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::RealTimeInterventionSystem_eventCompleteIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execCompleteIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EffectivenessScore);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteIntervention(Z_Param_InterventionID,Z_Param_EffectivenessScore);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function CompleteIntervention ******************

// ********** Begin Class URealTimeInterventionSystem Function EscalateIntervention ****************
struct Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics
{
	struct RealTimeInterventionSystem_eventEscalateIntervention_Parms
	{
		FString InterventionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventEscalateIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::NewProp_InterventionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "EscalateIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::RealTimeInterventionSystem_eventEscalateIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::RealTimeInterventionSystem_eventEscalateIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execEscalateIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EscalateIntervention(Z_Param_InterventionID);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function EscalateIntervention ******************

// ********** Begin Class URealTimeInterventionSystem Function ExecuteFollowUpIntervention *********
struct Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics
{
	struct RealTimeInterventionSystem_eventExecuteFollowUpIntervention_Parms
	{
		FString OriginalInterventionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalInterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_OriginalInterventionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::NewProp_OriginalInterventionID = { "OriginalInterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventExecuteFollowUpIntervention_Parms, OriginalInterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalInterventionID_MetaData), NewProp_OriginalInterventionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::NewProp_OriginalInterventionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "ExecuteFollowUpIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::RealTimeInterventionSystem_eventExecuteFollowUpIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::RealTimeInterventionSystem_eventExecuteFollowUpIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execExecuteFollowUpIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_OriginalInterventionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteFollowUpIntervention(Z_Param_OriginalInterventionID);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function ExecuteFollowUpIntervention ***********

// ********** Begin Class URealTimeInterventionSystem Function ExecuteIntervention *****************
struct Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics
{
	struct RealTimeInterventionSystem_eventExecuteIntervention_Parms
	{
		FString PlayerID;
		FHarmonyInterventionData InterventionData;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core Intervention Functions\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core Intervention Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventExecuteIntervention_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_InterventionData = { "InterventionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventExecuteIntervention_Parms, InterventionData), Z_Construct_UScriptStruct_FHarmonyInterventionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionData_MetaData), NewProp_InterventionData_MetaData) }; // 1406652677
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventExecuteIntervention_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_InterventionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "ExecuteIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::RealTimeInterventionSystem_eventExecuteIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::RealTimeInterventionSystem_eventExecuteIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execExecuteIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FHarmonyInterventionData,Z_Param_Out_InterventionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->ExecuteIntervention(Z_Param_PlayerID,Z_Param_Out_InterventionData);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function ExecuteIntervention *******************

// ********** Begin Class URealTimeInterventionSystem Function GetActiveInterventions **************
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics
{
	struct RealTimeInterventionSystem_eventGetActiveInterventions_Parms
	{
		TArray<FActiveIntervention> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervention Management\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervention Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FActiveIntervention, METADATA_PARAMS(0, nullptr) }; // 1784746687
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetActiveInterventions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1784746687
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetActiveInterventions", Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::RealTimeInterventionSystem_eventGetActiveInterventions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::RealTimeInterventionSystem_eventGetActiveInterventions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetActiveInterventions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FActiveIntervention>*)Z_Param__Result=P_THIS->GetActiveInterventions();
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetActiveInterventions ****************

// ********** Begin Class URealTimeInterventionSystem Function GetIntervention *********************
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics
{
	struct RealTimeInterventionSystem_eventGetIntervention_Parms
	{
		FString InterventionID;
		FActiveIntervention ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetIntervention_Parms, ReturnValue), Z_Construct_UScriptStruct_FActiveIntervention, METADATA_PARAMS(0, nullptr) }; // 1784746687
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::RealTimeInterventionSystem_eventGetIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::RealTimeInterventionSystem_eventGetIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FActiveIntervention*)Z_Param__Result=P_THIS->GetIntervention(Z_Param_InterventionID);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetIntervention ***********************

// ********** Begin Class URealTimeInterventionSystem Function GetInterventionTypeStatistics *******
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics
{
	struct RealTimeInterventionSystem_eventGetInterventionTypeStatistics_Parms
	{
		TMap<EInterventionType,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetInterventionTypeStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetInterventionTypeStatistics", Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::RealTimeInterventionSystem_eventGetInterventionTypeStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::RealTimeInterventionSystem_eventGetInterventionTypeStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetInterventionTypeStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<EInterventionType,int32>*)Z_Param__Result=P_THIS->GetInterventionTypeStatistics();
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetInterventionTypeStatistics *********

// ********** Begin Class URealTimeInterventionSystem Function GetOverallInterventionSuccessRate ***
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics
{
	struct RealTimeInterventionSystem_eventGetOverallInterventionSuccessRate_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Analytics\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetOverallInterventionSuccessRate_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetOverallInterventionSuccessRate", Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::RealTimeInterventionSystem_eventGetOverallInterventionSuccessRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::RealTimeInterventionSystem_eventGetOverallInterventionSuccessRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetOverallInterventionSuccessRate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetOverallInterventionSuccessRate();
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetOverallInterventionSuccessRate *****

// ********** Begin Class URealTimeInterventionSystem Function GetStrategyEffectiveness ************
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics
{
	struct RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms
	{
		EInterventionType InterventionType;
		EInterventionMethod Method;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Method_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Method;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_InterventionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_InterventionType = { "InterventionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms, InterventionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_Method_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_Method = { "Method", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms, Method), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod, METADATA_PARAMS(0, nullptr) }; // 1459886174
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_InterventionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_InterventionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_Method_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_Method,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetStrategyEffectiveness", Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::RealTimeInterventionSystem_eventGetStrategyEffectiveness_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetStrategyEffectiveness)
{
	P_GET_ENUM(EInterventionType,Z_Param_InterventionType);
	P_GET_ENUM(EInterventionMethod,Z_Param_Method);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStrategyEffectiveness(EInterventionType(Z_Param_InterventionType),EInterventionMethod(Z_Param_Method));
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetStrategyEffectiveness **************

// ********** Begin Class URealTimeInterventionSystem Function GetTotalInterventionsToday **********
struct Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics
{
	struct RealTimeInterventionSystem_eventGetTotalInterventionsToday_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventGetTotalInterventionsToday_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "GetTotalInterventionsToday", Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::RealTimeInterventionSystem_eventGetTotalInterventionsToday_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::RealTimeInterventionSystem_eventGetTotalInterventionsToday_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execGetTotalInterventionsToday)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalInterventionsToday();
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function GetTotalInterventionsToday ************

// ********** Begin Class URealTimeInterventionSystem Function IsPlayerUnderIntervention ***********
struct Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics
{
	struct RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms
	{
		FString PlayerID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
void Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms), &Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "IsPlayerUnderIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::RealTimeInterventionSystem_eventIsPlayerUnderIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execIsPlayerUnderIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerUnderIntervention(Z_Param_PlayerID);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function IsPlayerUnderIntervention *************

// ********** Begin Class URealTimeInterventionSystem Function RegisterInterventionStrategy ********
struct Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics
{
	struct RealTimeInterventionSystem_eventRegisterInterventionStrategy_Parms
	{
		FInterventionStrategy Strategy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Strategy Management\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Strategy Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Strategy_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Strategy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::NewProp_Strategy = { "Strategy", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventRegisterInterventionStrategy_Parms, Strategy), Z_Construct_UScriptStruct_FInterventionStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Strategy_MetaData), NewProp_Strategy_MetaData) }; // 3753248025
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::NewProp_Strategy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "RegisterInterventionStrategy", Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::RealTimeInterventionSystem_eventRegisterInterventionStrategy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::RealTimeInterventionSystem_eventRegisterInterventionStrategy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execRegisterInterventionStrategy)
{
	P_GET_STRUCT_REF(FInterventionStrategy,Z_Param_Out_Strategy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterInterventionStrategy(Z_Param_Out_Strategy);
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function RegisterInterventionStrategy **********

// ********** Begin Class URealTimeInterventionSystem Function RespondToIntervention ***************
struct Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics
{
	struct RealTimeInterventionSystem_eventRespondToIntervention_Parms
	{
		FString InterventionID;
		EInterventionResult Response;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Response_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Response;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_InterventionID = { "InterventionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventRespondToIntervention_Parms, InterventionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionID_MetaData), NewProp_InterventionID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_Response_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_Response = { "Response", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventRespondToIntervention_Parms, Response), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionResult, METADATA_PARAMS(0, nullptr) }; // 2085991710
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_InterventionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_Response_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::NewProp_Response,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "RespondToIntervention", Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::RealTimeInterventionSystem_eventRespondToIntervention_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::RealTimeInterventionSystem_eventRespondToIntervention_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execRespondToIntervention)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InterventionID);
	P_GET_ENUM(EInterventionResult,Z_Param_Response);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RespondToIntervention(Z_Param_InterventionID,EInterventionResult(Z_Param_Response));
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function RespondToIntervention *****************

// ********** Begin Class URealTimeInterventionSystem Function SelectOptimalMethod *****************
struct Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics
{
	struct RealTimeInterventionSystem_eventSelectOptimalMethod_Parms
	{
		FString PlayerID;
		EInterventionType InterventionType;
		EInterventionMethod ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Real-Time Intervention" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventSelectOptimalMethod_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_InterventionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_InterventionType = { "InterventionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventSelectOptimalMethod_Parms, InterventionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RealTimeInterventionSystem_eventSelectOptimalMethod_Parms, ReturnValue), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionMethod, METADATA_PARAMS(0, nullptr) }; // 1459886174
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_InterventionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_InterventionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_URealTimeInterventionSystem, nullptr, "SelectOptimalMethod", Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::PropPointers), sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::RealTimeInterventionSystem_eventSelectOptimalMethod_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::Function_MetaDataParams), Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::RealTimeInterventionSystem_eventSelectOptimalMethod_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URealTimeInterventionSystem::execSelectOptimalMethod)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_ENUM(EInterventionType,Z_Param_InterventionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EInterventionMethod*)Z_Param__Result=P_THIS->SelectOptimalMethod(Z_Param_PlayerID,EInterventionType(Z_Param_InterventionType));
	P_NATIVE_END;
}
// ********** End Class URealTimeInterventionSystem Function SelectOptimalMethod *******************

// ********** Begin Class URealTimeInterventionSystem **********************************************
void URealTimeInterventionSystem::StaticRegisterNativesURealTimeInterventionSystem()
{
	UClass* Class = URealTimeInterventionSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelIntervention", &URealTimeInterventionSystem::execCancelIntervention },
		{ "CompleteIntervention", &URealTimeInterventionSystem::execCompleteIntervention },
		{ "EscalateIntervention", &URealTimeInterventionSystem::execEscalateIntervention },
		{ "ExecuteFollowUpIntervention", &URealTimeInterventionSystem::execExecuteFollowUpIntervention },
		{ "ExecuteIntervention", &URealTimeInterventionSystem::execExecuteIntervention },
		{ "GetActiveInterventions", &URealTimeInterventionSystem::execGetActiveInterventions },
		{ "GetIntervention", &URealTimeInterventionSystem::execGetIntervention },
		{ "GetInterventionTypeStatistics", &URealTimeInterventionSystem::execGetInterventionTypeStatistics },
		{ "GetOverallInterventionSuccessRate", &URealTimeInterventionSystem::execGetOverallInterventionSuccessRate },
		{ "GetStrategyEffectiveness", &URealTimeInterventionSystem::execGetStrategyEffectiveness },
		{ "GetTotalInterventionsToday", &URealTimeInterventionSystem::execGetTotalInterventionsToday },
		{ "IsPlayerUnderIntervention", &URealTimeInterventionSystem::execIsPlayerUnderIntervention },
		{ "RegisterInterventionStrategy", &URealTimeInterventionSystem::execRegisterInterventionStrategy },
		{ "RespondToIntervention", &URealTimeInterventionSystem::execRespondToIntervention },
		{ "SelectOptimalMethod", &URealTimeInterventionSystem::execSelectOptimalMethod },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_URealTimeInterventionSystem;
UClass* URealTimeInterventionSystem::GetPrivateStaticClass()
{
	using TClass = URealTimeInterventionSystem;
	if (!Z_Registration_Info_UClass_URealTimeInterventionSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("RealTimeInterventionSystem"),
			Z_Registration_Info_UClass_URealTimeInterventionSystem.InnerSingleton,
			StaticRegisterNativesURealTimeInterventionSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_URealTimeInterventionSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_URealTimeInterventionSystem_NoRegister()
{
	return URealTimeInterventionSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_URealTimeInterventionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Real-Time Intervention System\n * Provides immediate, contextual interventions to prevent toxicity and promote positive behavior\n */" },
#endif
		{ "IncludePath", "RealTimeInterventionSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Real-Time Intervention System\nProvides immediate, contextual interventions to prevent toxicity and promote positive behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentInterventions_MetaData[] = {
		{ "Category", "Intervention Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionTimeout_MetaData[] = {
		{ "Category", "Intervention Config" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EscalationThreshold_MetaData[] = {
		{ "Category", "Intervention Config" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticEscalation_MetaData[] = {
		{ "Category", "Intervention Config" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableInterventionLearning_MetaData[] = {
		{ "Category", "Intervention Config" },
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveInterventions_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data Storage\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedInterventions_MetaData[] = {
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionStrategies_MetaData[] = {
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerInterventionCounts_MetaData[] = {
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Timers\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSpecialEventActive_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Special Event State\n" },
#endif
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special Event State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEventMultiplier_MetaData[] = {
		{ "ModuleRelativePath", "Public/RealTimeInterventionSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentInterventions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InterventionTimeout;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EscalationThreshold;
	static void NewProp_bEnableAutomaticEscalation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticEscalation;
	static void NewProp_bEnableInterventionLearning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInterventionLearning;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveInterventions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveInterventions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveInterventions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedInterventions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedInterventions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionStrategies_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionStrategies_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionStrategies_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_InterventionStrategies;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerInterventionCounts_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerInterventionCounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerInterventionCounts;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionTimers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_InterventionTimers;
	static void NewProp_bSpecialEventActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSpecialEventActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentEventMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_CancelIntervention, "CancelIntervention" }, // 591401786
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_CompleteIntervention, "CompleteIntervention" }, // 3568043790
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_EscalateIntervention, "EscalateIntervention" }, // 176539879
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteFollowUpIntervention, "ExecuteFollowUpIntervention" }, // 1830270490
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_ExecuteIntervention, "ExecuteIntervention" }, // 3589602461
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetActiveInterventions, "GetActiveInterventions" }, // 3275261143
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetIntervention, "GetIntervention" }, // 3748924007
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetInterventionTypeStatistics, "GetInterventionTypeStatistics" }, // 2558137708
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetOverallInterventionSuccessRate, "GetOverallInterventionSuccessRate" }, // 1569617014
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetStrategyEffectiveness, "GetStrategyEffectiveness" }, // 664060469
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_GetTotalInterventionsToday, "GetTotalInterventionsToday" }, // 2172710259
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_IsPlayerUnderIntervention, "IsPlayerUnderIntervention" }, // 763693158
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_RegisterInterventionStrategy, "RegisterInterventionStrategy" }, // 3911917940
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_RespondToIntervention, "RespondToIntervention" }, // 2033817572
		{ &Z_Construct_UFunction_URealTimeInterventionSystem_SelectOptimalMethod, "SelectOptimalMethod" }, // 812664575
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URealTimeInterventionSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_MaxConcurrentInterventions = { "MaxConcurrentInterventions", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, MaxConcurrentInterventions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentInterventions_MetaData), NewProp_MaxConcurrentInterventions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimeout = { "InterventionTimeout", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, InterventionTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionTimeout_MetaData), NewProp_InterventionTimeout_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_EscalationThreshold = { "EscalationThreshold", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, EscalationThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EscalationThreshold_MetaData), NewProp_EscalationThreshold_MetaData) };
void Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableAutomaticEscalation_SetBit(void* Obj)
{
	((URealTimeInterventionSystem*)Obj)->bEnableAutomaticEscalation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableAutomaticEscalation = { "bEnableAutomaticEscalation", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URealTimeInterventionSystem), &Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableAutomaticEscalation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticEscalation_MetaData), NewProp_bEnableAutomaticEscalation_MetaData) };
void Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableInterventionLearning_SetBit(void* Obj)
{
	((URealTimeInterventionSystem*)Obj)->bEnableInterventionLearning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableInterventionLearning = { "bEnableInterventionLearning", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URealTimeInterventionSystem), &Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableInterventionLearning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableInterventionLearning_MetaData), NewProp_bEnableInterventionLearning_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions_ValueProp = { "ActiveInterventions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveIntervention, METADATA_PARAMS(0, nullptr) }; // 1784746687
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions_Key_KeyProp = { "ActiveInterventions_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions = { "ActiveInterventions", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, ActiveInterventions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveInterventions_MetaData), NewProp_ActiveInterventions_MetaData) }; // 1784746687
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CompletedInterventions_Inner = { "CompletedInterventions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FActiveIntervention, METADATA_PARAMS(0, nullptr) }; // 1784746687
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CompletedInterventions = { "CompletedInterventions", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, CompletedInterventions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedInterventions_MetaData), NewProp_CompletedInterventions_MetaData) }; // 1784746687
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_ValueProp = { "InterventionStrategies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FInterventionStrategy, METADATA_PARAMS(0, nullptr) }; // 3753248025
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_Key_KeyProp = { "InterventionStrategies_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(0, nullptr) }; // 864251872
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies = { "InterventionStrategies", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, InterventionStrategies), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionStrategies_MetaData), NewProp_InterventionStrategies_MetaData) }; // 864251872 3753248025
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts_ValueProp = { "PlayerInterventionCounts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts_Key_KeyProp = { "PlayerInterventionCounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts = { "PlayerInterventionCounts", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, PlayerInterventionCounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerInterventionCounts_MetaData), NewProp_PlayerInterventionCounts_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers_ValueProp = { "InterventionTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers_Key_KeyProp = { "InterventionTimers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers = { "InterventionTimers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, InterventionTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionTimers_MetaData), NewProp_InterventionTimers_MetaData) }; // 3834150579
void Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bSpecialEventActive_SetBit(void* Obj)
{
	((URealTimeInterventionSystem*)Obj)->bSpecialEventActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bSpecialEventActive = { "bSpecialEventActive", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URealTimeInterventionSystem), &Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bSpecialEventActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSpecialEventActive_MetaData), NewProp_bSpecialEventActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CurrentEventMultiplier = { "CurrentEventMultiplier", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URealTimeInterventionSystem, CurrentEventMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEventMultiplier_MetaData), NewProp_CurrentEventMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URealTimeInterventionSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_MaxConcurrentInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_EscalationThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableAutomaticEscalation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bEnableInterventionLearning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_ActiveInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CompletedInterventions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CompletedInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionStrategies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_PlayerInterventionCounts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_InterventionTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_bSpecialEventActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URealTimeInterventionSystem_Statics::NewProp_CurrentEventMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URealTimeInterventionSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URealTimeInterventionSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URealTimeInterventionSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URealTimeInterventionSystem_Statics::ClassParams = {
	&URealTimeInterventionSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URealTimeInterventionSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URealTimeInterventionSystem_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URealTimeInterventionSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_URealTimeInterventionSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URealTimeInterventionSystem()
{
	if (!Z_Registration_Info_UClass_URealTimeInterventionSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URealTimeInterventionSystem.OuterSingleton, Z_Construct_UClass_URealTimeInterventionSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URealTimeInterventionSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URealTimeInterventionSystem);
URealTimeInterventionSystem::~URealTimeInterventionSystem() {}
// ********** End Class URealTimeInterventionSystem ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EInterventionMethod_StaticEnum, TEXT("EInterventionMethod"), &Z_Registration_Info_UEnum_EInterventionMethod, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1459886174U) },
		{ EInterventionResult_StaticEnum, TEXT("EInterventionResult"), &Z_Registration_Info_UEnum_EInterventionResult, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2085991710U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FActiveIntervention::StaticStruct, Z_Construct_UScriptStruct_FActiveIntervention_Statics::NewStructOps, TEXT("ActiveIntervention"), &Z_Registration_Info_UScriptStruct_FActiveIntervention, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FActiveIntervention), 1784746687U) },
		{ FInterventionStrategy::StaticStruct, Z_Construct_UScriptStruct_FInterventionStrategy_Statics::NewStructOps, TEXT("InterventionStrategy"), &Z_Registration_Info_UScriptStruct_FInterventionStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FInterventionStrategy), 3753248025U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URealTimeInterventionSystem, URealTimeInterventionSystem::StaticClass, TEXT("URealTimeInterventionSystem"), &Z_Registration_Info_UClass_URealTimeInterventionSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URealTimeInterventionSystem), 2923023595U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_2464179654(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_RealTimeInterventionSystem_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
