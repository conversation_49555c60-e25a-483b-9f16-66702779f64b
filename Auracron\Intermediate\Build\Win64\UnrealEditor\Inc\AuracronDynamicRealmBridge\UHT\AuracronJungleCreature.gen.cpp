// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronJungleCreature.h"
#include "AuracronAdaptiveCreaturesBridge/Public/AuracronAdaptiveCreaturesBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronJungleCreature() {}

// ********** Begin Cross Module References ********************************************************
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTree_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UBlackboardData_NoRegister();
AURACRONADAPTIVECREATURESBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCreatureAdaptation();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronJungleCreature();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronJungleCreature_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemComponent_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAbilitySystemInterface_NoRegister();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UAttributeSet_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EJungleCreatureAdaptationType *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EJungleCreatureAdaptationType;
static UEnum* EJungleCreatureAdaptationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EJungleCreatureAdaptationType"));
	}
	return Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleCreatureAdaptationType>()
{
	return EJungleCreatureAdaptationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EJungleCreatureAdaptationType::Aggressive" },
		{ "Behavioral.DisplayName", "Behavioral" },
		{ "Behavioral.Name", "EJungleCreatureAdaptationType::Behavioral" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Jungle creature adaptation types\n */" },
#endif
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "EJungleCreatureAdaptationType::Defensive" },
		{ "Environmental.DisplayName", "Environmental" },
		{ "Environmental.Name", "EJungleCreatureAdaptationType::Environmental" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EJungleCreatureAdaptationType::None" },
		{ "Physical.DisplayName", "Physical" },
		{ "Physical.Name", "EJungleCreatureAdaptationType::Physical" },
		{ "Tactical.DisplayName", "Tactical" },
		{ "Tactical.Name", "EJungleCreatureAdaptationType::Tactical" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle creature adaptation types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EJungleCreatureAdaptationType::None", (int64)EJungleCreatureAdaptationType::None },
		{ "EJungleCreatureAdaptationType::Behavioral", (int64)EJungleCreatureAdaptationType::Behavioral },
		{ "EJungleCreatureAdaptationType::Physical", (int64)EJungleCreatureAdaptationType::Physical },
		{ "EJungleCreatureAdaptationType::Tactical", (int64)EJungleCreatureAdaptationType::Tactical },
		{ "EJungleCreatureAdaptationType::Environmental", (int64)EJungleCreatureAdaptationType::Environmental },
		{ "EJungleCreatureAdaptationType::Aggressive", (int64)EJungleCreatureAdaptationType::Aggressive },
		{ "EJungleCreatureAdaptationType::Defensive", (int64)EJungleCreatureAdaptationType::Defensive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EJungleCreatureAdaptationType",
	"EJungleCreatureAdaptationType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType()
{
	if (!Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EJungleCreatureAdaptationType.InnerSingleton;
}
// ********** End Enum EJungleCreatureAdaptationType ***********************************************

// ********** Begin Enum EJungleCreatureType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EJungleCreatureType;
static UEnum* EJungleCreatureType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EJungleCreatureType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EJungleCreatureType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EJungleCreatureType"));
	}
	return Z_Registration_Info_UEnum_EJungleCreatureType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleCreatureType>()
{
	return EJungleCreatureType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Jungle creature types\n */" },
#endif
		{ "Elemental.DisplayName", "Elemental" },
		{ "Elemental.Name", "EJungleCreatureType::Elemental" },
		{ "Guardian.DisplayName", "Guardian" },
		{ "Guardian.Name", "EJungleCreatureType::Guardian" },
		{ "Herbivore.DisplayName", "Herbivore" },
		{ "Herbivore.Name", "EJungleCreatureType::Herbivore" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EJungleCreatureType::Hybrid" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
		{ "Predator.DisplayName", "Predator" },
		{ "Predator.Name", "EJungleCreatureType::Predator" },
		{ "Scavenger.DisplayName", "Scavenger" },
		{ "Scavenger.Name", "EJungleCreatureType::Scavenger" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle creature types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EJungleCreatureType::Predator", (int64)EJungleCreatureType::Predator },
		{ "EJungleCreatureType::Herbivore", (int64)EJungleCreatureType::Herbivore },
		{ "EJungleCreatureType::Scavenger", (int64)EJungleCreatureType::Scavenger },
		{ "EJungleCreatureType::Guardian", (int64)EJungleCreatureType::Guardian },
		{ "EJungleCreatureType::Elemental", (int64)EJungleCreatureType::Elemental },
		{ "EJungleCreatureType::Hybrid", (int64)EJungleCreatureType::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EJungleCreatureType",
	"EJungleCreatureType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType()
{
	if (!Z_Registration_Info_UEnum_EJungleCreatureType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EJungleCreatureType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EJungleCreatureType.InnerSingleton;
}
// ********** End Enum EJungleCreatureType *********************************************************

// ********** Begin Enum ECreatureBehaviorState ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECreatureBehaviorState;
static UEnum* ECreatureBehaviorState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECreatureBehaviorState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECreatureBehaviorState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ECreatureBehaviorState"));
	}
	return Z_Registration_Info_UEnum_ECreatureBehaviorState.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ECreatureBehaviorState>()
{
	return ECreatureBehaviorState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adapted.DisplayName", "Adapted" },
		{ "Adapted.Name", "ECreatureBehaviorState::Adapted" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Creature behavior states\n */" },
#endif
		{ "Fleeing.DisplayName", "Fleeing" },
		{ "Fleeing.Name", "ECreatureBehaviorState::Fleeing" },
		{ "Hunting.DisplayName", "Hunting" },
		{ "Hunting.Name", "ECreatureBehaviorState::Hunting" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "ECreatureBehaviorState::Idle" },
		{ "Investigating.DisplayName", "Investigating" },
		{ "Investigating.Name", "ECreatureBehaviorState::Investigating" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
		{ "Patrolling.DisplayName", "Patrolling" },
		{ "Patrolling.Name", "ECreatureBehaviorState::Patrolling" },
		{ "Social.DisplayName", "Social" },
		{ "Social.Name", "ECreatureBehaviorState::Social" },
		{ "Territorial.DisplayName", "Territorial" },
		{ "Territorial.Name", "ECreatureBehaviorState::Territorial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creature behavior states" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECreatureBehaviorState::Idle", (int64)ECreatureBehaviorState::Idle },
		{ "ECreatureBehaviorState::Patrolling", (int64)ECreatureBehaviorState::Patrolling },
		{ "ECreatureBehaviorState::Hunting", (int64)ECreatureBehaviorState::Hunting },
		{ "ECreatureBehaviorState::Fleeing", (int64)ECreatureBehaviorState::Fleeing },
		{ "ECreatureBehaviorState::Investigating", (int64)ECreatureBehaviorState::Investigating },
		{ "ECreatureBehaviorState::Territorial", (int64)ECreatureBehaviorState::Territorial },
		{ "ECreatureBehaviorState::Social", (int64)ECreatureBehaviorState::Social },
		{ "ECreatureBehaviorState::Adapted", (int64)ECreatureBehaviorState::Adapted },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ECreatureBehaviorState",
	"ECreatureBehaviorState",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState()
{
	if (!Z_Registration_Info_UEnum_ECreatureBehaviorState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECreatureBehaviorState.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECreatureBehaviorState.InnerSingleton;
}
// ********** End Enum ECreatureBehaviorState ******************************************************

// ********** Begin ScriptStruct FAuracronCreatureAdaptationStatus *********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus;
class UScriptStruct* FAuracronCreatureAdaptationStatus::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronCreatureAdaptationStatus"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Creature adaptation status\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creature adaptation status" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAdapted_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether creature is currently adapted */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether creature is currently adapted" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAdaptationType_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current adaptation type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current adaptation type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationStrength_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Adaptation strength */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adaptation strength" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationStartTime_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time when adaptation was applied */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time when adaptation was applied" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationDuration_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Duration of current adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Duration of current adaptation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPlayer_MetaData[] = {
		{ "Category", "Adaptation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target player for adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target player for adaptation" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsAdapted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAdapted;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentAdaptationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentAdaptationType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronCreatureAdaptationStatus>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_bIsAdapted_SetBit(void* Obj)
{
	((FAuracronCreatureAdaptationStatus*)Obj)->bIsAdapted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_bIsAdapted = { "bIsAdapted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronCreatureAdaptationStatus), &Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_bIsAdapted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAdapted_MetaData), NewProp_bIsAdapted_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_CurrentAdaptationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_CurrentAdaptationType = { "CurrentAdaptationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCreatureAdaptationStatus, CurrentAdaptationType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAdaptationType_MetaData), NewProp_CurrentAdaptationType_MetaData) }; // 3773843215
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationStrength = { "AdaptationStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCreatureAdaptationStatus, AdaptationStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationStrength_MetaData), NewProp_AdaptationStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationStartTime = { "AdaptationStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCreatureAdaptationStatus, AdaptationStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationStartTime_MetaData), NewProp_AdaptationStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationDuration = { "AdaptationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCreatureAdaptationStatus, AdaptationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationDuration_MetaData), NewProp_AdaptationDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_TargetPlayer = { "TargetPlayer", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronCreatureAdaptationStatus, TargetPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPlayer_MetaData), NewProp_TargetPlayer_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_bIsAdapted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_CurrentAdaptationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_CurrentAdaptationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_AdaptationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewProp_TargetPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronCreatureAdaptationStatus",
	Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::PropPointers),
	sizeof(FAuracronCreatureAdaptationStatus),
	alignof(FAuracronCreatureAdaptationStatus),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.InnerSingleton, Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus.InnerSingleton;
}
// ********** End ScriptStruct FAuracronCreatureAdaptationStatus ***********************************

// ********** Begin Class AAuracronJungleCreature Function ApplyAIAdaptation ***********************
struct Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics
{
	struct AuracronJungleCreature_eventApplyAIAdaptation_Parms
	{
		FAuracronCreatureAdaptation Adaptation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply AI adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply AI adaptation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Adaptation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Adaptation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::NewProp_Adaptation = { "Adaptation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventApplyAIAdaptation_Parms, Adaptation), Z_Construct_UScriptStruct_FAuracronCreatureAdaptation, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Adaptation_MetaData), NewProp_Adaptation_MetaData) }; // 3328928115
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::NewProp_Adaptation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "ApplyAIAdaptation", Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::AuracronJungleCreature_eventApplyAIAdaptation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::AuracronJungleCreature_eventApplyAIAdaptation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execApplyAIAdaptation)
{
	P_GET_STRUCT_REF(FAuracronCreatureAdaptation,Z_Param_Out_Adaptation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyAIAdaptation(Z_Param_Out_Adaptation);
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function ApplyAIAdaptation *************************

// ********** Begin Class AAuracronJungleCreature Function GetAdaptationStatus *********************
struct Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics
{
	struct AuracronJungleCreature_eventGetAdaptationStatus_Parms
	{
		FAuracronCreatureAdaptationStatus ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get adaptation status */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get adaptation status" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventGetAdaptationStatus_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus, METADATA_PARAMS(0, nullptr) }; // 1202869703
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "GetAdaptationStatus", Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::AuracronJungleCreature_eventGetAdaptationStatus_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::AuracronJungleCreature_eventGetAdaptationStatus_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execGetAdaptationStatus)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronCreatureAdaptationStatus*)Z_Param__Result=P_THIS->GetAdaptationStatus();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function GetAdaptationStatus ***********************

// ********** Begin Class AAuracronJungleCreature Function GetBehaviorState ************************
struct Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics
{
	struct AuracronJungleCreature_eventGetBehaviorState_Parms
	{
		ECreatureBehaviorState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current behavior state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current behavior state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventGetBehaviorState_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState, METADATA_PARAMS(0, nullptr) }; // 2384548983
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "GetBehaviorState", Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::AuracronJungleCreature_eventGetBehaviorState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::AuracronJungleCreature_eventGetBehaviorState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execGetBehaviorState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ECreatureBehaviorState*)Z_Param__Result=P_THIS->GetBehaviorState();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function GetBehaviorState **************************

// ********** Begin Class AAuracronJungleCreature Function GetCreatureType *************************
struct Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics
{
	struct AuracronJungleCreature_eventGetCreatureType_Parms
	{
		EJungleCreatureType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get creature type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get creature type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventGetCreatureType_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType, METADATA_PARAMS(0, nullptr) }; // 397863734
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "GetCreatureType", Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::AuracronJungleCreature_eventGetCreatureType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::AuracronJungleCreature_eventGetCreatureType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execGetCreatureType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EJungleCreatureType*)Z_Param__Result=P_THIS->GetCreatureType();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function GetCreatureType ***************************

// ********** Begin Class AAuracronJungleCreature Function IsCreatureAdapted ***********************
struct Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics
{
	struct AuracronJungleCreature_eventIsCreatureAdapted_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if creature is adapted */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if creature is adapted" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronJungleCreature_eventIsCreatureAdapted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronJungleCreature_eventIsCreatureAdapted_Parms), &Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "IsCreatureAdapted", Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::AuracronJungleCreature_eventIsCreatureAdapted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::AuracronJungleCreature_eventIsCreatureAdapted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execIsCreatureAdapted)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCreatureAdapted();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function IsCreatureAdapted *************************

// ********** Begin Class AAuracronJungleCreature Function OnAdaptationRemoved *********************
static FName NAME_AAuracronJungleCreature_OnAdaptationRemoved = FName(TEXT("OnAdaptationRemoved"));
void AAuracronJungleCreature::OnAdaptationRemoved()
{
	UFunction* Func = FindFunctionChecked(NAME_AAuracronJungleCreature_OnAdaptationRemoved);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when adaptation is removed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when adaptation is removed" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "OnAdaptationRemoved", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronJungleCreature Function OnAdaptationRemoved ***********************

// ********** Begin Class AAuracronJungleCreature Function OnCreatureAdapted ***********************
struct AuracronJungleCreature_eventOnCreatureAdapted_Parms
{
	EJungleCreatureAdaptationType AdaptationType;
};
static FName NAME_AAuracronJungleCreature_OnCreatureAdapted = FName(TEXT("OnCreatureAdapted"));
void AAuracronJungleCreature::OnCreatureAdapted(EJungleCreatureAdaptationType AdaptationType)
{
	AuracronJungleCreature_eventOnCreatureAdapted_Parms Parms;
	Parms.AdaptationType=AdaptationType;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronJungleCreature_OnCreatureAdapted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when creature is adapted */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when creature is adapted" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AdaptationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AdaptationType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::NewProp_AdaptationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::NewProp_AdaptationType = { "AdaptationType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventOnCreatureAdapted_Parms, AdaptationType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureAdaptationType, METADATA_PARAMS(0, nullptr) }; // 3773843215
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::NewProp_AdaptationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::NewProp_AdaptationType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "OnCreatureAdapted", Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::PropPointers), sizeof(AuracronJungleCreature_eventOnCreatureAdapted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronJungleCreature_eventOnCreatureAdapted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronJungleCreature Function OnCreatureAdapted *************************

// ********** Begin Class AAuracronJungleCreature Function OnPlayerDetected ************************
struct AuracronJungleCreature_eventOnPlayerDetected_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronJungleCreature_OnPlayerDetected = FName(TEXT("OnPlayerDetected"));
void AAuracronJungleCreature::OnPlayerDetected(APawn* Player)
{
	AuracronJungleCreature_eventOnPlayerDetected_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronJungleCreature_OnPlayerDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Creature Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when player is detected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when player is detected" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventOnPlayerDetected_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "OnPlayerDetected", Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::PropPointers), sizeof(AuracronJungleCreature_eventOnPlayerDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronJungleCreature_eventOnPlayerDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronJungleCreature Function OnPlayerDetected **************************

// ********** Begin Class AAuracronJungleCreature Function ReactToPlayerPresence *******************
struct Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics
{
	struct AuracronJungleCreature_eventReactToPlayerPresence_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** React to player presence */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "React to player presence" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventReactToPlayerPresence_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "ReactToPlayerPresence", Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::AuracronJungleCreature_eventReactToPlayerPresence_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::AuracronJungleCreature_eventReactToPlayerPresence_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execReactToPlayerPresence)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReactToPlayerPresence(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function ReactToPlayerPresence *********************

// ********** Begin Class AAuracronJungleCreature Function RegisterWithAdaptiveAI ******************
struct Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics
{
	struct AuracronJungleCreature_eventRegisterWithAdaptiveAI_Parms
	{
		AAuracronAdaptiveJungleAI* AISystem;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Register with adaptive AI system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Register with adaptive AI system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AISystem;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::NewProp_AISystem = { "AISystem", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventRegisterWithAdaptiveAI_Parms, AISystem), Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::NewProp_AISystem,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "RegisterWithAdaptiveAI", Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::AuracronJungleCreature_eventRegisterWithAdaptiveAI_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::AuracronJungleCreature_eventRegisterWithAdaptiveAI_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execRegisterWithAdaptiveAI)
{
	P_GET_OBJECT(AAuracronAdaptiveJungleAI,Z_Param_AISystem);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterWithAdaptiveAI(Z_Param_AISystem);
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function RegisterWithAdaptiveAI ********************

// ********** Begin Class AAuracronJungleCreature Function RemoveAIAdaptation **********************
struct Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Remove AI adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove AI adaptation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "RemoveAIAdaptation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execRemoveAIAdaptation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveAIAdaptation();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function RemoveAIAdaptation ************************

// ********** Begin Class AAuracronJungleCreature Function SetBehaviorState ************************
struct Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics
{
	struct AuracronJungleCreature_eventSetBehaviorState_Parms
	{
		ECreatureBehaviorState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set behavior state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set behavior state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventSetBehaviorState_Parms, NewState), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState, METADATA_PARAMS(0, nullptr) }; // 2384548983
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "SetBehaviorState", Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::AuracronJungleCreature_eventSetBehaviorState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::AuracronJungleCreature_eventSetBehaviorState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execSetBehaviorState)
{
	P_GET_ENUM(ECreatureBehaviorState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBehaviorState(ECreatureBehaviorState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function SetBehaviorState **************************

// ********** Begin Class AAuracronJungleCreature Function SetCreatureType *************************
struct Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics
{
	struct AuracronJungleCreature_eventSetCreatureType_Parms
	{
		EJungleCreatureType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Jungle Creature" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set creature type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set creature type" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventSetCreatureType_Parms, NewType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType, METADATA_PARAMS(0, nullptr) }; // 397863734
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "SetCreatureType", Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::AuracronJungleCreature_eventSetCreatureType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::AuracronJungleCreature_eventSetCreatureType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execSetCreatureType)
{
	P_GET_ENUM(EJungleCreatureType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCreatureType(EJungleCreatureType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function SetCreatureType ***************************

// ********** Begin Class AAuracronJungleCreature Function UnregisterFromAdaptiveAI ****************
struct Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unregister from adaptive AI system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unregister from adaptive AI system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "UnregisterFromAdaptiveAI", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execUnregisterFromAdaptiveAI)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterFromAdaptiveAI();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function UnregisterFromAdaptiveAI ******************

// ********** Begin Class AAuracronJungleCreature Function UpdateCreatureBehavior ******************
struct Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics
{
	struct AuracronJungleCreature_eventUpdateCreatureBehavior_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update creature behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update creature behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronJungleCreature_eventUpdateCreatureBehavior_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "UpdateCreatureBehavior", Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::AuracronJungleCreature_eventUpdateCreatureBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::AuracronJungleCreature_eventUpdateCreatureBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execUpdateCreatureBehavior)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCreatureBehavior(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function UpdateCreatureBehavior ********************

// ********** Begin Class AAuracronJungleCreature Function UpdateSocialBehavior ********************
struct Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update social behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update social behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "UpdateSocialBehavior", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execUpdateSocialBehavior)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateSocialBehavior();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function UpdateSocialBehavior **********************

// ********** Begin Class AAuracronJungleCreature Function UpdateTerritorialBehavior ***************
struct Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update territorial behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update territorial behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronJungleCreature, nullptr, "UpdateTerritorialBehavior", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronJungleCreature::execUpdateTerritorialBehavior)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTerritorialBehavior();
	P_NATIVE_END;
}
// ********** End Class AAuracronJungleCreature Function UpdateTerritorialBehavior *****************

// ********** Begin Class AAuracronJungleCreature **************************************************
void AAuracronJungleCreature::StaticRegisterNativesAAuracronJungleCreature()
{
	UClass* Class = AAuracronJungleCreature::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyAIAdaptation", &AAuracronJungleCreature::execApplyAIAdaptation },
		{ "GetAdaptationStatus", &AAuracronJungleCreature::execGetAdaptationStatus },
		{ "GetBehaviorState", &AAuracronJungleCreature::execGetBehaviorState },
		{ "GetCreatureType", &AAuracronJungleCreature::execGetCreatureType },
		{ "IsCreatureAdapted", &AAuracronJungleCreature::execIsCreatureAdapted },
		{ "ReactToPlayerPresence", &AAuracronJungleCreature::execReactToPlayerPresence },
		{ "RegisterWithAdaptiveAI", &AAuracronJungleCreature::execRegisterWithAdaptiveAI },
		{ "RemoveAIAdaptation", &AAuracronJungleCreature::execRemoveAIAdaptation },
		{ "SetBehaviorState", &AAuracronJungleCreature::execSetBehaviorState },
		{ "SetCreatureType", &AAuracronJungleCreature::execSetCreatureType },
		{ "UnregisterFromAdaptiveAI", &AAuracronJungleCreature::execUnregisterFromAdaptiveAI },
		{ "UpdateCreatureBehavior", &AAuracronJungleCreature::execUpdateCreatureBehavior },
		{ "UpdateSocialBehavior", &AAuracronJungleCreature::execUpdateSocialBehavior },
		{ "UpdateTerritorialBehavior", &AAuracronJungleCreature::execUpdateTerritorialBehavior },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronJungleCreature;
UClass* AAuracronJungleCreature::GetPrivateStaticClass()
{
	using TClass = AAuracronJungleCreature;
	if (!Z_Registration_Info_UClass_AAuracronJungleCreature.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronJungleCreature"),
			Z_Registration_Info_UClass_AAuracronJungleCreature.InnerSingleton,
			StaticRegisterNativesAAuracronJungleCreature,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronJungleCreature.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronJungleCreature_NoRegister()
{
	return AAuracronJungleCreature::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronJungleCreature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Jungle Creature\n * \n * Base class for all jungle creatures that can be adapted by the AI system.\n * Provides foundation for behavior adaptation, stat modification, and AI control.\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "AuracronJungleCreature.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Jungle Creature\n\nBase class for all jungle creatures that can be adapted by the AI system.\nProvides foundation for behavior adaptation, stat modification, and AI control." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureType_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Creature type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Creature type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseBehaviorTree_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base behavior tree */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base behavior tree" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureBlackboard_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Blackboard data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blackboard data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseMovementSpeed_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Base movement speed */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base movement speed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerritorialRadius_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Territorial radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Territorial radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRange_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Detection range */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detection range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAIAdaptation_MetaData[] = {
		{ "Category", "Creature Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable AI adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable AI adaptation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySystemComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ability system component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ability system component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttributeSet_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Attribute set */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attribute set" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureAudio_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio component for creature sounds */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio component for creature sounds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatureVFX_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX component for creature effects */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX component for creature effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBehaviorState_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current behavior state */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current behavior state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationStatus_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current adaptation status */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current adaptation status" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredAISystem_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registered AI system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registered AI system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastBehaviorUpdateTime_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last behavior update time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last behavior update time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HomeLocation_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Home location */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Home location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTargetPlayer_MetaData[] = {
		{ "Category", "Creature State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current target player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronJungleCreature.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current target player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CreatureType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CreatureType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseBehaviorTree;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatureBlackboard;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseMovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerritorialRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRange;
	static void NewProp_bEnableAIAdaptation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAIAdaptation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AbilitySystemComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AttributeSet;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatureAudio;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatureVFX;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentBehaviorState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentBehaviorState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdaptationStatus;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RegisteredAISystem;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastBehaviorUpdateTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HomeLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentTargetPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronJungleCreature_ApplyAIAdaptation, "ApplyAIAdaptation" }, // 2998486691
		{ &Z_Construct_UFunction_AAuracronJungleCreature_GetAdaptationStatus, "GetAdaptationStatus" }, // 2043345501
		{ &Z_Construct_UFunction_AAuracronJungleCreature_GetBehaviorState, "GetBehaviorState" }, // 3206292082
		{ &Z_Construct_UFunction_AAuracronJungleCreature_GetCreatureType, "GetCreatureType" }, // 4249790015
		{ &Z_Construct_UFunction_AAuracronJungleCreature_IsCreatureAdapted, "IsCreatureAdapted" }, // 3118322808
		{ &Z_Construct_UFunction_AAuracronJungleCreature_OnAdaptationRemoved, "OnAdaptationRemoved" }, // 2264768942
		{ &Z_Construct_UFunction_AAuracronJungleCreature_OnCreatureAdapted, "OnCreatureAdapted" }, // 1414581869
		{ &Z_Construct_UFunction_AAuracronJungleCreature_OnPlayerDetected, "OnPlayerDetected" }, // 169843685
		{ &Z_Construct_UFunction_AAuracronJungleCreature_ReactToPlayerPresence, "ReactToPlayerPresence" }, // 2408341619
		{ &Z_Construct_UFunction_AAuracronJungleCreature_RegisterWithAdaptiveAI, "RegisterWithAdaptiveAI" }, // 2168778877
		{ &Z_Construct_UFunction_AAuracronJungleCreature_RemoveAIAdaptation, "RemoveAIAdaptation" }, // 2917675438
		{ &Z_Construct_UFunction_AAuracronJungleCreature_SetBehaviorState, "SetBehaviorState" }, // 3214071716
		{ &Z_Construct_UFunction_AAuracronJungleCreature_SetCreatureType, "SetCreatureType" }, // 2949695400
		{ &Z_Construct_UFunction_AAuracronJungleCreature_UnregisterFromAdaptiveAI, "UnregisterFromAdaptiveAI" }, // 2602483937
		{ &Z_Construct_UFunction_AAuracronJungleCreature_UpdateCreatureBehavior, "UpdateCreatureBehavior" }, // 720814491
		{ &Z_Construct_UFunction_AAuracronJungleCreature_UpdateSocialBehavior, "UpdateSocialBehavior" }, // 3874416686
		{ &Z_Construct_UFunction_AAuracronJungleCreature_UpdateTerritorialBehavior, "UpdateTerritorialBehavior" }, // 2164678607
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronJungleCreature>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureType = { "CreatureType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CreatureType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EJungleCreatureType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureType_MetaData), NewProp_CreatureType_MetaData) }; // 397863734
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_BaseBehaviorTree = { "BaseBehaviorTree", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, BaseBehaviorTree), Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseBehaviorTree_MetaData), NewProp_BaseBehaviorTree_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureBlackboard = { "CreatureBlackboard", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CreatureBlackboard), Z_Construct_UClass_UBlackboardData_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureBlackboard_MetaData), NewProp_CreatureBlackboard_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_BaseMovementSpeed = { "BaseMovementSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, BaseMovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseMovementSpeed_MetaData), NewProp_BaseMovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_TerritorialRadius = { "TerritorialRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, TerritorialRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerritorialRadius_MetaData), NewProp_TerritorialRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_DetectionRange = { "DetectionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, DetectionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRange_MetaData), NewProp_DetectionRange_MetaData) };
void Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_bEnableAIAdaptation_SetBit(void* Obj)
{
	((AAuracronJungleCreature*)Obj)->bEnableAIAdaptation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_bEnableAIAdaptation = { "bEnableAIAdaptation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronJungleCreature), &Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_bEnableAIAdaptation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAIAdaptation_MetaData), NewProp_bEnableAIAdaptation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AbilitySystemComponent = { "AbilitySystemComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, AbilitySystemComponent), Z_Construct_UClass_UAbilitySystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySystemComponent_MetaData), NewProp_AbilitySystemComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AttributeSet = { "AttributeSet", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, AttributeSet), Z_Construct_UClass_UAttributeSet_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttributeSet_MetaData), NewProp_AttributeSet_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureAudio = { "CreatureAudio", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CreatureAudio), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureAudio_MetaData), NewProp_CreatureAudio_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureVFX = { "CreatureVFX", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CreatureVFX), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatureVFX_MetaData), NewProp_CreatureVFX_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentBehaviorState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentBehaviorState = { "CurrentBehaviorState", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CurrentBehaviorState), Z_Construct_UEnum_AuracronDynamicRealmBridge_ECreatureBehaviorState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBehaviorState_MetaData), NewProp_CurrentBehaviorState_MetaData) }; // 2384548983
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AdaptationStatus = { "AdaptationStatus", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, AdaptationStatus), Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationStatus_MetaData), NewProp_AdaptationStatus_MetaData) }; // 1202869703
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_RegisteredAISystem = { "RegisteredAISystem", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, RegisteredAISystem), Z_Construct_UClass_AAuracronAdaptiveJungleAI_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredAISystem_MetaData), NewProp_RegisteredAISystem_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_LastBehaviorUpdateTime = { "LastBehaviorUpdateTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, LastBehaviorUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastBehaviorUpdateTime_MetaData), NewProp_LastBehaviorUpdateTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_HomeLocation = { "HomeLocation", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, HomeLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HomeLocation_MetaData), NewProp_HomeLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentTargetPlayer = { "CurrentTargetPlayer", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronJungleCreature, CurrentTargetPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTargetPlayer_MetaData), NewProp_CurrentTargetPlayer_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronJungleCreature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_BaseBehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureBlackboard,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_BaseMovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_TerritorialRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_DetectionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_bEnableAIAdaptation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AbilitySystemComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AttributeSet,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CreatureVFX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentBehaviorState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentBehaviorState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_AdaptationStatus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_RegisteredAISystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_LastBehaviorUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_HomeLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronJungleCreature_Statics::NewProp_CurrentTargetPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronJungleCreature_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronJungleCreature_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronJungleCreature_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_AAuracronJungleCreature_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UAbilitySystemInterface_NoRegister, (int32)VTABLE_OFFSET(AAuracronJungleCreature, IAbilitySystemInterface), false },  // 1199015870
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronJungleCreature_Statics::ClassParams = {
	&AAuracronJungleCreature::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronJungleCreature_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronJungleCreature_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronJungleCreature_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronJungleCreature_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronJungleCreature()
{
	if (!Z_Registration_Info_UClass_AAuracronJungleCreature.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronJungleCreature.OuterSingleton, Z_Construct_UClass_AAuracronJungleCreature_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronJungleCreature.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronJungleCreature);
AAuracronJungleCreature::~AAuracronJungleCreature() {}
// ********** End Class AAuracronJungleCreature ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EJungleCreatureAdaptationType_StaticEnum, TEXT("EJungleCreatureAdaptationType"), &Z_Registration_Info_UEnum_EJungleCreatureAdaptationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3773843215U) },
		{ EJungleCreatureType_StaticEnum, TEXT("EJungleCreatureType"), &Z_Registration_Info_UEnum_EJungleCreatureType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 397863734U) },
		{ ECreatureBehaviorState_StaticEnum, TEXT("ECreatureBehaviorState"), &Z_Registration_Info_UEnum_ECreatureBehaviorState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2384548983U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronCreatureAdaptationStatus::StaticStruct, Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics::NewStructOps, TEXT("AuracronCreatureAdaptationStatus"), &Z_Registration_Info_UScriptStruct_FAuracronCreatureAdaptationStatus, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronCreatureAdaptationStatus), 1202869703U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronJungleCreature, AAuracronJungleCreature::StaticClass, TEXT("AAuracronJungleCreature"), &Z_Registration_Info_UClass_AAuracronJungleCreature, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronJungleCreature), 3230696207U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_4218849351(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
