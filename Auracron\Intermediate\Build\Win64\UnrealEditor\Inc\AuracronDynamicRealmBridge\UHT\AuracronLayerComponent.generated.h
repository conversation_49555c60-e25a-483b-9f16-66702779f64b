// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronLayerComponent.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronLayerComponent_generated_h
#error "AuracronLayerComponent.generated.h already included, missing '#pragma once' in AuracronLayerComponent.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronLayerComponent_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronRealmLayer : uint8;
enum class ERealmTransitionType : uint8;
struct FAuracronLayerModifiers;

// ********** Begin ScriptStruct FAuracronLayerInteraction *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_22_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerInteraction;
// ********** End ScriptStruct FAuracronLayerInteraction *******************************************

// ********** Begin ScriptStruct FAuracronLayerMovement ********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_59_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerMovement;
// ********** End ScriptStruct FAuracronLayerMovement **********************************************

// ********** Begin ScriptStruct FAuracronLayerModifiers *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerModifiers;
// ********** End ScriptStruct FAuracronLayerModifiers *********************************************

// ********** Begin Class UAuracronLayerComponent **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsLayerVisible); \
	DECLARE_FUNCTION(execSetLayerRenderingEnabled); \
	DECLARE_FUNCTION(execUpdateLayerVisibility); \
	DECLARE_FUNCTION(execCalculateCrossLayerRange); \
	DECLARE_FUNCTION(execCalculateCrossLayerDamage); \
	DECLARE_FUNCTION(execCanAffectLayer); \
	DECLARE_FUNCTION(execRemoveLayerModifiers); \
	DECLARE_FUNCTION(execApplyLayerModifiers); \
	DECLARE_FUNCTION(execGetCurrentLayerModifiers); \
	DECLARE_FUNCTION(execCancelTransition); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execIsInTransition); \
	DECLARE_FUNCTION(execRequestLayerTransition); \
	DECLARE_FUNCTION(execCanTransitionToLayer); \
	DECLARE_FUNCTION(execIsInLayer); \
	DECLARE_FUNCTION(execGetCurrentLayer); \
	DECLARE_FUNCTION(execSetCurrentLayer);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronLayerComponent(); \
	friend struct Z_Construct_UClass_UAuracronLayerComponent_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronLayerComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronLayerComponent_NoRegister) \
	DECLARE_SERIALIZER(UAuracronLayerComponent)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronLayerComponent(UAuracronLayerComponent&&) = delete; \
	UAuracronLayerComponent(const UAuracronLayerComponent&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronLayerComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronLayerComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronLayerComponent) \
	NO_API virtual ~UAuracronLayerComponent();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_153_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h_156_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronLayerComponent;

// ********** End Class UAuracronLayerComponent ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
