{"Version": "1.2", "Data": {"Source": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracrondynamicrealmbridge\\permoduleinline.gen.cpp", "ProvidedModule": "", "PCH": "c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.nonoptimized.valapi.valexpapi.cpplatest.h.pch", "Includes": ["c:\\aura\\projeto\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracrondynamicrealmbridge\\definitions.auracrondynamicrealmbridge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}