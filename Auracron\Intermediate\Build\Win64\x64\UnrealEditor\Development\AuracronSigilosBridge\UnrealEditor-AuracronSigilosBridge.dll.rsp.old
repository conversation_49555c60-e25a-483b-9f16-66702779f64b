/MANIFEST:NO
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/lib/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/ucrt/x64"
/LIBPATH:"C:/Program Files (x86)/Windows Kits/10/lib/10.0.26100.0/um/x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NOEXP
/NATVIS:"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/RenderCore.natvis"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/AuracronEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/Module.AuracronSigilosBridge.gen.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/AuracronSigilEffects.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/AuracronSigilGameplayTags.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/AuracronSigilosBridge.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/AuracronSigilSystemTests.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/PerModuleInline.gen.cpp.obj"
"C:/Aura/projeto/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/AuracronSigilosBridge/Default.rc2.res"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/CoreUObject/UnrealEditor-CoreUObject.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Engine/UnrealEditor-Engine.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Slate/UnrealEditor-Slate.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SlateCore/UnrealEditor-SlateCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RenderCore/UnrealEditor-RenderCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/RHI/UnrealEditor-RHI.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraCore/UnrealEditor-NiagaraCore.lib"
"../Plugins/FX/Niagara/Intermediate/Build/Win64/x64/UnrealEditor/Development/NiagaraShader/UnrealEditor-NiagaraShader.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ChaosCore/UnrealEditor-ChaosCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PhysicsCore/UnrealEditor-PhysicsCore.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/AudioMixer/UnrealEditor-AudioMixer.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundFrontend/UnrealEditor-MetasoundFrontend.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundEngine/UnrealEditor-MetasoundEngine.lib"
"../Plugins/Runtime/Metasound/Intermediate/Build/Win64/x64/UnrealEditor/Development/MetasoundStandardNodes/UnrealEditor-MetasoundStandardNodes.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/SignalProcessing/UnrealEditor-SignalProcessing.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Json/UnrealEditor-Json.lib"
"../Plugins/Online/OnlineSubsystemUtils/Intermediate/Build/Win64/x64/UnrealEditor/Development/OnlineSubsystemUtils/UnrealEditor-OnlineSubsystemUtils.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Sockets/UnrealEditor-Sockets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Networking/UnrealEditor-Networking.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PacketHandler/UnrealEditor-PacketHandler.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ReliableHComp/UnrealEditor-ReliabilityHandlerComponent.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/ToolMenus/UnrealEditor-ToolMenus.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorStyle/UnrealEditor-EditorStyle.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EditorWidgets/UnrealEditor-EditorWidgets.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/UnrealEditor-UnrealEd.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/PropertyEditor/UnrealEditor-PropertyEditor.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/KismetCompiler/UnrealEditor-KismetCompiler.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/BlueprintGraph/UnrealEditor-BlueprintGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Kismet/UnrealEditor-Kismet.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/Core/UnrealEditor-Core.lib"
"../Plugins/Runtime/GameplayAbilities/Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayAbilities/UnrealEditor-GameplayAbilities.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTags/UnrealEditor-GameplayTags.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/GameplayTasks/UnrealEditor-GameplayTasks.lib"
"../Plugins/Runtime/ModularGameplay/Intermediate/Build/Win64/x64/UnrealEditor/Development/ModularGameplay/UnrealEditor-ModularGameplay.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/NetCore/UnrealEditor-NetCore.lib"
"../Plugins/Runtime/ReplicationGraph/Intermediate/Build/Win64/x64/UnrealEditor/Development/ReplicationGraph/UnrealEditor-ReplicationGraph.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/UMG/UnrealEditor-UMG.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/InputCore/UnrealEditor-InputCore.lib"
"../Plugins/EnhancedInput/Intermediate/Build/Win64/x64/UnrealEditor/Development/EnhancedInput/UnrealEditor-EnhancedInput.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/DeveloperSettings/UnrealEditor-DeveloperSettings.lib"
"../Intermediate/Build/Win64/x64/UnrealEditor/Development/EngineSettings/UnrealEditor-EngineSettings.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronSigilosBridge.dll"
/PDB:"C:/Aura/projeto/Auracron/Binaries/Win64/UnrealEditor-AuracronSigilosBridge.pdb"
/ignore:4078