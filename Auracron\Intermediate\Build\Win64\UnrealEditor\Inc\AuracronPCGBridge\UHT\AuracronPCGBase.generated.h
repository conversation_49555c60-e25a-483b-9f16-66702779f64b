// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPCGBase.h"

#ifdef AURACRONPCGBRIDGE_AuracronPCGBase_generated_h
#error "AuracronPCGBase.generated.h already included, missing '#pragma once' in AuracronPCGBase.h"
#endif
#define AURACRONPCGBRIDGE_AuracronPCGBase_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGSettingsBase(); \
	friend struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGSettingsBase, UPCGSettings, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGSettingsBase)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGSettingsBase(UAuracronPCGSettingsBase&&) = delete; \
	UAuracronPCGSettingsBase(const UAuracronPCGSettingsBase&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGSettingsBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGSettingsBase); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGSettingsBase) \
	NO_API virtual ~UAuracronPCGSettingsBase();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_26_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_29_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGSettingsBase;

// ********** End Class UAuracronPCGSettingsBase ***************************************************

// ********** Begin Class UAuracronBiomePCGSettings ************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomePCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_78_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronBiomePCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronBiomePCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronBiomePCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronBiomePCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronBiomePCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronBiomePCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_78_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronBiomePCGSettings(UAuracronBiomePCGSettings&&) = delete; \
	UAuracronBiomePCGSettings(const UAuracronBiomePCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronBiomePCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronBiomePCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronBiomePCGSettings) \
	NO_API virtual ~UAuracronBiomePCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_75_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_78_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_78_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_78_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronBiomePCGSettings;

// ********** End Class UAuracronBiomePCGSettings **************************************************

// ********** Begin Class UAuracronTerrainPCGSettings **********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_126_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronTerrainPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronTerrainPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronTerrainPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronTerrainPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronTerrainPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronTerrainPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_126_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronTerrainPCGSettings(UAuracronTerrainPCGSettings&&) = delete; \
	UAuracronTerrainPCGSettings(const UAuracronTerrainPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronTerrainPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronTerrainPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronTerrainPCGSettings) \
	NO_API virtual ~UAuracronTerrainPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_123_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_126_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_126_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_126_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronTerrainPCGSettings;

// ********** End Class UAuracronTerrainPCGSettings ************************************************

// ********** Begin Class UAuracronStructurePCGSettings ********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronStructurePCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_170_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronStructurePCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronStructurePCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronStructurePCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronStructurePCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronStructurePCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronStructurePCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_170_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronStructurePCGSettings(UAuracronStructurePCGSettings&&) = delete; \
	UAuracronStructurePCGSettings(const UAuracronStructurePCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronStructurePCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronStructurePCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronStructurePCGSettings) \
	NO_API virtual ~UAuracronStructurePCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_167_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_170_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_170_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_170_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronStructurePCGSettings;

// ********** End Class UAuracronStructurePCGSettings **********************************************

// ********** Begin Class UAuracronVegetationPCGSettings *******************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_206_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronVegetationPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronVegetationPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronVegetationPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronVegetationPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronVegetationPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronVegetationPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_206_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronVegetationPCGSettings(UAuracronVegetationPCGSettings&&) = delete; \
	UAuracronVegetationPCGSettings(const UAuracronVegetationPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronVegetationPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronVegetationPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronVegetationPCGSettings) \
	NO_API virtual ~UAuracronVegetationPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_203_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_206_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_206_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_206_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronVegetationPCGSettings;

// ********** End Class UAuracronVegetationPCGSettings *********************************************

// ********** Begin Class UAuracronResourcePCGSettings *********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronResourcePCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_254_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronResourcePCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronResourcePCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronResourcePCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronResourcePCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronResourcePCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronResourcePCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_254_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronResourcePCGSettings(UAuracronResourcePCGSettings&&) = delete; \
	UAuracronResourcePCGSettings(const UAuracronResourcePCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronResourcePCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronResourcePCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronResourcePCGSettings) \
	NO_API virtual ~UAuracronResourcePCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_251_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_254_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_254_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_254_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronResourcePCGSettings;

// ********** End Class UAuracronResourcePCGSettings ***********************************************

// ********** Begin Class UAuracronEnemySpawnPCGSettings *******************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_294_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronEnemySpawnPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronEnemySpawnPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronEnemySpawnPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronEnemySpawnPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_294_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronEnemySpawnPCGSettings(UAuracronEnemySpawnPCGSettings&&) = delete; \
	UAuracronEnemySpawnPCGSettings(const UAuracronEnemySpawnPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronEnemySpawnPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronEnemySpawnPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronEnemySpawnPCGSettings) \
	NO_API virtual ~UAuracronEnemySpawnPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_291_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_294_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_294_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_294_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronEnemySpawnPCGSettings;

// ********** End Class UAuracronEnemySpawnPCGSettings *********************************************

// ********** Begin Class UAuracronDungeonPCGSettings **********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_338_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronDungeonPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronDungeonPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronDungeonPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronDungeonPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronDungeonPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronDungeonPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_338_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronDungeonPCGSettings(UAuracronDungeonPCGSettings&&) = delete; \
	UAuracronDungeonPCGSettings(const UAuracronDungeonPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronDungeonPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronDungeonPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronDungeonPCGSettings) \
	NO_API virtual ~UAuracronDungeonPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_335_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_338_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_338_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_338_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronDungeonPCGSettings;

// ********** End Class UAuracronDungeonPCGSettings ************************************************

// ********** Begin Class UAuracronWeatherPCGSettings **********************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_382_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronWeatherPCGSettings(); \
	friend struct Z_Construct_UClass_UAuracronWeatherPCGSettings_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronWeatherPCGSettings_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronWeatherPCGSettings, UAuracronPCGSettingsBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronWeatherPCGSettings_NoRegister) \
	DECLARE_SERIALIZER(UAuracronWeatherPCGSettings)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_382_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronWeatherPCGSettings(UAuracronWeatherPCGSettings&&) = delete; \
	UAuracronWeatherPCGSettings(const UAuracronWeatherPCGSettings&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronWeatherPCGSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronWeatherPCGSettings); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronWeatherPCGSettings) \
	NO_API virtual ~UAuracronWeatherPCGSettings();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_379_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_382_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_382_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_382_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronWeatherPCGSettings;

// ********** End Class UAuracronWeatherPCGSettings ************************************************

// ********** Begin Class UAuracronPCGBridgeAPI ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIntegrateWithAudioBridge); \
	DECLARE_FUNCTION(execIntegrateWithVFXBridge); \
	DECLARE_FUNCTION(execIntegrateWithCombatBridge); \
	DECLARE_FUNCTION(execIntegrateWithWorldPartitionBridge); \
	DECLARE_FUNCTION(execIntegrateWithDynamicRealmBridge); \
	DECLARE_FUNCTION(execIntegrateWithFoliageBridge); \
	DECLARE_FUNCTION(execApplyWeatherEffects); \
	DECLARE_FUNCTION(execGenerateDungeon); \
	DECLARE_FUNCTION(execGenerateEnemySpawns); \
	DECLARE_FUNCTION(execGenerateResources); \
	DECLARE_FUNCTION(execGenerateStructures); \
	DECLARE_FUNCTION(execGenerateVegetation); \
	DECLARE_FUNCTION(execGenerateTerrainFeatures); \
	DECLARE_FUNCTION(execGenerateBiomeContent); \
	DECLARE_FUNCTION(execIsSystemReady); \
	DECLARE_FUNCTION(execShutdownPCGSystem); \
	DECLARE_FUNCTION(execInitializePCGSystem);


AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronPCGBridgeAPI(); \
	friend struct Z_Construct_UClass_UAuracronPCGBridgeAPI_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGBridgeAPI_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronPCGBridgeAPI, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronPCGBridge"), Z_Construct_UClass_UAuracronPCGBridgeAPI_NoRegister) \
	DECLARE_SERIALIZER(UAuracronPCGBridgeAPI)


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronPCGBridgeAPI(UAuracronPCGBridgeAPI&&) = delete; \
	UAuracronPCGBridgeAPI(const UAuracronPCGBridgeAPI&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronPCGBridgeAPI); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronPCGBridgeAPI); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAuracronPCGBridgeAPI) \
	NO_API virtual ~UAuracronPCGBridgeAPI();


#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_561_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h_564_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronPCGBridgeAPI;

// ********** End Class UAuracronPCGBridgeAPI ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronPCGBridge_Public_AuracronPCGBase_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
