// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for AuracronFoliageBridge
#pragma once
#include "SharedDefinitions.UnrealEd.Project.NonOptimized.RTTI.ValApi.ValExpApi.Cpp20.h"
#undef AURACRONFOLIAGEBRIDGE_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Auracron
#define UE_TARGET_NAME AuracronEditor
#define AURACRON_FOLIAGE_BRIDGE_PLATFORM_WINDOWS 1
#define WITH_FOLIAGE 1
#define AURACRON_FOLIAGE_BRIDGE_VERSION_MAJOR 1
#define AURACRON_FOLIAGE_BRIDGE_VERSION_MINOR 0
#define AURACRON_FOLIAGE_BRIDGE_VERSION_PATCH 0
#define UE_MODULE_NAME "AuracronFoliageBridge"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define SKELETALMESHUTILITIESCOMMON_API DLLIMPORT
#define CLOTHINGSYSTEMRUNTIMECOMMON_API DLLIMPORT
#define COMPONENTVISUALIZERS_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define LANDSCAPEEDITOR_API DLLIMPORT
#define EDITORINTERACTIVETOOLSFRAMEWORK_API DLLIMPORT
#define SOURCECONTROLWINDOWS_API DLLIMPORT
#define AURACRONFOLIAGEBRIDGE_API DLLEXPORT
#define FOLIAGE_API DLLIMPORT
#define DYNAMICMESH_API DLLIMPORT
#define GEOMETRYALGORITHMS_API DLLIMPORT
#define GEOMETRYFRAMEWORK_API DLLIMPORT
#define MESHCONVERSION_API DLLIMPORT
#define MODELINGCOMPONENTS_API DLLIMPORT
#define MODELINGOPERATORS_API DLLIMPORT
#define TEXTUREUTILITIESCOMMON_API DLLIMPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define PCGGEOMETRYSCRIPTINTEROP_API DLLIMPORT
#define GEOMETRYSCRIPTINGCORE_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
