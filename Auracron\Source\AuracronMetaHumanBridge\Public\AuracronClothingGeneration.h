#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Math/Vector.h"
#include "Math/Transform.h"
#include "Engine/SkeletalMesh.h"
#include "Components/SkeletalMeshComponent.h"
#include "ClothingAsset.h"
#include "ClothingAssetBase.h"
#include "ClothingSimulation.h"
#include "ClothingSimulationFactory.h"
#include "ClothingSystemRuntimeInterface.h"
#include "ChaosCloth/ChaosClothingSimulation.h"
#include "ChaosCloth/ChaosClothingSimulationFactory.h"
#include "ChaosCloth/ChaosClothingSimulationSolver.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Texture2D.h"

// Forward declarations
class UClothingAssetBase;
class USkeletalMesh;
class UMaterialInterface;
class UTexture2D;

/**
 * Enumeration for different types of cloth
 */
UENUM(BlueprintType)
enum class EClothType : uint8
{
    None        UMETA(DisplayName = "None"),
    Shirt       UMETA(DisplayName = "Shirt"),
    Pants       UMETA(DisplayName = "Pants"),
    Dress       UMETA(DisplayName = "Dress"),
    Skirt       UMETA(DisplayName = "Skirt"),
    Cape        UMETA(DisplayName = "Cape"),
    Custom      UMETA(DisplayName = "Custom")
};

/**
 * Structure for cloth mesh data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothMeshData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    EClothType ClothType = EClothType::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    int32 Resolution = 32;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    FVector2D Size = FVector2D(100.0f, 100.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    TArray<FVector3f> CustomVertices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    TArray<FVector3f> CustomNormals;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloth Mesh")
    TArray<FVector2f> CustomUVs;

    FClothMeshData()
    {
        ClothType = EClothType::None;
        Resolution = 32;
        Size = FVector2D(100.0f, 100.0f);
    }
};

/**
 * Structure for cloth physics data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothPhysicsData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Mass = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Density = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float MinParticleMass = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float EdgeStiffness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float StiffnessMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float BendingStiffness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float BendingStiffnessMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Damping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float LocalDamping = 0.05f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float GravityScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bUseGravityOverride = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    FVector GravityOverride = FVector(0.0f, 0.0f, -980.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    FVector WindVelocity = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float WindDragCoefficient = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float WindLiftCoefficient = 0.05f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float CollisionThickness = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float SelfCollisionThickness = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float FrictionCoefficient = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bUseContinuousCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnableSelfCollision = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float SelfCollisionStiffness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bUseLongRangeAttachment = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float TetherStiffness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float TetherScale = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float AreaStiffness = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float VolumeStiffness = 0.1f;

    FClothPhysicsData()
    {
        Mass = 1.0f;
        Density = 1.0f;
        MinParticleMass = 0.01f;
        EdgeStiffness = 0.8f;
        StiffnessMultiplier = 1.0f;
        BendingStiffness = 0.5f;
        BendingStiffnessMultiplier = 1.0f;
        Damping = 0.1f;
        LocalDamping = 0.05f;
        GravityScale = 1.0f;
        bUseGravityOverride = false;
        GravityOverride = FVector(0.0f, 0.0f, -980.0f);
        WindVelocity = FVector::ZeroVector;
        WindDragCoefficient = 0.1f;
        WindLiftCoefficient = 0.05f;
        CollisionThickness = 1.0f;
        SelfCollisionThickness = 2.0f;
        FrictionCoefficient = 0.3f;
        bUseContinuousCollision = true;
        bEnableSelfCollision = false;
        SelfCollisionStiffness = 0.5f;
        bUseLongRangeAttachment = true;
        TetherStiffness = 0.8f;
        TetherScale = 1.0f;
        AreaStiffness = 0.3f;
        VolumeStiffness = 0.1f;
    }
};

/**
 * Structure for cloth simulation data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothSimulationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    int32 IterationCount = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    int32 MaxIterationCount = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    int32 SubdivisionCount = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bUsePointBasedWind = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bUseFastTether = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float SolverFrequency = 60.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bUseXPBDConstraints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bUseBackstop = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float BackstopRadius = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    float BackstopDistance = 10.0f;

    FClothSimulationData()
    {
        IterationCount = 3;
        MaxIterationCount = 10;
        SubdivisionCount = 1;
        bUsePointBasedWind = false;
        bUseFastTether = true;
        SolverFrequency = 60.0f;
        bUseXPBDConstraints = true;
        bUseBackstop = false;
        BackstopRadius = 5.0f;
        BackstopDistance = 10.0f;
    }
};

/**
 * Structure for cloth material data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothMaterialData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UMaterialInterface* BaseMaterial = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* DiffuseTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* NormalTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    UTexture2D* RoughnessTexture = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FLinearColor BaseColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Metallic = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Roughness = 0.8f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Specular = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float Opacity = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float ClothStiffness = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    float ClothDamping = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FVector WindResponse = FVector(1.0f, 1.0f, 0.5f);

    FClothMaterialData()
    {
        BaseMaterial = nullptr;
        DiffuseTexture = nullptr;
        NormalTexture = nullptr;
        RoughnessTexture = nullptr;
        BaseColor = FLinearColor::White;
        Metallic = 0.0f;
        Roughness = 0.8f;
        Specular = 0.5f;
        Opacity = 1.0f;
        ClothStiffness = 0.5f;
        ClothDamping = 0.1f;
        WindResponse = FVector(1.0f, 1.0f, 0.5f);
    }
};

/**
 * Structure for cloth LOD level data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothLODLevel
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 LODIndex = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float ReductionPercentage = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxVertices = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float ScreenSize = 0.5f;

    FClothLODLevel()
    {
        LODIndex = 0;
        ReductionPercentage = 0.5f;
        MaxVertices = 1000;
        ScreenSize = 0.5f;
    }
};

/**
 * Structure for cloth LOD data
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothLODData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    TArray<FClothLODLevel> LODLevels;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bAutoGenerateLODs = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevels = 4;

    FClothLODData()
    {
        bAutoGenerateLODs = true;
        MaxLODLevels = 4;
    }
};

/**
 * Structure for cloth generation parameters
 */
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FClothingGenerationParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "General")
    FString ClothingName = TEXT("DefaultCloth");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Mesh")
    FClothMeshData MeshData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bEnablePhysics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    FClothPhysicsData PhysicsData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    bool bEnableSimulation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Simulation")
    FClothSimulationData SimulationData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
    FClothMaterialData MaterialData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    bool bGenerateLODs = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    FClothLODData LODData;

    FClothingGenerationParameters()
    {
        ClothingName = TEXT("DefaultCloth");
        bEnablePhysics = true;
        bEnableSimulation = true;
        bGenerateLODs = false;
    }
};

/**
 * Clothing generation system for MetaHuman Bridge
 * Provides advanced clothing generation and simulation with UE5.6 Chaos Cloth APIs
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronClothingGeneration
{
public:
    FAuracronClothingGeneration();
    ~FAuracronClothingGeneration();

    // ========================================
    // Core Clothing Generation Methods
    // ========================================

    /**
     * Generate a clothing asset with the specified parameters
     * @param Parameters - Clothing generation parameters
     * @return Generated clothing asset or nullptr if failed
     */
    UClothingAssetBase* GenerateClothingAsset(const FClothingGenerationParameters& Parameters);

    /**
     * Attach clothing to a skeletal mesh
     * @param ClothingAsset - The clothing asset to attach
     * @param SkeletalMesh - The target skeletal mesh
     * @param BoneNames - Names of bones to attach to
     * @return True if successful
     */
    bool AttachClothToSkeleton(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<FString>& BoneNames);

    // ========================================
    // Cache Management
    // ========================================

    /**
     * Clear the clothing asset cache
     */
    void ClearClothingAssetCache();

    /**
     * Get cache memory usage in bytes
     */
    int32 GetClothingAssetCacheMemoryUsage() const { return ClothingAssetCacheMemoryUsage; }

    /**
     * Get total clothing generation time
     */
    float GetTotalClothingGenerationTime() const { return TotalClothingGenerationTime; }

private:
    // ========================================
    // Internal Implementation Methods
    // ========================================

    bool InitializeClothingAsset(UClothingAssetBase* ClothingAsset, const FClothingGenerationParameters& Parameters);
    bool GenerateClothMesh(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData);
    bool SetupClothPhysics(UClothingAssetBase* ClothingAsset, const FClothPhysicsData& PhysicsData);
    bool ConfigureClothSimulation(UClothingAssetBase* ClothingAsset, const FClothSimulationData& SimulationData);
    bool ApplyClothMaterials(UClothingAssetBase* ClothingAsset, const FClothMaterialData& MaterialData);
    bool GenerateClothLODs(UClothingAssetBase* ClothingAsset, const FClothLODData& LODData);

    // Helper methods
    bool ValidateClothingGenerationParameters(const FClothingGenerationParameters& Parameters, FString& OutError);
    FString CalculateClothingGenerationHash(const FClothingGenerationParameters& Parameters);
    bool GenerateClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs);
    bool GenerateClothIndices(const FClothMeshData& MeshData, const TArray<FVector3f>& Vertices, TArray<uint32>& OutIndices);
    bool GenerateShirtVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs);
    bool GeneratePantsVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs);
    bool GenerateCustomClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs);
    bool GenerateClothConstraints(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData);
    bool CreateClothBinding(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<int32>& BoneIndices);
    void OptimizeClothPerformance(UClothingAssetBase* ClothingAsset);
    void BuildClothingAsset(UClothingAssetBase* ClothingAsset);
    UMaterialInterface* GetDefaultClothMaterial();
    void UpdateClothingAssetCacheStats();
    int32 EstimateClothingAssetMemoryUsage(UClothingAssetBase* ClothingAsset);
    void UpdateClothingGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess);

    // ========================================
    // Member Variables
    // ========================================

    mutable FCriticalSection ClothingGenerationMutex;
    TMap<FString, TWeakObjectPtr<UClothingAssetBase>> ClothingAssetCache;
    TMap<FString, TWeakObjectPtr<UMaterialInstanceDynamic>> ClothMaterialCache;
    TMap<FString, FString> ClothingGenerationStats;
    int32 ClothingAssetCacheMemoryUsage;
    float TotalClothingGenerationTime;
};

#include "AuracronClothingGeneration.generated.h"
