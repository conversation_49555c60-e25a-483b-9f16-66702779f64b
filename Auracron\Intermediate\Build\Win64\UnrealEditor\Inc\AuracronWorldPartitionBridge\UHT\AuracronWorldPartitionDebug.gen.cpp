// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionDebug.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionDebug() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugCellInfo();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugConfiguration();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugPerformanceData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronCellStreamingState ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCellStreamingState;
static UEnum* EAuracronCellStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCellStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCellStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronCellStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronCellStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronCellStreamingState>()
{
	return EAuracronCellStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// =============================================================================\n// DEBUG TYPES AND ENUMS\n// =============================================================================\n" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EAuracronCellStreamingState::Error" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronCellStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronCellStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronCellStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
		{ "Preloaded.DisplayName", "Preloaded" },
		{ "Preloaded.Name", "EAuracronCellStreamingState::Preloaded" },
		{ "Preloading.DisplayName", "Preloading" },
		{ "Preloading.Name", "EAuracronCellStreamingState::Preloading" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "DEBUG TYPES AND ENUMS" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronCellStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronCellStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCellStreamingState::Unloaded", (int64)EAuracronCellStreamingState::Unloaded },
		{ "EAuracronCellStreamingState::Loading", (int64)EAuracronCellStreamingState::Loading },
		{ "EAuracronCellStreamingState::Loaded", (int64)EAuracronCellStreamingState::Loaded },
		{ "EAuracronCellStreamingState::Unloading", (int64)EAuracronCellStreamingState::Unloading },
		{ "EAuracronCellStreamingState::Error", (int64)EAuracronCellStreamingState::Error },
		{ "EAuracronCellStreamingState::Failed", (int64)EAuracronCellStreamingState::Failed },
		{ "EAuracronCellStreamingState::Preloading", (int64)EAuracronCellStreamingState::Preloading },
		{ "EAuracronCellStreamingState::Preloaded", (int64)EAuracronCellStreamingState::Preloaded },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronCellStreamingState",
	"EAuracronCellStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronCellStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCellStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCellStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronCellStreamingState *************************************************

// ********** Begin Enum EAuracronDebugVisualizationMode *******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode;
static UEnum* EAuracronDebugVisualizationMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDebugVisualizationMode"));
	}
	return Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugVisualizationMode>()
{
	return EAuracronDebugVisualizationMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "ActorDistribution.DisplayName", "Actor Distribution" },
		{ "ActorDistribution.Name", "EAuracronDebugVisualizationMode::ActorDistribution" },
		{ "All.DisplayName", "All" },
		{ "All.Name", "EAuracronDebugVisualizationMode::All" },
		{ "BlueprintType", "true" },
		{ "CellBounds.DisplayName", "Cell Bounds" },
		{ "CellBounds.Name", "EAuracronDebugVisualizationMode::CellBounds" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization modes\n" },
#endif
		{ "LoadingProgress.DisplayName", "Loading Progress" },
		{ "LoadingProgress.Name", "EAuracronDebugVisualizationMode::LoadingProgress" },
		{ "MemoryUsage.DisplayName", "Memory Usage" },
		{ "MemoryUsage.Name", "EAuracronDebugVisualizationMode::MemoryUsage" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronDebugVisualizationMode::None" },
		{ "PerformanceMetrics.DisplayName", "Performance Metrics" },
		{ "PerformanceMetrics.Name", "EAuracronDebugVisualizationMode::PerformanceMetrics" },
		{ "StreamingStates.DisplayName", "Streaming States" },
		{ "StreamingStates.Name", "EAuracronDebugVisualizationMode::StreamingStates" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization modes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDebugVisualizationMode::None", (int64)EAuracronDebugVisualizationMode::None },
		{ "EAuracronDebugVisualizationMode::CellBounds", (int64)EAuracronDebugVisualizationMode::CellBounds },
		{ "EAuracronDebugVisualizationMode::StreamingStates", (int64)EAuracronDebugVisualizationMode::StreamingStates },
		{ "EAuracronDebugVisualizationMode::LoadingProgress", (int64)EAuracronDebugVisualizationMode::LoadingProgress },
		{ "EAuracronDebugVisualizationMode::PerformanceMetrics", (int64)EAuracronDebugVisualizationMode::PerformanceMetrics },
		{ "EAuracronDebugVisualizationMode::ActorDistribution", (int64)EAuracronDebugVisualizationMode::ActorDistribution },
		{ "EAuracronDebugVisualizationMode::MemoryUsage", (int64)EAuracronDebugVisualizationMode::MemoryUsage },
		{ "EAuracronDebugVisualizationMode::All", (int64)EAuracronDebugVisualizationMode::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDebugVisualizationMode",
	"EAuracronDebugVisualizationMode",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode.InnerSingleton;
}
// ********** End Enum EAuracronDebugVisualizationMode *********************************************

// ********** Begin Enum EAuracronDebugInfoLevel ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDebugInfoLevel;
static UEnum* EAuracronDebugInfoLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDebugInfoLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugInfoLevel>()
{
	return EAuracronDebugInfoLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EAuracronDebugInfoLevel::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug info levels\n" },
#endif
		{ "Detailed.DisplayName", "Detailed" },
		{ "Detailed.Name", "EAuracronDebugInfoLevel::Detailed" },
		{ "Expert.DisplayName", "Expert" },
		{ "Expert.Name", "EAuracronDebugInfoLevel::Expert" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug info levels" },
#endif
		{ "Verbose.DisplayName", "Verbose" },
		{ "Verbose.Name", "EAuracronDebugInfoLevel::Verbose" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDebugInfoLevel::Basic", (int64)EAuracronDebugInfoLevel::Basic },
		{ "EAuracronDebugInfoLevel::Detailed", (int64)EAuracronDebugInfoLevel::Detailed },
		{ "EAuracronDebugInfoLevel::Verbose", (int64)EAuracronDebugInfoLevel::Verbose },
		{ "EAuracronDebugInfoLevel::Expert", (int64)EAuracronDebugInfoLevel::Expert },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDebugInfoLevel",
	"EAuracronDebugInfoLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDebugInfoLevel.InnerSingleton;
}
// ********** End Enum EAuracronDebugInfoLevel *****************************************************

// ********** Begin Enum EAuracronDebugColorScheme *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronDebugColorScheme;
static UEnum* EAuracronDebugColorScheme_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugColorScheme.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronDebugColorScheme.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronDebugColorScheme"));
	}
	return Z_Registration_Info_UEnum_EAuracronDebugColorScheme.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronDebugColorScheme>()
{
	return EAuracronDebugColorScheme_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ColorBlind.DisplayName", "Color Blind Friendly" },
		{ "ColorBlind.Name", "EAuracronDebugColorScheme::ColorBlind" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug color schemes\n" },
#endif
		{ "Default.DisplayName", "Default" },
		{ "Default.Name", "EAuracronDebugColorScheme::Default" },
		{ "HighContrast.DisplayName", "High Contrast" },
		{ "HighContrast.Name", "EAuracronDebugColorScheme::HighContrast" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
		{ "Monochrome.DisplayName", "Monochrome" },
		{ "Monochrome.Name", "EAuracronDebugColorScheme::Monochrome" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug color schemes" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronDebugColorScheme::Default", (int64)EAuracronDebugColorScheme::Default },
		{ "EAuracronDebugColorScheme::HighContrast", (int64)EAuracronDebugColorScheme::HighContrast },
		{ "EAuracronDebugColorScheme::ColorBlind", (int64)EAuracronDebugColorScheme::ColorBlind },
		{ "EAuracronDebugColorScheme::Monochrome", (int64)EAuracronDebugColorScheme::Monochrome },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronDebugColorScheme",
	"EAuracronDebugColorScheme",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme()
{
	if (!Z_Registration_Info_UEnum_EAuracronDebugColorScheme.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronDebugColorScheme.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronDebugColorScheme.InnerSingleton;
}
// ********** End Enum EAuracronDebugColorScheme ***************************************************

// ********** Begin ScriptStruct FAuracronDebugConfiguration ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration;
class UScriptStruct* FAuracronDebugConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDebugConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDebugConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Debug Configuration\n * Configuration settings for World Partition debugging tools\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug Configuration\nConfiguration settings for World Partition debugging tools" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugVisualization_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableStreamingDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceProfiler_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWorldPartitionInspector_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationMode_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InfoLevel_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorScheme_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugDrawDistance_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DebugTextScale_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowCellBounds_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowStreamingStates_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowLoadingProgress_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPerformanceMetrics_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowActorCounts_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowMemoryUsage_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProfilerUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxProfilerSamples_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedProfiling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInspectorWindow_MetaData[] = {
		{ "Category", "Inspector" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoRefreshInspector_MetaData[] = {
		{ "Category", "Inspector" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InspectorRefreshRate_MetaData[] = {
		{ "Category", "Inspector" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugLogging_MetaData[] = {
		{ "Category", "Logging" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogStreamingEvents_MetaData[] = {
		{ "Category", "Logging" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogPerformanceWarnings_MetaData[] = {
		{ "Category", "Logging" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDebugVisualization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugVisualization;
	static void NewProp_bEnableStreamingDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableStreamingDebug;
	static void NewProp_bEnablePerformanceProfiler_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceProfiler;
	static void NewProp_bEnableWorldPartitionInspector_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWorldPartitionInspector;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VisualizationMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VisualizationMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InfoLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InfoLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ColorScheme_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ColorScheme;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DebugDrawDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DebugTextScale;
	static void NewProp_bShowCellBounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowCellBounds;
	static void NewProp_bShowStreamingStates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowStreamingStates;
	static void NewProp_bShowLoadingProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowLoadingProgress;
	static void NewProp_bShowPerformanceMetrics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPerformanceMetrics;
	static void NewProp_bShowActorCounts_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowActorCounts;
	static void NewProp_bShowMemoryUsage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowMemoryUsage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ProfilerUpdateInterval;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxProfilerSamples;
	static void NewProp_bEnableDetailedProfiling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedProfiling;
	static void NewProp_bShowInspectorWindow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInspectorWindow;
	static void NewProp_bAutoRefreshInspector_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoRefreshInspector;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InspectorRefreshRate;
	static void NewProp_bEnableDebugLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugLogging;
	static void NewProp_bLogStreamingEvents_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogStreamingEvents;
	static void NewProp_bLogPerformanceWarnings_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogPerformanceWarnings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDebugConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnableDebugVisualization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugVisualization = { "bEnableDebugVisualization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugVisualization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugVisualization_MetaData), NewProp_bEnableDebugVisualization_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnableStreamingDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableStreamingDebug = { "bEnableStreamingDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableStreamingDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableStreamingDebug_MetaData), NewProp_bEnableStreamingDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnablePerformanceProfiler_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnablePerformanceProfiler = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnablePerformanceProfiler = { "bEnablePerformanceProfiler", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnablePerformanceProfiler_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceProfiler_MetaData), NewProp_bEnablePerformanceProfiler_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableWorldPartitionInspector_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnableWorldPartitionInspector = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableWorldPartitionInspector = { "bEnableWorldPartitionInspector", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableWorldPartitionInspector_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWorldPartitionInspector_MetaData), NewProp_bEnableWorldPartitionInspector_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_VisualizationMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_VisualizationMode = { "VisualizationMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, VisualizationMode), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationMode_MetaData), NewProp_VisualizationMode_MetaData) }; // 2443708541
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InfoLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InfoLevel = { "InfoLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, InfoLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InfoLevel_MetaData), NewProp_InfoLevel_MetaData) }; // 3717273309
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ColorScheme_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ColorScheme = { "ColorScheme", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, ColorScheme), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorScheme_MetaData), NewProp_ColorScheme_MetaData) }; // 2462672971
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_DebugDrawDistance = { "DebugDrawDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, DebugDrawDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugDrawDistance_MetaData), NewProp_DebugDrawDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_DebugTextScale = { "DebugTextScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, DebugTextScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DebugTextScale_MetaData), NewProp_DebugTextScale_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowCellBounds_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowCellBounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowCellBounds = { "bShowCellBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowCellBounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowCellBounds_MetaData), NewProp_bShowCellBounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowStreamingStates_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowStreamingStates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowStreamingStates = { "bShowStreamingStates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowStreamingStates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowStreamingStates_MetaData), NewProp_bShowStreamingStates_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowLoadingProgress_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowLoadingProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowLoadingProgress = { "bShowLoadingProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowLoadingProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowLoadingProgress_MetaData), NewProp_bShowLoadingProgress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowPerformanceMetrics_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowPerformanceMetrics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowPerformanceMetrics = { "bShowPerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowPerformanceMetrics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPerformanceMetrics_MetaData), NewProp_bShowPerformanceMetrics_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowActorCounts_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowActorCounts = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowActorCounts = { "bShowActorCounts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowActorCounts_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowActorCounts_MetaData), NewProp_bShowActorCounts_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowMemoryUsage_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowMemoryUsage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowMemoryUsage = { "bShowMemoryUsage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowMemoryUsage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowMemoryUsage_MetaData), NewProp_bShowMemoryUsage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ProfilerUpdateInterval = { "ProfilerUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, ProfilerUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProfilerUpdateInterval_MetaData), NewProp_ProfilerUpdateInterval_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_MaxProfilerSamples = { "MaxProfilerSamples", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, MaxProfilerSamples), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxProfilerSamples_MetaData), NewProp_MaxProfilerSamples_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDetailedProfiling_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnableDetailedProfiling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDetailedProfiling = { "bEnableDetailedProfiling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDetailedProfiling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedProfiling_MetaData), NewProp_bEnableDetailedProfiling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowInspectorWindow_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bShowInspectorWindow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowInspectorWindow = { "bShowInspectorWindow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowInspectorWindow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInspectorWindow_MetaData), NewProp_bShowInspectorWindow_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bAutoRefreshInspector_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bAutoRefreshInspector = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bAutoRefreshInspector = { "bAutoRefreshInspector", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bAutoRefreshInspector_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoRefreshInspector_MetaData), NewProp_bAutoRefreshInspector_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InspectorRefreshRate = { "InspectorRefreshRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugConfiguration, InspectorRefreshRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InspectorRefreshRate_MetaData), NewProp_InspectorRefreshRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugLogging_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bEnableDebugLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugLogging = { "bEnableDebugLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugLogging_MetaData), NewProp_bEnableDebugLogging_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogStreamingEvents_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bLogStreamingEvents = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogStreamingEvents = { "bLogStreamingEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogStreamingEvents_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogStreamingEvents_MetaData), NewProp_bLogStreamingEvents_MetaData) };
void Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogPerformanceWarnings_SetBit(void* Obj)
{
	((FAuracronDebugConfiguration*)Obj)->bLogPerformanceWarnings = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogPerformanceWarnings = { "bLogPerformanceWarnings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronDebugConfiguration), &Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogPerformanceWarnings_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogPerformanceWarnings_MetaData), NewProp_bLogPerformanceWarnings_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugVisualization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableStreamingDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnablePerformanceProfiler,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableWorldPartitionInspector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_VisualizationMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_VisualizationMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InfoLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InfoLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ColorScheme_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ColorScheme,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_DebugDrawDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_DebugTextScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowCellBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowStreamingStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowLoadingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowPerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowActorCounts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowMemoryUsage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_ProfilerUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_MaxProfilerSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDetailedProfiling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bShowInspectorWindow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bAutoRefreshInspector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_InspectorRefreshRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bEnableDebugLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogStreamingEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewProp_bLogPerformanceWarnings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDebugConfiguration",
	Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::PropPointers),
	sizeof(FAuracronDebugConfiguration),
	alignof(FAuracronDebugConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDebugConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDebugCellInfo ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo;
class UScriptStruct* FAuracronDebugCellInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDebugCellInfo, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDebugCellInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Debug Cell Info\n * Debug information for a single world partition cell\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug Cell Info\nDebug information for a single world partition cell" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellBounds_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingProgress_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorCount_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToViewer_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContainedActors_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMetrics_MetaData[] = {
		{ "Category", "Debug Cell Info" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CellBounds;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToViewer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ContainedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ContainedActors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PerformanceMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDebugCellInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_CellBounds = { "CellBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, CellBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellBounds_MetaData), NewProp_CellBounds_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronCellStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 3879821968
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_LoadingProgress = { "LoadingProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, LoadingProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingProgress_MetaData), NewProp_LoadingProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ActorCount = { "ActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, ActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorCount_MetaData), NewProp_ActorCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_DistanceToViewer = { "DistanceToViewer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, DistanceToViewer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToViewer_MetaData), NewProp_DistanceToViewer_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ContainedActors_Inner = { "ContainedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ContainedActors = { "ContainedActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, ContainedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContainedActors_MetaData), NewProp_ContainedActors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics_ValueProp = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics_Key_KeyProp = { "PerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics = { "PerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugCellInfo, PerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMetrics_MetaData), NewProp_PerformanceMetrics_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_CellBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_LoadingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ActorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_DistanceToViewer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_LastAccessTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ContainedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_ContainedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewProp_PerformanceMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDebugCellInfo",
	Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::PropPointers),
	sizeof(FAuracronDebugCellInfo),
	alignof(FAuracronDebugCellInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugCellInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDebugCellInfo **********************************************

// ********** Begin ScriptStruct FAuracronDebugPerformanceData *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData;
class UScriptStruct* FAuracronDebugPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronDebugPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronDebugPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Debug Performance Data\n * Performance profiling data for World Partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug Performance Data\nPerformance profiling data for World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrameTime_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingTime_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingTime_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingTime_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedCells_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingCells_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingBandwidthMBps_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalActors_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Debug Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedCells;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingCells;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingBandwidthMBps;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronDebugPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, FrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrameTime_MetaData), NewProp_FrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingTime = { "StreamingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, StreamingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingTime_MetaData), NewProp_StreamingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_LoadingTime = { "LoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, LoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingTime_MetaData), NewProp_LoadingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_UnloadingTime = { "UnloadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, UnloadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingTime_MetaData), NewProp_UnloadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_LoadedCells = { "LoadedCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, LoadedCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedCells_MetaData), NewProp_LoadedCells_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingCells = { "StreamingCells", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, StreamingCells), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingCells_MetaData), NewProp_StreamingCells_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingBandwidthMBps = { "StreamingBandwidthMBps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, StreamingBandwidthMBps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingBandwidthMBps_MetaData), NewProp_StreamingBandwidthMBps_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_TotalActors = { "TotalActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, TotalActors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalActors_MetaData), NewProp_TotalActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronDebugPerformanceData, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_LoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_UnloadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_LoadedCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingCells,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_StreamingBandwidthMBps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_TotalActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronDebugPerformanceData",
	Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::PropPointers),
	sizeof(FAuracronDebugPerformanceData),
	alignof(FAuracronDebugPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronDebugPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronDebugPerformanceData ***************************************

// ********** Begin Delegate FOnDebugEvent *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics
{
	struct AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms
	{
		FString EventType;
		FString Details;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Details;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms, EventType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::NewProp_Details = { "Details", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms, Details), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::NewProp_Details,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "OnDebugEvent__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionDebugManager::FOnDebugEvent_DelegateWrapper(const FMulticastScriptDelegate& OnDebugEvent, const FString& EventType, const FString& Details)
{
	struct AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms
	{
		FString EventType;
		FString Details;
	};
	AuracronWorldPartitionDebugManager_eventOnDebugEvent_Parms Parms;
	Parms.EventType=EventType;
	Parms.Details=Details;
	OnDebugEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnDebugEvent ***********************************************************

// ********** Begin Delegate FOnPerformanceAlert ***************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics
{
	struct AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms
	{
		FAuracronDebugPerformanceData PerformanceData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_PerformanceData = { "PerformanceData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms, PerformanceData), Z_Construct_UScriptStruct_FAuracronDebugPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2293270951
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::NewProp_PerformanceData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "OnPerformanceAlert__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionDebugManager::FOnPerformanceAlert_DelegateWrapper(const FMulticastScriptDelegate& OnPerformanceAlert, FAuracronDebugPerformanceData PerformanceData)
{
	struct AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms
	{
		FAuracronDebugPerformanceData PerformanceData;
	};
	AuracronWorldPartitionDebugManager_eventOnPerformanceAlert_Parms Parms;
	Parms.PerformanceData=PerformanceData;
	OnPerformanceAlert.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPerformanceAlert *****************************************************

// ********** Begin Delegate FOnStreamingEvent *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics
{
	struct AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms
	{
		FString CellId;
		FString EventDetails;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventDetails;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms, CellId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::NewProp_EventDetails = { "EventDetails", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms, EventDetails), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::NewProp_EventDetails,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "OnStreamingEvent__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionDebugManager::FOnStreamingEvent_DelegateWrapper(const FMulticastScriptDelegate& OnStreamingEvent, const FString& CellId, const FString& EventDetails)
{
	struct AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms
	{
		FString CellId;
		FString EventDetails;
	};
	AuracronWorldPartitionDebugManager_eventOnStreamingEvent_Parms Parms;
	Parms.CellId=CellId;
	Parms.EventDetails=EventDetails;
	OnStreamingEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnStreamingEvent *******************************************************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function ClearStreamingEventLog ******
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "ClearStreamingEventLog", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execClearStreamingEventLog)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearStreamingEventLog();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function ClearStreamingEventLog ********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawCellBounds **************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawCellBounds_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell debugging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell debugging" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawCellBounds_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawCellBounds", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::AuracronWorldPartitionDebugManager_eventDrawCellBounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::AuracronWorldPartitionDebugManager_eventDrawCellBounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawCellBounds)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawCellBounds(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawCellBounds ****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawDebugVisualization ******
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawDebugVisualization_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawDebugVisualization_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawDebugVisualization", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::AuracronWorldPartitionDebugManager_eventDrawDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::AuracronWorldPartitionDebugManager_eventDrawDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawDebugVisualization)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugVisualization(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawDebugVisualization ********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawInspectorWindow *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawInspectorWindow_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawInspectorWindow_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawInspectorWindow", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::AuracronWorldPartitionDebugManager_eventDrawInspectorWindow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::AuracronWorldPartitionDebugManager_eventDrawInspectorWindow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawInspectorWindow)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawInspectorWindow(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawInspectorWindow ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawLoadingProgress *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawLoadingProgress_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawLoadingProgress_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawLoadingProgress", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::AuracronWorldPartitionDebugManager_eventDrawLoadingProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::AuracronWorldPartitionDebugManager_eventDrawLoadingProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawLoadingProgress)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawLoadingProgress(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawLoadingProgress ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawPerformanceMetrics ******
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawPerformanceMetrics_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawPerformanceMetrics_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawPerformanceMetrics", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::AuracronWorldPartitionDebugManager_eventDrawPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::AuracronWorldPartitionDebugManager_eventDrawPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawPerformanceMetrics)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawPerformanceMetrics(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawPerformanceMetrics ********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function DrawStreamingStates *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics
{
	struct AuracronWorldPartitionDebugManager_eventDrawStreamingStates_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventDrawStreamingStates_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "DrawStreamingStates", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::AuracronWorldPartitionDebugManager_eventDrawStreamingStates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::AuracronWorldPartitionDebugManager_eventDrawStreamingStates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execDrawStreamingStates)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawStreamingStates(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function DrawStreamingStates ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function EnableDebugVisualization ****
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics
{
	struct AuracronWorldPartitionDebugManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::AuracronWorldPartitionDebugManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::AuracronWorldPartitionDebugManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function EnableDebugVisualization ******

// ********** Begin Class UAuracronWorldPartitionDebugManager Function EnablePerformanceProfiler ***
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics
{
	struct AuracronWorldPartitionDebugManager_eventEnablePerformanceProfiler_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance profiling\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance profiling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventEnablePerformanceProfiler_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventEnablePerformanceProfiler_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "EnablePerformanceProfiler", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::AuracronWorldPartitionDebugManager_eventEnablePerformanceProfiler_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::AuracronWorldPartitionDebugManager_eventEnablePerformanceProfiler_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execEnablePerformanceProfiler)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnablePerformanceProfiler(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function EnablePerformanceProfiler *****

// ********** Begin Class UAuracronWorldPartitionDebugManager Function EnableStreamingDebug ********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics
{
	struct AuracronWorldPartitionDebugManager_eventEnableStreamingDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Streaming debugging\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Streaming debugging" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventEnableStreamingDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventEnableStreamingDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "EnableStreamingDebug", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::AuracronWorldPartitionDebugManager_eventEnableStreamingDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::AuracronWorldPartitionDebugManager_eventEnableStreamingDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execEnableStreamingDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableStreamingDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function EnableStreamingDebug **********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function EnableWorldPartitionInspector 
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics
{
	struct AuracronWorldPartitionDebugManager_eventEnableWorldPartitionInspector_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World Partition Inspector\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Inspector" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventEnableWorldPartitionInspector_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventEnableWorldPartitionInspector_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "EnableWorldPartitionInspector", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::AuracronWorldPartitionDebugManager_eventEnableWorldPartitionInspector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::AuracronWorldPartitionDebugManager_eventEnableWorldPartitionInspector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execEnableWorldPartitionInspector)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableWorldPartitionInspector(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function EnableWorldPartitionInspector *

// ********** Begin Class UAuracronWorldPartitionDebugManager Function ExecuteDebugCommand *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics
{
	struct AuracronWorldPartitionDebugManager_eventExecuteDebugCommand_Parms
	{
		FString Command;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug commands\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug commands" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Command;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventExecuteDebugCommand_Parms, Command), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::NewProp_Command,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "ExecuteDebugCommand", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::AuracronWorldPartitionDebugManager_eventExecuteDebugCommand_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::AuracronWorldPartitionDebugManager_eventExecuteDebugCommand_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execExecuteDebugCommand)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Command);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteDebugCommand(Z_Param_Command);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function ExecuteDebugCommand ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function ExportDebugData *************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics
{
	struct AuracronWorldPartitionDebugManager_eventExportDebugData_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventExportDebugData_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "ExportDebugData", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::AuracronWorldPartitionDebugManager_eventExportDebugData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::AuracronWorldPartitionDebugManager_eventExportDebugData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execExportDebugData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportDebugData(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function ExportDebugData ***************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetAllCellDebugInfo *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetAllCellDebugInfo_Parms
	{
		TArray<FAuracronDebugCellInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDebugCellInfo, METADATA_PARAMS(0, nullptr) }; // 2262776029
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetAllCellDebugInfo_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2262776029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetAllCellDebugInfo", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::AuracronWorldPartitionDebugManager_eventGetAllCellDebugInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::AuracronWorldPartitionDebugManager_eventGetAllCellDebugInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetAllCellDebugInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDebugCellInfo>*)Z_Param__Result=P_THIS->GetAllCellDebugInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetAllCellDebugInfo ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetAvailableDebugCommands ***
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetAvailableDebugCommands_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetAvailableDebugCommands_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetAvailableDebugCommands", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::AuracronWorldPartitionDebugManager_eventGetAvailableDebugCommands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::AuracronWorldPartitionDebugManager_eventGetAvailableDebugCommands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetAvailableDebugCommands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAvailableDebugCommands();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetAvailableDebugCommands *****

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetCellDebugInfo ************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetCellDebugInfo_Parms
	{
		FString CellId;
		FAuracronDebugCellInfo ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetCellDebugInfo_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetCellDebugInfo_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDebugCellInfo, METADATA_PARAMS(0, nullptr) }; // 2262776029
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetCellDebugInfo", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::AuracronWorldPartitionDebugManager_eventGetCellDebugInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::AuracronWorldPartitionDebugManager_eventGetCellDebugInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetCellDebugInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDebugCellInfo*)Z_Param__Result=P_THIS->GetCellDebugInfo(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetCellDebugInfo **************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetColorScheme **************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetColorScheme_Parms
	{
		EAuracronDebugColorScheme ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetColorScheme_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme, METADATA_PARAMS(0, nullptr) }; // 2462672971
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetColorScheme", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::AuracronWorldPartitionDebugManager_eventGetColorScheme_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::AuracronWorldPartitionDebugManager_eventGetColorScheme_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetColorScheme)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronDebugColorScheme*)Z_Param__Result=P_THIS->GetColorScheme();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetColorScheme ****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetConfiguration_Parms
	{
		FAuracronDebugConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDebugConfiguration, METADATA_PARAMS(0, nullptr) }; // 3233916620
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::AuracronWorldPartitionDebugManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::AuracronWorldPartitionDebugManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDebugConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetCurrentPerformanceData ***
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetCurrentPerformanceData_Parms
	{
		FAuracronDebugPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetCurrentPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronDebugPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2293270951
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetCurrentPerformanceData", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::AuracronWorldPartitionDebugManager_eventGetCurrentPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::AuracronWorldPartitionDebugManager_eventGetCurrentPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetCurrentPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronDebugPerformanceData*)Z_Param__Result=P_THIS->GetCurrentPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetCurrentPerformanceData *****

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetDebugColor ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetDebugColor_Parms
	{
		FString ColorName;
		FColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::NewProp_ColorName = { "ColorName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetDebugColor_Parms, ColorName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorName_MetaData), NewProp_ColorName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetDebugColor_Parms, ReturnValue), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::NewProp_ColorName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetDebugColor", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::AuracronWorldPartitionDebugManager_eventGetDebugColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::AuracronWorldPartitionDebugManager_eventGetDebugColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetDebugColor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ColorName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FColor*)Z_Param__Result=P_THIS->GetDebugColor(Z_Param_ColorName);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetDebugColor *****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetDebugCommandHelp *********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetDebugCommandHelp_Parms
	{
		FString Command;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Command_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Command;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::NewProp_Command = { "Command", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetDebugCommandHelp_Parms, Command), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Command_MetaData), NewProp_Command_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetDebugCommandHelp_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::NewProp_Command,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetDebugCommandHelp", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::AuracronWorldPartitionDebugManager_eventGetDebugCommandHelp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::AuracronWorldPartitionDebugManager_eventGetDebugCommandHelp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetDebugCommandHelp)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Command);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetDebugCommandHelp(Z_Param_Command);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetDebugCommandHelp ***********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetDebugInfoLevel ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetDebugInfoLevel_Parms
	{
		EAuracronDebugInfoLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetDebugInfoLevel_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel, METADATA_PARAMS(0, nullptr) }; // 3717273309
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetDebugInfoLevel", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::AuracronWorldPartitionDebugManager_eventGetDebugInfoLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::AuracronWorldPartitionDebugManager_eventGetDebugInfoLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetDebugInfoLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronDebugInfoLevel*)Z_Param__Result=P_THIS->GetDebugInfoLevel();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetDebugInfoLevel *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetInspectorData ************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetInspectorData_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetInspectorData_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetInspectorData", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::AuracronWorldPartitionDebugManager_eventGetInspectorData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::AuracronWorldPartitionDebugManager_eventGetInspectorData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetInspectorData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetInspectorData();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetInspectorData **************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetInstance *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionDebugManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::AuracronWorldPartitionDebugManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::AuracronWorldPartitionDebugManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionDebugManager**)Z_Param__Result=UAuracronWorldPartitionDebugManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetInstance *******************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetPerformanceHistory *******
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetPerformanceHistory_Parms
	{
		int32 MaxSamples;
		TArray<FAuracronDebugPerformanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "CPP_Default_MaxSamples", "100" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSamples;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_MaxSamples = { "MaxSamples", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetPerformanceHistory_Parms, MaxSamples), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronDebugPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2293270951
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetPerformanceHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2293270951
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_MaxSamples,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetPerformanceHistory", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::AuracronWorldPartitionDebugManager_eventGetPerformanceHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::AuracronWorldPartitionDebugManager_eventGetPerformanceHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetPerformanceHistory)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxSamples);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronDebugPerformanceData>*)Z_Param__Result=P_THIS->GetPerformanceHistory(Z_Param_MaxSamples);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetPerformanceHistory *********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetStreamingEventLog ********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetStreamingEventLog_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetStreamingEventLog_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetStreamingEventLog", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::AuracronWorldPartitionDebugManager_eventGetStreamingEventLog_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::AuracronWorldPartitionDebugManager_eventGetStreamingEventLog_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetStreamingEventLog)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingEventLog();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetStreamingEventLog **********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function GetVisualizationMode ********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics
{
	struct AuracronWorldPartitionDebugManager_eventGetVisualizationMode_Parms
	{
		EAuracronDebugVisualizationMode ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventGetVisualizationMode_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode, METADATA_PARAMS(0, nullptr) }; // 2443708541
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "GetVisualizationMode", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::AuracronWorldPartitionDebugManager_eventGetVisualizationMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::AuracronWorldPartitionDebugManager_eventGetVisualizationMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execGetVisualizationMode)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronDebugVisualizationMode*)Z_Param__Result=P_THIS->GetVisualizationMode();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function GetVisualizationMode **********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function Initialize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics
{
	struct AuracronWorldPartitionDebugManager_eventInitialize_Parms
	{
		FAuracronDebugConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronDebugConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3233916620
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::AuracronWorldPartitionDebugManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::AuracronWorldPartitionDebugManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronDebugConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function Initialize ********************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function IsDebugVisualizationEnabled *
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronWorldPartitionDebugManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function IsDebugVisualizationEnabled ***

// ********** Begin Class UAuracronWorldPartitionDebugManager Function IsInitialized ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionDebugManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::AuracronWorldPartitionDebugManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::AuracronWorldPartitionDebugManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function IsInitialized *****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function IsPerformanceProfilerEnabled 
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics
{
	struct AuracronWorldPartitionDebugManager_eventIsPerformanceProfilerEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventIsPerformanceProfilerEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventIsPerformanceProfilerEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "IsPerformanceProfilerEnabled", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsPerformanceProfilerEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsPerformanceProfilerEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execIsPerformanceProfilerEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPerformanceProfilerEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function IsPerformanceProfilerEnabled **

// ********** Begin Class UAuracronWorldPartitionDebugManager Function IsStreamingDebugEnabled *****
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics
{
	struct AuracronWorldPartitionDebugManager_eventIsStreamingDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventIsStreamingDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventIsStreamingDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "IsStreamingDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsStreamingDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsStreamingDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execIsStreamingDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsStreamingDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function IsStreamingDebugEnabled *******

// ********** Begin Class UAuracronWorldPartitionDebugManager Function IsWorldPartitionInspectorEnabled 
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics
{
	struct AuracronWorldPartitionDebugManager_eventIsWorldPartitionInspectorEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventIsWorldPartitionInspectorEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventIsWorldPartitionInspectorEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "IsWorldPartitionInspectorEnabled", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsWorldPartitionInspectorEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::AuracronWorldPartitionDebugManager_eventIsWorldPartitionInspectorEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execIsWorldPartitionInspectorEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWorldPartitionInspectorEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function IsWorldPartitionInspectorEnabled 

// ********** Begin Class UAuracronWorldPartitionDebugManager Function LoadDebugSnapshot ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics
{
	struct AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "LoadDebugSnapshot", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::AuracronWorldPartitionDebugManager_eventLoadDebugSnapshot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execLoadDebugSnapshot)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadDebugSnapshot(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function LoadDebugSnapshot *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function LogStreamingEvent ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics
{
	struct AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms
	{
		FString EventType;
		FString CellId;
		FString Details;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Details_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Details;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_Details = { "Details", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms, Details), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Details_MetaData), NewProp_Details_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::NewProp_Details,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "LogStreamingEvent", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::AuracronWorldPartitionDebugManager_eventLogStreamingEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execLogStreamingEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventType);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_GET_PROPERTY(FStrProperty,Z_Param_Details);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogStreamingEvent(Z_Param_EventType,Z_Param_CellId,Z_Param_Details);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function LogStreamingEvent *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function RefreshInspectorData ********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "RefreshInspectorData", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execRefreshInspectorData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshInspectorData();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function RefreshInspectorData **********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function SaveDebugSnapshot ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics
{
	struct AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "SaveDebugSnapshot", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::AuracronWorldPartitionDebugManager_eventSaveDebugSnapshot_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execSaveDebugSnapshot)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveDebugSnapshot(Z_Param_FilePath);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function SaveDebugSnapshot *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function SetColorScheme **************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics
{
	struct AuracronWorldPartitionDebugManager_eventSetColorScheme_Parms
	{
		EAuracronDebugColorScheme Scheme;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Color schemes\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Color schemes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Scheme_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Scheme;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::NewProp_Scheme_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::NewProp_Scheme = { "Scheme", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventSetColorScheme_Parms, Scheme), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugColorScheme, METADATA_PARAMS(0, nullptr) }; // 2462672971
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::NewProp_Scheme_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::NewProp_Scheme,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "SetColorScheme", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::AuracronWorldPartitionDebugManager_eventSetColorScheme_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::AuracronWorldPartitionDebugManager_eventSetColorScheme_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execSetColorScheme)
{
	P_GET_ENUM(EAuracronDebugColorScheme,Z_Param_Scheme);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetColorScheme(EAuracronDebugColorScheme(Z_Param_Scheme));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function SetColorScheme ****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function SetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionDebugManager_eventSetConfiguration_Parms
	{
		FAuracronDebugConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronDebugConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3233916620
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::AuracronWorldPartitionDebugManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::AuracronWorldPartitionDebugManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronDebugConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function SetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function SetDebugInfoLevel ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics
{
	struct AuracronWorldPartitionDebugManager_eventSetDebugInfoLevel_Parms
	{
		EAuracronDebugInfoLevel Level;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug info levels\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug info levels" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Level_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Level;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::NewProp_Level_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventSetDebugInfoLevel_Parms, Level), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugInfoLevel, METADATA_PARAMS(0, nullptr) }; // 3717273309
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::NewProp_Level_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::NewProp_Level,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "SetDebugInfoLevel", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::AuracronWorldPartitionDebugManager_eventSetDebugInfoLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::AuracronWorldPartitionDebugManager_eventSetDebugInfoLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execSetDebugInfoLevel)
{
	P_GET_ENUM(EAuracronDebugInfoLevel,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDebugInfoLevel(EAuracronDebugInfoLevel(Z_Param_Level));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function SetDebugInfoLevel *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function SetVisualizationMode ********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics
{
	struct AuracronWorldPartitionDebugManager_eventSetVisualizationMode_Parms
	{
		EAuracronDebugVisualizationMode Mode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Mode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Mode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::NewProp_Mode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::NewProp_Mode = { "Mode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventSetVisualizationMode_Parms, Mode), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronDebugVisualizationMode, METADATA_PARAMS(0, nullptr) }; // 2443708541
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::NewProp_Mode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::NewProp_Mode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "SetVisualizationMode", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::AuracronWorldPartitionDebugManager_eventSetVisualizationMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::AuracronWorldPartitionDebugManager_eventSetVisualizationMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execSetVisualizationMode)
{
	P_GET_ENUM(EAuracronDebugVisualizationMode,Z_Param_Mode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualizationMode(EAuracronDebugVisualizationMode(Z_Param_Mode));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function SetVisualizationMode **********

// ********** Begin Class UAuracronWorldPartitionDebugManager Function Shutdown ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function Shutdown **********************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function StartProfiling **************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "StartProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execStartProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function StartProfiling ****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function StopProfiling ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "StopProfiling", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execStopProfiling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopProfiling();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function StopProfiling *****************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function TakeDebugSnapshot ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug utilities" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "TakeDebugSnapshot", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execTakeDebugSnapshot)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TakeDebugSnapshot();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function TakeDebugSnapshot *************

// ********** Begin Class UAuracronWorldPartitionDebugManager Function Tick ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics
{
	struct AuracronWorldPartitionDebugManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionDebugManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionDebugManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::AuracronWorldPartitionDebugManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::AuracronWorldPartitionDebugManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionDebugManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionDebugManager Function Tick **************************

// ********** Begin Class UAuracronWorldPartitionDebugManager **************************************
void UAuracronWorldPartitionDebugManager::StaticRegisterNativesUAuracronWorldPartitionDebugManager()
{
	UClass* Class = UAuracronWorldPartitionDebugManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearStreamingEventLog", &UAuracronWorldPartitionDebugManager::execClearStreamingEventLog },
		{ "DrawCellBounds", &UAuracronWorldPartitionDebugManager::execDrawCellBounds },
		{ "DrawDebugVisualization", &UAuracronWorldPartitionDebugManager::execDrawDebugVisualization },
		{ "DrawInspectorWindow", &UAuracronWorldPartitionDebugManager::execDrawInspectorWindow },
		{ "DrawLoadingProgress", &UAuracronWorldPartitionDebugManager::execDrawLoadingProgress },
		{ "DrawPerformanceMetrics", &UAuracronWorldPartitionDebugManager::execDrawPerformanceMetrics },
		{ "DrawStreamingStates", &UAuracronWorldPartitionDebugManager::execDrawStreamingStates },
		{ "EnableDebugVisualization", &UAuracronWorldPartitionDebugManager::execEnableDebugVisualization },
		{ "EnablePerformanceProfiler", &UAuracronWorldPartitionDebugManager::execEnablePerformanceProfiler },
		{ "EnableStreamingDebug", &UAuracronWorldPartitionDebugManager::execEnableStreamingDebug },
		{ "EnableWorldPartitionInspector", &UAuracronWorldPartitionDebugManager::execEnableWorldPartitionInspector },
		{ "ExecuteDebugCommand", &UAuracronWorldPartitionDebugManager::execExecuteDebugCommand },
		{ "ExportDebugData", &UAuracronWorldPartitionDebugManager::execExportDebugData },
		{ "GetAllCellDebugInfo", &UAuracronWorldPartitionDebugManager::execGetAllCellDebugInfo },
		{ "GetAvailableDebugCommands", &UAuracronWorldPartitionDebugManager::execGetAvailableDebugCommands },
		{ "GetCellDebugInfo", &UAuracronWorldPartitionDebugManager::execGetCellDebugInfo },
		{ "GetColorScheme", &UAuracronWorldPartitionDebugManager::execGetColorScheme },
		{ "GetConfiguration", &UAuracronWorldPartitionDebugManager::execGetConfiguration },
		{ "GetCurrentPerformanceData", &UAuracronWorldPartitionDebugManager::execGetCurrentPerformanceData },
		{ "GetDebugColor", &UAuracronWorldPartitionDebugManager::execGetDebugColor },
		{ "GetDebugCommandHelp", &UAuracronWorldPartitionDebugManager::execGetDebugCommandHelp },
		{ "GetDebugInfoLevel", &UAuracronWorldPartitionDebugManager::execGetDebugInfoLevel },
		{ "GetInspectorData", &UAuracronWorldPartitionDebugManager::execGetInspectorData },
		{ "GetInstance", &UAuracronWorldPartitionDebugManager::execGetInstance },
		{ "GetPerformanceHistory", &UAuracronWorldPartitionDebugManager::execGetPerformanceHistory },
		{ "GetStreamingEventLog", &UAuracronWorldPartitionDebugManager::execGetStreamingEventLog },
		{ "GetVisualizationMode", &UAuracronWorldPartitionDebugManager::execGetVisualizationMode },
		{ "Initialize", &UAuracronWorldPartitionDebugManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronWorldPartitionDebugManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionDebugManager::execIsInitialized },
		{ "IsPerformanceProfilerEnabled", &UAuracronWorldPartitionDebugManager::execIsPerformanceProfilerEnabled },
		{ "IsStreamingDebugEnabled", &UAuracronWorldPartitionDebugManager::execIsStreamingDebugEnabled },
		{ "IsWorldPartitionInspectorEnabled", &UAuracronWorldPartitionDebugManager::execIsWorldPartitionInspectorEnabled },
		{ "LoadDebugSnapshot", &UAuracronWorldPartitionDebugManager::execLoadDebugSnapshot },
		{ "LogStreamingEvent", &UAuracronWorldPartitionDebugManager::execLogStreamingEvent },
		{ "RefreshInspectorData", &UAuracronWorldPartitionDebugManager::execRefreshInspectorData },
		{ "SaveDebugSnapshot", &UAuracronWorldPartitionDebugManager::execSaveDebugSnapshot },
		{ "SetColorScheme", &UAuracronWorldPartitionDebugManager::execSetColorScheme },
		{ "SetConfiguration", &UAuracronWorldPartitionDebugManager::execSetConfiguration },
		{ "SetDebugInfoLevel", &UAuracronWorldPartitionDebugManager::execSetDebugInfoLevel },
		{ "SetVisualizationMode", &UAuracronWorldPartitionDebugManager::execSetVisualizationMode },
		{ "Shutdown", &UAuracronWorldPartitionDebugManager::execShutdown },
		{ "StartProfiling", &UAuracronWorldPartitionDebugManager::execStartProfiling },
		{ "StopProfiling", &UAuracronWorldPartitionDebugManager::execStopProfiling },
		{ "TakeDebugSnapshot", &UAuracronWorldPartitionDebugManager::execTakeDebugSnapshot },
		{ "Tick", &UAuracronWorldPartitionDebugManager::execTick },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager;
UClass* UAuracronWorldPartitionDebugManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionDebugManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionDebugManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionDebugManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager_NoRegister()
{
	return UAuracronWorldPartitionDebugManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Debug Manager\n * Central manager for World Partition debugging tools\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionDebug.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Debug Manager\nCentral manager for World Partition debugging tools" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDebugEvent_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPerformanceAlert_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnStreamingEvent_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionDebug.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDebugEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPerformanceAlert;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnStreamingEvent;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ClearStreamingEventLog, "ClearStreamingEventLog" }, // 2501146022
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawCellBounds, "DrawCellBounds" }, // 3806482958
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawDebugVisualization, "DrawDebugVisualization" }, // 2422917760
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawInspectorWindow, "DrawInspectorWindow" }, // 442452480
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawLoadingProgress, "DrawLoadingProgress" }, // 2710548373
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawPerformanceMetrics, "DrawPerformanceMetrics" }, // 4286348338
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_DrawStreamingStates, "DrawStreamingStates" }, // 849975110
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 2321277981
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnablePerformanceProfiler, "EnablePerformanceProfiler" }, // 477524803
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableStreamingDebug, "EnableStreamingDebug" }, // 1881006493
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_EnableWorldPartitionInspector, "EnableWorldPartitionInspector" }, // 1853795558
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExecuteDebugCommand, "ExecuteDebugCommand" }, // 3921583415
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_ExportDebugData, "ExportDebugData" }, // 3083665639
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAllCellDebugInfo, "GetAllCellDebugInfo" }, // 3857149485
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetAvailableDebugCommands, "GetAvailableDebugCommands" }, // 613473746
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCellDebugInfo, "GetCellDebugInfo" }, // 3126210215
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetColorScheme, "GetColorScheme" }, // 1105774368
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetConfiguration, "GetConfiguration" }, // 2240145191
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetCurrentPerformanceData, "GetCurrentPerformanceData" }, // 1193054314
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugColor, "GetDebugColor" }, // 3361661467
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugCommandHelp, "GetDebugCommandHelp" }, // 2027836687
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetDebugInfoLevel, "GetDebugInfoLevel" }, // 375388064
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInspectorData, "GetInspectorData" }, // 2673574438
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetInstance, "GetInstance" }, // 3038742394
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetPerformanceHistory, "GetPerformanceHistory" }, // 3272935010
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetStreamingEventLog, "GetStreamingEventLog" }, // 2515592582
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_GetVisualizationMode, "GetVisualizationMode" }, // 595791429
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Initialize, "Initialize" }, // 3056118088
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 1194312276
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsInitialized, "IsInitialized" }, // 549301726
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsPerformanceProfilerEnabled, "IsPerformanceProfilerEnabled" }, // 2075849784
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsStreamingDebugEnabled, "IsStreamingDebugEnabled" }, // 1498584885
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_IsWorldPartitionInspectorEnabled, "IsWorldPartitionInspectorEnabled" }, // 3599824522
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LoadDebugSnapshot, "LoadDebugSnapshot" }, // 2554256214
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_LogStreamingEvent, "LogStreamingEvent" }, // 3135748135
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature, "OnDebugEvent__DelegateSignature" }, // 3634083584
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature, "OnPerformanceAlert__DelegateSignature" }, // 1200058824
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature, "OnStreamingEvent__DelegateSignature" }, // 299922634
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_RefreshInspectorData, "RefreshInspectorData" }, // 3884146756
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SaveDebugSnapshot, "SaveDebugSnapshot" }, // 1109096654
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetColorScheme, "SetColorScheme" }, // 3654605194
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetConfiguration, "SetConfiguration" }, // 1613716289
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetDebugInfoLevel, "SetDebugInfoLevel" }, // 790190302
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_SetVisualizationMode, "SetVisualizationMode" }, // 63993184
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Shutdown, "Shutdown" }, // 3956576633
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StartProfiling, "StartProfiling" }, // 2260602904
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_StopProfiling, "StopProfiling" }, // 2945416731
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_TakeDebugSnapshot, "TakeDebugSnapshot" }, // 1036061284
		{ &Z_Construct_UFunction_UAuracronWorldPartitionDebugManager_Tick, "Tick" }, // 118000977
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionDebugManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnDebugEvent = { "OnDebugEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionDebugManager, OnDebugEvent), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnDebugEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDebugEvent_MetaData), NewProp_OnDebugEvent_MetaData) }; // 3634083584
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnPerformanceAlert = { "OnPerformanceAlert", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionDebugManager, OnPerformanceAlert), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnPerformanceAlert__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPerformanceAlert_MetaData), NewProp_OnPerformanceAlert_MetaData) }; // 1200058824
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnStreamingEvent = { "OnStreamingEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionDebugManager, OnStreamingEvent), Z_Construct_UDelegateFunction_UAuracronWorldPartitionDebugManager_OnStreamingEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnStreamingEvent_MetaData), NewProp_OnStreamingEvent_MetaData) }; // 299922634
void Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionDebugManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionDebugManager), &Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionDebugManager, Configuration), Z_Construct_UScriptStruct_FAuracronDebugConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3233916620
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionDebugManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnDebugEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnPerformanceAlert,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_OnStreamingEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::ClassParams = {
	&UAuracronWorldPartitionDebugManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionDebugManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionDebugManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager.OuterSingleton;
}
UAuracronWorldPartitionDebugManager::UAuracronWorldPartitionDebugManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionDebugManager);
UAuracronWorldPartitionDebugManager::~UAuracronWorldPartitionDebugManager() {}
// ********** End Class UAuracronWorldPartitionDebugManager ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronCellStreamingState_StaticEnum, TEXT("EAuracronCellStreamingState"), &Z_Registration_Info_UEnum_EAuracronCellStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3879821968U) },
		{ EAuracronDebugVisualizationMode_StaticEnum, TEXT("EAuracronDebugVisualizationMode"), &Z_Registration_Info_UEnum_EAuracronDebugVisualizationMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2443708541U) },
		{ EAuracronDebugInfoLevel_StaticEnum, TEXT("EAuracronDebugInfoLevel"), &Z_Registration_Info_UEnum_EAuracronDebugInfoLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3717273309U) },
		{ EAuracronDebugColorScheme_StaticEnum, TEXT("EAuracronDebugColorScheme"), &Z_Registration_Info_UEnum_EAuracronDebugColorScheme, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2462672971U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronDebugConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronDebugConfiguration_Statics::NewStructOps, TEXT("AuracronDebugConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronDebugConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDebugConfiguration), 3233916620U) },
		{ FAuracronDebugCellInfo::StaticStruct, Z_Construct_UScriptStruct_FAuracronDebugCellInfo_Statics::NewStructOps, TEXT("AuracronDebugCellInfo"), &Z_Registration_Info_UScriptStruct_FAuracronDebugCellInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDebugCellInfo), 2262776029U) },
		{ FAuracronDebugPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronDebugPerformanceData_Statics::NewStructOps, TEXT("AuracronDebugPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronDebugPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronDebugPerformanceData), 2293270951U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionDebugManager, UAuracronWorldPartitionDebugManager::StaticClass, TEXT("UAuracronWorldPartitionDebugManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionDebugManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionDebugManager), 2885480212U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_644855755(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionDebug_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
