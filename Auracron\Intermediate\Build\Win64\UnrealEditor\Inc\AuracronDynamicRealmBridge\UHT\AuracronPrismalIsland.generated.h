// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronPrismalIsland.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronPrismalIsland_generated_h
#error "AuracronPrismalIsland.generated.h already included, missing '#pragma once' in AuracronPrismalIsland.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronPrismalIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class APawn;
class UPrimitiveComponent;
struct FHitResult;

// ********** Begin ScriptStruct FAuracronIslandActivationRequirements *****************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_31_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronIslandActivationRequirements;
// ********** End ScriptStruct FAuracronIslandActivationRequirements *******************************

// ********** Begin ScriptStruct FAuracronIslandBenefits *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_74_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronIslandBenefits;
// ********** End ScriptStruct FAuracronIslandBenefits *********************************************

// ********** Begin Class AAuracronPrismalIsland ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnBenefitAreaEndOverlap); \
	DECLARE_FUNCTION(execOnBenefitAreaBeginOverlap); \
	DECLARE_FUNCTION(execOnActivationTriggerEndOverlap); \
	DECLARE_FUNCTION(execOnActivationTriggerBeginOverlap); \
	DECLARE_FUNCTION(execPlayDeactivationEffects); \
	DECLARE_FUNCTION(execPlayActivationEffects); \
	DECLARE_FUNCTION(execUpdateIslandVisuals); \
	DECLARE_FUNCTION(execUpdatePlayerBenefits); \
	DECLARE_FUNCTION(execRemoveBenefitsFromPlayer); \
	DECLARE_FUNCTION(execApplyBenefitsToPlayer); \
	DECLARE_FUNCTION(execGetPlayerCount); \
	DECLARE_FUNCTION(execGetPlayersOnIsland); \
	DECLARE_FUNCTION(execRemovePlayerFromIsland); \
	DECLARE_FUNCTION(execAddPlayerToIsland); \
	DECLARE_FUNCTION(execUpdateIslandState); \
	DECLARE_FUNCTION(execCanActivate); \
	DECLARE_FUNCTION(execIsIslandActive); \
	DECLARE_FUNCTION(execDeactivateIsland); \
	DECLARE_FUNCTION(execActivateIsland);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalIsland_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronPrismalIsland(); \
	friend struct Z_Construct_UClass_AAuracronPrismalIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronPrismalIsland, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronPrismalIsland_NoRegister) \
	DECLARE_SERIALIZER(AAuracronPrismalIsland)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronPrismalIsland(AAuracronPrismalIsland&&) = delete; \
	AAuracronPrismalIsland(const AAuracronPrismalIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronPrismalIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronPrismalIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronPrismalIsland) \
	NO_API virtual ~AAuracronPrismalIsland();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_150_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h_153_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronPrismalIsland;

// ********** End Class AAuracronPrismalIsland *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
