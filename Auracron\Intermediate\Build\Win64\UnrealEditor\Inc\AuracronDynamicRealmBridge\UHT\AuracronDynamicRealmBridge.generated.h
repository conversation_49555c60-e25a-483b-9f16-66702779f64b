// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronDynamicRealmBridge.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRealmBridge_generated_h
#error "AuracronDynamicRealmBridge.generated.h already included, missing '#pragma once' in AuracronDynamicRealmBridge.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRealmBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EAuracronGlobalEvolutionEvent : uint8;
enum class EAuracronLayerEvolutionStage : uint8;
enum class EAuracronRealmLayer : uint8;
enum class EPrismalIslandType : uint8;
enum class ERealmEvolutionPhase : uint8;

// ********** Begin ScriptStruct FAuracronLayerEvolutionData ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_124_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerEvolutionData;
// ********** End ScriptStruct FAuracronLayerEvolutionData *****************************************

// ********** Begin ScriptStruct FAuracronGlobalEvolutionData **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_192_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronGlobalEvolutionData;
// ********** End ScriptStruct FAuracronGlobalEvolutionData ****************************************

// ********** Begin ScriptStruct FPlayerRailData ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_275_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerRailData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerRailData;
// ********** End ScriptStruct FPlayerRailData *****************************************************

// ********** Begin ScriptStruct FSigilRealmBonus **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_311_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSigilRealmBonus_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSigilRealmBonus;
// ********** End ScriptStruct FSigilRealmBonus ****************************************************

// ********** Begin ScriptStruct FRailGenerationConfig *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_344_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRailGenerationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRailGenerationConfig;
// ********** End ScriptStruct FRailGenerationConfig ***********************************************

// ********** Begin ScriptStruct FRailSystemMetrics ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_384_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRailSystemMetrics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FRailSystemMetrics;
// ********** End ScriptStruct FRailSystemMetrics **************************************************

// ********** Begin ScriptStruct FPlayerRailExperience *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_416_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerRailExperience_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerRailExperience;
// ********** End ScriptStruct FPlayerRailExperience ***********************************************

// ********** Begin Delegate FOnSystemFullyInitialized *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_452_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnSystemFullyInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnSystemFullyInitialized);


// ********** End Delegate FOnSystemFullyInitialized ***********************************************

// ********** Begin Delegate FOnRealmLayerChanged **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_463_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnRealmLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRealmLayerChanged, EAuracronRealmLayer OldLayer, EAuracronRealmLayer NewLayer);


// ********** End Delegate FOnRealmLayerChanged ****************************************************

// ********** Begin Delegate FOnRealmEvolutionPhaseChanged *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_464_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnRealmEvolutionPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRealmEvolutionPhaseChanged, ERealmEvolutionPhase NewPhase);


// ********** End Delegate FOnRealmEvolutionPhaseChanged *******************************************

// ********** Begin Delegate FOnRealmTransitionStarted *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_465_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnRealmTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnRealmTransitionStarted, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer);


// ********** End Delegate FOnRealmTransitionStarted ***********************************************

// ********** Begin Delegate FOnRealmTransitionCompleted *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_466_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnRealmTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRealmTransitionCompleted, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer);


// ********** End Delegate FOnRealmTransitionCompleted *********************************************

// ********** Begin Delegate FOnPrismalIslandActivated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_467_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnPrismalIslandActivated_DelegateWrapper(const FMulticastScriptDelegate& OnPrismalIslandActivated, EPrismalIslandType IslandType, FVector Location);


// ********** End Delegate FOnPrismalIslandActivated ***********************************************

// ********** Begin Delegate FOnLayerEvolutionStageChanged *****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_470_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnLayerEvolutionStageChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLayerEvolutionStageChanged, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage OldStage, EAuracronLayerEvolutionStage NewStage);


// ********** End Delegate FOnLayerEvolutionStageChanged *******************************************

// ********** Begin Delegate FOnAdvancedRealmTransitionComplete ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_471_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnAdvancedRealmTransitionComplete_DelegateWrapper(const FMulticastScriptDelegate& OnAdvancedRealmTransitionComplete, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer, float Duration);


// ********** End Delegate FOnAdvancedRealmTransitionComplete **************************************

// ********** Begin Delegate FOnGlobalEvolutionEvent ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_472_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnGlobalEvolutionEvent_DelegateWrapper(const FMulticastScriptDelegate& OnGlobalEvolutionEvent, EAuracronGlobalEvolutionEvent EventType);


// ********** End Delegate FOnGlobalEvolutionEvent *************************************************

// ********** Begin Delegate FOnTranscendentContentUnlocked ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h_473_DELEGATE \
AURACRONDYNAMICREALMBRIDGE_API void FOnTranscendentContentUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnTranscendentContentUnlocked);


// ********** End Delegate FOnTranscendentContentUnlocked ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h

// ********** Begin Enum EAuracronRealmLayer *******************************************************
#define FOREACH_ENUM_EAURACRONREALMLAYER(op) \
	op(EAuracronRealmLayer::None) \
	op(EAuracronRealmLayer::Terrestrial) \
	op(EAuracronRealmLayer::Celestial) \
	op(EAuracronRealmLayer::Abyssal) \
	op(EAuracronRealmLayer::Transition) \
	op(EAuracronRealmLayer::All) 

enum class EAuracronRealmLayer : uint8;
template<> struct TIsUEnumClass<EAuracronRealmLayer> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronRealmLayer>();
// ********** End Enum EAuracronRealmLayer *********************************************************

// ********** Begin Enum EAuracronLayerEvolutionStage **********************************************
#define FOREACH_ENUM_EAURACRONLAYEREVOLUTIONSTAGE(op) \
	op(EAuracronLayerEvolutionStage::Dormant) \
	op(EAuracronLayerEvolutionStage::Awakening) \
	op(EAuracronLayerEvolutionStage::Active) \
	op(EAuracronLayerEvolutionStage::Resonant) \
	op(EAuracronLayerEvolutionStage::Transcendent) 

enum class EAuracronLayerEvolutionStage : uint8;
template<> struct TIsUEnumClass<EAuracronLayerEvolutionStage> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronLayerEvolutionStage>();
// ********** End Enum EAuracronLayerEvolutionStage ************************************************

// ********** Begin Enum EAuracronGlobalEvolutionEvent *********************************************
#define FOREACH_ENUM_EAURACRONGLOBALEVOLUTIONEVENT(op) \
	op(EAuracronGlobalEvolutionEvent::AllLayersAwakened) \
	op(EAuracronGlobalEvolutionEvent::TranscendentReached) \
	op(EAuracronGlobalEvolutionEvent::MaximumEvolution) 

enum class EAuracronGlobalEvolutionEvent : uint8;
template<> struct TIsUEnumClass<EAuracronGlobalEvolutionEvent> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronGlobalEvolutionEvent>();
// ********** End Enum EAuracronGlobalEvolutionEvent ***********************************************

// ********** Begin Enum EAuracronEvolutionTrigger *************************************************
#define FOREACH_ENUM_EAURACRONEVOLUTIONTRIGGER(op) \
	op(EAuracronEvolutionTrigger::PlayerPresence) \
	op(EAuracronEvolutionTrigger::PlayerActivity) \
	op(EAuracronEvolutionTrigger::CombatActivity) \
	op(EAuracronEvolutionTrigger::SigilActivation) \
	op(EAuracronEvolutionTrigger::RailUsage) \
	op(EAuracronEvolutionTrigger::IslandActivation) \
	op(EAuracronEvolutionTrigger::TimeProgression) \
	op(EAuracronEvolutionTrigger::WeatherEvents) \
	op(EAuracronEvolutionTrigger::DayNightCycle) \
	op(EAuracronEvolutionTrigger::SeasonalChange) 

enum class EAuracronEvolutionTrigger : uint8;
template<> struct TIsUEnumClass<EAuracronEvolutionTrigger> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronEvolutionTrigger>();
// ********** End Enum EAuracronEvolutionTrigger ***************************************************

// ********** Begin Enum ERealmEvolutionPhase ******************************************************
#define FOREACH_ENUM_EREALMEVOLUTIONPHASE(op) \
	op(ERealmEvolutionPhase::Despertar) \
	op(ERealmEvolutionPhase::Convergencia) \
	op(ERealmEvolutionPhase::Intensificacao) \
	op(ERealmEvolutionPhase::Resolucao) 

enum class ERealmEvolutionPhase : uint8;
template<> struct TIsUEnumClass<ERealmEvolutionPhase> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERealmEvolutionPhase>();
// ********** End Enum ERealmEvolutionPhase ********************************************************

// ********** Begin Enum ERealmTransitionType ******************************************************
#define FOREACH_ENUM_EREALMTRANSITIONTYPE(op) \
	op(ERealmTransitionType::Instant) \
	op(ERealmTransitionType::Gradual) \
	op(ERealmTransitionType::Cinematic) \
	op(ERealmTransitionType::Combat) \
	op(ERealmTransitionType::Stealth) 

enum class ERealmTransitionType : uint8;
template<> struct TIsUEnumClass<ERealmTransitionType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERealmTransitionType>();
// ********** End Enum ERealmTransitionType ********************************************************

// ********** Begin Enum EPrismalIslandType ********************************************************
#define FOREACH_ENUM_EPRISMALISLANDTYPE(op) \
	op(EPrismalIslandType::Nexus) \
	op(EPrismalIslandType::Santuario) \
	op(EPrismalIslandType::Arsenal) \
	op(EPrismalIslandType::Caos) 

enum class EPrismalIslandType : uint8;
template<> struct TIsUEnumClass<EPrismalIslandType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPrismalIslandType>();
// ********** End Enum EPrismalIslandType **********************************************************

// ********** Begin Enum EAuracronRailType *********************************************************
#define FOREACH_ENUM_EAURACRONRAILTYPE(op) \
	op(EAuracronRailType::Solar) \
	op(EAuracronRailType::Axis) \
	op(EAuracronRailType::Lunar) 

enum class EAuracronRailType : uint8;
template<> struct TIsUEnumClass<EAuracronRailType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronRailType>();
// ********** End Enum EAuracronRailType ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
