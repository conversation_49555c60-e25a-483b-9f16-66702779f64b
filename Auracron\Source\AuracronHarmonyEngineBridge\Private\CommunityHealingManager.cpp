#include "CommunityHealingManager.h"
#include "AuracronHarmonyEngineBridge.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"

UCommunityHealingManager::UCommunityHealingManager()
{
    // Default configuration
    MaxConcurrentSessions = 10;
    MaxSessionDuration = 1800.0f; // 30 minutes
    MinHealerSkillLevel = 0.6f;
    MaxHealersPerSession = 3;
    bEnableAutomaticMatching = true;
    bEnableSessionRecording = true;
}

FString UCommunityHealingManager::InitiateHealingSession(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    if (!ValidateHealingRequest(VictimPlayerID, SessionType))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Invalid healing request for player: %s"), *VictimPlayerID);
        return TEXT("");
    }
    
    if (ActiveSessions.Num() >= MaxConcurrentSessions)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Maximum concurrent healing sessions reached"));
        return TEXT("");
    }
    
    // Create new healing session
    FHealingSession NewSession;
    NewSession.SessionID = GenerateSessionID();
    NewSession.SessionType = SessionType;
    NewSession.VictimPlayerID = VictimPlayerID;
    NewSession.Status = EHealingStatus::Pending;
    NewSession.StartTime = FDateTime::Now();
    NewSession.HealingGoal = GenerateHealingGoal(SessionType, VictimPlayerID);
    
    // Add session tags
    NewSession.SessionTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
    
    // Store session
    ActiveSessions.Add(NewSession.SessionID, NewSession);
    
    // Start session timer
    StartSessionTimer(NewSession.SessionID);
    
    // Auto-match healers if enabled
    if (bEnableAutomaticMatching)
    {
        TArray<FString> AvailableHealers = FindAvailableHealers(SessionType);
        if (AvailableHealers.Num() > 0)
        {
            FString BestHealer = FindBestMatchedHealer(VictimPlayerID, SessionType);
            if (!BestHealer.IsEmpty())
            {
                AddHealerToSession(NewSession.SessionID, BestHealer);
            }
        }
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initiated healing session %s for player %s"), *NewSession.SessionID, *VictimPlayerID);
    
    return NewSession.SessionID;
}

bool UCommunityHealingManager::AddHealerToSession(const FString& SessionID, const FString& HealerPlayerID)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Healing session not found: %s"), *SessionID);
        return false;
    }
    
    if (!ValidateHealerEligibility(HealerPlayerID, Session->SessionType))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Healer %s not eligible for session %s"), *HealerPlayerID, *SessionID);
        return false;
    }
    
    if (Session->HealerPlayerIDs.Num() >= MaxHealersPerSession)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Maximum healers reached for session %s"), *SessionID);
        return false;
    }
    
    // Add healer to session
    Session->HealerPlayerIDs.AddUnique(HealerPlayerID);
    
    // Update healer availability
    if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerPlayerID))
    {
        HealerProfile->bIsAvailable = false;
        HealerProfile->LastActiveTime = FDateTime::Now();
    }
    
    // Update session status
    if (Session->Status == EHealingStatus::Pending)
    {
        Session->Status = EHealingStatus::Active;
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Added healer %s to session %s"), *HealerPlayerID, *SessionID);
    
    return true;
}

bool UCommunityHealingManager::CompleteHealingSession(const FString& SessionID, float SuccessRating)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        return false;
    }
    
    // Update session completion data
    Session->Status = EHealingStatus::Completed;
    Session->EndTime = FDateTime::Now();
    Session->HealingProgress = 1.0f;
    
    // Update healer statistics
    for (const FString& HealerID : Session->HealerPlayerIDs)
    {
        UpdateHealerStatistics(HealerID, SuccessRating);
        
        // Make healer available again
        if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID))
        {
            HealerProfile->bIsAvailable = true;
        }
    }
    
    // Record session outcome
    RecordSessionOutcome(*Session, SuccessRating > 0.6f);
    
    // Move to completed sessions
    CompletedSessions.Add(*Session);
    ActiveSessions.Remove(SessionID);
    
    // Clear session timer
    if (UWorld* World = GetWorld())
    {
        if (FTimerHandle* Timer = SessionTimers.Find(SessionID))
        {
            World->GetTimerManager().ClearTimer(*Timer);
            SessionTimers.Remove(SessionID);
        }
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Completed healing session %s with rating %.2f"), *SessionID, SuccessRating);
    
    return true;
}

void UCommunityHealingManager::RegisterHealer(const FString& PlayerID, const TArray<EHealingSessionType>& Specializations)
{
    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot register healer with empty PlayerID"));
        return;
    }
    
    // Create or update healer profile
    FHealerProfile HealerProfile;
    HealerProfile.PlayerID = PlayerID;
    HealerProfile.Specializations = Specializations;
    HealerProfile.HealingSkillLevel = CalculateInitialHealerSkill(PlayerID);
    HealerProfile.bIsAvailable = true;
    HealerProfile.LastActiveTime = FDateTime::Now();
    
    // Add healer tags based on specializations
    for (EHealingSessionType Specialization : Specializations)
    {
        switch (Specialization)
        {
            case EHealingSessionType::Mentorship:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Behavior_Mentoring);
                break;
            case EHealingSessionType::PeerSupport:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
                break;
            case EHealingSessionType::CrisisIntervention:
                HealerProfile.HealerTags.AddTag(HarmonyEngineGameplayTags::Intervention_Emergency);
                break;
        }
    }
    
    RegisteredHealers.Add(PlayerID, HealerProfile);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Registered healer %s with %d specializations"), *PlayerID, Specializations.Num());
}

TArray<FString> UCommunityHealingManager::FindAvailableHealers(EHealingSessionType SessionType)
{
    TArray<FString> AvailableHealers;
    
    for (const auto& HealerPair : RegisteredHealers)
    {
        const FHealerProfile& Profile = HealerPair.Value;
        
        if (Profile.bIsAvailable && 
            Profile.HealingSkillLevel >= MinHealerSkillLevel &&
            IsHealerSpecializedFor(Profile.PlayerID, SessionType))
        {
            AvailableHealers.Add(Profile.PlayerID);
        }
    }
    
    // Sort by skill level (highest first)
    AvailableHealers.Sort([this](const FString& A, const FString& B) {
        const FHealerProfile* ProfileA = RegisteredHealers.Find(A);
        const FHealerProfile* ProfileB = RegisteredHealers.Find(B);
        
        if (ProfileA && ProfileB)
        {
            return ProfileA->HealingSkillLevel > ProfileB->HealingSkillLevel;
        }
        return false;
    });
    
    return AvailableHealers;
}

FString UCommunityHealingManager::FindBestMatchedHealer(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    TArray<FString> AvailableHealers = FindAvailableHealers(SessionType);
    
    if (AvailableHealers.Num() == 0)
    {
        return TEXT("");
    }
    
    // Rank healers by compatibility
    TArray<FString> RankedHealers = RankHealersByCompatibility(AvailableHealers, VictimPlayerID);
    
    return RankedHealers.Num() > 0 ? RankedHealers[0] : TEXT("");
}

bool UCommunityHealingManager::IsPlayerInHealingSession(const FString& PlayerID)
{
    // Check if player is victim in any active session
    for (const auto& SessionPair : ActiveSessions)
    {
        const FHealingSession& Session = SessionPair.Value;
        if (Session.VictimPlayerID == PlayerID)
        {
            return true;
        }
        
        // Check if player is healer in any active session
        if (Session.HealerPlayerIDs.Contains(PlayerID))
        {
            return true;
        }
    }
    
    return false;
}

float UCommunityHealingManager::GetCommunityHealingEffectiveness()
{
    if (CompletedSessions.Num() == 0)
    {
        return 0.0f;
    }
    
    float TotalEffectiveness = 0.0f;
    int32 ValidSessions = 0;
    
    for (const FHealingSession& Session : CompletedSessions)
    {
        if (Session.Status == EHealingStatus::Completed)
        {
            TotalEffectiveness += Session.HealingProgress;
            ValidSessions++;
        }
    }
    
    return ValidSessions > 0 ? (TotalEffectiveness / ValidSessions) : 0.0f;
}

TArray<FString> UCommunityHealingManager::GetTopHealers(int32 Count)
{
    TArray<FString> TopHealers;
    
    // Create array of healer IDs with their ratings
    TArray<TPair<FString, float>> HealerRatings;
    
    for (const auto& HealerPair : RegisteredHealers)
    {
        const FHealerProfile& Profile = HealerPair.Value;
        float Rating = (Profile.HealingSkillLevel * 0.6f) + (Profile.AverageSessionRating * 0.4f);
        HealerRatings.Add(TPair<FString, float>(Profile.PlayerID, Rating));
    }
    
    // Sort by rating (highest first)
    HealerRatings.Sort([](const TPair<FString, float>& A, const TPair<FString, float>& B) {
        return A.Value > B.Value;
    });
    
    // Extract top healers
    int32 NumToReturn = FMath::Min(Count, HealerRatings.Num());
    for (int32 i = 0; i < NumToReturn; i++)
    {
        TopHealers.Add(HealerRatings[i].Key);
    }
    
    return TopHealers;
}

// Private helper function implementations

bool UCommunityHealingManager::ValidateHealingRequest(const FString& VictimPlayerID, EHealingSessionType SessionType)
{
    if (VictimPlayerID.IsEmpty())
    {
        return false;
    }
    
    // Check if player is already in a healing session
    if (IsPlayerInHealingSession(VictimPlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is already in a healing session"), *VictimPlayerID);
        return false;
    }
    
    // Validate session type
    if (SessionType == EHealingSessionType::CrisisIntervention)
    {
        // Crisis intervention requires special validation
        // Check if player's emotional state warrants crisis intervention
        return true; // Simplified validation
    }
    
    return true;
}

bool UCommunityHealingManager::ValidateHealerEligibility(const FString& HealerPlayerID, EHealingSessionType SessionType)
{
    const FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return false;
    }
    
    // Check availability
    if (!Profile->bIsAvailable)
    {
        return false;
    }
    
    // Check skill level
    if (Profile->HealingSkillLevel < MinHealerSkillLevel)
    {
        return false;
    }
    
    // Check specialization
    if (!IsHealerSpecializedFor(HealerPlayerID, SessionType))
    {
        return false;
    }
    
    return true;
}

void UCommunityHealingManager::StartSessionTimer(const FString& SessionID)
{
    if (UWorld* World = GetWorld())
    {
        FTimerHandle SessionTimer;
        FTimerDelegate TimerDelegate;
        TimerDelegate.BindUFunction(this, FName("OnSessionTimeout"), SessionID);
        
        World->GetTimerManager().SetTimer(SessionTimer, TimerDelegate, MaxSessionDuration, false);
        SessionTimers.Add(SessionID, SessionTimer);
    }
}

void UCommunityHealingManager::OnSessionTimeout(const FString& SessionID)
{
    FHealingSession* Session = ActiveSessions.Find(SessionID);
    if (!Session)
    {
        return;
    }
    
    UE_LOG(LogHarmonyEngine, Warning, TEXT("Healing session %s timed out"), *SessionID);
    
    // Mark session as failed due to timeout
    Session->Status = EHealingStatus::Failed;
    Session->EndTime = FDateTime::Now();
    
    // Make healers available again
    for (const FString& HealerID : Session->HealerPlayerIDs)
    {
        if (FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerID))
        {
            HealerProfile->bIsAvailable = true;
        }
    }
    
    // Record failed session
    RecordSessionOutcome(*Session, false);
    
    // Move to completed sessions
    CompletedSessions.Add(*Session);
    ActiveSessions.Remove(SessionID);
    SessionTimers.Remove(SessionID);
}

float UCommunityHealingManager::CalculateHealerCompatibility(const FString& HealerPlayerID, const FString& VictimPlayerID)
{
    const FHealerProfile* HealerProfile = RegisteredHealers.Find(HealerPlayerID);
    if (!HealerProfile)
    {
        return 0.0f;
    }
    
    float Compatibility = 0.0f;
    
    // Base compatibility from healer skill level
    Compatibility += HealerProfile->HealingSkillLevel * 0.4f;
    
    // Bonus for successful session history
    Compatibility += (HealerProfile->AverageSessionRating / 5.0f) * 0.3f;
    
    // Bonus for experience
    float ExperienceBonus = FMath::Min(HealerProfile->SuccessfulHealingSessions / 10.0f, 1.0f) * 0.2f;
    Compatibility += ExperienceBonus;
    
    // Recent activity bonus
    FDateTime CurrentTime = FDateTime::Now();
    float TimeSinceActive = (CurrentTime - HealerProfile->LastActiveTime).GetTotalHours();
    float ActivityBonus = FMath::Max(0.0f, (24.0f - TimeSinceActive) / 24.0f) * 0.1f;
    Compatibility += ActivityBonus;
    
    return FMath::Clamp(Compatibility, 0.0f, 1.0f);
}

TArray<FString> UCommunityHealingManager::RankHealersByCompatibility(const TArray<FString>& AvailableHealers, const FString& VictimPlayerID)
{
    TArray<TPair<FString, float>> HealerCompatibility;
    
    for (const FString& HealerID : AvailableHealers)
    {
        float Compatibility = CalculateHealerCompatibility(HealerID, VictimPlayerID);
        HealerCompatibility.Add(TPair<FString, float>(HealerID, Compatibility));
    }
    
    // Sort by compatibility (highest first)
    HealerCompatibility.Sort([](const TPair<FString, float>& A, const TPair<FString, float>& B) {
        return A.Value > B.Value;
    });
    
    TArray<FString> RankedHealers;
    for (const auto& Pair : HealerCompatibility)
    {
        RankedHealers.Add(Pair.Key);
    }
    
    return RankedHealers;
}

bool UCommunityHealingManager::IsHealerSpecializedFor(const FString& HealerPlayerID, EHealingSessionType SessionType)
{
    const FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return false;
    }
    
    return Profile->Specializations.Contains(SessionType);
}

void UCommunityHealingManager::UpdateHealerStatistics(const FString& HealerPlayerID, float SessionRating)
{
    FHealerProfile* Profile = RegisteredHealers.Find(HealerPlayerID);
    if (!Profile)
    {
        return;
    }
    
    // Update successful sessions count
    if (SessionRating > 0.6f)
    {
        Profile->SuccessfulHealingSessions++;
    }
    
    // Update average rating
    float TotalRating = Profile->AverageSessionRating * (Profile->SuccessfulHealingSessions - 1);
    TotalRating += SessionRating;
    Profile->AverageSessionRating = TotalRating / Profile->SuccessfulHealingSessions;
    
    // Update skill level based on performance
    float SkillImprovement = (SessionRating - 0.5f) * 0.01f; // Small incremental improvement
    Profile->HealingSkillLevel = FMath::Clamp(Profile->HealingSkillLevel + SkillImprovement, 0.0f, 1.0f);
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Updated healer %s statistics - Rating: %.2f, Skill: %.3f"), 
        *HealerPlayerID, Profile->AverageSessionRating, Profile->HealingSkillLevel);
}

void UCommunityHealingManager::RecordSessionOutcome(const FHealingSession& Session, bool bSuccessful)
{
    // Record session outcome for analytics
    FString OutcomeData = FString::Printf(
        TEXT("Session %s: Type=%d, Duration=%.2f, Healers=%d, Success=%s"),
        *Session.SessionID,
        static_cast<int32>(Session.SessionType),
        (Session.EndTime - Session.StartTime).GetTotalMinutes(),
        Session.HealerPlayerIDs.Num(),
        bSuccessful ? TEXT("Yes") : TEXT("No")
    );
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Session outcome: %s"), *OutcomeData);
    
    // Update player healing history
    FPlayerHealingHistory& VictimHistory = PlayerHealingHistory.FindOrAdd(Session.VictimPlayerID);
    VictimHistory.SessionIDs.Add(Session.SessionID);

    for (const FString& HealerID : Session.HealerPlayerIDs)
    {
        FPlayerHealingHistory& HealerHistory = PlayerHealingHistory.FindOrAdd(HealerID);
        HealerHistory.SessionIDs.Add(Session.SessionID);
    }
}

FString UCommunityHealingManager::GenerateSessionID()
{
    return FGuid::NewGuid().ToString();
}

FString UCommunityHealingManager::GenerateHealingGoal(EHealingSessionType SessionType, const FString& VictimPlayerID)
{
    switch (SessionType)
    {
        case EHealingSessionType::PeerSupport:
            return TEXT("Provide peer support and encouragement");
        case EHealingSessionType::Mentorship:
            return TEXT("Guide player improvement and skill development");
        case EHealingSessionType::GroupTherapy:
            return TEXT("Facilitate group discussion and mutual support");
        case EHealingSessionType::CrisisIntervention:
            return TEXT("Provide immediate emotional support and crisis management");
        case EHealingSessionType::CelebrationCircle:
            return TEXT("Celebrate achievements and build positive momentum");
        default:
            return TEXT("Provide general emotional support");
    }
}

float UCommunityHealingManager::CalculateInitialHealerSkill(const FString& PlayerID)
{
    // Calculate initial healer skill based on player's positive behavior history
    // This would integrate with the player's overall positivity score
    return 0.5f; // Default starting skill level
}
