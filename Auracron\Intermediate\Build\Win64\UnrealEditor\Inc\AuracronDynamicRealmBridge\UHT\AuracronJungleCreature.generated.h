// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronJungleCreature.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronJungleCreature_generated_h
#error "AuracronJungleCreature.generated.h already included, missing '#pragma once' in AuracronJungleCreature.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronJungleCreature_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AAuracronAdaptiveJungleAI;
class APawn;
enum class ECreatureBehaviorState : uint8;
enum class EJungleCreatureAdaptationType : uint8;
enum class EJungleCreatureType : uint8;
struct FAuracronCreatureAdaptation;
struct FAuracronCreatureAdaptationStatus;

// ********** Begin ScriptStruct FAuracronCreatureAdaptationStatus *********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_85_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCreatureAdaptationStatus_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCreatureAdaptationStatus;
// ********** End ScriptStruct FAuracronCreatureAdaptationStatus ***********************************

// ********** Begin Class AAuracronJungleCreature **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateSocialBehavior); \
	DECLARE_FUNCTION(execUpdateTerritorialBehavior); \
	DECLARE_FUNCTION(execReactToPlayerPresence); \
	DECLARE_FUNCTION(execUpdateCreatureBehavior); \
	DECLARE_FUNCTION(execRemoveAIAdaptation); \
	DECLARE_FUNCTION(execApplyAIAdaptation); \
	DECLARE_FUNCTION(execUnregisterFromAdaptiveAI); \
	DECLARE_FUNCTION(execRegisterWithAdaptiveAI); \
	DECLARE_FUNCTION(execIsCreatureAdapted); \
	DECLARE_FUNCTION(execGetAdaptationStatus); \
	DECLARE_FUNCTION(execSetBehaviorState); \
	DECLARE_FUNCTION(execGetBehaviorState); \
	DECLARE_FUNCTION(execSetCreatureType); \
	DECLARE_FUNCTION(execGetCreatureType);


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_CALLBACK_WRAPPERS
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronJungleCreature_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAuracronJungleCreature(); \
	friend struct Z_Construct_UClass_AAuracronJungleCreature_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronJungleCreature_NoRegister(); \
public: \
	DECLARE_CLASS2(AAuracronJungleCreature, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_AAuracronJungleCreature_NoRegister) \
	DECLARE_SERIALIZER(AAuracronJungleCreature) \
	virtual UObject* _getUObject() const override { return const_cast<AAuracronJungleCreature*>(this); }


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAuracronJungleCreature(AAuracronJungleCreature&&) = delete; \
	AAuracronJungleCreature(const AAuracronJungleCreature&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAuracronJungleCreature); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAuracronJungleCreature); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAuracronJungleCreature) \
	NO_API virtual ~AAuracronJungleCreature();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_128_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_CALLBACK_WRAPPERS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h_131_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAuracronJungleCreature;

// ********** End Class AAuracronJungleCreature ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronJungleCreature_h

// ********** Begin Enum EJungleCreatureAdaptationType *********************************************
#define FOREACH_ENUM_EJUNGLECREATUREADAPTATIONTYPE(op) \
	op(EJungleCreatureAdaptationType::None) \
	op(EJungleCreatureAdaptationType::Behavioral) \
	op(EJungleCreatureAdaptationType::Physical) \
	op(EJungleCreatureAdaptationType::Tactical) \
	op(EJungleCreatureAdaptationType::Environmental) \
	op(EJungleCreatureAdaptationType::Aggressive) \
	op(EJungleCreatureAdaptationType::Defensive) 

enum class EJungleCreatureAdaptationType : uint8;
template<> struct TIsUEnumClass<EJungleCreatureAdaptationType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleCreatureAdaptationType>();
// ********** End Enum EJungleCreatureAdaptationType ***********************************************

// ********** Begin Enum EJungleCreatureType *******************************************************
#define FOREACH_ENUM_EJUNGLECREATURETYPE(op) \
	op(EJungleCreatureType::Predator) \
	op(EJungleCreatureType::Herbivore) \
	op(EJungleCreatureType::Scavenger) \
	op(EJungleCreatureType::Guardian) \
	op(EJungleCreatureType::Elemental) \
	op(EJungleCreatureType::Hybrid) 

enum class EJungleCreatureType : uint8;
template<> struct TIsUEnumClass<EJungleCreatureType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EJungleCreatureType>();
// ********** End Enum EJungleCreatureType *********************************************************

// ********** Begin Enum ECreatureBehaviorState ****************************************************
#define FOREACH_ENUM_ECREATUREBEHAVIORSTATE(op) \
	op(ECreatureBehaviorState::Idle) \
	op(ECreatureBehaviorState::Patrolling) \
	op(ECreatureBehaviorState::Hunting) \
	op(ECreatureBehaviorState::Fleeing) \
	op(ECreatureBehaviorState::Investigating) \
	op(ECreatureBehaviorState::Territorial) \
	op(ECreatureBehaviorState::Social) \
	op(ECreatureBehaviorState::Adapted) 

enum class ECreatureBehaviorState : uint8;
template<> struct TIsUEnumClass<ECreatureBehaviorState> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ECreatureBehaviorState>();
// ********** End Enum ECreatureBehaviorState ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
