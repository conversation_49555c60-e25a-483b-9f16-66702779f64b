// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronHarmonyEngineBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronHarmonyEngineBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType();
AURACRONHARMONYENGINEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyInterventionData();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FKindnessReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EHarmonyBehaviorType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHarmonyBehaviorType;
static UEnum* EHarmonyBehaviorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHarmonyBehaviorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHarmonyBehaviorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EHarmonyBehaviorType"));
	}
	return Z_Registration_Info_UEnum_EHarmonyBehaviorType.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EHarmonyBehaviorType>()
{
	return EHarmonyBehaviorType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Harmony Engine Core Data Structures\n */" },
#endif
		{ "Healing.DisplayName", "Healing Behavior" },
		{ "Healing.Name", "EHarmonyBehaviorType::Healing" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
		{ "Neutral.DisplayName", "Neutral Behavior" },
		{ "Neutral.Name", "EHarmonyBehaviorType::Neutral" },
		{ "Positive.DisplayName", "Positive Behavior" },
		{ "Positive.Name", "EHarmonyBehaviorType::Positive" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Harmony Engine Core Data Structures" },
#endif
		{ "Toxic.DisplayName", "Toxic Behavior" },
		{ "Toxic.Name", "EHarmonyBehaviorType::Toxic" },
		{ "Warning.DisplayName", "Warning Signs" },
		{ "Warning.Name", "EHarmonyBehaviorType::Warning" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHarmonyBehaviorType::Positive", (int64)EHarmonyBehaviorType::Positive },
		{ "EHarmonyBehaviorType::Neutral", (int64)EHarmonyBehaviorType::Neutral },
		{ "EHarmonyBehaviorType::Warning", (int64)EHarmonyBehaviorType::Warning },
		{ "EHarmonyBehaviorType::Toxic", (int64)EHarmonyBehaviorType::Toxic },
		{ "EHarmonyBehaviorType::Healing", (int64)EHarmonyBehaviorType::Healing },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EHarmonyBehaviorType",
	"EHarmonyBehaviorType",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType()
{
	if (!Z_Registration_Info_UEnum_EHarmonyBehaviorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHarmonyBehaviorType.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHarmonyBehaviorType.InnerSingleton;
}
// ********** End Enum EHarmonyBehaviorType ********************************************************

// ********** Begin Enum EEmotionalState ***********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEmotionalState;
static UEnum* EEmotionalState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEmotionalState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEmotionalState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EEmotionalState"));
	}
	return Z_Registration_Info_UEnum_EEmotionalState.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EEmotionalState>()
{
	return EEmotionalState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Angry.DisplayName", "Angry" },
		{ "Angry.Name", "EEmotionalState::Angry" },
		{ "Anxious.DisplayName", "Anxious" },
		{ "Anxious.Name", "EEmotionalState::Anxious" },
		{ "BlueprintType", "true" },
		{ "Calm.DisplayName", "Calm" },
		{ "Calm.Name", "EEmotionalState::Calm" },
		{ "Excited.DisplayName", "Excited" },
		{ "Excited.Name", "EEmotionalState::Excited" },
		{ "Frustrated.DisplayName", "Frustrated" },
		{ "Frustrated.Name", "EEmotionalState::Frustrated" },
		{ "Happy.DisplayName", "Happy" },
		{ "Happy.Name", "EEmotionalState::Happy" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
		{ "Neutral.DisplayName", "Neutral" },
		{ "Neutral.Name", "EEmotionalState::Neutral" },
		{ "Sad.DisplayName", "Sad" },
		{ "Sad.Name", "EEmotionalState::Sad" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEmotionalState::Happy", (int64)EEmotionalState::Happy },
		{ "EEmotionalState::Excited", (int64)EEmotionalState::Excited },
		{ "EEmotionalState::Calm", (int64)EEmotionalState::Calm },
		{ "EEmotionalState::Neutral", (int64)EEmotionalState::Neutral },
		{ "EEmotionalState::Frustrated", (int64)EEmotionalState::Frustrated },
		{ "EEmotionalState::Angry", (int64)EEmotionalState::Angry },
		{ "EEmotionalState::Sad", (int64)EEmotionalState::Sad },
		{ "EEmotionalState::Anxious", (int64)EEmotionalState::Anxious },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EEmotionalState",
	"EEmotionalState",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState()
{
	if (!Z_Registration_Info_UEnum_EEmotionalState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEmotionalState.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEmotionalState.InnerSingleton;
}
// ********** End Enum EEmotionalState *************************************************************

// ********** Begin Enum EInterventionType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EInterventionType;
static UEnum* EInterventionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EInterventionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EInterventionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("EInterventionType"));
	}
	return Z_Registration_Info_UEnum_EInterventionType.OuterSingleton;
}
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<EInterventionType>()
{
	return EInterventionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Emergency.DisplayName", "Emergency Intervention" },
		{ "Emergency.Name", "EInterventionType::Emergency" },
		{ "Gentle.DisplayName", "Gentle Suggestion" },
		{ "Gentle.Name", "EInterventionType::Gentle" },
		{ "Moderate.DisplayName", "Moderate Intervention" },
		{ "Moderate.Name", "EInterventionType::Moderate" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
		{ "None.DisplayName", "No Intervention" },
		{ "None.Name", "EInterventionType::None" },
		{ "Strong.DisplayName", "Strong Intervention" },
		{ "Strong.Name", "EInterventionType::Strong" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EInterventionType::None", (int64)EInterventionType::None },
		{ "EInterventionType::Gentle", (int64)EInterventionType::Gentle },
		{ "EInterventionType::Moderate", (int64)EInterventionType::Moderate },
		{ "EInterventionType::Strong", (int64)EInterventionType::Strong },
		{ "EInterventionType::Emergency", (int64)EInterventionType::Emergency },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	"EInterventionType",
	"EInterventionType",
	Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType()
{
	if (!Z_Registration_Info_UEnum_EInterventionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EInterventionType.InnerSingleton, Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EInterventionType.InnerSingleton;
}
// ********** End Enum EInterventionType ***********************************************************

// ********** Begin ScriptStruct FPlayerBehaviorSnapshot *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot;
class UScriptStruct* FPlayerBehaviorSnapshot::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("PlayerBehaviorSnapshot"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorType_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmotionalState_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToxicityScore_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositivityScore_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FrustrationLevel_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositiveActionsCount_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NegativeActionsCount_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionDuration_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTags_MetaData[] = {
		{ "Category", "PlayerBehaviorSnapshot" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EmotionalState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EmotionalState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ToxicityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PositivityScore;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrustrationLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PositiveActionsCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NegativeActionsCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SessionDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerBehaviorSnapshot>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, BehaviorType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EHarmonyBehaviorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorType_MetaData), NewProp_BehaviorType_MetaData) }; // 4194820134
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_EmotionalState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_EmotionalState = { "EmotionalState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, EmotionalState), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EEmotionalState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmotionalState_MetaData), NewProp_EmotionalState_MetaData) }; // 3787130921
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_ToxicityScore = { "ToxicityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, ToxicityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToxicityScore_MetaData), NewProp_ToxicityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PositivityScore = { "PositivityScore", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, PositivityScore), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositivityScore_MetaData), NewProp_PositivityScore_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_FrustrationLevel = { "FrustrationLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, FrustrationLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FrustrationLevel_MetaData), NewProp_FrustrationLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PositiveActionsCount = { "PositiveActionsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, PositiveActionsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositiveActionsCount_MetaData), NewProp_PositiveActionsCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_NegativeActionsCount = { "NegativeActionsCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, NegativeActionsCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NegativeActionsCount_MetaData), NewProp_NegativeActionsCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_SessionDuration = { "SessionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, SessionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionDuration_MetaData), NewProp_SessionDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, Timestamp), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorTags = { "BehaviorTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerBehaviorSnapshot, BehaviorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTags_MetaData), NewProp_BehaviorTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_EmotionalState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_EmotionalState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_ToxicityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PositivityScore,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_FrustrationLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_PositiveActionsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_NegativeActionsCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_SessionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewProp_BehaviorTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"PlayerBehaviorSnapshot",
	Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::PropPointers),
	sizeof(FPlayerBehaviorSnapshot),
	alignof(FPlayerBehaviorSnapshot),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.InnerSingleton, Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot.InnerSingleton;
}
// ********** End ScriptStruct FPlayerBehaviorSnapshot *********************************************

// ********** Begin ScriptStruct FHarmonyInterventionData ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FHarmonyInterventionData;
class UScriptStruct* FHarmonyInterventionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHarmonyInterventionData, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("HarmonyInterventionData"));
	}
	return Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionType_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionMessage_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuggestedAction_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionPriority_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresImmediateAction_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionTags_MetaData[] = {
		{ "Category", "HarmonyInterventionData" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InterventionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InterventionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SuggestedAction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InterventionPriority;
	static void NewProp_bRequiresImmediateAction_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresImmediateAction;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHarmonyInterventionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionType = { "InterventionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyInterventionData, InterventionType), Z_Construct_UEnum_AuracronHarmonyEngineBridge_EInterventionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionType_MetaData), NewProp_InterventionType_MetaData) }; // 864251872
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionMessage = { "InterventionMessage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyInterventionData, InterventionMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionMessage_MetaData), NewProp_InterventionMessage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_SuggestedAction = { "SuggestedAction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyInterventionData, SuggestedAction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuggestedAction_MetaData), NewProp_SuggestedAction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionPriority = { "InterventionPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyInterventionData, InterventionPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionPriority_MetaData), NewProp_InterventionPriority_MetaData) };
void Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_bRequiresImmediateAction_SetBit(void* Obj)
{
	((FHarmonyInterventionData*)Obj)->bRequiresImmediateAction = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_bRequiresImmediateAction = { "bRequiresImmediateAction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FHarmonyInterventionData), &Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_bRequiresImmediateAction_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresImmediateAction_MetaData), NewProp_bRequiresImmediateAction_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionTags = { "InterventionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHarmonyInterventionData, InterventionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionTags_MetaData), NewProp_InterventionTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_SuggestedAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_bRequiresImmediateAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewProp_InterventionTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"HarmonyInterventionData",
	Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::PropPointers),
	sizeof(FHarmonyInterventionData),
	alignof(FHarmonyInterventionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHarmonyInterventionData()
{
	if (!Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.InnerSingleton, Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FHarmonyInterventionData.InnerSingleton;
}
// ********** End ScriptStruct FHarmonyInterventionData ********************************************

// ********** Begin ScriptStruct FKindnessReward ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FKindnessReward;
class UScriptStruct* FKindnessReward::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FKindnessReward.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FKindnessReward.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FKindnessReward, (UObject*)Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge(), TEXT("KindnessReward"));
	}
	return Z_Registration_Info_UScriptStruct_FKindnessReward.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FKindnessReward_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_KindnessPoints_MetaData[] = {
		{ "Category", "KindnessReward" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceMultiplier_MetaData[] = {
		{ "Category", "KindnessReward" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardDescription_MetaData[] = {
		{ "Category", "KindnessReward" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewardTags_MetaData[] = {
		{ "Category", "KindnessReward" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSpecialReward_MetaData[] = {
		{ "Category", "KindnessReward" },
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_KindnessPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperienceMultiplier;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RewardDescription;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RewardTags;
	static void NewProp_bIsSpecialReward_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSpecialReward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FKindnessReward>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_KindnessPoints = { "KindnessPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FKindnessReward, KindnessPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_KindnessPoints_MetaData), NewProp_KindnessPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_ExperienceMultiplier = { "ExperienceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FKindnessReward, ExperienceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceMultiplier_MetaData), NewProp_ExperienceMultiplier_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_RewardDescription = { "RewardDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FKindnessReward, RewardDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardDescription_MetaData), NewProp_RewardDescription_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_RewardTags = { "RewardTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FKindnessReward, RewardTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewardTags_MetaData), NewProp_RewardTags_MetaData) }; // 2104890724
void Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_bIsSpecialReward_SetBit(void* Obj)
{
	((FKindnessReward*)Obj)->bIsSpecialReward = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_bIsSpecialReward = { "bIsSpecialReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FKindnessReward), &Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_bIsSpecialReward_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSpecialReward_MetaData), NewProp_bIsSpecialReward_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FKindnessReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_KindnessPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_ExperienceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_RewardDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_RewardTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FKindnessReward_Statics::NewProp_bIsSpecialReward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FKindnessReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FKindnessReward_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
	nullptr,
	&NewStructOps,
	"KindnessReward",
	Z_Construct_UScriptStruct_FKindnessReward_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FKindnessReward_Statics::PropPointers),
	sizeof(FKindnessReward),
	alignof(FKindnessReward),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FKindnessReward_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FKindnessReward_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FKindnessReward()
{
	if (!Z_Registration_Info_UScriptStruct_FKindnessReward.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FKindnessReward.InnerSingleton, Z_Construct_UScriptStruct_FKindnessReward_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FKindnessReward.InnerSingleton;
}
// ********** End ScriptStruct FKindnessReward *****************************************************

// ********** Begin Delegate FOnBehaviorDetected ***************************************************
struct Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms
	{
		FString PlayerID;
		FPlayerBehaviorSnapshot BehaviorData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegate declarations for Harmony Engine events\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations for Harmony Engine events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::NewProp_BehaviorData = { "BehaviorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms, BehaviorData), Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorData_MetaData), NewProp_BehaviorData_MetaData) }; // 3446946508
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::NewProp_BehaviorData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, nullptr, "OnBehaviorDetected__DelegateSignature", Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnBehaviorDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBehaviorDetected_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorDetected, const FString& PlayerID, FPlayerBehaviorSnapshot const& BehaviorData)
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms
	{
		FString PlayerID;
		FPlayerBehaviorSnapshot BehaviorData;
	};
	_Script_AuracronHarmonyEngineBridge_eventOnBehaviorDetected_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.BehaviorData=BehaviorData;
	OnBehaviorDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBehaviorDetected *****************************************************

// ********** Begin Delegate FOnInterventionTriggered **********************************************
struct Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms
	{
		FString PlayerID;
		FHarmonyInterventionData InterventionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::NewProp_InterventionData = { "InterventionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms, InterventionData), Z_Construct_UScriptStruct_FHarmonyInterventionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionData_MetaData), NewProp_InterventionData_MetaData) }; // 1406652677
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::NewProp_InterventionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, nullptr, "OnInterventionTriggered__DelegateSignature", Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnInterventionTriggered__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInterventionTriggered_DelegateWrapper(const FMulticastScriptDelegate& OnInterventionTriggered, const FString& PlayerID, FHarmonyInterventionData const& InterventionData)
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms
	{
		FString PlayerID;
		FHarmonyInterventionData InterventionData;
	};
	_Script_AuracronHarmonyEngineBridge_eventOnInterventionTriggered_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.InterventionData=InterventionData;
	OnInterventionTriggered.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnInterventionTriggered ************************************************

// ********** Begin Delegate FOnCommunityHealing ***************************************************
struct Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms
	{
		FString HealerID;
		FString VictimID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictimID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VictimID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::NewProp_HealerID = { "HealerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms, HealerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerID_MetaData), NewProp_HealerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::NewProp_VictimID = { "VictimID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms, VictimID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictimID_MetaData), NewProp_VictimID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::NewProp_HealerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::NewProp_VictimID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, nullptr, "OnCommunityHealing__DelegateSignature", Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnCommunityHealing__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCommunityHealing_DelegateWrapper(const FMulticastScriptDelegate& OnCommunityHealing, const FString& HealerID, const FString& VictimID)
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms
	{
		FString HealerID;
		FString VictimID;
	};
	_Script_AuracronHarmonyEngineBridge_eventOnCommunityHealing_Parms Parms;
	Parms.HealerID=HealerID;
	Parms.VictimID=VictimID;
	OnCommunityHealing.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnCommunityHealing *****************************************************

// ********** Begin Delegate FOnKindnessReward *****************************************************
struct Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms
	{
		FString PlayerID;
		FKindnessReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms, Reward), Z_Construct_UScriptStruct_FKindnessReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4200945998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, nullptr, "OnKindnessReward__DelegateSignature", Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnKindnessReward__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnKindnessReward_DelegateWrapper(const FMulticastScriptDelegate& OnKindnessReward, const FString& PlayerID, FKindnessReward const& Reward)
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms
	{
		FString PlayerID;
		FKindnessReward Reward;
	};
	_Script_AuracronHarmonyEngineBridge_eventOnKindnessReward_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.Reward=Reward;
	OnKindnessReward.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnKindnessReward *******************************************************

// ********** Begin Delegate FOnHarmonyLevelChanged ************************************************
struct Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms
	{
		int32 NewHarmonyLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronHarmonyEngineBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewHarmonyLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::NewProp_NewHarmonyLevel = { "NewHarmonyLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms, NewHarmonyLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::NewProp_NewHarmonyLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge, nullptr, "OnHarmonyLevelChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::_Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronHarmonyEngineBridge_OnHarmonyLevelChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnHarmonyLevelChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHarmonyLevelChanged, int32 NewHarmonyLevel)
{
	struct _Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms
	{
		int32 NewHarmonyLevel;
	};
	_Script_AuracronHarmonyEngineBridge_eventOnHarmonyLevelChanged_Parms Parms;
	Parms.NewHarmonyLevel=NewHarmonyLevel;
	OnHarmonyLevelChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnHarmonyLevelChanged **************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EHarmonyBehaviorType_StaticEnum, TEXT("EHarmonyBehaviorType"), &Z_Registration_Info_UEnum_EHarmonyBehaviorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4194820134U) },
		{ EEmotionalState_StaticEnum, TEXT("EEmotionalState"), &Z_Registration_Info_UEnum_EEmotionalState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3787130921U) },
		{ EInterventionType_StaticEnum, TEXT("EInterventionType"), &Z_Registration_Info_UEnum_EInterventionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 864251872U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPlayerBehaviorSnapshot::StaticStruct, Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot_Statics::NewStructOps, TEXT("PlayerBehaviorSnapshot"), &Z_Registration_Info_UScriptStruct_FPlayerBehaviorSnapshot, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerBehaviorSnapshot), 3446946508U) },
		{ FHarmonyInterventionData::StaticStruct, Z_Construct_UScriptStruct_FHarmonyInterventionData_Statics::NewStructOps, TEXT("HarmonyInterventionData"), &Z_Registration_Info_UScriptStruct_FHarmonyInterventionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHarmonyInterventionData), 1406652677U) },
		{ FKindnessReward::StaticStruct, Z_Construct_UScriptStruct_FKindnessReward_Statics::NewStructOps, TEXT("KindnessReward"), &Z_Registration_Info_UScriptStruct_FKindnessReward, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FKindnessReward), 4200945998U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_3994421673(TEXT("/Script/AuracronHarmonyEngineBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_AuracronHarmonyEngineBridge_h__Script_AuracronHarmonyEngineBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
