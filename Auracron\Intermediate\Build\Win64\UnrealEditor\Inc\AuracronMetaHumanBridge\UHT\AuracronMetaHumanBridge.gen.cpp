// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMetaHumanBridge/Public/AuracronMetaHumanBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMetaHumanBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod();
AURACRONMETAHUMANBRIDGE_API UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType();
UPackage* Z_Construct_UPackage__Script_AuracronMetaHumanBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EMetaHumanDNADataLayer ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanDNADataLayer;
static UEnum* EMetaHumanDNADataLayer_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanDNADataLayer"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanDNADataLayer>()
{
	return EMetaHumanDNADataLayer_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "All.DisplayName", "All Layers" },
		{ "All.Name", "EMetaHumanDNADataLayer::All" },
		{ "Behavior.DisplayName", "Behavior" },
		{ "Behavior.Name", "EMetaHumanDNADataLayer::Behavior" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enums for MetaHuman operations (moved before delegates)\n" },
#endif
		{ "Definition.DisplayName", "Definition" },
		{ "Definition.Name", "EMetaHumanDNADataLayer::Definition" },
		{ "Descriptor.DisplayName", "Descriptor" },
		{ "Descriptor.Name", "EMetaHumanDNADataLayer::Descriptor" },
		{ "Geometry.DisplayName", "Geometry" },
		{ "Geometry.Name", "EMetaHumanDNADataLayer::Geometry" },
		{ "GeometryWithoutBlendShapes.DisplayName", "Geometry Without Blend Shapes" },
		{ "GeometryWithoutBlendShapes.Name", "EMetaHumanDNADataLayer::GeometryWithoutBlendShapes" },
		{ "MachineLearnedBehavior.DisplayName", "Machine Learned Behavior" },
		{ "MachineLearnedBehavior.Name", "EMetaHumanDNADataLayer::MachineLearnedBehavior" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enums for MetaHuman operations (moved before delegates)" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanDNADataLayer::All", (int64)EMetaHumanDNADataLayer::All },
		{ "EMetaHumanDNADataLayer::Descriptor", (int64)EMetaHumanDNADataLayer::Descriptor },
		{ "EMetaHumanDNADataLayer::Definition", (int64)EMetaHumanDNADataLayer::Definition },
		{ "EMetaHumanDNADataLayer::Behavior", (int64)EMetaHumanDNADataLayer::Behavior },
		{ "EMetaHumanDNADataLayer::Geometry", (int64)EMetaHumanDNADataLayer::Geometry },
		{ "EMetaHumanDNADataLayer::GeometryWithoutBlendShapes", (int64)EMetaHumanDNADataLayer::GeometryWithoutBlendShapes },
		{ "EMetaHumanDNADataLayer::MachineLearnedBehavior", (int64)EMetaHumanDNADataLayer::MachineLearnedBehavior },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanDNADataLayer",
	"EMetaHumanDNADataLayer",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanDNADataLayer_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanDNADataLayer.InnerSingleton;
}
// ********** End Enum EMetaHumanDNADataLayer ******************************************************

// ********** Begin Enum EMetaHumanGender **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanGender;
static UEnum* EMetaHumanGender_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanGender.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanGender.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanGender"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanGender.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanGender>()
{
	return EMetaHumanGender_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Female.DisplayName", "Female" },
		{ "Female.Name", "EMetaHumanGender::Female" },
		{ "Male.DisplayName", "Male" },
		{ "Male.Name", "EMetaHumanGender::Male" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Other.DisplayName", "Other" },
		{ "Other.Name", "EMetaHumanGender::Other" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanGender::Male", (int64)EMetaHumanGender::Male },
		{ "EMetaHumanGender::Female", (int64)EMetaHumanGender::Female },
		{ "EMetaHumanGender::Other", (int64)EMetaHumanGender::Other },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanGender",
	"EMetaHumanGender",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanGender.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanGender.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanGender_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanGender.InnerSingleton;
}
// ********** End Enum EMetaHumanGender ************************************************************

// ********** Begin Enum EMetaHumanArchetype *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanArchetype;
static UEnum* EMetaHumanArchetype_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanArchetype.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanArchetype.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanArchetype"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanArchetype.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanArchetype>()
{
	return EMetaHumanArchetype_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Athletic.DisplayName", "Athletic" },
		{ "Athletic.Name", "EMetaHumanArchetype::Athletic" },
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EMetaHumanArchetype::Custom" },
		{ "Heavy.DisplayName", "Heavy" },
		{ "Heavy.Name", "EMetaHumanArchetype::Heavy" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Slim.DisplayName", "Slim" },
		{ "Slim.Name", "EMetaHumanArchetype::Slim" },
		{ "Standard.DisplayName", "Standard" },
		{ "Standard.Name", "EMetaHumanArchetype::Standard" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanArchetype::Standard", (int64)EMetaHumanArchetype::Standard },
		{ "EMetaHumanArchetype::Athletic", (int64)EMetaHumanArchetype::Athletic },
		{ "EMetaHumanArchetype::Heavy", (int64)EMetaHumanArchetype::Heavy },
		{ "EMetaHumanArchetype::Slim", (int64)EMetaHumanArchetype::Slim },
		{ "EMetaHumanArchetype::Custom", (int64)EMetaHumanArchetype::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanArchetype",
	"EMetaHumanArchetype",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanArchetype.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanArchetype.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanArchetype_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanArchetype.InnerSingleton;
}
// ********** End Enum EMetaHumanArchetype *********************************************************

// ********** Begin Enum EMetaHumanCoordinateSystem ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem;
static UEnum* EMetaHumanCoordinateSystem_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanCoordinateSystem"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanCoordinateSystem>()
{
	return EMetaHumanCoordinateSystem_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "LeftHanded.DisplayName", "Left Handed" },
		{ "LeftHanded.Name", "EMetaHumanCoordinateSystem::LeftHanded" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "RightHanded.DisplayName", "Right Handed" },
		{ "RightHanded.Name", "EMetaHumanCoordinateSystem::RightHanded" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanCoordinateSystem::LeftHanded", (int64)EMetaHumanCoordinateSystem::LeftHanded },
		{ "EMetaHumanCoordinateSystem::RightHanded", (int64)EMetaHumanCoordinateSystem::RightHanded },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanCoordinateSystem",
	"EMetaHumanCoordinateSystem",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanCoordinateSystem_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem.InnerSingleton;
}
// ********** End Enum EMetaHumanCoordinateSystem **************************************************

// ********** Begin Enum EMetaHumanRotationUnit ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanRotationUnit;
static UEnum* EMetaHumanRotationUnit_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanRotationUnit.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanRotationUnit.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanRotationUnit"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanRotationUnit.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanRotationUnit>()
{
	return EMetaHumanRotationUnit_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Degrees.DisplayName", "Degrees" },
		{ "Degrees.Name", "EMetaHumanRotationUnit::Degrees" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Radians.DisplayName", "Radians" },
		{ "Radians.Name", "EMetaHumanRotationUnit::Radians" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanRotationUnit::Degrees", (int64)EMetaHumanRotationUnit::Degrees },
		{ "EMetaHumanRotationUnit::Radians", (int64)EMetaHumanRotationUnit::Radians },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanRotationUnit",
	"EMetaHumanRotationUnit",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanRotationUnit.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanRotationUnit.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanRotationUnit_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanRotationUnit.InnerSingleton;
}
// ********** End Enum EMetaHumanRotationUnit ******************************************************

// ********** Begin Enum EMetaHumanTranslationUnit *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMetaHumanTranslationUnit;
static UEnum* EMetaHumanTranslationUnit_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMetaHumanTranslationUnit"));
	}
	return Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMetaHumanTranslationUnit>()
{
	return EMetaHumanTranslationUnit_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Centimeters.DisplayName", "Centimeters" },
		{ "Centimeters.Name", "EMetaHumanTranslationUnit::Centimeters" },
		{ "Meters.DisplayName", "Meters" },
		{ "Meters.Name", "EMetaHumanTranslationUnit::Meters" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMetaHumanTranslationUnit::Centimeters", (int64)EMetaHumanTranslationUnit::Centimeters },
		{ "EMetaHumanTranslationUnit::Meters", (int64)EMetaHumanTranslationUnit::Meters },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMetaHumanTranslationUnit",
	"EMetaHumanTranslationUnit",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit()
{
	if (!Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMetaHumanTranslationUnit_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMetaHumanTranslationUnit.InnerSingleton;
}
// ********** End Enum EMetaHumanTranslationUnit ***************************************************

// ********** Begin Enum EBlendShapeInterpolationType **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBlendShapeInterpolationType;
static UEnum* EBlendShapeInterpolationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBlendShapeInterpolationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBlendShapeInterpolationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EBlendShapeInterpolationType"));
	}
	return Z_Registration_Info_UEnum_EBlendShapeInterpolationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeInterpolationType>()
{
	return EBlendShapeInterpolationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Bezier.DisplayName", "Bezier" },
		{ "Bezier.Name", "EBlendShapeInterpolationType::Bezier" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === BLENDSHAPE ENUMS AND STRUCTURES ===\n" },
#endif
		{ "Cubic.DisplayName", "Cubic" },
		{ "Cubic.Name", "EBlendShapeInterpolationType::Cubic" },
		{ "Hermite.DisplayName", "Hermite" },
		{ "Hermite.Name", "EBlendShapeInterpolationType::Hermite" },
		{ "Linear.DisplayName", "Linear" },
		{ "Linear.Name", "EBlendShapeInterpolationType::Linear" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Smoothstep.DisplayName", "Smoothstep" },
		{ "Smoothstep.Name", "EBlendShapeInterpolationType::Smoothstep" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== BLENDSHAPE ENUMS AND STRUCTURES ===" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBlendShapeInterpolationType::Linear", (int64)EBlendShapeInterpolationType::Linear },
		{ "EBlendShapeInterpolationType::Cubic", (int64)EBlendShapeInterpolationType::Cubic },
		{ "EBlendShapeInterpolationType::Smoothstep", (int64)EBlendShapeInterpolationType::Smoothstep },
		{ "EBlendShapeInterpolationType::Hermite", (int64)EBlendShapeInterpolationType::Hermite },
		{ "EBlendShapeInterpolationType::Bezier", (int64)EBlendShapeInterpolationType::Bezier },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EBlendShapeInterpolationType",
	"EBlendShapeInterpolationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType()
{
	if (!Z_Registration_Info_UEnum_EBlendShapeInterpolationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBlendShapeInterpolationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeInterpolationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBlendShapeInterpolationType.InnerSingleton;
}
// ********** End Enum EBlendShapeInterpolationType ************************************************

// ********** Begin Enum EFacialExpressionType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EFacialExpressionType;
static UEnum* EFacialExpressionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EFacialExpressionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EFacialExpressionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EFacialExpressionType"));
	}
	return Z_Registration_Info_UEnum_EFacialExpressionType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EFacialExpressionType>()
{
	return EFacialExpressionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Angry.DisplayName", "Angry" },
		{ "Angry.Name", "EFacialExpressionType::Angry" },
		{ "BlueprintType", "true" },
		{ "Contempt.DisplayName", "Contempt" },
		{ "Contempt.Name", "EFacialExpressionType::Contempt" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EFacialExpressionType::Custom" },
		{ "Disgusted.DisplayName", "Disgusted" },
		{ "Disgusted.Name", "EFacialExpressionType::Disgusted" },
		{ "Fearful.DisplayName", "Fearful" },
		{ "Fearful.Name", "EFacialExpressionType::Fearful" },
		{ "Happy.DisplayName", "Happy" },
		{ "Happy.Name", "EFacialExpressionType::Happy" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Neutral.DisplayName", "Neutral" },
		{ "Neutral.Name", "EFacialExpressionType::Neutral" },
		{ "Sad.DisplayName", "Sad" },
		{ "Sad.Name", "EFacialExpressionType::Sad" },
		{ "Surprised.DisplayName", "Surprised" },
		{ "Surprised.Name", "EFacialExpressionType::Surprised" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EFacialExpressionType::Neutral", (int64)EFacialExpressionType::Neutral },
		{ "EFacialExpressionType::Happy", (int64)EFacialExpressionType::Happy },
		{ "EFacialExpressionType::Sad", (int64)EFacialExpressionType::Sad },
		{ "EFacialExpressionType::Angry", (int64)EFacialExpressionType::Angry },
		{ "EFacialExpressionType::Surprised", (int64)EFacialExpressionType::Surprised },
		{ "EFacialExpressionType::Disgusted", (int64)EFacialExpressionType::Disgusted },
		{ "EFacialExpressionType::Fearful", (int64)EFacialExpressionType::Fearful },
		{ "EFacialExpressionType::Contempt", (int64)EFacialExpressionType::Contempt },
		{ "EFacialExpressionType::Custom", (int64)EFacialExpressionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EFacialExpressionType",
	"EFacialExpressionType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType()
{
	if (!Z_Registration_Info_UEnum_EFacialExpressionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EFacialExpressionType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EFacialExpressionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EFacialExpressionType.InnerSingleton;
}
// ********** End Enum EFacialExpressionType *******************************************************

// ********** Begin Enum EBlendShapeValidationType *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EBlendShapeValidationType;
static UEnum* EBlendShapeValidationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EBlendShapeValidationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EBlendShapeValidationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EBlendShapeValidationType"));
	}
	return Z_Registration_Info_UEnum_EBlendShapeValidationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EBlendShapeValidationType>()
{
	return EBlendShapeValidationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic" },
		{ "Basic.Name", "EBlendShapeValidationType::Basic" },
		{ "BlueprintType", "true" },
		{ "Comprehensive.DisplayName", "Comprehensive" },
		{ "Comprehensive.Name", "EBlendShapeValidationType::Comprehensive" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EBlendShapeValidationType::None" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "EBlendShapeValidationType::Performance" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EBlendShapeValidationType::None", (int64)EBlendShapeValidationType::None },
		{ "EBlendShapeValidationType::Basic", (int64)EBlendShapeValidationType::Basic },
		{ "EBlendShapeValidationType::Comprehensive", (int64)EBlendShapeValidationType::Comprehensive },
		{ "EBlendShapeValidationType::Performance", (int64)EBlendShapeValidationType::Performance },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EBlendShapeValidationType",
	"EBlendShapeValidationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType()
{
	if (!Z_Registration_Info_UEnum_EBlendShapeValidationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EBlendShapeValidationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EBlendShapeValidationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EBlendShapeValidationType.InnerSingleton;
}
// ********** End Enum EBlendShapeValidationType ***************************************************

// ********** Begin Enum EJointConstraintType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EJointConstraintType;
static UEnum* EJointConstraintType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EJointConstraintType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EJointConstraintType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EJointConstraintType"));
	}
	return Z_Registration_Info_UEnum_EJointConstraintType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointConstraintType>()
{
	return EJointConstraintType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aim.DisplayName", "Aim" },
		{ "Aim.Name", "EJointConstraintType::Aim" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Joint Manipulation Enums and Structures\n" },
#endif
		{ "LookAt.DisplayName", "Look At" },
		{ "LookAt.Name", "EJointConstraintType::LookAt" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EJointConstraintType::None" },
		{ "Orient.DisplayName", "Orient" },
		{ "Orient.Name", "EJointConstraintType::Orient" },
		{ "Parent.DisplayName", "Parent" },
		{ "Parent.Name", "EJointConstraintType::Parent" },
		{ "Point.DisplayName", "Point" },
		{ "Point.Name", "EJointConstraintType::Point" },
		{ "Position.DisplayName", "Position" },
		{ "Position.Name", "EJointConstraintType::Position" },
		{ "Rotation.DisplayName", "Rotation" },
		{ "Rotation.Name", "EJointConstraintType::Rotation" },
		{ "Scale.DisplayName", "Scale" },
		{ "Scale.Name", "EJointConstraintType::Scale" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Joint Manipulation Enums and Structures" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EJointConstraintType::None", (int64)EJointConstraintType::None },
		{ "EJointConstraintType::Position", (int64)EJointConstraintType::Position },
		{ "EJointConstraintType::Rotation", (int64)EJointConstraintType::Rotation },
		{ "EJointConstraintType::Scale", (int64)EJointConstraintType::Scale },
		{ "EJointConstraintType::LookAt", (int64)EJointConstraintType::LookAt },
		{ "EJointConstraintType::Aim", (int64)EJointConstraintType::Aim },
		{ "EJointConstraintType::Parent", (int64)EJointConstraintType::Parent },
		{ "EJointConstraintType::Point", (int64)EJointConstraintType::Point },
		{ "EJointConstraintType::Orient", (int64)EJointConstraintType::Orient },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EJointConstraintType",
	"EJointConstraintType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType()
{
	if (!Z_Registration_Info_UEnum_EJointConstraintType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EJointConstraintType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EJointConstraintType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EJointConstraintType.InnerSingleton;
}
// ********** End Enum EJointConstraintType ********************************************************

// ********** Begin Enum EJointTransformSpace ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EJointTransformSpace;
static UEnum* EJointTransformSpace_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EJointTransformSpace.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EJointTransformSpace.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EJointTransformSpace"));
	}
	return Z_Registration_Info_UEnum_EJointTransformSpace.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EJointTransformSpace>()
{
	return EJointTransformSpace_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Component.DisplayName", "Component Space" },
		{ "Component.Name", "EJointTransformSpace::Component" },
		{ "Local.DisplayName", "Local Space" },
		{ "Local.Name", "EJointTransformSpace::Local" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Parent.DisplayName", "Parent Space" },
		{ "Parent.Name", "EJointTransformSpace::Parent" },
		{ "World.DisplayName", "World Space" },
		{ "World.Name", "EJointTransformSpace::World" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EJointTransformSpace::Local", (int64)EJointTransformSpace::Local },
		{ "EJointTransformSpace::World", (int64)EJointTransformSpace::World },
		{ "EJointTransformSpace::Parent", (int64)EJointTransformSpace::Parent },
		{ "EJointTransformSpace::Component", (int64)EJointTransformSpace::Component },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EJointTransformSpace",
	"EJointTransformSpace",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace()
{
	if (!Z_Registration_Info_UEnum_EJointTransformSpace.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EJointTransformSpace.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EJointTransformSpace_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EJointTransformSpace.InnerSingleton;
}
// ********** End Enum EJointTransformSpace ********************************************************

// ********** Begin Enum EClothingMaterialType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingMaterialType;
static UEnum* EClothingMaterialType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingMaterialType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingMaterialType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingMaterialType"));
	}
	return Z_Registration_Info_UEnum_EClothingMaterialType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaterialType>()
{
	return EClothingMaterialType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chainmail.DisplayName", "Chainmail" },
		{ "Chainmail.Name", "EClothingMaterialType::Chainmail" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Clothing material type enumeration */" },
#endif
		{ "Cotton.DisplayName", "Cotton" },
		{ "Cotton.Name", "EClothingMaterialType::Cotton" },
		{ "Denim.DisplayName", "Denim" },
		{ "Denim.Name", "EClothingMaterialType::Denim" },
		{ "Fabric.DisplayName", "Generic Fabric" },
		{ "Fabric.Name", "EClothingMaterialType::Fabric" },
		{ "Leather.DisplayName", "Leather" },
		{ "Leather.Name", "EClothingMaterialType::Leather" },
		{ "Linen.DisplayName", "Linen" },
		{ "Linen.Name", "EClothingMaterialType::Linen" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Plate.DisplayName", "Plate Armor" },
		{ "Plate.Name", "EClothingMaterialType::Plate" },
		{ "Silk.DisplayName", "Silk" },
		{ "Silk.Name", "EClothingMaterialType::Silk" },
		{ "Synthetic.DisplayName", "Synthetic Material" },
		{ "Synthetic.Name", "EClothingMaterialType::Synthetic" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clothing material type enumeration" },
#endif
		{ "Wool.DisplayName", "Wool" },
		{ "Wool.Name", "EClothingMaterialType::Wool" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingMaterialType::Cotton", (int64)EClothingMaterialType::Cotton },
		{ "EClothingMaterialType::Silk", (int64)EClothingMaterialType::Silk },
		{ "EClothingMaterialType::Leather", (int64)EClothingMaterialType::Leather },
		{ "EClothingMaterialType::Chainmail", (int64)EClothingMaterialType::Chainmail },
		{ "EClothingMaterialType::Plate", (int64)EClothingMaterialType::Plate },
		{ "EClothingMaterialType::Fabric", (int64)EClothingMaterialType::Fabric },
		{ "EClothingMaterialType::Denim", (int64)EClothingMaterialType::Denim },
		{ "EClothingMaterialType::Wool", (int64)EClothingMaterialType::Wool },
		{ "EClothingMaterialType::Linen", (int64)EClothingMaterialType::Linen },
		{ "EClothingMaterialType::Synthetic", (int64)EClothingMaterialType::Synthetic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingMaterialType",
	"EClothingMaterialType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType()
{
	if (!Z_Registration_Info_UEnum_EClothingMaterialType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingMaterialType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaterialType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingMaterialType.InnerSingleton;
}
// ********** End Enum EClothingMaterialType *******************************************************

// ********** Begin Enum ELipSyncType **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELipSyncType;
static UEnum* ELipSyncType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELipSyncType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELipSyncType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ELipSyncType"));
	}
	return Z_Registration_Info_UEnum_ELipSyncType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ELipSyncType>()
{
	return ELipSyncType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio2Face.DisplayName", "Audio2Face" },
		{ "Audio2Face.Name", "ELipSyncType::Audio2Face" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lip sync type enumeration */" },
#endif
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "ELipSyncType::Hybrid" },
		{ "MachineLearning.DisplayName", "Machine Learning" },
		{ "MachineLearning.Name", "ELipSyncType::MachineLearning" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Phoneme.DisplayName", "Phoneme Based" },
		{ "Phoneme.Name", "ELipSyncType::Phoneme" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "ELipSyncType::Procedural" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lip sync type enumeration" },
#endif
		{ "Viseme.DisplayName", "Viseme Based" },
		{ "Viseme.Name", "ELipSyncType::Viseme" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELipSyncType::Phoneme", (int64)ELipSyncType::Phoneme },
		{ "ELipSyncType::Viseme", (int64)ELipSyncType::Viseme },
		{ "ELipSyncType::Audio2Face", (int64)ELipSyncType::Audio2Face },
		{ "ELipSyncType::Procedural", (int64)ELipSyncType::Procedural },
		{ "ELipSyncType::MachineLearning", (int64)ELipSyncType::MachineLearning },
		{ "ELipSyncType::Hybrid", (int64)ELipSyncType::Hybrid },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ELipSyncType",
	"ELipSyncType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType()
{
	if (!Z_Registration_Info_UEnum_ELipSyncType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELipSyncType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ELipSyncType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELipSyncType.InnerSingleton;
}
// ********** End Enum ELipSyncType ****************************************************************

// ********** Begin Enum EEmotionMappingType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEmotionMappingType;
static UEnum* EEmotionMappingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEmotionMappingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEmotionMappingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EEmotionMappingType"));
	}
	return Z_Registration_Info_UEnum_EEmotionMappingType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EEmotionMappingType>()
{
	return EEmotionMappingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic Emotions" },
		{ "Basic.Name", "EEmotionMappingType::Basic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Emotion mapping type enumeration */" },
#endif
		{ "Custom.DisplayName", "Custom Mapping" },
		{ "Custom.Name", "EEmotionMappingType::Custom" },
		{ "Extended.DisplayName", "Extended Emotions" },
		{ "Extended.Name", "EEmotionMappingType::Extended" },
		{ "FACS.DisplayName", "FACS (Facial Action Coding System)" },
		{ "FACS.Name", "EEmotionMappingType::FACS" },
		{ "MachineLearning.DisplayName", "Machine Learning" },
		{ "MachineLearning.Name", "EEmotionMappingType::MachineLearning" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Procedural.DisplayName", "Procedural" },
		{ "Procedural.Name", "EEmotionMappingType::Procedural" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Emotion mapping type enumeration" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEmotionMappingType::Basic", (int64)EEmotionMappingType::Basic },
		{ "EEmotionMappingType::Extended", (int64)EEmotionMappingType::Extended },
		{ "EEmotionMappingType::FACS", (int64)EEmotionMappingType::FACS },
		{ "EEmotionMappingType::Custom", (int64)EEmotionMappingType::Custom },
		{ "EEmotionMappingType::Procedural", (int64)EEmotionMappingType::Procedural },
		{ "EEmotionMappingType::MachineLearning", (int64)EEmotionMappingType::MachineLearning },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EEmotionMappingType",
	"EEmotionMappingType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType()
{
	if (!Z_Registration_Info_UEnum_EEmotionMappingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEmotionMappingType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EEmotionMappingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEmotionMappingType.InnerSingleton;
}
// ********** End Enum EEmotionMappingType *********************************************************

// ********** Begin Enum EPoseGenerationMethod *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPoseGenerationMethod;
static UEnum* EPoseGenerationMethod_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPoseGenerationMethod.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPoseGenerationMethod.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EPoseGenerationMethod"));
	}
	return Z_Registration_Info_UEnum_EPoseGenerationMethod.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPoseGenerationMethod>()
{
	return EPoseGenerationMethod_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pose generation method enumeration */" },
#endif
		{ "Hybrid.DisplayName", "Hybrid Approach" },
		{ "Hybrid.Name", "EPoseGenerationMethod::Hybrid" },
		{ "MachineLearning.DisplayName", "Machine Learning" },
		{ "MachineLearning.Name", "EPoseGenerationMethod::MachineLearning" },
		{ "Manual.DisplayName", "Manual Pose Creation" },
		{ "Manual.Name", "EPoseGenerationMethod::Manual" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "MotionCapture.DisplayName", "Motion Capture Based" },
		{ "MotionCapture.Name", "EPoseGenerationMethod::MotionCapture" },
		{ "Procedural.DisplayName", "Procedural Generation" },
		{ "Procedural.Name", "EPoseGenerationMethod::Procedural" },
		{ "Template.DisplayName", "Template Based" },
		{ "Template.Name", "EPoseGenerationMethod::Template" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pose generation method enumeration" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPoseGenerationMethod::Manual", (int64)EPoseGenerationMethod::Manual },
		{ "EPoseGenerationMethod::Procedural", (int64)EPoseGenerationMethod::Procedural },
		{ "EPoseGenerationMethod::MotionCapture", (int64)EPoseGenerationMethod::MotionCapture },
		{ "EPoseGenerationMethod::MachineLearning", (int64)EPoseGenerationMethod::MachineLearning },
		{ "EPoseGenerationMethod::Hybrid", (int64)EPoseGenerationMethod::Hybrid },
		{ "EPoseGenerationMethod::Template", (int64)EPoseGenerationMethod::Template },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EPoseGenerationMethod",
	"EPoseGenerationMethod",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod()
{
	if (!Z_Registration_Info_UEnum_EPoseGenerationMethod.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPoseGenerationMethod.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EPoseGenerationMethod_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPoseGenerationMethod.InnerSingleton;
}
// ********** End Enum EPoseGenerationMethod *******************************************************

// ********** Begin Enum EAnimationBlueprintQuality ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAnimationBlueprintQuality;
static UEnum* EAnimationBlueprintQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAnimationBlueprintQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAnimationBlueprintQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EAnimationBlueprintQuality"));
	}
	return Z_Registration_Info_UEnum_EAnimationBlueprintQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EAnimationBlueprintQuality>()
{
	return EAnimationBlueprintQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive Quality" },
		{ "Adaptive.Name", "EAnimationBlueprintQuality::Adaptive" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Animation Blueprint quality level */" },
#endif
		{ "Custom.DisplayName", "Custom Quality" },
		{ "Custom.Name", "EAnimationBlueprintQuality::Custom" },
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EAnimationBlueprintQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EAnimationBlueprintQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EAnimationBlueprintQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "PerformanceBased.DisplayName", "Performance Based" },
		{ "PerformanceBased.Name", "EAnimationBlueprintQuality::PerformanceBased" },
		{ "QualityBased.DisplayName", "Quality Based" },
		{ "QualityBased.Name", "EAnimationBlueprintQuality::QualityBased" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation Blueprint quality level" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EAnimationBlueprintQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAnimationBlueprintQuality::Low", (int64)EAnimationBlueprintQuality::Low },
		{ "EAnimationBlueprintQuality::Medium", (int64)EAnimationBlueprintQuality::Medium },
		{ "EAnimationBlueprintQuality::High", (int64)EAnimationBlueprintQuality::High },
		{ "EAnimationBlueprintQuality::Ultra", (int64)EAnimationBlueprintQuality::Ultra },
		{ "EAnimationBlueprintQuality::Custom", (int64)EAnimationBlueprintQuality::Custom },
		{ "EAnimationBlueprintQuality::Adaptive", (int64)EAnimationBlueprintQuality::Adaptive },
		{ "EAnimationBlueprintQuality::PerformanceBased", (int64)EAnimationBlueprintQuality::PerformanceBased },
		{ "EAnimationBlueprintQuality::QualityBased", (int64)EAnimationBlueprintQuality::QualityBased },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EAnimationBlueprintQuality",
	"EAnimationBlueprintQuality",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality()
{
	if (!Z_Registration_Info_UEnum_EAnimationBlueprintQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAnimationBlueprintQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EAnimationBlueprintQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAnimationBlueprintQuality.InnerSingleton;
}
// ********** End Enum EAnimationBlueprintQuality **************************************************

// ********** Begin Enum EPhonemeType **************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPhonemeType;
static UEnum* EPhonemeType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPhonemeType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPhonemeType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EPhonemeType"));
	}
	return Z_Registration_Info_UEnum_EPhonemeType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EPhonemeType>()
{
	return EPhonemeType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "A.DisplayName", "A (ah)" },
		{ "A.Name", "EPhonemeType::A" },
		{ "B.DisplayName", "B (buh)" },
		{ "B.Name", "EPhonemeType::B" },
		{ "BlueprintType", "true" },
		{ "C.DisplayName", "C (kuh)" },
		{ "C.Name", "EPhonemeType::C" },
		{ "CH.DisplayName", "CH (chuh)" },
		{ "CH.Name", "EPhonemeType::CH" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Phoneme type for lip sync */" },
#endif
		{ "D.DisplayName", "D (duh)" },
		{ "D.Name", "EPhonemeType::D" },
		{ "E.DisplayName", "E (eh)" },
		{ "E.Name", "EPhonemeType::E" },
		{ "F.DisplayName", "F (fuh)" },
		{ "F.Name", "EPhonemeType::F" },
		{ "G.DisplayName", "G (guh)" },
		{ "G.Name", "EPhonemeType::G" },
		{ "H.DisplayName", "H (huh)" },
		{ "H.Name", "EPhonemeType::H" },
		{ "I.DisplayName", "I (ih)" },
		{ "I.Name", "EPhonemeType::I" },
		{ "J.DisplayName", "J (juh)" },
		{ "J.Name", "EPhonemeType::J" },
		{ "K.DisplayName", "K (kuh)" },
		{ "K.Name", "EPhonemeType::K" },
		{ "L.DisplayName", "L (luh)" },
		{ "L.Name", "EPhonemeType::L" },
		{ "M.DisplayName", "M (muh)" },
		{ "M.Name", "EPhonemeType::M" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "N.DisplayName", "N (nuh)" },
		{ "N.Name", "EPhonemeType::N" },
		{ "O.DisplayName", "O (oh)" },
		{ "O.Name", "EPhonemeType::O" },
		{ "P.DisplayName", "P (puh)" },
		{ "P.Name", "EPhonemeType::P" },
		{ "Q.DisplayName", "Q (kuh)" },
		{ "Q.Name", "EPhonemeType::Q" },
		{ "R.DisplayName", "R (ruh)" },
		{ "R.Name", "EPhonemeType::R" },
		{ "S.DisplayName", "S (suh)" },
		{ "S.Name", "EPhonemeType::S" },
		{ "SH.DisplayName", "SH (shuh)" },
		{ "SH.Name", "EPhonemeType::SH" },
		{ "Silent.DisplayName", "Silent" },
		{ "Silent.Name", "EPhonemeType::Silent" },
		{ "T.DisplayName", "T (tuh)" },
		{ "T.Name", "EPhonemeType::T" },
		{ "TH.DisplayName", "TH (thuh)" },
		{ "TH.Name", "EPhonemeType::TH" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Phoneme type for lip sync" },
#endif
		{ "U.DisplayName", "U (uh)" },
		{ "U.Name", "EPhonemeType::U" },
		{ "V.DisplayName", "V (vuh)" },
		{ "V.Name", "EPhonemeType::V" },
		{ "W.DisplayName", "W (wuh)" },
		{ "W.Name", "EPhonemeType::W" },
		{ "X.DisplayName", "X (ksuh)" },
		{ "X.Name", "EPhonemeType::X" },
		{ "Y.DisplayName", "Y (yuh)" },
		{ "Y.Name", "EPhonemeType::Y" },
		{ "Z.DisplayName", "Z (zuh)" },
		{ "Z.Name", "EPhonemeType::Z" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPhonemeType::A", (int64)EPhonemeType::A },
		{ "EPhonemeType::E", (int64)EPhonemeType::E },
		{ "EPhonemeType::I", (int64)EPhonemeType::I },
		{ "EPhonemeType::O", (int64)EPhonemeType::O },
		{ "EPhonemeType::U", (int64)EPhonemeType::U },
		{ "EPhonemeType::B", (int64)EPhonemeType::B },
		{ "EPhonemeType::C", (int64)EPhonemeType::C },
		{ "EPhonemeType::D", (int64)EPhonemeType::D },
		{ "EPhonemeType::F", (int64)EPhonemeType::F },
		{ "EPhonemeType::G", (int64)EPhonemeType::G },
		{ "EPhonemeType::H", (int64)EPhonemeType::H },
		{ "EPhonemeType::J", (int64)EPhonemeType::J },
		{ "EPhonemeType::K", (int64)EPhonemeType::K },
		{ "EPhonemeType::L", (int64)EPhonemeType::L },
		{ "EPhonemeType::M", (int64)EPhonemeType::M },
		{ "EPhonemeType::N", (int64)EPhonemeType::N },
		{ "EPhonemeType::P", (int64)EPhonemeType::P },
		{ "EPhonemeType::Q", (int64)EPhonemeType::Q },
		{ "EPhonemeType::R", (int64)EPhonemeType::R },
		{ "EPhonemeType::S", (int64)EPhonemeType::S },
		{ "EPhonemeType::T", (int64)EPhonemeType::T },
		{ "EPhonemeType::V", (int64)EPhonemeType::V },
		{ "EPhonemeType::W", (int64)EPhonemeType::W },
		{ "EPhonemeType::X", (int64)EPhonemeType::X },
		{ "EPhonemeType::Y", (int64)EPhonemeType::Y },
		{ "EPhonemeType::Z", (int64)EPhonemeType::Z },
		{ "EPhonemeType::TH", (int64)EPhonemeType::TH },
		{ "EPhonemeType::SH", (int64)EPhonemeType::SH },
		{ "EPhonemeType::CH", (int64)EPhonemeType::CH },
		{ "EPhonemeType::Silent", (int64)EPhonemeType::Silent },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EPhonemeType",
	"EPhonemeType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType()
{
	if (!Z_Registration_Info_UEnum_EPhonemeType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPhonemeType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EPhonemeType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPhonemeType.InnerSingleton;
}
// ********** End Enum EPhonemeType ****************************************************************

// ********** Begin Enum EClothingFittingMethod ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingFittingMethod;
static UEnum* EClothingFittingMethod_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingFittingMethod.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingFittingMethod.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingFittingMethod"));
	}
	return Z_Registration_Info_UEnum_EClothingFittingMethod.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingFittingMethod>()
{
	return EClothingFittingMethod_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic Fitting" },
		{ "Automatic.Name", "EClothingFittingMethod::Automatic" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Move enums before structures that use them\n" },
#endif
		{ "Manual.DisplayName", "Manual Fitting" },
		{ "Manual.Name", "EClothingFittingMethod::Manual" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "MorphTarget.DisplayName", "Morph Target Based" },
		{ "MorphTarget.Name", "EClothingFittingMethod::MorphTarget" },
		{ "Procedural.DisplayName", "Procedural Fitting" },
		{ "Procedural.Name", "EClothingFittingMethod::Procedural" },
		{ "TemplateBase.DisplayName", "Template Based" },
		{ "TemplateBase.Name", "EClothingFittingMethod::TemplateBase" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Move enums before structures that use them" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingFittingMethod::Automatic", (int64)EClothingFittingMethod::Automatic },
		{ "EClothingFittingMethod::Manual", (int64)EClothingFittingMethod::Manual },
		{ "EClothingFittingMethod::Procedural", (int64)EClothingFittingMethod::Procedural },
		{ "EClothingFittingMethod::TemplateBase", (int64)EClothingFittingMethod::TemplateBase },
		{ "EClothingFittingMethod::MorphTarget", (int64)EClothingFittingMethod::MorphTarget },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingFittingMethod",
	"EClothingFittingMethod",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod()
{
	if (!Z_Registration_Info_UEnum_EClothingFittingMethod.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingFittingMethod.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingFittingMethod_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingFittingMethod.InnerSingleton;
}
// ********** End Enum EClothingFittingMethod ******************************************************

// ********** Begin Enum EClothingLODMode **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingLODMode;
static UEnum* EClothingLODMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingLODMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingLODMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingLODMode"));
	}
	return Z_Registration_Info_UEnum_EClothingLODMode.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingLODMode>()
{
	return EClothingLODMode_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Automatic.DisplayName", "Automatic LOD" },
		{ "Automatic.Name", "EClothingLODMode::Automatic" },
		{ "BlueprintType", "true" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EClothingLODMode::DistanceBased" },
		{ "Manual.DisplayName", "Manual LOD" },
		{ "Manual.Name", "EClothingLODMode::Manual" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "PerformanceBased.DisplayName", "Performance Based" },
		{ "PerformanceBased.Name", "EClothingLODMode::PerformanceBased" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingLODMode::Automatic", (int64)EClothingLODMode::Automatic },
		{ "EClothingLODMode::Manual", (int64)EClothingLODMode::Manual },
		{ "EClothingLODMode::DistanceBased", (int64)EClothingLODMode::DistanceBased },
		{ "EClothingLODMode::PerformanceBased", (int64)EClothingLODMode::PerformanceBased },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingLODMode",
	"EClothingLODMode",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode()
{
	if (!Z_Registration_Info_UEnum_EClothingLODMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingLODMode.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLODMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingLODMode.InnerSingleton;
}
// ********** End Enum EClothingLODMode ************************************************************

// ********** Begin Enum EClothingPaintTool ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingPaintTool;
static UEnum* EClothingPaintTool_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingPaintTool.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingPaintTool.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingPaintTool"));
	}
	return Z_Registration_Info_UEnum_EClothingPaintTool.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingPaintTool>()
{
	return EClothingPaintTool_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Brush.DisplayName", "Brush" },
		{ "Brush.Name", "EClothingPaintTool::Brush" },
		{ "Erase.DisplayName", "Erase" },
		{ "Erase.Name", "EClothingPaintTool::Erase" },
		{ "Fill.DisplayName", "Fill" },
		{ "Fill.Name", "EClothingPaintTool::Fill" },
		{ "Gradient.DisplayName", "Gradient" },
		{ "Gradient.Name", "EClothingPaintTool::Gradient" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Smooth.DisplayName", "Smooth" },
		{ "Smooth.Name", "EClothingPaintTool::Smooth" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingPaintTool::Brush", (int64)EClothingPaintTool::Brush },
		{ "EClothingPaintTool::Gradient", (int64)EClothingPaintTool::Gradient },
		{ "EClothingPaintTool::Smooth", (int64)EClothingPaintTool::Smooth },
		{ "EClothingPaintTool::Fill", (int64)EClothingPaintTool::Fill },
		{ "EClothingPaintTool::Erase", (int64)EClothingPaintTool::Erase },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingPaintTool",
	"EClothingPaintTool",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool()
{
	if (!Z_Registration_Info_UEnum_EClothingPaintTool.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingPaintTool.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingPaintTool_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingPaintTool.InnerSingleton;
}
// ********** End Enum EClothingPaintTool **********************************************************

// ********** Begin Enum EClothingLayerType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingLayerType;
static UEnum* EClothingLayerType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingLayerType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingLayerType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingLayerType"));
	}
	return Z_Registration_Info_UEnum_EClothingLayerType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingLayerType>()
{
	return EClothingLayerType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AccessoryLayer.DisplayName", "Accessory Layer" },
		{ "AccessoryLayer.Name", "EClothingLayerType::AccessoryLayer" },
		{ "BaseLayer.DisplayName", "Base Layer" },
		{ "BaseLayer.Name", "EClothingLayerType::BaseLayer" },
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "OverLayer.DisplayName", "Over Layer" },
		{ "OverLayer.Name", "EClothingLayerType::OverLayer" },
		{ "UnderLayer.DisplayName", "Under Layer" },
		{ "UnderLayer.Name", "EClothingLayerType::UnderLayer" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingLayerType::BaseLayer", (int64)EClothingLayerType::BaseLayer },
		{ "EClothingLayerType::OverLayer", (int64)EClothingLayerType::OverLayer },
		{ "EClothingLayerType::UnderLayer", (int64)EClothingLayerType::UnderLayer },
		{ "EClothingLayerType::AccessoryLayer", (int64)EClothingLayerType::AccessoryLayer },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingLayerType",
	"EClothingLayerType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType()
{
	if (!Z_Registration_Info_UEnum_EClothingLayerType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingLayerType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingLayerType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingLayerType.InnerSingleton;
}
// ********** End Enum EClothingLayerType **********************************************************

// ********** Begin Enum EClothingMaskTarget *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingMaskTarget;
static UEnum* EClothingMaskTarget_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingMaskTarget.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingMaskTarget.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingMaskTarget"));
	}
	return Z_Registration_Info_UEnum_EClothingMaskTarget.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingMaskTarget>()
{
	return EClothingMaskTarget_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AnimDriveDamping.DisplayName", "Animation Drive Damping" },
		{ "AnimDriveDamping.Name", "EClothingMaskTarget::AnimDriveDamping" },
		{ "AnimDriveStiffness.DisplayName", "Animation Drive Stiffness" },
		{ "AnimDriveStiffness.Name", "EClothingMaskTarget::AnimDriveStiffness" },
		{ "BackstopDistance.DisplayName", "Backstop Distance" },
		{ "BackstopDistance.Name", "EClothingMaskTarget::BackstopDistance" },
		{ "BackstopRadius.DisplayName", "Backstop Radius" },
		{ "BackstopRadius.Name", "EClothingMaskTarget::BackstopRadius" },
		{ "BlueprintType", "true" },
		{ "MaxDistance.DisplayName", "Max Distance" },
		{ "MaxDistance.Name", "EClothingMaskTarget::MaxDistance" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingMaskTarget::MaxDistance", (int64)EClothingMaskTarget::MaxDistance },
		{ "EClothingMaskTarget::BackstopDistance", (int64)EClothingMaskTarget::BackstopDistance },
		{ "EClothingMaskTarget::BackstopRadius", (int64)EClothingMaskTarget::BackstopRadius },
		{ "EClothingMaskTarget::AnimDriveStiffness", (int64)EClothingMaskTarget::AnimDriveStiffness },
		{ "EClothingMaskTarget::AnimDriveDamping", (int64)EClothingMaskTarget::AnimDriveDamping },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingMaskTarget",
	"EClothingMaskTarget",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget()
{
	if (!Z_Registration_Info_UEnum_EClothingMaskTarget.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingMaskTarget.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingMaskTarget_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingMaskTarget.InnerSingleton;
}
// ********** End Enum EClothingMaskTarget *********************************************************

// ********** Begin Enum EMeshDeformationType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMeshDeformationType;
static UEnum* EMeshDeformationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMeshDeformationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMeshDeformationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EMeshDeformationType"));
	}
	return Z_Registration_Info_UEnum_EMeshDeformationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EMeshDeformationType>()
{
	return EMeshDeformationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined Deformation" },
		{ "Combined.Name", "EMeshDeformationType::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Mesh deformation enums (moved to global scope)\n" },
#endif
		{ "LOD.DisplayName", "LOD Generation" },
		{ "LOD.Name", "EMeshDeformationType::LOD" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Normal.DisplayName", "Normal Recalculation" },
		{ "Normal.Name", "EMeshDeformationType::Normal" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh deformation enums (moved to global scope)" },
#endif
		{ "UV.DisplayName", "UV Mapping" },
		{ "UV.Name", "EMeshDeformationType::UV" },
		{ "Vertex.DisplayName", "Vertex Deformation" },
		{ "Vertex.Name", "EMeshDeformationType::Vertex" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EMeshDeformationType::Vertex", (int64)EMeshDeformationType::Vertex },
		{ "EMeshDeformationType::Normal", (int64)EMeshDeformationType::Normal },
		{ "EMeshDeformationType::UV", (int64)EMeshDeformationType::UV },
		{ "EMeshDeformationType::LOD", (int64)EMeshDeformationType::LOD },
		{ "EMeshDeformationType::Combined", (int64)EMeshDeformationType::Combined },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EMeshDeformationType",
	"EMeshDeformationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType()
{
	if (!Z_Registration_Info_UEnum_EMeshDeformationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMeshDeformationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EMeshDeformationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMeshDeformationType.InnerSingleton;
}
// ********** End Enum EMeshDeformationType ********************************************************

// ********** Begin Enum EHairCardQuality **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EHairCardQuality;
static UEnum* EHairCardQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EHairCardQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EHairCardQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EHairCardQuality"));
	}
	return Z_Registration_Info_UEnum_EHairCardQuality.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EHairCardQuality>()
{
	return EHairCardQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Missing enums definitions\n" },
#endif
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EHairCardQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EHairCardQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EHairCardQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Missing enums definitions" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EHairCardQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EHairCardQuality::Low", (int64)EHairCardQuality::Low },
		{ "EHairCardQuality::Medium", (int64)EHairCardQuality::Medium },
		{ "EHairCardQuality::High", (int64)EHairCardQuality::High },
		{ "EHairCardQuality::Ultra", (int64)EHairCardQuality::Ultra },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EHairCardQuality",
	"EHairCardQuality",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality()
{
	if (!Z_Registration_Info_UEnum_EHairCardQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EHairCardQuality.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EHairCardQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EHairCardQuality.InnerSingleton;
}
// ********** End Enum EHairCardQuality ************************************************************

// ********** Begin Enum EClothingCollisionType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingCollisionType;
static UEnum* EClothingCollisionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingCollisionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingCollisionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingCollisionType"));
	}
	return Z_Registration_Info_UEnum_EClothingCollisionType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingCollisionType>()
{
	return EClothingCollisionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Basic.DisplayName", "Basic Collision" },
		{ "Basic.Name", "EClothingCollisionType::Basic" },
		{ "BlueprintType", "true" },
		{ "Convex.DisplayName", "Convex Collision" },
		{ "Convex.Name", "EClothingCollisionType::Convex" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "None.DisplayName", "No Collision" },
		{ "None.Name", "EClothingCollisionType::None" },
		{ "Precise.DisplayName", "Precise Collision" },
		{ "Precise.Name", "EClothingCollisionType::Precise" },
		{ "TriangleMesh.DisplayName", "Triangle Mesh Collision" },
		{ "TriangleMesh.Name", "EClothingCollisionType::TriangleMesh" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingCollisionType::None", (int64)EClothingCollisionType::None },
		{ "EClothingCollisionType::Basic", (int64)EClothingCollisionType::Basic },
		{ "EClothingCollisionType::Precise", (int64)EClothingCollisionType::Precise },
		{ "EClothingCollisionType::Convex", (int64)EClothingCollisionType::Convex },
		{ "EClothingCollisionType::TriangleMesh", (int64)EClothingCollisionType::TriangleMesh },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingCollisionType",
	"EClothingCollisionType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType()
{
	if (!Z_Registration_Info_UEnum_EClothingCollisionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingCollisionType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingCollisionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingCollisionType.InnerSingleton;
}
// ********** End Enum EClothingCollisionType ******************************************************

// ********** Begin Enum EClothingQualityLevel *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EClothingQualityLevel;
static UEnum* EClothingQualityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EClothingQualityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EClothingQualityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("EClothingQualityLevel"));
	}
	return Z_Registration_Info_UEnum_EClothingQualityLevel.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<EClothingQualityLevel>()
{
	return EClothingQualityLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EClothingQualityLevel::Cinematic" },
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EClothingQualityLevel::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EClothingQualityLevel::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EClothingQualityLevel::Medium" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EClothingQualityLevel::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EClothingQualityLevel::Low", (int64)EClothingQualityLevel::Low },
		{ "EClothingQualityLevel::Medium", (int64)EClothingQualityLevel::Medium },
		{ "EClothingQualityLevel::High", (int64)EClothingQualityLevel::High },
		{ "EClothingQualityLevel::Ultra", (int64)EClothingQualityLevel::Ultra },
		{ "EClothingQualityLevel::Cinematic", (int64)EClothingQualityLevel::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"EClothingQualityLevel",
	"EClothingQualityLevel",
	Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel()
{
	if (!Z_Registration_Info_UEnum_EClothingQualityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EClothingQualityLevel.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_EClothingQualityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EClothingQualityLevel.InnerSingleton;
}
// ********** End Enum EClothingQualityLevel *******************************************************

// ********** Begin Enum ERigTransformationType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERigTransformationType;
static UEnum* ERigTransformationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERigTransformationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERigTransformationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType, (UObject*)Z_Construct_UPackage__Script_AuracronMetaHumanBridge(), TEXT("ERigTransformationType"));
	}
	return Z_Registration_Info_UEnum_ERigTransformationType.OuterSingleton;
}
template<> AURACRONMETAHUMANBRIDGE_API UEnum* StaticEnum<ERigTransformationType>()
{
	return ERigTransformationType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BoneScaling.DisplayName", "Bone Scaling" },
		{ "BoneScaling.Name", "ERigTransformationType::BoneScaling" },
		{ "Combined.DisplayName", "Combined Transformations" },
		{ "Combined.Name", "ERigTransformationType::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// ETextureBlendMode is defined in AuracronTextureGeneration.h\n" },
#endif
		{ "Constraint.DisplayName", "Constraint Modification" },
		{ "Constraint.Name", "ERigTransformationType::Constraint" },
		{ "FKSetup.DisplayName", "FK Setup" },
		{ "FKSetup.Name", "ERigTransformationType::FKSetup" },
		{ "IKSetup.DisplayName", "IK Setup" },
		{ "IKSetup.Name", "ERigTransformationType::IKSetup" },
		{ "ModuleRelativePath", "Public/AuracronMetaHumanBridge.h" },
		{ "Retargeting.DisplayName", "Animation Retargeting" },
		{ "Retargeting.Name", "ERigTransformationType::Retargeting" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ETextureBlendMode is defined in AuracronTextureGeneration.h" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERigTransformationType::BoneScaling", (int64)ERigTransformationType::BoneScaling },
		{ "ERigTransformationType::Constraint", (int64)ERigTransformationType::Constraint },
		{ "ERigTransformationType::IKSetup", (int64)ERigTransformationType::IKSetup },
		{ "ERigTransformationType::FKSetup", (int64)ERigTransformationType::FKSetup },
		{ "ERigTransformationType::Retargeting", (int64)ERigTransformationType::Retargeting },
		{ "ERigTransformationType::Combined", (int64)ERigTransformationType::Combined },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronMetaHumanBridge,
	nullptr,
	"ERigTransformationType",
	"ERigTransformationType",
	Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType()
{
	if (!Z_Registration_Info_UEnum_ERigTransformationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERigTransformationType.InnerSingleton, Z_Construct_UEnum_AuracronMetaHumanBridge_ERigTransformationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERigTransformationType.InnerSingleton;
}
// ********** End Enum ERigTransformationType ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h__Script_AuracronMetaHumanBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EMetaHumanDNADataLayer_StaticEnum, TEXT("EMetaHumanDNADataLayer"), &Z_Registration_Info_UEnum_EMetaHumanDNADataLayer, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1411510605U) },
		{ EMetaHumanGender_StaticEnum, TEXT("EMetaHumanGender"), &Z_Registration_Info_UEnum_EMetaHumanGender, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3311821244U) },
		{ EMetaHumanArchetype_StaticEnum, TEXT("EMetaHumanArchetype"), &Z_Registration_Info_UEnum_EMetaHumanArchetype, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1689276464U) },
		{ EMetaHumanCoordinateSystem_StaticEnum, TEXT("EMetaHumanCoordinateSystem"), &Z_Registration_Info_UEnum_EMetaHumanCoordinateSystem, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1654731741U) },
		{ EMetaHumanRotationUnit_StaticEnum, TEXT("EMetaHumanRotationUnit"), &Z_Registration_Info_UEnum_EMetaHumanRotationUnit, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1334884008U) },
		{ EMetaHumanTranslationUnit_StaticEnum, TEXT("EMetaHumanTranslationUnit"), &Z_Registration_Info_UEnum_EMetaHumanTranslationUnit, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1328495797U) },
		{ EBlendShapeInterpolationType_StaticEnum, TEXT("EBlendShapeInterpolationType"), &Z_Registration_Info_UEnum_EBlendShapeInterpolationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 389234855U) },
		{ EFacialExpressionType_StaticEnum, TEXT("EFacialExpressionType"), &Z_Registration_Info_UEnum_EFacialExpressionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1368388503U) },
		{ EBlendShapeValidationType_StaticEnum, TEXT("EBlendShapeValidationType"), &Z_Registration_Info_UEnum_EBlendShapeValidationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1659915784U) },
		{ EJointConstraintType_StaticEnum, TEXT("EJointConstraintType"), &Z_Registration_Info_UEnum_EJointConstraintType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 157641126U) },
		{ EJointTransformSpace_StaticEnum, TEXT("EJointTransformSpace"), &Z_Registration_Info_UEnum_EJointTransformSpace, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1111932712U) },
		{ EClothingMaterialType_StaticEnum, TEXT("EClothingMaterialType"), &Z_Registration_Info_UEnum_EClothingMaterialType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3955288675U) },
		{ ELipSyncType_StaticEnum, TEXT("ELipSyncType"), &Z_Registration_Info_UEnum_ELipSyncType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3561698443U) },
		{ EEmotionMappingType_StaticEnum, TEXT("EEmotionMappingType"), &Z_Registration_Info_UEnum_EEmotionMappingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2892973230U) },
		{ EPoseGenerationMethod_StaticEnum, TEXT("EPoseGenerationMethod"), &Z_Registration_Info_UEnum_EPoseGenerationMethod, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3693825245U) },
		{ EAnimationBlueprintQuality_StaticEnum, TEXT("EAnimationBlueprintQuality"), &Z_Registration_Info_UEnum_EAnimationBlueprintQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2005304443U) },
		{ EPhonemeType_StaticEnum, TEXT("EPhonemeType"), &Z_Registration_Info_UEnum_EPhonemeType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3882383786U) },
		{ EClothingFittingMethod_StaticEnum, TEXT("EClothingFittingMethod"), &Z_Registration_Info_UEnum_EClothingFittingMethod, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1093233944U) },
		{ EClothingLODMode_StaticEnum, TEXT("EClothingLODMode"), &Z_Registration_Info_UEnum_EClothingLODMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1436812725U) },
		{ EClothingPaintTool_StaticEnum, TEXT("EClothingPaintTool"), &Z_Registration_Info_UEnum_EClothingPaintTool, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 960906960U) },
		{ EClothingLayerType_StaticEnum, TEXT("EClothingLayerType"), &Z_Registration_Info_UEnum_EClothingLayerType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3754729639U) },
		{ EClothingMaskTarget_StaticEnum, TEXT("EClothingMaskTarget"), &Z_Registration_Info_UEnum_EClothingMaskTarget, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3635640624U) },
		{ EMeshDeformationType_StaticEnum, TEXT("EMeshDeformationType"), &Z_Registration_Info_UEnum_EMeshDeformationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 266283332U) },
		{ EHairCardQuality_StaticEnum, TEXT("EHairCardQuality"), &Z_Registration_Info_UEnum_EHairCardQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3012960578U) },
		{ EClothingCollisionType_StaticEnum, TEXT("EClothingCollisionType"), &Z_Registration_Info_UEnum_EClothingCollisionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4168426429U) },
		{ EClothingQualityLevel_StaticEnum, TEXT("EClothingQualityLevel"), &Z_Registration_Info_UEnum_EClothingQualityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 801096823U) },
		{ ERigTransformationType_StaticEnum, TEXT("ERigTransformationType"), &Z_Registration_Info_UEnum_ERigTransformationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2378567409U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h__Script_AuracronMetaHumanBridge_2777380410(TEXT("/Script/AuracronMetaHumanBridge"),
	nullptr, 0,
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronMetaHumanBridge_Public_AuracronMetaHumanBridge_h__Script_AuracronMetaHumanBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
