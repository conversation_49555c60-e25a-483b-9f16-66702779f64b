// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicRealmBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronDynamicRealmBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerEvolutionData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailExperience();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailGenerationConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailSystemMetrics();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FSigilRealmBonus();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronRealmLayer *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRealmLayer;
static UEnum* EAuracronRealmLayer_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmLayer.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRealmLayer.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronRealmLayer"));
	}
	return Z_Registration_Info_UEnum_EAuracronRealmLayer.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronRealmLayer>()
{
	return EAuracronRealmLayer_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abyssal.DisplayName", "Abismo Umbrio (Abyssal)" },
		{ "Abyssal.Name", "EAuracronRealmLayer::Abyssal" },
		{ "All.DisplayName", "All Layers" },
		{ "All.Name", "EAuracronRealmLayer::All" },
		{ "BlueprintType", "true" },
		{ "Celestial.DisplayName", "Firmamento Zephyr (Celestial)" },
		{ "Celestial.Name", "EAuracronRealmLayer::Celestial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Realm layer enumeration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronRealmLayer::None" },
		{ "Terrestrial.DisplayName", "Plan\xc3\xad""cie Radiante (Terrestrial)" },
		{ "Terrestrial.Name", "EAuracronRealmLayer::Terrestrial" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm layer enumeration" },
#endif
		{ "Transition.DisplayName", "Transition Zone" },
		{ "Transition.Name", "EAuracronRealmLayer::Transition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRealmLayer::None", (int64)EAuracronRealmLayer::None },
		{ "EAuracronRealmLayer::Terrestrial", (int64)EAuracronRealmLayer::Terrestrial },
		{ "EAuracronRealmLayer::Celestial", (int64)EAuracronRealmLayer::Celestial },
		{ "EAuracronRealmLayer::Abyssal", (int64)EAuracronRealmLayer::Abyssal },
		{ "EAuracronRealmLayer::Transition", (int64)EAuracronRealmLayer::Transition },
		{ "EAuracronRealmLayer::All", (int64)EAuracronRealmLayer::All },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronRealmLayer",
	"EAuracronRealmLayer",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer()
{
	if (!Z_Registration_Info_UEnum_EAuracronRealmLayer.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRealmLayer.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRealmLayer.InnerSingleton;
}
// ********** End Enum EAuracronRealmLayer *********************************************************

// ********** Begin Enum EAuracronLayerEvolutionStage **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage;
static UEnum* EAuracronLayerEvolutionStage_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronLayerEvolutionStage"));
	}
	return Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronLayerEvolutionStage>()
{
	return EAuracronLayerEvolutionStage_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAuracronLayerEvolutionStage::Active" },
		{ "Awakening.DisplayName", "Awakening" },
		{ "Awakening.Name", "EAuracronLayerEvolutionStage::Awakening" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer evolution stage enumeration\n" },
#endif
		{ "Dormant.DisplayName", "Dormant" },
		{ "Dormant.Name", "EAuracronLayerEvolutionStage::Dormant" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "Resonant.DisplayName", "Resonant" },
		{ "Resonant.Name", "EAuracronLayerEvolutionStage::Resonant" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer evolution stage enumeration" },
#endif
		{ "Transcendent.DisplayName", "Transcendent" },
		{ "Transcendent.Name", "EAuracronLayerEvolutionStage::Transcendent" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLayerEvolutionStage::Dormant", (int64)EAuracronLayerEvolutionStage::Dormant },
		{ "EAuracronLayerEvolutionStage::Awakening", (int64)EAuracronLayerEvolutionStage::Awakening },
		{ "EAuracronLayerEvolutionStage::Active", (int64)EAuracronLayerEvolutionStage::Active },
		{ "EAuracronLayerEvolutionStage::Resonant", (int64)EAuracronLayerEvolutionStage::Resonant },
		{ "EAuracronLayerEvolutionStage::Transcendent", (int64)EAuracronLayerEvolutionStage::Transcendent },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronLayerEvolutionStage",
	"EAuracronLayerEvolutionStage",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage()
{
	if (!Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage.InnerSingleton;
}
// ********** End Enum EAuracronLayerEvolutionStage ************************************************

// ********** Begin Enum EAuracronGlobalEvolutionEvent *********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent;
static UEnum* EAuracronGlobalEvolutionEvent_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronGlobalEvolutionEvent"));
	}
	return Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronGlobalEvolutionEvent>()
{
	return EAuracronGlobalEvolutionEvent_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AllLayersAwakened.DisplayName", "All Layers Awakened" },
		{ "AllLayersAwakened.Name", "EAuracronGlobalEvolutionEvent::AllLayersAwakened" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Global evolution event enumeration\n" },
#endif
		{ "MaximumEvolution.DisplayName", "Maximum Evolution" },
		{ "MaximumEvolution.Name", "EAuracronGlobalEvolutionEvent::MaximumEvolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global evolution event enumeration" },
#endif
		{ "TranscendentReached.DisplayName", "Transcendent Reached" },
		{ "TranscendentReached.Name", "EAuracronGlobalEvolutionEvent::TranscendentReached" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronGlobalEvolutionEvent::AllLayersAwakened", (int64)EAuracronGlobalEvolutionEvent::AllLayersAwakened },
		{ "EAuracronGlobalEvolutionEvent::TranscendentReached", (int64)EAuracronGlobalEvolutionEvent::TranscendentReached },
		{ "EAuracronGlobalEvolutionEvent::MaximumEvolution", (int64)EAuracronGlobalEvolutionEvent::MaximumEvolution },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronGlobalEvolutionEvent",
	"EAuracronGlobalEvolutionEvent",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent()
{
	if (!Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent.InnerSingleton;
}
// ********** End Enum EAuracronGlobalEvolutionEvent ***********************************************

// ********** Begin Enum EAuracronEvolutionTrigger *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronEvolutionTrigger;
static UEnum* EAuracronEvolutionTrigger_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronEvolutionTrigger"));
	}
	return Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronEvolutionTrigger>()
{
	return EAuracronEvolutionTrigger_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CombatActivity.DisplayName", "Combat Activity" },
		{ "CombatActivity.Name", "EAuracronEvolutionTrigger::CombatActivity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evolution trigger enumeration\n" },
#endif
		{ "DayNightCycle.DisplayName", "Day/Night Cycle" },
		{ "DayNightCycle.Name", "EAuracronEvolutionTrigger::DayNightCycle" },
		{ "IslandActivation.DisplayName", "Island Activation" },
		{ "IslandActivation.Name", "EAuracronEvolutionTrigger::IslandActivation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "PlayerActivity.DisplayName", "Player Activity" },
		{ "PlayerActivity.Name", "EAuracronEvolutionTrigger::PlayerActivity" },
		{ "PlayerPresence.DisplayName", "Player Presence" },
		{ "PlayerPresence.Name", "EAuracronEvolutionTrigger::PlayerPresence" },
		{ "RailUsage.DisplayName", "Rail Usage" },
		{ "RailUsage.Name", "EAuracronEvolutionTrigger::RailUsage" },
		{ "SeasonalChange.DisplayName", "Seasonal Change" },
		{ "SeasonalChange.Name", "EAuracronEvolutionTrigger::SeasonalChange" },
		{ "SigilActivation.DisplayName", "Sigil Activation" },
		{ "SigilActivation.Name", "EAuracronEvolutionTrigger::SigilActivation" },
		{ "TimeProgression.DisplayName", "Time Progression" },
		{ "TimeProgression.Name", "EAuracronEvolutionTrigger::TimeProgression" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolution trigger enumeration" },
#endif
		{ "WeatherEvents.DisplayName", "Weather Events" },
		{ "WeatherEvents.Name", "EAuracronEvolutionTrigger::WeatherEvents" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronEvolutionTrigger::PlayerPresence", (int64)EAuracronEvolutionTrigger::PlayerPresence },
		{ "EAuracronEvolutionTrigger::PlayerActivity", (int64)EAuracronEvolutionTrigger::PlayerActivity },
		{ "EAuracronEvolutionTrigger::CombatActivity", (int64)EAuracronEvolutionTrigger::CombatActivity },
		{ "EAuracronEvolutionTrigger::SigilActivation", (int64)EAuracronEvolutionTrigger::SigilActivation },
		{ "EAuracronEvolutionTrigger::RailUsage", (int64)EAuracronEvolutionTrigger::RailUsage },
		{ "EAuracronEvolutionTrigger::IslandActivation", (int64)EAuracronEvolutionTrigger::IslandActivation },
		{ "EAuracronEvolutionTrigger::TimeProgression", (int64)EAuracronEvolutionTrigger::TimeProgression },
		{ "EAuracronEvolutionTrigger::WeatherEvents", (int64)EAuracronEvolutionTrigger::WeatherEvents },
		{ "EAuracronEvolutionTrigger::DayNightCycle", (int64)EAuracronEvolutionTrigger::DayNightCycle },
		{ "EAuracronEvolutionTrigger::SeasonalChange", (int64)EAuracronEvolutionTrigger::SeasonalChange },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronEvolutionTrigger",
	"EAuracronEvolutionTrigger",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger()
{
	if (!Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronEvolutionTrigger.InnerSingleton;
}
// ********** End Enum EAuracronEvolutionTrigger ***************************************************

// ********** Begin ScriptStruct FAuracronLayerEvolutionData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData;
class UScriptStruct* FAuracronLayerEvolutionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerEvolutionData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronLayerEvolutionData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Layer evolution data structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer evolution data structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Layer_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer being tracked */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer being tracked" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionStage_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current evolution stage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current evolution stage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionProgress_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evolution progress (0.0 to 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolution progress (0.0 to 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StageStartTime_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time when current stage started */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time when current stage started" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastEvolutionTime_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last evolution update time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last evolution update time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionSpeed_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evolution speed multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolution speed multiplier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerInfluence_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player influence on evolution */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player influence on evolution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentalFactors_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Environmental factors affecting evolution */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Environmental factors affecting evolution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredPlayerTime_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required player time in layer for evolution */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required player time in layer for evolution" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanEvolve_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether layer can evolve */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether layer can evolve" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoEvolution_MetaData[] = {
		{ "Category", "Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether evolution is automatic */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether evolution is automatic" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EvolutionStage_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EvolutionStage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StageStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastEvolutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerInfluence;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnvironmentalFactors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequiredPlayerTime;
	static void NewProp_bCanEvolve_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanEvolve;
	static void NewProp_bAutoEvolution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoEvolution;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerEvolutionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Layer_MetaData), NewProp_Layer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionStage_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionStage = { "EvolutionStage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, EvolutionStage), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionStage_MetaData), NewProp_EvolutionStage_MetaData) }; // 3335440642
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionProgress = { "EvolutionProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, EvolutionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionProgress_MetaData), NewProp_EvolutionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_StageStartTime = { "StageStartTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, StageStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StageStartTime_MetaData), NewProp_StageStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_LastEvolutionTime = { "LastEvolutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, LastEvolutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastEvolutionTime_MetaData), NewProp_LastEvolutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionSpeed = { "EvolutionSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, EvolutionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionSpeed_MetaData), NewProp_EvolutionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_PlayerInfluence = { "PlayerInfluence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, PlayerInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerInfluence_MetaData), NewProp_PlayerInfluence_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EnvironmentalFactors = { "EnvironmentalFactors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, EnvironmentalFactors), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentalFactors_MetaData), NewProp_EnvironmentalFactors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_RequiredPlayerTime = { "RequiredPlayerTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerEvolutionData, RequiredPlayerTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredPlayerTime_MetaData), NewProp_RequiredPlayerTime_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bCanEvolve_SetBit(void* Obj)
{
	((FAuracronLayerEvolutionData*)Obj)->bCanEvolve = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bCanEvolve = { "bCanEvolve", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerEvolutionData), &Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bCanEvolve_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanEvolve_MetaData), NewProp_bCanEvolve_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bAutoEvolution_SetBit(void* Obj)
{
	((FAuracronLayerEvolutionData*)Obj)->bAutoEvolution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bAutoEvolution = { "bAutoEvolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerEvolutionData), &Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bAutoEvolution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoEvolution_MetaData), NewProp_bAutoEvolution_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionStage_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionStage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_StageStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_LastEvolutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EvolutionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_PlayerInfluence,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_EnvironmentalFactors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_RequiredPlayerTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bCanEvolve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewProp_bAutoEvolution,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronLayerEvolutionData",
	Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::PropPointers),
	sizeof(FAuracronLayerEvolutionData),
	alignof(FAuracronLayerEvolutionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerEvolutionData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerEvolutionData *****************************************

// ********** Begin ScriptStruct FAuracronGlobalEvolutionData **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData;
class UScriptStruct* FAuracronGlobalEvolutionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronGlobalEvolutionData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Global evolution data structure\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global evolution data structure" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalEvolutionTime_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total evolution time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total evolution time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayersAwakened_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of layers awakened */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of layers awakened" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayersTranscendent_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Number of layers transcendent */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Number of layers transcendent" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalEvolutionLevel_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Global evolution level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global evolution level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastGlobalEvent_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last global event triggered */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last global event triggered" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMaxEvolutionReached_MetaData[] = {
		{ "Category", "Global Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether maximum evolution has been reached */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether maximum evolution has been reached" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalEvolutionTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayersAwakened;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayersTranscendent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GlobalEvolutionLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LastGlobalEvent_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LastGlobalEvent;
	static void NewProp_bMaxEvolutionReached_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaxEvolutionReached;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronGlobalEvolutionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_TotalEvolutionTime = { "TotalEvolutionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGlobalEvolutionData, TotalEvolutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalEvolutionTime_MetaData), NewProp_TotalEvolutionTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LayersAwakened = { "LayersAwakened", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGlobalEvolutionData, LayersAwakened), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayersAwakened_MetaData), NewProp_LayersAwakened_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LayersTranscendent = { "LayersTranscendent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGlobalEvolutionData, LayersTranscendent), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayersTranscendent_MetaData), NewProp_LayersTranscendent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_GlobalEvolutionLevel = { "GlobalEvolutionLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGlobalEvolutionData, GlobalEvolutionLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalEvolutionLevel_MetaData), NewProp_GlobalEvolutionLevel_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LastGlobalEvent_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LastGlobalEvent = { "LastGlobalEvent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronGlobalEvolutionData, LastGlobalEvent), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastGlobalEvent_MetaData), NewProp_LastGlobalEvent_MetaData) }; // 2743291875
void Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_bMaxEvolutionReached_SetBit(void* Obj)
{
	((FAuracronGlobalEvolutionData*)Obj)->bMaxEvolutionReached = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_bMaxEvolutionReached = { "bMaxEvolutionReached", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronGlobalEvolutionData), &Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_bMaxEvolutionReached_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMaxEvolutionReached_MetaData), NewProp_bMaxEvolutionReached_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_TotalEvolutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LayersAwakened,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LayersTranscendent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_GlobalEvolutionLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LastGlobalEvent_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_LastGlobalEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewProp_bMaxEvolutionReached,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronGlobalEvolutionData",
	Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::PropPointers),
	sizeof(FAuracronGlobalEvolutionData),
	alignof(FAuracronGlobalEvolutionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronGlobalEvolutionData ****************************************

// ********** Begin Enum ERealmEvolutionPhase ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERealmEvolutionPhase;
static UEnum* ERealmEvolutionPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERealmEvolutionPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERealmEvolutionPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ERealmEvolutionPhase"));
	}
	return Z_Registration_Info_UEnum_ERealmEvolutionPhase.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERealmEvolutionPhase>()
{
	return ERealmEvolutionPhase_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Realm evolution phases\n" },
#endif
		{ "Convergencia.DisplayName", "Converg\xc3\xaancia (15-25min)" },
		{ "Convergencia.Name", "ERealmEvolutionPhase::Convergencia" },
		{ "Despertar.DisplayName", "Despertar (0-15min)" },
		{ "Despertar.Name", "ERealmEvolutionPhase::Despertar" },
		{ "Intensificacao.DisplayName", "Intensifica\xc3\xa7\xc3\xa3o (25-35min)" },
		{ "Intensificacao.Name", "ERealmEvolutionPhase::Intensificacao" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "Resolucao.DisplayName", "Resolu\xc3\xa7\xc3\xa3o (35+min)" },
		{ "Resolucao.Name", "ERealmEvolutionPhase::Resolucao" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm evolution phases" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERealmEvolutionPhase::Despertar", (int64)ERealmEvolutionPhase::Despertar },
		{ "ERealmEvolutionPhase::Convergencia", (int64)ERealmEvolutionPhase::Convergencia },
		{ "ERealmEvolutionPhase::Intensificacao", (int64)ERealmEvolutionPhase::Intensificacao },
		{ "ERealmEvolutionPhase::Resolucao", (int64)ERealmEvolutionPhase::Resolucao },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ERealmEvolutionPhase",
	"ERealmEvolutionPhase",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase()
{
	if (!Z_Registration_Info_UEnum_ERealmEvolutionPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERealmEvolutionPhase.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERealmEvolutionPhase.InnerSingleton;
}
// ********** End Enum ERealmEvolutionPhase ********************************************************

// ********** Begin Enum ERealmTransitionType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERealmTransitionType;
static UEnum* ERealmTransitionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERealmTransitionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERealmTransitionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ERealmTransitionType"));
	}
	return Z_Registration_Info_UEnum_ERealmTransitionType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERealmTransitionType>()
{
	return ERealmTransitionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Transition" },
		{ "Cinematic.Name", "ERealmTransitionType::Cinematic" },
		{ "Combat.DisplayName", "Combat Transition" },
		{ "Combat.Name", "ERealmTransitionType::Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Realm transition types\n" },
#endif
		{ "Gradual.DisplayName", "Gradual Transition" },
		{ "Gradual.Name", "ERealmTransitionType::Gradual" },
		{ "Instant.DisplayName", "Instant Teleport" },
		{ "Instant.Name", "ERealmTransitionType::Instant" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "Stealth.DisplayName", "Stealth Transition" },
		{ "Stealth.Name", "ERealmTransitionType::Stealth" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realm transition types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERealmTransitionType::Instant", (int64)ERealmTransitionType::Instant },
		{ "ERealmTransitionType::Gradual", (int64)ERealmTransitionType::Gradual },
		{ "ERealmTransitionType::Cinematic", (int64)ERealmTransitionType::Cinematic },
		{ "ERealmTransitionType::Combat", (int64)ERealmTransitionType::Combat },
		{ "ERealmTransitionType::Stealth", (int64)ERealmTransitionType::Stealth },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ERealmTransitionType",
	"ERealmTransitionType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType()
{
	if (!Z_Registration_Info_UEnum_ERealmTransitionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERealmTransitionType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERealmTransitionType.InnerSingleton;
}
// ********** End Enum ERealmTransitionType ********************************************************

// ********** Begin Enum EPrismalIslandType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPrismalIslandType;
static UEnum* EPrismalIslandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPrismalIslandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPrismalIslandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPrismalIslandType"));
	}
	return Z_Registration_Info_UEnum_EPrismalIslandType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPrismalIslandType>()
{
	return EPrismalIslandType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Arsenal.DisplayName", "Arsenal (Upgrades)" },
		{ "Arsenal.Name", "EPrismalIslandType::Arsenal" },
		{ "BlueprintType", "true" },
		{ "Caos.DisplayName", "Caos (High Risk)" },
		{ "Caos.Name", "EPrismalIslandType::Caos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island types in Fluxo Prismal Serpentino\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "Nexus.DisplayName", "Nexus (Control)" },
		{ "Nexus.Name", "EPrismalIslandType::Nexus" },
		{ "Santuario.DisplayName", "Santu\xc3\xa1rio (Safe Zone)" },
		{ "Santuario.Name", "EPrismalIslandType::Santuario" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island types in Fluxo Prismal Serpentino" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPrismalIslandType::Nexus", (int64)EPrismalIslandType::Nexus },
		{ "EPrismalIslandType::Santuario", (int64)EPrismalIslandType::Santuario },
		{ "EPrismalIslandType::Arsenal", (int64)EPrismalIslandType::Arsenal },
		{ "EPrismalIslandType::Caos", (int64)EPrismalIslandType::Caos },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPrismalIslandType",
	"EPrismalIslandType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType()
{
	if (!Z_Registration_Info_UEnum_EPrismalIslandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPrismalIslandType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPrismalIslandType.InnerSingleton;
}
// ********** End Enum EPrismalIslandType **********************************************************

// ********** Begin Enum EAuracronRailType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronRailType;
static UEnum* EAuracronRailType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronRailType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronRailType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronRailType"));
	}
	return Z_Registration_Info_UEnum_EAuracronRailType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronRailType>()
{
	return EAuracronRailType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Axis.DisplayName", "Axis Trilhos (Vertical Instant)" },
		{ "Axis.Name", "EAuracronRailType::Axis" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rail types for dynamic movement\n" },
#endif
		{ "Lunar.DisplayName", "Lunar Trilhos (Ethereal Stealth)" },
		{ "Lunar.Name", "EAuracronRailType::Lunar" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
		{ "Solar.DisplayName", "Solar Trilhos (Golden Speed)" },
		{ "Solar.Name", "EAuracronRailType::Solar" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rail types for dynamic movement" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronRailType::Solar", (int64)EAuracronRailType::Solar },
		{ "EAuracronRailType::Axis", (int64)EAuracronRailType::Axis },
		{ "EAuracronRailType::Lunar", (int64)EAuracronRailType::Lunar },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronRailType",
	"EAuracronRailType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType()
{
	if (!Z_Registration_Info_UEnum_EAuracronRailType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronRailType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronRailType.InnerSingleton;
}
// ********** End Enum EAuracronRailType ***********************************************************

// ********** Begin ScriptStruct FPlayerRailData ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerRailData;
class UScriptStruct* FPlayerRailData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRailData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerRailData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerRailData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("PlayerRailData"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRailData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerRailData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para dados de movimento do jogador no trilho\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de movimento do jogador no trilho" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Player_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMovingForward_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSpeed_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Rail Movement" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static void NewProp_bMovingForward_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMovingForward;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerRailData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailData, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Player_MetaData), NewProp_Player_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailData, Progress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) };
void Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bMovingForward_SetBit(void* Obj)
{
	((FPlayerRailData*)Obj)->bMovingForward = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bMovingForward = { "bMovingForward", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlayerRailData), &Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bMovingForward_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMovingForward_MetaData), NewProp_bMovingForward_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_CurrentSpeed = { "CurrentSpeed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailData, CurrentSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSpeed_MetaData), NewProp_CurrentSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailData, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
void Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FPlayerRailData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlayerRailData), &Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerRailData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bMovingForward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_CurrentSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerRailData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"PlayerRailData",
	Z_Construct_UScriptStruct_FPlayerRailData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailData_Statics::PropPointers),
	sizeof(FPlayerRailData),
	alignof(FPlayerRailData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerRailData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailData()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRailData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerRailData.InnerSingleton, Z_Construct_UScriptStruct_FPlayerRailData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRailData.InnerSingleton;
}
// ********** End ScriptStruct FPlayerRailData *****************************************************

// ********** Begin ScriptStruct FSigilRealmBonus **************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSigilRealmBonus;
class UScriptStruct* FSigilRealmBonus::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilRealmBonus.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSigilRealmBonus.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSigilRealmBonus, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("SigilRealmBonus"));
	}
	return Z_Registration_Info_UScriptStruct_FSigilRealmBonus.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSigilRealmBonus_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para bonus de s\xc3\xadgilo por realm\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para bonus de s\xc3\xadgilo por realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmLayer_MetaData[] = {
		{ "Category", "Sigil Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AegisBonusMultiplier_MetaData[] = {
		{ "Category", "Sigil Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuinBonusMultiplier_MetaData[] = {
		{ "Category", "Sigil Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VesperBonusMultiplier_MetaData[] = {
		{ "Category", "Sigil Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusionBonusMultiplier_MetaData[] = {
		{ "Category", "Sigil Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AegisBonusMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RuinBonusMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VesperBonusMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FusionBonusMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSigilRealmBonus>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RealmLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RealmLayer = { "RealmLayer", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilRealmBonus, RealmLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmLayer_MetaData), NewProp_RealmLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_AegisBonusMultiplier = { "AegisBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilRealmBonus, AegisBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AegisBonusMultiplier_MetaData), NewProp_AegisBonusMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RuinBonusMultiplier = { "RuinBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilRealmBonus, RuinBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuinBonusMultiplier_MetaData), NewProp_RuinBonusMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_VesperBonusMultiplier = { "VesperBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilRealmBonus, VesperBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VesperBonusMultiplier_MetaData), NewProp_VesperBonusMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_FusionBonusMultiplier = { "FusionBonusMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSigilRealmBonus, FusionBonusMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusionBonusMultiplier_MetaData), NewProp_FusionBonusMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RealmLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RealmLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_AegisBonusMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_RuinBonusMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_VesperBonusMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewProp_FusionBonusMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"SigilRealmBonus",
	Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::PropPointers),
	sizeof(FSigilRealmBonus),
	alignof(FSigilRealmBonus),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSigilRealmBonus()
{
	if (!Z_Registration_Info_UScriptStruct_FSigilRealmBonus.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSigilRealmBonus.InnerSingleton, Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSigilRealmBonus.InnerSingleton;
}
// ********** End ScriptStruct FSigilRealmBonus ****************************************************

// ********** Begin ScriptStruct FRailGenerationConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRailGenerationConfig;
class UScriptStruct* FRailGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRailGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRailGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRailGenerationConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("RailGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FRailGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRailGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de trilhos\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xa3o de gera\xc3\xa7\xc3\xa3o de trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRailsPerLayer_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinRailLength_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRailLength_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailSpacing_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerateRails_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptiveGeneration_MetaData[] = {
		{ "Category", "Rail Generation" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRailsPerLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinRailLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRailLength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RailSpacing;
	static void NewProp_bAutoGenerateRails_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerateRails;
	static void NewProp_bAdaptiveGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptiveGeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRailGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MaxRailsPerLayer = { "MaxRailsPerLayer", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailGenerationConfig, MaxRailsPerLayer), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRailsPerLayer_MetaData), NewProp_MaxRailsPerLayer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MinRailLength = { "MinRailLength", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailGenerationConfig, MinRailLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinRailLength_MetaData), NewProp_MinRailLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MaxRailLength = { "MaxRailLength", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailGenerationConfig, MaxRailLength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRailLength_MetaData), NewProp_MaxRailLength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_RailSpacing = { "RailSpacing", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailGenerationConfig, RailSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailSpacing_MetaData), NewProp_RailSpacing_MetaData) };
void Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAutoGenerateRails_SetBit(void* Obj)
{
	((FRailGenerationConfig*)Obj)->bAutoGenerateRails = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAutoGenerateRails = { "bAutoGenerateRails", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailGenerationConfig), &Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAutoGenerateRails_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerateRails_MetaData), NewProp_bAutoGenerateRails_MetaData) };
void Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAdaptiveGeneration_SetBit(void* Obj)
{
	((FRailGenerationConfig*)Obj)->bAdaptiveGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAdaptiveGeneration = { "bAdaptiveGeneration", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FRailGenerationConfig), &Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAdaptiveGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptiveGeneration_MetaData), NewProp_bAdaptiveGeneration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MaxRailsPerLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MinRailLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_MaxRailLength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_RailSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAutoGenerateRails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewProp_bAdaptiveGeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"RailGenerationConfig",
	Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::PropPointers),
	sizeof(FRailGenerationConfig),
	alignof(FRailGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRailGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FRailGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRailGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRailGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FRailGenerationConfig ***********************************************

// ********** Begin ScriptStruct FRailSystemMetrics ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FRailSystemMetrics;
class UScriptStruct* FRailSystemMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FRailSystemMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FRailSystemMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRailSystemMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("RailSystemMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FRailSystemMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FRailSystemMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para m\xc3\xa9tricas do sistema de trilhos\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para m\xc3\xa9tricas do sistema de trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rail_MetaData[] = {
		{ "Category", "Rail Metrics" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalUsageTime_MetaData[] = {
		{ "Category", "Rail Metrics" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerCount_MetaData[] = {
		{ "Category", "Rail Metrics" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EfficiencyRating_MetaData[] = {
		{ "Category", "Rail Metrics" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUsageTime_MetaData[] = {
		{ "Category", "Rail Metrics" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Rail;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalUsageTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EfficiencyRating;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUsageTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRailSystemMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_Rail = { "Rail", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailSystemMetrics, Rail), Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rail_MetaData), NewProp_Rail_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_TotalUsageTime = { "TotalUsageTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailSystemMetrics, TotalUsageTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalUsageTime_MetaData), NewProp_TotalUsageTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_PlayerCount = { "PlayerCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailSystemMetrics, PlayerCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerCount_MetaData), NewProp_PlayerCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_EfficiencyRating = { "EfficiencyRating", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailSystemMetrics, EfficiencyRating), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EfficiencyRating_MetaData), NewProp_EfficiencyRating_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_LastUsageTime = { "LastUsageTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRailSystemMetrics, LastUsageTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUsageTime_MetaData), NewProp_LastUsageTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_Rail,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_TotalUsageTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_PlayerCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_EfficiencyRating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewProp_LastUsageTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"RailSystemMetrics",
	Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::PropPointers),
	sizeof(FRailSystemMetrics),
	alignof(FRailSystemMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRailSystemMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FRailSystemMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FRailSystemMetrics.InnerSingleton, Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FRailSystemMetrics.InnerSingleton;
}
// ********** End ScriptStruct FRailSystemMetrics **************************************************

// ********** Begin ScriptStruct FPlayerRailExperience *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPlayerRailExperience;
class UScriptStruct* FPlayerRailExperience::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRailExperience.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPlayerRailExperience.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerRailExperience, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("PlayerRailExperience"));
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRailExperience.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPlayerRailExperience_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para experi\xc3\xaancia do jogador com trilhos\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para experi\xc3\xaancia do jogador com trilhos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Player_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalRailTime_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailsUsed_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperienceLevel_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExperiencePoints_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedMultiplier_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyEfficiency_MetaData[] = {
		{ "Category", "Rail Experience" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalRailTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RailsUsed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExperienceLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExperiencePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyEfficiency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerRailExperience>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Player_MetaData), NewProp_Player_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_TotalRailTime = { "TotalRailTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, TotalRailTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalRailTime_MetaData), NewProp_TotalRailTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_RailsUsed = { "RailsUsed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, RailsUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailsUsed_MetaData), NewProp_RailsUsed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_ExperienceLevel = { "ExperienceLevel", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, ExperienceLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperienceLevel_MetaData), NewProp_ExperienceLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_ExperiencePoints = { "ExperiencePoints", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, ExperiencePoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExperiencePoints_MetaData), NewProp_ExperiencePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_SpeedMultiplier = { "SpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, SpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedMultiplier_MetaData), NewProp_SpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_EnergyEfficiency = { "EnergyEfficiency", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerRailExperience, EnergyEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyEfficiency_MetaData), NewProp_EnergyEfficiency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_TotalRailTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_RailsUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_ExperienceLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_ExperiencePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_SpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewProp_EnergyEfficiency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"PlayerRailExperience",
	Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::PropPointers),
	sizeof(FPlayerRailExperience),
	alignof(FPlayerRailExperience),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailExperience()
{
	if (!Z_Registration_Info_UScriptStruct_FPlayerRailExperience.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPlayerRailExperience.InnerSingleton, Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPlayerRailExperience.InnerSingleton;
}
// ********** End ScriptStruct FPlayerRailExperience ***********************************************

// ********** Begin Delegate FOnSystemFullyInitialized *********************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Delegates for System Events ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Delegates for System Events ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnSystemFullyInitialized__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSystemFullyInitialized_DelegateWrapper(const FMulticastScriptDelegate& OnSystemFullyInitialized)
{
	OnSystemFullyInitialized.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnSystemFullyInitialized ***********************************************

// ********** Begin Delegate FOnRealmLayerChanged **************************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms
	{
		EAuracronRealmLayer OldLayer;
		EAuracronRealmLayer NewLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Delegate declarations\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate declarations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_OldLayer = { "OldLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms, OldLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms, NewLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_OldLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_OldLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnRealmLayerChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRealmLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRealmLayerChanged, EAuracronRealmLayer OldLayer, EAuracronRealmLayer NewLayer)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms
	{
		EAuracronRealmLayer OldLayer;
		EAuracronRealmLayer NewLayer;
	};
	_Script_AuracronDynamicRealmBridge_eventOnRealmLayerChanged_Parms Parms;
	Parms.OldLayer=OldLayer;
	Parms.NewLayer=NewLayer;
	OnRealmLayerChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmLayerChanged ****************************************************

// ********** Begin Delegate FOnRealmEvolutionPhaseChanged *****************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms
	{
		ERealmEvolutionPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms, NewPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(0, nullptr) }; // 560471848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnRealmEvolutionPhaseChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRealmEvolutionPhaseChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRealmEvolutionPhaseChanged, ERealmEvolutionPhase NewPhase)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms
	{
		ERealmEvolutionPhase NewPhase;
	};
	_Script_AuracronDynamicRealmBridge_eventOnRealmEvolutionPhaseChanged_Parms Parms;
	Parms.NewPhase=NewPhase;
	OnRealmEvolutionPhaseChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmEvolutionPhaseChanged *******************************************

// ********** Begin Delegate FOnRealmTransitionStarted *********************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::NewProp_TargetLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnRealmTransitionStarted__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRealmTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnRealmTransitionStarted, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
	};
	_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionStarted_Parms Parms;
	Parms.Actor=Actor;
	Parms.SourceLayer=SourceLayer;
	Parms.TargetLayer=TargetLayer;
	OnRealmTransitionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmTransitionStarted ***********************************************

// ********** Begin Delegate FOnRealmTransitionCompleted *******************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::NewProp_TargetLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnRealmTransitionCompleted__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRealmTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRealmTransitionCompleted, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
	};
	_Script_AuracronDynamicRealmBridge_eventOnRealmTransitionCompleted_Parms Parms;
	Parms.Actor=Actor;
	Parms.SourceLayer=SourceLayer;
	Parms.TargetLayer=TargetLayer;
	OnRealmTransitionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnRealmTransitionCompleted *********************************************

// ********** Begin Delegate FOnPrismalIslandActivated *********************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms
	{
		EPrismalIslandType IslandType;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnPrismalIslandActivated__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPrismalIslandActivated_DelegateWrapper(const FMulticastScriptDelegate& OnPrismalIslandActivated, EPrismalIslandType IslandType, FVector Location)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms
	{
		EPrismalIslandType IslandType;
		FVector Location;
	};
	_Script_AuracronDynamicRealmBridge_eventOnPrismalIslandActivated_Parms Parms;
	Parms.IslandType=IslandType;
	Parms.Location=Location;
	OnPrismalIslandActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnPrismalIslandActivated ***********************************************

// ********** Begin Delegate FOnLayerEvolutionStageChanged *****************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms
	{
		EAuracronRealmLayer Layer;
		EAuracronLayerEvolutionStage OldStage;
		EAuracronLayerEvolutionStage NewStage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced evolution system delegates\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced evolution system delegates" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldStage_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldStage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewStage_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewStage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_OldStage_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_OldStage = { "OldStage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms, OldStage), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, METADATA_PARAMS(0, nullptr) }; // 3335440642
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_NewStage_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_NewStage = { "NewStage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms, NewStage), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, METADATA_PARAMS(0, nullptr) }; // 3335440642
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_OldStage_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_OldStage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_NewStage_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::NewProp_NewStage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnLayerEvolutionStageChanged__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLayerEvolutionStageChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLayerEvolutionStageChanged, EAuracronRealmLayer Layer, EAuracronLayerEvolutionStage OldStage, EAuracronLayerEvolutionStage NewStage)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms
	{
		EAuracronRealmLayer Layer;
		EAuracronLayerEvolutionStage OldStage;
		EAuracronLayerEvolutionStage NewStage;
	};
	_Script_AuracronDynamicRealmBridge_eventOnLayerEvolutionStageChanged_Parms Parms;
	Parms.Layer=Layer;
	Parms.OldStage=OldStage;
	Parms.NewStage=NewStage;
	OnLayerEvolutionStageChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLayerEvolutionStageChanged *******************************************

// ********** Begin Delegate FOnAdvancedRealmTransitionComplete ************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnAdvancedRealmTransitionComplete__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAdvancedRealmTransitionComplete_DelegateWrapper(const FMulticastScriptDelegate& OnAdvancedRealmTransitionComplete, AActor* Actor, EAuracronRealmLayer SourceLayer, EAuracronRealmLayer TargetLayer, float Duration)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer SourceLayer;
		EAuracronRealmLayer TargetLayer;
		float Duration;
	};
	_Script_AuracronDynamicRealmBridge_eventOnAdvancedRealmTransitionComplete_Parms Parms;
	Parms.Actor=Actor;
	Parms.SourceLayer=SourceLayer;
	Parms.TargetLayer=TargetLayer;
	Parms.Duration=Duration;
	OnAdvancedRealmTransitionComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAdvancedRealmTransitionComplete **************************************

// ********** Begin Delegate FOnGlobalEvolutionEvent ***********************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics
{
	struct _Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms
	{
		EAuracronGlobalEvolutionEvent EventType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EventType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::NewProp_EventType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms, EventType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronGlobalEvolutionEvent, METADATA_PARAMS(0, nullptr) }; // 2743291875
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::NewProp_EventType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::NewProp_EventType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnGlobalEvolutionEvent__DelegateSignature", Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::_Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnGlobalEvolutionEvent_DelegateWrapper(const FMulticastScriptDelegate& OnGlobalEvolutionEvent, EAuracronGlobalEvolutionEvent EventType)
{
	struct _Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms
	{
		EAuracronGlobalEvolutionEvent EventType;
	};
	_Script_AuracronDynamicRealmBridge_eventOnGlobalEvolutionEvent_Parms Parms;
	Parms.EventType=EventType;
	OnGlobalEvolutionEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnGlobalEvolutionEvent *************************************************

// ********** Begin Delegate FOnTranscendentContentUnlocked ****************************************
struct Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge, nullptr, "OnTranscendentContentUnlocked__DelegateSignature", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTranscendentContentUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnTranscendentContentUnlocked)
{
	OnTranscendentContentUnlocked.ProcessMulticastDelegate<UObject>(NULL);
}
// ********** End Delegate FOnTranscendentContentUnlocked ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronRealmLayer_StaticEnum, TEXT("EAuracronRealmLayer"), &Z_Registration_Info_UEnum_EAuracronRealmLayer, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3153537035U) },
		{ EAuracronLayerEvolutionStage_StaticEnum, TEXT("EAuracronLayerEvolutionStage"), &Z_Registration_Info_UEnum_EAuracronLayerEvolutionStage, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3335440642U) },
		{ EAuracronGlobalEvolutionEvent_StaticEnum, TEXT("EAuracronGlobalEvolutionEvent"), &Z_Registration_Info_UEnum_EAuracronGlobalEvolutionEvent, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2743291875U) },
		{ EAuracronEvolutionTrigger_StaticEnum, TEXT("EAuracronEvolutionTrigger"), &Z_Registration_Info_UEnum_EAuracronEvolutionTrigger, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3530945112U) },
		{ ERealmEvolutionPhase_StaticEnum, TEXT("ERealmEvolutionPhase"), &Z_Registration_Info_UEnum_ERealmEvolutionPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 560471848U) },
		{ ERealmTransitionType_StaticEnum, TEXT("ERealmTransitionType"), &Z_Registration_Info_UEnum_ERealmTransitionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2122775647U) },
		{ EPrismalIslandType_StaticEnum, TEXT("EPrismalIslandType"), &Z_Registration_Info_UEnum_EPrismalIslandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1145382866U) },
		{ EAuracronRailType_StaticEnum, TEXT("EAuracronRailType"), &Z_Registration_Info_UEnum_EAuracronRailType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 913509891U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLayerEvolutionData::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerEvolutionData_Statics::NewStructOps, TEXT("AuracronLayerEvolutionData"), &Z_Registration_Info_UScriptStruct_FAuracronLayerEvolutionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerEvolutionData), 2638580268U) },
		{ FAuracronGlobalEvolutionData::StaticStruct, Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData_Statics::NewStructOps, TEXT("AuracronGlobalEvolutionData"), &Z_Registration_Info_UScriptStruct_FAuracronGlobalEvolutionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronGlobalEvolutionData), 2455143879U) },
		{ FPlayerRailData::StaticStruct, Z_Construct_UScriptStruct_FPlayerRailData_Statics::NewStructOps, TEXT("PlayerRailData"), &Z_Registration_Info_UScriptStruct_FPlayerRailData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerRailData), 2949666401U) },
		{ FSigilRealmBonus::StaticStruct, Z_Construct_UScriptStruct_FSigilRealmBonus_Statics::NewStructOps, TEXT("SigilRealmBonus"), &Z_Registration_Info_UScriptStruct_FSigilRealmBonus, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSigilRealmBonus), 1360275077U) },
		{ FRailGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FRailGenerationConfig_Statics::NewStructOps, TEXT("RailGenerationConfig"), &Z_Registration_Info_UScriptStruct_FRailGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRailGenerationConfig), 3271473709U) },
		{ FRailSystemMetrics::StaticStruct, Z_Construct_UScriptStruct_FRailSystemMetrics_Statics::NewStructOps, TEXT("RailSystemMetrics"), &Z_Registration_Info_UScriptStruct_FRailSystemMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRailSystemMetrics), 1396374881U) },
		{ FPlayerRailExperience::StaticStruct, Z_Construct_UScriptStruct_FPlayerRailExperience_Statics::NewStructOps, TEXT("PlayerRailExperience"), &Z_Registration_Info_UScriptStruct_FPlayerRailExperience, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerRailExperience), 2334213312U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_2857617812(TEXT("/Script/AuracronDynamicRealmBridge"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmBridge_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
