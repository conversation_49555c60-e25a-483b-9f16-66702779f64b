// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Element Base Header
// Bridge 2.2: PCG Framework - Base Classes

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "PCGContext.h"
#include "PCGNode.h"
#include "PCGPin.h"
#include "AuracronPCGBase.generated.h"

// Forward declarations
class UPCGComponent;
class UPCGGraph;
struct FPCGContext;

/**
 * Base class for all Auracron PCG Settings
 * Provides common functionality and interface for all PCG nodes
 */
UCLASS(BlueprintType, Blueprintable, Abstract, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API UAuracronPCGSettingsBase : public UPCGSettings
{
    GENERATED_BODY()

public:
    UAuracronPCGSettingsBase(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    // Auracron specific settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auracron Settings")
    FString NodeDescription = TEXT("Auracron PCG Node");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 BatchSize = 1000;

protected:
    // Override in derived classes
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Base class for all Auracron PCG Elements
 * Provides common execution framework
 */
class AURACRONPCGBRIDGE_API FAuracronPCGElementBase : public IPCGElement
{
public:
    FAuracronPCGElementBase() = default;
    virtual ~FAuracronPCGElementBase() = default;

    // IPCGElement interface
    virtual FPCGContext* Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node) override;

protected:
    // Override in derived classes
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    virtual void ProcessPoints(FPCGContext* Context, const FPCGDataCollection& InputData, FPCGDataCollection& OutputData) const;
};

// ========================================
// AURACRON CORE PCG SYSTEMS
// ========================================

/**
 * Auracron Biome PCG Settings
 * Generates biome-specific content based on environmental parameters
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Biome")
class AURACRONPCGBRIDGE_API UAuracronBiomePCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronBiomePCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Biome type identifier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    FString BiomeType = TEXT("Forest");

    /** Temperature range for this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "-50.0", ClampMax = "50.0"))
    FVector2D TemperatureRange = FVector2D(15.0f, 25.0f);

    /** Humidity range for this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    FVector2D HumidityRange = FVector2D(0.4f, 0.8f);

    /** Elevation range for this biome */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "8000.0"))
    FVector2D ElevationRange = FVector2D(0.0f, 1000.0f);

    /** Vegetation density multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation", meta = (ClampMin = "0.0", ClampMax = "5.0"))
    float VegetationDensity = 1.0f;

    /** Resource spawn rate multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resources", meta = (ClampMin = "0.0", ClampMax = "3.0"))
    float ResourceSpawnRate = 1.0f;

    /** Integration with Foliage Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseFoliageBridge = true;

    /** Integration with Dynamic Realm Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseDynamicRealm = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Terrain PCG Settings
 * Generates terrain features, heightmaps, and landscape modifications
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Terrain")
class AURACRONPCGBRIDGE_API UAuracronTerrainPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronTerrainPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Terrain generation seed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation")
    int32 TerrainSeed = 12345;

    /** Noise scale for terrain generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation", meta = (ClampMin = "0.001", ClampMax = "1.0"))
    float NoiseScale = 0.01f;

    /** Height multiplier for terrain */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation", meta = (ClampMin = "1.0", ClampMax = "10000.0"))
    float HeightMultiplier = 1000.0f;

    /** Number of octaves for noise generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation", meta = (ClampMin = "1", ClampMax = "8"))
    int32 NoiseOctaves = 4;

    /** Persistence for noise generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation", meta = (ClampMin = "0.1", ClampMax = "1.0"))
    float NoisePersistence = 0.5f;

    /** Integration with World Partition Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseWorldPartition = true;

    /** Integration with Lumen Bridge for lighting */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseLumenIntegration = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Structure PCG Settings
 * Generates buildings, ruins, and architectural elements
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Structure")
class AURACRONPCGBRIDGE_API UAuracronStructurePCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronStructurePCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Structure type to generate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Structure")
    FString StructureType = TEXT("Village");

    /** Minimum distance between structures */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement", meta = (ClampMin = "100.0", ClampMax = "10000.0"))
    float MinStructureDistance = 500.0f;

    /** Maximum structures per area */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement", meta = (ClampMin = "1", ClampMax = "50"))
    int32 MaxStructuresPerArea = 5;

    /** Structure scale variation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (ClampMin = "0.1", ClampMax = "3.0"))
    FVector2D ScaleRange = FVector2D(0.8f, 1.2f);

    /** Rotation variation in degrees */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (ClampMin = "0.0", ClampMax = "360.0"))
    float RotationVariation = 45.0f;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Vegetation PCG Settings
 * Generates trees, plants, and foliage with integration to Foliage Bridge
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Vegetation")
class AURACRONPCGBRIDGE_API UAuracronVegetationPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronVegetationPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Vegetation type to spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vegetation")
    FString VegetationType = TEXT("Forest");

    /** Base density of vegetation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Density", meta = (ClampMin = "0.0", ClampMax = "10.0"))
    float BaseDensity = 1.0f;

    /** Slope threshold for vegetation placement */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement", meta = (ClampMin = "0.0", ClampMax = "90.0"))
    float MaxSlope = 45.0f;

    /** Minimum altitude for vegetation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement", meta = (ClampMin = "-1000.0", ClampMax = "8000.0"))
    float MinAltitude = 0.0f;

    /** Maximum altitude for vegetation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement", meta = (ClampMin = "-1000.0", ClampMax = "8000.0"))
    float MaxAltitude = 2000.0f;

    /** Size variation range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    FVector2D SizeRange = FVector2D(0.8f, 1.2f);

    /** Seasonal variation support */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Seasonal")
    bool bUseSeasonalVariation = true;

    /** Wind interaction support */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    bool bUseWindInteraction = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Resource PCG Settings
 * Generates collectible resources, minerals, and materials
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Resources")
class AURACRONPCGBRIDGE_API UAuracronResourcePCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronResourcePCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Resource type to generate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
    FString ResourceType = TEXT("Iron");

    /** Resource rarity (affects spawn rate) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rarity", meta = (ClampMin = "0.01", ClampMax = "1.0"))
    float ResourceRarity = 0.1f;

    /** Cluster size for resource nodes */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering", meta = (ClampMin = "1", ClampMax = "20"))
    int32 ClusterSize = 3;

    /** Cluster radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Clustering", meta = (ClampMin = "50.0", ClampMax = "1000.0"))
    float ClusterRadius = 200.0f;

    /** Respawn time in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay", meta = (ClampMin = "60.0", ClampMax = "3600.0"))
    float RespawnTime = 300.0f;

    /** Quality variation range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    FVector2D QualityRange = FVector2D(0.8f, 1.2f);

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Enemy Spawn PCG Settings
 * Generates enemy spawn points and patrol routes
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Combat")
class AURACRONPCGBRIDGE_API UAuracronEnemySpawnPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronEnemySpawnPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Enemy type to spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy")
    FString EnemyType = TEXT("Goblin");

    /** Spawn density per area */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning", meta = (ClampMin = "0.01", ClampMax = "5.0"))
    float SpawnDensity = 0.5f;

    /** Minimum distance from player structures */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Safety", meta = (ClampMin = "100.0", ClampMax = "5000.0"))
    float MinDistanceFromStructures = 1000.0f;

    /** Patrol radius for spawned enemies */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI", meta = (ClampMin = "100.0", ClampMax = "2000.0"))
    float PatrolRadius = 500.0f;

    /** Level scaling based on distance from spawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scaling")
    bool bUseLevelScaling = true;

    /** Integration with Combat Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseCombatBridge = true;

    /** Integration with Adaptive Creatures Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseAdaptiveCreatures = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Dungeon PCG Settings
 * Generates procedural dungeons, caves, and underground structures
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Dungeon")
class AURACRONPCGBRIDGE_API UAuracronDungeonPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronDungeonPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Dungeon type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dungeon")
    FString DungeonType = TEXT("Cave");

    /** Number of rooms to generate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout", meta = (ClampMin = "3", ClampMax = "50"))
    int32 RoomCount = 10;

    /** Room size range */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout", meta = (ClampMin = "200.0", ClampMax = "2000.0"))
    FVector2D RoomSizeRange = FVector2D(400.0f, 800.0f);

    /** Corridor width */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layout", meta = (ClampMin = "100.0", ClampMax = "500.0"))
    float CorridorWidth = 200.0f;

    /** Dungeon depth levels */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Depth", meta = (ClampMin = "1", ClampMax = "10"))
    int32 DepthLevels = 3;

    /** Treasure room probability */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float TreasureRoomProbability = 0.2f;

    /** Boss room generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Boss")
    bool bGenerateBossRoom = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

/**
 * Auracron Weather PCG Settings
 * Generates weather-based environmental modifications
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG|Weather")
class AURACRONPCGBRIDGE_API UAuracronWeatherPCGSettings : public UAuracronPCGSettingsBase
{
    GENERATED_BODY()

public:
    UAuracronWeatherPCGSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Weather type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weather")
    FString WeatherType = TEXT("Rain");

    /** Weather intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Intensity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float WeatherIntensity = 0.5f;

    /** Affects vegetation growth */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bAffectsVegetation = true;

    /** Affects resource spawning */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bAffectsResources = true;

    /** Affects enemy behavior */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    bool bAffectsEnemies = true;

    /** Weather duration in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration", meta = (ClampMin = "60.0", ClampMax = "3600.0"))
    float WeatherDuration = 600.0f;

    /** Integration with VFX Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseVFXBridge = true;

    /** Integration with Audio Bridge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bUseAudioBridge = true;

protected:
    virtual FPCGElementPtr CreateElement() const override;
};

// ========================================
// AURACRON PCG ELEMENTS
// ========================================

/**
 * Auracron Biome PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronBiomePCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronBiomePCGElement() = default;
    virtual ~FAuracronBiomePCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateBiomePoints(FPCGContext* Context, const UAuracronBiomePCGSettings* Settings, FPCGDataCollection& OutputData) const;
    bool ValidateBiomeConditions(const FVector& Location, const UAuracronBiomePCGSettings* Settings) const;
};

/**
 * Auracron Terrain PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronTerrainPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronTerrainPCGElement() = default;
    virtual ~FAuracronTerrainPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateTerrainHeights(FPCGContext* Context, const UAuracronTerrainPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    float CalculateNoiseValue(const FVector2D& Position, const UAuracronTerrainPCGSettings* Settings) const;
};

/**
 * Auracron Structure PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronStructurePCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronStructurePCGElement() = default;
    virtual ~FAuracronStructurePCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void PlaceStructures(FPCGContext* Context, const UAuracronStructurePCGSettings* Settings, FPCGDataCollection& OutputData) const;
    bool ValidateStructurePlacement(const FVector& Location, const UAuracronStructurePCGSettings* Settings) const;
};

/**
 * Auracron Vegetation PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronVegetationPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronVegetationPCGElement() = default;
    virtual ~FAuracronVegetationPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateVegetationPoints(FPCGContext* Context, const UAuracronVegetationPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    bool ValidateVegetationPlacement(const FVector& Location, const UAuracronVegetationPCGSettings* Settings) const;
    void IntegrateWithFoliageBridge(FPCGContext* Context, const UAuracronVegetationPCGSettings* Settings) const;
};

/**
 * Auracron Resource PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronResourcePCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronResourcePCGElement() = default;
    virtual ~FAuracronResourcePCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateResourceClusters(FPCGContext* Context, const UAuracronResourcePCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void CreateResourceCluster(const FVector& CenterLocation, const UAuracronResourcePCGSettings* Settings, FPCGDataCollection& OutputData) const;
};

/**
 * Auracron Enemy Spawn PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronEnemySpawnPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronEnemySpawnPCGElement() = default;
    virtual ~FAuracronEnemySpawnPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateSpawnPoints(FPCGContext* Context, const UAuracronEnemySpawnPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void CreatePatrolRoutes(FPCGContext* Context, const UAuracronEnemySpawnPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void IntegrateWithCombatBridge(FPCGContext* Context, const UAuracronEnemySpawnPCGSettings* Settings) const;
};

/**
 * Auracron Dungeon PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronDungeonPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronDungeonPCGElement() = default;
    virtual ~FAuracronDungeonPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void GenerateDungeonLayout(FPCGContext* Context, const UAuracronDungeonPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void CreateRooms(FPCGContext* Context, const UAuracronDungeonPCGSettings* Settings, TArray<FVector>& RoomLocations) const;
    void ConnectRoomsWithCorridors(const TArray<FVector>& RoomLocations, const UAuracronDungeonPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void PlaceTreasureRooms(FPCGContext* Context, const UAuracronDungeonPCGSettings* Settings, FPCGDataCollection& OutputData) const;
};

/**
 * Auracron Weather PCG Element
 */
class AURACRONPCGBRIDGE_API FAuracronWeatherPCGElement : public FAuracronPCGElementBase
{
public:
    FAuracronWeatherPCGElement() = default;
    virtual ~FAuracronWeatherPCGElement() = default;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    void ApplyWeatherEffects(FPCGContext* Context, const UAuracronWeatherPCGSettings* Settings, FPCGDataCollection& OutputData) const;
    void ModifyVegetationForWeather(FPCGContext* Context, const UAuracronWeatherPCGSettings* Settings) const;
    void IntegrateWithVFXBridge(FPCGContext* Context, const UAuracronWeatherPCGSettings* Settings) const;
    void IntegrateWithAudioBridge(FPCGContext* Context, const UAuracronWeatherPCGSettings* Settings) const;
};

// ========================================
// AURACRON PCG INTEGRATION SYSTEMS
// ========================================

/**
 * Auracron PCG Bridge API
 * Main interface for integrating with other Auracron bridges
 */
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API UAuracronPCGBridgeAPI : public UObject
{
    GENERATED_BODY()

public:
    UAuracronPCGBridgeAPI(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get());

    /** Initialize the PCG system */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool InitializePCGSystem();

    /** Shutdown the PCG system */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    void ShutdownPCGSystem();

    /** Check if system is ready */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Auracron PCG")
    bool IsSystemReady() const { return bSystemReady; }

    /** Generate biome content */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateBiomeContent(const FString& BiomeType, const FVector& Location, float Radius);

    /** Generate terrain features */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateTerrainFeatures(const FVector& Location, float Radius, int32 Seed);

    /** Generate vegetation */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateVegetation(const FString& VegetationType, const FVector& Location, float Density);

    /** Generate structures */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateStructures(const FString& StructureType, const FVector& Location, int32 Count);

    /** Generate resources */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateResources(const FString& ResourceType, const FVector& Location, float Rarity);

    /** Generate enemy spawns */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateEnemySpawns(const FString& EnemyType, const FVector& Location, float Density);

    /** Generate dungeon */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool GenerateDungeon(const FString& DungeonType, const FVector& Location, int32 RoomCount);

    /** Apply weather effects */
    UFUNCTION(BlueprintCallable, Category = "Auracron PCG")
    bool ApplyWeatherEffects(const FString& WeatherType, float Intensity, float Duration);

    /** Integration with Foliage Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithFoliageBridge();

    /** Integration with Dynamic Realm Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithDynamicRealmBridge();

    /** Integration with World Partition Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithWorldPartitionBridge();

    /** Integration with Combat Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithCombatBridge();

    /** Integration with VFX Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithVFXBridge();

    /** Integration with Audio Bridge */
    UFUNCTION(BlueprintCallable, Category = "Integration")
    bool IntegrateWithAudioBridge();

protected:
    /** System ready state */
    UPROPERTY(BlueprintReadOnly, Category = "State")
    bool bSystemReady = false;

    /** Integrated bridges */
    UPROPERTY(BlueprintReadOnly, Category = "Integration")
    TArray<FString> IntegratedBridges;

    /** Performance monitoring */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnablePerformanceMonitoring = true;

    /** Quality settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 QualityLevel = 3; // 1-5 scale

private:
    void RegisterPCGNodes();
    void InitializeBridgeIntegrations();
    bool ValidateSystemRequirements();
};
