// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronPrismalIsland.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPrismalIsland() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalIsland();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalIsland_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronIslandBenefits();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronIslandActivationRequirements *****************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements;
class UScriptStruct* FAuracronIslandActivationRequirements::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronIslandActivationRequirements"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Island activation requirements\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island activation requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinPlayersRequired_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Minimum players required for activation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Minimum players required for activation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPlayersSupported_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Maximum players that can benefit */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum players that can benefit" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationDuration_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Activation duration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Activation duration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownDuration_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown after deactivation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown after deactivation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamRequirement_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Team requirement (0 = any, 1 = same team, 2 = mixed teams) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Team requirement (0 = any, 1 = same team, 2 = mixed teams)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredPhase_MetaData[] = {
		{ "Category", "Activation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required evolution phase */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required evolution phase" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinPlayersRequired;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPlayersSupported;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamRequirement;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronIslandActivationRequirements>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_MinPlayersRequired = { "MinPlayersRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, MinPlayersRequired), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinPlayersRequired_MetaData), NewProp_MinPlayersRequired_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_MaxPlayersSupported = { "MaxPlayersSupported", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, MaxPlayersSupported), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPlayersSupported_MetaData), NewProp_MaxPlayersSupported_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_ActivationDuration = { "ActivationDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, ActivationDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationDuration_MetaData), NewProp_ActivationDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_CooldownDuration = { "CooldownDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, CooldownDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownDuration_MetaData), NewProp_CooldownDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_TeamRequirement = { "TeamRequirement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, TeamRequirement), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamRequirement_MetaData), NewProp_TeamRequirement_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_RequiredPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_RequiredPhase = { "RequiredPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandActivationRequirements, RequiredPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredPhase_MetaData), NewProp_RequiredPhase_MetaData) }; // 560471848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_MinPlayersRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_MaxPlayersSupported,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_ActivationDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_CooldownDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_TeamRequirement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_RequiredPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewProp_RequiredPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronIslandActivationRequirements",
	Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::PropPointers),
	sizeof(FAuracronIslandActivationRequirements),
	alignof(FAuracronIslandActivationRequirements),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.InnerSingleton, Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements.InnerSingleton;
}
// ********** End ScriptStruct FAuracronIslandActivationRequirements *******************************

// ********** Begin ScriptStruct FAuracronIslandBenefits *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits;
class UScriptStruct* FAuracronIslandBenefits::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronIslandBenefits, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronIslandBenefits"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Island benefits and effects\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island benefits and effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegenRate_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Health regeneration rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Health regeneration rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManaRegenRate_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mana regeneration rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mana regeneration rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageBoost_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Damage boost percentage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Damage boost percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefenseBoost_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Defense boost percentage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Defense boost percentage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedBoost_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Movement speed boost */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement speed boost" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownReduction_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ability cooldown reduction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ability cooldown reduction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisionRangeIncrease_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Vision range increase */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Vision range increase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpecialAbilities_MetaData[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Special abilities granted */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special abilities granted" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegenRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ManaRegenRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageBoost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefenseBoost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedBoost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisionRangeIncrease;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SpecialAbilities_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpecialAbilities;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronIslandBenefits>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_HealthRegenRate = { "HealthRegenRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, HealthRegenRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegenRate_MetaData), NewProp_HealthRegenRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_ManaRegenRate = { "ManaRegenRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, ManaRegenRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManaRegenRate_MetaData), NewProp_ManaRegenRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_DamageBoost = { "DamageBoost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, DamageBoost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageBoost_MetaData), NewProp_DamageBoost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_DefenseBoost = { "DefenseBoost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, DefenseBoost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefenseBoost_MetaData), NewProp_DefenseBoost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_MovementSpeedBoost = { "MovementSpeedBoost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, MovementSpeedBoost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedBoost_MetaData), NewProp_MovementSpeedBoost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_CooldownReduction = { "CooldownReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, CooldownReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownReduction_MetaData), NewProp_CooldownReduction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_VisionRangeIncrease = { "VisionRangeIncrease", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, VisionRangeIncrease), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisionRangeIncrease_MetaData), NewProp_VisionRangeIncrease_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_SpecialAbilities_Inner = { "SpecialAbilities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_SpecialAbilities = { "SpecialAbilities", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronIslandBenefits, SpecialAbilities), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpecialAbilities_MetaData), NewProp_SpecialAbilities_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_HealthRegenRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_ManaRegenRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_DamageBoost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_DefenseBoost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_MovementSpeedBoost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_CooldownReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_VisionRangeIncrease,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_SpecialAbilities_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewProp_SpecialAbilities,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronIslandBenefits",
	Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::PropPointers),
	sizeof(FAuracronIslandBenefits),
	alignof(FAuracronIslandBenefits),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronIslandBenefits()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.InnerSingleton, Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits.InnerSingleton;
}
// ********** End ScriptStruct FAuracronIslandBenefits *********************************************

// ********** Begin Class AAuracronPrismalIsland Function ActivateIsland ***************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "ActivateIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execActivateIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateIsland();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function ActivateIsland *****************************

// ********** Begin Class AAuracronPrismalIsland Function AddPlayerToIsland ************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics
{
	struct AuracronPrismalIsland_eventAddPlayerToIsland_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player interaction\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player interaction" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventAddPlayerToIsland_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "AddPlayerToIsland", Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::AuracronPrismalIsland_eventAddPlayerToIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::AuracronPrismalIsland_eventAddPlayerToIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execAddPlayerToIsland)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddPlayerToIsland(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function AddPlayerToIsland **************************

// ********** Begin Class AAuracronPrismalIsland Function ApplyBenefitsToPlayer ********************
struct Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics
{
	struct AuracronPrismalIsland_eventApplyBenefitsToPlayer_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Benefits" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Benefit application\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Benefit application" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventApplyBenefitsToPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "ApplyBenefitsToPlayer", Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::AuracronPrismalIsland_eventApplyBenefitsToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::AuracronPrismalIsland_eventApplyBenefitsToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execApplyBenefitsToPlayer)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyBenefitsToPlayer(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function ApplyBenefitsToPlayer **********************

// ********** Begin Class AAuracronPrismalIsland Function CanActivate ******************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics
{
	struct AuracronPrismalIsland_eventCanActivate_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPrismalIsland_eventCanActivate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPrismalIsland_eventCanActivate_Parms), &Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "CanActivate", Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::AuracronPrismalIsland_eventCanActivate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::AuracronPrismalIsland_eventCanActivate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execCanActivate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanActivate();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function CanActivate ********************************

// ********** Begin Class AAuracronPrismalIsland Function DeactivateIsland *************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "DeactivateIsland", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execDeactivateIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateIsland();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function DeactivateIsland ***************************

// ********** Begin Class AAuracronPrismalIsland Function GetPlayerCount ***************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics
{
	struct AuracronPrismalIsland_eventGetPlayerCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventGetPlayerCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "GetPlayerCount", Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::AuracronPrismalIsland_eventGetPlayerCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::AuracronPrismalIsland_eventGetPlayerCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execGetPlayerCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayerCount();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function GetPlayerCount *****************************

// ********** Begin Class AAuracronPrismalIsland Function GetPlayersOnIsland ***********************
struct Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics
{
	struct AuracronPrismalIsland_eventGetPlayersOnIsland_Parms
	{
		TArray<APawn*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventGetPlayersOnIsland_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "GetPlayersOnIsland", Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::AuracronPrismalIsland_eventGetPlayersOnIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::AuracronPrismalIsland_eventGetPlayersOnIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execGetPlayersOnIsland)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<APawn*>*)Z_Param__Result=P_THIS->GetPlayersOnIsland();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function GetPlayersOnIsland *************************

// ********** Begin Class AAuracronPrismalIsland Function IsIslandActive ***************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics
{
	struct AuracronPrismalIsland_eventIsIslandActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronPrismalIsland_eventIsIslandActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPrismalIsland_eventIsIslandActive_Parms), &Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "IsIslandActive", Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::AuracronPrismalIsland_eventIsIslandActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::AuracronPrismalIsland_eventIsIslandActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execIsIslandActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsIslandActive();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function IsIslandActive *****************************

// ********** Begin Class AAuracronPrismalIsland Function OnActivationTriggerBeginOverlap **********
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics
{
	struct AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collision events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collision events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms), &Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnActivationTriggerBeginOverlap", Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::AuracronPrismalIsland_eventOnActivationTriggerBeginOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execOnActivationTriggerBeginOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActivationTriggerBeginOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function OnActivationTriggerBeginOverlap ************

// ********** Begin Class AAuracronPrismalIsland Function OnActivationTriggerEndOverlap ************
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics
{
	struct AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnActivationTriggerEndOverlap", Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::AuracronPrismalIsland_eventOnActivationTriggerEndOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execOnActivationTriggerEndOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActivationTriggerEndOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function OnActivationTriggerEndOverlap **************

// ********** Begin Class AAuracronPrismalIsland Function OnBenefitAreaBeginOverlap ****************
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics
{
	struct AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms), &Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnBenefitAreaBeginOverlap", Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::AuracronPrismalIsland_eventOnBenefitAreaBeginOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execOnBenefitAreaBeginOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBenefitAreaBeginOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function OnBenefitAreaBeginOverlap ******************

// ********** Begin Class AAuracronPrismalIsland Function OnBenefitAreaEndOverlap ******************
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics
{
	struct AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::NewProp_OtherBodyIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnBenefitAreaEndOverlap", Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::AuracronPrismalIsland_eventOnBenefitAreaEndOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execOnBenefitAreaEndOverlap)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBenefitAreaEndOverlap(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function OnBenefitAreaEndOverlap ********************

// ********** Begin Class AAuracronPrismalIsland Function OnIslandActivated ************************
static FName NAME_AAuracronPrismalIsland_OnIslandActivated = FName(TEXT("OnIslandActivated"));
void AAuracronPrismalIsland::OnIslandActivated()
{
	UFunction* Func = FindFunctionChecked(NAME_AAuracronPrismalIsland_OnIslandActivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnIslandActivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronPrismalIsland Function OnIslandActivated **************************

// ********** Begin Class AAuracronPrismalIsland Function OnIslandDeactivated **********************
static FName NAME_AAuracronPrismalIsland_OnIslandDeactivated = FName(TEXT("OnIslandDeactivated"));
void AAuracronPrismalIsland::OnIslandDeactivated()
{
	UFunction* Func = FindFunctionChecked(NAME_AAuracronPrismalIsland_OnIslandDeactivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Events" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnIslandDeactivated", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronPrismalIsland Function OnIslandDeactivated ************************

// ********** Begin Class AAuracronPrismalIsland Function OnPlayerEntered **************************
struct AuracronPrismalIsland_eventOnPlayerEntered_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronPrismalIsland_OnPlayerEntered = FName(TEXT("OnPlayerEntered"));
void AAuracronPrismalIsland::OnPlayerEntered(APawn* Player)
{
	AuracronPrismalIsland_eventOnPlayerEntered_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronPrismalIsland_OnPlayerEntered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Events" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnPlayerEntered_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnPlayerEntered", Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::PropPointers), sizeof(AuracronPrismalIsland_eventOnPlayerEntered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronPrismalIsland_eventOnPlayerEntered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronPrismalIsland Function OnPlayerEntered ****************************

// ********** Begin Class AAuracronPrismalIsland Function OnPlayerLeft *****************************
struct AuracronPrismalIsland_eventOnPlayerLeft_Parms
{
	APawn* Player;
};
static FName NAME_AAuracronPrismalIsland_OnPlayerLeft = FName(TEXT("OnPlayerLeft"));
void AAuracronPrismalIsland::OnPlayerLeft(APawn* Player)
{
	AuracronPrismalIsland_eventOnPlayerLeft_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronPrismalIsland_OnPlayerLeft);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Events" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventOnPlayerLeft_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "OnPlayerLeft", Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::PropPointers), sizeof(AuracronPrismalIsland_eventOnPlayerLeft_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronPrismalIsland_eventOnPlayerLeft_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronPrismalIsland Function OnPlayerLeft *******************************

// ********** Begin Class AAuracronPrismalIsland Function PlayActivationEffects ********************
struct Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "PlayActivationEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execPlayActivationEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayActivationEffects();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function PlayActivationEffects **********************

// ********** Begin Class AAuracronPrismalIsland Function PlayDeactivationEffects ******************
struct Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "PlayDeactivationEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execPlayDeactivationEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayDeactivationEffects();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function PlayDeactivationEffects ********************

// ********** Begin Class AAuracronPrismalIsland Function RemoveBenefitsFromPlayer *****************
struct Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics
{
	struct AuracronPrismalIsland_eventRemoveBenefitsFromPlayer_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Benefits" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventRemoveBenefitsFromPlayer_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "RemoveBenefitsFromPlayer", Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::AuracronPrismalIsland_eventRemoveBenefitsFromPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::AuracronPrismalIsland_eventRemoveBenefitsFromPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execRemoveBenefitsFromPlayer)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveBenefitsFromPlayer(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function RemoveBenefitsFromPlayer *******************

// ********** Begin Class AAuracronPrismalIsland Function RemovePlayerFromIsland *******************
struct Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics
{
	struct AuracronPrismalIsland_eventRemovePlayerFromIsland_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player Interaction" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventRemovePlayerFromIsland_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "RemovePlayerFromIsland", Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::AuracronPrismalIsland_eventRemovePlayerFromIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::AuracronPrismalIsland_eventRemovePlayerFromIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execRemovePlayerFromIsland)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemovePlayerFromIsland(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function RemovePlayerFromIsland *********************

// ********** Begin Class AAuracronPrismalIsland Function UpdateIslandState ************************
struct Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics
{
	struct AuracronPrismalIsland_eventUpdateIslandState_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Island Management" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventUpdateIslandState_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "UpdateIslandState", Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::AuracronPrismalIsland_eventUpdateIslandState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::AuracronPrismalIsland_eventUpdateIslandState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execUpdateIslandState)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateIslandState(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function UpdateIslandState **************************

// ********** Begin Class AAuracronPrismalIsland Function UpdateIslandVisuals **********************
struct Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Presentation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual and audio\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual and audio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "UpdateIslandVisuals", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execUpdateIslandVisuals)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateIslandVisuals();
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function UpdateIslandVisuals ************************

// ********** Begin Class AAuracronPrismalIsland Function UpdatePlayerBenefits *********************
struct Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics
{
	struct AuracronPrismalIsland_eventUpdatePlayerBenefits_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Benefits" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronPrismalIsland_eventUpdatePlayerBenefits_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronPrismalIsland, nullptr, "UpdatePlayerBenefits", Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::AuracronPrismalIsland_eventUpdatePlayerBenefits_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::AuracronPrismalIsland_eventUpdatePlayerBenefits_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronPrismalIsland::execUpdatePlayerBenefits)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePlayerBenefits(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAuracronPrismalIsland Function UpdatePlayerBenefits ***********************

// ********** Begin Class AAuracronPrismalIsland ***************************************************
void AAuracronPrismalIsland::StaticRegisterNativesAAuracronPrismalIsland()
{
	UClass* Class = AAuracronPrismalIsland::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateIsland", &AAuracronPrismalIsland::execActivateIsland },
		{ "AddPlayerToIsland", &AAuracronPrismalIsland::execAddPlayerToIsland },
		{ "ApplyBenefitsToPlayer", &AAuracronPrismalIsland::execApplyBenefitsToPlayer },
		{ "CanActivate", &AAuracronPrismalIsland::execCanActivate },
		{ "DeactivateIsland", &AAuracronPrismalIsland::execDeactivateIsland },
		{ "GetPlayerCount", &AAuracronPrismalIsland::execGetPlayerCount },
		{ "GetPlayersOnIsland", &AAuracronPrismalIsland::execGetPlayersOnIsland },
		{ "IsIslandActive", &AAuracronPrismalIsland::execIsIslandActive },
		{ "OnActivationTriggerBeginOverlap", &AAuracronPrismalIsland::execOnActivationTriggerBeginOverlap },
		{ "OnActivationTriggerEndOverlap", &AAuracronPrismalIsland::execOnActivationTriggerEndOverlap },
		{ "OnBenefitAreaBeginOverlap", &AAuracronPrismalIsland::execOnBenefitAreaBeginOverlap },
		{ "OnBenefitAreaEndOverlap", &AAuracronPrismalIsland::execOnBenefitAreaEndOverlap },
		{ "PlayActivationEffects", &AAuracronPrismalIsland::execPlayActivationEffects },
		{ "PlayDeactivationEffects", &AAuracronPrismalIsland::execPlayDeactivationEffects },
		{ "RemoveBenefitsFromPlayer", &AAuracronPrismalIsland::execRemoveBenefitsFromPlayer },
		{ "RemovePlayerFromIsland", &AAuracronPrismalIsland::execRemovePlayerFromIsland },
		{ "UpdateIslandState", &AAuracronPrismalIsland::execUpdateIslandState },
		{ "UpdateIslandVisuals", &AAuracronPrismalIsland::execUpdateIslandVisuals },
		{ "UpdatePlayerBenefits", &AAuracronPrismalIsland::execUpdatePlayerBenefits },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronPrismalIsland;
UClass* AAuracronPrismalIsland::GetPrivateStaticClass()
{
	using TClass = AAuracronPrismalIsland;
	if (!Z_Registration_Info_UClass_AAuracronPrismalIsland.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPrismalIsland"),
			Z_Registration_Info_UClass_AAuracronPrismalIsland.InnerSingleton,
			StaticRegisterNativesAAuracronPrismalIsland,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronPrismalIsland.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronPrismalIsland_NoRegister()
{
	return AAuracronPrismalIsland::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronPrismalIsland_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Prismal Island\n * \n * Strategic islands within the Prismal Flow:\n * \n * Nexus Islands (5 total):\n * - Control towers with defensive positions\n * - Resource generators\n * - Flow manipulation abilities\n * \n * Santu\xc3\xa1rio Islands (8 total):\n * - Safe zones for healing\n * - Temporary shields\n * - Vision amplifiers\n * \n * Arsenal Islands (6 total):\n * - Weapon upgrades\n * - Ability enhancers\n * - Temporary buffs\n * \n * Caos Islands (4 total):\n * - High risk, high reward\n * - Environmental hazards\n * - Unstable terrain\n */" },
#endif
		{ "IncludePath", "AuracronPrismalIsland.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Prismal Island\n\nStrategic islands within the Prismal Flow:\n\nNexus Islands (5 total):\n- Control towers with defensive positions\n- Resource generators\n- Flow manipulation abilities\n\nSantu\xc3\xa1rio Islands (8 total):\n- Safe zones for healing\n- Temporary shields\n- Vision amplifiers\n\nArsenal Islands (6 total):\n- Weapon upgrades\n- Ability enhancers\n- Temporary buffs\n\nCaos Islands (4 total):\n- High risk, high reward\n- Environmental hazards\n- Unstable terrain" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandType_MetaData[] = {
		{ "Category", "Island Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationRequirements_MetaData[] = {
		{ "Category", "Island Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandBenefits_MetaData[] = {
		{ "Category", "Island Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandRadius_MetaData[] = {
		{ "Category", "Island Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoActivate_MetaData[] = {
		{ "Category", "Island Configuration" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandMesh_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationTrigger_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BenefitArea_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandEffects_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandAudio_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Island State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Island state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsOnCooldown_MetaData[] = {
		{ "Category", "Island State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationTime_MetaData[] = {
		{ "Category", "Island State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CooldownStartTime_MetaData[] = {
		{ "Category", "Island State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "Island State" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayersOnIsland_MetaData[] = {
		{ "Category", "Player Tracking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayersInBenefitArea_MetaData[] = {
		{ "Category", "Player Tracking" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerBenefitTimers_MetaData[] = {
		{ "Category", "Player Tracking" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InactiveMesh_MetaData[] = {
		{ "Category", "Visual Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual assets\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveMesh_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InactiveMaterial_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveMaterial_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationEffect_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveEffect_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeactivationEffect_MetaData[] = {
		{ "Category", "Visual Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationSound_MetaData[] = {
		{ "Category", "Audio Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio assets\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveLoopSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeactivationSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BenefitAppliedSound_MetaData[] = {
		{ "Category", "Audio Assets" },
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached references\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedPrismalFlow_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicIslandMaterial_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dynamic materials\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPrismalIsland.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic materials" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActivationRequirements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IslandBenefits;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_IslandRadius;
	static void NewProp_bAutoActivate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoActivate;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivationTrigger;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BenefitArea;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IslandEffects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandAudio;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsOnCooldown_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOnCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CooldownStartTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayersOnIsland_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayersOnIsland;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayersInBenefitArea_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayersInBenefitArea;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerBenefitTimers_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerBenefitTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerBenefitTimers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InactiveMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InactiveMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveMaterial;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivationEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeactivationEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivationSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveLoopSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeactivationSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BenefitAppliedSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedPrismalFlow;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DynamicIslandMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_ActivateIsland, "ActivateIsland" }, // 2963818922
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_AddPlayerToIsland, "AddPlayerToIsland" }, // 2264384226
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_ApplyBenefitsToPlayer, "ApplyBenefitsToPlayer" }, // 765410912
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_CanActivate, "CanActivate" }, // 2571021773
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_DeactivateIsland, "DeactivateIsland" }, // 2645174684
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayerCount, "GetPlayerCount" }, // 3964920934
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_GetPlayersOnIsland, "GetPlayersOnIsland" }, // 4292150413
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_IsIslandActive, "IsIslandActive" }, // 1538838336
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerBeginOverlap, "OnActivationTriggerBeginOverlap" }, // 3965398561
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnActivationTriggerEndOverlap, "OnActivationTriggerEndOverlap" }, // 1512306036
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaBeginOverlap, "OnBenefitAreaBeginOverlap" }, // 1320420315
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnBenefitAreaEndOverlap, "OnBenefitAreaEndOverlap" }, // 1633324796
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandActivated, "OnIslandActivated" }, // 2195454462
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnIslandDeactivated, "OnIslandDeactivated" }, // 2836723073
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerEntered, "OnPlayerEntered" }, // 2344903171
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_OnPlayerLeft, "OnPlayerLeft" }, // 863518767
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_PlayActivationEffects, "PlayActivationEffects" }, // 115524956
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_PlayDeactivationEffects, "PlayDeactivationEffects" }, // 2797400786
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_RemoveBenefitsFromPlayer, "RemoveBenefitsFromPlayer" }, // 3476229792
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_RemovePlayerFromIsland, "RemovePlayerFromIsland" }, // 2525203743
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandState, "UpdateIslandState" }, // 3590803860
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_UpdateIslandVisuals, "UpdateIslandVisuals" }, // 1848791323
		{ &Z_Construct_UFunction_AAuracronPrismalIsland_UpdatePlayerBenefits, "UpdatePlayerBenefits" }, // 3303990603
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronPrismalIsland>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandType_MetaData), NewProp_IslandType_MetaData) }; // 1145382866
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationRequirements = { "ActivationRequirements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActivationRequirements), Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationRequirements_MetaData), NewProp_ActivationRequirements_MetaData) }; // 1812216149
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandBenefits = { "IslandBenefits", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandBenefits), Z_Construct_UScriptStruct_FAuracronIslandBenefits, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandBenefits_MetaData), NewProp_IslandBenefits_MetaData) }; // 417448644
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandRadius = { "IslandRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandRadius_MetaData), NewProp_IslandRadius_MetaData) };
void Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bAutoActivate_SetBit(void* Obj)
{
	((AAuracronPrismalIsland*)Obj)->bAutoActivate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bAutoActivate = { "bAutoActivate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronPrismalIsland), &Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bAutoActivate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoActivate_MetaData), NewProp_bAutoActivate_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandMesh = { "IslandMesh", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandMesh_MetaData), NewProp_IslandMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationTrigger = { "ActivationTrigger", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActivationTrigger), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationTrigger_MetaData), NewProp_ActivationTrigger_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_BenefitArea = { "BenefitArea", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, BenefitArea), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BenefitArea_MetaData), NewProp_BenefitArea_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandEffects_Inner = { "IslandEffects", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandEffects = { "IslandEffects", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandEffects_MetaData), NewProp_IslandEffects_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandAudio = { "IslandAudio", nullptr, (EPropertyFlags)0x01140000000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, IslandAudio), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandAudio_MetaData), NewProp_IslandAudio_MetaData) };
void Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((AAuracronPrismalIsland*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronPrismalIsland), &Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsOnCooldown_SetBit(void* Obj)
{
	((AAuracronPrismalIsland*)Obj)->bIsOnCooldown = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsOnCooldown = { "bIsOnCooldown", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAuracronPrismalIsland), &Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsOnCooldown_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsOnCooldown_MetaData), NewProp_bIsOnCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationTime = { "ActivationTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActivationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationTime_MetaData), NewProp_ActivationTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CooldownStartTime = { "CooldownStartTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, CooldownStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CooldownStartTime_MetaData), NewProp_CooldownStartTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersOnIsland_Inner = { "PlayersOnIsland", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersOnIsland = { "PlayersOnIsland", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, PlayersOnIsland), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayersOnIsland_MetaData), NewProp_PlayersOnIsland_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersInBenefitArea_Inner = { "PlayersInBenefitArea", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersInBenefitArea = { "PlayersInBenefitArea", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, PlayersInBenefitArea), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayersInBenefitArea_MetaData), NewProp_PlayersInBenefitArea_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers_ValueProp = { "PlayerBenefitTimers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers_Key_KeyProp = { "PlayerBenefitTimers_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers = { "PlayerBenefitTimers", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, PlayerBenefitTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerBenefitTimers_MetaData), NewProp_PlayerBenefitTimers_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_InactiveMesh = { "InactiveMesh", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, InactiveMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InactiveMesh_MetaData), NewProp_InactiveMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveMesh = { "ActiveMesh", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActiveMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveMesh_MetaData), NewProp_ActiveMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_InactiveMaterial = { "InactiveMaterial", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, InactiveMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InactiveMaterial_MetaData), NewProp_InactiveMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveMaterial = { "ActiveMaterial", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActiveMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveMaterial_MetaData), NewProp_ActiveMaterial_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationEffect = { "ActivationEffect", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActivationEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationEffect_MetaData), NewProp_ActivationEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveEffect = { "ActiveEffect", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActiveEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveEffect_MetaData), NewProp_ActiveEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DeactivationEffect = { "DeactivationEffect", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, DeactivationEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeactivationEffect_MetaData), NewProp_DeactivationEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationSound = { "ActivationSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActivationSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationSound_MetaData), NewProp_ActivationSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveLoopSound = { "ActiveLoopSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, ActiveLoopSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveLoopSound_MetaData), NewProp_ActiveLoopSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DeactivationSound = { "DeactivationSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, DeactivationSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeactivationSound_MetaData), NewProp_DeactivationSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_BenefitAppliedSound = { "BenefitAppliedSound", nullptr, (EPropertyFlags)0x0124080000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, BenefitAppliedSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BenefitAppliedSound_MetaData), NewProp_BenefitAppliedSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CachedPrismalFlow = { "CachedPrismalFlow", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, CachedPrismalFlow), Z_Construct_UClass_AAuracronPrismalFlow_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedPrismalFlow_MetaData), NewProp_CachedPrismalFlow_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DynamicIslandMaterial = { "DynamicIslandMaterial", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronPrismalIsland, DynamicIslandMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicIslandMaterial_MetaData), NewProp_DynamicIslandMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronPrismalIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationRequirements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandBenefits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bAutoActivate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationTrigger,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_BenefitArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_IslandAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_bIsOnCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CooldownStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersOnIsland_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersOnIsland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersInBenefitArea_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayersInBenefitArea,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_PlayerBenefitTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_InactiveMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_InactiveMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DeactivationEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActivationSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_ActiveLoopSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DeactivationSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_BenefitAppliedSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CachedRealmSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_CachedPrismalFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronPrismalIsland_Statics::NewProp_DynamicIslandMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalIsland_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronPrismalIsland_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalIsland_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronPrismalIsland_Statics::ClassParams = {
	&AAuracronPrismalIsland::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronPrismalIsland_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalIsland_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronPrismalIsland_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronPrismalIsland_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronPrismalIsland()
{
	if (!Z_Registration_Info_UClass_AAuracronPrismalIsland.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronPrismalIsland.OuterSingleton, Z_Construct_UClass_AAuracronPrismalIsland_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronPrismalIsland.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronPrismalIsland);
AAuracronPrismalIsland::~AAuracronPrismalIsland() {}
// ********** End Class AAuracronPrismalIsland *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronIslandActivationRequirements::StaticStruct, Z_Construct_UScriptStruct_FAuracronIslandActivationRequirements_Statics::NewStructOps, TEXT("AuracronIslandActivationRequirements"), &Z_Registration_Info_UScriptStruct_FAuracronIslandActivationRequirements, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronIslandActivationRequirements), 1812216149U) },
		{ FAuracronIslandBenefits::StaticStruct, Z_Construct_UScriptStruct_FAuracronIslandBenefits_Statics::NewStructOps, TEXT("AuracronIslandBenefits"), &Z_Registration_Info_UScriptStruct_FAuracronIslandBenefits, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronIslandBenefits), 417448644U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronPrismalIsland, AAuracronPrismalIsland::StaticClass, TEXT("AAuracronPrismalIsland"), &Z_Registration_Info_UClass_AAuracronPrismalIsland, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronPrismalIsland), 192469908U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_3988098523(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronPrismalIsland_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
