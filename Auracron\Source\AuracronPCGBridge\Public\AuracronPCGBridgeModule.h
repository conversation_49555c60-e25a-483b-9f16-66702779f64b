// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Main Bridge Header
// Bridge 2.2: PCG Framework - Core Integration

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronPCGBridge, Log, All);

/**
 * Auracron PCG Bridge Module
 * Manages the PCG framework integration and bridge communications
 */
class AURACRONPCGBRIDGE_API FAuracronPCGBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /** Check if a bridge is available for integration */
    bool IsBridgeAvailable(const FString& BridgeName) const;

    /** Check if a bridge is currently integrated */
    bool IsBridgeIntegrated(const FString& BridgeName) const;

    /** Register a bridge integration */
    void RegisterBridgeIntegration(const FString& BridgeName);

    /** Unregister a bridge integration */
    void UnregisterBridgeIntegration(const FString& BridgeName);

    /** Get list of available bridges */
    const TArray<FString>& GetAvailableBridges() const { return AvailableBridges; }

    /** Get list of integrated bridges */
    const TArray<FString>& GetIntegratedBridges() const { return IntegratedBridges; }

private:
    /** Initialize PCG systems */
    void InitializePCGSystems();

    /** Register PCG nodes */
    void RegisterPCGNodes();

    /** Initialize bridge integrations */
    void InitializeBridgeIntegrations();

    /** Shutdown PCG systems */
    void ShutdownPCGSystems();

    /** Unregister PCG nodes */
    void UnregisterPCGNodes();

    /** Shutdown bridge integrations */
    void ShutdownBridgeIntegrations();

    /** Available bridges for integration */
    TArray<FString> AvailableBridges;

    /** Currently integrated bridges */
    TArray<FString> IntegratedBridges;
};
