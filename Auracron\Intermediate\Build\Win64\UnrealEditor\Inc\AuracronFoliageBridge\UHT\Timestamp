C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliage.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageBiome.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageBridge.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageCollision.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageInteraction.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageLOD.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageMaterialVariation.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliagePerformanceOptimization.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageProcedural.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageSeasonal.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageInstanced.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageWind.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\AuracronFoliageStreaming.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\FoliageEditModule.h
C:\Aura\projeto\Auracron\Source\AuracronFoliageBridge\Public\PCGBiomeCache.h
