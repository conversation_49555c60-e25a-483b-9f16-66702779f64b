// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "HarmonyRewardsSystem.h"

#ifdef AURACRONHARMONYENGINEBRIDGE_HarmonyRewardsSystem_generated_h
#error "HarmonyRewardsSystem.generated.h already included, missing '#pragma once' in HarmonyRewardsSystem.h"
#endif
#define AURACRONHARMONYENGINEBRIDGE_HarmonyRewardsSystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class ERewardCategory : uint8;
enum class ERewardTier : uint8;
struct FHarmonyReward;
struct FKindnessReward;
struct FPlayerRewardProgress;

// ********** Begin ScriptStruct FHarmonyReward ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_42_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHarmonyReward_Statics; \
	static class UScriptStruct* StaticStruct();


struct FHarmonyReward;
// ********** End ScriptStruct FHarmonyReward ******************************************************

// ********** Begin ScriptStruct FPlayerRewardProgress *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_101_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerRewardProgress_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPlayerRewardProgress;
// ********** End ScriptStruct FPlayerRewardProgress ***********************************************

// ********** Begin Class UHarmonyRewardsSystem ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCalculateTierExperienceBonus); \
	DECLARE_FUNCTION(execCalculateTierCurrencyBonus); \
	DECLARE_FUNCTION(execGetTierDisplayName); \
	DECLARE_FUNCTION(execGetNextTier); \
	DECLARE_FUNCTION(execApplyExperienceBonus); \
	DECLARE_FUNCTION(execApplyCurrencyBonus); \
	DECLARE_FUNCTION(execGetCurrentEventMultiplier); \
	DECLARE_FUNCTION(execIsSpecialEventActive); \
	DECLARE_FUNCTION(execTriggerSpecialEvent); \
	DECLARE_FUNCTION(execGetReward); \
	DECLARE_FUNCTION(execUnregisterReward); \
	DECLARE_FUNCTION(execRegisterReward); \
	DECLARE_FUNCTION(execCalculatePlayerTier); \
	DECLARE_FUNCTION(execGetProgressToNextTier); \
	DECLARE_FUNCTION(execUpdatePlayerProgress); \
	DECLARE_FUNCTION(execGetPlayerProgress); \
	DECLARE_FUNCTION(execIsRewardUnlocked); \
	DECLARE_FUNCTION(execGetUnlockedRewards); \
	DECLARE_FUNCTION(execGetAvailableRewards); \
	DECLARE_FUNCTION(execGrantReward); \
	DECLARE_FUNCTION(execProcessKindnessReward);


AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyRewardsSystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUHarmonyRewardsSystem(); \
	friend struct Z_Construct_UClass_UHarmonyRewardsSystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyRewardsSystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UHarmonyRewardsSystem, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronHarmonyEngineBridge"), Z_Construct_UClass_UHarmonyRewardsSystem_NoRegister) \
	DECLARE_SERIALIZER(UHarmonyRewardsSystem)


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UHarmonyRewardsSystem(UHarmonyRewardsSystem&&) = delete; \
	UHarmonyRewardsSystem(const UHarmonyRewardsSystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UHarmonyRewardsSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UHarmonyRewardsSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UHarmonyRewardsSystem) \
	NO_API virtual ~UHarmonyRewardsSystem();


#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_142_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h_145_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UHarmonyRewardsSystem;

// ********** End Class UHarmonyRewardsSystem ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyRewardsSystem_h

// ********** Begin Enum ERewardTier ***************************************************************
#define FOREACH_ENUM_EREWARDTIER(op) \
	op(ERewardTier::Bronze) \
	op(ERewardTier::Silver) \
	op(ERewardTier::Gold) \
	op(ERewardTier::Platinum) \
	op(ERewardTier::Diamond) \
	op(ERewardTier::Legendary) 

enum class ERewardTier : uint8;
template<> struct TIsUEnumClass<ERewardTier> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<ERewardTier>();
// ********** End Enum ERewardTier *****************************************************************

// ********** Begin Enum ERewardCategory ***********************************************************
#define FOREACH_ENUM_EREWARDCATEGORY(op) \
	op(ERewardCategory::Kindness) \
	op(ERewardCategory::Mentorship) \
	op(ERewardCategory::Leadership) \
	op(ERewardCategory::Healing) \
	op(ERewardCategory::Consistency) \
	op(ERewardCategory::Innovation) \
	op(ERewardCategory::Teamwork) \
	op(ERewardCategory::Resilience) 

enum class ERewardCategory : uint8;
template<> struct TIsUEnumClass<ERewardCategory> { enum { Value = true }; };
template<> AURACRONHARMONYENGINEBRIDGE_API UEnum* StaticEnum<ERewardCategory>();
// ********** End Enum ERewardCategory *************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
