# Script para corrigir includes da macro AURACRON_PCG_NODE_SETTINGS
# Adiciona o include do AuracronPCGNodeSystem.h nos arquivos que usam a macro

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao de includes da macro AURACRON_PCG_NODE_SETTINGS..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Encontra todos os arquivos .h que usam a macro AURACRON_PCG_NODE_SETTINGS
$filesWithMacro = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h" | 
    Where-Object { $_.Name -notlike "*.generated.*" } |
    Where-Object { (Get-Content $_.FullName -Raw) -match "AURACRON_PCG_NODE_SETTINGS" }

Write-Host "Encontrados $($filesWithMacro.Count) arquivos que usam a macro..." -ForegroundColor Cyan

$totalModifications = 0
$filesModified = 0

foreach ($file in $filesWithMacro) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $modified = $false
    
    # Verifica se ja tem o include do AuracronPCGNodeSystem.h
    if ($content -notmatch '#include "AuracronPCGNodeSystem\.h"') {
        # Procura por um local apropriado para inserir o include
        # Tenta inserir apos #include "CoreMinimal.h"
        if ($content -match '#include "CoreMinimal\.h"') {
            $content = $content -replace '(#include "CoreMinimal\.h")', '$1`n#include "AuracronPCGNodeSystem.h"'
            $modified = $true
            Write-Host "    Adicionado include do AuracronPCGNodeSystem.h apos CoreMinimal.h" -ForegroundColor Yellow
        }
        # Se nao encontrou CoreMinimal.h, tenta inserir no inicio dos includes
        elseif ($content -match '#pragma once\s*\n') {
            $content = $content -replace '(#pragma once\s*\n)', '$1`n#include "AuracronPCGNodeSystem.h"`n'
            $modified = $true
            Write-Host "    Adicionado include do AuracronPCGNodeSystem.h apos #pragma once" -ForegroundColor Yellow
        }
        else {
            Write-Host "    AVISO: Nao foi possivel encontrar local apropriado para inserir o include" -ForegroundColor Red
        }
    } else {
        Write-Host "    Include ja existe" -ForegroundColor Gray
    }
    
    # Salva o arquivo se houve mudancas
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalModifications++
        $filesModified++
        Write-Host "  OK Include adicionado" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma modificacao necessaria" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($filesWithMacro.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de modificacoes: $totalModifications" -ForegroundColor Green

if ($totalModifications -gt 0) {
    Write-Host "`nCorrecao de includes da macro concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma correcao necessaria." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
