// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronRealmsBridge.h"

#ifdef AURACRONREALMSBRIDGE_AuracronRealmsBridge_generated_h
#error "AuracronRealmsBridge.generated.h already included, missing '#pragma once' in AuracronRealmsBridge.h"
#endif
#define AURACRONREALMSBRIDGE_AuracronRealmsBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAuracronRealmTransitionState : uint8;
enum class EAuracronRealmType : uint8;
struct FAuracronDimensionalPortal;
struct FAuracronMapEvolution;
struct FAuracronRealmConfiguration;

// ********** Begin ScriptStruct FAuracronRealmConfiguration ***************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_84_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmConfiguration;
// ********** End ScriptStruct FAuracronRealmConfiguration *****************************************

// ********** Begin ScriptStruct FAuracronDimensionalPortal ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_165_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDimensionalPortal_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDimensionalPortal;
// ********** End ScriptStruct FAuracronDimensionalPortal ******************************************

// ********** Begin ScriptStruct FAuracronMapEvolution *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_230_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronMapEvolution_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronMapEvolution;
// ********** End ScriptStruct FAuracronMapEvolution ***********************************************

// ********** Begin ScriptStruct FAuracronVerticalConnector ****************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_291_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronVerticalConnector_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronVerticalConnector;
// ********** End ScriptStruct FAuracronVerticalConnector ******************************************

// ********** Begin ScriptStruct FAuracronProceduralGenerationConfig *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_356_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralGenerationConfig;
// ********** End ScriptStruct FAuracronProceduralGenerationConfig *********************************

// ********** Begin ScriptStruct FAuracronRealmConfigurationEntry **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_409_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmConfigurationEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmConfigurationEntry;
// ********** End ScriptStruct FAuracronRealmConfigurationEntry ************************************

// ********** Begin ScriptStruct FAuracronProceduralGenerationConfigEntry **************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_434_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralGenerationConfigEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralGenerationConfigEntry;
// ********** End ScriptStruct FAuracronProceduralGenerationConfigEntry ****************************

// ********** Begin Delegate FOnRealmActivated *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_807_DELEGATE \
static void FOnRealmActivated_DelegateWrapper(const FMulticastScriptDelegate& OnRealmActivated, EAuracronRealmType RealmType);


// ********** End Delegate FOnRealmActivated *******************************************************

// ********** Begin Delegate FOnRealmDeactivated ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_812_DELEGATE \
static void FOnRealmDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnRealmDeactivated, EAuracronRealmType RealmType);


// ********** End Delegate FOnRealmDeactivated *****************************************************

// ********** Begin Delegate FOnTransitionStarted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_817_DELEGATE \
static void FOnTransitionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnTransitionStarted, EAuracronRealmType FromRealm, EAuracronRealmType ToRealm);


// ********** End Delegate FOnTransitionStarted ****************************************************

// ********** Begin Delegate FOnTransitionCompleted ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_822_DELEGATE \
static void FOnTransitionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTransitionCompleted, EAuracronRealmType FromRealm, EAuracronRealmType ToRealm);


// ********** End Delegate FOnTransitionCompleted **************************************************

// ********** Begin Delegate FOnEvolutionStarted ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_827_DELEGATE \
static void FOnEvolutionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnEvolutionStarted, int32 EvolutionIndex);


// ********** End Delegate FOnEvolutionStarted *****************************************************

// ********** Begin Delegate FOnEvolutionCompleted *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_832_DELEGATE \
static void FOnEvolutionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnEvolutionCompleted, int32 EvolutionIndex);


// ********** End Delegate FOnEvolutionCompleted ***************************************************

// ********** Begin Delegate FOnPortalCreated ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_837_DELEGATE \
static void FOnPortalCreated_DelegateWrapper(const FMulticastScriptDelegate& OnPortalCreated, int32 PortalIndex);


// ********** End Delegate FOnPortalCreated ********************************************************

// ********** Begin Delegate FOnPortalRemoved ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_842_DELEGATE \
static void FOnPortalRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPortalRemoved, int32 PortalIndex);


// ********** End Delegate FOnPortalRemoved ********************************************************

// ********** Begin Delegate FOnProceduralGenerationCompleted **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_847_DELEGATE \
static void FOnProceduralGenerationCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnProceduralGenerationCompleted, EAuracronRealmType RealmType);


// ********** End Delegate FOnProceduralGenerationCompleted ****************************************

// ********** Begin Class UAuracronRealmsBridge ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_TransitionState); \
	DECLARE_FUNCTION(execOnRep_ActiveRealm); \
	DECLARE_FUNCTION(execLoadDefaultRealmConfigurations); \
	DECLARE_FUNCTION(execSetRealmConfiguration); \
	DECLARE_FUNCTION(execGetRealmConfiguration); \
	DECLARE_FUNCTION(execOptimizeWorldPartitionStreaming); \
	DECLARE_FUNCTION(execUnloadRealmDataLayers); \
	DECLARE_FUNCTION(execLoadRealmDataLayers); \
	DECLARE_FUNCTION(execIsEvolutionActive); \
	DECLARE_FUNCTION(execGetEvolutionProgress); \
	DECLARE_FUNCTION(execStopMapEvolution); \
	DECLARE_FUNCTION(execStartMapEvolution); \
	DECLARE_FUNCTION(execGetProceduralGenerationProgress); \
	DECLARE_FUNCTION(execClearProceduralContent); \
	DECLARE_FUNCTION(execRegenerateProceduralContent); \
	DECLARE_FUNCTION(execExecuteProceduralGeneration); \
	DECLARE_FUNCTION(execFindNearestPortal); \
	DECLARE_FUNCTION(execGetActivePortals); \
	DECLARE_FUNCTION(execSetPortalActive); \
	DECLARE_FUNCTION(execRemoveDimensionalPortal); \
	DECLARE_FUNCTION(execCreateDimensionalPortal); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execGetTransitionState); \
	DECLARE_FUNCTION(execCancelRealmTransition); \
	DECLARE_FUNCTION(execCompleteRealmTransition); \
	DECLARE_FUNCTION(execStartRealmTransition); \
	DECLARE_FUNCTION(execIsRealmActive); \
	DECLARE_FUNCTION(execGetActiveRealms); \
	DECLARE_FUNCTION(execGetActiveRealm); \
	DECLARE_FUNCTION(execToggleRealmVisibility); \
	DECLARE_FUNCTION(execDeactivateRealm); \
	DECLARE_FUNCTION(execActivateRealm);


AURACRONREALMSBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmsBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronRealmsBridge(); \
	friend struct Z_Construct_UClass_UAuracronRealmsBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONREALMSBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmsBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronRealmsBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronRealmsBridge"), Z_Construct_UClass_UAuracronRealmsBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronRealmsBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		RealmConfigurations=NETFIELD_REP_START, \
		DimensionalPortals, \
		VerticalConnectors, \
		ProceduralConfigs, \
		ScheduledEvolutions, \
		CurrentActiveRealm, \
		CurrentTransitionState, \
		TransitionStartTime, \
		TransitionFromRealm, \
		TransitionToRealm, \
		CurrentEvolutionIndex, \
		EvolutionStartTime, \
		NETFIELD_REP_END=EvolutionStartTime	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronRealmsBridge(UAuracronRealmsBridge&&) = delete; \
	UAuracronRealmsBridge(const UAuracronRealmsBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronRealmsBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronRealmsBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronRealmsBridge) \
	NO_API virtual ~UAuracronRealmsBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_457_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h_460_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronRealmsBridge;

// ********** End Class UAuracronRealmsBridge ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronRealmsBridge_Public_AuracronRealmsBridge_h

// ********** Begin Enum EAuracronRealmType ********************************************************
#define FOREACH_ENUM_EAURACRONREALMTYPE(op) \
	op(EAuracronRealmType::None) \
	op(EAuracronRealmType::PlanicieRadiante) \
	op(EAuracronRealmType::FirmamentoZephyr) \
	op(EAuracronRealmType::AbismoUmbrio) 

enum class EAuracronRealmType : uint8;
template<> struct TIsUEnumClass<EAuracronRealmType> { enum { Value = true }; };
template<> AURACRONREALMSBRIDGE_API UEnum* StaticEnum<EAuracronRealmType>();
// ********** End Enum EAuracronRealmType **********************************************************

// ********** Begin Enum EAuracronRealmTransitionState *********************************************
#define FOREACH_ENUM_EAURACRONREALMTRANSITIONSTATE(op) \
	op(EAuracronRealmTransitionState::Stable) \
	op(EAuracronRealmTransitionState::Transitioning) \
	op(EAuracronRealmTransitionState::Evolving) \
	op(EAuracronRealmTransitionState::Merging) \
	op(EAuracronRealmTransitionState::Splitting) 

enum class EAuracronRealmTransitionState : uint8;
template<> struct TIsUEnumClass<EAuracronRealmTransitionState> { enum { Value = true }; };
template<> AURACRONREALMSBRIDGE_API UEnum* StaticEnum<EAuracronRealmTransitionState>();
// ********** End Enum EAuracronRealmTransitionState ***********************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
