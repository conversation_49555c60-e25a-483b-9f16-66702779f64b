{"SourceFiles": ["C:\\Aura\\projeto\\Auracron\\Source\\AuracronAbismoUmbrioBridge\\AuracronAbismoUmbrioBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronAdaptiveCreaturesBridge\\AuracronAdaptiveCreaturesBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronAnalyticsBridge\\AuracronAnalyticsBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronAntiCheatBridge\\AuracronAntiCheatBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronAudioBridge\\AuracronAudioBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronAutomatedQABridge\\AuracronAutomatedQABridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronChampionsBridge\\AuracronChampionsBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronCombatBridge\\AuracronCombatBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronDynamicRealmBridge\\AuracronDynamicRealmBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronEOSBridge\\AuracronEOSBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronFoliageBridge\\AuracronFoliageBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronHarmonyEngineBridge\\AuracronHarmonyEngineBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronLoreBridge\\AuracronLoreBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronLumenBridge\\AuracronLumenBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronMasterOrchestrator\\AuracronMasterOrchestrator.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronMetaHumanBridge\\AuracronMetaHumanBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronMetaHumanFramework\\AuracronMetaHumanFramework.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronMonetizationBridge\\AuracronMonetizationBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronNaniteBridge\\AuracronNaniteBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronNetworkingBridge\\AuracronNetworkingBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronPCGBridge\\AuracronPCGBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronPhysicsBridge\\AuracronPhysicsBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronProgressionBridge\\AuracronProgressionBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronQABridge\\AuracronQABridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronRealmsBridge\\AuracronRealmsBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronSigilSystem\\AuracronSigilSystem.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronSigilosBridge\\AuracronSigilosBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronTutorialBridge\\AuracronTutorialBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronUIBridge\\AuracronUIBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronVFXBridge\\AuracronVFXBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronVerticalTransitionsBridge\\AuracronVerticalTransitionsBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronVoiceBridge\\AuracronVoiceBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronWorldPartitionBridge\\AuracronWorldPartitionBridge.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\Auracron\\Auracron.Build.cs", "C:\\Aura\\projeto\\Auracron\\Source\\Auracron.Target.cs", "C:\\Aura\\projeto\\Auracron\\Source\\AuracronEditor.Target.cs"], "EngineVersion": "5.6.0"}