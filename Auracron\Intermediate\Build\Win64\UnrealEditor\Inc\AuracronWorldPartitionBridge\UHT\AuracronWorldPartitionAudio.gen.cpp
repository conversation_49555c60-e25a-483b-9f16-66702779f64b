// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronWorldPartitionBridge/Public/AuracronWorldPartitionAudio.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronWorldPartitionAudio() {}

// ********** Begin Cross Module References ********************************************************
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager();
AURACRONWORLDPARTITIONBRIDGE_API UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState();
AURACRONWORLDPARTITIONBRIDGE_API UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioDescriptor();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioStatistics();
AURACRONWORLDPARTITIONBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronWorldPartitionBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronAudioStreamingState **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAudioStreamingState;
static UEnum* EAuracronAudioStreamingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioStreamingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAudioStreamingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronAudioStreamingState"));
	}
	return Z_Registration_Info_UEnum_EAuracronAudioStreamingState.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioStreamingState>()
{
	return EAuracronAudioStreamingState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio streaming states\n" },
#endif
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EAuracronAudioStreamingState::Failed" },
		{ "Loaded.DisplayName", "Loaded" },
		{ "Loaded.Name", "EAuracronAudioStreamingState::Loaded" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EAuracronAudioStreamingState::Loading" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
		{ "Paused.DisplayName", "Paused" },
		{ "Paused.Name", "EAuracronAudioStreamingState::Paused" },
		{ "Playing.DisplayName", "Playing" },
		{ "Playing.Name", "EAuracronAudioStreamingState::Playing" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio streaming states" },
#endif
		{ "Unloaded.DisplayName", "Unloaded" },
		{ "Unloaded.Name", "EAuracronAudioStreamingState::Unloaded" },
		{ "Unloading.DisplayName", "Unloading" },
		{ "Unloading.Name", "EAuracronAudioStreamingState::Unloading" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAudioStreamingState::Unloaded", (int64)EAuracronAudioStreamingState::Unloaded },
		{ "EAuracronAudioStreamingState::Loading", (int64)EAuracronAudioStreamingState::Loading },
		{ "EAuracronAudioStreamingState::Loaded", (int64)EAuracronAudioStreamingState::Loaded },
		{ "EAuracronAudioStreamingState::Playing", (int64)EAuracronAudioStreamingState::Playing },
		{ "EAuracronAudioStreamingState::Paused", (int64)EAuracronAudioStreamingState::Paused },
		{ "EAuracronAudioStreamingState::Unloading", (int64)EAuracronAudioStreamingState::Unloading },
		{ "EAuracronAudioStreamingState::Failed", (int64)EAuracronAudioStreamingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronAudioStreamingState",
	"EAuracronAudioStreamingState",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioStreamingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAudioStreamingState.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAudioStreamingState.InnerSingleton;
}
// ********** End Enum EAuracronAudioStreamingState ************************************************

// ********** Begin Enum EAuracronAudioLODLevel ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAudioLODLevel;
static UEnum* EAuracronAudioLODLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioLODLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAudioLODLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronAudioLODLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronAudioLODLevel.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioLODLevel>()
{
	return EAuracronAudioLODLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio LOD levels\n" },
#endif
		{ "LOD0.DisplayName", "LOD 0 (Highest)" },
		{ "LOD0.Name", "EAuracronAudioLODLevel::LOD0" },
		{ "LOD1.DisplayName", "LOD 1" },
		{ "LOD1.Name", "EAuracronAudioLODLevel::LOD1" },
		{ "LOD2.DisplayName", "LOD 2" },
		{ "LOD2.Name", "EAuracronAudioLODLevel::LOD2" },
		{ "LOD3.DisplayName", "LOD 3" },
		{ "LOD3.Name", "EAuracronAudioLODLevel::LOD3" },
		{ "LOD4.DisplayName", "LOD 4 (Lowest)" },
		{ "LOD4.Name", "EAuracronAudioLODLevel::LOD4" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio LOD levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAudioLODLevel::LOD0", (int64)EAuracronAudioLODLevel::LOD0 },
		{ "EAuracronAudioLODLevel::LOD1", (int64)EAuracronAudioLODLevel::LOD1 },
		{ "EAuracronAudioLODLevel::LOD2", (int64)EAuracronAudioLODLevel::LOD2 },
		{ "EAuracronAudioLODLevel::LOD3", (int64)EAuracronAudioLODLevel::LOD3 },
		{ "EAuracronAudioLODLevel::LOD4", (int64)EAuracronAudioLODLevel::LOD4 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronAudioLODLevel",
	"EAuracronAudioLODLevel",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioLODLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAudioLODLevel.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAudioLODLevel.InnerSingleton;
}
// ********** End Enum EAuracronAudioLODLevel ******************************************************

// ********** Begin Enum EAuracronWorldPartitionAudioType ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType;
static UEnum* EAuracronWorldPartitionAudioType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronWorldPartitionAudioType"));
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronWorldPartitionAudioType>()
{
	return EAuracronWorldPartitionAudioType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ambient.DisplayName", "Ambient" },
		{ "Ambient.Name", "EAuracronWorldPartitionAudioType::Ambient" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio types\n" },
#endif
		{ "Foley.DisplayName", "Foley" },
		{ "Foley.Name", "EAuracronWorldPartitionAudioType::Foley" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
		{ "Music.DisplayName", "Music" },
		{ "Music.Name", "EAuracronWorldPartitionAudioType::Music" },
		{ "SFX.DisplayName", "Sound Effects" },
		{ "SFX.Name", "EAuracronWorldPartitionAudioType::SFX" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio types" },
#endif
		{ "UI.DisplayName", "User Interface" },
		{ "UI.Name", "EAuracronWorldPartitionAudioType::UI" },
		{ "Voice.DisplayName", "Voice" },
		{ "Voice.Name", "EAuracronWorldPartitionAudioType::Voice" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronWorldPartitionAudioType::Ambient", (int64)EAuracronWorldPartitionAudioType::Ambient },
		{ "EAuracronWorldPartitionAudioType::Music", (int64)EAuracronWorldPartitionAudioType::Music },
		{ "EAuracronWorldPartitionAudioType::SFX", (int64)EAuracronWorldPartitionAudioType::SFX },
		{ "EAuracronWorldPartitionAudioType::Voice", (int64)EAuracronWorldPartitionAudioType::Voice },
		{ "EAuracronWorldPartitionAudioType::UI", (int64)EAuracronWorldPartitionAudioType::UI },
		{ "EAuracronWorldPartitionAudioType::Foley", (int64)EAuracronWorldPartitionAudioType::Foley },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronWorldPartitionAudioType",
	"EAuracronWorldPartitionAudioType",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType()
{
	if (!Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType.InnerSingleton;
}
// ********** End Enum EAuracronWorldPartitionAudioType ********************************************

// ********** Begin Enum EAuracronAudioSpatialization **********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronAudioSpatialization;
static UEnum* EAuracronAudioSpatialization_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioSpatialization.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronAudioSpatialization.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("EAuracronAudioSpatialization"));
	}
	return Z_Registration_Info_UEnum_EAuracronAudioSpatialization.OuterSingleton;
}
template<> AURACRONWORLDPARTITIONBRIDGE_API UEnum* StaticEnum<EAuracronAudioSpatialization>()
{
	return EAuracronAudioSpatialization_StaticEnum();
}
struct Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Atmos.DisplayName", "Dolby Atmos" },
		{ "Atmos.Name", "EAuracronAudioSpatialization::Atmos" },
		{ "Binaural.DisplayName", "Binaural" },
		{ "Binaural.Name", "EAuracronAudioSpatialization::Binaural" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio spatialization types\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
		{ "None.DisplayName", "None (2D)" },
		{ "None.Name", "EAuracronAudioSpatialization::None" },
		{ "Simple.DisplayName", "Simple 3D" },
		{ "Simple.Name", "EAuracronAudioSpatialization::Simple" },
		{ "Surround.DisplayName", "Surround" },
		{ "Surround.Name", "EAuracronAudioSpatialization::Surround" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio spatialization types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronAudioSpatialization::None", (int64)EAuracronAudioSpatialization::None },
		{ "EAuracronAudioSpatialization::Simple", (int64)EAuracronAudioSpatialization::Simple },
		{ "EAuracronAudioSpatialization::Binaural", (int64)EAuracronAudioSpatialization::Binaural },
		{ "EAuracronAudioSpatialization::Surround", (int64)EAuracronAudioSpatialization::Surround },
		{ "EAuracronAudioSpatialization::Atmos", (int64)EAuracronAudioSpatialization::Atmos },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	"EAuracronAudioSpatialization",
	"EAuracronAudioSpatialization",
	Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization()
{
	if (!Z_Registration_Info_UEnum_EAuracronAudioSpatialization.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronAudioSpatialization.InnerSingleton, Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronAudioSpatialization.InnerSingleton;
}
// ********** End Enum EAuracronAudioSpatialization ************************************************

// ********** Begin ScriptStruct FAuracronWorldPartitionAudioConfiguration *************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration;
class UScriptStruct* FAuracronWorldPartitionAudioConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronWorldPartitionAudioConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Audio Configuration\n * Configuration settings for audio streaming in world partition\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio Configuration\nConfiguration settings for audio streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioStreaming_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioLOD_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnable3DAudio_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableMetaSounds_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioStreamingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioUnloadingDistance_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentAudioSources_MetaData[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseLODDistance_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODDistanceMultiplier_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasterVolume_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientVolume_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicVolume_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SFXVolume_MetaData[] = {
		{ "Category", "Quality" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAudioMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioCaching_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioCulling_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultSpatialization_MetaData[] = {
		{ "Category", "Spatialization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAudibleDistance_MetaData[] = {
		{ "Category", "Spatialization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FalloffDistance_MetaData[] = {
		{ "Category", "Spatialization" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAudioDebug_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogAudioOperations_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAudioStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioStreaming;
	static void NewProp_bEnableAudioLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioLOD;
	static void NewProp_bEnable3DAudio_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable3DAudio;
	static void NewProp_bEnableMetaSounds_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMetaSounds;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioStreamingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioUnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentAudioSources;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseLODDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODDistanceMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MasterVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MusicVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SFXVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAudioMemoryUsageMB;
	static void NewProp_bEnableAudioCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioCaching;
	static void NewProp_bEnableAudioCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioCulling;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultSpatialization_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultSpatialization;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAudibleDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FalloffDistance;
	static void NewProp_bEnableAudioDebug_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAudioDebug;
	static void NewProp_bLogAudioOperations_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogAudioOperations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronWorldPartitionAudioConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioStreaming_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableAudioStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioStreaming = { "bEnableAudioStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioStreaming_MetaData), NewProp_bEnableAudioStreaming_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioLOD_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableAudioLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioLOD = { "bEnableAudioLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioLOD_MetaData), NewProp_bEnableAudioLOD_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnable3DAudio_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnable3DAudio = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnable3DAudio = { "bEnable3DAudio", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnable3DAudio_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnable3DAudio_MetaData), NewProp_bEnable3DAudio_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableMetaSounds_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableMetaSounds = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableMetaSounds = { "bEnableMetaSounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableMetaSounds_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableMetaSounds_MetaData), NewProp_bEnableMetaSounds_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AudioStreamingDistance = { "AudioStreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, AudioStreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioStreamingDistance_MetaData), NewProp_AudioStreamingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AudioUnloadingDistance = { "AudioUnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, AudioUnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioUnloadingDistance_MetaData), NewProp_AudioUnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxConcurrentAudioSources = { "MaxConcurrentAudioSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MaxConcurrentAudioSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentAudioSources_MetaData), NewProp_MaxConcurrentAudioSources_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_BaseLODDistance = { "BaseLODDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, BaseLODDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseLODDistance_MetaData), NewProp_BaseLODDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_LODDistanceMultiplier = { "LODDistanceMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, LODDistanceMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODDistanceMultiplier_MetaData), NewProp_LODDistanceMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxLODLevel = { "MaxLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MaxLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODLevel_MetaData), NewProp_MaxLODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MasterVolume = { "MasterVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MasterVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasterVolume_MetaData), NewProp_MasterVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AmbientVolume = { "AmbientVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, AmbientVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientVolume_MetaData), NewProp_AmbientVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MusicVolume = { "MusicVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MusicVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicVolume_MetaData), NewProp_MusicVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_SFXVolume = { "SFXVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, SFXVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SFXVolume_MetaData), NewProp_SFXVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxAudioMemoryUsageMB = { "MaxAudioMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MaxAudioMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAudioMemoryUsageMB_MetaData), NewProp_MaxAudioMemoryUsageMB_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCaching_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableAudioCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCaching = { "bEnableAudioCaching", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioCaching_MetaData), NewProp_bEnableAudioCaching_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCulling_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableAudioCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCulling = { "bEnableAudioCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioCulling_MetaData), NewProp_bEnableAudioCulling_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_DefaultSpatialization_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_DefaultSpatialization = { "DefaultSpatialization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, DefaultSpatialization), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultSpatialization_MetaData), NewProp_DefaultSpatialization_MetaData) }; // 3554700511
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxAudibleDistance = { "MaxAudibleDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, MaxAudibleDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAudibleDistance_MetaData), NewProp_MaxAudibleDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_FalloffDistance = { "FalloffDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronWorldPartitionAudioConfiguration, FalloffDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FalloffDistance_MetaData), NewProp_FalloffDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioDebug_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bEnableAudioDebug = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioDebug = { "bEnableAudioDebug", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioDebug_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAudioDebug_MetaData), NewProp_bEnableAudioDebug_MetaData) };
void Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bLogAudioOperations_SetBit(void* Obj)
{
	((FAuracronWorldPartitionAudioConfiguration*)Obj)->bLogAudioOperations = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bLogAudioOperations = { "bLogAudioOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronWorldPartitionAudioConfiguration), &Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bLogAudioOperations_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogAudioOperations_MetaData), NewProp_bLogAudioOperations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnable3DAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableMetaSounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AudioStreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AudioUnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxConcurrentAudioSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_BaseLODDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_LODDistanceMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MasterVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_AmbientVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MusicVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_SFXVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxAudioMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioCulling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_DefaultSpatialization_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_DefaultSpatialization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_MaxAudibleDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_FalloffDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bEnableAudioDebug,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewProp_bLogAudioOperations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronWorldPartitionAudioConfiguration",
	Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::PropPointers),
	sizeof(FAuracronWorldPartitionAudioConfiguration),
	alignof(FAuracronWorldPartitionAudioConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronWorldPartitionAudioConfiguration ***************************

// ********** Begin ScriptStruct FAuracronAudioDescriptor ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor;
class UScriptStruct* FAuracronAudioDescriptor::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAudioDescriptor, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronAudioDescriptor"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Audio Descriptor\n * Descriptor for audio sources and their properties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio Descriptor\nDescriptor for audio sources and their properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioName_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoundAssetPath_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioType_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Spatialization_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingState_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Volume_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Pitch_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAudibleDistance_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FalloffDistance_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLooping_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoPlay_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIs3D_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreationTime_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "Audio Descriptor" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SoundAssetPath;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AudioType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AudioType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Spatialization_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Spatialization;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StreamingState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StreamingState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Pitch;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAudibleDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FalloffDistance;
	static void NewProp_bIsLooping_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLooping;
	static void NewProp_bAutoPlay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoPlay;
	static void NewProp_bIs3D_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIs3D;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CreationTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAudioDescriptor>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioName = { "AudioName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, AudioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioName_MetaData), NewProp_AudioName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_SoundAssetPath = { "SoundAssetPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, SoundAssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoundAssetPath_MetaData), NewProp_SoundAssetPath_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioType = { "AudioType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, AudioType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioType_MetaData), NewProp_AudioType_MetaData) }; // 3875613457
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Spatialization_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Spatialization = { "Spatialization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, Spatialization), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioSpatialization, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Spatialization_MetaData), NewProp_Spatialization_MetaData) }; // 3554700511
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_StreamingState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_StreamingState = { "StreamingState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, StreamingState), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingState_MetaData), NewProp_StreamingState_MetaData) }; // 1065821441
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CurrentLODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, CurrentLODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) }; // 769740428
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, Volume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Volume_MetaData), NewProp_Volume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Pitch = { "Pitch", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, Pitch), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Pitch_MetaData), NewProp_Pitch_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_MaxAudibleDistance = { "MaxAudibleDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, MaxAudibleDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAudibleDistance_MetaData), NewProp_MaxAudibleDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_FalloffDistance = { "FalloffDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, FalloffDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FalloffDistance_MetaData), NewProp_FalloffDistance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsLooping_SetBit(void* Obj)
{
	((FAuracronAudioDescriptor*)Obj)->bIsLooping = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsLooping = { "bIsLooping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioDescriptor), &Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsLooping_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLooping_MetaData), NewProp_bIsLooping_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bAutoPlay_SetBit(void* Obj)
{
	((FAuracronAudioDescriptor*)Obj)->bAutoPlay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bAutoPlay = { "bAutoPlay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioDescriptor), &Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bAutoPlay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoPlay_MetaData), NewProp_bAutoPlay_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIs3D_SetBit(void* Obj)
{
	((FAuracronAudioDescriptor*)Obj)->bIs3D = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIs3D = { "bIs3D", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioDescriptor), &Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIs3D_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIs3D_MetaData), NewProp_bIs3D_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((FAuracronAudioDescriptor*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAudioDescriptor), &Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CreationTime = { "CreationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, CreationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreationTime_MetaData), NewProp_CreationTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioDescriptor, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_SoundAssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_AudioType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Spatialization_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Spatialization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_StreamingState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_StreamingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CurrentLODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Volume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_Pitch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_MaxAudibleDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_FalloffDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsLooping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bAutoPlay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIs3D,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_CreationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewProp_LastAccessTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronAudioDescriptor",
	Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::PropPointers),
	sizeof(FAuracronAudioDescriptor),
	alignof(FAuracronAudioDescriptor),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioDescriptor()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAudioDescriptor ********************************************

// ********** Begin ScriptStruct FAuracronAudioStatistics ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics;
class UScriptStruct* FAuracronAudioStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAudioStatistics, (UObject*)Z_Construct_UPackage__Script_AuracronWorldPartitionBridge(), TEXT("AuracronAudioStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Audio Statistics\n * Performance and usage statistics for audio system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio Statistics\nPerformance and usage statistics for audio system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAudioSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedAudioSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayingAudioSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingAudioSources_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalMemoryUsageMB_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadingTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODTransitions_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedOperations_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioEfficiency_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAudioSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedAudioSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayingAudioSources;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingAudioSources;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadingTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODTransitions;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FailedOperations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AudioEfficiency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAudioStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_TotalAudioSources = { "TotalAudioSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, TotalAudioSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAudioSources_MetaData), NewProp_TotalAudioSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LoadedAudioSources = { "LoadedAudioSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, LoadedAudioSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedAudioSources_MetaData), NewProp_LoadedAudioSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_PlayingAudioSources = { "PlayingAudioSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, PlayingAudioSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayingAudioSources_MetaData), NewProp_PlayingAudioSources_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_StreamingAudioSources = { "StreamingAudioSources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, StreamingAudioSources), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingAudioSources_MetaData), NewProp_StreamingAudioSources_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_TotalMemoryUsageMB = { "TotalMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, TotalMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalMemoryUsageMB_MetaData), NewProp_TotalMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_AverageLoadingTime = { "AverageLoadingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, AverageLoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadingTime_MetaData), NewProp_AverageLoadingTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LODTransitions = { "LODTransitions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, LODTransitions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODTransitions_MetaData), NewProp_LODTransitions_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_FailedOperations = { "FailedOperations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, FailedOperations), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedOperations_MetaData), NewProp_FailedOperations_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_AudioEfficiency = { "AudioEfficiency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, AudioEfficiency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioEfficiency_MetaData), NewProp_AudioEfficiency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAudioStatistics, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_TotalAudioSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LoadedAudioSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_PlayingAudioSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_StreamingAudioSources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_TotalMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_AverageLoadingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LODTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_FailedOperations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_AudioEfficiency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
	nullptr,
	&NewStructOps,
	"AuracronAudioStatistics",
	Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::PropPointers),
	sizeof(FAuracronAudioStatistics),
	alignof(FAuracronAudioStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAudioStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAudioStatistics ********************************************

// ********** Begin Delegate FOnAudioLoaded ********************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms
	{
		FString AudioId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms, AudioId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms), &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "OnAudioLoaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionAudioManager::FOnAudioLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLoaded, const FString& AudioId, bool bSuccess)
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms
	{
		FString AudioId;
		bool bSuccess;
	};
	AuracronWorldPartitionAudioManager_eventOnAudioLoaded_Parms Parms;
	Parms.AudioId=AudioId;
	Parms.bSuccess=bSuccess ? true : false;
	OnAudioLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioLoaded **********************************************************

// ********** Begin Delegate FOnAudioUnloaded ******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms
	{
		FString AudioId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms, AudioId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::NewProp_AudioId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "OnAudioUnloaded__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionAudioManager::FOnAudioUnloaded_DelegateWrapper(const FMulticastScriptDelegate& OnAudioUnloaded, const FString& AudioId)
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms
	{
		FString AudioId;
	};
	AuracronWorldPartitionAudioManager_eventOnAudioUnloaded_Parms Parms;
	Parms.AudioId=AudioId;
	OnAudioUnloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioUnloaded ********************************************************

// ********** Begin Delegate FOnAudioStarted *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms
	{
		FString AudioId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms, AudioId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::NewProp_AudioId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "OnAudioStarted__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionAudioManager::FOnAudioStarted_DelegateWrapper(const FMulticastScriptDelegate& OnAudioStarted, const FString& AudioId)
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms
	{
		FString AudioId;
	};
	AuracronWorldPartitionAudioManager_eventOnAudioStarted_Parms Parms;
	Parms.AudioId=AudioId;
	OnAudioStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioStarted *********************************************************

// ********** Begin Delegate FOnAudioStopped *******************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms
	{
		FString AudioId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms, AudioId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::NewProp_AudioId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "OnAudioStopped__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionAudioManager::FOnAudioStopped_DelegateWrapper(const FMulticastScriptDelegate& OnAudioStopped, const FString& AudioId)
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms
	{
		FString AudioId;
	};
	AuracronWorldPartitionAudioManager_eventOnAudioStopped_Parms Parms;
	Parms.AudioId=AudioId;
	OnAudioStopped.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioStopped *********************************************************

// ********** Begin Delegate FOnAudioLODChanged ****************************************************
struct Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms
	{
		FString AudioId;
		EAuracronAudioLODLevel NewLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLOD_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms, AudioId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_NewLOD = { "NewLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms, NewLOD), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, METADATA_PARAMS(0, nullptr) }; // 769740428
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_NewLOD_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::NewProp_NewLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "OnAudioLODChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronWorldPartitionAudioManager::FOnAudioLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAudioLODChanged, const FString& AudioId, EAuracronAudioLODLevel NewLOD)
{
	struct AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms
	{
		FString AudioId;
		EAuracronAudioLODLevel NewLOD;
	};
	AuracronWorldPartitionAudioManager_eventOnAudioLODChanged_Parms Parms;
	Parms.AudioId=AudioId;
	Parms.NewLOD=NewLOD;
	OnAudioLODChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnAudioLODChanged ******************************************************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function CalculateLODForDistance *****
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics
{
	struct AuracronWorldPartitionAudioManager_eventCalculateLODForDistance_Parms
	{
		float Distance;
		EAuracronAudioLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCalculateLODForDistance_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCalculateLODForDistance_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, METADATA_PARAMS(0, nullptr) }; // 769740428
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "CalculateLODForDistance", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::AuracronWorldPartitionAudioManager_eventCalculateLODForDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::AuracronWorldPartitionAudioManager_eventCalculateLODForDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execCalculateLODForDistance)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronAudioLODLevel*)Z_Param__Result=P_THIS->CalculateLODForDistance(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function CalculateLODForDistance *******

// ********** Begin Class UAuracronWorldPartitionAudioManager Function CreateAudioSource ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms
	{
		FString SoundAssetPath;
		FVector Location;
		EAuracronWorldPartitionAudioType AudioType;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio source creation and management\n" },
#endif
		{ "CPP_Default_AudioType", "Ambient" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio source creation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SoundAssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SoundAssetPath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AudioType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AudioType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_SoundAssetPath = { "SoundAssetPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms, SoundAssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SoundAssetPath_MetaData), NewProp_SoundAssetPath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_AudioType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_AudioType = { "AudioType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms, AudioType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType, METADATA_PARAMS(0, nullptr) }; // 3875613457
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_SoundAssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_AudioType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_AudioType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "CreateAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::AuracronWorldPartitionAudioManager_eventCreateAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execCreateAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SoundAssetPath);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_ENUM(EAuracronWorldPartitionAudioType,Z_Param_AudioType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->CreateAudioSource(Z_Param_SoundAssetPath,Z_Param_Out_Location,EAuracronWorldPartitionAudioType(Z_Param_AudioType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function CreateAudioSource *************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function DoesAudioSourceExist ********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics
{
	struct AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "DoesAudioSourceExist", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::AuracronWorldPartitionAudioManager_eventDoesAudioSourceExist_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execDoesAudioSourceExist)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesAudioSourceExist(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function DoesAudioSourceExist **********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function DrawDebugAudioInfo **********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics
{
	struct AuracronWorldPartitionAudioManager_eventDrawDebugAudioInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventDrawDebugAudioInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "DrawDebugAudioInfo", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::AuracronWorldPartitionAudioManager_eventDrawDebugAudioInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::AuracronWorldPartitionAudioManager_eventDrawDebugAudioInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execDrawDebugAudioInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugAudioInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function DrawDebugAudioInfo ************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function EnableAudioDebug ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics
{
	struct AuracronWorldPartitionAudioManager_eventEnableAudioDebug_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and utilities\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and utilities" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventEnableAudioDebug_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventEnableAudioDebug_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "EnableAudioDebug", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::AuracronWorldPartitionAudioManager_eventEnableAudioDebug_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::AuracronWorldPartitionAudioManager_eventEnableAudioDebug_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execEnableAudioDebug)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableAudioDebug(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function EnableAudioDebug **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAllAudioSources **********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAllAudioSources_Parms
	{
		TArray<FAuracronAudioDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAudioDescriptor, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAllAudioSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAllAudioSources", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetAllAudioSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetAllAudioSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAllAudioSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronAudioDescriptor>*)Z_Param__Result=P_THIS->GetAllAudioSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAllAudioSources ************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudibleAudioSources ******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudibleAudioSources_Parms
	{
		FVector ListenerLocation;
		TArray<FAuracronAudioDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ListenerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ListenerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ListenerLocation = { "ListenerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudibleAudioSources_Parms, ListenerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ListenerLocation_MetaData), NewProp_ListenerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAudioDescriptor, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudibleAudioSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ListenerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudibleAudioSources", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetAudibleAudioSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetAudibleAudioSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudibleAudioSources)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ListenerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronAudioDescriptor>*)Z_Param__Result=P_THIS->GetAudibleAudioSources(Z_Param_Out_ListenerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudibleAudioSources ********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioDescriptor **********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioDescriptor_Parms
	{
		FString AudioId;
		FAuracronAudioDescriptor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioDescriptor_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioDescriptor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAudioDescriptor, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioDescriptor", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::AuracronWorldPartitionAudioManager_eventGetAudioDescriptor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::AuracronWorldPartitionAudioManager_eventGetAudioDescriptor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioDescriptor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAudioDescriptor*)Z_Param__Result=P_THIS->GetAudioDescriptor(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioDescriptor ************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioIds *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioIds", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::AuracronWorldPartitionAudioManager_eventGetAudioIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::AuracronWorldPartitionAudioManager_eventGetAudioIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAudioIds();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioIds *******************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioLOD *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioLOD_Parms
	{
		FString AudioId;
		EAuracronAudioLODLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioLOD_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioLOD_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, METADATA_PARAMS(0, nullptr) }; // 769740428
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioLOD", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::AuracronWorldPartitionAudioManager_eventGetAudioLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::AuracronWorldPartitionAudioManager_eventGetAudioLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronAudioLODLevel*)Z_Param__Result=P_THIS->GetAudioLOD(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioLOD *******************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioSourceCell **********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioSourceCell_Parms
	{
		FString AudioId;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourceCell_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourceCell_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioSourceCell", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourceCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourceCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioSourceCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetAudioSourceCell(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioSourceCell ************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesByType *******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioSourcesByType_Parms
	{
		EAuracronWorldPartitionAudioType AudioType;
		TArray<FAuracronAudioDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AudioType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AudioType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_AudioType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_AudioType = { "AudioType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesByType_Parms, AudioType), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronWorldPartitionAudioType, METADATA_PARAMS(0, nullptr) }; // 3875613457
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAudioDescriptor, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_AudioType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_AudioType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioSourcesByType", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioSourcesByType)
{
	P_GET_ENUM(EAuracronWorldPartitionAudioType,Z_Param_AudioType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronAudioDescriptor>*)Z_Param__Result=P_THIS->GetAudioSourcesByType(EAuracronWorldPartitionAudioType(Z_Param_AudioType));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesByType *********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesInCell *******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioSourcesInCell_Parms
	{
		FString CellId;
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cell integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cell integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesInCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesInCell_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioSourcesInCell", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesInCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesInCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioSourcesInCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAudioSourcesInCell(Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesInCell *********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesInRadius *****
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronAudioDescriptor> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio queries\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio queries" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAudioDescriptor, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4090481949
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioSourcesInRadius", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::AuracronWorldPartitionAudioManager_eventGetAudioSourcesInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioSourcesInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronAudioDescriptor>*)Z_Param__Result=P_THIS->GetAudioSourcesInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioSourcesInRadius *******

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioStatistics **********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioStatistics_Parms
	{
		FAuracronAudioStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics and monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics and monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAudioStatistics, METADATA_PARAMS(0, nullptr) }; // 3977450782
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioStatistics", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::AuracronWorldPartitionAudioManager_eventGetAudioStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::AuracronWorldPartitionAudioManager_eventGetAudioStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAudioStatistics*)Z_Param__Result=P_THIS->GetAudioStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioStatistics ************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetAudioStreamingState ******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetAudioStreamingState_Parms
	{
		FString AudioId;
		EAuracronAudioStreamingState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioStreamingState_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetAudioStreamingState_Parms, ReturnValue), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioStreamingState, METADATA_PARAMS(0, nullptr) }; // 1065821441
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetAudioStreamingState", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::AuracronWorldPartitionAudioManager_eventGetAudioStreamingState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::AuracronWorldPartitionAudioManager_eventGetAudioStreamingState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetAudioStreamingState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronAudioStreamingState*)Z_Param__Result=P_THIS->GetAudioStreamingState(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetAudioStreamingState ********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetConfiguration_Parms
	{
		FAuracronWorldPartitionAudioConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, METADATA_PARAMS(0, nullptr) }; // 2054200090
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::AuracronWorldPartitionAudioManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::AuracronWorldPartitionAudioManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronWorldPartitionAudioConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetInstance *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetInstance_Parms
	{
		UAuracronWorldPartitionAudioManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::AuracronWorldPartitionAudioManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::AuracronWorldPartitionAudioManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronWorldPartitionAudioManager**)Z_Param__Result=UAuracronWorldPartitionAudioManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetInstance *******************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetLoadedAudioSourceCount ***
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetLoadedAudioSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetLoadedAudioSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetLoadedAudioSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetLoadedAudioSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetLoadedAudioSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetLoadedAudioSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetLoadedAudioSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetLoadedAudioSourceCount *****

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetLoadedAudioSources *******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetLoadedAudioSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetLoadedAudioSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetLoadedAudioSources", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetLoadedAudioSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetLoadedAudioSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetLoadedAudioSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetLoadedAudioSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetLoadedAudioSources *********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetPlayingAudioSourceCount **
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetPlayingAudioSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetPlayingAudioSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetPlayingAudioSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetPlayingAudioSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetPlayingAudioSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetPlayingAudioSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayingAudioSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetPlayingAudioSourceCount ****

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetPlayingAudioSources ******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetPlayingAudioSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetPlayingAudioSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetPlayingAudioSources", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetPlayingAudioSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetPlayingAudioSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetPlayingAudioSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPlayingAudioSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetPlayingAudioSources ********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetStreamingAudioSources ****
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetStreamingAudioSources_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetStreamingAudioSources_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetStreamingAudioSources", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetStreamingAudioSources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::AuracronWorldPartitionAudioManager_eventGetStreamingAudioSources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetStreamingAudioSources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetStreamingAudioSources();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetStreamingAudioSources ******

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetTotalAudioSourceCount ****
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetTotalAudioSourceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetTotalAudioSourceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetTotalAudioSourceCount", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetTotalAudioSourceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::AuracronWorldPartitionAudioManager_eventGetTotalAudioSourceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetTotalAudioSourceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalAudioSourceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetTotalAudioSourceCount ******

// ********** Begin Class UAuracronWorldPartitionAudioManager Function GetTotalMemoryUsage *********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics
{
	struct AuracronWorldPartitionAudioManager_eventGetTotalMemoryUsage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventGetTotalMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "GetTotalMemoryUsage", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionAudioManager_eventGetTotalMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::AuracronWorldPartitionAudioManager_eventGetTotalMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execGetTotalMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalMemoryUsage();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function GetTotalMemoryUsage ***********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function Initialize ******************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics
{
	struct AuracronWorldPartitionAudioManager_eventInitialize_Parms
	{
		FAuracronWorldPartitionAudioConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2054200090
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::AuracronWorldPartitionAudioManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::AuracronWorldPartitionAudioManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronWorldPartitionAudioConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function Initialize ********************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function IsAudioDebugEnabled *********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics
{
	struct AuracronWorldPartitionAudioManager_eventIsAudioDebugEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventIsAudioDebugEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventIsAudioDebugEnabled_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "IsAudioDebugEnabled", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::AuracronWorldPartitionAudioManager_eventIsAudioDebugEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::AuracronWorldPartitionAudioManager_eventIsAudioDebugEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execIsAudioDebugEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAudioDebugEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function IsAudioDebugEnabled ***********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function IsInitialized ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics
{
	struct AuracronWorldPartitionAudioManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::AuracronWorldPartitionAudioManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::AuracronWorldPartitionAudioManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function IsInitialized *****************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function LoadAudioSource *************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio streaming\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "LoadAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::AuracronWorldPartitionAudioManager_eventLoadAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execLoadAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function LoadAudioSource ***************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function LogAudioState ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "LogAudioState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execLogAudioState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogAudioState();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function LogAudioState *****************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function MoveAudioSourceToCell *******
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics
{
	struct AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms
	{
		FString AudioId;
		FString CellId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CellId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CellId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_CellId = { "CellId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms, CellId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CellId_MetaData), NewProp_CellId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_CellId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "MoveAudioSourceToCell", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::AuracronWorldPartitionAudioManager_eventMoveAudioSourceToCell_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execMoveAudioSourceToCell)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_PROPERTY(FStrProperty,Z_Param_CellId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveAudioSourceToCell(Z_Param_AudioId,Z_Param_CellId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function MoveAudioSourceToCell *********

// ********** Begin Class UAuracronWorldPartitionAudioManager Function PauseAudioSource ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "PauseAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::AuracronWorldPartitionAudioManager_eventPauseAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execPauseAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PauseAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function PauseAudioSource **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function PlayAudioSource *************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio playback\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio playback" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "PlayAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::AuracronWorldPartitionAudioManager_eventPlayAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execPlayAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function PlayAudioSource ***************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function RemoveAudioSource ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "RemoveAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::AuracronWorldPartitionAudioManager_eventRemoveAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execRemoveAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function RemoveAudioSource *************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function ResetStatistics *************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "ResetStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function ResetStatistics ***************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function ResumeAudioSource ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "ResumeAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::AuracronWorldPartitionAudioManager_eventResumeAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execResumeAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResumeAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function ResumeAudioSource *************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetAudioLocation ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms
	{
		FString AudioId;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetAudioLocation", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::AuracronWorldPartitionAudioManager_eventSetAudioLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetAudioLocation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetAudioLocation(Z_Param_AudioId,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetAudioLocation **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetAudioLOD *****************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms
	{
		FString AudioId;
		EAuracronAudioLODLevel LODLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LODLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LODLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_LODLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms, LODLevel), Z_Construct_UEnum_AuracronWorldPartitionBridge_EAuracronAudioLODLevel, METADATA_PARAMS(0, nullptr) }; // 769740428
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_LODLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetAudioLOD", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::AuracronWorldPartitionAudioManager_eventSetAudioLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetAudioLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_ENUM(EAuracronAudioLODLevel,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetAudioLOD(Z_Param_AudioId,EAuracronAudioLODLevel(Z_Param_LODLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetAudioLOD *******************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetAudioPitch ***************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms
	{
		FString AudioId;
		float Pitch;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Pitch;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_Pitch = { "Pitch", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms, Pitch), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_Pitch,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetAudioPitch", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::AuracronWorldPartitionAudioManager_eventSetAudioPitch_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetAudioPitch)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Pitch);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetAudioPitch(Z_Param_AudioId,Z_Param_Pitch);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetAudioPitch *****************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetAudioRotation ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms
	{
		FString AudioId;
		FRotator Rotation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetAudioRotation", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::AuracronWorldPartitionAudioManager_eventSetAudioRotation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetAudioRotation)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetAudioRotation(Z_Param_AudioId,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetAudioRotation **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetAudioVolume **************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms
	{
		FString AudioId;
		float Volume;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Audio properties\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms, Volume), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_Volume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetAudioVolume", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::AuracronWorldPartitionAudioManager_eventSetAudioVolume_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetAudioVolume)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Volume);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetAudioVolume(Z_Param_AudioId,Z_Param_Volume);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetAudioVolume ****************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function SetConfiguration ************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics
{
	struct AuracronWorldPartitionAudioManager_eventSetConfiguration_Parms
	{
		FAuracronWorldPartitionAudioConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2054200090
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::AuracronWorldPartitionAudioManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::AuracronWorldPartitionAudioManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronWorldPartitionAudioConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function SetConfiguration **************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function Shutdown ********************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function Shutdown **********************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function StopAudioSource *************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "StopAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::AuracronWorldPartitionAudioManager_eventStopAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execStopAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function StopAudioSource ***************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function Tick ************************
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics
{
	struct AuracronWorldPartitionAudioManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::AuracronWorldPartitionAudioManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::AuracronWorldPartitionAudioManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function Tick **************************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function UnloadAudioSource ***********
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics
{
	struct AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms
	{
		FString AudioId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AudioId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_AudioId = { "AudioId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms, AudioId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioId_MetaData), NewProp_AudioId_MetaData) };
void Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms), &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_AudioId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "UnloadAudioSource", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::AuracronWorldPartitionAudioManager_eventUnloadAudioSource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execUnloadAudioSource)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_AudioId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnloadAudioSource(Z_Param_AudioId);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function UnloadAudioSource *************

// ********** Begin Class UAuracronWorldPartitionAudioManager Function UpdateDistanceBasedLODs *****
struct Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics
{
	struct AuracronWorldPartitionAudioManager_eventUpdateDistanceBasedLODs_Parms
	{
		FVector ListenerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio Manager" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ListenerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ListenerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::NewProp_ListenerLocation = { "ListenerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronWorldPartitionAudioManager_eventUpdateDistanceBasedLODs_Parms, ListenerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ListenerLocation_MetaData), NewProp_ListenerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::NewProp_ListenerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronWorldPartitionAudioManager, nullptr, "UpdateDistanceBasedLODs", Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionAudioManager_eventUpdateDistanceBasedLODs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::AuracronWorldPartitionAudioManager_eventUpdateDistanceBasedLODs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronWorldPartitionAudioManager::execUpdateDistanceBasedLODs)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ListenerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLODs(Z_Param_Out_ListenerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronWorldPartitionAudioManager Function UpdateDistanceBasedLODs *******

// ********** Begin Class UAuracronWorldPartitionAudioManager **************************************
void UAuracronWorldPartitionAudioManager::StaticRegisterNativesUAuracronWorldPartitionAudioManager()
{
	UClass* Class = UAuracronWorldPartitionAudioManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateLODForDistance", &UAuracronWorldPartitionAudioManager::execCalculateLODForDistance },
		{ "CreateAudioSource", &UAuracronWorldPartitionAudioManager::execCreateAudioSource },
		{ "DoesAudioSourceExist", &UAuracronWorldPartitionAudioManager::execDoesAudioSourceExist },
		{ "DrawDebugAudioInfo", &UAuracronWorldPartitionAudioManager::execDrawDebugAudioInfo },
		{ "EnableAudioDebug", &UAuracronWorldPartitionAudioManager::execEnableAudioDebug },
		{ "GetAllAudioSources", &UAuracronWorldPartitionAudioManager::execGetAllAudioSources },
		{ "GetAudibleAudioSources", &UAuracronWorldPartitionAudioManager::execGetAudibleAudioSources },
		{ "GetAudioDescriptor", &UAuracronWorldPartitionAudioManager::execGetAudioDescriptor },
		{ "GetAudioIds", &UAuracronWorldPartitionAudioManager::execGetAudioIds },
		{ "GetAudioLOD", &UAuracronWorldPartitionAudioManager::execGetAudioLOD },
		{ "GetAudioSourceCell", &UAuracronWorldPartitionAudioManager::execGetAudioSourceCell },
		{ "GetAudioSourcesByType", &UAuracronWorldPartitionAudioManager::execGetAudioSourcesByType },
		{ "GetAudioSourcesInCell", &UAuracronWorldPartitionAudioManager::execGetAudioSourcesInCell },
		{ "GetAudioSourcesInRadius", &UAuracronWorldPartitionAudioManager::execGetAudioSourcesInRadius },
		{ "GetAudioStatistics", &UAuracronWorldPartitionAudioManager::execGetAudioStatistics },
		{ "GetAudioStreamingState", &UAuracronWorldPartitionAudioManager::execGetAudioStreamingState },
		{ "GetConfiguration", &UAuracronWorldPartitionAudioManager::execGetConfiguration },
		{ "GetInstance", &UAuracronWorldPartitionAudioManager::execGetInstance },
		{ "GetLoadedAudioSourceCount", &UAuracronWorldPartitionAudioManager::execGetLoadedAudioSourceCount },
		{ "GetLoadedAudioSources", &UAuracronWorldPartitionAudioManager::execGetLoadedAudioSources },
		{ "GetPlayingAudioSourceCount", &UAuracronWorldPartitionAudioManager::execGetPlayingAudioSourceCount },
		{ "GetPlayingAudioSources", &UAuracronWorldPartitionAudioManager::execGetPlayingAudioSources },
		{ "GetStreamingAudioSources", &UAuracronWorldPartitionAudioManager::execGetStreamingAudioSources },
		{ "GetTotalAudioSourceCount", &UAuracronWorldPartitionAudioManager::execGetTotalAudioSourceCount },
		{ "GetTotalMemoryUsage", &UAuracronWorldPartitionAudioManager::execGetTotalMemoryUsage },
		{ "Initialize", &UAuracronWorldPartitionAudioManager::execInitialize },
		{ "IsAudioDebugEnabled", &UAuracronWorldPartitionAudioManager::execIsAudioDebugEnabled },
		{ "IsInitialized", &UAuracronWorldPartitionAudioManager::execIsInitialized },
		{ "LoadAudioSource", &UAuracronWorldPartitionAudioManager::execLoadAudioSource },
		{ "LogAudioState", &UAuracronWorldPartitionAudioManager::execLogAudioState },
		{ "MoveAudioSourceToCell", &UAuracronWorldPartitionAudioManager::execMoveAudioSourceToCell },
		{ "PauseAudioSource", &UAuracronWorldPartitionAudioManager::execPauseAudioSource },
		{ "PlayAudioSource", &UAuracronWorldPartitionAudioManager::execPlayAudioSource },
		{ "RemoveAudioSource", &UAuracronWorldPartitionAudioManager::execRemoveAudioSource },
		{ "ResetStatistics", &UAuracronWorldPartitionAudioManager::execResetStatistics },
		{ "ResumeAudioSource", &UAuracronWorldPartitionAudioManager::execResumeAudioSource },
		{ "SetAudioLocation", &UAuracronWorldPartitionAudioManager::execSetAudioLocation },
		{ "SetAudioLOD", &UAuracronWorldPartitionAudioManager::execSetAudioLOD },
		{ "SetAudioPitch", &UAuracronWorldPartitionAudioManager::execSetAudioPitch },
		{ "SetAudioRotation", &UAuracronWorldPartitionAudioManager::execSetAudioRotation },
		{ "SetAudioVolume", &UAuracronWorldPartitionAudioManager::execSetAudioVolume },
		{ "SetConfiguration", &UAuracronWorldPartitionAudioManager::execSetConfiguration },
		{ "Shutdown", &UAuracronWorldPartitionAudioManager::execShutdown },
		{ "StopAudioSource", &UAuracronWorldPartitionAudioManager::execStopAudioSource },
		{ "Tick", &UAuracronWorldPartitionAudioManager::execTick },
		{ "UnloadAudioSource", &UAuracronWorldPartitionAudioManager::execUnloadAudioSource },
		{ "UpdateDistanceBasedLODs", &UAuracronWorldPartitionAudioManager::execUpdateDistanceBasedLODs },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager;
UClass* UAuracronWorldPartitionAudioManager::GetPrivateStaticClass()
{
	using TClass = UAuracronWorldPartitionAudioManager;
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronWorldPartitionAudioManager"),
			Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.InnerSingleton,
			StaticRegisterNativesUAuracronWorldPartitionAudioManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager_NoRegister()
{
	return UAuracronWorldPartitionAudioManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * World Partition Audio Manager\n * Central manager for audio streaming in world partition\n */" },
#endif
		{ "IncludePath", "AuracronWorldPartitionAudio.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World Partition Audio Manager\nCentral manager for audio streaming in world partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioUnloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioStarted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioStopped_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAudioLODChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronWorldPartitionAudio.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioUnloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioStopped;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAudioLODChanged;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CalculateLODForDistance, "CalculateLODForDistance" }, // 2652147468
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_CreateAudioSource, "CreateAudioSource" }, // 85517522
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DoesAudioSourceExist, "DoesAudioSourceExist" }, // 693248708
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_DrawDebugAudioInfo, "DrawDebugAudioInfo" }, // 2264988369
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_EnableAudioDebug, "EnableAudioDebug" }, // 2669636840
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAllAudioSources, "GetAllAudioSources" }, // 3303735674
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudibleAudioSources, "GetAudibleAudioSources" }, // 3496486187
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioDescriptor, "GetAudioDescriptor" }, // 2839724551
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioIds, "GetAudioIds" }, // 1586080165
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioLOD, "GetAudioLOD" }, // 1576438018
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourceCell, "GetAudioSourceCell" }, // 2088781048
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesByType, "GetAudioSourcesByType" }, // 702967072
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInCell, "GetAudioSourcesInCell" }, // 3289960785
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioSourcesInRadius, "GetAudioSourcesInRadius" }, // 1419824725
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStatistics, "GetAudioStatistics" }, // 3760901689
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetAudioStreamingState, "GetAudioStreamingState" }, // 2133930185
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetConfiguration, "GetConfiguration" }, // 2408100410
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetInstance, "GetInstance" }, // 1893937391
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSourceCount, "GetLoadedAudioSourceCount" }, // 4115945811
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetLoadedAudioSources, "GetLoadedAudioSources" }, // 2972783376
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSourceCount, "GetPlayingAudioSourceCount" }, // 2640575649
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetPlayingAudioSources, "GetPlayingAudioSources" }, // 1716517074
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetStreamingAudioSources, "GetStreamingAudioSources" }, // 1425843631
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalAudioSourceCount, "GetTotalAudioSourceCount" }, // 3415394034
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_GetTotalMemoryUsage, "GetTotalMemoryUsage" }, // 1832486170
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Initialize, "Initialize" }, // 3880615480
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsAudioDebugEnabled, "IsAudioDebugEnabled" }, // 1744616934
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_IsInitialized, "IsInitialized" }, // 1061239742
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LoadAudioSource, "LoadAudioSource" }, // 313073899
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_LogAudioState, "LogAudioState" }, // 1668031078
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_MoveAudioSourceToCell, "MoveAudioSourceToCell" }, // 2762004198
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature, "OnAudioLoaded__DelegateSignature" }, // 1369871358
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature, "OnAudioLODChanged__DelegateSignature" }, // 1138922753
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature, "OnAudioStarted__DelegateSignature" }, // 1170552583
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature, "OnAudioStopped__DelegateSignature" }, // 2392944647
		{ &Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature, "OnAudioUnloaded__DelegateSignature" }, // 210179569
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PauseAudioSource, "PauseAudioSource" }, // 2399672637
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_PlayAudioSource, "PlayAudioSource" }, // 914432525
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_RemoveAudioSource, "RemoveAudioSource" }, // 2199896553
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResetStatistics, "ResetStatistics" }, // 3321718390
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_ResumeAudioSource, "ResumeAudioSource" }, // 2402982189
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLocation, "SetAudioLocation" }, // 3392039589
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioLOD, "SetAudioLOD" }, // 1787894055
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioPitch, "SetAudioPitch" }, // 2898716391
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioRotation, "SetAudioRotation" }, // 2771486408
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetAudioVolume, "SetAudioVolume" }, // 296544602
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_SetConfiguration, "SetConfiguration" }, // 192242307
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Shutdown, "Shutdown" }, // 2958988722
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_StopAudioSource, "StopAudioSource" }, // 431681658
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_Tick, "Tick" }, // 2253818776
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UnloadAudioSource, "UnloadAudioSource" }, // 1268273188
		{ &Z_Construct_UFunction_UAuracronWorldPartitionAudioManager_UpdateDistanceBasedLODs, "UpdateDistanceBasedLODs" }, // 1487595681
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronWorldPartitionAudioManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioLoaded = { "OnAudioLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, OnAudioLoaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioLoaded_MetaData), NewProp_OnAudioLoaded_MetaData) }; // 1369871358
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioUnloaded = { "OnAudioUnloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, OnAudioUnloaded), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioUnloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioUnloaded_MetaData), NewProp_OnAudioUnloaded_MetaData) }; // 210179569
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioStarted = { "OnAudioStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, OnAudioStarted), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioStarted_MetaData), NewProp_OnAudioStarted_MetaData) }; // 1170552583
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioStopped = { "OnAudioStopped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, OnAudioStopped), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioStopped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioStopped_MetaData), NewProp_OnAudioStopped_MetaData) }; // 2392944647
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioLODChanged = { "OnAudioLODChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, OnAudioLODChanged), Z_Construct_UDelegateFunction_UAuracronWorldPartitionAudioManager_OnAudioLODChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAudioLODChanged_MetaData), NewProp_OnAudioLODChanged_MetaData) }; // 1138922753
void Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronWorldPartitionAudioManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronWorldPartitionAudioManager), &Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, Configuration), Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 2054200090
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronWorldPartitionAudioManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioUnloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioStopped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_OnAudioLODChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronWorldPartitionBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::ClassParams = {
	&UAuracronWorldPartitionAudioManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronWorldPartitionAudioManager()
{
	if (!Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.OuterSingleton, Z_Construct_UClass_UAuracronWorldPartitionAudioManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager.OuterSingleton;
}
UAuracronWorldPartitionAudioManager::UAuracronWorldPartitionAudioManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronWorldPartitionAudioManager);
UAuracronWorldPartitionAudioManager::~UAuracronWorldPartitionAudioManager() {}
// ********** End Class UAuracronWorldPartitionAudioManager ****************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronAudioStreamingState_StaticEnum, TEXT("EAuracronAudioStreamingState"), &Z_Registration_Info_UEnum_EAuracronAudioStreamingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1065821441U) },
		{ EAuracronAudioLODLevel_StaticEnum, TEXT("EAuracronAudioLODLevel"), &Z_Registration_Info_UEnum_EAuracronAudioLODLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 769740428U) },
		{ EAuracronWorldPartitionAudioType_StaticEnum, TEXT("EAuracronWorldPartitionAudioType"), &Z_Registration_Info_UEnum_EAuracronWorldPartitionAudioType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3875613457U) },
		{ EAuracronAudioSpatialization_StaticEnum, TEXT("EAuracronAudioSpatialization"), &Z_Registration_Info_UEnum_EAuracronAudioSpatialization, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3554700511U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronWorldPartitionAudioConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronWorldPartitionAudioConfiguration_Statics::NewStructOps, TEXT("AuracronWorldPartitionAudioConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronWorldPartitionAudioConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronWorldPartitionAudioConfiguration), 2054200090U) },
		{ FAuracronAudioDescriptor::StaticStruct, Z_Construct_UScriptStruct_FAuracronAudioDescriptor_Statics::NewStructOps, TEXT("AuracronAudioDescriptor"), &Z_Registration_Info_UScriptStruct_FAuracronAudioDescriptor, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAudioDescriptor), 4090481949U) },
		{ FAuracronAudioStatistics::StaticStruct, Z_Construct_UScriptStruct_FAuracronAudioStatistics_Statics::NewStructOps, TEXT("AuracronAudioStatistics"), &Z_Registration_Info_UScriptStruct_FAuracronAudioStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAudioStatistics), 3977450782U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronWorldPartitionAudioManager, UAuracronWorldPartitionAudioManager::StaticClass, TEXT("UAuracronWorldPartitionAudioManager"), &Z_Registration_Info_UClass_UAuracronWorldPartitionAudioManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronWorldPartitionAudioManager), 3106331231U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_1824403780(TEXT("/Script/AuracronWorldPartitionBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronWorldPartitionBridge_Public_AuracronWorldPartitionAudio_h__Script_AuracronWorldPartitionBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
