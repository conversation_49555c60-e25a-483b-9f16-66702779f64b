// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCGBiomeCache.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodePCGBiomeCache() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UPCGBiomeCache();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UPCGBiomeCache_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPCGBiomeCacheEntry();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGGraph_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EPCGBiomeCacheState *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPCGBiomeCacheState;
static UEnum* EPCGBiomeCacheState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPCGBiomeCacheState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPCGBiomeCacheState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EPCGBiomeCacheState"));
	}
	return Z_Registration_Info_UEnum_EPCGBiomeCacheState.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EPCGBiomeCacheState>()
{
	return EPCGBiomeCacheState_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enum for PCG biome cache states\n */" },
#endif
		{ "Error.DisplayName", "Error" },
		{ "Error.Name", "EPCGBiomeCacheState::Error" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "EPCGBiomeCacheState::Loading" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
		{ "Ready.DisplayName", "Ready" },
		{ "Ready.Name", "EPCGBiomeCacheState::Ready" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enum for PCG biome cache states" },
#endif
		{ "Uninitialized.DisplayName", "Uninitialized" },
		{ "Uninitialized.Name", "EPCGBiomeCacheState::Uninitialized" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPCGBiomeCacheState::Uninitialized", (int64)EPCGBiomeCacheState::Uninitialized },
		{ "EPCGBiomeCacheState::Loading", (int64)EPCGBiomeCacheState::Loading },
		{ "EPCGBiomeCacheState::Ready", (int64)EPCGBiomeCacheState::Ready },
		{ "EPCGBiomeCacheState::Error", (int64)EPCGBiomeCacheState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EPCGBiomeCacheState",
	"EPCGBiomeCacheState",
	Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState()
{
	if (!Z_Registration_Info_UEnum_EPCGBiomeCacheState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPCGBiomeCacheState.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPCGBiomeCacheState.InnerSingleton;
}
// ********** End Enum EPCGBiomeCacheState *********************************************************

// ********** Begin ScriptStruct FPCGBiomeCacheEntry ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry;
class UScriptStruct* FPCGBiomeCacheEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPCGBiomeCacheEntry, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("PCGBiomeCacheEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Struct for PCG biome cache entry\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Struct for PCG biome cache entry" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGGraph_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponents_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheState_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastAccessTime_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "PCG Biome" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PCGGraph;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_PCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGComponents;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CacheState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CacheState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastAccessTime;
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPCGBiomeCacheEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGGraph = { "PCGGraph", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, PCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGGraph_MetaData), NewProp_PCGGraph_MetaData) };
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGComponents_Inner = { "PCGComponents", nullptr, (EPropertyFlags)0x0004000000080008, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGComponents = { "PCGComponents", nullptr, (EPropertyFlags)0x0014008000000009, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, PCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponents_MetaData), NewProp_PCGComponents_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_CacheState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_CacheState = { "CacheState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, CacheState), Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheState_MetaData), NewProp_CacheState_MetaData) }; // 2853839575
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, LastUpdateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_LastAccessTime = { "LastAccessTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPCGBiomeCacheEntry, LastAccessTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastAccessTime_MetaData), NewProp_LastAccessTime_MetaData) };
void Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FPCGBiomeCacheEntry*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPCGBiomeCacheEntry), &Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_PCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_CacheState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_CacheState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_LastAccessTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewProp_bIsValid,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"PCGBiomeCacheEntry",
	Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::PropPointers),
	sizeof(FPCGBiomeCacheEntry),
	alignof(FPCGBiomeCacheEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPCGBiomeCacheEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.InnerSingleton, Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry.InnerSingleton;
}
// ********** End ScriptStruct FPCGBiomeCacheEntry *************************************************

// ********** Begin Class UPCGBiomeCache Function AddBiomeEntry ************************************
struct Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics
{
	struct PCGBiomeCache_eventAddBiomeEntry_Parms
	{
		FString BiomeId;
		UPCGGraph* PCGGraph;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGGraph;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventAddBiomeEntry_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_PCGGraph = { "PCGGraph", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventAddBiomeEntry_Parms, PCGGraph), Z_Construct_UClass_UPCGGraph_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGBiomeCache_eventAddBiomeEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGBiomeCache_eventAddBiomeEntry_Parms), &Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_PCGGraph,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "AddBiomeEntry", Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PCGBiomeCache_eventAddBiomeEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::PCGBiomeCache_eventAddBiomeEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execAddBiomeEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_OBJECT(UPCGGraph,Z_Param_PCGGraph);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddBiomeEntry(Z_Param_BiomeId,Z_Param_PCGGraph);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function AddBiomeEntry **************************************

// ********** Begin Class UPCGBiomeCache Function ClearCache ***************************************
struct Z_Construct_UFunction_UPCGBiomeCache_ClearCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_ClearCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "ClearCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_ClearCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_ClearCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGBiomeCache_ClearCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_ClearCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execClearCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCache();
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function ClearCache *****************************************

// ********** Begin Class UPCGBiomeCache Function GetAllBiomeIds ***********************************
struct Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics
{
	struct PCGBiomeCache_eventGetAllBiomeIds_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetAllBiomeIds_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "GetAllBiomeIds", Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PCGBiomeCache_eventGetAllBiomeIds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::PCGBiomeCache_eventGetAllBiomeIds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execGetAllBiomeIds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetAllBiomeIds();
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function GetAllBiomeIds *************************************

// ********** Begin Class UPCGBiomeCache Function GetBiomeCacheState *******************************
struct Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics
{
	struct PCGBiomeCache_eventGetBiomeCacheState_Parms
	{
		FString BiomeId;
		EPCGBiomeCacheState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache State Management\n" },
#endif
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache State Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetBiomeCacheState_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetBiomeCacheState_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState, METADATA_PARAMS(0, nullptr) }; // 2853839575
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "GetBiomeCacheState", Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PCGBiomeCache_eventGetBiomeCacheState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::PCGBiomeCache_eventGetBiomeCacheState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execGetBiomeCacheState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EPCGBiomeCacheState*)Z_Param__Result=P_THIS->GetBiomeCacheState(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function GetBiomeCacheState *********************************

// ********** Begin Class UPCGBiomeCache Function GetBiomeEntry ************************************
struct Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics
{
	struct PCGBiomeCache_eventGetBiomeEntry_Parms
	{
		FString BiomeId;
		FPCGBiomeCacheEntry ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetBiomeEntry_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetBiomeEntry_Parms, ReturnValue), Z_Construct_UScriptStruct_FPCGBiomeCacheEntry, METADATA_PARAMS(0, nullptr) }; // 3252490556
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "GetBiomeEntry", Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PCGBiomeCache_eventGetBiomeEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::PCGBiomeCache_eventGetBiomeEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execGetBiomeEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPCGBiomeCacheEntry*)Z_Param__Result=P_THIS->GetBiomeEntry(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function GetBiomeEntry **************************************

// ********** Begin Class UPCGBiomeCache Function GetPCGComponents *********************************
struct Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics
{
	struct PCGBiomeCache_eventGetPCGComponents_Parms
	{
		FString BiomeId;
		TArray<UPCGComponent*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetPCGComponents_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000588, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventGetPCGComponents_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "GetPCGComponents", Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PCGBiomeCache_eventGetPCGComponents_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::PCGBiomeCache_eventGetPCGComponents_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execGetPCGComponents)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<UPCGComponent*>*)Z_Param__Result=P_THIS->GetPCGComponents(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function GetPCGComponents ***********************************

// ********** Begin Class UPCGBiomeCache Function InitializeCache **********************************
struct Z_Construct_UFunction_UPCGBiomeCache_InitializeCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache Management\n" },
#endif
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_InitializeCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "InitializeCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_InitializeCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_InitializeCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGBiomeCache_InitializeCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_InitializeCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execInitializeCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeCache();
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function InitializeCache ************************************

// ********** Begin Class UPCGBiomeCache Function RefreshCache *************************************
struct Z_Construct_UFunction_UPCGBiomeCache_RefreshCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_RefreshCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "RefreshCache", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RefreshCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_RefreshCache_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UPCGBiomeCache_RefreshCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_RefreshCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execRefreshCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RefreshCache();
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function RefreshCache ***************************************

// ********** Begin Class UPCGBiomeCache Function RegisterPCGComponent *****************************
struct Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics
{
	struct PCGBiomeCache_eventRegisterPCGComponent_Parms
	{
		FString BiomeId;
		UPCGComponent* PCGComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG Component Management\n" },
#endif
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG Component Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventRegisterPCGComponent_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventRegisterPCGComponent_Parms, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
void Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGBiomeCache_eventRegisterPCGComponent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGBiomeCache_eventRegisterPCGComponent_Parms), &Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "RegisterPCGComponent", Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PCGBiomeCache_eventRegisterPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::PCGBiomeCache_eventRegisterPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execRegisterPCGComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_OBJECT(UPCGComponent,Z_Param_PCGComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterPCGComponent(Z_Param_BiomeId,Z_Param_PCGComponent);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function RegisterPCGComponent *******************************

// ********** Begin Class UPCGBiomeCache Function RemoveBiomeEntry *********************************
struct Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics
{
	struct PCGBiomeCache_eventRemoveBiomeEntry_Parms
	{
		FString BiomeId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventRemoveBiomeEntry_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
void Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGBiomeCache_eventRemoveBiomeEntry_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGBiomeCache_eventRemoveBiomeEntry_Parms), &Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "RemoveBiomeEntry", Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PCGBiomeCache_eventRemoveBiomeEntry_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::PCGBiomeCache_eventRemoveBiomeEntry_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execRemoveBiomeEntry)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveBiomeEntry(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function RemoveBiomeEntry ***********************************

// ********** Begin Class UPCGBiomeCache Function SetBiomeCacheState *******************************
struct Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics
{
	struct PCGBiomeCache_eventSetBiomeCacheState_Parms
	{
		FString BiomeId;
		EPCGBiomeCacheState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventSetBiomeCacheState_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventSetBiomeCacheState_Parms, NewState), Z_Construct_UEnum_AuracronFoliageBridge_EPCGBiomeCacheState, METADATA_PARAMS(0, nullptr) }; // 2853839575
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "SetBiomeCacheState", Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PCGBiomeCache_eventSetBiomeCacheState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::PCGBiomeCache_eventSetBiomeCacheState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execSetBiomeCacheState)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_ENUM(EPCGBiomeCacheState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBiomeCacheState(Z_Param_BiomeId,EPCGBiomeCacheState(Z_Param_NewState));
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function SetBiomeCacheState *********************************

// ********** Begin Class UPCGBiomeCache Function UnregisterPCGComponent ***************************
struct Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics
{
	struct PCGBiomeCache_eventUnregisterPCGComponent_Parms
	{
		FString BiomeId;
		UPCGComponent* PCGComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventUnregisterPCGComponent_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PCGBiomeCache_eventUnregisterPCGComponent_Parms, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
void Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGBiomeCache_eventUnregisterPCGComponent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGBiomeCache_eventUnregisterPCGComponent_Parms), &Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "UnregisterPCGComponent", Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PCGBiomeCache_eventUnregisterPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::PCGBiomeCache_eventUnregisterPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execUnregisterPCGComponent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_OBJECT(UPCGComponent,Z_Param_PCGComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterPCGComponent(Z_Param_BiomeId,Z_Param_PCGComponent);
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function UnregisterPCGComponent *****************************

// ********** Begin Class UPCGBiomeCache Function ValidateCache ************************************
struct Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics
{
	struct PCGBiomeCache_eventValidateCache_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "PCG Biome Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache Validation\n" },
#endif
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache Validation" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PCGBiomeCache_eventValidateCache_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PCGBiomeCache_eventValidateCache_Parms), &Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UPCGBiomeCache, nullptr, "ValidateCache", Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PCGBiomeCache_eventValidateCache_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::PCGBiomeCache_eventValidateCache_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPCGBiomeCache_ValidateCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPCGBiomeCache_ValidateCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPCGBiomeCache::execValidateCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateCache();
	P_NATIVE_END;
}
// ********** End Class UPCGBiomeCache Function ValidateCache **************************************

// ********** Begin Class UPCGBiomeCache ***********************************************************
void UPCGBiomeCache::StaticRegisterNativesUPCGBiomeCache()
{
	UClass* Class = UPCGBiomeCache::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddBiomeEntry", &UPCGBiomeCache::execAddBiomeEntry },
		{ "ClearCache", &UPCGBiomeCache::execClearCache },
		{ "GetAllBiomeIds", &UPCGBiomeCache::execGetAllBiomeIds },
		{ "GetBiomeCacheState", &UPCGBiomeCache::execGetBiomeCacheState },
		{ "GetBiomeEntry", &UPCGBiomeCache::execGetBiomeEntry },
		{ "GetPCGComponents", &UPCGBiomeCache::execGetPCGComponents },
		{ "InitializeCache", &UPCGBiomeCache::execInitializeCache },
		{ "RefreshCache", &UPCGBiomeCache::execRefreshCache },
		{ "RegisterPCGComponent", &UPCGBiomeCache::execRegisterPCGComponent },
		{ "RemoveBiomeEntry", &UPCGBiomeCache::execRemoveBiomeEntry },
		{ "SetBiomeCacheState", &UPCGBiomeCache::execSetBiomeCacheState },
		{ "UnregisterPCGComponent", &UPCGBiomeCache::execUnregisterPCGComponent },
		{ "ValidateCache", &UPCGBiomeCache::execValidateCache },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UPCGBiomeCache;
UClass* UPCGBiomeCache::GetPrivateStaticClass()
{
	using TClass = UPCGBiomeCache;
	if (!Z_Registration_Info_UClass_UPCGBiomeCache.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("PCGBiomeCache"),
			Z_Registration_Info_UClass_UPCGBiomeCache.InnerSingleton,
			StaticRegisterNativesUPCGBiomeCache,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UPCGBiomeCache.InnerSingleton;
}
UClass* Z_Construct_UClass_UPCGBiomeCache_NoRegister()
{
	return UPCGBiomeCache::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UPCGBiomeCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * PCG Biome Cache Manager\n */" },
#endif
		{ "IncludePath", "PCGBiomeCache.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG Biome Cache Manager" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeCache_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCacheInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCGBiomeCache.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BiomeCache_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeCache_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BiomeCache;
	static void NewProp_bCacheInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCacheInitialized;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPCGBiomeCache_AddBiomeEntry, "AddBiomeEntry" }, // 606535846
		{ &Z_Construct_UFunction_UPCGBiomeCache_ClearCache, "ClearCache" }, // 1693909550
		{ &Z_Construct_UFunction_UPCGBiomeCache_GetAllBiomeIds, "GetAllBiomeIds" }, // 1393873308
		{ &Z_Construct_UFunction_UPCGBiomeCache_GetBiomeCacheState, "GetBiomeCacheState" }, // 3041110315
		{ &Z_Construct_UFunction_UPCGBiomeCache_GetBiomeEntry, "GetBiomeEntry" }, // 1083090810
		{ &Z_Construct_UFunction_UPCGBiomeCache_GetPCGComponents, "GetPCGComponents" }, // 738238524
		{ &Z_Construct_UFunction_UPCGBiomeCache_InitializeCache, "InitializeCache" }, // 34848752
		{ &Z_Construct_UFunction_UPCGBiomeCache_RefreshCache, "RefreshCache" }, // 3610399254
		{ &Z_Construct_UFunction_UPCGBiomeCache_RegisterPCGComponent, "RegisterPCGComponent" }, // 4028539596
		{ &Z_Construct_UFunction_UPCGBiomeCache_RemoveBiomeEntry, "RemoveBiomeEntry" }, // 1302682591
		{ &Z_Construct_UFunction_UPCGBiomeCache_SetBiomeCacheState, "SetBiomeCacheState" }, // 2894674932
		{ &Z_Construct_UFunction_UPCGBiomeCache_UnregisterPCGComponent, "UnregisterPCGComponent" }, // 1612039355
		{ &Z_Construct_UFunction_UPCGBiomeCache_ValidateCache, "ValidateCache" }, // 2746468980
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPCGBiomeCache>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache_ValueProp = { "BiomeCache", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPCGBiomeCacheEntry, METADATA_PARAMS(0, nullptr) }; // 3252490556
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache_Key_KeyProp = { "BiomeCache_Key", nullptr, (EPropertyFlags)0x0000008000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache = { "BiomeCache", nullptr, (EPropertyFlags)0x0020088000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPCGBiomeCache, BiomeCache), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeCache_MetaData), NewProp_BiomeCache_MetaData) }; // 3252490556
void Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_bCacheInitialized_SetBit(void* Obj)
{
	((UPCGBiomeCache*)Obj)->bCacheInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_bCacheInitialized = { "bCacheInitialized", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPCGBiomeCache), &Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_bCacheInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCacheInitialized_MetaData), NewProp_bCacheInitialized_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPCGBiomeCache_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_BiomeCache,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPCGBiomeCache_Statics::NewProp_bCacheInitialized,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGBiomeCache_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPCGBiomeCache_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGBiomeCache_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPCGBiomeCache_Statics::ClassParams = {
	&UPCGBiomeCache::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPCGBiomeCache_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPCGBiomeCache_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPCGBiomeCache_Statics::Class_MetaDataParams), Z_Construct_UClass_UPCGBiomeCache_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPCGBiomeCache()
{
	if (!Z_Registration_Info_UClass_UPCGBiomeCache.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPCGBiomeCache.OuterSingleton, Z_Construct_UClass_UPCGBiomeCache_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPCGBiomeCache.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPCGBiomeCache);
UPCGBiomeCache::~UPCGBiomeCache() {}
// ********** End Class UPCGBiomeCache *************************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPCGBiomeCacheState_StaticEnum, TEXT("EPCGBiomeCacheState"), &Z_Registration_Info_UEnum_EPCGBiomeCacheState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2853839575U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPCGBiomeCacheEntry::StaticStruct, Z_Construct_UScriptStruct_FPCGBiomeCacheEntry_Statics::NewStructOps, TEXT("PCGBiomeCacheEntry"), &Z_Registration_Info_UScriptStruct_FPCGBiomeCacheEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPCGBiomeCacheEntry), 3252490556U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPCGBiomeCache, UPCGBiomeCache::StaticClass, TEXT("UPCGBiomeCache"), &Z_Registration_Info_UClass_UPCGBiomeCache, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPCGBiomeCache), 2348997040U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_3890996419(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_PCGBiomeCache_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
