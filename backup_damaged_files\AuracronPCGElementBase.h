﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Base Element Class Header
// Bridge 2.1: PCG Framework - Core Infrastructure

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGContext.h"
#include "PCGData.h"
// UE 5.6 Compatible - PCGPointData.h path updated
#include "Data/PCGPointData.h"
#include "AuracronPCGFramework.h"

#include "AuracronPCGElementBase.generated.h"

// Forward declarations
class UPCGNode;
// UE 5.6 Compatible - FPCGContext is a struct, not class
struct FPCGContext;
struct FPCGElementExecutionContext;

// Element execution result
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGElementResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 OutputDataCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ExecutionTimeSeconds = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 PointsProcessed = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TMap<FString, FString> Metadata;

    FAuracronPCGElementResult()
    {
        bSuccess = false;
        ErrorMessage = TEXT("");
        OutputDataCount = 0;
        ExecutionTimeSeconds = 0.0f;
        PointsProcessed = 0;
    }
};

// Element execution parameters
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGElementParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, FString> StringParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, float> FloatParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, int32> IntParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, bool> BoolParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, FVector> VectorParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, FRotator> RotatorParameters;

    // Helper methods
    FString GetStringParameter(const FString& Key, const FString& DefaultValue = TEXT("")) const
    {
        if (const FString* Value = StringParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }

    float GetFloatParameter(const FString& Key, float DefaultValue = 0.0f) const
    {
        if (const float* Value = FloatParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }

    int32 GetIntParameter(const FString& Key, int32 DefaultValue = 0) const
    {
        if (const int32* Value = IntParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }

    bool GetBoolParameter(const FString& Key, bool DefaultValue = false) const
    {
        if (const bool* Value = BoolParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }

    FVector GetVectorParameter(const FString& Key, const FVector& DefaultValue = FVector::ZeroVector) const
    {
        if (const FVector* Value = VectorParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }

    FRotator GetRotatorParameter(const FString& Key, const FRotator& DefaultValue = FRotator::ZeroRotator) const
    {
        if (const FRotator* Value = RotatorParameters.Find(Key))
        {
            return *Value;
        }
        return DefaultValue;
    }
};

/**
 * Base settings class for AURACRON PCG elements
 * Provides common functionality and parameter management
 */

public:
    UAuracronPCGSettingsBase();

    // Element parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Element Parameters")
    FAuracronPCGElementParams ElementParameters;

    // bEnabled is inherited from UPCGSettingsInterface, no need to redeclare

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Element Settings")
    bool bDebugMode = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Element Settings")
    FString ElementName = TEXT("AuracronPCGElement");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Element Settings")
    FString ElementDescription = TEXT("Custom AURACRON PCG Element");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableMultithreading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 BatchSize = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TimeoutSeconds = 30.0f;

    // UPCGSettings interface
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;

#if WITH_EDITOR
    virtual FText GetDefaultNodeTitle() const override;
    virtual FText GetNodeTooltipText() const override;
    virtual FLinearColor GetNodeTitleColor() const override;
#endif

protected:
    // Override these in derived classes to customize pin configuration
    virtual TArray<FPCGPinProperties> GetCustomInputPinProperties() const;
    virtual TArray<FPCGPinProperties> GetCustomOutputPinProperties() const;
    virtual FPCGElementPtr CreateCustomElement() const;
};

/**
 * Base element class for AURACRON PCG elements
 * Provides common execution framework and utilities
 */
class AURACRONPCGBRIDGE_API FAuracronPCGElementBase : public IPCGElement
{
public:
    FAuracronPCGElementBase();
    virtual ~FAuracronPCGElementBase() = default;

    // IPCGElement interface
    virtual FPCGContext* Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node) override;
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
    virtual bool CanExecuteOnlyOnMainThread(FPCGContext* Context) const override;
    virtual bool IsCacheable(const UPCGSettings* InSettings) const override;

protected:
    // Core execution methods - override these in derived classes
    virtual FAuracronPCGElementResult ProcessData(const FPCGDataCollection& InputData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const;

    // Utility methods
    virtual bool ValidateInputData(const FPCGDataCollection& InputData, TArray<FString>& ValidationErrors) const;
    virtual void LogExecutionStart(FPCGContext* Context) const;
    virtual void LogExecutionEnd(FPCGContext* Context, const FAuracronPCGElementResult& Result) const;
    virtual void ReportError(FPCGContext* Context, const FString& ErrorMessage) const;
    virtual void ReportWarning(FPCGContext* Context, const FString& WarningMessage) const;

    // Data processing utilities
    virtual UPCGPointData* CreateOutputPointData(FPCGContext* Context) const;
    virtual bool ProcessPointData(const UPCGPointData* InputPointData, UPCGPointData* OutputPointData, const FAuracronPCGElementParams& Parameters) const;
    virtual bool ProcessSpatialData(const UPCGSpatialData* InputSpatialData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const;

    // Parameter extraction
    virtual FAuracronPCGElementParams ExtractParameters(const UPCGSettings* Settings) const;
    virtual FAuracronPCGElementParams ExtractParametersFromContext(FPCGContext* Context) const;

    // Performance monitoring
    virtual void StartPerformanceTimer() const;
    virtual float EndPerformanceTimer() const;

private:
    mutable double PerformanceStartTime = 0.0;
};

/**
 * Template base class for typed PCG elements
 * Provides type-safe parameter handling
 */
template<typename SettingsType>
class AURACRONPCGBRIDGE_API TAuracronPCGElementBase : public FAuracronPCGElementBase
{
    static_assert(TIsDerivedFrom<SettingsType, UAuracronPCGSettingsBase>::IsDerived, "SettingsType must derive from UAuracronPCGSettingsBase");

public:
    using SettingsClass = SettingsType;

protected:
    // Type-safe parameter extraction
    virtual FAuracronPCGElementParams ExtractParameters(const UPCGSettings* Settings) const override
    {
        if (const SettingsType* TypedSettings = Cast<SettingsType>(Settings))
        {
            return TypedSettings->ElementParameters;
        }
        return FAuracronPCGElementParams();
    }

    // Access to typed settings
    const SettingsType* GetTypedSettings(FPCGContext* Context) const
    {
        if (Context && Context->Node)
        {
            return Cast<SettingsType>(Context->Node->GetSettings());
        }
        return nullptr;
    }
};

// Convenience macros for element creation
#define AURACRON_PCG_ELEMENT_CLASS(ElementClass, SettingsClass) \
    class ElementClass : public TAuracronPCGElementBase<SettingsClass> \
    { \
    public: \
        ElementClass() = default; \
        virtual ~ElementClass() = default; \
    protected: \
        virtual FAuracronPCGElementResult ProcessData(const FPCGDataCollection& InputData, FPCGDataCollection& OutputData, const FAuracronPCGElementParams& Parameters) const override; \
    };

#define AURACRON_PCG_SETTINGS_CLASS(SettingsClass, ElementClass) \
    UCLASS(BlueprintType, Blueprintable) \
    class AURACRONPCGBRIDGE_API SettingsClass : public UAuracronPCGSettingsBase \
    { \
        GENERATED_BODY() \
    public: \
        SettingsClass() { ElementName = TEXT(#ElementClass); } \
    protected: \
        virtual FPCGElementPtr CreateCustomElement() const override \
        { \
            return MakeShared<ElementClass>(); \
        } \
    };

// Helper functions for common operations
namespace AuracronPCGUtils
{
    AURACRONPCGBRIDGE_API UPCGBasePointData* CreatePointDataFromBounds(const FBox& Bounds, float PointDensity = 1.0f);
    AURACRONPCGBRIDGE_API bool CopyPointAttributes(const UPCGBasePointData* Source, UPCGBasePointData* Target);
    AURACRONPCGBRIDGE_API TArray<FPCGPoint> FilterPointsByAttribute(const TArray<FPCGPoint>& Points, const FString& AttributeName, const FString& AttributeValue);
    AURACRONPCGBRIDGE_API void TransformPoints(TArray<FPCGPoint>& Points, const FTransform& Transform);
    AURACRONPCGBRIDGE_API FBox CalculatePointsBounds(const TArray<FPCGPoint>& Points);
}

// Performance monitoring macros
#define AURACRON_PCG_ELEMENT_PERFORMANCE_START() \
    StartPerformanceTimer()

#define AURACRON_PCG_ELEMENT_PERFORMANCE_END(Context, Operation) \
    do { \
        float Duration = EndPerformanceTimer(); \
        AURACRON_PCG_LOG_PERFORMANCE(Log, TEXT("Element '%s' operation '%s' took %.6f seconds"), \
            Context && Context->Node ? *Context->Node->GetNodeTitle().ToString() : TEXT("Unknown"), \
            TEXT(#Operation), Duration); \
    } while(0)
