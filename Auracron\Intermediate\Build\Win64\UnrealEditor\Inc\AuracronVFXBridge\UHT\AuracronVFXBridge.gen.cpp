// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronVFXBridge.h"
#include "GameplayTagContainer.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronVFXBridge() {}

// ********** Begin Cross Module References ********************************************************
AURACRONVFXBRIDGE_API UClass* Z_Construct_UClass_UAuracronVFXBridge();
AURACRONVFXBRIDGE_API UClass* Z_Construct_UClass_UAuracronVFXBridge_NoRegister();
AURACRONVFXBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality();
AURACRONVFXBRIDGE_API UEnum* Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType();
AURACRONVFXBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature();
AURACRONVFXBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature();
AURACRONVFXBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration();
AURACRONVFXBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronVFXConfiguration();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UParticleSystem_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
MODULARGAMEPLAY_API UClass* Z_Construct_UClass_UGameFrameworkComponent();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronVFXBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronVFXType **********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVFXType;
static UEnum* EAuracronVFXType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVFXType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVFXType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType, (UObject*)Z_Construct_UPackage__Script_AuracronVFXBridge(), TEXT("EAuracronVFXType"));
	}
	return Z_Registration_Info_UEnum_EAuracronVFXType.OuterSingleton;
}
template<> AURACRONVFXBRIDGE_API UEnum* StaticEnum<EAuracronVFXType>()
{
	return EAuracronVFXType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ability.DisplayName", "Ability Effect" },
		{ "Ability.Name", "EAuracronVFXType::Ability" },
		{ "Aura.DisplayName", "Aura Effect" },
		{ "Aura.Name", "EAuracronVFXType::Aura" },
		{ "BlueprintType", "true" },
		{ "Buff.DisplayName", "Buff Effect" },
		{ "Buff.Name", "EAuracronVFXType::Buff" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de efeitos visuais\n */" },
#endif
		{ "Death.DisplayName", "Death Effect" },
		{ "Death.Name", "EAuracronVFXType::Death" },
		{ "Debuff.DisplayName", "Debuff Effect" },
		{ "Debuff.Name", "EAuracronVFXType::Debuff" },
		{ "Destruction.DisplayName", "Destruction Effect" },
		{ "Destruction.Name", "EAuracronVFXType::Destruction" },
		{ "Environmental.DisplayName", "Environmental Effect" },
		{ "Environmental.Name", "EAuracronVFXType::Environmental" },
		{ "Healing.DisplayName", "Healing Effect" },
		{ "Healing.Name", "EAuracronVFXType::Healing" },
		{ "Impact.DisplayName", "Impact Effect" },
		{ "Impact.Name", "EAuracronVFXType::Impact" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronVFXType::None" },
		{ "Projectile.DisplayName", "Projectile Effect" },
		{ "Projectile.Name", "EAuracronVFXType::Projectile" },
		{ "Respawn.DisplayName", "Respawn Effect" },
		{ "Respawn.Name", "EAuracronVFXType::Respawn" },
		{ "Teleport.DisplayName", "Teleport Effect" },
		{ "Teleport.Name", "EAuracronVFXType::Teleport" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para tipos de efeitos visuais" },
#endif
		{ "Transition.DisplayName", "Transition Effect" },
		{ "Transition.Name", "EAuracronVFXType::Transition" },
		{ "UI.DisplayName", "UI Effect" },
		{ "UI.Name", "EAuracronVFXType::UI" },
		{ "Weather.DisplayName", "Weather Effect" },
		{ "Weather.Name", "EAuracronVFXType::Weather" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVFXType::None", (int64)EAuracronVFXType::None },
		{ "EAuracronVFXType::Ability", (int64)EAuracronVFXType::Ability },
		{ "EAuracronVFXType::Impact", (int64)EAuracronVFXType::Impact },
		{ "EAuracronVFXType::Projectile", (int64)EAuracronVFXType::Projectile },
		{ "EAuracronVFXType::Aura", (int64)EAuracronVFXType::Aura },
		{ "EAuracronVFXType::Buff", (int64)EAuracronVFXType::Buff },
		{ "EAuracronVFXType::Debuff", (int64)EAuracronVFXType::Debuff },
		{ "EAuracronVFXType::Healing", (int64)EAuracronVFXType::Healing },
		{ "EAuracronVFXType::Death", (int64)EAuracronVFXType::Death },
		{ "EAuracronVFXType::Respawn", (int64)EAuracronVFXType::Respawn },
		{ "EAuracronVFXType::Teleport", (int64)EAuracronVFXType::Teleport },
		{ "EAuracronVFXType::Environmental", (int64)EAuracronVFXType::Environmental },
		{ "EAuracronVFXType::Weather", (int64)EAuracronVFXType::Weather },
		{ "EAuracronVFXType::Destruction", (int64)EAuracronVFXType::Destruction },
		{ "EAuracronVFXType::UI", (int64)EAuracronVFXType::UI },
		{ "EAuracronVFXType::Transition", (int64)EAuracronVFXType::Transition },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVFXBridge,
	nullptr,
	"EAuracronVFXType",
	"EAuracronVFXType",
	Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType()
{
	if (!Z_Registration_Info_UEnum_EAuracronVFXType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVFXType.InnerSingleton, Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVFXType.InnerSingleton;
}
// ********** End Enum EAuracronVFXType ************************************************************

// ********** Begin Enum EAuracronVFXQuality *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronVFXQuality;
static UEnum* EAuracronVFXQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronVFXQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronVFXQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, (UObject*)Z_Construct_UPackage__Script_AuracronVFXBridge(), TEXT("EAuracronVFXQuality"));
	}
	return Z_Registration_Info_UEnum_EAuracronVFXQuality.OuterSingleton;
}
template<> AURACRONVFXBRIDGE_API UEnum* StaticEnum<EAuracronVFXQuality>()
{
	return EAuracronVFXQuality_StaticEnum();
}
struct Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic Quality" },
		{ "Cinematic.Name", "EAuracronVFXQuality::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de VFX\n */" },
#endif
		{ "High.DisplayName", "High Quality" },
		{ "High.Name", "EAuracronVFXQuality::High" },
		{ "Low.DisplayName", "Low Quality" },
		{ "Low.Name", "EAuracronVFXQuality::Low" },
		{ "Medium.DisplayName", "Medium Quality" },
		{ "Medium.Name", "EAuracronVFXQuality::Medium" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o para qualidade de VFX" },
#endif
		{ "Ultra.DisplayName", "Ultra Quality" },
		{ "Ultra.Name", "EAuracronVFXQuality::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronVFXQuality::Low", (int64)EAuracronVFXQuality::Low },
		{ "EAuracronVFXQuality::Medium", (int64)EAuracronVFXQuality::Medium },
		{ "EAuracronVFXQuality::High", (int64)EAuracronVFXQuality::High },
		{ "EAuracronVFXQuality::Ultra", (int64)EAuracronVFXQuality::Ultra },
		{ "EAuracronVFXQuality::Cinematic", (int64)EAuracronVFXQuality::Cinematic },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronVFXBridge,
	nullptr,
	"EAuracronVFXQuality",
	"EAuracronVFXQuality",
	Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality()
{
	if (!Z_Registration_Info_UEnum_EAuracronVFXQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronVFXQuality.InnerSingleton, Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronVFXQuality.InnerSingleton;
}
// ********** End Enum EAuracronVFXQuality *********************************************************

// ********** Begin ScriptStruct FAuracronVFXConfiguration *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration;
class UScriptStruct* FAuracronVFXConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronVFXConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronVFXBridge(), TEXT("AuracronVFXConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeito visual\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeito visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NiagaraSystem_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema Niagara do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema Niagara do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LegacyParticleSystem_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\x83\xc2\xad""culas legacy (fallback) */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\x83\xc2\xad""culas legacy (fallback)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXType_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "60.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Speed_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseComponentPooling_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar pooling de componentes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar pooling de componentes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bFollowActor_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Seguir ator */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seguir ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorToFollow_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator a seguir */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator a seguir" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttachSocket_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Socket para anexar */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Socket para anexar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionOffset_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Offset de posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset de posi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationOffset_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Offset de rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Offset de rota\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomParameters_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\x83\xc2\xa2metros customizados */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\x83\xc2\xa2metros customizados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\x83\xc2\xa2metros de vetor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\x83\xc2\xa2metros de vetor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorParameters_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Par\xc3\x83\xc2\xa2metros de cor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Par\xc3\x83\xc2\xa2metros de cor" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDistanceLOD_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar LOD baseado em dist\xc3\x83\xc2\xa2ncia */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar LOD baseado em dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRenderDistance_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "10000.0" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\x83\xc2\xa2ncia m\xc3\x83\xc2\xa1xima de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quality_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderPriority_MetaData[] = {
		{ "Category", "VFX Configuration" },
		{ "ClampMax", "10" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de renderiza\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXTags_MetaData[] = {
		{ "Category", "VFX Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tags do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags do efeito" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_NiagaraSystem;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LegacyParticleSystem;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VFXType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VFXType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static void NewProp_bUseComponentPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseComponentPooling;
	static void NewProp_bFollowActor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFollowActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorToFollow;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AttachSocket;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RotationOffset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CustomParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ColorParameters;
	static void NewProp_bUseDistanceLOD_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDistanceLOD;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRenderDistance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RenderPriority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VFXTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronVFXConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_NiagaraSystem = { "NiagaraSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, NiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NiagaraSystem_MetaData), NewProp_NiagaraSystem_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_LegacyParticleSystem = { "LegacyParticleSystem", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, LegacyParticleSystem), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LegacyParticleSystem_MetaData), NewProp_LegacyParticleSystem_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXType = { "VFXType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, VFXType), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXType_MetaData), NewProp_VFXType_MetaData) }; // 3334367885
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Scale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Speed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Speed_MetaData), NewProp_Speed_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseComponentPooling_SetBit(void* Obj)
{
	((FAuracronVFXConfiguration*)Obj)->bUseComponentPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseComponentPooling = { "bUseComponentPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVFXConfiguration), &Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseComponentPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseComponentPooling_MetaData), NewProp_bUseComponentPooling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bFollowActor_SetBit(void* Obj)
{
	((FAuracronVFXConfiguration*)Obj)->bFollowActor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bFollowActor = { "bFollowActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVFXConfiguration), &Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bFollowActor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bFollowActor_MetaData), NewProp_bFollowActor_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ActorToFollow = { "ActorToFollow", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, ActorToFollow), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorToFollow_MetaData), NewProp_ActorToFollow_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_AttachSocket = { "AttachSocket", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, AttachSocket), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttachSocket_MetaData), NewProp_AttachSocket_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_PositionOffset = { "PositionOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, PositionOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionOffset_MetaData), NewProp_PositionOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_RotationOffset = { "RotationOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, RotationOffset), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationOffset_MetaData), NewProp_RotationOffset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters_ValueProp = { "CustomParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters_Key_KeyProp = { "CustomParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters = { "CustomParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, CustomParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomParameters_MetaData), NewProp_CustomParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters_ValueProp = { "ColorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters_Key_KeyProp = { "ColorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters = { "ColorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, ColorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorParameters_MetaData), NewProp_ColorParameters_MetaData) };
void Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseDistanceLOD_SetBit(void* Obj)
{
	((FAuracronVFXConfiguration*)Obj)->bUseDistanceLOD = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseDistanceLOD = { "bUseDistanceLOD", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronVFXConfiguration), &Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseDistanceLOD_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDistanceLOD_MetaData), NewProp_bUseDistanceLOD_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_MaxRenderDistance = { "MaxRenderDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, MaxRenderDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRenderDistance_MetaData), NewProp_MaxRenderDistance_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, Quality), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quality_MetaData), NewProp_Quality_MetaData) }; // 2708674787
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_RenderPriority = { "RenderPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, RenderPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderPriority_MetaData), NewProp_RenderPriority_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXTags = { "VFXTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronVFXConfiguration, VFXTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXTags_MetaData), NewProp_VFXTags_MetaData) }; // 2104890724
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_NiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_LegacyParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Scale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Speed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseComponentPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bFollowActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ActorToFollow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_AttachSocket,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_PositionOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_RotationOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_CustomParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VectorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_ColorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_bUseDistanceLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_MaxRenderDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_RenderPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewProp_VFXTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVFXBridge,
	nullptr,
	&NewStructOps,
	"AuracronVFXConfiguration",
	Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::PropPointers),
	sizeof(FAuracronVFXConfiguration),
	alignof(FAuracronVFXConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronVFXConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronVFXConfiguration *******************************************

// ********** Begin ScriptStruct FAuracronRealmVFXConfiguration ************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration;
class UScriptStruct* FAuracronRealmVFXConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronVFXBridge(), TEXT("AuracronRealmVFXConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de realm\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceAmbientEffects_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos ambientes da Plan\xc3\x83\xc2\xad""cie */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos ambientes da Plan\xc3\x83\xc2\xad""cie" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyAmbientEffects_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos ambientes do Firmamento */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos ambientes do Firmamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UndergroundAmbientEffects_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos ambientes do Abismo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos ambientes do Abismo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmTransitionEffects_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre realms */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o entre realms" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalEffect_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos de portais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de portais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerticalConnectorEffect_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos de conectores verticais */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos de conectores verticais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientEffectDensity_MetaData[] = {
		{ "Category", "Realm VFX" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade de efeitos ambientes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade de efeitos ambientes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseWeatherEffects_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar efeitos de clima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar efeitos de clima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatherIntensity_MetaData[] = {
		{ "Category", "Realm VFX" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade dos efeitos de clima */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade dos efeitos de clima" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVolumetricFog_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Usar fog volum\xc3\x83\xc2\xa9trico */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Usar fog volum\xc3\x83\xc2\xa9trico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FogDensity_MetaData[] = {
		{ "Category", "Realm VFX" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Densidade do fog */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Densidade do fog" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmFogColors_MetaData[] = {
		{ "Category", "Realm VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do fog por realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do fog por realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SurfaceAmbientEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SurfaceAmbientEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SkyAmbientEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SkyAmbientEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UndergroundAmbientEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UndergroundAmbientEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RealmTransitionEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmTransitionEffects;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PortalEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_VerticalConnectorEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientEffectDensity;
	static void NewProp_bUseWeatherEffects_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseWeatherEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatherIntensity;
	static void NewProp_bUseVolumetricFog_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVolumetricFog;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FogDensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmFogColors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RealmFogColors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmVFXConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SurfaceAmbientEffects_Inner = { "SurfaceAmbientEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SurfaceAmbientEffects = { "SurfaceAmbientEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, SurfaceAmbientEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceAmbientEffects_MetaData), NewProp_SurfaceAmbientEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SkyAmbientEffects_Inner = { "SkyAmbientEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SkyAmbientEffects = { "SkyAmbientEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, SkyAmbientEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyAmbientEffects_MetaData), NewProp_SkyAmbientEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_UndergroundAmbientEffects_Inner = { "UndergroundAmbientEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_UndergroundAmbientEffects = { "UndergroundAmbientEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, UndergroundAmbientEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UndergroundAmbientEffects_MetaData), NewProp_UndergroundAmbientEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmTransitionEffects_Inner = { "RealmTransitionEffects", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmTransitionEffects = { "RealmTransitionEffects", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, RealmTransitionEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmTransitionEffects_MetaData), NewProp_RealmTransitionEffects_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_PortalEffect = { "PortalEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, PortalEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalEffect_MetaData), NewProp_PortalEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_VerticalConnectorEffect = { "VerticalConnectorEffect", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, VerticalConnectorEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerticalConnectorEffect_MetaData), NewProp_VerticalConnectorEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_AmbientEffectDensity = { "AmbientEffectDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, AmbientEffectDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientEffectDensity_MetaData), NewProp_AmbientEffectDensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseWeatherEffects_SetBit(void* Obj)
{
	((FAuracronRealmVFXConfiguration*)Obj)->bUseWeatherEffects = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseWeatherEffects = { "bUseWeatherEffects", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmVFXConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseWeatherEffects_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseWeatherEffects_MetaData), NewProp_bUseWeatherEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_WeatherIntensity = { "WeatherIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, WeatherIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatherIntensity_MetaData), NewProp_WeatherIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseVolumetricFog_SetBit(void* Obj)
{
	((FAuracronRealmVFXConfiguration*)Obj)->bUseVolumetricFog = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseVolumetricFog = { "bUseVolumetricFog", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmVFXConfiguration), &Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseVolumetricFog_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVolumetricFog_MetaData), NewProp_bUseVolumetricFog_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_FogDensity = { "FogDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, FogDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FogDensity_MetaData), NewProp_FogDensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmFogColors_Inner = { "RealmFogColors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmFogColors = { "RealmFogColors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmVFXConfiguration, RealmFogColors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmFogColors_MetaData), NewProp_RealmFogColors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SurfaceAmbientEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SurfaceAmbientEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SkyAmbientEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_SkyAmbientEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_UndergroundAmbientEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_UndergroundAmbientEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmTransitionEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmTransitionEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_PortalEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_VerticalConnectorEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_AmbientEffectDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseWeatherEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_WeatherIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_bUseVolumetricFog,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_FogDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmFogColors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewProp_RealmFogColors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVFXBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmVFXConfiguration",
	Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::PropPointers),
	sizeof(FAuracronRealmVFXConfiguration),
	alignof(FAuracronRealmVFXConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmVFXConfiguration **************************************

// ********** Begin Delegate FOnVFXSpawned *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics
{
	struct AuracronVFXBridge_eventOnVFXSpawned_Parms
	{
		UNiagaraComponent* EffectComponent;
		FAuracronVFXConfiguration VFXConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando efeito \xc3\x83\xc2\xa9 spawnado */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando efeito \xc3\x83\xc2\xa9 spawnado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VFXConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::NewProp_EffectComponent = { "EffectComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventOnVFXSpawned_Parms, EffectComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectComponent_MetaData), NewProp_EffectComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::NewProp_VFXConfig = { "VFXConfig", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventOnVFXSpawned_Parms, VFXConfig), Z_Construct_UScriptStruct_FAuracronVFXConfiguration, METADATA_PARAMS(0, nullptr) }; // 148941632
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::NewProp_EffectComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::NewProp_VFXConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "OnVFXSpawned__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::AuracronVFXBridge_eventOnVFXSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::AuracronVFXBridge_eventOnVFXSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVFXBridge::FOnVFXSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnVFXSpawned, UNiagaraComponent* EffectComponent, FAuracronVFXConfiguration VFXConfig)
{
	struct AuracronVFXBridge_eventOnVFXSpawned_Parms
	{
		UNiagaraComponent* EffectComponent;
		FAuracronVFXConfiguration VFXConfig;
	};
	AuracronVFXBridge_eventOnVFXSpawned_Parms Parms;
	Parms.EffectComponent=EffectComponent;
	Parms.VFXConfig=VFXConfig;
	OnVFXSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVFXSpawned ***********************************************************

// ********** Begin Delegate FOnVFXQualityChanged **************************************************
struct Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics
{
	struct AuracronVFXBridge_eventOnVFXQualityChanged_Parms
	{
		EAuracronVFXQuality OldQuality;
		EAuracronVFXQuality NewQuality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delegate chamado quando qualidade de VFX muda */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegate chamado quando qualidade de VFX muda" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldQuality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewQuality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_OldQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_OldQuality = { "OldQuality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventOnVFXQualityChanged_Parms, OldQuality), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, METADATA_PARAMS(0, nullptr) }; // 2708674787
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_NewQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_NewQuality = { "NewQuality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventOnVFXQualityChanged_Parms, NewQuality), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, METADATA_PARAMS(0, nullptr) }; // 2708674787
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_OldQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_OldQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_NewQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::NewProp_NewQuality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "OnVFXQualityChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::AuracronVFXBridge_eventOnVFXQualityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::AuracronVFXBridge_eventOnVFXQualityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronVFXBridge::FOnVFXQualityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVFXQualityChanged, EAuracronVFXQuality OldQuality, EAuracronVFXQuality NewQuality)
{
	struct AuracronVFXBridge_eventOnVFXQualityChanged_Parms
	{
		EAuracronVFXQuality OldQuality;
		EAuracronVFXQuality NewQuality;
	};
	AuracronVFXBridge_eventOnVFXQualityChanged_Parms Parms;
	Parms.OldQuality=OldQuality;
	Parms.NewQuality=NewQuality;
	OnVFXQualityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnVFXQualityChanged ****************************************************

// ********** Begin Class UAuracronVFXBridge Function ActivateRealmVFX *****************************
struct Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics
{
	struct AuracronVFXBridge_eventActivateRealmVFX_Parms
	{
		int32 RealmIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Ativar efeitos de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar efeitos de realm" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventActivateRealmVFX_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventActivateRealmVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventActivateRealmVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "ActivateRealmVFX", Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::AuracronVFXBridge_eventActivateRealmVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::AuracronVFXBridge_eventActivateRealmVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execActivateRealmVFX)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateRealmVFX(Z_Param_RealmIndex);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function ActivateRealmVFX *******************************

// ********** Begin Class UAuracronVFXBridge Function ApplyDissolveEffect **************************
struct Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics
{
	struct AuracronVFXBridge_eventApplyDissolveEffect_Parms
	{
		AActor* TargetActor;
		float DissolveTime;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplicar efeito de dissolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o\n     */" },
#endif
		{ "CPP_Default_DissolveTime", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeito de dissolu\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DissolveTime;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventApplyDissolveEffect_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_DissolveTime = { "DissolveTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventApplyDissolveEffect_Parms, DissolveTime), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventApplyDissolveEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventApplyDissolveEffect_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_DissolveTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "ApplyDissolveEffect", Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::AuracronVFXBridge_eventApplyDissolveEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::AuracronVFXBridge_eventApplyDissolveEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execApplyDissolveEffect)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DissolveTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyDissolveEffect(Z_Param_TargetActor,Z_Param_DissolveTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function ApplyDissolveEffect ****************************

// ********** Begin Class UAuracronVFXBridge Function CleanupInactiveVFX ***************************
struct Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics
{
	struct AuracronVFXBridge_eventCleanupInactiveVFX_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Limpar efeitos inativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar efeitos inativos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventCleanupInactiveVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventCleanupInactiveVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "CleanupInactiveVFX", Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::AuracronVFXBridge_eventCleanupInactiveVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::AuracronVFXBridge_eventCleanupInactiveVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execCleanupInactiveVFX)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CleanupInactiveVFX();
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function CleanupInactiveVFX *****************************

// ********** Begin Class UAuracronVFXBridge Function CreateDynamicMaterial ************************
struct Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics
{
	struct AuracronVFXBridge_eventCreateDynamicMaterial_Parms
	{
		UMaterialInterface* BaseMaterial;
		TMap<FString,float> Parameters;
		UMaterialInstanceDynamic* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Criar material din\xc3\x83\xc2\xa2mico\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar material din\xc3\x83\xc2\xa2mico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventCreateDynamicMaterial_Parms, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventCreateDynamicMaterial_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventCreateDynamicMaterial_Parms, ReturnValue), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "CreateDynamicMaterial", Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::AuracronVFXBridge_eventCreateDynamicMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::AuracronVFXBridge_eventCreateDynamicMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execCreateDynamicMaterial)
{
	P_GET_OBJECT(UMaterialInterface,Z_Param_BaseMaterial);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInstanceDynamic**)Z_Param__Result=P_THIS->CreateDynamicMaterial(Z_Param_BaseMaterial,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function CreateDynamicMaterial **************************

// ********** Begin Class UAuracronVFXBridge Function OnRep_VFXQuality *****************************
struct Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Replication Callbacks ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Replication Callbacks ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "OnRep_VFXQuality", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execOnRep_VFXQuality)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_VFXQuality();
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function OnRep_VFXQuality *******************************

// ********** Begin Class UAuracronVFXBridge Function OptimizeVFXByDistance ************************
struct Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics
{
	struct AuracronVFXBridge_eventOptimizeVFXByDistance_Parms
	{
		FVector ViewerLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Otimizar efeitos por dist\xc3\x83\xc2\xa2ncia\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Otimizar efeitos por dist\xc3\x83\xc2\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ViewerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ViewerLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ViewerLocation = { "ViewerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventOptimizeVFXByDistance_Parms, ViewerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ViewerLocation_MetaData), NewProp_ViewerLocation_MetaData) };
void Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventOptimizeVFXByDistance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventOptimizeVFXByDistance_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ViewerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "OptimizeVFXByDistance", Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::AuracronVFXBridge_eventOptimizeVFXByDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::AuracronVFXBridge_eventOptimizeVFXByDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execOptimizeVFXByDistance)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ViewerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->OptimizeVFXByDistance(Z_Param_Out_ViewerLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function OptimizeVFXByDistance **************************

// ********** Begin Class UAuracronVFXBridge Function PauseAllVFXEffects ***************************
struct Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics
{
	struct AuracronVFXBridge_eventPauseAllVFXEffects_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Pausar todos os efeitos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pausar todos os efeitos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPauseAllVFXEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPauseAllVFXEffects_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PauseAllVFXEffects", Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::AuracronVFXBridge_eventPauseAllVFXEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::AuracronVFXBridge_eventPauseAllVFXEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPauseAllVFXEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PauseAllVFXEffects();
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PauseAllVFXEffects *****************************

// ********** Begin Class UAuracronVFXBridge Function PlayAbilityVFX *******************************
struct Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics
{
	struct AuracronVFXBridge_eventPlayAbilityVFX_Parms
	{
		FString ChampionID;
		FString AbilitySlot;
		FVector Location;
		FVector TargetLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Abilities" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir efeito de habilidade\n     */" },
#endif
		{ "CPP_Default_TargetLocation", "" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir efeito de habilidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChampionID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilitySlot_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChampionID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AbilitySlot;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ChampionID = { "ChampionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayAbilityVFX_Parms, ChampionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChampionID_MetaData), NewProp_ChampionID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_AbilitySlot = { "AbilitySlot", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayAbilityVFX_Parms, AbilitySlot), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilitySlot_MetaData), NewProp_AbilitySlot_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayAbilityVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayAbilityVFX_Parms, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
void Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPlayAbilityVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPlayAbilityVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ChampionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_AbilitySlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PlayAbilityVFX", Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::AuracronVFXBridge_eventPlayAbilityVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::AuracronVFXBridge_eventPlayAbilityVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPlayAbilityVFX)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ChampionID);
	P_GET_PROPERTY(FStrProperty,Z_Param_AbilitySlot);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TargetLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayAbilityVFX(Z_Param_ChampionID,Z_Param_AbilitySlot,Z_Param_Out_Location,Z_Param_Out_TargetLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PlayAbilityVFX *********************************

// ********** Begin Class UAuracronVFXBridge Function PlayHealingVFX *******************************
struct Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics
{
	struct AuracronVFXBridge_eventPlayHealingVFX_Parms
	{
		AActor* TargetActor;
		float HealAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Healing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir efeito de cura\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir efeito de cura" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayHealingVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_HealAmount = { "HealAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayHealingVFX_Parms, HealAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPlayHealingVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPlayHealingVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_HealAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PlayHealingVFX", Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::AuracronVFXBridge_eventPlayHealingVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::AuracronVFXBridge_eventPlayHealingVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPlayHealingVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayHealingVFX(Z_Param_TargetActor,Z_Param_HealAmount);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PlayHealingVFX *********************************

// ********** Begin Class UAuracronVFXBridge Function PlayImpactVFX ********************************
struct Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics
{
	struct AuracronVFXBridge_eventPlayImpactVFX_Parms
	{
		FVector Location;
		FString ImpactType;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir efeito de impacto\n     */" },
#endif
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir efeito de impacto" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImpactType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayImpactVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ImpactType = { "ImpactType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayImpactVFX_Parms, ImpactType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactType_MetaData), NewProp_ImpactType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayImpactVFX_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPlayImpactVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPlayImpactVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ImpactType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PlayImpactVFX", Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::AuracronVFXBridge_eventPlayImpactVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::AuracronVFXBridge_eventPlayImpactVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPlayImpactVFX)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FStrProperty,Z_Param_ImpactType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayImpactVFX(Z_Param_Out_Location,Z_Param_ImpactType,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PlayImpactVFX **********************************

// ********** Begin Class UAuracronVFXBridge Function PlayRealmTransitionVFX ***********************
struct Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics
{
	struct AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms
	{
		int32 FromRealm;
		int32 ToRealm;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de realm\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir transi\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_FromRealm;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ToRealm;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_FromRealm = { "FromRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms, FromRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ToRealm = { "ToRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms, ToRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_FromRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ToRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PlayRealmTransitionVFX", Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::AuracronVFXBridge_eventPlayRealmTransitionVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPlayRealmTransitionVFX)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_FromRealm);
	P_GET_PROPERTY(FIntProperty,Z_Param_ToRealm);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayRealmTransitionVFX(Z_Param_FromRealm,Z_Param_ToRealm,Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PlayRealmTransitionVFX *************************

// ********** Begin Class UAuracronVFXBridge Function PlayStatusEffectVFX **************************
struct Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics
{
	struct AuracronVFXBridge_eventPlayStatusEffectVFX_Parms
	{
		AActor* TargetActor;
		FString EffectType;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|StatusEffects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Reproduzir efeito de buff/debuff\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reproduzir efeito de buff/debuff" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayStatusEffectVFX_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayStatusEffectVFX_Parms, EffectType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectType_MetaData), NewProp_EffectType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventPlayStatusEffectVFX_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventPlayStatusEffectVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventPlayStatusEffectVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "PlayStatusEffectVFX", Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::AuracronVFXBridge_eventPlayStatusEffectVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::AuracronVFXBridge_eventPlayStatusEffectVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execPlayStatusEffectVFX)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FStrProperty,Z_Param_EffectType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlayStatusEffectVFX(Z_Param_TargetActor,Z_Param_EffectType,Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function PlayStatusEffectVFX ****************************

// ********** Begin Class UAuracronVFXBridge Function ResumeAllVFXEffects **************************
struct Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics
{
	struct AuracronVFXBridge_eventResumeAllVFXEffects_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Retomar todos os efeitos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Retomar todos os efeitos" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventResumeAllVFXEffects_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventResumeAllVFXEffects_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "ResumeAllVFXEffects", Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::AuracronVFXBridge_eventResumeAllVFXEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::AuracronVFXBridge_eventResumeAllVFXEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execResumeAllVFXEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ResumeAllVFXEffects();
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function ResumeAllVFXEffects ****************************

// ********** Begin Class UAuracronVFXBridge Function SetVFXQuality ********************************
struct Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics
{
	struct AuracronVFXBridge_eventSetVFXQuality_Parms
	{
		EAuracronVFXQuality Quality;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Definir qualidade de VFX\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir qualidade de VFX" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Quality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Quality;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_Quality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_Quality = { "Quality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSetVFXQuality_Parms, Quality), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, METADATA_PARAMS(0, nullptr) }; // 2708674787
void Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventSetVFXQuality_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventSetVFXQuality_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_Quality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_Quality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "SetVFXQuality", Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::AuracronVFXBridge_eventSetVFXQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::AuracronVFXBridge_eventSetVFXQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execSetVFXQuality)
{
	P_GET_ENUM(EAuracronVFXQuality,Z_Param_Quality);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetVFXQuality(EAuracronVFXQuality(Z_Param_Quality));
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function SetVFXQuality **********************************

// ********** Begin Class UAuracronVFXBridge Function SpawnAttachedNiagaraEffect *******************
struct Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics
{
	struct AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms
	{
		FAuracronVFXConfiguration VFXConfig;
		AActor* AttachActor;
		FName SocketName;
		UNiagaraComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Niagara" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar efeito anexado\n     */" },
#endif
		{ "CPP_Default_SocketName", "None" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar efeito anexado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SocketName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VFXConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AttachActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_SocketName;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_VFXConfig = { "VFXConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms, VFXConfig), Z_Construct_UScriptStruct_FAuracronVFXConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXConfig_MetaData), NewProp_VFXConfig_MetaData) }; // 148941632
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_AttachActor = { "AttachActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms, AttachActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_SocketName = { "SocketName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms, SocketName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SocketName_MetaData), NewProp_SocketName_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms, ReturnValue), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_VFXConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_AttachActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_SocketName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "SpawnAttachedNiagaraEffect", Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::AuracronVFXBridge_eventSpawnAttachedNiagaraEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execSpawnAttachedNiagaraEffect)
{
	P_GET_STRUCT_REF(FAuracronVFXConfiguration,Z_Param_Out_VFXConfig);
	P_GET_OBJECT(AActor,Z_Param_AttachActor);
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_SocketName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UNiagaraComponent**)Z_Param__Result=P_THIS->SpawnAttachedNiagaraEffect(Z_Param_Out_VFXConfig,Z_Param_AttachActor,Z_Param_Out_SocketName);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function SpawnAttachedNiagaraEffect *********************

// ********** Begin Class UAuracronVFXBridge Function SpawnNiagaraEffect ***************************
struct Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics
{
	struct AuracronVFXBridge_eventSpawnNiagaraEffect_Parms
	{
		FAuracronVFXConfiguration VFXConfig;
		FVector Location;
		FRotator Rotation;
		UNiagaraComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Niagara" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar efeito Niagara\n     */" },
#endif
		{ "CPP_Default_Rotation", "" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar efeito Niagara" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_VFXConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_VFXConfig = { "VFXConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnNiagaraEffect_Parms, VFXConfig), Z_Construct_UScriptStruct_FAuracronVFXConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXConfig_MetaData), NewProp_VFXConfig_MetaData) }; // 148941632
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnNiagaraEffect_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnNiagaraEffect_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnNiagaraEffect_Parms, ReturnValue), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_VFXConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "SpawnNiagaraEffect", Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::AuracronVFXBridge_eventSpawnNiagaraEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::AuracronVFXBridge_eventSpawnNiagaraEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execSpawnNiagaraEffect)
{
	P_GET_STRUCT_REF(FAuracronVFXConfiguration,Z_Param_Out_VFXConfig);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UNiagaraComponent**)Z_Param__Result=P_THIS->SpawnNiagaraEffect(Z_Param_Out_VFXConfig,Z_Param_Out_Location,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function SpawnNiagaraEffect *****************************

// ********** Begin Class UAuracronVFXBridge Function SpawnPortalVFX *******************************
struct Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics
{
	struct AuracronVFXBridge_eventSpawnPortalVFX_Parms
	{
		FVector Location;
		int32 DestinationRealm;
		UNiagaraComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Spawnar portal visual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawnar portal visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DestinationRealm;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnPortalVFX_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_DestinationRealm = { "DestinationRealm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnPortalVFX_Parms, DestinationRealm), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventSpawnPortalVFX_Parms, ReturnValue), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_DestinationRealm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "SpawnPortalVFX", Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::AuracronVFXBridge_eventSpawnPortalVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::AuracronVFXBridge_eventSpawnPortalVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execSpawnPortalVFX)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_DestinationRealm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UNiagaraComponent**)Z_Param__Result=P_THIS->SpawnPortalVFX(Z_Param_Out_Location,Z_Param_DestinationRealm);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function SpawnPortalVFX *********************************

// ********** Begin Class UAuracronVFXBridge Function StopVFXEffect ********************************
struct Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics
{
	struct AuracronVFXBridge_eventStopVFXEffect_Parms
	{
		UNiagaraComponent* EffectComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Control" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Parar efeito\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_EffectComponent = { "EffectComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventStopVFXEffect_Parms, EffectComponent), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectComponent_MetaData), NewProp_EffectComponent_MetaData) };
void Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventStopVFXEffect_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventStopVFXEffect_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_EffectComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "StopVFXEffect", Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::AuracronVFXBridge_eventStopVFXEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::AuracronVFXBridge_eventStopVFXEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execStopVFXEffect)
{
	P_GET_OBJECT(UNiagaraComponent,Z_Param_EffectComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->StopVFXEffect(Z_Param_EffectComponent);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function StopVFXEffect **********************************

// ********** Begin Class UAuracronVFXBridge Function UpdateAmbientVFX *****************************
struct Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics
{
	struct AuracronVFXBridge_eventUpdateAmbientVFX_Parms
	{
		int32 RealmIndex;
		float Intensity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar efeitos ambientes\n     */" },
#endif
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar efeitos ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RealmIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_RealmIndex = { "RealmIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventUpdateAmbientVFX_Parms, RealmIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventUpdateAmbientVFX_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventUpdateAmbientVFX_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventUpdateAmbientVFX_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_RealmIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "UpdateAmbientVFX", Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::AuracronVFXBridge_eventUpdateAmbientVFX_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::AuracronVFXBridge_eventUpdateAmbientVFX_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execUpdateAmbientVFX)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_RealmIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateAmbientVFX(Z_Param_RealmIndex,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function UpdateAmbientVFX *******************************

// ********** Begin Class UAuracronVFXBridge Function UpdateMaterialParameters *********************
struct Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics
{
	struct AuracronVFXBridge_eventUpdateMaterialParameters_Parms
	{
		UMaterialInstanceDynamic* Material;
		TMap<FString,float> Parameters;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "AURACRON VFX|Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Atualizar par\xc3\x83\xc2\xa2metros de material\n     */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar par\xc3\x83\xc2\xa2metros de material" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Material;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Material = { "Material", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventUpdateMaterialParameters_Parms, Material), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronVFXBridge_eventUpdateMaterialParameters_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
void Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronVFXBridge_eventUpdateMaterialParameters_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronVFXBridge_eventUpdateMaterialParameters_Parms), &Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Material,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronVFXBridge, nullptr, "UpdateMaterialParameters", Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::AuracronVFXBridge_eventUpdateMaterialParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::AuracronVFXBridge_eventUpdateMaterialParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronVFXBridge::execUpdateMaterialParameters)
{
	P_GET_OBJECT(UMaterialInstanceDynamic,Z_Param_Material);
	P_GET_TMAP_REF(FString,float,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpdateMaterialParameters(Z_Param_Material,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// ********** End Class UAuracronVFXBridge Function UpdateMaterialParameters ***********************

// ********** Begin Class UAuracronVFXBridge *******************************************************
void UAuracronVFXBridge::StaticRegisterNativesUAuracronVFXBridge()
{
	UClass* Class = UAuracronVFXBridge::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateRealmVFX", &UAuracronVFXBridge::execActivateRealmVFX },
		{ "ApplyDissolveEffect", &UAuracronVFXBridge::execApplyDissolveEffect },
		{ "CleanupInactiveVFX", &UAuracronVFXBridge::execCleanupInactiveVFX },
		{ "CreateDynamicMaterial", &UAuracronVFXBridge::execCreateDynamicMaterial },
		{ "OnRep_VFXQuality", &UAuracronVFXBridge::execOnRep_VFXQuality },
		{ "OptimizeVFXByDistance", &UAuracronVFXBridge::execOptimizeVFXByDistance },
		{ "PauseAllVFXEffects", &UAuracronVFXBridge::execPauseAllVFXEffects },
		{ "PlayAbilityVFX", &UAuracronVFXBridge::execPlayAbilityVFX },
		{ "PlayHealingVFX", &UAuracronVFXBridge::execPlayHealingVFX },
		{ "PlayImpactVFX", &UAuracronVFXBridge::execPlayImpactVFX },
		{ "PlayRealmTransitionVFX", &UAuracronVFXBridge::execPlayRealmTransitionVFX },
		{ "PlayStatusEffectVFX", &UAuracronVFXBridge::execPlayStatusEffectVFX },
		{ "ResumeAllVFXEffects", &UAuracronVFXBridge::execResumeAllVFXEffects },
		{ "SetVFXQuality", &UAuracronVFXBridge::execSetVFXQuality },
		{ "SpawnAttachedNiagaraEffect", &UAuracronVFXBridge::execSpawnAttachedNiagaraEffect },
		{ "SpawnNiagaraEffect", &UAuracronVFXBridge::execSpawnNiagaraEffect },
		{ "SpawnPortalVFX", &UAuracronVFXBridge::execSpawnPortalVFX },
		{ "StopVFXEffect", &UAuracronVFXBridge::execStopVFXEffect },
		{ "UpdateAmbientVFX", &UAuracronVFXBridge::execUpdateAmbientVFX },
		{ "UpdateMaterialParameters", &UAuracronVFXBridge::execUpdateMaterialParameters },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronVFXBridge;
UClass* UAuracronVFXBridge::GetPrivateStaticClass()
{
	using TClass = UAuracronVFXBridge;
	if (!Z_Registration_Info_UClass_UAuracronVFXBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronVFXBridge"),
			Z_Registration_Info_UClass_UAuracronVFXBridge.InnerSingleton,
			StaticRegisterNativesUAuracronVFXBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronVFXBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronVFXBridge_NoRegister()
{
	return UAuracronVFXBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronVFXBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "Category", "AURACRON|VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe principal do Bridge para Sistema de Part\xc3\x83\xc2\xad""culas e Efeitos Visuais\n * Respons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de VFX com Niagara\n */" },
#endif
		{ "DisplayName", "AURACRON VFX Bridge" },
		{ "HideCategories", "Trigger PhysicsVolume" },
		{ "IncludePath", "AuracronVFXBridge.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe principal do Bridge para Sistema de Part\xc3\x83\xc2\xad""culas e Efeitos Visuais\nRespons\xc3\x83\xc2\xa1vel pelo gerenciamento completo de VFX com Niagara" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmVFXConfiguration_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de realm */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\x83\xc2\xa7\xc3\x83\xc2\xa3o de efeitos de realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentVFXQuality_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Qualidade atual de VFX */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Qualidade atual de VFX" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveVFXComponents_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeitos ativos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeitos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NiagaraComponentPool_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pool de componentes Niagara */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool de componentes Niagara" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentPool_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pool de componentes para otimiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool de componentes para otimiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveDynamicMaterials_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Materiais din\xc3\x83\xc2\xa2micos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Materiais din\xc3\x83\xc2\xa2micos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVFXSpawned_MetaData[] = {
		{ "Category", "AURACRON VFX|Events" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVFXQualityChanged_MetaData[] = {
		{ "Category", "AURACRON VFX|Events" },
		{ "ModuleRelativePath", "Public/AuracronVFXBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmVFXConfiguration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentVFXQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentVFXQuality;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveVFXComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveVFXComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NiagaraComponentPool_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_NiagaraComponentPool;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ComponentPool_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ComponentPool;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveDynamicMaterials_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveDynamicMaterials;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVFXSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVFXQualityChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronVFXBridge_ActivateRealmVFX, "ActivateRealmVFX" }, // 3727683895
		{ &Z_Construct_UFunction_UAuracronVFXBridge_ApplyDissolveEffect, "ApplyDissolveEffect" }, // 2910225891
		{ &Z_Construct_UFunction_UAuracronVFXBridge_CleanupInactiveVFX, "CleanupInactiveVFX" }, // 2798983565
		{ &Z_Construct_UFunction_UAuracronVFXBridge_CreateDynamicMaterial, "CreateDynamicMaterial" }, // 3536217201
		{ &Z_Construct_UFunction_UAuracronVFXBridge_OnRep_VFXQuality, "OnRep_VFXQuality" }, // 3158076418
		{ &Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature, "OnVFXQualityChanged__DelegateSignature" }, // 1913182092
		{ &Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature, "OnVFXSpawned__DelegateSignature" }, // 1273998463
		{ &Z_Construct_UFunction_UAuracronVFXBridge_OptimizeVFXByDistance, "OptimizeVFXByDistance" }, // 4013132230
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PauseAllVFXEffects, "PauseAllVFXEffects" }, // 1097532604
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PlayAbilityVFX, "PlayAbilityVFX" }, // 3543108814
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PlayHealingVFX, "PlayHealingVFX" }, // 242727728
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PlayImpactVFX, "PlayImpactVFX" }, // 2753746168
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PlayRealmTransitionVFX, "PlayRealmTransitionVFX" }, // 2874904921
		{ &Z_Construct_UFunction_UAuracronVFXBridge_PlayStatusEffectVFX, "PlayStatusEffectVFX" }, // 650176845
		{ &Z_Construct_UFunction_UAuracronVFXBridge_ResumeAllVFXEffects, "ResumeAllVFXEffects" }, // 273779578
		{ &Z_Construct_UFunction_UAuracronVFXBridge_SetVFXQuality, "SetVFXQuality" }, // 2451138939
		{ &Z_Construct_UFunction_UAuracronVFXBridge_SpawnAttachedNiagaraEffect, "SpawnAttachedNiagaraEffect" }, // 1688100498
		{ &Z_Construct_UFunction_UAuracronVFXBridge_SpawnNiagaraEffect, "SpawnNiagaraEffect" }, // 3230131336
		{ &Z_Construct_UFunction_UAuracronVFXBridge_SpawnPortalVFX, "SpawnPortalVFX" }, // 732898116
		{ &Z_Construct_UFunction_UAuracronVFXBridge_StopVFXEffect, "StopVFXEffect" }, // 3418617101
		{ &Z_Construct_UFunction_UAuracronVFXBridge_UpdateAmbientVFX, "UpdateAmbientVFX" }, // 2895325044
		{ &Z_Construct_UFunction_UAuracronVFXBridge_UpdateMaterialParameters, "UpdateMaterialParameters" }, // 893940798
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronVFXBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_RealmVFXConfiguration = { "RealmVFXConfiguration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, RealmVFXConfiguration), Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmVFXConfiguration_MetaData), NewProp_RealmVFXConfiguration_MetaData) }; // 1307560573
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_CurrentVFXQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_CurrentVFXQuality = { "CurrentVFXQuality", "OnRep_VFXQuality", (EPropertyFlags)0x0010000100000025, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, CurrentVFXQuality), Z_Construct_UEnum_AuracronVFXBridge_EAuracronVFXQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentVFXQuality_MetaData), NewProp_CurrentVFXQuality_MetaData) }; // 2708674787
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveVFXComponents_Inner = { "ActiveVFXComponents", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveVFXComponents = { "ActiveVFXComponents", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, ActiveVFXComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveVFXComponents_MetaData), NewProp_ActiveVFXComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_NiagaraComponentPool_Inner = { "NiagaraComponentPool", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_NiagaraComponentPool = { "NiagaraComponentPool", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, NiagaraComponentPool), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NiagaraComponentPool_MetaData), NewProp_NiagaraComponentPool_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ComponentPool_Inner = { "ComponentPool", nullptr, (EPropertyFlags)0x01040000000a0008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ComponentPool = { "ComponentPool", nullptr, (EPropertyFlags)0x011400800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, ComponentPool), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentPool_MetaData), NewProp_ComponentPool_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveDynamicMaterials_Inner = { "ActiveDynamicMaterials", nullptr, (EPropertyFlags)0x0104000000020000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveDynamicMaterials = { "ActiveDynamicMaterials", nullptr, (EPropertyFlags)0x0114000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, ActiveDynamicMaterials), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveDynamicMaterials_MetaData), NewProp_ActiveDynamicMaterials_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_OnVFXSpawned = { "OnVFXSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, OnVFXSpawned), Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVFXSpawned_MetaData), NewProp_OnVFXSpawned_MetaData) }; // 1273998463
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_OnVFXQualityChanged = { "OnVFXQualityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronVFXBridge, OnVFXQualityChanged), Z_Construct_UDelegateFunction_UAuracronVFXBridge_OnVFXQualityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVFXQualityChanged_MetaData), NewProp_OnVFXQualityChanged_MetaData) }; // 1913182092
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronVFXBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_RealmVFXConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_CurrentVFXQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_CurrentVFXQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveVFXComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveVFXComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_NiagaraComponentPool_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_NiagaraComponentPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ComponentPool_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ComponentPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveDynamicMaterials_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_ActiveDynamicMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_OnVFXSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronVFXBridge_Statics::NewProp_OnVFXQualityChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVFXBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronVFXBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameFrameworkComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronVFXBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVFXBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronVFXBridge_Statics::ClassParams = {
	&UAuracronVFXBridge::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronVFXBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVFXBridge_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronVFXBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronVFXBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronVFXBridge()
{
	if (!Z_Registration_Info_UClass_UAuracronVFXBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronVFXBridge.OuterSingleton, Z_Construct_UClass_UAuracronVFXBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronVFXBridge.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void UAuracronVFXBridge::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentVFXQuality(TEXT("CurrentVFXQuality"));
	const bool bIsValid = true
		&& Name_CurrentVFXQuality == ClassReps[(int32)ENetFields_Private::CurrentVFXQuality].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in UAuracronVFXBridge"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronVFXBridge);
UAuracronVFXBridge::~UAuracronVFXBridge() {}
// ********** End Class UAuracronVFXBridge *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronVFXType_StaticEnum, TEXT("EAuracronVFXType"), &Z_Registration_Info_UEnum_EAuracronVFXType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3334367885U) },
		{ EAuracronVFXQuality_StaticEnum, TEXT("EAuracronVFXQuality"), &Z_Registration_Info_UEnum_EAuracronVFXQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2708674787U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronVFXConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronVFXConfiguration_Statics::NewStructOps, TEXT("AuracronVFXConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronVFXConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronVFXConfiguration), 148941632U) },
		{ FAuracronRealmVFXConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmVFXConfiguration_Statics::NewStructOps, TEXT("AuracronRealmVFXConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronRealmVFXConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmVFXConfiguration), 1307560573U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronVFXBridge, UAuracronVFXBridge::StaticClass, TEXT("UAuracronVFXBridge"), &Z_Registration_Info_UClass_UAuracronVFXBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronVFXBridge), 804537010U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_3055268830(TEXT("/Script/AuracronVFXBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronVFXBridge_Public_AuracronVFXBridge_h__Script_AuracronVFXBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
