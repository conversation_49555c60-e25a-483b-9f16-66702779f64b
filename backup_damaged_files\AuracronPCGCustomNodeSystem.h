﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Custom Node System Header
// Bridge 2.13: PCG Framework - Custom Node Creation

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronPCGFramework.h"
#include "AuracronPCGElementBase.h"
#include "AuracronPCGNodeSystem.h"

// PCG Framework includes for UE5.6
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGData.h"
#include "Data/PCGBasePointData.h"
#include "Data/PCGSpatialData.h"
#include "Metadata/PCGMetadata.h"
#include "PCGPin.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "K2Node.h"

#include "AuracronPCGCustomNodeSystem.generated.h"

// Custom node parameter types
UENUM(BlueprintType)
enum class EAuracronPCGCustomParameterType : uint8
{
    Boolean                 UMETA(DisplayName = "Boolean"),
    Integer                 UMETA(DisplayName = "Integer"),
    Float                   UMETA(DisplayName = "Float"),
    String                  UMETA(DisplayName = "String"),
    Vector                  UMETA(DisplayName = "Vector"),
    Rotator                 UMETA(DisplayName = "Rotator"),
    Transform               UMETA(DisplayName = "Transform"),
    Color                   UMETA(DisplayName = "Color"),
    Object                  UMETA(DisplayName = "Object"),
    Class                   UMETA(DisplayName = "Class"),
    Enum                    UMETA(DisplayName = "Enum"),
    Struct                  UMETA(DisplayName = "Struct"),
    Array                   UMETA(DisplayName = "Array"),
    Map                     UMETA(DisplayName = "Map"),
    Set                     UMETA(DisplayName = "Set"),
    Delegate                UMETA(DisplayName = "Delegate"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Custom node execution modes
UENUM(BlueprintType)
enum class EAuracronPCGCustomNodeExecutionMode : uint8
{
    Synchronous             UMETA(DisplayName = "Synchronous"),
    Asynchronous            UMETA(DisplayName = "Asynchronous"),
    Threaded                UMETA(DisplayName = "Threaded"),
    GPU                     UMETA(DisplayName = "GPU"),
    Distributed             UMETA(DisplayName = "Distributed"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Custom node template types
UENUM(BlueprintType)
enum class EAuracronPCGCustomNodeTemplateType : uint8
{
    Generator               UMETA(DisplayName = "Generator"),
    Modifier                UMETA(DisplayName = "Modifier"),
    Filter                  UMETA(DisplayName = "Filter"),
    Sampler                 UMETA(DisplayName = "Sampler"),
    Transformer             UMETA(DisplayName = "Transformer"),
    Analyzer                UMETA(DisplayName = "Analyzer"),
    Utility                 UMETA(DisplayName = "Utility"),
    Debug                   UMETA(DisplayName = "Debug"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Custom node validation levels
UENUM(BlueprintType)
enum class EAuracronPCGCustomNodeValidationLevel : uint8
{
    None                    UMETA(DisplayName = "None"),
    Basic                   UMETA(DisplayName = "Basic"),
    Standard                UMETA(DisplayName = "Standard"),
    Strict                  UMETA(DisplayName = "Strict"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// CUSTOM PARAMETER DESCRIPTOR
// =============================================================================

/**
 * Custom Parameter Descriptor
 * Describes a custom parameter for a custom node
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCustomParameterDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    FString ParameterName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    FString DisplayName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    FString Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameter")
    EAuracronPCGCustomParameterType ParameterType = EAuracronPCGCustomParameterType::Float;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Default Value")
    FString DefaultValue;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bIsRequired = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bHasMinValue = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation", meta = (EditCondition = "bHasMinValue"))
    float MinValue = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    bool bHasMaxValue = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation", meta = (EditCondition = "bHasMaxValue"))
    float MaxValue = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    bool bIsAdvanced = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    bool bIsHidden = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    FString Category = TEXT("Default");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    FString Tooltip;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enum", meta = (EditCondition = "ParameterType == EAuracronPCGCustomParameterType::Enum"))
    TArray<FString> EnumValues;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Object", meta = (EditCondition = "ParameterType == EAuracronPCGCustomParameterType::Object || ParameterType == EAuracronPCGCustomParameterType::Class"))
    TSoftClassPtr<UObject> ObjectClass;

    FAuracronPCGCustomParameterDescriptor()
    {
        ParameterType = EAuracronPCGCustomParameterType::Float;
        bIsRequired = false;
        bHasMinValue = false;
        MinValue = 0.0f;
        bHasMaxValue = false;
        MaxValue = 1.0f;
        bIsAdvanced = false;
        bIsHidden = false;
        Category = TEXT("Default");
    }
};

// =============================================================================
// CUSTOM PIN DESCRIPTOR
// =============================================================================

/**
 * Custom Pin Descriptor
 * Describes input/output pins for a custom node
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCustomPinDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    FString PinName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    FString DisplayName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    FString Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    EPCGDataType AllowedTypes = EPCGDataType::Point;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    bool bIsInput = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    bool bIsRequired = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    bool bAllowMultipleConnections = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    bool bIsAdvanced = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    bool bIsHidden = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    FLinearColor PinColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pin")
    FString Tooltip;

    FAuracronPCGCustomPinDescriptor()
    {
        AllowedTypes = EPCGDataType::Point;
        bIsInput = true;
        bIsRequired = true;
        bAllowMultipleConnections = false;
        bIsAdvanced = false;
        bIsHidden = false;
        PinColor = FLinearColor::White;
    }
};

// =============================================================================
// CUSTOM NODE TEMPLATE
// =============================================================================

/**
 * Custom Node Template
 * Template for creating custom PCG nodes
 */
USTRUCT(BlueprintType)
struct AURACRONPCGBRIDGE_API FAuracronPCGCustomNodeTemplate
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    FString TemplateName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    FString DisplayName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    FString Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    EAuracronPCGCustomNodeTemplateType TemplateType = EAuracronPCGCustomNodeTemplateType::Modifier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    EAuracronPCGNodeCategory Category = EAuracronPCGNodeCategory::Modifier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    TArray<FString> Tags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
    FLinearColor NodeColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TArray<FAuracronPCGCustomParameterDescriptor> Parameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pins")
    TArray<FAuracronPCGCustomPinDescriptor> InputPins;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pins")
    TArray<FAuracronPCGCustomPinDescriptor> OutputPins;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    EAuracronPCGCustomNodeExecutionMode ExecutionMode = EAuracronPCGCustomNodeExecutionMode::Synchronous;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    FString ExecutionFunction;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    TSoftObjectPtr<UBlueprint> BlueprintImplementation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Execution")
    FString NativeClassName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    EAuracronPCGCustomNodeValidationLevel ValidationLevel = EAuracronPCGCustomNodeValidationLevel::Standard;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
    FString ValidationFunction;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bIsExperimental = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bIsDeprecated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    FString Version = TEXT("1.0");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    FString Author;

    FAuracronPCGCustomNodeTemplate()
    {
        TemplateType = EAuracronPCGCustomNodeTemplateType::Modifier;
        Category = EAuracronPCGNodeCategory::Modifier;
        NodeColor = FLinearColor::White;
        ExecutionMode = EAuracronPCGCustomNodeExecutionMode::Synchronous;
        ValidationLevel = EAuracronPCGCustomNodeValidationLevel::Standard;
        bIsExperimental = false;
        bIsDeprecated = false;
        Version = TEXT("1.0");
    }
};

// =============================================================================
// CUSTOM NODE FACTORY
// =============================================================================

/**
 * Custom Node Factory
 * Factory for creating custom PCG nodes from templates
 */

public:
    // Node creation functions
    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static UClass* CreateCustomNodeClass(const FAuracronPCGCustomNodeTemplate& Template);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static UPCGSettings* CreateCustomNodeInstance(const FAuracronPCGCustomNodeTemplate& Template);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static bool RegisterCustomNodeTemplate(const FAuracronPCGCustomNodeTemplate& Template);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static bool UnregisterCustomNodeTemplate(const FString& TemplateName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static TArray<FAuracronPCGCustomNodeTemplate> GetRegisteredTemplates();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static FAuracronPCGCustomNodeTemplate GetTemplate(const FString& TemplateName);

    // Template validation
    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static bool ValidateTemplate(const FAuracronPCGCustomNodeTemplate& Template, FString& OutErrorMessage);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static TArray<FString> GetTemplateValidationErrors(const FAuracronPCGCustomNodeTemplate& Template);

    // Blueprint integration
    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static UBlueprint* CreateBlueprintFromTemplate(const FAuracronPCGCustomNodeTemplate& Template);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static bool CompileBlueprintNode(UBlueprint* Blueprint);

    // Template management
    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static bool SaveTemplateToFile(const FAuracronPCGCustomNodeTemplate& Template, const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static FAuracronPCGCustomNodeTemplate LoadTemplateFromFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Factory")
    static TArray<FString> GetAvailableTemplateFiles(const FString& Directory);

private:
    static TMap<FString, FAuracronPCGCustomNodeTemplate> RegisteredTemplates;
};

// =============================================================================
// CUSTOM NODE BUILDER
// =============================================================================

/**
 * Custom Node Builder
 * Builder pattern for creating custom nodes step by step
 */

public:
    UAuracronPCGCustomNodeBuilder();

    // Builder pattern methods
    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetNodeName(const FString& Name);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetDisplayName(const FString& DisplayName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetDescription(const FString& Description);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetTemplateType(EAuracronPCGCustomNodeTemplateType TemplateType);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetCategory(EAuracronPCGNodeCategory Category);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* AddTag(const FString& Tag);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetNodeColor(const FLinearColor& Color);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* AddParameter(const FAuracronPCGCustomParameterDescriptor& Parameter);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* AddInputPin(const FAuracronPCGCustomPinDescriptor& Pin);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* AddOutputPin(const FAuracronPCGCustomPinDescriptor& Pin);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetExecutionMode(EAuracronPCGCustomNodeExecutionMode ExecutionMode);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetExecutionFunction(const FString& FunctionName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetBlueprintImplementation(UBlueprint* Blueprint);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetNativeClassName(const FString& ClassName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetValidationLevel(EAuracronPCGCustomNodeValidationLevel ValidationLevel);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetAuthor(const FString& Author);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UAuracronPCGCustomNodeBuilder* SetVersion(const FString& Version);

    // Build methods
    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    FAuracronPCGCustomNodeTemplate BuildTemplate();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UClass* BuildNodeClass();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    UPCGSettings* BuildNodeInstance();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Builder")
    void Reset();

private:
    UPROPERTY()
    FAuracronPCGCustomNodeTemplate CurrentTemplate;
};

// =============================================================================
// CUSTOM NODE REGISTRY
// =============================================================================

/**
 * Custom Node Registry
 * Registry for managing custom node templates and instances
 */

public:
    // Registry management
    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    static UAuracronPCGCustomNodeRegistry* GetInstance();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    bool RegisterTemplate(const FAuracronPCGCustomNodeTemplate& Template);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    bool UnregisterTemplate(const FString& TemplateName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    bool IsTemplateRegistered(const FString& TemplateName) const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    FAuracronPCGCustomNodeTemplate GetTemplate(const FString& TemplateName) const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    TArray<FAuracronPCGCustomNodeTemplate> GetAllTemplates() const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    TArray<FAuracronPCGCustomNodeTemplate> GetTemplatesByCategory(EAuracronPCGNodeCategory Category) const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    TArray<FAuracronPCGCustomNodeTemplate> GetTemplatesByType(EAuracronPCGCustomNodeTemplateType TemplateType) const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    TArray<FString> GetTemplateNames() const;

    // Node instance management
    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    UPCGSettings* CreateNodeInstance(const FString& TemplateName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    bool RegisterNodeInstance(const FString& InstanceName, UPCGSettings* NodeInstance);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    UPCGSettings* GetNodeInstance(const FString& InstanceName) const;

    // Validation and utilities
    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    bool ValidateAllTemplates(TArray<FString>& OutErrors) const;

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    void RefreshRegistry();

    UFUNCTION(BlueprintCallable, Category = "Custom Node Registry")
    void ClearRegistry();

private:
    UPROPERTY()
    TMap<FString, FAuracronPCGCustomNodeTemplate> Templates;

    UPROPERTY()
    TMap<FString, UPCGSettings*> NodeInstances;

    static UAuracronPCGCustomNodeRegistry* Instance;
};

// =============================================================================
// CUSTOM NODE UTILITIES
// =============================================================================

/**
 * Custom Node Utilities
 * Utility functions for custom node operations
 */

public:
    // Template utilities
    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomNodeTemplate CreateBasicTemplate(const FString& NodeName, EAuracronPCGCustomNodeTemplateType TemplateType);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateFloatParameter(const FString& Name, float DefaultValue, float MinValue = 0.0f, float MaxValue = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateIntParameter(const FString& Name, int32 DefaultValue, int32 MinValue = 0, int32 MaxValue = 100);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateBoolParameter(const FString& Name, bool DefaultValue);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateStringParameter(const FString& Name, const FString& DefaultValue);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateVectorParameter(const FString& Name, const FVector& DefaultValue);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateColorParameter(const FString& Name, const FLinearColor& DefaultValue);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomParameterDescriptor CreateEnumParameter(const FString& Name, const TArray<FString>& EnumValues, const FString& DefaultValue);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomPinDescriptor CreateInputPin(const FString& Name, EPCGDataType DataType, bool bRequired = true);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomPinDescriptor CreateOutputPin(const FString& Name, EPCGDataType DataType);

    // Validation utilities
    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static bool ValidateParameterName(const FString& ParameterName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static bool ValidatePinName(const FString& PinName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static bool ValidateNodeName(const FString& NodeName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FString SanitizeParameterName(const FString& ParameterName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FString SanitizePinName(const FString& PinName);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FString SanitizeNodeName(const FString& NodeName);

    // Conversion utilities
    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FString ParameterTypeToString(EAuracronPCGCustomParameterType ParameterType);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static EAuracronPCGCustomParameterType StringToParameterType(const FString& TypeString);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FString ExecutionModeToString(EAuracronPCGCustomNodeExecutionMode ExecutionMode);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static EAuracronPCGCustomNodeExecutionMode StringToExecutionMode(const FString& ModeString);

    // Template generation utilities
    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomNodeTemplate GenerateTemplateFromBlueprint(UBlueprint* Blueprint);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static FAuracronPCGCustomNodeTemplate GenerateTemplateFromClass(UClass* NodeClass);

    UFUNCTION(BlueprintCallable, Category = "Custom Node Utils")
    static TArray<FAuracronPCGCustomNodeTemplate> GenerateTemplatesFromDirectory(const FString& Directory);
};
