# Script para expandir a macro AURACRON_PCG_NODE_SETTINGS manualmente
# Resolve o problema de compilação da macro

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando expansao da macro AURACRON_PCG_NODE_SETTINGS..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Encontra todos os arquivos .h que usam a macro
$filesWithMacro = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h" | 
    Where-Object { $_.Name -notlike "*.generated.*" } |
    Where-Object { (Get-Content $_.FullName -Raw) -match "AURACRON_PCG_NODE_SETTINGS" }

Write-Host "Encontrados $($filesWithMacro.Count) arquivos que usam a macro..." -ForegroundColor Cyan

$totalReplacements = 0
$filesModified = 0

foreach ($file in $filesWithMacro) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # Regex para encontrar a macro e extrair os parametros
    $macroPattern = 'AURACRON_PCG_NODE_SETTINGS\s*\(\s*([^,]+)\s*,\s*([^)]+)\s*\)'
    
    # Encontra todas as ocorrencias da macro
    $matches = [regex]::Matches($content, $macroPattern)
    
    foreach ($match in $matches) {
        $fullMatch = $match.Value
        $settingsClass = $match.Groups[1].Value.Trim()
        $elementClass = $match.Groups[2].Value.Trim()
        
        Write-Host "    Expandindo macro para: $settingsClass" -ForegroundColor Yellow
        
        # Cria a expansao da macro
        $expansion = @"
UCLASS(BlueprintType, Blueprintable, Category = "Auracron PCG")
class AURACRONPCGBRIDGE_API $settingsClass : public UAuracronPCGNodeSettings
{
    GENERATED_BODY()
public:
    $settingsClass(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get())
        : Super(ObjectInitializer) {}
    virtual UPCGNode* CreateNode() const override;
protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
    virtual FPCGElementPtr CreateElement() const override;
"@
        
        # Substitui a macro pela expansao
        $content = $content -replace [regex]::Escape($fullMatch), $expansion
        $fileReplacements++
        
        # Remove a declaracao de classe duplicada que vem depois da macro
        $duplicateClassPattern = "class\s+AURACRONPCGBRIDGE_API\s+$settingsClass\s*:\s*public\s+UAuracronPCGNodeSettings\s*\{"
        if ($content -match $duplicateClassPattern) {
            # Encontra a linha da declaracao duplicada e remove apenas a linha da declaracao
            $lines = $content -split "`n"
            $newLines = @()
            $skipNext = $false
            
            for ($i = 0; $i -lt $lines.Length; $i++) {
                if ($lines[$i] -match $duplicateClassPattern) {
                    # Pula esta linha (declaracao duplicada)
                    Write-Host "    Removendo declaracao duplicada de classe" -ForegroundColor Gray
                    continue
                }
                $newLines += $lines[$i]
            }
            
            $content = $newLines -join "`n"
        }
    }
    
    # Salva o arquivo se houve mudancas
    if ($fileReplacements -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalReplacements += $fileReplacements
        $filesModified++
        Write-Host "  OK $fileReplacements macros expandidas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma macro encontrada" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($filesWithMacro.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de macros expandidas: $totalReplacements" -ForegroundColor Green

if ($totalReplacements -gt 0) {
    Write-Host "`nExpansao de macros concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma macro encontrada para expandir." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
