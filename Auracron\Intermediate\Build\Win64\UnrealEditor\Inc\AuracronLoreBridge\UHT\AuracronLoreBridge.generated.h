// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronLoreBridge.h"

#ifdef AURACRONLOREBRIDGE_AuracronLoreBridge_generated_h
#error "AuracronLoreBridge.generated.h already included, missing '#pragma once' in AuracronLoreBridge.h"
#endif
#define AURACRONLOREBRIDGE_AuracronLoreBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
enum class EAuracronLoreDiscoveryMethod : uint8;
struct FAuracronLoreCollection;
struct FAuracronLoreEntry;

// ********** Begin ScriptStruct FAuracronLoreEntry ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_89_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLoreEntry_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLoreEntry;
// ********** End ScriptStruct FAuracronLoreEntry **************************************************

// ********** Begin ScriptStruct FAuracronLoreCollection *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_198_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLoreCollection_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLoreCollection;
// ********** End ScriptStruct FAuracronLoreCollection *********************************************

// ********** Begin Delegate FOnLoreDiscovered *****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_477_DELEGATE \
static void FOnLoreDiscovered_DelegateWrapper(const FMulticastScriptDelegate& OnLoreDiscovered, const FString& LoreID, EAuracronLoreDiscoveryMethod DiscoveryMethod);


// ********** End Delegate FOnLoreDiscovered *******************************************************

// ********** Begin Delegate FOnLoreRead ***********************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_482_DELEGATE \
static void FOnLoreRead_DelegateWrapper(const FMulticastScriptDelegate& OnLoreRead, const FString& LoreID);


// ********** End Delegate FOnLoreRead *************************************************************

// ********** Begin Delegate FOnCollectionCompleted ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_487_DELEGATE \
static void FOnCollectionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnCollectionCompleted, const FString& CollectionID);


// ********** End Delegate FOnCollectionCompleted **************************************************

// ********** Begin Class UAuracronLoreBridge ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetLoreStatistics); \
	DECLARE_FUNCTION(execTrackLoreReading); \
	DECLARE_FUNCTION(execTrackLoreDiscovery); \
	DECLARE_FUNCTION(execDiscoverLoreByChampion); \
	DECLARE_FUNCTION(execDiscoverLoreByAction); \
	DECLARE_FUNCTION(execDiscoverLoreByLocation); \
	DECLARE_FUNCTION(execCheckDiscoveryConditions); \
	DECLARE_FUNCTION(execHideLoreUI); \
	DECLARE_FUNCTION(execShowLoreUI); \
	DECLARE_FUNCTION(execPlayLoreAudio); \
	DECLARE_FUNCTION(execPlayLoreCinematic); \
	DECLARE_FUNCTION(execUpdateCollectionProgress); \
	DECLARE_FUNCTION(execIsCollectionCompleted); \
	DECLARE_FUNCTION(execGetAllLoreCollections); \
	DECLARE_FUNCTION(execGetLoreCollection); \
	DECLARE_FUNCTION(execCreateLoreCollection); \
	DECLARE_FUNCTION(execIsLoreDiscovered); \
	DECLARE_FUNCTION(execGetDiscoveredLoreEntries); \
	DECLARE_FUNCTION(execGetLoreEntry); \
	DECLARE_FUNCTION(execReadLoreEntry); \
	DECLARE_FUNCTION(execDiscoverLoreEntry);


AURACRONLOREBRIDGE_API UClass* Z_Construct_UClass_UAuracronLoreBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronLoreBridge(); \
	friend struct Z_Construct_UClass_UAuracronLoreBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONLOREBRIDGE_API UClass* Z_Construct_UClass_UAuracronLoreBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronLoreBridge, UGameFrameworkComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronLoreBridge"), Z_Construct_UClass_UAuracronLoreBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronLoreBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		DiscoveredLoreIDs=NETFIELD_REP_START, \
		ReadLoreIDs, \
		TotalLorePoints, \
		NETFIELD_REP_END=TotalLorePoints	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronLoreBridge(UAuracronLoreBridge&&) = delete; \
	UAuracronLoreBridge(const UAuracronLoreBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronLoreBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronLoreBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronLoreBridge) \
	NO_API virtual ~UAuracronLoreBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_257_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h_260_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronLoreBridge;

// ********** End Class UAuracronLoreBridge ********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronLoreBridge_Public_AuracronLoreBridge_h

// ********** Begin Enum EAuracronLoreType *********************************************************
#define FOREACH_ENUM_EAURACRONLORETYPE(op) \
	op(EAuracronLoreType::None) \
	op(EAuracronLoreType::ChampionBackstory) \
	op(EAuracronLoreType::RealmHistory) \
	op(EAuracronLoreType::SigiloOrigins) \
	op(EAuracronLoreType::WorldLore) \
	op(EAuracronLoreType::EventLore) \
	op(EAuracronLoreType::ItemLore) \
	op(EAuracronLoreType::LocationLore) \
	op(EAuracronLoreType::FactionLore) \
	op(EAuracronLoreType::MythologyLore) \
	op(EAuracronLoreType::SecretLore) 

enum class EAuracronLoreType : uint8;
template<> struct TIsUEnumClass<EAuracronLoreType> { enum { Value = true }; };
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreType>();
// ********** End Enum EAuracronLoreType ***********************************************************

// ********** Begin Enum EAuracronLoreRarity *******************************************************
#define FOREACH_ENUM_EAURACRONLORERARITY(op) \
	op(EAuracronLoreRarity::Common) \
	op(EAuracronLoreRarity::Uncommon) \
	op(EAuracronLoreRarity::Rare) \
	op(EAuracronLoreRarity::Epic) \
	op(EAuracronLoreRarity::Legendary) \
	op(EAuracronLoreRarity::Mythic) \
	op(EAuracronLoreRarity::Secret) 

enum class EAuracronLoreRarity : uint8;
template<> struct TIsUEnumClass<EAuracronLoreRarity> { enum { Value = true }; };
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreRarity>();
// ********** End Enum EAuracronLoreRarity *********************************************************

// ********** Begin Enum EAuracronLoreDiscoveryMethod **********************************************
#define FOREACH_ENUM_EAURACRONLOREDISCOVERYMETHOD(op) \
	op(EAuracronLoreDiscoveryMethod::Automatic) \
	op(EAuracronLoreDiscoveryMethod::Exploration) \
	op(EAuracronLoreDiscoveryMethod::Combat) \
	op(EAuracronLoreDiscoveryMethod::Quest) \
	op(EAuracronLoreDiscoveryMethod::Achievement) \
	op(EAuracronLoreDiscoveryMethod::Purchase) \
	op(EAuracronLoreDiscoveryMethod::Event) \
	op(EAuracronLoreDiscoveryMethod::Secret) \
	op(EAuracronLoreDiscoveryMethod::Social) \
	op(EAuracronLoreDiscoveryMethod::Progression) 

enum class EAuracronLoreDiscoveryMethod : uint8;
template<> struct TIsUEnumClass<EAuracronLoreDiscoveryMethod> { enum { Value = true }; };
template<> AURACRONLOREBRIDGE_API UEnum* StaticEnum<EAuracronLoreDiscoveryMethod>();
// ********** End Enum EAuracronLoreDiscoveryMethod ************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
