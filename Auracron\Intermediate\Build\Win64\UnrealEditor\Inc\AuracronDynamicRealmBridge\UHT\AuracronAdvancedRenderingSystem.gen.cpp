// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronAdvancedRenderingSystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdvancedRenderingSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics();
AURACRONLUMENBRIDGE_API UClass* Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister();
AURACRONNANITEBRIDGE_API UClass* Z_Construct_UClass_UAuracronNaniteBridge_NoRegister();
AURACRONVFXBRIDGE_API UClass* Z_Construct_UClass_UAuracronVFXBridge_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ERenderingQualityLevel ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERenderingQualityLevel;
static UEnum* ERenderingQualityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERenderingQualityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERenderingQualityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ERenderingQualityLevel"));
	}
	return Z_Registration_Info_UEnum_ERenderingQualityLevel.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERenderingQualityLevel>()
{
	return ERenderingQualityLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "ERenderingQualityLevel::Adaptive" },
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic" },
		{ "Cinematic.Name", "ERenderingQualityLevel::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Rendering quality levels\n */" },
#endif
		{ "High.DisplayName", "High" },
		{ "High.Name", "ERenderingQualityLevel::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "ERenderingQualityLevel::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "ERenderingQualityLevel::Medium" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering quality levels" },
#endif
		{ "Ultra.DisplayName", "Ultra" },
		{ "Ultra.Name", "ERenderingQualityLevel::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERenderingQualityLevel::Low", (int64)ERenderingQualityLevel::Low },
		{ "ERenderingQualityLevel::Medium", (int64)ERenderingQualityLevel::Medium },
		{ "ERenderingQualityLevel::High", (int64)ERenderingQualityLevel::High },
		{ "ERenderingQualityLevel::Ultra", (int64)ERenderingQualityLevel::Ultra },
		{ "ERenderingQualityLevel::Cinematic", (int64)ERenderingQualityLevel::Cinematic },
		{ "ERenderingQualityLevel::Adaptive", (int64)ERenderingQualityLevel::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ERenderingQualityLevel",
	"ERenderingQualityLevel",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel()
{
	if (!Z_Registration_Info_UEnum_ERenderingQualityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERenderingQualityLevel.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERenderingQualityLevel.InnerSingleton;
}
// ********** End Enum ERenderingQualityLevel ******************************************************

// ********** Begin Enum ERenderingOptimizationStrategy ********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERenderingOptimizationStrategy;
static UEnum* ERenderingOptimizationStrategy_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ERenderingOptimizationStrategy"));
	}
	return Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ERenderingOptimizationStrategy>()
{
	return ERenderingOptimizationStrategy_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "ERenderingOptimizationStrategy::Adaptive" },
		{ "Balanced.DisplayName", "Balanced" },
		{ "Balanced.Name", "ERenderingOptimizationStrategy::Balanced" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Rendering optimization strategies\n */" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "ERenderingOptimizationStrategy::Custom" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
		{ "Performance.DisplayName", "Performance" },
		{ "Performance.Name", "ERenderingOptimizationStrategy::Performance" },
		{ "Platform.DisplayName", "Platform" },
		{ "Platform.Name", "ERenderingOptimizationStrategy::Platform" },
		{ "Quality.DisplayName", "Quality" },
		{ "Quality.Name", "ERenderingOptimizationStrategy::Quality" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering optimization strategies" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERenderingOptimizationStrategy::Performance", (int64)ERenderingOptimizationStrategy::Performance },
		{ "ERenderingOptimizationStrategy::Quality", (int64)ERenderingOptimizationStrategy::Quality },
		{ "ERenderingOptimizationStrategy::Balanced", (int64)ERenderingOptimizationStrategy::Balanced },
		{ "ERenderingOptimizationStrategy::Adaptive", (int64)ERenderingOptimizationStrategy::Adaptive },
		{ "ERenderingOptimizationStrategy::Platform", (int64)ERenderingOptimizationStrategy::Platform },
		{ "ERenderingOptimizationStrategy::Custom", (int64)ERenderingOptimizationStrategy::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ERenderingOptimizationStrategy",
	"ERenderingOptimizationStrategy",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy()
{
	if (!Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERenderingOptimizationStrategy.InnerSingleton;
}
// ********** End Enum ERenderingOptimizationStrategy **********************************************

// ********** Begin Enum EPlatformRenderingProfile *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPlatformRenderingProfile;
static UEnum* EPlatformRenderingProfile_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPlatformRenderingProfile.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPlatformRenderingProfile.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EPlatformRenderingProfile"));
	}
	return Z_Registration_Info_UEnum_EPlatformRenderingProfile.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EPlatformRenderingProfile>()
{
	return EPlatformRenderingProfile_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Platform rendering profiles\n */" },
#endif
		{ "Console_Current.DisplayName", "Current-Gen Console" },
		{ "Console_Current.Name", "EPlatformRenderingProfile::Console_Current" },
		{ "Console_Next.DisplayName", "Next-Gen Console" },
		{ "Console_Next.Name", "EPlatformRenderingProfile::Console_Next" },
		{ "Desktop_High.DisplayName", "Desktop High-End" },
		{ "Desktop_High.Name", "EPlatformRenderingProfile::Desktop_High" },
		{ "Desktop_Low.DisplayName", "Desktop Low-End" },
		{ "Desktop_Low.Name", "EPlatformRenderingProfile::Desktop_Low" },
		{ "Desktop_Mid.DisplayName", "Desktop Mid-Range" },
		{ "Desktop_Mid.Name", "EPlatformRenderingProfile::Desktop_Mid" },
		{ "Mobile_High.DisplayName", "Mobile High-End" },
		{ "Mobile_High.Name", "EPlatformRenderingProfile::Mobile_High" },
		{ "Mobile_Low.DisplayName", "Mobile Low-End" },
		{ "Mobile_Low.Name", "EPlatformRenderingProfile::Mobile_Low" },
		{ "Mobile_Mid.DisplayName", "Mobile Mid-Range" },
		{ "Mobile_Mid.Name", "EPlatformRenderingProfile::Mobile_Mid" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Platform rendering profiles" },
#endif
		{ "VR_High.DisplayName", "VR High-End" },
		{ "VR_High.Name", "EPlatformRenderingProfile::VR_High" },
		{ "VR_Standard.DisplayName", "VR Standard" },
		{ "VR_Standard.Name", "EPlatformRenderingProfile::VR_Standard" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPlatformRenderingProfile::Desktop_High", (int64)EPlatformRenderingProfile::Desktop_High },
		{ "EPlatformRenderingProfile::Desktop_Mid", (int64)EPlatformRenderingProfile::Desktop_Mid },
		{ "EPlatformRenderingProfile::Desktop_Low", (int64)EPlatformRenderingProfile::Desktop_Low },
		{ "EPlatformRenderingProfile::Console_Next", (int64)EPlatformRenderingProfile::Console_Next },
		{ "EPlatformRenderingProfile::Console_Current", (int64)EPlatformRenderingProfile::Console_Current },
		{ "EPlatformRenderingProfile::Mobile_High", (int64)EPlatformRenderingProfile::Mobile_High },
		{ "EPlatformRenderingProfile::Mobile_Mid", (int64)EPlatformRenderingProfile::Mobile_Mid },
		{ "EPlatformRenderingProfile::Mobile_Low", (int64)EPlatformRenderingProfile::Mobile_Low },
		{ "EPlatformRenderingProfile::VR_High", (int64)EPlatformRenderingProfile::VR_High },
		{ "EPlatformRenderingProfile::VR_Standard", (int64)EPlatformRenderingProfile::VR_Standard },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EPlatformRenderingProfile",
	"EPlatformRenderingProfile",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile()
{
	if (!Z_Registration_Info_UEnum_EPlatformRenderingProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPlatformRenderingProfile.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPlatformRenderingProfile.InnerSingleton;
}
// ********** End Enum EPlatformRenderingProfile ***************************************************

// ********** Begin ScriptStruct FAuracronAdvancedRenderingConfig **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig;
class UScriptStruct* FAuracronAdvancedRenderingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronAdvancedRenderingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Advanced rendering configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced rendering configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OptimizationStrategy_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimization strategy */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimization strategy" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlatformProfile_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Platform profile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Platform profile" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLumen_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Lumen */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Lumen" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNanite_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable Nanite */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable Nanite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHardwareRayTracing_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable hardware ray tracing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable hardware ray tracing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableTemporalUpsampling_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable temporal upsampling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable temporal upsampling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableVariableRateShading_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable variable rate shading */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable variable rate shading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetFrameRate_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Target frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Target frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDynamicResolution_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dynamic resolution scaling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic resolution scaling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXQualityMultiplier_MetaData[] = {
		{ "Category", "Rendering Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX quality multiplier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX quality multiplier" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OptimizationStrategy_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OptimizationStrategy;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlatformProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlatformProfile;
	static void NewProp_bEnableLumen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLumen;
	static void NewProp_bEnableNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNanite;
	static void NewProp_bEnableHardwareRayTracing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHardwareRayTracing;
	static void NewProp_bEnableTemporalUpsampling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableTemporalUpsampling;
	static void NewProp_bEnableVariableRateShading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableVariableRateShading;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetFrameRate;
	static void NewProp_bEnableDynamicResolution_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDynamicResolution;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VFXQualityMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAdvancedRenderingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRenderingConfig, QualityLevel), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) }; // 2094509193
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_OptimizationStrategy_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_OptimizationStrategy = { "OptimizationStrategy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRenderingConfig, OptimizationStrategy), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingOptimizationStrategy, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OptimizationStrategy_MetaData), NewProp_OptimizationStrategy_MetaData) }; // 2758857315
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_PlatformProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_PlatformProfile = { "PlatformProfile", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRenderingConfig, PlatformProfile), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlatformProfile_MetaData), NewProp_PlatformProfile_MetaData) }; // 4171469515
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableLumen_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableLumen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableLumen = { "bEnableLumen", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableLumen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLumen_MetaData), NewProp_bEnableLumen_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableNanite_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableNanite = { "bEnableNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNanite_MetaData), NewProp_bEnableNanite_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableHardwareRayTracing_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableHardwareRayTracing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableHardwareRayTracing = { "bEnableHardwareRayTracing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableHardwareRayTracing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHardwareRayTracing_MetaData), NewProp_bEnableHardwareRayTracing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableTemporalUpsampling_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableTemporalUpsampling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableTemporalUpsampling = { "bEnableTemporalUpsampling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableTemporalUpsampling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableTemporalUpsampling_MetaData), NewProp_bEnableTemporalUpsampling_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableVariableRateShading_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableVariableRateShading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableVariableRateShading = { "bEnableVariableRateShading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableVariableRateShading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableVariableRateShading_MetaData), NewProp_bEnableVariableRateShading_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_TargetFrameRate = { "TargetFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRenderingConfig, TargetFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetFrameRate_MetaData), NewProp_TargetFrameRate_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableDynamicResolution_SetBit(void* Obj)
{
	((FAuracronAdvancedRenderingConfig*)Obj)->bEnableDynamicResolution = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableDynamicResolution = { "bEnableDynamicResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRenderingConfig), &Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableDynamicResolution_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDynamicResolution_MetaData), NewProp_bEnableDynamicResolution_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_VFXQualityMultiplier = { "VFXQualityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRenderingConfig, VFXQualityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXQualityMultiplier_MetaData), NewProp_VFXQualityMultiplier_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_OptimizationStrategy_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_OptimizationStrategy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_PlatformProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_PlatformProfile,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableLumen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableHardwareRayTracing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableTemporalUpsampling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableVariableRateShading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_TargetFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_bEnableDynamicResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewProp_VFXQualityMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronAdvancedRenderingConfig",
	Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::PropPointers),
	sizeof(FAuracronAdvancedRenderingConfig),
	alignof(FAuracronAdvancedRenderingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAdvancedRenderingConfig ************************************

// ********** Begin ScriptStruct FAuracronRenderingPerformanceMetrics ******************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics;
class UScriptStruct* FAuracronRenderingPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronRenderingPerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Rendering performance metrics\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFPS_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current frame rate */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current frame rate" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUFrameTime_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GPU frame time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU frame time" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Draw calls */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Draw calls" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrianglesRendered_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Triangles rendered */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Triangles rendered" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GPU memory usage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GPU memory usage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenPerformanceMs_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lumen performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lumen performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NanitePerformanceMs_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nanite performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nanite performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VFXPerformanceMs_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** VFX performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "VFX performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentResolutionScale_MetaData[] = {
		{ "Category", "Performance Metrics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Resolution scale */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resolution scale" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUFrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TrianglesRendered;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LumenPerformanceMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NanitePerformanceMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VFXPerformanceMs;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentResolutionScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRenderingPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, CurrentFPS), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFPS_MetaData), NewProp_CurrentFPS_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_GPUFrameTime = { "GPUFrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, GPUFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUFrameTime_MetaData), NewProp_GPUFrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_TrianglesRendered = { "TrianglesRendered", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, TrianglesRendered), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrianglesRendered_MetaData), NewProp_TrianglesRendered_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_GPUMemoryUsageMB = { "GPUMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, GPUMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsageMB_MetaData), NewProp_GPUMemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_LumenPerformanceMs = { "LumenPerformanceMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, LumenPerformanceMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenPerformanceMs_MetaData), NewProp_LumenPerformanceMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_NanitePerformanceMs = { "NanitePerformanceMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, NanitePerformanceMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NanitePerformanceMs_MetaData), NewProp_NanitePerformanceMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_VFXPerformanceMs = { "VFXPerformanceMs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, VFXPerformanceMs), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VFXPerformanceMs_MetaData), NewProp_VFXPerformanceMs_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_CurrentResolutionScale = { "CurrentResolutionScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRenderingPerformanceMetrics, CurrentResolutionScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentResolutionScale_MetaData), NewProp_CurrentResolutionScale_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_CurrentFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_GPUFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_TrianglesRendered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_GPUMemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_LumenPerformanceMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_NanitePerformanceMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_VFXPerformanceMs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewProp_CurrentResolutionScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronRenderingPerformanceMetrics",
	Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::PropPointers),
	sizeof(FAuracronRenderingPerformanceMetrics),
	alignof(FAuracronRenderingPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRenderingPerformanceMetrics ********************************

// ********** Begin Class UAuracronAdvancedRenderingSystem Function ApplyPlatformSpecificSettings **
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Platform Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply platform-specific settings */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply platform-specific settings" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "ApplyPlatformSpecificSettings", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execApplyPlatformSpecificSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyPlatformSpecificSettings();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function ApplyPlatformSpecificSettings ****

// ********** Begin Class UAuracronAdvancedRenderingSystem Function ApplyRenderingConfiguration ****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics
{
	struct AuracronAdvancedRenderingSystem_eventApplyRenderingConfiguration_Parms
	{
		FAuracronAdvancedRenderingConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Apply rendering configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply rendering configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventApplyRenderingConfiguration_Parms, Config), Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 1121808770
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "ApplyRenderingConfiguration", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::AuracronAdvancedRenderingSystem_eventApplyRenderingConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::AuracronAdvancedRenderingSystem_eventApplyRenderingConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execApplyRenderingConfiguration)
{
	P_GET_STRUCT_REF(FAuracronAdvancedRenderingConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyRenderingConfiguration(Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function ApplyRenderingConfiguration ******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function CoordinateRenderingSubsystems **
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Subsystem Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Coordinate all rendering subsystems */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Coordinate all rendering subsystems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "CoordinateRenderingSubsystems", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execCoordinateRenderingSubsystems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CoordinateRenderingSubsystems();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function CoordinateRenderingSubsystems ****

// ********** Begin Class UAuracronAdvancedRenderingSystem Function GetCurrentQualityLevel *********
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics
{
	struct AuracronAdvancedRenderingSystem_eventGetCurrentQualityLevel_Parms
	{
		ERenderingQualityLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Quality Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current quality level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current quality level" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventGetCurrentQualityLevel_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(0, nullptr) }; // 2094509193
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "GetCurrentQualityLevel", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::AuracronAdvancedRenderingSystem_eventGetCurrentQualityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::AuracronAdvancedRenderingSystem_eventGetCurrentQualityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execGetCurrentQualityLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ERenderingQualityLevel*)Z_Param__Result=P_THIS->GetCurrentQualityLevel();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function GetCurrentQualityLevel ***********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function GetCurrentRenderingConfiguration 
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics
{
	struct AuracronAdvancedRenderingSystem_eventGetCurrentRenderingConfiguration_Parms
	{
		FAuracronAdvancedRenderingConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get current rendering configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current rendering configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventGetCurrentRenderingConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig, METADATA_PARAMS(0, nullptr) }; // 1121808770
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "GetCurrentRenderingConfiguration", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::AuracronAdvancedRenderingSystem_eventGetCurrentRenderingConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::AuracronAdvancedRenderingSystem_eventGetCurrentRenderingConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execGetCurrentRenderingConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronAdvancedRenderingConfig*)Z_Param__Result=P_THIS->GetCurrentRenderingConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function GetCurrentRenderingConfiguration *

// ********** Begin Class UAuracronAdvancedRenderingSystem Function GetRenderingPerformanceMetrics *
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics
{
	struct AuracronAdvancedRenderingSystem_eventGetRenderingPerformanceMetrics_Parms
	{
		FAuracronRenderingPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get rendering performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get rendering performance metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventGetRenderingPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 822221741
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "GetRenderingPerformanceMetrics", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::AuracronAdvancedRenderingSystem_eventGetRenderingPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::AuracronAdvancedRenderingSystem_eventGetRenderingPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execGetRenderingPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronRenderingPerformanceMetrics*)Z_Param__Result=P_THIS->GetRenderingPerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function GetRenderingPerformanceMetrics ***

// ********** Begin Class UAuracronAdvancedRenderingSystem Function InitializeLumenIntegration *****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Subsystem Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize Lumen integration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Lumen integration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "InitializeLumenIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execInitializeLumenIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeLumenIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function InitializeLumenIntegration *******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function InitializeNaniteIntegration ****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Subsystem Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize Nanite integration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize Nanite integration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "InitializeNaniteIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execInitializeNaniteIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeNaniteIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function InitializeNaniteIntegration ******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function InitializeRenderingSystem ******
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize advanced rendering system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize advanced rendering system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "InitializeRenderingSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execInitializeRenderingSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeRenderingSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function InitializeRenderingSystem ********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function InitializeVFXIntegration *******
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Subsystem Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize VFX integration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize VFX integration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "InitializeVFXIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execInitializeVFXIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeVFXIntegration();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function InitializeVFXIntegration *********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function MonitorRenderingPerformance ****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Monitor rendering performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Monitor rendering performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "MonitorRenderingPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execMonitorRenderingPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MonitorRenderingPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function MonitorRenderingPerformance ******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function OnPerformanceThresholdExceeded *
struct AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms
{
	float CurrentFPS;
	float TargetFPS;
};
static FName NAME_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded = FName(TEXT("OnPerformanceThresholdExceeded"));
void UAuracronAdvancedRenderingSystem::OnPerformanceThresholdExceeded(float CurrentFPS, float TargetFPS)
{
	AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms Parms;
	Parms.CurrentFPS=CurrentFPS;
	Parms.TargetFPS=TargetFPS;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when performance threshold is exceeded */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when performance threshold is exceeded" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFPS;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetFPS;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::NewProp_CurrentFPS = { "CurrentFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms, CurrentFPS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::NewProp_TargetFPS = { "TargetFPS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms, TargetFPS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::NewProp_CurrentFPS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::NewProp_TargetFPS,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "OnPerformanceThresholdExceeded", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::PropPointers), sizeof(AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedRenderingSystem_eventOnPerformanceThresholdExceeded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function OnPerformanceThresholdExceeded ***

// ********** Begin Class UAuracronAdvancedRenderingSystem Function OnPlatformProfileChanged *******
struct AuracronAdvancedRenderingSystem_eventOnPlatformProfileChanged_Parms
{
	EPlatformRenderingProfile NewProfile;
};
static FName NAME_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged = FName(TEXT("OnPlatformProfileChanged"));
void UAuracronAdvancedRenderingSystem::OnPlatformProfileChanged(EPlatformRenderingProfile NewProfile)
{
	AuracronAdvancedRenderingSystem_eventOnPlatformProfileChanged_Parms Parms;
	Parms.NewProfile=NewProfile;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when platform profile changes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when platform profile changes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewProfile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewProfile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::NewProp_NewProfile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::NewProp_NewProfile = { "NewProfile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventOnPlatformProfileChanged_Parms, NewProfile), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile, METADATA_PARAMS(0, nullptr) }; // 4171469515
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::NewProp_NewProfile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::NewProp_NewProfile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "OnPlatformProfileChanged", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::PropPointers), sizeof(AuracronAdvancedRenderingSystem_eventOnPlatformProfileChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedRenderingSystem_eventOnPlatformProfileChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function OnPlatformProfileChanged *********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function OnQualityLevelChanged **********
struct AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms
{
	ERenderingQualityLevel OldLevel;
	ERenderingQualityLevel NewLevel;
};
static FName NAME_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged = FName(TEXT("OnQualityLevelChanged"));
void UAuracronAdvancedRenderingSystem::OnQualityLevelChanged(ERenderingQualityLevel OldLevel, ERenderingQualityLevel NewLevel)
{
	AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms Parms;
	Parms.OldLevel=OldLevel;
	Parms.NewLevel=NewLevel;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when quality level changes */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when quality level changes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLevel;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_OldLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_OldLevel = { "OldLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms, OldLevel), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(0, nullptr) }; // 2094509193
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_NewLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms, NewLevel), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(0, nullptr) }; // 2094509193
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_OldLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_OldLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_NewLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "OnQualityLevelChanged", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::PropPointers), sizeof(AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronAdvancedRenderingSystem_eventOnQualityLevelChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function OnQualityLevelChanged ************

// ********** Begin Class UAuracronAdvancedRenderingSystem Function OptimizeForCurrentPlatform *****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Platform Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize for current platform */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize for current platform" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "OptimizeForCurrentPlatform", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execOptimizeForCurrentPlatform)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeForCurrentPlatform();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function OptimizeForCurrentPlatform *******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function OptimizeRenderingPerformance ***
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance Monitoring" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Optimize rendering performance */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Optimize rendering performance" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "OptimizeRenderingPerformance", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execOptimizeRenderingPerformance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeRenderingPerformance();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function OptimizeRenderingPerformance *****

// ********** Begin Class UAuracronAdvancedRenderingSystem Function SetAdaptiveQualityEnabled ******
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics
{
	struct AuracronAdvancedRenderingSystem_eventSetAdaptiveQualityEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Quality Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable adaptive quality scaling */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable adaptive quality scaling" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronAdvancedRenderingSystem_eventSetAdaptiveQualityEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedRenderingSystem_eventSetAdaptiveQualityEnabled_Parms), &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "SetAdaptiveQualityEnabled", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::AuracronAdvancedRenderingSystem_eventSetAdaptiveQualityEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::AuracronAdvancedRenderingSystem_eventSetAdaptiveQualityEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execSetAdaptiveQualityEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAdaptiveQualityEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function SetAdaptiveQualityEnabled ********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function SetPlatformRenderingProfile ****
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics
{
	struct AuracronAdvancedRenderingSystem_eventSetPlatformRenderingProfile_Parms
	{
		EPlatformRenderingProfile Profile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Platform Optimization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set platform rendering profile */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set platform rendering profile" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Profile_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Profile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::NewProp_Profile_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::NewProp_Profile = { "Profile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventSetPlatformRenderingProfile_Parms, Profile), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPlatformRenderingProfile, METADATA_PARAMS(0, nullptr) }; // 4171469515
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::NewProp_Profile_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::NewProp_Profile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "SetPlatformRenderingProfile", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::AuracronAdvancedRenderingSystem_eventSetPlatformRenderingProfile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::AuracronAdvancedRenderingSystem_eventSetPlatformRenderingProfile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execSetPlatformRenderingProfile)
{
	P_GET_ENUM(EPlatformRenderingProfile,Z_Param_Profile);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlatformRenderingProfile(EPlatformRenderingProfile(Z_Param_Profile));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function SetPlatformRenderingProfile ******

// ********** Begin Class UAuracronAdvancedRenderingSystem Function SetRenderingQualityLevel *******
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics
{
	struct AuracronAdvancedRenderingSystem_eventSetRenderingQualityLevel_Parms
	{
		ERenderingQualityLevel QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Quality Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set rendering quality level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set rendering quality level" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventSetRenderingQualityLevel_Parms, QualityLevel), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(0, nullptr) }; // 2094509193
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "SetRenderingQualityLevel", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::AuracronAdvancedRenderingSystem_eventSetRenderingQualityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::AuracronAdvancedRenderingSystem_eventSetRenderingQualityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execSetRenderingQualityLevel)
{
	P_GET_ENUM(ERenderingQualityLevel,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRenderingQualityLevel(ERenderingQualityLevel(Z_Param_QualityLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function SetRenderingQualityLevel *********

// ********** Begin Class UAuracronAdvancedRenderingSystem Function UpdateAdaptiveQuality **********
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Quality Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update adaptive quality */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update adaptive quality" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "UpdateAdaptiveQuality", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execUpdateAdaptiveQuality)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAdaptiveQuality();
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function UpdateAdaptiveQuality ************

// ********** Begin Class UAuracronAdvancedRenderingSystem Function UpdateRenderingSystem **********
struct Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics
{
	struct AuracronAdvancedRenderingSystem_eventUpdateRenderingSystem_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update rendering system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update rendering system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedRenderingSystem_eventUpdateRenderingSystem_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronAdvancedRenderingSystem, nullptr, "UpdateRenderingSystem", Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::AuracronAdvancedRenderingSystem_eventUpdateRenderingSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::AuracronAdvancedRenderingSystem_eventUpdateRenderingSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronAdvancedRenderingSystem::execUpdateRenderingSystem)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRenderingSystem(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronAdvancedRenderingSystem Function UpdateRenderingSystem ************

// ********** Begin Class UAuracronAdvancedRenderingSystem *****************************************
void UAuracronAdvancedRenderingSystem::StaticRegisterNativesUAuracronAdvancedRenderingSystem()
{
	UClass* Class = UAuracronAdvancedRenderingSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyPlatformSpecificSettings", &UAuracronAdvancedRenderingSystem::execApplyPlatformSpecificSettings },
		{ "ApplyRenderingConfiguration", &UAuracronAdvancedRenderingSystem::execApplyRenderingConfiguration },
		{ "CoordinateRenderingSubsystems", &UAuracronAdvancedRenderingSystem::execCoordinateRenderingSubsystems },
		{ "GetCurrentQualityLevel", &UAuracronAdvancedRenderingSystem::execGetCurrentQualityLevel },
		{ "GetCurrentRenderingConfiguration", &UAuracronAdvancedRenderingSystem::execGetCurrentRenderingConfiguration },
		{ "GetRenderingPerformanceMetrics", &UAuracronAdvancedRenderingSystem::execGetRenderingPerformanceMetrics },
		{ "InitializeLumenIntegration", &UAuracronAdvancedRenderingSystem::execInitializeLumenIntegration },
		{ "InitializeNaniteIntegration", &UAuracronAdvancedRenderingSystem::execInitializeNaniteIntegration },
		{ "InitializeRenderingSystem", &UAuracronAdvancedRenderingSystem::execInitializeRenderingSystem },
		{ "InitializeVFXIntegration", &UAuracronAdvancedRenderingSystem::execInitializeVFXIntegration },
		{ "MonitorRenderingPerformance", &UAuracronAdvancedRenderingSystem::execMonitorRenderingPerformance },
		{ "OptimizeForCurrentPlatform", &UAuracronAdvancedRenderingSystem::execOptimizeForCurrentPlatform },
		{ "OptimizeRenderingPerformance", &UAuracronAdvancedRenderingSystem::execOptimizeRenderingPerformance },
		{ "SetAdaptiveQualityEnabled", &UAuracronAdvancedRenderingSystem::execSetAdaptiveQualityEnabled },
		{ "SetPlatformRenderingProfile", &UAuracronAdvancedRenderingSystem::execSetPlatformRenderingProfile },
		{ "SetRenderingQualityLevel", &UAuracronAdvancedRenderingSystem::execSetRenderingQualityLevel },
		{ "UpdateAdaptiveQuality", &UAuracronAdvancedRenderingSystem::execUpdateAdaptiveQuality },
		{ "UpdateRenderingSystem", &UAuracronAdvancedRenderingSystem::execUpdateRenderingSystem },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem;
UClass* UAuracronAdvancedRenderingSystem::GetPrivateStaticClass()
{
	using TClass = UAuracronAdvancedRenderingSystem;
	if (!Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdvancedRenderingSystem"),
			Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.InnerSingleton,
			StaticRegisterNativesUAuracronAdvancedRenderingSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem_NoRegister()
{
	return UAuracronAdvancedRenderingSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Advanced Rendering System\n * \n * Comprehensive rendering coordination system that manages all rendering\n * subsystems (Lumen, Nanite, VFX) for optimal visual quality and performance\n * across multiple platforms.\n */" },
#endif
		{ "IncludePath", "AuracronAdvancedRenderingSystem.h" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Advanced Rendering System\n\nComprehensive rendering coordination system that manages all rendering\nsubsystems (Lumen, Nanite, VFX) for optimal visual quality and performance\nacross multiple platforms." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRenderingConfig_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current rendering configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current rendering configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRenderingSystemEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable rendering system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable rendering system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptiveQualityEnabled_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable adaptive quality */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable adaptive quality" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceMonitoringFrequency_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance monitoring frequency */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring frequency" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPerformanceMetrics_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PerformanceHistory_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance history */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance history" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityHistory_MetaData[] = {
		{ "Category", "Performance State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Quality adjustment history */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quality adjustment history" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedLumenBridge_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Cached References ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Cached References ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedNaniteBridge_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedVFXBridge_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedPerformanceAnalyzer_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedRenderingSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentRenderingConfig;
	static void NewProp_bRenderingSystemEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRenderingSystemEnabled;
	static void NewProp_bAdaptiveQualityEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptiveQualityEnabled;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PerformanceMonitoringFrequency;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentPerformanceMetrics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PerformanceHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PerformanceHistory;
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityHistory_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_QualityHistory;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedLumenBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedNaniteBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedVFXBridge;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedPerformanceAnalyzer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyPlatformSpecificSettings, "ApplyPlatformSpecificSettings" }, // 157639220
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_ApplyRenderingConfiguration, "ApplyRenderingConfiguration" }, // 2407022683
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_CoordinateRenderingSubsystems, "CoordinateRenderingSubsystems" }, // 3063168035
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentQualityLevel, "GetCurrentQualityLevel" }, // 3126849913
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetCurrentRenderingConfiguration, "GetCurrentRenderingConfiguration" }, // 1027832305
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_GetRenderingPerformanceMetrics, "GetRenderingPerformanceMetrics" }, // 4064995679
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeLumenIntegration, "InitializeLumenIntegration" }, // 3140601180
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeNaniteIntegration, "InitializeNaniteIntegration" }, // 3954965657
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeRenderingSystem, "InitializeRenderingSystem" }, // 2241774354
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_InitializeVFXIntegration, "InitializeVFXIntegration" }, // 1773041088
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_MonitorRenderingPerformance, "MonitorRenderingPerformance" }, // 2098546147
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPerformanceThresholdExceeded, "OnPerformanceThresholdExceeded" }, // 463830102
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnPlatformProfileChanged, "OnPlatformProfileChanged" }, // 2610380922
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OnQualityLevelChanged, "OnQualityLevelChanged" }, // 2503914718
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeForCurrentPlatform, "OptimizeForCurrentPlatform" }, // 469601889
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_OptimizeRenderingPerformance, "OptimizeRenderingPerformance" }, // 2424095992
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetAdaptiveQualityEnabled, "SetAdaptiveQualityEnabled" }, // 475110509
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetPlatformRenderingProfile, "SetPlatformRenderingProfile" }, // 2772508243
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_SetRenderingQualityLevel, "SetRenderingQualityLevel" }, // 2564336431
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateAdaptiveQuality, "UpdateAdaptiveQuality" }, // 2500845630
		{ &Z_Construct_UFunction_UAuracronAdvancedRenderingSystem_UpdateRenderingSystem, "UpdateRenderingSystem" }, // 181715080
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronAdvancedRenderingSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CurrentRenderingConfig = { "CurrentRenderingConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CurrentRenderingConfig), Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRenderingConfig_MetaData), NewProp_CurrentRenderingConfig_MetaData) }; // 1121808770
void Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bRenderingSystemEnabled_SetBit(void* Obj)
{
	((UAuracronAdvancedRenderingSystem*)Obj)->bRenderingSystemEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bRenderingSystemEnabled = { "bRenderingSystemEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedRenderingSystem), &Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bRenderingSystemEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRenderingSystemEnabled_MetaData), NewProp_bRenderingSystemEnabled_MetaData) };
void Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bAdaptiveQualityEnabled_SetBit(void* Obj)
{
	((UAuracronAdvancedRenderingSystem*)Obj)->bAdaptiveQualityEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bAdaptiveQualityEnabled = { "bAdaptiveQualityEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronAdvancedRenderingSystem), &Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bAdaptiveQualityEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptiveQualityEnabled_MetaData), NewProp_bAdaptiveQualityEnabled_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceMonitoringFrequency = { "PerformanceMonitoringFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, PerformanceMonitoringFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceMonitoringFrequency_MetaData), NewProp_PerformanceMonitoringFrequency_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CurrentPerformanceMetrics = { "CurrentPerformanceMetrics", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CurrentPerformanceMetrics), Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPerformanceMetrics_MetaData), NewProp_CurrentPerformanceMetrics_MetaData) }; // 822221741
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceHistory_Inner = { "PerformanceHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 822221741
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceHistory = { "PerformanceHistory", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, PerformanceHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PerformanceHistory_MetaData), NewProp_PerformanceHistory_MetaData) }; // 822221741
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory_Inner = { "QualityHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_ERenderingQualityLevel, METADATA_PARAMS(0, nullptr) }; // 2094509193
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory = { "QualityHistory", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, QualityHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityHistory_MetaData), NewProp_QualityHistory_MetaData) }; // 2094509193
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedLumenBridge = { "CachedLumenBridge", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CachedLumenBridge), Z_Construct_UClass_UAuracronLumenBridgeAPI_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedLumenBridge_MetaData), NewProp_CachedLumenBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedNaniteBridge = { "CachedNaniteBridge", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CachedNaniteBridge), Z_Construct_UClass_UAuracronNaniteBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedNaniteBridge_MetaData), NewProp_CachedNaniteBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedVFXBridge = { "CachedVFXBridge", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CachedVFXBridge), Z_Construct_UClass_UAuracronVFXBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedVFXBridge_MetaData), NewProp_CachedVFXBridge_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedPerformanceAnalyzer = { "CachedPerformanceAnalyzer", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronAdvancedRenderingSystem, CachedPerformanceAnalyzer), Z_Construct_UClass_UAuracronAdvancedPerformanceAnalyzer_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedPerformanceAnalyzer_MetaData), NewProp_CachedPerformanceAnalyzer_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CurrentRenderingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bRenderingSystemEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_bAdaptiveQualityEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceMonitoringFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CurrentPerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_PerformanceHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_QualityHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedLumenBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedNaniteBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedVFXBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::NewProp_CachedPerformanceAnalyzer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::ClassParams = {
	&UAuracronAdvancedRenderingSystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronAdvancedRenderingSystem()
{
	if (!Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.OuterSingleton, Z_Construct_UClass_UAuracronAdvancedRenderingSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem.OuterSingleton;
}
UAuracronAdvancedRenderingSystem::UAuracronAdvancedRenderingSystem() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronAdvancedRenderingSystem);
UAuracronAdvancedRenderingSystem::~UAuracronAdvancedRenderingSystem() {}
// ********** End Class UAuracronAdvancedRenderingSystem *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERenderingQualityLevel_StaticEnum, TEXT("ERenderingQualityLevel"), &Z_Registration_Info_UEnum_ERenderingQualityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2094509193U) },
		{ ERenderingOptimizationStrategy_StaticEnum, TEXT("ERenderingOptimizationStrategy"), &Z_Registration_Info_UEnum_ERenderingOptimizationStrategy, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2758857315U) },
		{ EPlatformRenderingProfile_StaticEnum, TEXT("EPlatformRenderingProfile"), &Z_Registration_Info_UEnum_EPlatformRenderingProfile, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4171469515U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronAdvancedRenderingConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronAdvancedRenderingConfig_Statics::NewStructOps, TEXT("AuracronAdvancedRenderingConfig"), &Z_Registration_Info_UScriptStruct_FAuracronAdvancedRenderingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAdvancedRenderingConfig), 1121808770U) },
		{ FAuracronRenderingPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FAuracronRenderingPerformanceMetrics_Statics::NewStructOps, TEXT("AuracronRenderingPerformanceMetrics"), &Z_Registration_Info_UScriptStruct_FAuracronRenderingPerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRenderingPerformanceMetrics), 822221741U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronAdvancedRenderingSystem, UAuracronAdvancedRenderingSystem::StaticClass, TEXT("UAuracronAdvancedRenderingSystem"), &Z_Registration_Info_UClass_UAuracronAdvancedRenderingSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronAdvancedRenderingSystem), 849308721U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_1233556354(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronAdvancedRenderingSystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
