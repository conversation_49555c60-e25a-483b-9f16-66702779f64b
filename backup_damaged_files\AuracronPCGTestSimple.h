﻿#pragma once

// UE 5.6 Compatible - Minimal test header
#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "AuracronPCGTestSimple.generated.h"

public:
    UAuracronPCGTestSimple();

    UFUNCTION(BlueprintCallable, Category = "Auracron PCG Test")
    FString GetTestMessage() const;

private:
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Test", meta = (AllowPrivateAccess = "true"))
    FString TestMessage;
};
