// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronCombatBridge.h"

#ifdef AURACRONCOMBATBRIDGE_AuracronCombatBridge_generated_h
#error "AuracronCombatBridge.generated.h already included, missing '#pragma once' in AuracronCombatBridge.h"
#endif
#define AURACRONCOMBATBRIDGE_AuracronCombatBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class AActor;
enum class EAuracronAICombatBehavior : uint8;
enum class EAuracronCombatLayer : uint8;
enum class EAuracronComboType : uint8;
enum class EAuracronDamageType : uint8;
enum class EAuracronElementalType : uint8;
enum class EAuracronElementalType : uint8; 
struct FAuracronAdvancedDestructionConfig;
struct FAuracronAICombatConfig;
struct FAuracronCombatAnalytics;
struct FAuracronCombatEffectsConfiguration;
struct FAuracronComboConfig;
struct FAuracronDamageConfiguration;
struct FAuracronElementalDamageConfig;
struct FAuracronEnhancedInputConfig;
struct FAuracronTargetingConfiguration;
struct FAuracronTargetingResult;

// ********** Begin ScriptStruct FAuracronDamageConfiguration **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_189_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronDamageConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronDamageConfiguration;
// ********** End ScriptStruct FAuracronDamageConfiguration ****************************************

// ********** Begin ScriptStruct FAuracronTargetingConfiguration ***********************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_254_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTargetingConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTargetingConfiguration;
// ********** End ScriptStruct FAuracronTargetingConfiguration *************************************

// ********** Begin ScriptStruct FAuracronTargetingResult ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_331_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronTargetingResult_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronTargetingResult;
// ********** End ScriptStruct FAuracronTargetingResult ********************************************

// ********** Begin ScriptStruct FAuracronCombatEffectsConfiguration *******************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_372_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCombatEffectsConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCombatEffectsConfiguration;
// ********** End ScriptStruct FAuracronCombatEffectsConfiguration *********************************

// ********** Begin ScriptStruct FAuracronEnhancedInputConfig **************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_437_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronEnhancedInputConfig;
// ********** End ScriptStruct FAuracronEnhancedInputConfig ****************************************

// ********** Begin ScriptStruct FAuracronAICombatConfig *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_484_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAICombatConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAICombatConfig;
// ********** End ScriptStruct FAuracronAICombatConfig *********************************************

// ********** Begin ScriptStruct FAuracronElementalDamageConfig ************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_535_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronElementalDamageConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronElementalDamageConfig;
// ********** End ScriptStruct FAuracronElementalDamageConfig **************************************

// ********** Begin ScriptStruct FAuracronCombatAnalytics ******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_581_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCombatAnalytics_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCombatAnalytics;
// ********** End ScriptStruct FAuracronCombatAnalytics ********************************************

// ********** Begin ScriptStruct FAuracronComboConfig **********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_649_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronComboConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronComboConfig;
// ********** End ScriptStruct FAuracronComboConfig ************************************************

// ********** Begin ScriptStruct FAuracronAdvancedDestructionConfig ********************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_697_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAdvancedDestructionConfig;
// ********** End ScriptStruct FAuracronAdvancedDestructionConfig **********************************

// ********** Begin Delegate FOnTargetingExecuted **************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1106_DELEGATE \
static void FOnTargetingExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnTargetingExecuted, FAuracronTargetingResult Result, FAuracronTargetingConfiguration Config);


// ********** End Delegate FOnTargetingExecuted ****************************************************

// ********** Begin Delegate FOnDamageApplied ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1111_DELEGATE \
static void FOnDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnDamageApplied, AActor* TargetActor, float DamageAmount, EAuracronDamageType DamageType);


// ********** End Delegate FOnDamageApplied ********************************************************

// ********** Begin Delegate FOnCombatLayerChanged *************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1116_DELEGATE \
static void FOnCombatLayerChanged_DelegateWrapper(const FMulticastScriptDelegate& OnCombatLayerChanged, EAuracronCombatLayer OldLayer, EAuracronCombatLayer NewLayer);


// ********** End Delegate FOnCombatLayerChanged ***************************************************

// ********** Begin Delegate FOnCombatEffectsSpawned ***********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1121_DELEGATE \
static void FOnCombatEffectsSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnCombatEffectsSpawned, FVector Location, FAuracronCombatEffectsConfiguration EffectsConfig);


// ********** End Delegate FOnCombatEffectsSpawned *************************************************

// ********** Begin Delegate FOnComboExecuted ******************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1126_DELEGATE \
static void FOnComboExecuted_DelegateWrapper(const FMulticastScriptDelegate& OnComboExecuted, EAuracronComboType ComboType, int32 ComboStep, float DamageMultiplier);


// ********** End Delegate FOnComboExecuted ********************************************************

// ********** Begin Delegate FOnElementalDamageApplied *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1131_DELEGATE \
static void FOnElementalDamageApplied_DelegateWrapper(const FMulticastScriptDelegate& OnElementalDamageApplied, AActor* TargetActor, EAuracronElementalType ElementType, float Damage, bool bStatusEffectApplied);


// ********** End Delegate FOnElementalDamageApplied ***********************************************

// ********** Begin Delegate FOnAICombatDecision ***************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1136_DELEGATE \
static void FOnAICombatDecision_DelegateWrapper(const FMulticastScriptDelegate& OnAICombatDecision, EAuracronAICombatBehavior BehaviorType, const FString& DecisionType, float ConfidenceLevel);


// ********** End Delegate FOnAICombatDecision *****************************************************

// ********** Begin Delegate FOnAdvancedDestruction ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1141_DELEGATE \
static void FOnAdvancedDestruction_DelegateWrapper(const FMulticastScriptDelegate& OnAdvancedDestruction, FVector Location, float DestructionForce, int32 AffectedObjects);


// ********** End Delegate FOnAdvancedDestruction **************************************************

// ********** Begin Delegate FOnCombatAnalyticsUpdated *********************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_1146_DELEGATE \
static void FOnCombatAnalyticsUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnCombatAnalyticsUpdated, FAuracronCombatAnalytics Analytics, float EfficiencyScore);


// ********** End Delegate FOnCombatAnalyticsUpdated ***********************************************

// ********** Begin Class UAuracronCombatBridge ****************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnRep_CurrentCombatLayer); \
	DECLARE_FUNCTION(execSimulateDestructionPhysics); \
	DECLARE_FUNCTION(execApplyProceduralDamage); \
	DECLARE_FUNCTION(execCreateAdvancedDestruction); \
	DECLARE_FUNCTION(execExportCombatData); \
	DECLARE_FUNCTION(execCalculateCombatEfficiency); \
	DECLARE_FUNCTION(execGetCombatAnalytics); \
	DECLARE_FUNCTION(execUpdateCombatAnalytics); \
	DECLARE_FUNCTION(execGetElementalResistances); \
	DECLARE_FUNCTION(execApplyElementalStatusEffect); \
	DECLARE_FUNCTION(execCalculateElementalEffectiveness); \
	DECLARE_FUNCTION(execApplyElementalDamage); \
	DECLARE_FUNCTION(execAdaptAIBehavior); \
	DECLARE_FUNCTION(execExecuteAICombatDecision); \
	DECLARE_FUNCTION(execUpdateAICombatState); \
	DECLARE_FUNCTION(execInitializeAICombatBehavior); \
	DECLARE_FUNCTION(execResetComboChain); \
	DECLARE_FUNCTION(execExecuteComboSequence); \
	DECLARE_FUNCTION(execProcessComboInput); \
	DECLARE_FUNCTION(execSetupCombatInputActions); \
	DECLARE_FUNCTION(execInitializeEnhancedInputSystem); \
	DECLARE_FUNCTION(execCreateChaosExplosion); \
	DECLARE_FUNCTION(execApplyFieldSystemDestruction); \
	DECLARE_FUNCTION(execSpawnCombatEffects); \
	DECLARE_FUNCTION(execGetActorCombatLayer); \
	DECLARE_FUNCTION(execIsValidTarget); \
	DECLARE_FUNCTION(execCalculateFinalDamage); \
	DECLARE_FUNCTION(execGetTargetsInVerticalColumn); \
	DECLARE_FUNCTION(execGetTargetsInCone); \
	DECLARE_FUNCTION(execGetTargetsInRadius); \
	DECLARE_FUNCTION(execCheckLineOfSight); \
	DECLARE_FUNCTION(execApplyAreaDamage); \
	DECLARE_FUNCTION(execApplyDamageToTarget); \
	DECLARE_FUNCTION(execExecuteTargeting);


AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronCombatBridge(); \
	friend struct Z_Construct_UClass_UAuracronCombatBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronCombatBridge, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AuracronCombatBridge"), Z_Construct_UClass_UAuracronCombatBridge_NoRegister) \
	DECLARE_SERIALIZER(UAuracronCombatBridge) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		CurrentCombatLayer=NETFIELD_REP_START, \
		NETFIELD_REP_END=CurrentCombatLayer	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronCombatBridge(UAuracronCombatBridge&&) = delete; \
	UAuracronCombatBridge(const UAuracronCombatBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronCombatBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronCombatBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronCombatBridge) \
	NO_API virtual ~UAuracronCombatBridge();


#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_738_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h_741_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronCombatBridge;

// ********** End Class UAuracronCombatBridge ******************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronCombatBridge_h

// ********** Begin Enum EAuracronDamageType *******************************************************
#define FOREACH_ENUM_EAURACRONDAMAGETYPE(op) \
	op(EAuracronDamageType::None) \
	op(EAuracronDamageType::Physical) \
	op(EAuracronDamageType::Magical) \
	op(EAuracronDamageType::TrueDamage) \
	op(EAuracronDamageType::Healing) \
	op(EAuracronDamageType::Shield) \
	op(EAuracronDamageType::Percentage) \
	op(EAuracronDamageType::OverTime) \
	op(EAuracronDamageType::Area) 

enum class EAuracronDamageType : uint8;
template<> struct TIsUEnumClass<EAuracronDamageType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronDamageType>();
// ********** End Enum EAuracronDamageType *********************************************************

// ********** Begin Enum EAuracronCombatLayer ******************************************************
#define FOREACH_ENUM_EAURACRONCOMBATLAYER(op) \
	op(EAuracronCombatLayer::Surface) \
	op(EAuracronCombatLayer::Sky) \
	op(EAuracronCombatLayer::Underground) \
	op(EAuracronCombatLayer::All) 

enum class EAuracronCombatLayer : uint8;
template<> struct TIsUEnumClass<EAuracronCombatLayer> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronCombatLayer>();
// ********** End Enum EAuracronCombatLayer ********************************************************

// ********** Begin Enum EAuracronTargetingType ****************************************************
#define FOREACH_ENUM_EAURACRONTARGETINGTYPE(op) \
	op(EAuracronTargetingType::None) \
	op(EAuracronTargetingType::SingleTarget) \
	op(EAuracronTargetingType::LineTrace) \
	op(EAuracronTargetingType::AreaOfEffect) \
	op(EAuracronTargetingType::Cone) \
	op(EAuracronTargetingType::Sphere) \
	op(EAuracronTargetingType::Box) \
	op(EAuracronTargetingType::Cylinder) \
	op(EAuracronTargetingType::VerticalColumn) \
	op(EAuracronTargetingType::CrossLayer) \
	op(EAuracronTargetingType::GroundTarget) \
	op(EAuracronTargetingType::SkyTarget) \
	op(EAuracronTargetingType::UndergroundTarget) \
	op(EAuracronTargetingType::SmartTarget) \
	op(EAuracronTargetingType::PredictiveTarget) \
	op(EAuracronTargetingType::ChainTarget) 

enum class EAuracronTargetingType : uint8;
template<> struct TIsUEnumClass<EAuracronTargetingType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronTargetingType>();
// ********** End Enum EAuracronTargetingType ******************************************************

// ********** Begin Enum EAuracronElementalType ****************************************************
#define FOREACH_ENUM_EAURACRONELEMENTALTYPE(op) \
	op(EAuracronElementalType::None) \
	op(EAuracronElementalType::Fire) \
	op(EAuracronElementalType::Water) \
	op(EAuracronElementalType::Earth) \
	op(EAuracronElementalType::Air) \
	op(EAuracronElementalType::Lightning) \
	op(EAuracronElementalType::Ice) \
	op(EAuracronElementalType::Poison) \
	op(EAuracronElementalType::Shadow) \
	op(EAuracronElementalType::Light) \
	op(EAuracronElementalType::Chaos) \
	op(EAuracronElementalType::Order) \
	op(EAuracronElementalType::Void) 

enum class EAuracronElementalType : uint8;
template<> struct TIsUEnumClass<EAuracronElementalType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronElementalType>();
// ********** End Enum EAuracronElementalType ******************************************************

// ********** Begin Enum EAuracronAICombatBehavior *************************************************
#define FOREACH_ENUM_EAURACRONAICOMBATBEHAVIOR(op) \
	op(EAuracronAICombatBehavior::Passive) \
	op(EAuracronAICombatBehavior::Defensive) \
	op(EAuracronAICombatBehavior::Aggressive) \
	op(EAuracronAICombatBehavior::Tactical) \
	op(EAuracronAICombatBehavior::Berserker) \
	op(EAuracronAICombatBehavior::Support) \
	op(EAuracronAICombatBehavior::Assassin) \
	op(EAuracronAICombatBehavior::Tank) \
	op(EAuracronAICombatBehavior::Adaptive) \
	op(EAuracronAICombatBehavior::Learning) 

enum class EAuracronAICombatBehavior : uint8;
template<> struct TIsUEnumClass<EAuracronAICombatBehavior> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronAICombatBehavior>();
// ********** End Enum EAuracronAICombatBehavior ***************************************************

// ********** Begin Enum EAuracronComboType ********************************************************
#define FOREACH_ENUM_EAURACRONCOMBOTYPE(op) \
	op(EAuracronComboType::None) \
	op(EAuracronComboType::Light) \
	op(EAuracronComboType::Heavy) \
	op(EAuracronComboType::Special) \
	op(EAuracronComboType::Ultimate) \
	op(EAuracronComboType::Elemental) \
	op(EAuracronComboType::Chain) \
	op(EAuracronComboType::Finisher) 

enum class EAuracronComboType : uint8;
template<> struct TIsUEnumClass<EAuracronComboType> { enum { Value = true }; };
template<> AURACRONCOMBATBRIDGE_API UEnum* StaticEnum<EAuracronComboType>();
// ********** End Enum EAuracronComboType **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
