// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronMultiplayerExample.h"
#include "AuracronAdvancedNetworkingCoordinator.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronMultiplayerExample() {}

// ********** Begin Cross Module References ********************************************************
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerExample();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerExample_NoRegister();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController_NoRegister();
AURACRONNETWORKINGBRIDGE_API UClass* Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister();
AURACRONNETWORKINGBRIDGE_API UEnum* Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig();
AURACRONNETWORKINGBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
ENGINE_API UClass* Z_Construct_UClass_APlayerController();
UPackage* Z_Construct_UPackage__Script_AuracronNetworkingBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAuracronMultiplayerExample Function CreateCompetitiveSession ************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create a competitive multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create a competitive multiplayer session" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "CreateCompetitiveSession", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execCreateCompetitiveSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateCompetitiveSession();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function CreateCompetitiveSession **************

// ********** Begin Class AAuracronMultiplayerExample Function CreateCooperativeSession ************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create a cooperative multiplayer session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create a cooperative multiplayer session" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "CreateCooperativeSession", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execCreateCooperativeSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateCooperativeSession();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function CreateCooperativeSession **************

// ********** Begin Class AAuracronMultiplayerExample Function InitializeAntiCheat *****************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize anti-cheat system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize anti-cheat system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "InitializeAntiCheat", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execInitializeAntiCheat)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAntiCheat();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function InitializeAntiCheat *******************

// ********** Begin Class AAuracronMultiplayerExample Function JoinSession *************************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics
{
	struct AuracronMultiplayerExample_eventJoinSession_Parms
	{
		FString SessionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Join an existing session */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Join an existing session" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventJoinSession_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::NewProp_SessionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "JoinSession", Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::AuracronMultiplayerExample_eventJoinSession_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::AuracronMultiplayerExample_eventJoinSession_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execJoinSession)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SessionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->JoinSession(Z_Param_SessionID);
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function JoinSession ***************************

// ********** Begin Class AAuracronMultiplayerExample Function OnCheatDetected *********************
struct AuracronMultiplayerExample_eventOnCheatDetected_Parms
{
	FString PlayerID;
	FString CheatType;
};
static FName NAME_AAuracronMultiplayerExample_OnCheatDetected = FName(TEXT("OnCheatDetected"));
void AAuracronMultiplayerExample::OnCheatDetected(const FString& PlayerID, const FString& CheatType)
{
	AuracronMultiplayerExample_eventOnCheatDetected_Parms Parms;
	Parms.PlayerID=PlayerID;
	Parms.CheatType=CheatType;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronMultiplayerExample_OnCheatDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when anti-cheat violation is detected */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when anti-cheat violation is detected" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheatType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CheatType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventOnCheatDetected_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::NewProp_CheatType = { "CheatType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventOnCheatDetected_Parms, CheatType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheatType_MetaData), NewProp_CheatType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::NewProp_CheatType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "OnCheatDetected", Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::PropPointers), sizeof(AuracronMultiplayerExample_eventOnCheatDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronMultiplayerExample_eventOnCheatDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronMultiplayerExample Function OnCheatDetected ***********************

// ********** Begin Class AAuracronMultiplayerExample Function OnPlayerJoined **********************
struct AuracronMultiplayerExample_eventOnPlayerJoined_Parms
{
	FString PlayerID;
};
static FName NAME_AAuracronMultiplayerExample_OnPlayerJoined = FName(TEXT("OnPlayerJoined"));
void AAuracronMultiplayerExample::OnPlayerJoined(const FString& PlayerID)
{
	AuracronMultiplayerExample_eventOnPlayerJoined_Parms Parms;
	Parms.PlayerID=PlayerID;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronMultiplayerExample_OnPlayerJoined);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when player joins */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when player joins" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventOnPlayerJoined_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::NewProp_PlayerID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "OnPlayerJoined", Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::PropPointers), sizeof(AuracronMultiplayerExample_eventOnPlayerJoined_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronMultiplayerExample_eventOnPlayerJoined_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronMultiplayerExample Function OnPlayerJoined ************************

// ********** Begin Class AAuracronMultiplayerExample Function OnSessionCreated ********************
struct AuracronMultiplayerExample_eventOnSessionCreated_Parms
{
	FString SessionID;
};
static FName NAME_AAuracronMultiplayerExample_OnSessionCreated = FName(TEXT("OnSessionCreated"));
void AAuracronMultiplayerExample::OnSessionCreated(const FString& SessionID)
{
	AuracronMultiplayerExample_eventOnSessionCreated_Parms Parms;
	Parms.SessionID=SessionID;
	UFunction* Func = FindFunctionChecked(NAME_AAuracronMultiplayerExample_OnSessionCreated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when session is created */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when session is created" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventOnSessionCreated_Parms, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::NewProp_SessionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "OnSessionCreated", Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::PropPointers), sizeof(AuracronMultiplayerExample_eventOnSessionCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronMultiplayerExample_eventOnSessionCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AAuracronMultiplayerExample Function OnSessionCreated **********************

// ********** Begin Class AAuracronMultiplayerExample Function RegisterGameObjectsForReplication ***
struct Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Register important game objects for Iris replication */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Register important game objects for Iris replication" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "RegisterGameObjectsForReplication", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execRegisterGameObjectsForReplication)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterGameObjectsForReplication();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function RegisterGameObjectsForReplication *****

// ********** Begin Class AAuracronMultiplayerExample Function SetupIrisReplication ****************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Setup Iris replication for game objects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup Iris replication for game objects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "SetupIrisReplication", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execSetupIrisReplication)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupIrisReplication();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function SetupIrisReplication ******************

// ********** Begin Class AAuracronMultiplayerExample Function SetupServerAuthority ****************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Setup server authority for game systems */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup server authority for game systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "SetupServerAuthority", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execSetupServerAuthority)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupServerAuthority();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function SetupServerAuthority ******************

// ********** Begin Class AAuracronMultiplayerExample Function SyncGameStateAcrossNetwork **********
struct Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sync game state across network */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sync game state across network" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "SyncGameStateAcrossNetwork", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execSyncGameStateAcrossNetwork)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SyncGameStateAcrossNetwork();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function SyncGameStateAcrossNetwork ************

// ********** Begin Class AAuracronMultiplayerExample Function ValidatePlayerAction ****************
struct Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics
{
	struct AuracronMultiplayerExample_eventValidatePlayerAction_Parms
	{
		FString PlayerID;
		FString ActionType;
		TMap<FString,FString> ActionData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Example" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate a player action */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate a player action" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventValidatePlayerAction_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventValidatePlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerExample_eventValidatePlayerAction_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
void Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronMultiplayerExample_eventValidatePlayerAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMultiplayerExample_eventValidatePlayerAction_Parms), &Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ActionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerExample, nullptr, "ValidatePlayerAction", Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::AuracronMultiplayerExample_eventValidatePlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::AuracronMultiplayerExample_eventValidatePlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerExample::execValidatePlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidatePlayerAction(Z_Param_PlayerID,Z_Param_ActionType,Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerExample Function ValidatePlayerAction ******************

// ********** Begin Class AAuracronMultiplayerExample **********************************************
void AAuracronMultiplayerExample::StaticRegisterNativesAAuracronMultiplayerExample()
{
	UClass* Class = AAuracronMultiplayerExample::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateCompetitiveSession", &AAuracronMultiplayerExample::execCreateCompetitiveSession },
		{ "CreateCooperativeSession", &AAuracronMultiplayerExample::execCreateCooperativeSession },
		{ "InitializeAntiCheat", &AAuracronMultiplayerExample::execInitializeAntiCheat },
		{ "JoinSession", &AAuracronMultiplayerExample::execJoinSession },
		{ "RegisterGameObjectsForReplication", &AAuracronMultiplayerExample::execRegisterGameObjectsForReplication },
		{ "SetupIrisReplication", &AAuracronMultiplayerExample::execSetupIrisReplication },
		{ "SetupServerAuthority", &AAuracronMultiplayerExample::execSetupServerAuthority },
		{ "SyncGameStateAcrossNetwork", &AAuracronMultiplayerExample::execSyncGameStateAcrossNetwork },
		{ "ValidatePlayerAction", &AAuracronMultiplayerExample::execValidatePlayerAction },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronMultiplayerExample;
UClass* AAuracronMultiplayerExample::GetPrivateStaticClass()
{
	using TClass = AAuracronMultiplayerExample;
	if (!Z_Registration_Info_UClass_AAuracronMultiplayerExample.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMultiplayerExample"),
			Z_Registration_Info_UClass_AAuracronMultiplayerExample.InnerSingleton,
			StaticRegisterNativesAAuracronMultiplayerExample,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronMultiplayerExample.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronMultiplayerExample_NoRegister()
{
	return AAuracronMultiplayerExample::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronMultiplayerExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Example game mode demonstrating advanced multiplayer networking\n */" },
#endif
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "AuracronMultiplayerExample.h" },
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example game mode demonstrating advanced multiplayer networking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkingCoordinator_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reference to the networking coordinator */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reference to the networking coordinator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionConfig_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current session configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current session configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IrisConfig_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iris replication configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iris replication configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AntiCheatLevel_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Anti-cheat level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Anti-cheat level" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NetworkingCoordinator;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SessionConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_IrisConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AntiCheatLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AntiCheatLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCompetitiveSession, "CreateCompetitiveSession" }, // 1531604109
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_CreateCooperativeSession, "CreateCooperativeSession" }, // 3937237862
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_InitializeAntiCheat, "InitializeAntiCheat" }, // 4021670186
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_JoinSession, "JoinSession" }, // 3231514133
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_OnCheatDetected, "OnCheatDetected" }, // 885814091
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_OnPlayerJoined, "OnPlayerJoined" }, // 4034530547
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_OnSessionCreated, "OnSessionCreated" }, // 2013823193
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_RegisterGameObjectsForReplication, "RegisterGameObjectsForReplication" }, // 3581623586
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_SetupIrisReplication, "SetupIrisReplication" }, // 2740656945
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_SetupServerAuthority, "SetupServerAuthority" }, // 2798741314
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_SyncGameStateAcrossNetwork, "SyncGameStateAcrossNetwork" }, // 2450694941
		{ &Z_Construct_UFunction_AAuracronMultiplayerExample_ValidatePlayerAction, "ValidatePlayerAction" }, // 447405668
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronMultiplayerExample>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_NetworkingCoordinator = { "NetworkingCoordinator", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerExample, NetworkingCoordinator), Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkingCoordinator_MetaData), NewProp_NetworkingCoordinator_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_SessionConfig = { "SessionConfig", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerExample, SessionConfig), Z_Construct_UScriptStruct_FAuracronMultiplayerSessionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionConfig_MetaData), NewProp_SessionConfig_MetaData) }; // 729078944
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_IrisConfig = { "IrisConfig", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerExample, IrisConfig), Z_Construct_UScriptStruct_FAuracronIrisReplicationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IrisConfig_MetaData), NewProp_IrisConfig_MetaData) }; // 1594044851
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_AntiCheatLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_AntiCheatLevel = { "AntiCheatLevel", nullptr, (EPropertyFlags)0x0040000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerExample, AntiCheatLevel), Z_Construct_UEnum_AuracronNetworkingBridge_EAuracronAntiCheatLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AntiCheatLevel_MetaData), NewProp_AntiCheatLevel_MetaData) }; // 1227517794
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronMultiplayerExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_NetworkingCoordinator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_SessionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_IrisConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_AntiCheatLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerExample_Statics::NewProp_AntiCheatLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerExample_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronMultiplayerExample_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerExample_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronMultiplayerExample_Statics::ClassParams = {
	&AAuracronMultiplayerExample::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronMultiplayerExample_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerExample_Statics::PropPointers),
	0,
	0x009003ACu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerExample_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronMultiplayerExample_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronMultiplayerExample()
{
	if (!Z_Registration_Info_UClass_AAuracronMultiplayerExample.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronMultiplayerExample.OuterSingleton, Z_Construct_UClass_AAuracronMultiplayerExample_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronMultiplayerExample.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronMultiplayerExample);
AAuracronMultiplayerExample::~AAuracronMultiplayerExample() {}
// ********** End Class AAuracronMultiplayerExample ************************************************

// ********** Begin Class AAuracronMultiplayerPlayerController Function EnableClientPrediction *****
struct Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enable client-side prediction for this player */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable client-side prediction for this player" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerPlayerController, nullptr, "EnableClientPrediction", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerPlayerController::execEnableClientPrediction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableClientPrediction();
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerPlayerController Function EnableClientPrediction *******

// ********** Begin Class AAuracronMultiplayerPlayerController Function HandleServerReconciliation *
struct Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics
{
	struct AuracronMultiplayerPlayerController_eventHandleServerReconciliation_Parms
	{
		FVector ServerPosition;
		float Timestamp;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle server reconciliation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle server reconciliation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ServerPosition_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ServerPosition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::NewProp_ServerPosition = { "ServerPosition", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerPlayerController_eventHandleServerReconciliation_Parms, ServerPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ServerPosition_MetaData), NewProp_ServerPosition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerPlayerController_eventHandleServerReconciliation_Parms, Timestamp), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::NewProp_ServerPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerPlayerController, nullptr, "HandleServerReconciliation", Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::AuracronMultiplayerPlayerController_eventHandleServerReconciliation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::AuracronMultiplayerPlayerController_eventHandleServerReconciliation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerPlayerController::execHandleServerReconciliation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ServerPosition);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Timestamp);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandleServerReconciliation(Z_Param_Out_ServerPosition,Z_Param_Timestamp);
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerPlayerController Function HandleServerReconciliation ***

// ********** Begin Class AAuracronMultiplayerPlayerController Function ReceiveValidatedAction *****
struct Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics
{
	struct AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms
	{
		FString ActionType;
		bool bWasValid;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Receive validated action from server */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Receive validated action from server" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static void NewProp_bWasValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasValid;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
void Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_bWasValid_SetBit(void* Obj)
{
	((AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms*)Obj)->bWasValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_bWasValid = { "bWasValid", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms), &Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_bWasValid_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::NewProp_bWasValid,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerPlayerController, nullptr, "ReceiveValidatedAction", Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::AuracronMultiplayerPlayerController_eventReceiveValidatedAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerPlayerController::execReceiveValidatedAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_UBOOL(Z_Param_bWasValid);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReceiveValidatedAction(Z_Param_ActionType,Z_Param_bWasValid);
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerPlayerController Function ReceiveValidatedAction *******

// ********** Begin Class AAuracronMultiplayerPlayerController Function SendActionToServer *********
struct Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics
{
	struct AuracronMultiplayerPlayerController_eventSendActionToServer_Parms
	{
		FString ActionType;
		TMap<FString,FString> ActionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Multiplayer Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Send action to server for validation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Send action to server for validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerPlayerController_eventSendActionToServer_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData_ValueProp = { "ActionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData_Key_KeyProp = { "ActionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData = { "ActionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronMultiplayerPlayerController_eventSendActionToServer_Parms, ActionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionData_MetaData), NewProp_ActionData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::NewProp_ActionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronMultiplayerPlayerController, nullptr, "SendActionToServer", Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::AuracronMultiplayerPlayerController_eventSendActionToServer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04480401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::AuracronMultiplayerPlayerController_eventSendActionToServer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronMultiplayerPlayerController::execSendActionToServer)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_ActionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SendActionToServer(Z_Param_ActionType,Z_Param_Out_ActionData);
	P_NATIVE_END;
}
// ********** End Class AAuracronMultiplayerPlayerController Function SendActionToServer ***********

// ********** Begin Class AAuracronMultiplayerPlayerController *************************************
void AAuracronMultiplayerPlayerController::StaticRegisterNativesAAuracronMultiplayerPlayerController()
{
	UClass* Class = AAuracronMultiplayerPlayerController::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "EnableClientPrediction", &AAuracronMultiplayerPlayerController::execEnableClientPrediction },
		{ "HandleServerReconciliation", &AAuracronMultiplayerPlayerController::execHandleServerReconciliation },
		{ "ReceiveValidatedAction", &AAuracronMultiplayerPlayerController::execReceiveValidatedAction },
		{ "SendActionToServer", &AAuracronMultiplayerPlayerController::execSendActionToServer },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController;
UClass* AAuracronMultiplayerPlayerController::GetPrivateStaticClass()
{
	using TClass = AAuracronMultiplayerPlayerController;
	if (!Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronMultiplayerPlayerController"),
			Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.InnerSingleton,
			StaticRegisterNativesAAuracronMultiplayerPlayerController,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController_NoRegister()
{
	return AAuracronMultiplayerPlayerController::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Example player controller with networking features\n */" },
#endif
		{ "HideCategories", "Collision Rendering Transformation" },
		{ "IncludePath", "AuracronMultiplayerExample.h" },
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example player controller with networking features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NetworkingCoordinator_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reference to networking coordinator */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reference to networking coordinator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerNetworkMetrics_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player's network metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronMultiplayerExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player's network metrics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NetworkingCoordinator;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerNetworkMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronMultiplayerPlayerController_EnableClientPrediction, "EnableClientPrediction" }, // 638266921
		{ &Z_Construct_UFunction_AAuracronMultiplayerPlayerController_HandleServerReconciliation, "HandleServerReconciliation" }, // 4029769404
		{ &Z_Construct_UFunction_AAuracronMultiplayerPlayerController_ReceiveValidatedAction, "ReceiveValidatedAction" }, // 4257704010
		{ &Z_Construct_UFunction_AAuracronMultiplayerPlayerController_SendActionToServer, "SendActionToServer" }, // 1763428270
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronMultiplayerPlayerController>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::NewProp_NetworkingCoordinator = { "NetworkingCoordinator", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerPlayerController, NetworkingCoordinator), Z_Construct_UClass_UAuracronAdvancedNetworkingCoordinator_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NetworkingCoordinator_MetaData), NewProp_NetworkingCoordinator_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::NewProp_PlayerNetworkMetrics = { "PlayerNetworkMetrics", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronMultiplayerPlayerController, PlayerNetworkMetrics), Z_Construct_UScriptStruct_FAuracronNetworkQualityMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerNetworkMetrics_MetaData), NewProp_PlayerNetworkMetrics_MetaData) }; // 1513915643
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::NewProp_NetworkingCoordinator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::NewProp_PlayerNetworkMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APlayerController,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronNetworkingBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::ClassParams = {
	&AAuracronMultiplayerPlayerController::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::PropPointers),
	0,
	0x009003A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronMultiplayerPlayerController()
{
	if (!Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.OuterSingleton, Z_Construct_UClass_AAuracronMultiplayerPlayerController_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronMultiplayerPlayerController);
AAuracronMultiplayerPlayerController::~AAuracronMultiplayerPlayerController() {}
// ********** End Class AAuracronMultiplayerPlayerController ***************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h__Script_AuracronNetworkingBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronMultiplayerExample, AAuracronMultiplayerExample::StaticClass, TEXT("AAuracronMultiplayerExample"), &Z_Registration_Info_UClass_AAuracronMultiplayerExample, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronMultiplayerExample), 859248996U) },
		{ Z_Construct_UClass_AAuracronMultiplayerPlayerController, AAuracronMultiplayerPlayerController::StaticClass, TEXT("AAuracronMultiplayerPlayerController"), &Z_Registration_Info_UClass_AAuracronMultiplayerPlayerController, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronMultiplayerPlayerController), 2860436001U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h__Script_AuracronNetworkingBridge_3593674858(TEXT("/Script/AuracronNetworkingBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h__Script_AuracronNetworkingBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronNetworkingBridge_Public_AuracronMultiplayerExample_h__Script_AuracronNetworkingBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
