// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronLayerComponent.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronLayerComponent() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerInteraction();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerModifiers();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerMovement();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronLayerInteraction *****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction;
class UScriptStruct* FAuracronLayerInteraction::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerInteraction, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronLayerInteraction"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Layer interaction data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer interaction data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanInteractCrossLayer_MetaData[] = {
		{ "Category", "Layer Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Can this actor interact with other layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can this actor interact with other layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AffectableLayers_MetaData[] = {
		{ "Category", "Layer Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layers this actor can affect */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layers this actor can affect" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrossLayerDamageMultiplier_MetaData[] = {
		{ "Category", "Layer Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Damage multiplier when affecting other layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Damage multiplier when affecting other layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrossLayerRangeMultiplier_MetaData[] = {
		{ "Category", "Layer Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Range multiplier when affecting other layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Range multiplier when affecting other layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrossLayerEffectIntensity_MetaData[] = {
		{ "Category", "Layer Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Effect intensity when affecting other layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effect intensity when affecting other layers" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bCanInteractCrossLayer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanInteractCrossLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AffectableLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AffectableLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AffectableLayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CrossLayerDamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CrossLayerRangeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CrossLayerEffectIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerInteraction>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_bCanInteractCrossLayer_SetBit(void* Obj)
{
	((FAuracronLayerInteraction*)Obj)->bCanInteractCrossLayer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_bCanInteractCrossLayer = { "bCanInteractCrossLayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerInteraction), &Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_bCanInteractCrossLayer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanInteractCrossLayer_MetaData), NewProp_bCanInteractCrossLayer_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers_Inner = { "AffectableLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers = { "AffectableLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerInteraction, AffectableLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AffectableLayers_MetaData), NewProp_AffectableLayers_MetaData) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerDamageMultiplier = { "CrossLayerDamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerInteraction, CrossLayerDamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrossLayerDamageMultiplier_MetaData), NewProp_CrossLayerDamageMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerRangeMultiplier = { "CrossLayerRangeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerInteraction, CrossLayerRangeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrossLayerRangeMultiplier_MetaData), NewProp_CrossLayerRangeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerEffectIntensity = { "CrossLayerEffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerInteraction, CrossLayerEffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrossLayerEffectIntensity_MetaData), NewProp_CrossLayerEffectIntensity_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_bCanInteractCrossLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_AffectableLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerDamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerRangeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewProp_CrossLayerEffectIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronLayerInteraction",
	Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::PropPointers),
	sizeof(FAuracronLayerInteraction),
	alignof(FAuracronLayerInteraction),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerInteraction()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerInteraction *******************************************

// ********** Begin ScriptStruct FAuracronLayerMovement ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerMovement;
class UScriptStruct* FAuracronLayerMovement::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerMovement, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronLayerMovement"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Layer movement data\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer movement data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanTransitionLayers_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Can this actor move between layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can this actor move between layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AllowedLayers_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Allowed destination layers */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Allowed destination layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionSpeedMultiplier_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition speed modifier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition speed modifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEnergyCost_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Energy cost for transitions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Energy cost for transitions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionCooldown_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cooldown between transitions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cooldown between transitions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastTransitionTime_MetaData[] = {
		{ "Category", "Layer Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last transition time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last transition time" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bCanTransitionLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanTransitionLayers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AllowedLayers_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AllowedLayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AllowedLayers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionEnergyCost;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastTransitionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerMovement>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_bCanTransitionLayers_SetBit(void* Obj)
{
	((FAuracronLayerMovement*)Obj)->bCanTransitionLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_bCanTransitionLayers = { "bCanTransitionLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerMovement), &Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_bCanTransitionLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanTransitionLayers_MetaData), NewProp_bCanTransitionLayers_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers_Inner = { "AllowedLayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers = { "AllowedLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMovement, AllowedLayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AllowedLayers_MetaData), NewProp_AllowedLayers_MetaData) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionSpeedMultiplier = { "TransitionSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMovement, TransitionSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionSpeedMultiplier_MetaData), NewProp_TransitionSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionEnergyCost = { "TransitionEnergyCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMovement, TransitionEnergyCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEnergyCost_MetaData), NewProp_TransitionEnergyCost_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionCooldown = { "TransitionCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMovement, TransitionCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionCooldown_MetaData), NewProp_TransitionCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_LastTransitionTime = { "LastTransitionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMovement, LastTransitionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastTransitionTime_MetaData), NewProp_LastTransitionTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_bCanTransitionLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_AllowedLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionEnergyCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_TransitionCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewProp_LastTransitionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronLayerMovement",
	Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::PropPointers),
	sizeof(FAuracronLayerMovement),
	alignof(FAuracronLayerMovement),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerMovement()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerMovement.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerMovement **********************************************

// ********** Begin ScriptStruct FAuracronLayerModifiers *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers;
class UScriptStruct* FAuracronLayerModifiers::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerModifiers, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronLayerModifiers"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Layer-specific bonuses and penalties\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer-specific bonuses and penalties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Movement speed modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement speed modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Damage modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Damage modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefenseMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Defense modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Defense modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityRangeMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ability range modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ability range modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbilityCooldownMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ability cooldown modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ability cooldown modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibilityMultiplier_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visibility modifier in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visibility modifier in this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StealthBonus_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Stealth bonus in this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stealth bonus in this layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefenseMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityRangeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbilityCooldownMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisibilityMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StealthBonus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerModifiers>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_MovementSpeedMultiplier = { "MovementSpeedMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, MovementSpeedMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedMultiplier_MetaData), NewProp_MovementSpeedMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, DamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_DefenseMultiplier = { "DefenseMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, DefenseMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefenseMultiplier_MetaData), NewProp_DefenseMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_AbilityRangeMultiplier = { "AbilityRangeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, AbilityRangeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityRangeMultiplier_MetaData), NewProp_AbilityRangeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_AbilityCooldownMultiplier = { "AbilityCooldownMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, AbilityCooldownMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbilityCooldownMultiplier_MetaData), NewProp_AbilityCooldownMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_VisibilityMultiplier = { "VisibilityMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, VisibilityMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibilityMultiplier_MetaData), NewProp_VisibilityMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_StealthBonus = { "StealthBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerModifiers, StealthBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StealthBonus_MetaData), NewProp_StealthBonus_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_MovementSpeedMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_DefenseMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_AbilityRangeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_AbilityCooldownMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_VisibilityMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewProp_StealthBonus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronLayerModifiers",
	Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::PropPointers),
	sizeof(FAuracronLayerModifiers),
	alignof(FAuracronLayerModifiers),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerModifiers()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerModifiers *********************************************

// ********** Begin Class UAuracronLayerComponent Function ApplyLayerModifiers *********************
struct Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Modifiers" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "ApplyLayerModifiers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execApplyLayerModifiers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyLayerModifiers();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function ApplyLayerModifiers ***********************

// ********** Begin Class UAuracronLayerComponent Function CalculateCrossLayerDamage ***************
struct Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics
{
	struct AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms
	{
		float BaseDamage;
		EAuracronRealmLayer TargetLayer;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cross-Layer" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDamage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_BaseDamage = { "BaseDamage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms, BaseDamage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_BaseDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "CalculateCrossLayerDamage", Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::AuracronLayerComponent_eventCalculateCrossLayerDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execCalculateCrossLayerDamage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_BaseDamage);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateCrossLayerDamage(Z_Param_BaseDamage,EAuracronRealmLayer(Z_Param_TargetLayer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function CalculateCrossLayerDamage *****************

// ********** Begin Class UAuracronLayerComponent Function CalculateCrossLayerRange ****************
struct Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics
{
	struct AuracronLayerComponent_eventCalculateCrossLayerRange_Parms
	{
		float BaseRange;
		EAuracronRealmLayer TargetLayer;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cross-Layer" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseRange;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_BaseRange = { "BaseRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerRange_Parms, BaseRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerRange_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCalculateCrossLayerRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_BaseRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "CalculateCrossLayerRange", Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::AuracronLayerComponent_eventCalculateCrossLayerRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::AuracronLayerComponent_eventCalculateCrossLayerRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execCalculateCrossLayerRange)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_BaseRange);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateCrossLayerRange(Z_Param_BaseRange,EAuracronRealmLayer(Z_Param_TargetLayer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function CalculateCrossLayerRange ******************

// ********** Begin Class UAuracronLayerComponent Function CanAffectLayer **************************
struct Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics
{
	struct AuracronLayerComponent_eventCanAffectLayer_Parms
	{
		EAuracronRealmLayer TargetLayer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cross-Layer" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cross-layer interactions\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cross-layer interactions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCanAffectLayer_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventCanAffectLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventCanAffectLayer_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "CanAffectLayer", Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::AuracronLayerComponent_eventCanAffectLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::AuracronLayerComponent_eventCanAffectLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execCanAffectLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAffectLayer(EAuracronRealmLayer(Z_Param_TargetLayer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function CanAffectLayer ****************************

// ********** Begin Class UAuracronLayerComponent Function CancelTransition ************************
struct Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "CancelTransition", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execCancelTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelTransition();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function CancelTransition **************************

// ********** Begin Class UAuracronLayerComponent Function CanTransitionToLayer ********************
struct Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics
{
	struct AuracronLayerComponent_eventCanTransitionToLayer_Parms
	{
		EAuracronRealmLayer TargetLayer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Management" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventCanTransitionToLayer_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventCanTransitionToLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventCanTransitionToLayer_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "CanTransitionToLayer", Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::AuracronLayerComponent_eventCanTransitionToLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::AuracronLayerComponent_eventCanTransitionToLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execCanTransitionToLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanTransitionToLayer(EAuracronRealmLayer(Z_Param_TargetLayer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function CanTransitionToLayer **********************

// ********** Begin Class UAuracronLayerComponent Function GetCurrentLayer *************************
struct Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics
{
	struct AuracronLayerComponent_eventGetCurrentLayer_Parms
	{
		EAuracronRealmLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Management" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventGetCurrentLayer_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "GetCurrentLayer", Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::AuracronLayerComponent_eventGetCurrentLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::AuracronLayerComponent_eventGetCurrentLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execGetCurrentLayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmLayer*)Z_Param__Result=P_THIS->GetCurrentLayer();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function GetCurrentLayer ***************************

// ********** Begin Class UAuracronLayerComponent Function GetCurrentLayerModifiers ****************
struct Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics
{
	struct AuracronLayerComponent_eventGetCurrentLayerModifiers_Parms
	{
		FAuracronLayerModifiers ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer modifiers\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer modifiers" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventGetCurrentLayerModifiers_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLayerModifiers, METADATA_PARAMS(0, nullptr) }; // 1781256813
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "GetCurrentLayerModifiers", Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::AuracronLayerComponent_eventGetCurrentLayerModifiers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::AuracronLayerComponent_eventGetCurrentLayerModifiers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execGetCurrentLayerModifiers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLayerModifiers*)Z_Param__Result=P_THIS->GetCurrentLayerModifiers();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function GetCurrentLayerModifiers ******************

// ********** Begin Class UAuracronLayerComponent Function GetTransitionProgress *******************
struct Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics
{
	struct AuracronLayerComponent_eventGetTransitionProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "GetTransitionProgress", Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::AuracronLayerComponent_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::AuracronLayerComponent_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execGetTransitionProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function GetTransitionProgress *********************

// ********** Begin Class UAuracronLayerComponent Function IsInLayer *******************************
struct Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics
{
	struct AuracronLayerComponent_eventIsInLayer_Parms
	{
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Management" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventIsInLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventIsInLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventIsInLayer_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "IsInLayer", Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::AuracronLayerComponent_eventIsInLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::AuracronLayerComponent_eventIsInLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execIsInLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function IsInLayer *********************************

// ********** Begin Class UAuracronLayerComponent Function IsInTransition **************************
struct Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics
{
	struct AuracronLayerComponent_eventIsInTransition_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventIsInTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventIsInTransition_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "IsInTransition", Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::AuracronLayerComponent_eventIsInTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::AuracronLayerComponent_eventIsInTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execIsInTransition)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInTransition();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function IsInTransition ****************************

// ********** Begin Class UAuracronLayerComponent Function IsLayerVisible **************************
struct Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics
{
	struct AuracronLayerComponent_eventIsLayerVisible_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventIsLayerVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventIsLayerVisible_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "IsLayerVisible", Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::AuracronLayerComponent_eventIsLayerVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::AuracronLayerComponent_eventIsLayerVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execIsLayerVisible)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLayerVisible();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function IsLayerVisible ****************************

// ********** Begin Class UAuracronLayerComponent Function OnLayerChanged **************************
struct AuracronLayerComponent_eventOnLayerChanged_Parms
{
	EAuracronRealmLayer OldLayer;
	EAuracronRealmLayer NewLayer;
};
static FName NAME_UAuracronLayerComponent_OnLayerChanged = FName(TEXT("OnLayerChanged"));
void UAuracronLayerComponent::OnLayerChanged(EAuracronRealmLayer OldLayer, EAuracronRealmLayer NewLayer)
{
	AuracronLayerComponent_eventOnLayerChanged_Parms Parms;
	Parms.OldLayer=OldLayer;
	Parms.NewLayer=NewLayer;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronLayerComponent_OnLayerChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_OldLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_OldLayer = { "OldLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventOnLayerChanged_Parms, OldLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventOnLayerChanged_Parms, NewLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_OldLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_OldLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "OnLayerChanged", Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::PropPointers), sizeof(AuracronLayerComponent_eventOnLayerChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronLayerComponent_eventOnLayerChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronLayerComponent Function OnLayerChanged ****************************

// ********** Begin Class UAuracronLayerComponent Function OnLayerModifiersApplied *****************
struct AuracronLayerComponent_eventOnLayerModifiersApplied_Parms
{
	FAuracronLayerModifiers Modifiers;
};
static FName NAME_UAuracronLayerComponent_OnLayerModifiersApplied = FName(TEXT("OnLayerModifiersApplied"));
void UAuracronLayerComponent::OnLayerModifiersApplied(FAuracronLayerModifiers const& Modifiers)
{
	AuracronLayerComponent_eventOnLayerModifiersApplied_Parms Parms;
	Parms.Modifiers=Modifiers;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronLayerComponent_OnLayerModifiersApplied);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Events" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Modifiers_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Modifiers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::NewProp_Modifiers = { "Modifiers", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventOnLayerModifiersApplied_Parms, Modifiers), Z_Construct_UScriptStruct_FAuracronLayerModifiers, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Modifiers_MetaData), NewProp_Modifiers_MetaData) }; // 1781256813
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::NewProp_Modifiers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "OnLayerModifiersApplied", Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::PropPointers), sizeof(AuracronLayerComponent_eventOnLayerModifiersApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08420800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronLayerComponent_eventOnLayerModifiersApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronLayerComponent Function OnLayerModifiersApplied *******************

// ********** Begin Class UAuracronLayerComponent Function OnTransitionCompleted *******************
struct AuracronLayerComponent_eventOnTransitionCompleted_Parms
{
	EAuracronRealmLayer NewLayer;
};
static FName NAME_UAuracronLayerComponent_OnTransitionCompleted = FName(TEXT("OnTransitionCompleted"));
void UAuracronLayerComponent::OnTransitionCompleted(EAuracronRealmLayer NewLayer)
{
	AuracronLayerComponent_eventOnTransitionCompleted_Parms Parms;
	Parms.NewLayer=NewLayer;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronLayerComponent_OnTransitionCompleted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Events" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventOnTransitionCompleted_Parms, NewLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "OnTransitionCompleted", Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::PropPointers), sizeof(AuracronLayerComponent_eventOnTransitionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronLayerComponent_eventOnTransitionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronLayerComponent Function OnTransitionCompleted *********************

// ********** Begin Class UAuracronLayerComponent Function OnTransitionStarted *********************
struct AuracronLayerComponent_eventOnTransitionStarted_Parms
{
	EAuracronRealmLayer TargetLayer;
};
static FName NAME_UAuracronLayerComponent_OnTransitionStarted = FName(TEXT("OnTransitionStarted"));
void UAuracronLayerComponent::OnTransitionStarted(EAuracronRealmLayer TargetLayer)
{
	AuracronLayerComponent_eventOnTransitionStarted_Parms Parms;
	Parms.TargetLayer=TargetLayer;
	UFunction* Func = FindFunctionChecked(NAME_UAuracronLayerComponent_OnTransitionStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Events" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventOnTransitionStarted_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::NewProp_TargetLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "OnTransitionStarted", Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::PropPointers), sizeof(AuracronLayerComponent_eventOnTransitionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AuracronLayerComponent_eventOnTransitionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class UAuracronLayerComponent Function OnTransitionStarted ***********************

// ********** Begin Class UAuracronLayerComponent Function RemoveLayerModifiers ********************
struct Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Modifiers" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "RemoveLayerModifiers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execRemoveLayerModifiers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveLayerModifiers();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function RemoveLayerModifiers **********************

// ********** Begin Class UAuracronLayerComponent Function RequestLayerTransition ******************
struct Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics
{
	struct AuracronLayerComponent_eventRequestLayerTransition_Parms
	{
		EAuracronRealmLayer TargetLayer;
		ERealmTransitionType TransitionType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition management\n" },
#endif
		{ "CPP_Default_TransitionType", "Gradual" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventRequestLayerTransition_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventRequestLayerTransition_Parms, TransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(0, nullptr) }; // 2122775647
void Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventRequestLayerTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventRequestLayerTransition_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "RequestLayerTransition", Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::AuracronLayerComponent_eventRequestLayerTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::AuracronLayerComponent_eventRequestLayerTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execRequestLayerTransition)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_GET_ENUM(ERealmTransitionType,Z_Param_TransitionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RequestLayerTransition(EAuracronRealmLayer(Z_Param_TargetLayer),ERealmTransitionType(Z_Param_TransitionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function RequestLayerTransition ********************

// ********** Begin Class UAuracronLayerComponent Function SetCurrentLayer *************************
struct Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics
{
	struct AuracronLayerComponent_eventSetCurrentLayer_Parms
	{
		EAuracronRealmLayer NewLayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Layer Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::NewProp_NewLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::NewProp_NewLayer = { "NewLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronLayerComponent_eventSetCurrentLayer_Parms, NewLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::NewProp_NewLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::NewProp_NewLayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "SetCurrentLayer", Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::AuracronLayerComponent_eventSetCurrentLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::AuracronLayerComponent_eventSetCurrentLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execSetCurrentLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_NewLayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCurrentLayer(EAuracronRealmLayer(Z_Param_NewLayer));
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function SetCurrentLayer ***************************

// ********** Begin Class UAuracronLayerComponent Function SetLayerRenderingEnabled ****************
struct Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics
{
	struct AuracronLayerComponent_eventSetLayerRenderingEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronLayerComponent_eventSetLayerRenderingEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronLayerComponent_eventSetLayerRenderingEnabled_Parms), &Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "SetLayerRenderingEnabled", Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::AuracronLayerComponent_eventSetLayerRenderingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::AuracronLayerComponent_eventSetLayerRenderingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execSetLayerRenderingEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerRenderingEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function SetLayerRenderingEnabled ******************

// ********** Begin Class UAuracronLayerComponent Function UpdateLayerVisibility *******************
struct Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visibility and rendering\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visibility and rendering" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronLayerComponent, nullptr, "UpdateLayerVisibility", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronLayerComponent::execUpdateLayerVisibility)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLayerVisibility();
	P_NATIVE_END;
}
// ********** End Class UAuracronLayerComponent Function UpdateLayerVisibility *********************

// ********** Begin Class UAuracronLayerComponent **************************************************
void UAuracronLayerComponent::StaticRegisterNativesUAuracronLayerComponent()
{
	UClass* Class = UAuracronLayerComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyLayerModifiers", &UAuracronLayerComponent::execApplyLayerModifiers },
		{ "CalculateCrossLayerDamage", &UAuracronLayerComponent::execCalculateCrossLayerDamage },
		{ "CalculateCrossLayerRange", &UAuracronLayerComponent::execCalculateCrossLayerRange },
		{ "CanAffectLayer", &UAuracronLayerComponent::execCanAffectLayer },
		{ "CancelTransition", &UAuracronLayerComponent::execCancelTransition },
		{ "CanTransitionToLayer", &UAuracronLayerComponent::execCanTransitionToLayer },
		{ "GetCurrentLayer", &UAuracronLayerComponent::execGetCurrentLayer },
		{ "GetCurrentLayerModifiers", &UAuracronLayerComponent::execGetCurrentLayerModifiers },
		{ "GetTransitionProgress", &UAuracronLayerComponent::execGetTransitionProgress },
		{ "IsInLayer", &UAuracronLayerComponent::execIsInLayer },
		{ "IsInTransition", &UAuracronLayerComponent::execIsInTransition },
		{ "IsLayerVisible", &UAuracronLayerComponent::execIsLayerVisible },
		{ "RemoveLayerModifiers", &UAuracronLayerComponent::execRemoveLayerModifiers },
		{ "RequestLayerTransition", &UAuracronLayerComponent::execRequestLayerTransition },
		{ "SetCurrentLayer", &UAuracronLayerComponent::execSetCurrentLayer },
		{ "SetLayerRenderingEnabled", &UAuracronLayerComponent::execSetLayerRenderingEnabled },
		{ "UpdateLayerVisibility", &UAuracronLayerComponent::execUpdateLayerVisibility },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronLayerComponent;
UClass* UAuracronLayerComponent::GetPrivateStaticClass()
{
	using TClass = UAuracronLayerComponent;
	if (!Z_Registration_Info_UClass_UAuracronLayerComponent.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronLayerComponent"),
			Z_Registration_Info_UClass_UAuracronLayerComponent.InnerSingleton,
			StaticRegisterNativesUAuracronLayerComponent,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronLayerComponent.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronLayerComponent_NoRegister()
{
	return UAuracronLayerComponent::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronLayerComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Auracron" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Layer Component\n * \n * Component that manages an actor's relationship with the dynamic realm system:\n * - Tracks which layer the actor belongs to\n * - Handles layer transitions\n * - Applies layer-specific modifiers\n * - Manages cross-layer interactions\n * - Optimizes rendering based on layer visibility\n */" },
#endif
		{ "IncludePath", "AuracronLayerComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Layer Component\n\nComponent that manages an actor's relationship with the dynamic realm system:\n- Tracks which layer the actor belongs to\n- Handles layer transitions\n- Applies layer-specific modifiers\n- Manages cross-layer interactions\n- Optimizes rendering based on layer visibility" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLayer_MetaData[] = {
		{ "Category", "Layer Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core layer data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core layer data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviousLayer_MetaData[] = {
		{ "Category", "Layer Configuration" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionSettings_MetaData[] = {
		{ "Category", "Layer Configuration" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSettings_MetaData[] = {
		{ "Category", "Layer Configuration" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerModifiers_MetaData[] = {
		{ "Category", "Layer Modifiers" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer-specific modifiers\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer-specific modifiers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTransitioning_MetaData[] = {
		{ "Category", "Transition State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTargetLayer_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTransitionType_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionStartTime_MetaData[] = {
		{ "Category", "Transition State" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLayerVisible_MetaData[] = {
		{ "Category", "Rendering" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Rendering state\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rendering state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRenderingEnabled_MetaData[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "Category", "Rendering" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedRealmSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached references\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronLayerComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreviousLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreviousLayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionSettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovementSettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerModifiers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerModifiers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerModifiers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerModifiers;
	static void NewProp_bIsTransitioning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTransitioning;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionTargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionTargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentTransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentTransitionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionStartTime;
	static void NewProp_bIsLayerVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLayerVisible;
	static void NewProp_bRenderingEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRenderingEnabled;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedRealmSubsystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronLayerComponent_ApplyLayerModifiers, "ApplyLayerModifiers" }, // 2482924023
		{ &Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerDamage, "CalculateCrossLayerDamage" }, // 3771248330
		{ &Z_Construct_UFunction_UAuracronLayerComponent_CalculateCrossLayerRange, "CalculateCrossLayerRange" }, // 2078397938
		{ &Z_Construct_UFunction_UAuracronLayerComponent_CanAffectLayer, "CanAffectLayer" }, // 2883020573
		{ &Z_Construct_UFunction_UAuracronLayerComponent_CancelTransition, "CancelTransition" }, // 504271265
		{ &Z_Construct_UFunction_UAuracronLayerComponent_CanTransitionToLayer, "CanTransitionToLayer" }, // 3055466792
		{ &Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayer, "GetCurrentLayer" }, // 1677844748
		{ &Z_Construct_UFunction_UAuracronLayerComponent_GetCurrentLayerModifiers, "GetCurrentLayerModifiers" }, // 3274509855
		{ &Z_Construct_UFunction_UAuracronLayerComponent_GetTransitionProgress, "GetTransitionProgress" }, // 1323770359
		{ &Z_Construct_UFunction_UAuracronLayerComponent_IsInLayer, "IsInLayer" }, // 472615707
		{ &Z_Construct_UFunction_UAuracronLayerComponent_IsInTransition, "IsInTransition" }, // 3211450993
		{ &Z_Construct_UFunction_UAuracronLayerComponent_IsLayerVisible, "IsLayerVisible" }, // 3791214323
		{ &Z_Construct_UFunction_UAuracronLayerComponent_OnLayerChanged, "OnLayerChanged" }, // 4144961409
		{ &Z_Construct_UFunction_UAuracronLayerComponent_OnLayerModifiersApplied, "OnLayerModifiersApplied" }, // 1407774946
		{ &Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionCompleted, "OnTransitionCompleted" }, // 1527318932
		{ &Z_Construct_UFunction_UAuracronLayerComponent_OnTransitionStarted, "OnTransitionStarted" }, // 8981196
		{ &Z_Construct_UFunction_UAuracronLayerComponent_RemoveLayerModifiers, "RemoveLayerModifiers" }, // 1639482466
		{ &Z_Construct_UFunction_UAuracronLayerComponent_RequestLayerTransition, "RequestLayerTransition" }, // 738069557
		{ &Z_Construct_UFunction_UAuracronLayerComponent_SetCurrentLayer, "SetCurrentLayer" }, // 3289801239
		{ &Z_Construct_UFunction_UAuracronLayerComponent_SetLayerRenderingEnabled, "SetLayerRenderingEnabled" }, // 2139550899
		{ &Z_Construct_UFunction_UAuracronLayerComponent_UpdateLayerVisibility, "UpdateLayerVisibility" }, // 410266303
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronLayerComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLayer = { "CurrentLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, CurrentLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLayer_MetaData), NewProp_CurrentLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_PreviousLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_PreviousLayer = { "PreviousLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, PreviousLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviousLayer_MetaData), NewProp_PreviousLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_InteractionSettings = { "InteractionSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, InteractionSettings), Z_Construct_UScriptStruct_FAuracronLayerInteraction, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionSettings_MetaData), NewProp_InteractionSettings_MetaData) }; // 3868464225
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_MovementSettings = { "MovementSettings", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, MovementSettings), Z_Construct_UScriptStruct_FAuracronLayerMovement, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSettings_MetaData), NewProp_MovementSettings_MetaData) }; // 3602032642
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_ValueProp = { "LayerModifiers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronLayerModifiers, METADATA_PARAMS(0, nullptr) }; // 1781256813
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_Key_KeyProp = { "LayerModifiers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers = { "LayerModifiers", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, LayerModifiers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerModifiers_MetaData), NewProp_LayerModifiers_MetaData) }; // 3153537035 1781256813
void Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsTransitioning_SetBit(void* Obj)
{
	((UAuracronLayerComponent*)Obj)->bIsTransitioning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsTransitioning = { "bIsTransitioning", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronLayerComponent), &Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsTransitioning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTransitioning_MetaData), NewProp_bIsTransitioning_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionTargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionTargetLayer = { "TransitionTargetLayer", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, TransitionTargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTargetLayer_MetaData), NewProp_TransitionTargetLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentTransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentTransitionType = { "CurrentTransitionType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, CurrentTransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTransitionType_MetaData), NewProp_CurrentTransitionType_MetaData) }; // 2122775647
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionStartTime = { "TransitionStartTime", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, TransitionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionStartTime_MetaData), NewProp_TransitionStartTime_MetaData) };
void Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsLayerVisible_SetBit(void* Obj)
{
	((UAuracronLayerComponent*)Obj)->bIsLayerVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsLayerVisible = { "bIsLayerVisible", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronLayerComponent), &Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsLayerVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLayerVisible_MetaData), NewProp_bIsLayerVisible_MetaData) };
void Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bRenderingEnabled_SetBit(void* Obj)
{
	((UAuracronLayerComponent*)Obj)->bRenderingEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bRenderingEnabled = { "bRenderingEnabled", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronLayerComponent), &Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bRenderingEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRenderingEnabled_MetaData), NewProp_bRenderingEnabled_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, CurrentLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CachedRealmSubsystem = { "CachedRealmSubsystem", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, CachedRealmSubsystem), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedRealmSubsystem_MetaData), NewProp_CachedRealmSubsystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionComponent = { "TransitionComponent", nullptr, (EPropertyFlags)0x0144000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronLayerComponent, TransitionComponent), Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionComponent_MetaData), NewProp_TransitionComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronLayerComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_PreviousLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_PreviousLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_InteractionSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_MovementSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_LayerModifiers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsTransitioning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionTargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionTargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentTransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentTransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bIsLayerVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_bRenderingEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_CachedRealmSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronLayerComponent_Statics::NewProp_TransitionComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLayerComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronLayerComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLayerComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronLayerComponent_Statics::ClassParams = {
	&UAuracronLayerComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronLayerComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLayerComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronLayerComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronLayerComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronLayerComponent()
{
	if (!Z_Registration_Info_UClass_UAuracronLayerComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronLayerComponent.OuterSingleton, Z_Construct_UClass_UAuracronLayerComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronLayerComponent.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronLayerComponent);
UAuracronLayerComponent::~UAuracronLayerComponent() {}
// ********** End Class UAuracronLayerComponent ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLayerInteraction::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerInteraction_Statics::NewStructOps, TEXT("AuracronLayerInteraction"), &Z_Registration_Info_UScriptStruct_FAuracronLayerInteraction, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerInteraction), 3868464225U) },
		{ FAuracronLayerMovement::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerMovement_Statics::NewStructOps, TEXT("AuracronLayerMovement"), &Z_Registration_Info_UScriptStruct_FAuracronLayerMovement, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerMovement), 3602032642U) },
		{ FAuracronLayerModifiers::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerModifiers_Statics::NewStructOps, TEXT("AuracronLayerModifiers"), &Z_Registration_Info_UScriptStruct_FAuracronLayerModifiers, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerModifiers), 1781256813U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronLayerComponent, UAuracronLayerComponent::StaticClass, TEXT("UAuracronLayerComponent"), &Z_Registration_Info_UClass_UAuracronLayerComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronLayerComponent), 2690020570U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_3121023899(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronLayerComponent_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
