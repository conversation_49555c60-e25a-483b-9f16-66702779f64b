# Script final para corrigir todos os includes restantes do PCG
# Baseado na estrutura real do UE 5.6 PCG

param(
    [string]$ProjectPath = "C:\Aura\projeto\Auracron\Source\AuracronPCGBridge"
)

Write-Host "Iniciando correcao final de includes PCG..." -ForegroundColor Green
Write-Host "Diretorio: $ProjectPath" -ForegroundColor Yellow

# Verifica se o diretorio existe
if (-not (Test-Path $ProjectPath)) {
    Write-Error "Diretorio nao encontrado: $ProjectPath"
    exit 1
}

# Mapeamento final de includes incorretos para corretos
$includeMap = @{
    # Includes que não existem no UE 5.6 - REMOVER ou SUBSTITUIR
    '#include "Elements/PCGBounds.h"' = '#include "Elements/PCGGetBounds.h"'
    '#include "Elements/PCGDuplicatePoints.h"' = '#include "Elements/PCGCopyPoints.h"'
    '#include "Elements/PCGNormalToVector.h"' = '#include "Elements/PCGNormalToDensity.h"'
    '#include "PCGSplineData.h"' = '#include "Data/PCGSplineData.h"'
    
    # Includes que causam problemas - COMENTAR
    '#include "Components/StaticMeshComponent.h"' = '// #include "Components/StaticMeshComponent.h" // Removido - não necessário'
    
    # Includes malformados - CORRIGIR
    '#include "CoreMinimal.h"`n#include "AuracronPCGNodeSystem.h"' = '#include "CoreMinimal.h"'
}

# Encontra todos os arquivos .h e .cpp
$files = Get-ChildItem -Path $ProjectPath -Recurse -Include "*.h", "*.cpp" | Where-Object { $_.Name -notlike "*.generated.*" }

Write-Host "Encontrados $($files.Count) arquivos para processar..." -ForegroundColor Cyan

$totalReplacements = 0
$filesModified = 0

foreach ($file in $files) {
    Write-Host "Processando: $($file.Name)" -ForegroundColor White
    
    # Le o conteudo do arquivo
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    $fileReplacements = 0
    
    # Aplica todas as substituicoes
    foreach ($oldInclude in $includeMap.Keys) {
        $newInclude = $includeMap[$oldInclude]
        if ($content -match [regex]::Escape($oldInclude)) {
            $matches = [regex]::Matches($content, [regex]::Escape($oldInclude))
            $count = $matches.Count
            $content = $content -replace [regex]::Escape($oldInclude), $newInclude
            $fileReplacements += $count
            Write-Host "    Substituido: $oldInclude -> $newInclude ($count vezes)" -ForegroundColor Yellow
        }
    }
    
    # Salva o arquivo se houve mudancas
    if ($fileReplacements -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
        $totalReplacements += $fileReplacements
        $filesModified++
        Write-Host "  OK $fileReplacements substituicoes aplicadas" -ForegroundColor Green
    } else {
        Write-Host "  - Nenhuma substituicao necessaria" -ForegroundColor Gray
    }
}

Write-Host "`n=== RESUMO ===" -ForegroundColor Magenta
Write-Host "Arquivos processados: $($files.Count)" -ForegroundColor White
Write-Host "Arquivos modificados: $filesModified" -ForegroundColor Green
Write-Host "Total de substituicoes: $totalReplacements" -ForegroundColor Green

if ($totalReplacements -gt 0) {
    Write-Host "`nCorrecao final de includes concluida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "`nNenhuma correcao final necessaria." -ForegroundColor Yellow
}

Write-Host "`nScript concluido!" -ForegroundColor Green
