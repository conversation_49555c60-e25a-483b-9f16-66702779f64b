// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Base Implementation
// Bridge 2.2: PCG Framework - Base Classes Implementation

#include "AuracronPCGBase.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGData.h"
#include "PCGContext.h"
#include "Data/PCGPointData.h"
#include "Data/PCGSpatialData.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "Math/RandomStream.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronPCG, Log, All);

// ========================================
// AURACRON PCG SETTINGS BASE
// ========================================

UAuracronPCGSettingsBase::UAuracronPCGSettingsBase(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Set default values
    NodeDescription = TEXT("Auracron PCG Node");
    BatchSize = 1000;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::InputPinProperties() const
{
    TArray<FPCGPinProperties> PinProperties;
    PinProperties.Emplace(PCGPinConstants::DefaultInputLabel, EPCGDataType::Spatial);
    return PinProperties;
}

TArray<FPCGPinProperties> UAuracronPCGSettingsBase::OutputPinProperties() const
{
    TArray<FPCGPinProperties> PinProperties;
    PinProperties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Point);
    return PinProperties;
}

FPCGElementPtr UAuracronPCGSettingsBase::CreateElement() const
{
    return MakeShared<FAuracronPCGElementBase>();
}

// FAuracronPCGElementBase Implementation
FPCGContext* FAuracronPCGElementBase::Initialize(const FPCGDataCollection& InputData, TWeakObjectPtr<UPCGComponent> SourceComponent, const UPCGNode* Node)
{
    FPCGContext* Context = new FPCGContext();
    Context->InputData = InputData;
    Context->Node = Node;
    return Context;
}

bool FAuracronPCGElementBase::ExecuteInternal(FPCGContext* Context) const
{
    // Default implementation - just pass through the input data
    Context->OutputData = Context->InputData;
    return true;
}

void FAuracronPCGElementBase::ProcessPoints(FPCGContext* Context, const FPCGDataCollection& InputData, FPCGDataCollection& OutputData) const
{
    // Default implementation - copy input to output
    OutputData = InputData;
}

// PCG Bridge API implementation moved to AuracronPCGBridgeAPI.cpp

// ========================================
// AURACRON BIOME PCG SETTINGS
// ========================================

UAuracronBiomePCGSettings::UAuracronBiomePCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Biome Generator");
    BiomeType = TEXT("Forest");
    TemperatureRange = FVector2D(15.0f, 25.0f);
    HumidityRange = FVector2D(0.4f, 0.8f);
    ElevationRange = FVector2D(0.0f, 1000.0f);
    VegetationDensity = 1.0f;
    ResourceSpawnRate = 1.0f;
    bUseFoliageBridge = true;
    bUseDynamicRealm = true;
}

FPCGElementPtr UAuracronBiomePCGSettings::CreateElement() const
{
    return MakeShared<FAuracronBiomePCGElement>();
}

// ========================================
// AURACRON TERRAIN PCG SETTINGS
// ========================================

UAuracronTerrainPCGSettings::UAuracronTerrainPCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Terrain Generator");
    TerrainSeed = 12345;
    NoiseScale = 0.01f;
    HeightMultiplier = 1000.0f;
    NoiseOctaves = 4;
    NoisePersistence = 0.5f;
    bUseWorldPartition = true;
    bUseLumenIntegration = true;
}

FPCGElementPtr UAuracronTerrainPCGSettings::CreateElement() const
{
    return MakeShared<FAuracronTerrainPCGElement>();
}

// ========================================
// AURACRON STRUCTURE PCG SETTINGS
// ========================================

UAuracronStructurePCGSettings::UAuracronStructurePCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Structure Generator");
    StructureType = TEXT("Village");
    MinStructureDistance = 500.0f;
    MaxStructuresPerArea = 5;
    ScaleRange = FVector2D(0.8f, 1.2f);
    RotationVariation = 45.0f;
}

FPCGElementPtr UAuracronStructurePCGSettings::CreateElement() const
{
    return MakeShared<FAuracronStructurePCGElement>();
}

// ========================================
// AURACRON VEGETATION PCG SETTINGS
// ========================================

UAuracronVegetationPCGSettings::UAuracronVegetationPCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Vegetation Generator");
    VegetationType = TEXT("Forest");
    BaseDensity = 1.0f;
    MaxSlope = 45.0f;
    MinAltitude = 0.0f;
    MaxAltitude = 2000.0f;
    SizeRange = FVector2D(0.8f, 1.2f);
    bUseSeasonalVariation = true;
    bUseWindInteraction = true;
}

FPCGElementPtr UAuracronVegetationPCGSettings::CreateElement() const
{
    return MakeShared<FAuracronVegetationPCGElement>();
}

// ========================================
// AURACRON RESOURCE PCG SETTINGS
// ========================================

UAuracronResourcePCGSettings::UAuracronResourcePCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Resource Generator");
    ResourceType = TEXT("Iron");
    ResourceRarity = 0.1f;
    ClusterSize = 3;
    ClusterRadius = 200.0f;
    RespawnTime = 300.0f;
    QualityRange = FVector2D(0.8f, 1.2f);
}

FPCGElementPtr UAuracronResourcePCGSettings::CreateElement() const
{
    return MakeShared<FAuracronResourcePCGElement>();
}

// ========================================
// AURACRON ENEMY SPAWN PCG SETTINGS
// ========================================

UAuracronEnemySpawnPCGSettings::UAuracronEnemySpawnPCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Enemy Spawn Generator");
    EnemyType = TEXT("Goblin");
    SpawnDensity = 0.5f;
    MinDistanceFromStructures = 1000.0f;
    PatrolRadius = 500.0f;
    bUseLevelScaling = true;
    bUseCombatBridge = true;
    bUseAdaptiveCreatures = true;
}

FPCGElementPtr UAuracronEnemySpawnPCGSettings::CreateElement() const
{
    return MakeShared<FAuracronEnemySpawnPCGElement>();
}

// ========================================
// AURACRON DUNGEON PCG SETTINGS
// ========================================

UAuracronDungeonPCGSettings::UAuracronDungeonPCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Dungeon Generator");
    DungeonType = TEXT("Cave");
    RoomCount = 10;
    RoomSizeRange = FVector2D(400.0f, 800.0f);
    CorridorWidth = 200.0f;
    DepthLevels = 3;
    TreasureRoomProbability = 0.2f;
    bGenerateBossRoom = true;
}

FPCGElementPtr UAuracronDungeonPCGSettings::CreateElement() const
{
    return MakeShared<FAuracronDungeonPCGElement>();
}

// ========================================
// AURACRON WEATHER PCG SETTINGS
// ========================================

UAuracronWeatherPCGSettings::UAuracronWeatherPCGSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NodeDescription = TEXT("Auracron Weather Generator");
    WeatherType = TEXT("Rain");
    WeatherIntensity = 0.5f;
    bAffectsVegetation = true;
    bAffectsResources = true;
    bAffectsEnemies = true;
    WeatherDuration = 600.0f;
    bUseVFXBridge = true;
    bUseAudioBridge = true;
}

FPCGElementPtr UAuracronWeatherPCGSettings::CreateElement() const
{
    return MakeShared<FAuracronWeatherPCGElement>();
}
