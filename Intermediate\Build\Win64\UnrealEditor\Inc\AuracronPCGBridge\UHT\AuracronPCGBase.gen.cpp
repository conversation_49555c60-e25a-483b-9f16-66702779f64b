// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronPCGBase.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronPCGBase() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleSettings();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGExampleSettings_NoRegister();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase();
AURACRONPCGBRIDGE_API UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
PCG_API UClass* Z_Construct_UClass_UPCGSettings();
UPackage* Z_Construct_UPackage__Script_AuracronPCGBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UAuracronPCGSettingsBase *************************************************
void UAuracronPCGSettingsBase::StaticRegisterNativesUAuracronPCGSettingsBase()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGSettingsBase;
UClass* UAuracronPCGSettingsBase::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGSettingsBase;
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGSettingsBase"),
			Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton,
			StaticRegisterNativesUAuracronPCGSettingsBase,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase_NoRegister()
{
	return UAuracronPCGSettingsBase::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGSettingsBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base class for all Auracron PCG Settings\n * Provides common functionality and interface for all PCG nodes\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base class for all Auracron PCG Settings\nProvides common functionality and interface for all PCG nodes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeDescription_MetaData[] = {
		{ "Category", "Auracron Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auracron specific settings\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron specific settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BatchSize_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeDescription;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BatchSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGSettingsBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_NodeDescription = { "NodeDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, NodeDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeDescription_MetaData), NewProp_NodeDescription_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize = { "BatchSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGSettingsBase, BatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BatchSize_MetaData), NewProp_BatchSize_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_NodeDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::NewProp_BatchSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UPCGSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams = {
	&UAuracronPCGSettingsBase::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGSettingsBase()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton, Z_Construct_UClass_UAuracronPCGSettingsBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGSettingsBase.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGSettingsBase);
UAuracronPCGSettingsBase::~UAuracronPCGSettingsBase() {}
// ********** End Class UAuracronPCGSettingsBase ***************************************************

// ********** Begin Class UAuracronPCGExampleSettings **********************************************
void UAuracronPCGExampleSettings::StaticRegisterNativesUAuracronPCGExampleSettings()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronPCGExampleSettings;
UClass* UAuracronPCGExampleSettings::GetPrivateStaticClass()
{
	using TClass = UAuracronPCGExampleSettings;
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleSettings.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronPCGExampleSettings"),
			Z_Registration_Info_UClass_UAuracronPCGExampleSettings.InnerSingleton,
			StaticRegisterNativesUAuracronPCGExampleSettings,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleSettings.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronPCGExampleSettings_NoRegister()
{
	return UAuracronPCGExampleSettings::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronPCGExampleSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Category", "Auracron PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Simple example PCG node that transforms points\n */" },
#endif
		{ "IncludePath", "AuracronPCGBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
		{ "ObjectInitializerConstructorDeclared", "" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simple example PCG node that transforms points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Offset_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Scale_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Public/AuracronPCGBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Offset;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronPCGExampleSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::NewProp_Offset = { "Offset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleSettings, Offset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Offset_MetaData), NewProp_Offset_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronPCGExampleSettings, Scale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Scale_MetaData), NewProp_Scale_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::NewProp_Offset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UAuracronPCGSettingsBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPCGBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::ClassParams = {
	&UAuracronPCGExampleSettings::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronPCGExampleSettings()
{
	if (!Z_Registration_Info_UClass_UAuracronPCGExampleSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronPCGExampleSettings.OuterSingleton, Z_Construct_UClass_UAuracronPCGExampleSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronPCGExampleSettings.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronPCGExampleSettings);
UAuracronPCGExampleSettings::~UAuracronPCGExampleSettings() {}
// ********** End Class UAuracronPCGExampleSettings ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronPCGSettingsBase, UAuracronPCGSettingsBase::StaticClass, TEXT("UAuracronPCGSettingsBase"), &Z_Registration_Info_UClass_UAuracronPCGSettingsBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGSettingsBase), 972174545U) },
		{ Z_Construct_UClass_UAuracronPCGExampleSettings, UAuracronPCGExampleSettings::StaticClass, TEXT("UAuracronPCGExampleSettings"), &Z_Registration_Info_UClass_UAuracronPCGExampleSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronPCGExampleSettings), 2684940062U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_3040136381(TEXT("/Script/AuracronPCGBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Source_AuracronPCGBridge_Public_AuracronPCGBase_h__Script_AuracronPCGBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
