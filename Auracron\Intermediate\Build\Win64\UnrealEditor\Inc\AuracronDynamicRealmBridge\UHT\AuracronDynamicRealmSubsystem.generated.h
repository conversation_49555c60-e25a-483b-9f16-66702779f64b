// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AuracronDynamicRealmSubsystem.h"

#ifdef AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRealmSubsystem_generated_h
#error "AuracronDynamicRealmSubsystem.generated.h already included, missing '#pragma once' in AuracronDynamicRealmSubsystem.h"
#endif
#define AURACRONDYNAMICREALMBRIDGE_AuracronDynamicRealmSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class AAuracronDynamicRail;
class APawn;
enum class EAuracronLayerEvolutionStage : uint8;
enum class EAuracronRailType : uint8;
enum class EAuracronRealmLayer : uint8;
enum class EAuracronSigilType : uint8;
enum class EPrismalIslandType : uint8;
enum class ERealmEvolutionPhase : uint8;
enum class ERealmTransitionType : uint8;
enum class ESigilType : uint8;
struct FAuracronAdvancedRealmTransition;
struct FAuracronGlobalEvolutionData;
struct FAuracronLayerEvolutionData;
struct FAuracronRealmLayerData;
struct FGuid;
struct FPlayerRailExperience;
struct FPrismalIslandData;
struct FRailGenerationConfig;

// ********** Begin ScriptStruct FAuracronRealmLayerData *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_64_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmLayerData;
// ********** End ScriptStruct FAuracronRealmLayerData *********************************************

// ********** Begin ScriptStruct FPrismalIslandData ************************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_123_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPrismalIslandData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPrismalIslandData;
// ********** End ScriptStruct FPrismalIslandData **************************************************

// ********** Begin ScriptStruct FAuracronRealmLayerEvolutionData **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_169_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronRealmLayerEvolutionData;
// ********** End ScriptStruct FAuracronRealmLayerEvolutionData ************************************

// ********** Begin ScriptStruct FAuracronAdvancedRealmTransition **********************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_210_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronAdvancedRealmTransition;
// ********** End ScriptStruct FAuracronAdvancedRealmTransition ************************************

// ********** Begin Class UAuracronDynamicRealmSubsystem *******************************************
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnSigilEvolved); \
	DECLARE_FUNCTION(execOnSigilDeactivated); \
	DECLARE_FUNCTION(execOnSigilActivated); \
	DECLARE_FUNCTION(execValidateSystemIntegrity); \
	DECLARE_FUNCTION(execFinalizeSystemInitialization); \
	DECLARE_FUNCTION(execGetRailGenerationConfig); \
	DECLARE_FUNCTION(execSetRailGenerationConfig); \
	DECLARE_FUNCTION(execGetPlayerRailExperience); \
	DECLARE_FUNCTION(execDeactivateAllRailsInLayer); \
	DECLARE_FUNCTION(execActivateAllRailsInLayer); \
	DECLARE_FUNCTION(execFindNearestRail); \
	DECLARE_FUNCTION(execGetRailsInLayer); \
	DECLARE_FUNCTION(execInitializeAdvancedRailSystem); \
	DECLARE_FUNCTION(execOnFusion20ActivatedInRealm); \
	DECLARE_FUNCTION(execOnSigilActivatedInRealm); \
	DECLARE_FUNCTION(execGetLayerResonanceValue); \
	DECLARE_FUNCTION(execIntegrateWithSigilSystem); \
	DECLARE_FUNCTION(execCancelAdvancedTransition); \
	DECLARE_FUNCTION(execGetActiveAdvancedTransitions); \
	DECLARE_FUNCTION(execInitiateAdvancedLayerTransition); \
	DECLARE_FUNCTION(execGetGlobalEvolutionData); \
	DECLARE_FUNCTION(execEnableLayerAutoEvolution); \
	DECLARE_FUNCTION(execSetLayerEvolutionSpeed); \
	DECLARE_FUNCTION(execForceLayerEvolution); \
	DECLARE_FUNCTION(execSetLayerEvolutionRate); \
	DECLARE_FUNCTION(execGetLayerEvolutionData); \
	DECLARE_FUNCTION(execInitializeEvolutionSystem); \
	DECLARE_FUNCTION(execInitializeLayerEvolutionSystem); \
	DECLARE_FUNCTION(execDebugGenerateLayerContent); \
	DECLARE_FUNCTION(execDebugToggleLayerVisibility); \
	DECLARE_FUNCTION(execDebugShowLayerInfo); \
	DECLARE_FUNCTION(execSetLayerLOD); \
	DECLARE_FUNCTION(execGetLayerPerformanceMetric); \
	DECLARE_FUNCTION(execOptimizeLayerPerformance); \
	DECLARE_FUNCTION(execGetActiveRails); \
	DECLARE_FUNCTION(execUseRail); \
	DECLARE_FUNCTION(execActivateRail); \
	DECLARE_FUNCTION(execInitializeDynamicRails); \
	DECLARE_FUNCTION(execGetNearestPrismalIsland); \
	DECLARE_FUNCTION(execGetActivePrismalIslands); \
	DECLARE_FUNCTION(execActivatePrismalIsland); \
	DECLARE_FUNCTION(execUpdatePrismalFlow); \
	DECLARE_FUNCTION(execInitializePrismalFlow); \
	DECLARE_FUNCTION(execGetTransitionProgress); \
	DECLARE_FUNCTION(execIsActorInTransition); \
	DECLARE_FUNCTION(execRequestLayerTransition); \
	DECLARE_FUNCTION(execGetActorsInLayer); \
	DECLARE_FUNCTION(execGetLayerCenter); \
	DECLARE_FUNCTION(execGetLocationLayer); \
	DECLARE_FUNCTION(execGetActorLayer); \
	DECLARE_FUNCTION(execUnregisterActorFromLayer); \
	DECLARE_FUNCTION(execRegisterActorToLayer); \
	DECLARE_FUNCTION(execGetLayerData); \
	DECLARE_FUNCTION(execIsLayerActive); \
	DECLARE_FUNCTION(execDeactivateLayer); \
	DECLARE_FUNCTION(execActivateLayer); \
	DECLARE_FUNCTION(execTransitionToPhase); \
	DECLARE_FUNCTION(execUpdateRealmEvolution); \
	DECLARE_FUNCTION(execInitializeRealmLayers);


AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();

#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAuracronDynamicRealmSubsystem(); \
	friend struct Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UAuracronDynamicRealmSubsystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AuracronDynamicRealmBridge"), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UAuracronDynamicRealmSubsystem)


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAuracronDynamicRealmSubsystem(UAuracronDynamicRealmSubsystem&&) = delete; \
	UAuracronDynamicRealmSubsystem(const UAuracronDynamicRealmSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAuracronDynamicRealmSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAuracronDynamicRealmSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAuracronDynamicRealmSubsystem) \
	NO_API virtual ~UAuracronDynamicRealmSubsystem();


#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_280_PROLOG
#define FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_INCLASS_NO_PURE_DECLS \
	FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h_283_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAuracronDynamicRealmSubsystem;

// ********** End Class UAuracronDynamicRealmSubsystem *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h

// ********** Begin Enum ESigilType ****************************************************************
#define FOREACH_ENUM_ESIGILTYPE(op) \
	op(ESigilType::None) \
	op(ESigilType::Aegis) \
	op(ESigilType::Tempest) \
	op(ESigilType::Inferno) \
	op(ESigilType::Glacial) \
	op(ESigilType::Umbral) \
	op(ESigilType::Prismatic) \
	op(ESigilType::Quantum) \
	op(ESigilType::Celestial) 

enum class ESigilType : uint8;
template<> struct TIsUEnumClass<ESigilType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ESigilType>();
// ********** End Enum ESigilType ******************************************************************

// ********** Begin Enum EAuracronSigilType ********************************************************
#define FOREACH_ENUM_EAURACRONSIGILTYPE(op) \
	op(EAuracronSigilType::None) \
	op(EAuracronSigilType::Aegis) \
	op(EAuracronSigilType::Tempest) \
	op(EAuracronSigilType::Inferno) \
	op(EAuracronSigilType::Glacial) \
	op(EAuracronSigilType::Umbral) \
	op(EAuracronSigilType::Prismatic) \
	op(EAuracronSigilType::Quantum) \
	op(EAuracronSigilType::Celestial) \
	op(EAuracronSigilType::Fusion20) 

enum class EAuracronSigilType : uint8;
template<> struct TIsUEnumClass<EAuracronSigilType> { enum { Value = true }; };
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronSigilType>();
// ********** End Enum EAuracronSigilType **********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
