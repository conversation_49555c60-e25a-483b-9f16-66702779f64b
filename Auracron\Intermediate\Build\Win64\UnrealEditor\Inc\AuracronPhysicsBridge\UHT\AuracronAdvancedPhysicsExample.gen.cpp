// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAdvancedPhysicsExample.h"
#include "AuracronPhysicsBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdvancedPhysicsExample() {}

// ********** Begin Cross Module References ********************************************************
AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample();
AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample_NoRegister();
AURACRONPHYSICSBRIDGE_API UClass* Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister();
AURACRONPHYSICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronFluidType();
AURACRONPHYSICSBRIDGE_API UEnum* Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsQuality();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedConstraintConfig();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronChaosClothConfig();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFluidSimulationConfig();
AURACRONPHYSICSBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronSoftBodyConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronPhysicsBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAuracronAdvancedPhysicsExample Function ConvertToJellySoftBody **********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics
{
	struct AuracronAdvancedPhysicsExample_eventConvertToJellySoftBody_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Soft Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Convert actor to jelly soft body */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert actor to jelly soft body" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventConvertToJellySoftBody_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "ConvertToJellySoftBody", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::AuracronAdvancedPhysicsExample_eventConvertToJellySoftBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::AuracronAdvancedPhysicsExample_eventConvertToJellySoftBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execConvertToJellySoftBody)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConvertToJellySoftBody(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function ConvertToJellySoftBody ************

// ********** Begin Class AAuracronAdvancedPhysicsExample Function ConvertToRubberSoftBody *********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics
{
	struct AuracronAdvancedPhysicsExample_eventConvertToRubberSoftBody_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Soft Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Convert actor to rubber soft body */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Convert actor to rubber soft body" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventConvertToRubberSoftBody_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "ConvertToRubberSoftBody", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::AuracronAdvancedPhysicsExample_eventConvertToRubberSoftBody_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::AuracronAdvancedPhysicsExample_eventConvertToRubberSoftBody_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execConvertToRubberSoftBody)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ConvertToRubberSoftBody(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function ConvertToRubberSoftBody ***********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateAdvancedVehicleExample ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateAdvancedVehicleExample_Parms
	{
		AActor* VehicleActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Vehicle Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create advanced vehicle */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create advanced vehicle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VehicleActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::NewProp_VehicleActor = { "VehicleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateAdvancedVehicleExample_Parms, VehicleActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::NewProp_VehicleActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateAdvancedVehicleExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::AuracronAdvancedPhysicsExample_eventCreateAdvancedVehicleExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::AuracronAdvancedPhysicsExample_eventCreateAdvancedVehicleExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateAdvancedVehicleExample)
{
	P_GET_OBJECT(AActor,Z_Param_VehicleActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateAdvancedVehicleExample(Z_Param_VehicleActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateAdvancedVehicleExample ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateClothSimulationExample ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateClothSimulationExample_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Chaos Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create cloth simulation on actor */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create cloth simulation on actor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateClothSimulationExample_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateClothSimulationExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateClothSimulationExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateClothSimulationExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateClothSimulationExample)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateClothSimulationExample(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateClothSimulationExample ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateLavaSimulationExample *****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateLavaSimulationExample_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Fluid Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create example lava simulation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create example lava simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateLavaSimulationExample_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateLavaSimulationExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateLavaSimulationExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateLavaSimulationExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateLavaSimulationExample)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateLavaSimulationExample(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateLavaSimulationExample *******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateMotorConstraintExample ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateMotorConstraintExample_Parms
	{
		AActor* FirstActor;
		AActor* SecondActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Advanced Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create example motor constraint */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create example motor constraint" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FirstActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SecondActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::NewProp_FirstActor = { "FirstActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateMotorConstraintExample_Parms, FirstActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::NewProp_SecondActor = { "SecondActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateMotorConstraintExample_Parms, SecondActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::NewProp_FirstActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::NewProp_SecondActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateMotorConstraintExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::AuracronAdvancedPhysicsExample_eventCreateMotorConstraintExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::AuracronAdvancedPhysicsExample_eventCreateMotorConstraintExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateMotorConstraintExample)
{
	P_GET_OBJECT(AActor,Z_Param_FirstActor);
	P_GET_OBJECT(AActor,Z_Param_SecondActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateMotorConstraintExample(Z_Param_FirstActor,Z_Param_SecondActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateMotorConstraintExample ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateSpringConstraintExample ***
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateSpringConstraintExample_Parms
	{
		AActor* FirstActor;
		AActor* SecondActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Advanced Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create example spring constraint */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create example spring constraint" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FirstActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SecondActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::NewProp_FirstActor = { "FirstActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateSpringConstraintExample_Parms, FirstActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::NewProp_SecondActor = { "SecondActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateSpringConstraintExample_Parms, SecondActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::NewProp_FirstActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::NewProp_SecondActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateSpringConstraintExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::AuracronAdvancedPhysicsExample_eventCreateSpringConstraintExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::AuracronAdvancedPhysicsExample_eventCreateSpringConstraintExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateSpringConstraintExample)
{
	P_GET_OBJECT(AActor,Z_Param_FirstActor);
	P_GET_OBJECT(AActor,Z_Param_SecondActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSpringConstraintExample(Z_Param_FirstActor,Z_Param_SecondActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateSpringConstraintExample *****

// ********** Begin Class AAuracronAdvancedPhysicsExample Function CreateWaterSimulationExample ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventCreateWaterSimulationExample_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Fluid Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Create example water simulation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create example water simulation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventCreateWaterSimulationExample_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "CreateWaterSimulationExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateWaterSimulationExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::AuracronAdvancedPhysicsExample_eventCreateWaterSimulationExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execCreateWaterSimulationExample)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateWaterSimulationExample(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function CreateWaterSimulationExample ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateClothTearing *********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics
{
	struct AuracronAdvancedPhysicsExample_eventDemonstrateClothTearing_Parms
	{
		AActor* ClothActor;
		FVector TearLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Chaos Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate cloth tearing */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate cloth tearing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TearLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TearLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::NewProp_ClothActor = { "ClothActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventDemonstrateClothTearing_Parms, ClothActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::NewProp_TearLocation = { "TearLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventDemonstrateClothTearing_Parms, TearLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TearLocation_MetaData), NewProp_TearLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::NewProp_ClothActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::NewProp_TearLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateClothTearing", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateClothTearing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateClothTearing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateClothTearing)
{
	P_GET_OBJECT(AActor,Z_Param_ClothActor);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_TearLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateClothTearing(Z_Param_ClothActor,Z_Param_Out_TearLocation);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateClothTearing ***********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateClothWindEffects *****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics
{
	struct AuracronAdvancedPhysicsExample_eventDemonstrateClothWindEffects_Parms
	{
		AActor* ClothActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Chaos Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate cloth wind effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate cloth wind effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::NewProp_ClothActor = { "ClothActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventDemonstrateClothWindEffects_Parms, ClothActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::NewProp_ClothActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateClothWindEffects", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateClothWindEffects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateClothWindEffects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateClothWindEffects)
{
	P_GET_OBJECT(AActor,Z_Param_ClothActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateClothWindEffects(Z_Param_ClothActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateClothWindEffects *******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateConstraintBreaking ***
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Advanced Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate constraint breaking */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate constraint breaking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateConstraintBreaking", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateConstraintBreaking)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateConstraintBreaking();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateConstraintBreaking *****

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateFluidInteractions ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Fluid Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate fluid interactions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate fluid interactions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateFluidInteractions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateFluidInteractions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateFluidInteractions();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateFluidInteractions ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstratePerformanceMonitoring 
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate physics performance monitoring */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate physics performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstratePerformanceMonitoring", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstratePerformanceMonitoring)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstratePerformanceMonitoring();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstratePerformanceMonitoring **

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateSoftBodyDeformation **
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics
{
	struct AuracronAdvancedPhysicsExample_eventDemonstrateSoftBodyDeformation_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Soft Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate soft body deformation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate soft body deformation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventDemonstrateSoftBodyDeformation_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateSoftBodyDeformation", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateSoftBodyDeformation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateSoftBodyDeformation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateSoftBodyDeformation)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateSoftBodyDeformation(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateSoftBodyDeformation ****

// ********** Begin Class AAuracronAdvancedPhysicsExample Function DemonstrateVehicleSuspension ****
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics
{
	struct AuracronAdvancedPhysicsExample_eventDemonstrateVehicleSuspension_Parms
	{
		AActor* VehicleActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Vehicle Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate vehicle suspension */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate vehicle suspension" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VehicleActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::NewProp_VehicleActor = { "VehicleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventDemonstrateVehicleSuspension_Parms, VehicleActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::NewProp_VehicleActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "DemonstrateVehicleSuspension", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateVehicleSuspension_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::AuracronAdvancedPhysicsExample_eventDemonstrateVehicleSuspension_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execDemonstrateVehicleSuspension)
{
	P_GET_OBJECT(AActor,Z_Param_VehicleActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateVehicleSuspension(Z_Param_VehicleActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function DemonstrateVehicleSuspension ******

// ********** Begin Class AAuracronAdvancedPhysicsExample Function ExportPhysicsAnalyticsExample ***
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Export physics analytics example */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Export physics analytics example" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "ExportPhysicsAnalyticsExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execExportPhysicsAnalyticsExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportPhysicsAnalyticsExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function ExportPhysicsAnalyticsExample *****

// ********** Begin Class AAuracronAdvancedPhysicsExample Function InitializeClothExample **********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Chaos Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize cloth system with example configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize cloth system with example configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "InitializeClothExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execInitializeClothExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeClothExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function InitializeClothExample ************

// ********** Begin Class AAuracronAdvancedPhysicsExample Function InitializeFluidSimulationExample 
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Fluid Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize fluid simulation with example configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize fluid simulation with example configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "InitializeFluidSimulationExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execInitializeFluidSimulationExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeFluidSimulationExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function InitializeFluidSimulationExample **

// ********** Begin Class AAuracronAdvancedPhysicsExample Function InitializeSoftBodyExample *******
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Soft Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize soft body system with example configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize soft body system with example configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "InitializeSoftBodyExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execInitializeSoftBodyExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeSoftBodyExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function InitializeSoftBodyExample *********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function InitializeVehiclePhysicsExample *
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Vehicle Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize vehicle physics example */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize vehicle physics example" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "InitializeVehiclePhysicsExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execInitializeVehiclePhysicsExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeVehiclePhysicsExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function InitializeVehiclePhysicsExample ***

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnClothTornExample **************
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms
	{
		AActor* ClothActor;
		FVector TearLocation;
		float TearRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ClothActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TearLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TearRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_ClothActor = { "ClothActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms, ClothActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_TearLocation = { "TearLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms, TearLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_TearRadius = { "TearRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms, TearRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_ClothActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_TearLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::NewProp_TearRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnClothTornExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00880401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::AuracronAdvancedPhysicsExample_eventOnClothTornExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnClothTornExample)
{
	P_GET_OBJECT(AActor,Z_Param_ClothActor);
	P_GET_STRUCT(FVector,Z_Param_TearLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TearRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnClothTornExample(Z_Param_ClothActor,Z_Param_TearLocation,Z_Param_TearRadius);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnClothTornExample ****************

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnConstraintBrokenExample *******
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms
	{
		int32 ConstraintID;
		AActor* FirstActor;
		AActor* SecondActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ConstraintID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FirstActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SecondActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_ConstraintID = { "ConstraintID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms, ConstraintID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_FirstActor = { "FirstActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms, FirstActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_SecondActor = { "SecondActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms, SecondActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_ConstraintID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_FirstActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::NewProp_SecondActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnConstraintBrokenExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::AuracronAdvancedPhysicsExample_eventOnConstraintBrokenExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnConstraintBrokenExample)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ConstraintID);
	P_GET_OBJECT(AActor,Z_Param_FirstActor);
	P_GET_OBJECT(AActor,Z_Param_SecondActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnConstraintBrokenExample(Z_Param_ConstraintID,Z_Param_FirstActor,Z_Param_SecondActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnConstraintBrokenExample *********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnFluidSimulationCreatedExample *
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms
	{
		FVector Location;
		EAuracronFluidType FluidType;
		int32 ParticleCount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Event Handlers ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Event Handlers ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FluidType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FluidType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ParticleCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_FluidType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_FluidType = { "FluidType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms, FluidType), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronFluidType, METADATA_PARAMS(0, nullptr) }; // 935379780
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_ParticleCount = { "ParticleCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms, ParticleCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_FluidType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_FluidType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::NewProp_ParticleCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnFluidSimulationCreatedExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00880401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnFluidSimulationCreatedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnFluidSimulationCreatedExample)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_ENUM(EAuracronFluidType,Z_Param_FluidType);
	P_GET_PROPERTY(FIntProperty,Z_Param_ParticleCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnFluidSimulationCreatedExample(Z_Param_Location,EAuracronFluidType(Z_Param_FluidType),Z_Param_ParticleCount);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnFluidSimulationCreatedExample ***

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnPhysicsPerformanceUpdatedExample 
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms
	{
		float FrameTime;
		int32 ActiveObjects;
		float MemoryUsage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveObjects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_FrameTime = { "FrameTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms, FrameTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_ActiveObjects = { "ActiveObjects", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms, ActiveObjects), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_MemoryUsage = { "MemoryUsage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms, MemoryUsage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_FrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_ActiveObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::NewProp_MemoryUsage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnPhysicsPerformanceUpdatedExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnPhysicsPerformanceUpdatedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnPhysicsPerformanceUpdatedExample)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_FrameTime);
	P_GET_PROPERTY(FIntProperty,Z_Param_ActiveObjects);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MemoryUsage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPhysicsPerformanceUpdatedExample(Z_Param_FrameTime,Z_Param_ActiveObjects,Z_Param_MemoryUsage);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnPhysicsPerformanceUpdatedExample 

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnSoftBodyDeformedExample *******
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms
	{
		AActor* TargetActor;
		FVector DeformationLocation;
		float DeformationForce;
		float DeformationRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DeformationLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeformationForce;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeformationRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationLocation = { "DeformationLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms, DeformationLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationForce = { "DeformationForce", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms, DeformationForce), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationRadius = { "DeformationRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms, DeformationRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::NewProp_DeformationRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnSoftBodyDeformedExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00880401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::AuracronAdvancedPhysicsExample_eventOnSoftBodyDeformedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnSoftBodyDeformedExample)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_STRUCT(FVector,Z_Param_DeformationLocation);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeformationForce);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeformationRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSoftBodyDeformedExample(Z_Param_TargetActor,Z_Param_DeformationLocation,Z_Param_DeformationForce,Z_Param_DeformationRadius);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnSoftBodyDeformedExample *********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function OnVehicleCreatedExample *********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventOnVehicleCreatedExample_Parms
	{
		AActor* VehicleActor;
		FString VehicleType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VehicleActor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VehicleType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::NewProp_VehicleActor = { "VehicleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnVehicleCreatedExample_Parms, VehicleActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::NewProp_VehicleType = { "VehicleType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventOnVehicleCreatedExample_Parms, VehicleType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::NewProp_VehicleActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::NewProp_VehicleType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "OnVehicleCreatedExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnVehicleCreatedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::AuracronAdvancedPhysicsExample_eventOnVehicleCreatedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execOnVehicleCreatedExample)
{
	P_GET_OBJECT(AActor,Z_Param_VehicleActor);
	P_GET_PROPERTY(FStrProperty,Z_Param_VehicleType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnVehicleCreatedExample(Z_Param_VehicleActor,Z_Param_VehicleType);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function OnVehicleCreatedExample ***********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function RunAdvancedPhysicsScenario ******
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Complete" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Run complete advanced physics scenario */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Run complete advanced physics scenario" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "RunAdvancedPhysicsScenario", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execRunAdvancedPhysicsScenario)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunAdvancedPhysicsScenario();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function RunAdvancedPhysicsScenario ********

// ********** Begin Class AAuracronAdvancedPhysicsExample Function SetPhysicsQualityExample ********
struct Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics
{
	struct AuracronAdvancedPhysicsExample_eventSetPhysicsQualityExample_Parms
	{
		EAuracronPhysicsQuality QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set physics quality example */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set physics quality example" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedPhysicsExample_eventSetPhysicsQualityExample_Parms, QualityLevel), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsQuality, METADATA_PARAMS(0, nullptr) }; // 401511254
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedPhysicsExample, nullptr, "SetPhysicsQualityExample", Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::AuracronAdvancedPhysicsExample_eventSetPhysicsQualityExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::AuracronAdvancedPhysicsExample_eventSetPhysicsQualityExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedPhysicsExample::execSetPhysicsQualityExample)
{
	P_GET_ENUM(EAuracronPhysicsQuality,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPhysicsQualityExample(EAuracronPhysicsQuality(Z_Param_QualityLevel));
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedPhysicsExample Function SetPhysicsQualityExample **********

// ********** Begin Class AAuracronAdvancedPhysicsExample ******************************************
void AAuracronAdvancedPhysicsExample::StaticRegisterNativesAAuracronAdvancedPhysicsExample()
{
	UClass* Class = AAuracronAdvancedPhysicsExample::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ConvertToJellySoftBody", &AAuracronAdvancedPhysicsExample::execConvertToJellySoftBody },
		{ "ConvertToRubberSoftBody", &AAuracronAdvancedPhysicsExample::execConvertToRubberSoftBody },
		{ "CreateAdvancedVehicleExample", &AAuracronAdvancedPhysicsExample::execCreateAdvancedVehicleExample },
		{ "CreateClothSimulationExample", &AAuracronAdvancedPhysicsExample::execCreateClothSimulationExample },
		{ "CreateLavaSimulationExample", &AAuracronAdvancedPhysicsExample::execCreateLavaSimulationExample },
		{ "CreateMotorConstraintExample", &AAuracronAdvancedPhysicsExample::execCreateMotorConstraintExample },
		{ "CreateSpringConstraintExample", &AAuracronAdvancedPhysicsExample::execCreateSpringConstraintExample },
		{ "CreateWaterSimulationExample", &AAuracronAdvancedPhysicsExample::execCreateWaterSimulationExample },
		{ "DemonstrateClothTearing", &AAuracronAdvancedPhysicsExample::execDemonstrateClothTearing },
		{ "DemonstrateClothWindEffects", &AAuracronAdvancedPhysicsExample::execDemonstrateClothWindEffects },
		{ "DemonstrateConstraintBreaking", &AAuracronAdvancedPhysicsExample::execDemonstrateConstraintBreaking },
		{ "DemonstrateFluidInteractions", &AAuracronAdvancedPhysicsExample::execDemonstrateFluidInteractions },
		{ "DemonstratePerformanceMonitoring", &AAuracronAdvancedPhysicsExample::execDemonstratePerformanceMonitoring },
		{ "DemonstrateSoftBodyDeformation", &AAuracronAdvancedPhysicsExample::execDemonstrateSoftBodyDeformation },
		{ "DemonstrateVehicleSuspension", &AAuracronAdvancedPhysicsExample::execDemonstrateVehicleSuspension },
		{ "ExportPhysicsAnalyticsExample", &AAuracronAdvancedPhysicsExample::execExportPhysicsAnalyticsExample },
		{ "InitializeClothExample", &AAuracronAdvancedPhysicsExample::execInitializeClothExample },
		{ "InitializeFluidSimulationExample", &AAuracronAdvancedPhysicsExample::execInitializeFluidSimulationExample },
		{ "InitializeSoftBodyExample", &AAuracronAdvancedPhysicsExample::execInitializeSoftBodyExample },
		{ "InitializeVehiclePhysicsExample", &AAuracronAdvancedPhysicsExample::execInitializeVehiclePhysicsExample },
		{ "OnClothTornExample", &AAuracronAdvancedPhysicsExample::execOnClothTornExample },
		{ "OnConstraintBrokenExample", &AAuracronAdvancedPhysicsExample::execOnConstraintBrokenExample },
		{ "OnFluidSimulationCreatedExample", &AAuracronAdvancedPhysicsExample::execOnFluidSimulationCreatedExample },
		{ "OnPhysicsPerformanceUpdatedExample", &AAuracronAdvancedPhysicsExample::execOnPhysicsPerformanceUpdatedExample },
		{ "OnSoftBodyDeformedExample", &AAuracronAdvancedPhysicsExample::execOnSoftBodyDeformedExample },
		{ "OnVehicleCreatedExample", &AAuracronAdvancedPhysicsExample::execOnVehicleCreatedExample },
		{ "RunAdvancedPhysicsScenario", &AAuracronAdvancedPhysicsExample::execRunAdvancedPhysicsScenario },
		{ "SetPhysicsQualityExample", &AAuracronAdvancedPhysicsExample::execSetPhysicsQualityExample },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample;
UClass* AAuracronAdvancedPhysicsExample::GetPrivateStaticClass()
{
	using TClass = AAuracronAdvancedPhysicsExample;
	if (!Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdvancedPhysicsExample"),
			Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.InnerSingleton,
			StaticRegisterNativesAAuracronAdvancedPhysicsExample,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample_NoRegister()
{
	return AAuracronAdvancedPhysicsExample::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Example implementation showcasing advanced physics features\n * This demonstrates how to use the expanded AuracronPhysicsBridge with UE 5.6 features\n */" },
#endif
		{ "IncludePath", "AuracronAdvancedPhysicsExample.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example implementation showcasing advanced physics features\nThis demonstrates how to use the expanded AuracronPhysicsBridge with UE 5.6 features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsBridge_MetaData[] = {
		{ "Category", "Physics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Physics Bridge Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Bridge Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleFluidConfig_MetaData[] = {
		{ "Category", "Fluid Simulation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Fluid Simulation Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Fluid Simulation Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleSoftBodyConfig_MetaData[] = {
		{ "Category", "Soft Body" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Soft Body Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Soft Body Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleClothConfig_MetaData[] = {
		{ "Category", "Chaos Cloth" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Cloth Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Cloth Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleConstraintConfig_MetaData[] = {
		{ "Category", "Advanced Constraints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Constraint Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Constraint Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhysicsQuality_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Physics Quality Level */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Physics Quality Level" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleActors_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example actors for demonstrations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedPhysicsExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example actors for demonstrations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PhysicsBridge;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleFluidConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleSoftBodyConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleClothConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleConstraintConfig;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhysicsQuality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhysicsQuality;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ExampleActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExampleActors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToJellySoftBody, "ConvertToJellySoftBody" }, // 3918859132
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ConvertToRubberSoftBody, "ConvertToRubberSoftBody" }, // 3550832678
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateAdvancedVehicleExample, "CreateAdvancedVehicleExample" }, // 498234983
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateClothSimulationExample, "CreateClothSimulationExample" }, // 3946562192
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateLavaSimulationExample, "CreateLavaSimulationExample" }, // 3735887742
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateMotorConstraintExample, "CreateMotorConstraintExample" }, // 1170461193
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateSpringConstraintExample, "CreateSpringConstraintExample" }, // 114313916
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_CreateWaterSimulationExample, "CreateWaterSimulationExample" }, // 2664995492
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothTearing, "DemonstrateClothTearing" }, // 2183912967
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateClothWindEffects, "DemonstrateClothWindEffects" }, // 2935705901
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateConstraintBreaking, "DemonstrateConstraintBreaking" }, // 1646870381
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateFluidInteractions, "DemonstrateFluidInteractions" }, // 3689213903
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstratePerformanceMonitoring, "DemonstratePerformanceMonitoring" }, // 933101000
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateSoftBodyDeformation, "DemonstrateSoftBodyDeformation" }, // 3092706663
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_DemonstrateVehicleSuspension, "DemonstrateVehicleSuspension" }, // 3185906209
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_ExportPhysicsAnalyticsExample, "ExportPhysicsAnalyticsExample" }, // 2940108476
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeClothExample, "InitializeClothExample" }, // 3638924839
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeFluidSimulationExample, "InitializeFluidSimulationExample" }, // 939464635
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeSoftBodyExample, "InitializeSoftBodyExample" }, // 3923048091
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_InitializeVehiclePhysicsExample, "InitializeVehiclePhysicsExample" }, // 2156624533
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnClothTornExample, "OnClothTornExample" }, // 3969684777
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnConstraintBrokenExample, "OnConstraintBrokenExample" }, // 1163507417
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnFluidSimulationCreatedExample, "OnFluidSimulationCreatedExample" }, // 3692288481
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnPhysicsPerformanceUpdatedExample, "OnPhysicsPerformanceUpdatedExample" }, // 2947251492
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnSoftBodyDeformedExample, "OnSoftBodyDeformedExample" }, // 2100801195
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_OnVehicleCreatedExample, "OnVehicleCreatedExample" }, // 3662559110
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_RunAdvancedPhysicsScenario, "RunAdvancedPhysicsScenario" }, // 2419373382
		{ &Z_Construct_UFunction_AAuracronAdvancedPhysicsExample_SetPhysicsQualityExample, "SetPhysicsQualityExample" }, // 150855163
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronAdvancedPhysicsExample>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsBridge = { "PhysicsBridge", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, PhysicsBridge), Z_Construct_UClass_UAuracronPhysicsBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsBridge_MetaData), NewProp_PhysicsBridge_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleFluidConfig = { "ExampleFluidConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, ExampleFluidConfig), Z_Construct_UScriptStruct_FAuracronFluidSimulationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleFluidConfig_MetaData), NewProp_ExampleFluidConfig_MetaData) }; // 1990973304
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleSoftBodyConfig = { "ExampleSoftBodyConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, ExampleSoftBodyConfig), Z_Construct_UScriptStruct_FAuracronSoftBodyConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleSoftBodyConfig_MetaData), NewProp_ExampleSoftBodyConfig_MetaData) }; // 1216563249
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleClothConfig = { "ExampleClothConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, ExampleClothConfig), Z_Construct_UScriptStruct_FAuracronChaosClothConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleClothConfig_MetaData), NewProp_ExampleClothConfig_MetaData) }; // 3966315721
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleConstraintConfig = { "ExampleConstraintConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, ExampleConstraintConfig), Z_Construct_UScriptStruct_FAuracronAdvancedConstraintConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleConstraintConfig_MetaData), NewProp_ExampleConstraintConfig_MetaData) }; // 2115335746
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsQuality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsQuality = { "PhysicsQuality", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, PhysicsQuality), Z_Construct_UEnum_AuracronPhysicsBridge_EAuracronPhysicsQuality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhysicsQuality_MetaData), NewProp_PhysicsQuality_MetaData) }; // 401511254
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleActors_Inner = { "ExampleActors", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleActors = { "ExampleActors", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedPhysicsExample, ExampleActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleActors_MetaData), NewProp_ExampleActors_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleFluidConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleSoftBodyConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleClothConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleConstraintConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsQuality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_PhysicsQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::NewProp_ExampleActors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronPhysicsBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::ClassParams = {
	&AAuracronAdvancedPhysicsExample::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronAdvancedPhysicsExample()
{
	if (!Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.OuterSingleton, Z_Construct_UClass_AAuracronAdvancedPhysicsExample_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronAdvancedPhysicsExample);
AAuracronAdvancedPhysicsExample::~AAuracronAdvancedPhysicsExample() {}
// ********** End Class AAuracronAdvancedPhysicsExample ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h__Script_AuracronPhysicsBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronAdvancedPhysicsExample, AAuracronAdvancedPhysicsExample::StaticClass, TEXT("AAuracronAdvancedPhysicsExample"), &Z_Registration_Info_UClass_AAuracronAdvancedPhysicsExample, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronAdvancedPhysicsExample), 2053750280U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h__Script_AuracronPhysicsBridge_464796904(TEXT("/Script/AuracronPhysicsBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h__Script_AuracronPhysicsBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronPhysicsBridge_Public_AuracronAdvancedPhysicsExample_h__Script_AuracronPhysicsBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
