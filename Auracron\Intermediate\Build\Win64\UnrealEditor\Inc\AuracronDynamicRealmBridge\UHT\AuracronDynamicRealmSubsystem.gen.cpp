// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicRealmSubsystem.h"
#include "ActiveGameplayEffectHandle.h"
#include "AuracronDynamicRealmBridge/Public/AuracronDynamicRealmBridge.h"
#include "Engine/TimerHandle.h"
#include "GameplayAbilitySpecHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronDynamicRealmSubsystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronDynamicRail_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_AAuracronPrismalFlow_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UClass* Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType();
AURACRONDYNAMICREALMBRIDGE_API UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UFunction* Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerEvolutionData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmLayerData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerRailExperience();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalIslandData();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailGenerationConfig();
AURACRONDYNAMICREALMBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FRailSystemMetrics();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FBox();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FActiveGameplayEffectHandle();
GAMEPLAYABILITIES_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronDynamicRealmBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum ESigilType ****************************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESigilType;
static UEnum* ESigilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESigilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESigilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("ESigilType"));
	}
	return Z_Registration_Info_UEnum_ESigilType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<ESigilType>()
{
	return ESigilType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.DisplayName", "Aegis" },
		{ "Aegis.Name", "ESigilType::Aegis" },
		{ "BlueprintType", "true" },
		{ "Celestial.DisplayName", "Celestial" },
		{ "Celestial.Name", "ESigilType::Celestial" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sigil Type Enums\n" },
#endif
		{ "Glacial.DisplayName", "Glacial" },
		{ "Glacial.Name", "ESigilType::Glacial" },
		{ "Inferno.DisplayName", "Inferno" },
		{ "Inferno.Name", "ESigilType::Inferno" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESigilType::None" },
		{ "Prismatic.DisplayName", "Prismatic" },
		{ "Prismatic.Name", "ESigilType::Prismatic" },
		{ "Quantum.DisplayName", "Quantum" },
		{ "Quantum.Name", "ESigilType::Quantum" },
		{ "Tempest.DisplayName", "Tempest" },
		{ "Tempest.Name", "ESigilType::Tempest" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigil Type Enums" },
#endif
		{ "Umbral.DisplayName", "Umbral" },
		{ "Umbral.Name", "ESigilType::Umbral" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESigilType::None", (int64)ESigilType::None },
		{ "ESigilType::Aegis", (int64)ESigilType::Aegis },
		{ "ESigilType::Tempest", (int64)ESigilType::Tempest },
		{ "ESigilType::Inferno", (int64)ESigilType::Inferno },
		{ "ESigilType::Glacial", (int64)ESigilType::Glacial },
		{ "ESigilType::Umbral", (int64)ESigilType::Umbral },
		{ "ESigilType::Prismatic", (int64)ESigilType::Prismatic },
		{ "ESigilType::Quantum", (int64)ESigilType::Quantum },
		{ "ESigilType::Celestial", (int64)ESigilType::Celestial },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"ESigilType",
	"ESigilType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType()
{
	if (!Z_Registration_Info_UEnum_ESigilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESigilType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESigilType.InnerSingleton;
}
// ********** End Enum ESigilType ******************************************************************

// ********** Begin Enum EAuracronSigilType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronSigilType;
static UEnum* EAuracronSigilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronSigilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("EAuracronSigilType"));
	}
	return Z_Registration_Info_UEnum_EAuracronSigilType.OuterSingleton;
}
template<> AURACRONDYNAMICREALMBRIDGE_API UEnum* StaticEnum<EAuracronSigilType>()
{
	return EAuracronSigilType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.DisplayName", "Aegis" },
		{ "Aegis.Name", "EAuracronSigilType::Aegis" },
		{ "BlueprintType", "true" },
		{ "Celestial.DisplayName", "Celestial" },
		{ "Celestial.Name", "EAuracronSigilType::Celestial" },
		{ "Fusion20.DisplayName", "Fusion 20" },
		{ "Fusion20.Name", "EAuracronSigilType::Fusion20" },
		{ "Glacial.DisplayName", "Glacial" },
		{ "Glacial.Name", "EAuracronSigilType::Glacial" },
		{ "Inferno.DisplayName", "Inferno" },
		{ "Inferno.Name", "EAuracronSigilType::Inferno" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAuracronSigilType::None" },
		{ "Prismatic.DisplayName", "Prismatic" },
		{ "Prismatic.Name", "EAuracronSigilType::Prismatic" },
		{ "Quantum.DisplayName", "Quantum" },
		{ "Quantum.Name", "EAuracronSigilType::Quantum" },
		{ "Tempest.DisplayName", "Tempest" },
		{ "Tempest.Name", "EAuracronSigilType::Tempest" },
		{ "Umbral.DisplayName", "Umbral" },
		{ "Umbral.Name", "EAuracronSigilType::Umbral" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronSigilType::None", (int64)EAuracronSigilType::None },
		{ "EAuracronSigilType::Aegis", (int64)EAuracronSigilType::Aegis },
		{ "EAuracronSigilType::Tempest", (int64)EAuracronSigilType::Tempest },
		{ "EAuracronSigilType::Inferno", (int64)EAuracronSigilType::Inferno },
		{ "EAuracronSigilType::Glacial", (int64)EAuracronSigilType::Glacial },
		{ "EAuracronSigilType::Umbral", (int64)EAuracronSigilType::Umbral },
		{ "EAuracronSigilType::Prismatic", (int64)EAuracronSigilType::Prismatic },
		{ "EAuracronSigilType::Quantum", (int64)EAuracronSigilType::Quantum },
		{ "EAuracronSigilType::Celestial", (int64)EAuracronSigilType::Celestial },
		{ "EAuracronSigilType::Fusion20", (int64)EAuracronSigilType::Fusion20 },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	"EAuracronSigilType",
	"EAuracronSigilType",
	Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType()
{
	if (!Z_Registration_Info_UEnum_EAuracronSigilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronSigilType.InnerSingleton, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronSigilType.InnerSingleton;
}
// ********** End Enum EAuracronSigilType **********************************************************

// ********** Begin ScriptStruct FAuracronRealmLayerData *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData;
class UScriptStruct* FAuracronRealmLayerData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmLayerData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronRealmLayerData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data structure for realm layer configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data structure for realm layer configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerType_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer identifier */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerHeight_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** World height for this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World height for this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerBounds_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer bounds */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer bounds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Is layer currently active */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is layer currently active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPhase_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current evolution phase */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current evolution phase" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponents_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer-specific PCG components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer-specific PCG components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerActors_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Actors belonging to this layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actors belonging to this layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "Layer Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer performance metrics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer performance metrics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveActorCount_MetaData[] = {
		{ "Category", "Layer Data" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Layer Data" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerBounds;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPhase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PCGComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LayerActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerActors;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActiveActorCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmLayerData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerType = { "LayerType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, LayerType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerType_MetaData), NewProp_LayerType_MetaData) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerHeight = { "LayerHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, LayerHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerHeight_MetaData), NewProp_LayerHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerBounds = { "LayerBounds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, LayerBounds), Z_Construct_UScriptStruct_FBox, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerBounds_MetaData), NewProp_LayerBounds_MetaData) };
void Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAuracronRealmLayerData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronRealmLayerData), &Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_CurrentPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_CurrentPhase = { "CurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, CurrentPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPhase_MetaData), NewProp_CurrentPhase_MetaData) }; // 560471848
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_PCGComponents_Inner = { "PCGComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_PCGComponents = { "PCGComponents", nullptr, (EPropertyFlags)0x011400800000000d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, PCGComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponents_MetaData), NewProp_PCGComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerActors_Inner = { "LayerActors", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerActors = { "LayerActors", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, LayerActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerActors_MetaData), NewProp_LayerActors_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_ActiveActorCount = { "ActiveActorCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, ActiveActorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveActorCount_MetaData), NewProp_ActiveActorCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_CurrentPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_CurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_PCGComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_PCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LayerActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_ActiveActorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewProp_MemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmLayerData",
	Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::PropPointers),
	sizeof(FAuracronRealmLayerData),
	alignof(FAuracronRealmLayerData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmLayerData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmLayerData *********************************************

// ********** Begin ScriptStruct FPrismalIslandData ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalIslandData;
class UScriptStruct* FPrismalIslandData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalIslandData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalIslandData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalIslandData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("PrismalIslandData"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalIslandData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalIslandData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data structure for Prismal Flow island\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data structure for Prismal Flow island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandType_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Island type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Island location in world space */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island location in world space" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Island radius */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island radius" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Is island currently active */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is island currently active" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayersOnIsland_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Players currently on island */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Players currently on island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IslandEffects_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Island-specific effects */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Island-specific effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastActivationTime_MetaData[] = {
		{ "Category", "Island Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Last activation time */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Last activation time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayersOnIsland_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlayersOnIsland;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IslandEffects_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IslandEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastActivationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalIslandData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandType_MetaData), NewProp_IslandType_MetaData) }; // 1145382866
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
void Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FPrismalIslandData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPrismalIslandData), &Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_PlayersOnIsland_Inner = { "PlayersOnIsland", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_PlayersOnIsland = { "PlayersOnIsland", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, PlayersOnIsland), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayersOnIsland_MetaData), NewProp_PlayersOnIsland_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandEffects_Inner = { "IslandEffects", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandEffects = { "IslandEffects", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, IslandEffects), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IslandEffects_MetaData), NewProp_IslandEffects_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_LastActivationTime = { "LastActivationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalIslandData, LastActivationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastActivationTime_MetaData), NewProp_LastActivationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalIslandData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_PlayersOnIsland_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_PlayersOnIsland,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandEffects_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_IslandEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewProp_LastActivationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalIslandData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalIslandData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"PrismalIslandData",
	Z_Construct_UScriptStruct_FPrismalIslandData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalIslandData_Statics::PropPointers),
	sizeof(FPrismalIslandData),
	alignof(FPrismalIslandData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalIslandData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalIslandData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalIslandData()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalIslandData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalIslandData.InnerSingleton, Z_Construct_UScriptStruct_FPrismalIslandData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalIslandData.InnerSingleton;
}
// ********** End ScriptStruct FPrismalIslandData **************************************************

// ********** Begin ScriptStruct FAuracronRealmLayerEvolutionData **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData;
class UScriptStruct* FAuracronRealmLayerEvolutionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronRealmLayerEvolutionData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para dados de evolu\xc3\xa7\xc3\xa3o de camada\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para dados de evolu\xc3\xa7\xc3\xa3o de camada" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Layer_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionStage_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionProgress_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StabilityLevel_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyAccumulation_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastEvolutionTime_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionRate_MetaData[] = {
		{ "Category", "Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EvolutionStage_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EvolutionStage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StabilityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EnergyAccumulation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastEvolutionTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronRealmLayerEvolutionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Layer_MetaData), NewProp_Layer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionStage_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionStage = { "EvolutionStage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, EvolutionStage), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionStage_MetaData), NewProp_EvolutionStage_MetaData) }; // 3335440642
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionProgress = { "EvolutionProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, EvolutionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionProgress_MetaData), NewProp_EvolutionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_StabilityLevel = { "StabilityLevel", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, StabilityLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StabilityLevel_MetaData), NewProp_StabilityLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EnergyAccumulation = { "EnergyAccumulation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, EnergyAccumulation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyAccumulation_MetaData), NewProp_EnergyAccumulation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_LastEvolutionTime = { "LastEvolutionTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, LastEvolutionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastEvolutionTime_MetaData), NewProp_LastEvolutionTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionRate = { "EvolutionRate", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronRealmLayerEvolutionData, EvolutionRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionRate_MetaData), NewProp_EvolutionRate_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionStage_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionStage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_StabilityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EnergyAccumulation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_LastEvolutionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewProp_EvolutionRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronRealmLayerEvolutionData",
	Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::PropPointers),
	sizeof(FAuracronRealmLayerEvolutionData),
	alignof(FAuracronRealmLayerEvolutionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronRealmLayerEvolutionData ************************************

// ********** Begin ScriptStruct FAuracronAdvancedRealmTransition **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition;
class UScriptStruct* FAuracronAdvancedRealmTransition::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition, (UObject*)Z_Construct_UPackage__Script_AuracronDynamicRealmBridge(), TEXT("AuracronAdvancedRealmTransition"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para transi\xc3\xa7\xc3\xa3o avan\xc3\xa7""ada entre camadas\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para transi\xc3\xa7\xc3\xa3o avan\xc3\xa7""ada entre camadas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actor_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceLayer_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLayer_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomEffect_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartTime_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalLocation_MetaData[] = {
		{ "Category", "Transition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition properties\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalRotation_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OriginalScale_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLocation_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRotation_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetScale_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLightIntensity_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetGravityScale_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetTimeScale_MetaData[] = {
		{ "Category", "Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SourceLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SourceLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static void NewProp_bUseCustomEffect_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OriginalScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetLightIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetGravityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TargetTimeScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronAdvancedRealmTransition>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0014000000000014, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actor_MetaData), NewProp_Actor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_SourceLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_SourceLayer = { "SourceLayer", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, SourceLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceLayer_MetaData), NewProp_SourceLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLayer_MetaData), NewProp_TargetLayer_MetaData) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, Progress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) };
void Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_bUseCustomEffect_SetBit(void* Obj)
{
	((FAuracronAdvancedRealmTransition*)Obj)->bUseCustomEffect = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_bUseCustomEffect = { "bUseCustomEffect", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronAdvancedRealmTransition), &Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_bUseCustomEffect_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomEffect_MetaData), NewProp_bUseCustomEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_StartTime = { "StartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, StartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartTime_MetaData), NewProp_StartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TransitionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalLocation = { "OriginalLocation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, OriginalLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalLocation_MetaData), NewProp_OriginalLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalRotation = { "OriginalRotation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, OriginalRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalRotation_MetaData), NewProp_OriginalRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalScale = { "OriginalScale", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, OriginalScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OriginalScale_MetaData), NewProp_OriginalScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLocation = { "TargetLocation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLocation_MetaData), NewProp_TargetLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetRotation = { "TargetRotation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRotation_MetaData), NewProp_TargetRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetScale = { "TargetScale", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetScale_MetaData), NewProp_TargetScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLightIntensity = { "TargetLightIntensity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLightIntensity_MetaData), NewProp_TargetLightIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetGravityScale = { "TargetGravityScale", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetGravityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetGravityScale_MetaData), NewProp_TargetGravityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetTimeScale = { "TargetTimeScale", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronAdvancedRealmTransition, TargetTimeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetTimeScale_MetaData), NewProp_TargetTimeScale_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_SourceLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_SourceLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_bUseCustomEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_StartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TransitionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_OriginalScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetGravityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewProp_TargetTimeScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
	nullptr,
	&NewStructOps,
	"AuracronAdvancedRealmTransition",
	Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::PropPointers),
	sizeof(FAuracronAdvancedRealmTransition),
	alignof(FAuracronAdvancedRealmTransition),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.InnerSingleton, Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition.InnerSingleton;
}
// ********** End ScriptStruct FAuracronAdvancedRealmTransition ************************************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ActivateAllRailsInLayer **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventActivateAllRailsInLayer_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Activate all rails in layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Activate all rails in layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivateAllRailsInLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ActivateAllRailsInLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventActivateAllRailsInLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventActivateAllRailsInLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execActivateAllRailsInLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateAllRailsInLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ActivateAllRailsInLayer ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ActivateLayer ********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventActivateLayer_Parms
	{
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Layer management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivateLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventActivateLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventActivateLayer_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ActivateLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::AuracronDynamicRealmSubsystem_eventActivateLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::AuracronDynamicRealmSubsystem_eventActivateLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execActivateLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ActivateLayer **********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ActivatePrismalIsland ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics
{
	struct AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms
	{
		EPrismalIslandType IslandType;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ActivatePrismalIsland", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::AuracronDynamicRealmSubsystem_eventActivatePrismalIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execActivatePrismalIsland)
{
	P_GET_ENUM(EPrismalIslandType,Z_Param_IslandType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivatePrismalIsland(EPrismalIslandType(Z_Param_IslandType),Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ActivatePrismalIsland **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ActivateRail *********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics
{
	struct AuracronDynamicRealmSubsystem_eventActivateRail_Parms
	{
		EAuracronRailType RailType;
		FVector StartLocation;
		FVector EndLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_RailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RailType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_RailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_RailType = { "RailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivateRail_Parms, RailType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType, METADATA_PARAMS(0, nullptr) }; // 913509891
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivateRail_Parms, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventActivateRail_Parms, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventActivateRail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventActivateRail_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_RailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_RailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ActivateRail", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::AuracronDynamicRealmSubsystem_eventActivateRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::AuracronDynamicRealmSubsystem_eventActivateRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execActivateRail)
{
	P_GET_ENUM(EAuracronRailType,Z_Param_RailType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateRail(EAuracronRailType(Z_Param_RailType),Z_Param_Out_StartLocation,Z_Param_Out_EndLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ActivateRail ***********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function CancelAdvancedTransition *********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics
{
	struct AuracronDynamicRealmSubsystem_eventCancelAdvancedTransition_Parms
	{
		FGuid TransitionID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::NewProp_TransitionID = { "TransitionID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventCancelAdvancedTransition_Parms, TransitionID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionID_MetaData), NewProp_TransitionID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::NewProp_TransitionID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "CancelAdvancedTransition", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::AuracronDynamicRealmSubsystem_eventCancelAdvancedTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::AuracronDynamicRealmSubsystem_eventCancelAdvancedTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execCancelAdvancedTransition)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_TransitionID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelAdvancedTransition(Z_Param_Out_TransitionID);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function CancelAdvancedTransition ***********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function DeactivateAllRailsInLayer ********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventDeactivateAllRailsInLayer_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Deactivate all rails in layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Deactivate all rails in layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventDeactivateAllRailsInLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "DeactivateAllRailsInLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventDeactivateAllRailsInLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventDeactivateAllRailsInLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execDeactivateAllRailsInLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateAllRailsInLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function DeactivateAllRailsInLayer **********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function DeactivateLayer ******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms
	{
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "DeactivateLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::AuracronDynamicRealmSubsystem_eventDeactivateLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execDeactivateLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DeactivateLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function DeactivateLayer ********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function DebugGenerateLayerContent ********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics
{
	struct AuracronDynamicRealmSubsystem_eventDebugGenerateLayerContent_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventDebugGenerateLayerContent_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "DebugGenerateLayerContent", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::AuracronDynamicRealmSubsystem_eventDebugGenerateLayerContent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::AuracronDynamicRealmSubsystem_eventDebugGenerateLayerContent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execDebugGenerateLayerContent)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugGenerateLayerContent(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function DebugGenerateLayerContent **********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function DebugShowLayerInfo ***************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and development\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and development" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "DebugShowLayerInfo", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execDebugShowLayerInfo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugShowLayerInfo();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function DebugShowLayerInfo *****************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function DebugToggleLayerVisibility *******
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics
{
	struct AuracronDynamicRealmSubsystem_eventDebugToggleLayerVisibility_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "CallInEditor", "true" },
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventDebugToggleLayerVisibility_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "DebugToggleLayerVisibility", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::AuracronDynamicRealmSubsystem_eventDebugToggleLayerVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::AuracronDynamicRealmSubsystem_eventDebugToggleLayerVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execDebugToggleLayerVisibility)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugToggleLayerVisibility(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function DebugToggleLayerVisibility *********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function EnableLayerAutoEvolution *********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics
{
	struct AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms
	{
		EAuracronRealmLayer Layer;
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "EnableLayerAutoEvolution", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::AuracronDynamicRealmSubsystem_eventEnableLayerAutoEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execEnableLayerAutoEvolution)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLayerAutoEvolution(EAuracronRealmLayer(Z_Param_Layer),Z_Param_bEnable);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function EnableLayerAutoEvolution ***********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function FinalizeSystemInitialization *****
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Finalize system initialization */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Finalize system initialization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "FinalizeSystemInitialization", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execFinalizeSystemInitialization)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FinalizeSystemInitialization();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function FinalizeSystemInitialization *******

// ********** Begin Class UAuracronDynamicRealmSubsystem Function FindNearestRail ******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics
{
	struct AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms
	{
		FVector Location;
		EAuracronRailType RailType;
		AAuracronDynamicRail* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Find nearest rail of specific type */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Find nearest rail of specific type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RailType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_RailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_RailType = { "RailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms, RailType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType, METADATA_PARAMS(0, nullptr) }; // 913509891
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms, ReturnValue), Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_RailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_RailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "FindNearestRail", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::AuracronDynamicRealmSubsystem_eventFindNearestRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execFindNearestRail)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_ENUM(EAuracronRailType,Z_Param_RailType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AAuracronDynamicRail**)Z_Param__Result=P_THIS->FindNearestRail(Z_Param_Out_Location,EAuracronRailType(Z_Param_RailType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function FindNearestRail ********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ForceLayerEvolution **************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics
{
	struct AuracronDynamicRealmSubsystem_eventForceLayerEvolution_Parms
	{
		EAuracronRealmLayer Layer;
		EAuracronLayerEvolutionStage TargetStage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetStage_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetStage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventForceLayerEvolution_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_TargetStage_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_TargetStage = { "TargetStage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventForceLayerEvolution_Parms, TargetStage), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronLayerEvolutionStage, METADATA_PARAMS(0, nullptr) }; // 3335440642
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_TargetStage_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::NewProp_TargetStage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ForceLayerEvolution", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::AuracronDynamicRealmSubsystem_eventForceLayerEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::AuracronDynamicRealmSubsystem_eventForceLayerEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execForceLayerEvolution)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_ENUM(EAuracronLayerEvolutionStage,Z_Param_TargetStage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceLayerEvolution(EAuracronRealmLayer(Z_Param_Layer),EAuracronLayerEvolutionStage(Z_Param_TargetStage));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ForceLayerEvolution ****************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetActiveAdvancedTransitions *****
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetActiveAdvancedTransitions_Parms
	{
		TArray<FAuracronAdvancedRealmTransition> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Transition" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition, METADATA_PARAMS(0, nullptr) }; // 2082472291
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActiveAdvancedTransitions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2082472291
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetActiveAdvancedTransitions", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::AuracronDynamicRealmSubsystem_eventGetActiveAdvancedTransitions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::AuracronDynamicRealmSubsystem_eventGetActiveAdvancedTransitions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetActiveAdvancedTransitions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronAdvancedRealmTransition>*)Z_Param__Result=P_THIS->GetActiveAdvancedTransitions();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetActiveAdvancedTransitions *******

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetActivePrismalIslands **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetActivePrismalIslands_Parms
	{
		TArray<FPrismalIslandData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrismalIslandData, METADATA_PARAMS(0, nullptr) }; // 81172973
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActivePrismalIslands_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 81172973
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetActivePrismalIslands", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::AuracronDynamicRealmSubsystem_eventGetActivePrismalIslands_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::AuracronDynamicRealmSubsystem_eventGetActivePrismalIslands_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetActivePrismalIslands)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPrismalIslandData>*)Z_Param__Result=P_THIS->GetActivePrismalIslands();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetActivePrismalIslands ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetActiveRails *******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetActiveRails_Parms
	{
		TArray<AAuracronDynamicRail*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActiveRails_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetActiveRails", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::AuracronDynamicRealmSubsystem_eventGetActiveRails_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::AuracronDynamicRealmSubsystem_eventGetActiveRails_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetActiveRails)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAuracronDynamicRail*>*)Z_Param__Result=P_THIS->GetActiveRails();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetActiveRails *********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetActorLayer ********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetActorLayer_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActorLayer_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActorLayer_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetActorLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::AuracronDynamicRealmSubsystem_eventGetActorLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::AuracronDynamicRealmSubsystem_eventGetActorLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetActorLayer)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmLayer*)Z_Param__Result=P_THIS->GetActorLayer(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetActorLayer **********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetActorsInLayer *****************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetActorsInLayer_Parms
	{
		EAuracronRealmLayer Layer;
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActorsInLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetActorsInLayer_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetActorsInLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::AuracronDynamicRealmSubsystem_eventGetActorsInLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::AuracronDynamicRealmSubsystem_eventGetActorsInLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetActorsInLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetActorsInLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetActorsInLayer *******************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetGlobalEvolutionData ***********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetGlobalEvolutionData_Parms
	{
		FAuracronGlobalEvolutionData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetGlobalEvolutionData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData, METADATA_PARAMS(0, nullptr) }; // 2455143879
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetGlobalEvolutionData", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::AuracronDynamicRealmSubsystem_eventGetGlobalEvolutionData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::AuracronDynamicRealmSubsystem_eventGetGlobalEvolutionData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetGlobalEvolutionData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronGlobalEvolutionData*)Z_Param__Result=P_THIS->GetGlobalEvolutionData();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetGlobalEvolutionData *************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLayerCenter *******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLayerCenter_Parms
	{
		EAuracronRealmLayer Layer;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerCenter_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerCenter_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLayerCenter", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::AuracronDynamicRealmSubsystem_eventGetLayerCenter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::AuracronDynamicRealmSubsystem_eventGetLayerCenter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLayerCenter)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetLayerCenter(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLayerCenter *********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLayerData *********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLayerData_Parms
	{
		EAuracronRealmLayer Layer;
		FAuracronRealmLayerData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerData_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010008000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronRealmLayerData, METADATA_PARAMS(0, nullptr) }; // 1866550139
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLayerData", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::AuracronDynamicRealmSubsystem_eventGetLayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::AuracronDynamicRealmSubsystem_eventGetLayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLayerData)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronRealmLayerData*)Z_Param__Result=P_THIS->GetLayerData(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLayerData ***********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLayerEvolutionData ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLayerEvolutionData_Parms
	{
		EAuracronRealmLayer Layer;
		FAuracronLayerEvolutionData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerEvolutionData_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerEvolutionData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLayerEvolutionData, METADATA_PARAMS(0, nullptr) }; // 2638580268
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLayerEvolutionData", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::AuracronDynamicRealmSubsystem_eventGetLayerEvolutionData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::AuracronDynamicRealmSubsystem_eventGetLayerEvolutionData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLayerEvolutionData)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLayerEvolutionData*)Z_Param__Result=P_THIS->GetLayerEvolutionData(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLayerEvolutionData **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLayerPerformanceMetric ********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLayerPerformanceMetric_Parms
	{
		EAuracronRealmLayer Layer;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerPerformanceMetric_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerPerformanceMetric_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLayerPerformanceMetric", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::AuracronDynamicRealmSubsystem_eventGetLayerPerformanceMetric_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::AuracronDynamicRealmSubsystem_eventGetLayerPerformanceMetric_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLayerPerformanceMetric)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetLayerPerformanceMetric(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLayerPerformanceMetric **********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLayerResonanceValue ***********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLayerResonanceValue_Parms
	{
		EAuracronRealmLayer Layer;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Integration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerResonanceValue_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLayerResonanceValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLayerResonanceValue", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::AuracronDynamicRealmSubsystem_eventGetLayerResonanceValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::AuracronDynamicRealmSubsystem_eventGetLayerResonanceValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLayerResonanceValue)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetLayerResonanceValue(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLayerResonanceValue *************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetLocationLayer *****************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetLocationLayer_Parms
	{
		FVector Location;
		EAuracronRealmLayer ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLocationLayer_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetLocationLayer_Parms, ReturnValue), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetLocationLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::AuracronDynamicRealmSubsystem_eventGetLocationLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::AuracronDynamicRealmSubsystem_eventGetLocationLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetLocationLayer)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronRealmLayer*)Z_Param__Result=P_THIS->GetLocationLayer(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetLocationLayer *******************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetNearestPrismalIsland **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms
	{
		FVector Location;
		EPrismalIslandType IslandType;
		FPrismalIslandData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "CPP_Default_IslandType", "Nexus" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms, IslandType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EPrismalIslandType, METADATA_PARAMS(0, nullptr) }; // 1145382866
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms, ReturnValue), Z_Construct_UScriptStruct_FPrismalIslandData, METADATA_PARAMS(0, nullptr) }; // 81172973
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetNearestPrismalIsland", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::AuracronDynamicRealmSubsystem_eventGetNearestPrismalIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetNearestPrismalIsland)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_ENUM(EPrismalIslandType,Z_Param_IslandType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPrismalIslandData*)Z_Param__Result=P_THIS->GetNearestPrismalIsland(Z_Param_Out_Location,EPrismalIslandType(Z_Param_IslandType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetNearestPrismalIsland ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetPlayerRailExperience **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetPlayerRailExperience_Parms
	{
		APawn* Player;
		FPlayerRailExperience ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get player rail experience */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get player rail experience" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetPlayerRailExperience_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetPlayerRailExperience_Parms, ReturnValue), Z_Construct_UScriptStruct_FPlayerRailExperience, METADATA_PARAMS(0, nullptr) }; // 2334213312
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetPlayerRailExperience", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::AuracronDynamicRealmSubsystem_eventGetPlayerRailExperience_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::AuracronDynamicRealmSubsystem_eventGetPlayerRailExperience_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetPlayerRailExperience)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPlayerRailExperience*)Z_Param__Result=P_THIS->GetPlayerRailExperience(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetPlayerRailExperience ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetRailGenerationConfig **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetRailGenerationConfig_Parms
	{
		FRailGenerationConfig ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get rail generation configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get rail generation configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetRailGenerationConfig_Parms, ReturnValue), Z_Construct_UScriptStruct_FRailGenerationConfig, METADATA_PARAMS(0, nullptr) }; // 3271473709
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetRailGenerationConfig", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::AuracronDynamicRealmSubsystem_eventGetRailGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::AuracronDynamicRealmSubsystem_eventGetRailGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetRailGenerationConfig)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRailGenerationConfig*)Z_Param__Result=P_THIS->GetRailGenerationConfig();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetRailGenerationConfig ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetRailsInLayer ******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetRailsInLayer_Parms
	{
		EAuracronRealmLayer Layer;
		TArray<AAuracronDynamicRail*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get rails in specific layer */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get rails in specific layer" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetRailsInLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetRailsInLayer_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetRailsInLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventGetRailsInLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::AuracronDynamicRealmSubsystem_eventGetRailsInLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetRailsInLayer)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AAuracronDynamicRail*>*)Z_Param__Result=P_THIS->GetRailsInLayer(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetRailsInLayer ********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function GetTransitionProgress ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics
{
	struct AuracronDynamicRealmSubsystem_eventGetTransitionProgress_Parms
	{
		AActor* Actor;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetTransitionProgress_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventGetTransitionProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "GetTransitionProgress", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::AuracronDynamicRealmSubsystem_eventGetTransitionProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::AuracronDynamicRealmSubsystem_eventGetTransitionProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execGetTransitionProgress)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTransitionProgress(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function GetTransitionProgress **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializeAdvancedRailSystem *****
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize advanced rail management system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize advanced rail management system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializeAdvancedRailSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializeAdvancedRailSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAdvancedRailSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializeAdvancedRailSystem *******

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializeDynamicRails ***********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dynamic rail system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic rail system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializeDynamicRails", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializeDynamicRails)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeDynamicRails();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializeDynamicRails *************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializeEvolutionSystem ********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializeEvolutionSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializeEvolutionSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeEvolutionSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializeEvolutionSystem **********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializeLayerEvolutionSystem ***
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced layer evolution system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced layer evolution system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializeLayerEvolutionSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializeLayerEvolutionSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeLayerEvolutionSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializeLayerEvolutionSystem *****

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializePrismalFlow ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prismal Flow system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prismal Flow system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializePrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializePrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePrismalFlow();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializePrismalFlow **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitializeRealmLayers ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core realm management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core realm management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitializeRealmLayers", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitializeRealmLayers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeRealmLayers();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitializeRealmLayers **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function InitiateAdvancedLayerTransition **
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics
{
	struct AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer TargetLayer;
		float TransitionDuration;
		bool bUseCustomEffect;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Transition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced transition system\n" },
#endif
		{ "CPP_Default_bUseCustomEffect", "false" },
		{ "CPP_Default_TransitionDuration", "2.000000" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced transition system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static void NewProp_bUseCustomEffect_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomEffect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms, TransitionDuration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_bUseCustomEffect_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms*)Obj)->bUseCustomEffect = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_bUseCustomEffect = { "bUseCustomEffect", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_bUseCustomEffect_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::NewProp_bUseCustomEffect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "InitiateAdvancedLayerTransition", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::AuracronDynamicRealmSubsystem_eventInitiateAdvancedLayerTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execInitiateAdvancedLayerTransition)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TransitionDuration);
	P_GET_UBOOL(Z_Param_bUseCustomEffect);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitiateAdvancedLayerTransition(Z_Param_Actor,EAuracronRealmLayer(Z_Param_TargetLayer),Z_Param_TransitionDuration,Z_Param_bUseCustomEffect);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function InitiateAdvancedLayerTransition ****

// ********** Begin Class UAuracronDynamicRealmSubsystem Function IntegrateWithSigilSystem *********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Integration with other systems\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with other systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "IntegrateWithSigilSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execIntegrateWithSigilSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IntegrateWithSigilSystem();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function IntegrateWithSigilSystem ***********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function IsActorInTransition **************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics
{
	struct AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "IsActorInTransition", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::AuracronDynamicRealmSubsystem_eventIsActorInTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execIsActorInTransition)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActorInTransition(Z_Param_Actor);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function IsActorInTransition ****************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function IsLayerActive ********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics
{
	struct AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms
	{
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "IsLayerActive", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::AuracronDynamicRealmSubsystem_eventIsLayerActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execIsLayerActive)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLayerActive(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function IsLayerActive **********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OnFusion20ActivatedInRealm *******
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms
	{
		AActor* User;
		TArray<ESigilType> FusedSigils;
		float TotalPowerLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FusedSigils_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_User;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FusedSigils_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FusedSigils_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FusedSigils;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalPowerLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_User = { "User", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms, User), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils_Inner = { "FusedSigils", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType, METADATA_PARAMS(0, nullptr) }; // 959477739
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils = { "FusedSigils", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms, FusedSigils), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FusedSigils_MetaData), NewProp_FusedSigils_MetaData) }; // 959477739
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_TotalPowerLevel = { "TotalPowerLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms, TotalPowerLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_User,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_FusedSigils,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::NewProp_TotalPowerLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OnFusion20ActivatedInRealm", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::AuracronDynamicRealmSubsystem_eventOnFusion20ActivatedInRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOnFusion20ActivatedInRealm)
{
	P_GET_OBJECT(AActor,Z_Param_User);
	P_GET_TARRAY_REF(ESigilType,Z_Param_Out_FusedSigils);
	P_GET_PROPERTY(FFloatProperty,Z_Param_TotalPowerLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnFusion20ActivatedInRealm(Z_Param_User,Z_Param_Out_FusedSigils,Z_Param_TotalPowerLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OnFusion20ActivatedInRealm *********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OnSigilActivated *****************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOnSigilActivated_Parms
	{
		EAuracronSigilType SigilType;
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle sigil activation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle sigil activation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilActivated_Parms, SigilType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType, METADATA_PARAMS(0, nullptr) }; // 3902901247
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilActivated_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OnSigilActivated", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::AuracronDynamicRealmSubsystem_eventOnSigilActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::AuracronDynamicRealmSubsystem_eventOnSigilActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOnSigilActivated)
{
	P_GET_ENUM(EAuracronSigilType,Z_Param_SigilType);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilActivated(EAuracronSigilType(Z_Param_SigilType),Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OnSigilActivated *******************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OnSigilActivatedInRealm **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms
	{
		ESigilType SigilType;
		AActor* User;
		float PowerLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sigil integration callbacks\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigil integration callbacks" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_User;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PowerLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms, SigilType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ESigilType, METADATA_PARAMS(0, nullptr) }; // 959477739
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_User = { "User", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms, User), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_PowerLevel = { "PowerLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms, PowerLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_User,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::NewProp_PowerLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OnSigilActivatedInRealm", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::AuracronDynamicRealmSubsystem_eventOnSigilActivatedInRealm_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOnSigilActivatedInRealm)
{
	P_GET_ENUM(ESigilType,Z_Param_SigilType);
	P_GET_OBJECT(AActor,Z_Param_User);
	P_GET_PROPERTY(FFloatProperty,Z_Param_PowerLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilActivatedInRealm(ESigilType(Z_Param_SigilType),Z_Param_User,Z_Param_PowerLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OnSigilActivatedInRealm ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OnSigilDeactivated ***************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOnSigilDeactivated_Parms
	{
		EAuracronSigilType SigilType;
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle sigil deactivation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle sigil deactivation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilDeactivated_Parms, SigilType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType, METADATA_PARAMS(0, nullptr) }; // 3902901247
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilDeactivated_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OnSigilDeactivated", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::AuracronDynamicRealmSubsystem_eventOnSigilDeactivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::AuracronDynamicRealmSubsystem_eventOnSigilDeactivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOnSigilDeactivated)
{
	P_GET_ENUM(EAuracronSigilType,Z_Param_SigilType);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilDeactivated(EAuracronSigilType(Z_Param_SigilType),Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OnSigilDeactivated *****************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OnSigilEvolved *******************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms
	{
		EAuracronSigilType SigilType;
		int32 NewLevel;
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Handle sigil evolution */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Handle sigil evolution" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SigilType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SigilType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_SigilType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_SigilType = { "SigilType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms, SigilType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronSigilType, METADATA_PARAMS(0, nullptr) }; // 3902901247
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_SigilType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_SigilType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_NewLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OnSigilEvolved", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::AuracronDynamicRealmSubsystem_eventOnSigilEvolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOnSigilEvolved)
{
	P_GET_ENUM(EAuracronSigilType,Z_Param_SigilType);
	P_GET_PROPERTY(FIntProperty,Z_Param_NewLevel);
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSigilEvolved(EAuracronSigilType(Z_Param_SigilType),Z_Param_NewLevel,Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OnSigilEvolved *********************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function OptimizeLayerPerformance *********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics
{
	struct AuracronDynamicRealmSubsystem_eventOptimizeLayerPerformance_Parms
	{
		EAuracronRealmLayer Layer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance and optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance and optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventOptimizeLayerPerformance_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::NewProp_Layer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "OptimizeLayerPerformance", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::AuracronDynamicRealmSubsystem_eventOptimizeLayerPerformance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::AuracronDynamicRealmSubsystem_eventOptimizeLayerPerformance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execOptimizeLayerPerformance)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeLayerPerformance(EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function OptimizeLayerPerformance ***********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function RegisterActorToLayer *************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor layer management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor layer management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "RegisterActorToLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::AuracronDynamicRealmSubsystem_eventRegisterActorToLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execRegisterActorToLayer)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterActorToLayer(Z_Param_Actor,EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function RegisterActorToLayer ***************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function RequestLayerTransition ***********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics
{
	struct AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer TargetLayer;
		ERealmTransitionType TransitionType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition system\n" },
#endif
		{ "CPP_Default_TransitionType", "Gradual" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetLayer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetLayer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TargetLayer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TargetLayer = { "TargetLayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms, TargetLayer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms, TransitionType), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmTransitionType, METADATA_PARAMS(0, nullptr) }; // 2122775647
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TargetLayer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TargetLayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "RequestLayerTransition", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::AuracronDynamicRealmSubsystem_eventRequestLayerTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execRequestLayerTransition)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_TargetLayer);
	P_GET_ENUM(ERealmTransitionType,Z_Param_TransitionType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RequestLayerTransition(Z_Param_Actor,EAuracronRealmLayer(Z_Param_TargetLayer),ERealmTransitionType(Z_Param_TransitionType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function RequestLayerTransition *************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function SetLayerEvolutionRate ************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics
{
	struct AuracronDynamicRealmSubsystem_eventSetLayerEvolutionRate_Parms
	{
		EAuracronRealmLayer Layer;
		float EvolutionRate;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerEvolutionRate_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_EvolutionRate = { "EvolutionRate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerEvolutionRate_Parms, EvolutionRate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::NewProp_EvolutionRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "SetLayerEvolutionRate", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::AuracronDynamicRealmSubsystem_eventSetLayerEvolutionRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::AuracronDynamicRealmSubsystem_eventSetLayerEvolutionRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execSetLayerEvolutionRate)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EvolutionRate);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerEvolutionRate(EAuracronRealmLayer(Z_Param_Layer),Z_Param_EvolutionRate);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function SetLayerEvolutionRate **************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function SetLayerEvolutionSpeed ***********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics
{
	struct AuracronDynamicRealmSubsystem_eventSetLayerEvolutionSpeed_Parms
	{
		EAuracronRealmLayer Layer;
		float Speed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm|Evolution" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Speed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerEvolutionSpeed_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Speed = { "Speed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerEvolutionSpeed_Parms, Speed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::NewProp_Speed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "SetLayerEvolutionSpeed", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::AuracronDynamicRealmSubsystem_eventSetLayerEvolutionSpeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::AuracronDynamicRealmSubsystem_eventSetLayerEvolutionSpeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execSetLayerEvolutionSpeed)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Speed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerEvolutionSpeed(EAuracronRealmLayer(Z_Param_Layer),Z_Param_Speed);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function SetLayerEvolutionSpeed *************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function SetLayerLOD **********************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics
{
	struct AuracronDynamicRealmSubsystem_eventSetLayerLOD_Parms
	{
		EAuracronRealmLayer Layer;
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerLOD_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetLayerLOD_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "SetLayerLOD", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::AuracronDynamicRealmSubsystem_eventSetLayerLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::AuracronDynamicRealmSubsystem_eventSetLayerLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execSetLayerLOD)
{
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLayerLOD(EAuracronRealmLayer(Z_Param_Layer),Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function SetLayerLOD ************************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function SetRailGenerationConfig **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics
{
	struct AuracronDynamicRealmSubsystem_eventSetRailGenerationConfig_Parms
	{
		FRailGenerationConfig NewConfig;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Advanced Rail System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Set rail generation configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set rail generation configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewConfig;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::NewProp_NewConfig = { "NewConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventSetRailGenerationConfig_Parms, NewConfig), Z_Construct_UScriptStruct_FRailGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewConfig_MetaData), NewProp_NewConfig_MetaData) }; // 3271473709
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::NewProp_NewConfig,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "SetRailGenerationConfig", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::AuracronDynamicRealmSubsystem_eventSetRailGenerationConfig_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::AuracronDynamicRealmSubsystem_eventSetRailGenerationConfig_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execSetRailGenerationConfig)
{
	P_GET_STRUCT_REF(FRailGenerationConfig,Z_Param_Out_NewConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRailGenerationConfig(Z_Param_Out_NewConfig);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function SetRailGenerationConfig ************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function TransitionToPhase ****************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics
{
	struct AuracronDynamicRealmSubsystem_eventTransitionToPhase_Parms
	{
		ERealmEvolutionPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventTransitionToPhase_Parms, NewPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(0, nullptr) }; // 560471848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "TransitionToPhase", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::AuracronDynamicRealmSubsystem_eventTransitionToPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::AuracronDynamicRealmSubsystem_eventTransitionToPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execTransitionToPhase)
{
	P_GET_ENUM(ERealmEvolutionPhase,Z_Param_NewPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TransitionToPhase(ERealmEvolutionPhase(Z_Param_NewPhase));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function TransitionToPhase ******************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function UnregisterActorFromLayer *********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics
{
	struct AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms
	{
		AActor* Actor;
		EAuracronRealmLayer Layer;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Layer_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Layer;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Layer_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Layer = { "Layer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms, Layer), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Layer_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_Layer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "UnregisterActorFromLayer", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::AuracronDynamicRealmSubsystem_eventUnregisterActorFromLayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execUnregisterActorFromLayer)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_ENUM(EAuracronRealmLayer,Z_Param_Layer);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterActorFromLayer(Z_Param_Actor,EAuracronRealmLayer(Z_Param_Layer));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function UnregisterActorFromLayer ***********

// ********** Begin Class UAuracronDynamicRealmSubsystem Function UpdatePrismalFlow ****************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics
{
	struct AuracronDynamicRealmSubsystem_eventUpdatePrismalFlow_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUpdatePrismalFlow_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "UpdatePrismalFlow", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::AuracronDynamicRealmSubsystem_eventUpdatePrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::AuracronDynamicRealmSubsystem_eventUpdatePrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execUpdatePrismalFlow)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePrismalFlow(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function UpdatePrismalFlow ******************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function UpdateRealmEvolution *************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics
{
	struct AuracronDynamicRealmSubsystem_eventUpdateRealmEvolution_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUpdateRealmEvolution_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "UpdateRealmEvolution", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::AuracronDynamicRealmSubsystem_eventUpdateRealmEvolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::AuracronDynamicRealmSubsystem_eventUpdateRealmEvolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execUpdateRealmEvolution)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateRealmEvolution(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function UpdateRealmEvolution ***************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function UseRail **************************
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics
{
	struct AuracronDynamicRealmSubsystem_eventUseRail_Parms
	{
		APawn* Pawn;
		EAuracronRailType RailType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dynamic Realm" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Pawn;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RailType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_Pawn = { "Pawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUseRail_Parms, Pawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_RailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_RailType = { "RailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronDynamicRealmSubsystem_eventUseRail_Parms, RailType), Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRailType, METADATA_PARAMS(0, nullptr) }; // 913509891
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventUseRail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventUseRail_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_Pawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_RailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_RailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "UseRail", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::AuracronDynamicRealmSubsystem_eventUseRail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::AuracronDynamicRealmSubsystem_eventUseRail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execUseRail)
{
	P_GET_OBJECT(APawn,Z_Param_Pawn);
	P_GET_ENUM(EAuracronRailType,Z_Param_RailType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UseRail(Z_Param_Pawn,EAuracronRailType(Z_Param_RailType));
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function UseRail ****************************

// ********** Begin Class UAuracronDynamicRealmSubsystem Function ValidateSystemIntegrity **********
struct Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics
{
	struct AuracronDynamicRealmSubsystem_eventValidateSystemIntegrity_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "System Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate system integrity */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate system integrity" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronDynamicRealmSubsystem_eventValidateSystemIntegrity_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronDynamicRealmSubsystem_eventValidateSystemIntegrity_Parms), &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronDynamicRealmSubsystem, nullptr, "ValidateSystemIntegrity", Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::AuracronDynamicRealmSubsystem_eventValidateSystemIntegrity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::AuracronDynamicRealmSubsystem_eventValidateSystemIntegrity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronDynamicRealmSubsystem::execValidateSystemIntegrity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSystemIntegrity();
	P_NATIVE_END;
}
// ********** End Class UAuracronDynamicRealmSubsystem Function ValidateSystemIntegrity ************

// ********** Begin Class UAuracronDynamicRealmSubsystem *******************************************
void UAuracronDynamicRealmSubsystem::StaticRegisterNativesUAuracronDynamicRealmSubsystem()
{
	UClass* Class = UAuracronDynamicRealmSubsystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateAllRailsInLayer", &UAuracronDynamicRealmSubsystem::execActivateAllRailsInLayer },
		{ "ActivateLayer", &UAuracronDynamicRealmSubsystem::execActivateLayer },
		{ "ActivatePrismalIsland", &UAuracronDynamicRealmSubsystem::execActivatePrismalIsland },
		{ "ActivateRail", &UAuracronDynamicRealmSubsystem::execActivateRail },
		{ "CancelAdvancedTransition", &UAuracronDynamicRealmSubsystem::execCancelAdvancedTransition },
		{ "DeactivateAllRailsInLayer", &UAuracronDynamicRealmSubsystem::execDeactivateAllRailsInLayer },
		{ "DeactivateLayer", &UAuracronDynamicRealmSubsystem::execDeactivateLayer },
		{ "DebugGenerateLayerContent", &UAuracronDynamicRealmSubsystem::execDebugGenerateLayerContent },
		{ "DebugShowLayerInfo", &UAuracronDynamicRealmSubsystem::execDebugShowLayerInfo },
		{ "DebugToggleLayerVisibility", &UAuracronDynamicRealmSubsystem::execDebugToggleLayerVisibility },
		{ "EnableLayerAutoEvolution", &UAuracronDynamicRealmSubsystem::execEnableLayerAutoEvolution },
		{ "FinalizeSystemInitialization", &UAuracronDynamicRealmSubsystem::execFinalizeSystemInitialization },
		{ "FindNearestRail", &UAuracronDynamicRealmSubsystem::execFindNearestRail },
		{ "ForceLayerEvolution", &UAuracronDynamicRealmSubsystem::execForceLayerEvolution },
		{ "GetActiveAdvancedTransitions", &UAuracronDynamicRealmSubsystem::execGetActiveAdvancedTransitions },
		{ "GetActivePrismalIslands", &UAuracronDynamicRealmSubsystem::execGetActivePrismalIslands },
		{ "GetActiveRails", &UAuracronDynamicRealmSubsystem::execGetActiveRails },
		{ "GetActorLayer", &UAuracronDynamicRealmSubsystem::execGetActorLayer },
		{ "GetActorsInLayer", &UAuracronDynamicRealmSubsystem::execGetActorsInLayer },
		{ "GetGlobalEvolutionData", &UAuracronDynamicRealmSubsystem::execGetGlobalEvolutionData },
		{ "GetLayerCenter", &UAuracronDynamicRealmSubsystem::execGetLayerCenter },
		{ "GetLayerData", &UAuracronDynamicRealmSubsystem::execGetLayerData },
		{ "GetLayerEvolutionData", &UAuracronDynamicRealmSubsystem::execGetLayerEvolutionData },
		{ "GetLayerPerformanceMetric", &UAuracronDynamicRealmSubsystem::execGetLayerPerformanceMetric },
		{ "GetLayerResonanceValue", &UAuracronDynamicRealmSubsystem::execGetLayerResonanceValue },
		{ "GetLocationLayer", &UAuracronDynamicRealmSubsystem::execGetLocationLayer },
		{ "GetNearestPrismalIsland", &UAuracronDynamicRealmSubsystem::execGetNearestPrismalIsland },
		{ "GetPlayerRailExperience", &UAuracronDynamicRealmSubsystem::execGetPlayerRailExperience },
		{ "GetRailGenerationConfig", &UAuracronDynamicRealmSubsystem::execGetRailGenerationConfig },
		{ "GetRailsInLayer", &UAuracronDynamicRealmSubsystem::execGetRailsInLayer },
		{ "GetTransitionProgress", &UAuracronDynamicRealmSubsystem::execGetTransitionProgress },
		{ "InitializeAdvancedRailSystem", &UAuracronDynamicRealmSubsystem::execInitializeAdvancedRailSystem },
		{ "InitializeDynamicRails", &UAuracronDynamicRealmSubsystem::execInitializeDynamicRails },
		{ "InitializeEvolutionSystem", &UAuracronDynamicRealmSubsystem::execInitializeEvolutionSystem },
		{ "InitializeLayerEvolutionSystem", &UAuracronDynamicRealmSubsystem::execInitializeLayerEvolutionSystem },
		{ "InitializePrismalFlow", &UAuracronDynamicRealmSubsystem::execInitializePrismalFlow },
		{ "InitializeRealmLayers", &UAuracronDynamicRealmSubsystem::execInitializeRealmLayers },
		{ "InitiateAdvancedLayerTransition", &UAuracronDynamicRealmSubsystem::execInitiateAdvancedLayerTransition },
		{ "IntegrateWithSigilSystem", &UAuracronDynamicRealmSubsystem::execIntegrateWithSigilSystem },
		{ "IsActorInTransition", &UAuracronDynamicRealmSubsystem::execIsActorInTransition },
		{ "IsLayerActive", &UAuracronDynamicRealmSubsystem::execIsLayerActive },
		{ "OnFusion20ActivatedInRealm", &UAuracronDynamicRealmSubsystem::execOnFusion20ActivatedInRealm },
		{ "OnSigilActivated", &UAuracronDynamicRealmSubsystem::execOnSigilActivated },
		{ "OnSigilActivatedInRealm", &UAuracronDynamicRealmSubsystem::execOnSigilActivatedInRealm },
		{ "OnSigilDeactivated", &UAuracronDynamicRealmSubsystem::execOnSigilDeactivated },
		{ "OnSigilEvolved", &UAuracronDynamicRealmSubsystem::execOnSigilEvolved },
		{ "OptimizeLayerPerformance", &UAuracronDynamicRealmSubsystem::execOptimizeLayerPerformance },
		{ "RegisterActorToLayer", &UAuracronDynamicRealmSubsystem::execRegisterActorToLayer },
		{ "RequestLayerTransition", &UAuracronDynamicRealmSubsystem::execRequestLayerTransition },
		{ "SetLayerEvolutionRate", &UAuracronDynamicRealmSubsystem::execSetLayerEvolutionRate },
		{ "SetLayerEvolutionSpeed", &UAuracronDynamicRealmSubsystem::execSetLayerEvolutionSpeed },
		{ "SetLayerLOD", &UAuracronDynamicRealmSubsystem::execSetLayerLOD },
		{ "SetRailGenerationConfig", &UAuracronDynamicRealmSubsystem::execSetRailGenerationConfig },
		{ "TransitionToPhase", &UAuracronDynamicRealmSubsystem::execTransitionToPhase },
		{ "UnregisterActorFromLayer", &UAuracronDynamicRealmSubsystem::execUnregisterActorFromLayer },
		{ "UpdatePrismalFlow", &UAuracronDynamicRealmSubsystem::execUpdatePrismalFlow },
		{ "UpdateRealmEvolution", &UAuracronDynamicRealmSubsystem::execUpdateRealmEvolution },
		{ "UseRail", &UAuracronDynamicRealmSubsystem::execUseRail },
		{ "ValidateSystemIntegrity", &UAuracronDynamicRealmSubsystem::execValidateSystemIntegrity },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem;
UClass* UAuracronDynamicRealmSubsystem::GetPrivateStaticClass()
{
	using TClass = UAuracronDynamicRealmSubsystem;
	if (!Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronDynamicRealmSubsystem"),
			Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.InnerSingleton,
			StaticRegisterNativesUAuracronDynamicRealmSubsystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem_NoRegister()
{
	return UAuracronDynamicRealmSubsystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron Dynamic Realm Subsystem\n * \n * Central management system for the three-layer dynamic realm:\n * - Manages layer transitions and evolution\n * - Controls Prismal Flow and islands\n * - Handles dynamic rail system\n * - Optimizes performance across layers\n * - Integrates with PCG for procedural content\n */" },
#endif
		{ "IncludePath", "AuracronDynamicRealmSubsystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron Dynamic Realm Subsystem\n\nCentral management system for the three-layer dynamic realm:\n- Manages layer transitions and evolution\n- Controls Prismal Flow and islands\n- Handles dynamic rail system\n- Optimizes performance across layers\n- Integrates with PCG for procedural content" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmLayerChanged_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLayerEvolutionStageChanged_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAdvancedRealmTransitionComplete_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGlobalEvolutionEvent_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTranscendentContentUnlocked_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmEvolutionPhaseChanged_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmTransitionStarted_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRealmTransitionCompleted_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPrismalIslandActivated_MetaData[] = {
		{ "Category", "Dynamic Realm Events" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSystemFullyInitialized_MetaData[] = {
		{ "Category", "Sigil Integration Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Sigil Integration Events ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Sigil Integration Events ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RealmLayers_MetaData[] = {
		{ "Category", "Realm Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Core realm data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core realm data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEvolutionPhase_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseStartTime_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalGameTime_MetaData[] = {
		{ "Category", "Realm Configuration" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowActor_MetaData[] = {
		{ "Category", "Prismal Flow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Prismal Flow data\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prismal Flow data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalIslands_MetaData[] = {
		{ "Category", "Prismal Flow" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveRails_MetaData[] = {
		{ "Category", "Dynamic Rails" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dynamic rails\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic rails" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTransitions_MetaData[] = {
		{ "Category", "Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition management\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastPerformanceUpdate_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerPerformanceMetrics_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLayerEvolutionActive_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced evolution system variables\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced evolution system variables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerEvolutionStartTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastEvolutionUpdateTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerEvolutionData_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveAdvancedTransitions_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveTransitionTimers_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionVFXComponents_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionAudioComponents_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionEffectHandles_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorLayerEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorEvolutionEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorLayerEntryTimes_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdvancedRailSystemActive_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Advanced Rail System Variables ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Advanced Rail System Variables ===" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailSystemStartTime_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastRailSystemUpdate_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailGenerationConfig_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerRailMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RailMetricsMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailExperienceMap_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailBonuses_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRailMasteryEffects_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSigilRealmEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sigil integration variables\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sigil integration variables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerMasterRailAbilities_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionVFXComponents_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalEvolutionBonusHandles_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TranscendentAbilityHandles_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGlobalEvolutionEventTriggered_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Global evolution event flags\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global evolution event flags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTranscendentEventTriggered_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bMaxEvolutionEventTriggered_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EvolutionTriggers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Evolution system data - using existing definitions above\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evolution system data - using existing definitions above" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalEvolutionData_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerEvolutionTimer_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronDynamicRealmSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmLayerChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLayerEvolutionStageChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAdvancedRealmTransitionComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGlobalEvolutionEvent;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTranscendentContentUnlocked;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmEvolutionPhaseChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmTransitionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRealmTransitionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPrismalIslandActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSystemFullyInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RealmLayers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RealmLayers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RealmLayers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RealmLayers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEvolutionPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEvolutionPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalGameTime;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalFlowActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrismalIslands_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PrismalIslands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveRails_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveRails;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveTransitions_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveTransitions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveTransitions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastPerformanceUpdate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerPerformanceMetrics_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerPerformanceMetrics_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerPerformanceMetrics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerPerformanceMetrics;
	static void NewProp_bLayerEvolutionActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLayerEvolutionActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LayerEvolutionStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastEvolutionUpdateTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerEvolutionData_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerEvolutionData_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerEvolutionData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerEvolutionData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveAdvancedTransitions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveAdvancedTransitions;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveTransitionTimers_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveTransitionTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveTransitionTimers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionVFXComponents_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionVFXComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TransitionVFXComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionAudioComponents_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionAudioComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TransitionAudioComponents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionEffectHandles_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TransitionEffectHandles_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TransitionEffectHandles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorLayerEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorLayerEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActorLayerEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorEvolutionEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorEvolutionEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActorEvolutionEffects;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActorLayerEntryTimes_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActorLayerEntryTimes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActorLayerEntryTimes;
	static void NewProp_bAdvancedRailSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdvancedRailSystemActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RailSystemStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastRailSystemUpdate;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RailGenerationConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LayerRailMap_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LayerRailMap_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LayerRailMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerRailMap;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RailMetricsMap_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RailMetricsMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RailMetricsMap;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRailExperienceMap_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailExperienceMap_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailExperienceMap;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRailBonuses_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailBonuses_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailBonuses;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRailMasteryEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerRailMasteryEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerRailMasteryEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerSigilRealmEffects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerSigilRealmEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerSigilRealmEffects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerMasterRailAbilities_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerMasterRailAbilities_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerMasterRailAbilities;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EvolutionVFXComponents_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EvolutionVFXComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EvolutionVFXComponents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GlobalEvolutionBonusHandles_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GlobalEvolutionBonusHandles_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GlobalEvolutionBonusHandles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TranscendentAbilityHandles_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TranscendentAbilityHandles_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TranscendentAbilityHandles;
	static void NewProp_bGlobalEvolutionEventTriggered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGlobalEvolutionEventTriggered;
	static void NewProp_bTranscendentEventTriggered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTranscendentEventTriggered;
	static void NewProp_bMaxEvolutionEventTriggered_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMaxEvolutionEventTriggered;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EvolutionTriggers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EvolutionTriggers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EvolutionTriggers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EvolutionTriggers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GlobalEvolutionData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerEvolutionTimer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateAllRailsInLayer, "ActivateAllRailsInLayer" }, // 74239607
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateLayer, "ActivateLayer" }, // 1267584752
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivatePrismalIsland, "ActivatePrismalIsland" }, // 2873956996
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ActivateRail, "ActivateRail" }, // 2816142687
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_CancelAdvancedTransition, "CancelAdvancedTransition" }, // 3696453508
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateAllRailsInLayer, "DeactivateAllRailsInLayer" }, // 868085823
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DeactivateLayer, "DeactivateLayer" }, // 4042068215
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugGenerateLayerContent, "DebugGenerateLayerContent" }, // 906819427
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugShowLayerInfo, "DebugShowLayerInfo" }, // 1404170122
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_DebugToggleLayerVisibility, "DebugToggleLayerVisibility" }, // 3138277155
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_EnableLayerAutoEvolution, "EnableLayerAutoEvolution" }, // 24282443
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FinalizeSystemInitialization, "FinalizeSystemInitialization" }, // 2297116214
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_FindNearestRail, "FindNearestRail" }, // 2385171559
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ForceLayerEvolution, "ForceLayerEvolution" }, // 1631083410
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveAdvancedTransitions, "GetActiveAdvancedTransitions" }, // 3049384932
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActivePrismalIslands, "GetActivePrismalIslands" }, // 2092070190
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActiveRails, "GetActiveRails" }, // 1983463774
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorLayer, "GetActorLayer" }, // 520033698
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetActorsInLayer, "GetActorsInLayer" }, // 2934243419
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetGlobalEvolutionData, "GetGlobalEvolutionData" }, // 3379211541
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerCenter, "GetLayerCenter" }, // 4023670540
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerData, "GetLayerData" }, // 1994640720
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerEvolutionData, "GetLayerEvolutionData" }, // 887141615
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerPerformanceMetric, "GetLayerPerformanceMetric" }, // 78799949
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLayerResonanceValue, "GetLayerResonanceValue" }, // 3114792846
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetLocationLayer, "GetLocationLayer" }, // 3285635519
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetNearestPrismalIsland, "GetNearestPrismalIsland" }, // 1864349362
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetPlayerRailExperience, "GetPlayerRailExperience" }, // 2622899797
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailGenerationConfig, "GetRailGenerationConfig" }, // 1667653570
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetRailsInLayer, "GetRailsInLayer" }, // 3783877716
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_GetTransitionProgress, "GetTransitionProgress" }, // 2513172318
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeAdvancedRailSystem, "InitializeAdvancedRailSystem" }, // 3366004907
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeDynamicRails, "InitializeDynamicRails" }, // 2901696843
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeEvolutionSystem, "InitializeEvolutionSystem" }, // 654381973
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeLayerEvolutionSystem, "InitializeLayerEvolutionSystem" }, // 2164723765
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializePrismalFlow, "InitializePrismalFlow" }, // 1027866788
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitializeRealmLayers, "InitializeRealmLayers" }, // 4148555108
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_InitiateAdvancedLayerTransition, "InitiateAdvancedLayerTransition" }, // 2326958087
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IntegrateWithSigilSystem, "IntegrateWithSigilSystem" }, // 2999132531
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsActorInTransition, "IsActorInTransition" }, // 1049824887
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_IsLayerActive, "IsLayerActive" }, // 2286712391
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnFusion20ActivatedInRealm, "OnFusion20ActivatedInRealm" }, // 981194558
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivated, "OnSigilActivated" }, // 3209452998
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilActivatedInRealm, "OnSigilActivatedInRealm" }, // 3883004593
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilDeactivated, "OnSigilDeactivated" }, // 745787610
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OnSigilEvolved, "OnSigilEvolved" }, // 739276318
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_OptimizeLayerPerformance, "OptimizeLayerPerformance" }, // 1392206751
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RegisterActorToLayer, "RegisterActorToLayer" }, // 2799082743
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_RequestLayerTransition, "RequestLayerTransition" }, // 263876999
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionRate, "SetLayerEvolutionRate" }, // 3167292934
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerEvolutionSpeed, "SetLayerEvolutionSpeed" }, // 3537417081
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetLayerLOD, "SetLayerLOD" }, // 2048261162
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_SetRailGenerationConfig, "SetRailGenerationConfig" }, // 3588564362
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_TransitionToPhase, "TransitionToPhase" }, // 3414608463
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UnregisterActorFromLayer, "UnregisterActorFromLayer" }, // 2678825419
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdatePrismalFlow, "UpdatePrismalFlow" }, // 527432028
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UpdateRealmEvolution, "UpdateRealmEvolution" }, // 306073598
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_UseRail, "UseRail" }, // 1875740855
		{ &Z_Construct_UFunction_UAuracronDynamicRealmSubsystem_ValidateSystemIntegrity, "ValidateSystemIntegrity" }, // 1090297915
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronDynamicRealmSubsystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmLayerChanged = { "OnRealmLayerChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnRealmLayerChanged), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmLayerChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmLayerChanged_MetaData), NewProp_OnRealmLayerChanged_MetaData) }; // 260541131
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnLayerEvolutionStageChanged = { "OnLayerEvolutionStageChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnLayerEvolutionStageChanged), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnLayerEvolutionStageChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLayerEvolutionStageChanged_MetaData), NewProp_OnLayerEvolutionStageChanged_MetaData) }; // 4200742688
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnAdvancedRealmTransitionComplete = { "OnAdvancedRealmTransitionComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnAdvancedRealmTransitionComplete), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnAdvancedRealmTransitionComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAdvancedRealmTransitionComplete_MetaData), NewProp_OnAdvancedRealmTransitionComplete_MetaData) }; // 1071059897
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnGlobalEvolutionEvent = { "OnGlobalEvolutionEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnGlobalEvolutionEvent), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnGlobalEvolutionEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGlobalEvolutionEvent_MetaData), NewProp_OnGlobalEvolutionEvent_MetaData) }; // 371007488
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnTranscendentContentUnlocked = { "OnTranscendentContentUnlocked", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnTranscendentContentUnlocked), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnTranscendentContentUnlocked__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTranscendentContentUnlocked_MetaData), NewProp_OnTranscendentContentUnlocked_MetaData) }; // 1024072315
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmEvolutionPhaseChanged = { "OnRealmEvolutionPhaseChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnRealmEvolutionPhaseChanged), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmEvolutionPhaseChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmEvolutionPhaseChanged_MetaData), NewProp_OnRealmEvolutionPhaseChanged_MetaData) }; // 2663248550
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmTransitionStarted = { "OnRealmTransitionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnRealmTransitionStarted), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmTransitionStarted_MetaData), NewProp_OnRealmTransitionStarted_MetaData) }; // 2023222675
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmTransitionCompleted = { "OnRealmTransitionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnRealmTransitionCompleted), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnRealmTransitionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRealmTransitionCompleted_MetaData), NewProp_OnRealmTransitionCompleted_MetaData) }; // 3206330515
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnPrismalIslandActivated = { "OnPrismalIslandActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnPrismalIslandActivated), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnPrismalIslandActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPrismalIslandActivated_MetaData), NewProp_OnPrismalIslandActivated_MetaData) }; // 1723693839
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnSystemFullyInitialized = { "OnSystemFullyInitialized", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, OnSystemFullyInitialized), Z_Construct_UDelegateFunction_AuracronDynamicRealmBridge_OnSystemFullyInitialized__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSystemFullyInitialized_MetaData), NewProp_OnSystemFullyInitialized_MetaData) }; // 1462266116
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_ValueProp = { "RealmLayers", nullptr, (EPropertyFlags)0x0000008000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronRealmLayerData, METADATA_PARAMS(0, nullptr) }; // 1866550139
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_Key_KeyProp = { "RealmLayers_Key", nullptr, (EPropertyFlags)0x0000008000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers = { "RealmLayers", nullptr, (EPropertyFlags)0x0020088000000015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, RealmLayers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RealmLayers_MetaData), NewProp_RealmLayers_MetaData) }; // 3153537035 1866550139
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_CurrentEvolutionPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_CurrentEvolutionPhase = { "CurrentEvolutionPhase", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, CurrentEvolutionPhase), Z_Construct_UEnum_AuracronDynamicRealmBridge_ERealmEvolutionPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEvolutionPhase_MetaData), NewProp_CurrentEvolutionPhase_MetaData) }; // 560471848
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PhaseStartTime = { "PhaseStartTime", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PhaseStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseStartTime_MetaData), NewProp_PhaseStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TotalGameTime = { "TotalGameTime", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, TotalGameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalGameTime_MetaData), NewProp_TotalGameTime_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalFlowActor = { "PrismalFlowActor", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PrismalFlowActor), Z_Construct_UClass_AAuracronPrismalFlow_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowActor_MetaData), NewProp_PrismalFlowActor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalIslands_Inner = { "PrismalIslands", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrismalIslandData, METADATA_PARAMS(0, nullptr) }; // 81172973
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalIslands = { "PrismalIslands", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PrismalIslands), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalIslands_MetaData), NewProp_PrismalIslands_MetaData) }; // 81172973
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveRails_Inner = { "ActiveRails", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveRails = { "ActiveRails", nullptr, (EPropertyFlags)0x0124080000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActiveRails), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveRails_MetaData), NewProp_ActiveRails_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions_ValueProp = { "ActiveTransitions", nullptr, (EPropertyFlags)0x0104000000080009, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UAuracronRealmTransitionComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions_Key_KeyProp = { "ActiveTransitions_Key", nullptr, (EPropertyFlags)0x0104000000080009, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions = { "ActiveTransitions", nullptr, (EPropertyFlags)0x012408800000001d, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActiveTransitions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTransitions_MetaData), NewProp_ActiveTransitions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastPerformanceUpdate = { "LastPerformanceUpdate", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LastPerformanceUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastPerformanceUpdate_MetaData), NewProp_LastPerformanceUpdate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_ValueProp = { "LayerPerformanceMetrics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_Key_KeyProp = { "LayerPerformanceMetrics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics = { "LayerPerformanceMetrics", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LayerPerformanceMetrics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerPerformanceMetrics_MetaData), NewProp_LayerPerformanceMetrics_MetaData) }; // 3153537035
void Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bLayerEvolutionActive_SetBit(void* Obj)
{
	((UAuracronDynamicRealmSubsystem*)Obj)->bLayerEvolutionActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bLayerEvolutionActive = { "bLayerEvolutionActive", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicRealmSubsystem), &Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bLayerEvolutionActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLayerEvolutionActive_MetaData), NewProp_bLayerEvolutionActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionStartTime = { "LayerEvolutionStartTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LayerEvolutionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerEvolutionStartTime_MetaData), NewProp_LayerEvolutionStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastEvolutionUpdateTime = { "LastEvolutionUpdateTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LastEvolutionUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastEvolutionUpdateTime_MetaData), NewProp_LastEvolutionUpdateTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_ValueProp = { "LayerEvolutionData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAuracronLayerEvolutionData, METADATA_PARAMS(0, nullptr) }; // 2638580268
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_Key_KeyProp = { "LayerEvolutionData_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData = { "LayerEvolutionData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LayerEvolutionData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerEvolutionData_MetaData), NewProp_LayerEvolutionData_MetaData) }; // 3153537035 2638580268
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveAdvancedTransitions_Inner = { "ActiveAdvancedTransitions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition, METADATA_PARAMS(0, nullptr) }; // 2082472291
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveAdvancedTransitions = { "ActiveAdvancedTransitions", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActiveAdvancedTransitions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveAdvancedTransitions_MetaData), NewProp_ActiveAdvancedTransitions_MetaData) }; // 2082472291
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers_ValueProp = { "ActiveTransitionTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers_Key_KeyProp = { "ActiveTransitionTimers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers = { "ActiveTransitionTimers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActiveTransitionTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveTransitionTimers_MetaData), NewProp_ActiveTransitionTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents_ValueProp = { "TransitionVFXComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents_Key_KeyProp = { "TransitionVFXComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents = { "TransitionVFXComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, TransitionVFXComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionVFXComponents_MetaData), NewProp_TransitionVFXComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents_ValueProp = { "TransitionAudioComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents_Key_KeyProp = { "TransitionAudioComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents = { "TransitionAudioComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, TransitionAudioComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionAudioComponents_MetaData), NewProp_TransitionAudioComponents_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles_ValueProp = { "TransitionEffectHandles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles_Key_KeyProp = { "TransitionEffectHandles_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles = { "TransitionEffectHandles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, TransitionEffectHandles), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionEffectHandles_MetaData), NewProp_TransitionEffectHandles_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects_ValueProp = { "ActorLayerEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects_Key_KeyProp = { "ActorLayerEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects = { "ActorLayerEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActorLayerEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorLayerEffects_MetaData), NewProp_ActorLayerEffects_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects_ValueProp = { "ActorEvolutionEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects_Key_KeyProp = { "ActorEvolutionEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects = { "ActorEvolutionEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActorEvolutionEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorEvolutionEffects_MetaData), NewProp_ActorEvolutionEffects_MetaData) }; // 386907876
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes_ValueProp = { "ActorLayerEntryTimes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes_Key_KeyProp = { "ActorLayerEntryTimes_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes = { "ActorLayerEntryTimes", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, ActorLayerEntryTimes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorLayerEntryTimes_MetaData), NewProp_ActorLayerEntryTimes_MetaData) };
void Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bAdvancedRailSystemActive_SetBit(void* Obj)
{
	((UAuracronDynamicRealmSubsystem*)Obj)->bAdvancedRailSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bAdvancedRailSystemActive = { "bAdvancedRailSystemActive", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicRealmSubsystem), &Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bAdvancedRailSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdvancedRailSystemActive_MetaData), NewProp_bAdvancedRailSystemActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailSystemStartTime = { "RailSystemStartTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, RailSystemStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailSystemStartTime_MetaData), NewProp_RailSystemStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastRailSystemUpdate = { "LastRailSystemUpdate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LastRailSystemUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastRailSystemUpdate_MetaData), NewProp_LastRailSystemUpdate_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailGenerationConfig = { "RailGenerationConfig", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, RailGenerationConfig), Z_Construct_UScriptStruct_FRailGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailGenerationConfig_MetaData), NewProp_RailGenerationConfig_MetaData) }; // 3271473709
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_ValueProp = { "LayerRailMap", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_Key_KeyProp = { "LayerRailMap_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronRealmLayer, METADATA_PARAMS(0, nullptr) }; // 3153537035
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap = { "LayerRailMap", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LayerRailMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerRailMap_MetaData), NewProp_LayerRailMap_MetaData) }; // 3153537035
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap_ValueProp = { "RailMetricsMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FRailSystemMetrics, METADATA_PARAMS(0, nullptr) }; // 1396374881
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap_Key_KeyProp = { "RailMetricsMap_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AAuracronDynamicRail_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap = { "RailMetricsMap", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, RailMetricsMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RailMetricsMap_MetaData), NewProp_RailMetricsMap_MetaData) }; // 1396374881
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap_ValueProp = { "PlayerRailExperienceMap", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPlayerRailExperience, METADATA_PARAMS(0, nullptr) }; // 2334213312
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap_Key_KeyProp = { "PlayerRailExperienceMap_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap = { "PlayerRailExperienceMap", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PlayerRailExperienceMap), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailExperienceMap_MetaData), NewProp_PlayerRailExperienceMap_MetaData) }; // 2334213312
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses_ValueProp = { "PlayerRailBonuses", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses_Key_KeyProp = { "PlayerRailBonuses_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses = { "PlayerRailBonuses", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PlayerRailBonuses), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailBonuses_MetaData), NewProp_PlayerRailBonuses_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects_ValueProp = { "PlayerRailMasteryEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects_Key_KeyProp = { "PlayerRailMasteryEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects = { "PlayerRailMasteryEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PlayerRailMasteryEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRailMasteryEffects_MetaData), NewProp_PlayerRailMasteryEffects_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects_ValueProp = { "PlayerSigilRealmEffects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects_Key_KeyProp = { "PlayerSigilRealmEffects_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects = { "PlayerSigilRealmEffects", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PlayerSigilRealmEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSigilRealmEffects_MetaData), NewProp_PlayerSigilRealmEffects_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities_ValueProp = { "PlayerMasterRailAbilities", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle, METADATA_PARAMS(0, nullptr) }; // 417001783
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities_Key_KeyProp = { "PlayerMasterRailAbilities_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities = { "PlayerMasterRailAbilities", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, PlayerMasterRailAbilities), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerMasterRailAbilities_MetaData), NewProp_PlayerMasterRailAbilities_MetaData) }; // 417001783
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents_ValueProp = { "EvolutionVFXComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents_Key_KeyProp = { "EvolutionVFXComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents = { "EvolutionVFXComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, EvolutionVFXComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionVFXComponents_MetaData), NewProp_EvolutionVFXComponents_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles_ValueProp = { "GlobalEvolutionBonusHandles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActiveGameplayEffectHandle, METADATA_PARAMS(0, nullptr) }; // 386907876
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles_Key_KeyProp = { "GlobalEvolutionBonusHandles_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles = { "GlobalEvolutionBonusHandles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, GlobalEvolutionBonusHandles), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalEvolutionBonusHandles_MetaData), NewProp_GlobalEvolutionBonusHandles_MetaData) }; // 386907876
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles_ValueProp = { "TranscendentAbilityHandles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FGameplayAbilitySpecHandle, METADATA_PARAMS(0, nullptr) }; // 417001783
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles_Key_KeyProp = { "TranscendentAbilityHandles_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles = { "TranscendentAbilityHandles", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, TranscendentAbilityHandles), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TranscendentAbilityHandles_MetaData), NewProp_TranscendentAbilityHandles_MetaData) }; // 417001783
void Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bGlobalEvolutionEventTriggered_SetBit(void* Obj)
{
	((UAuracronDynamicRealmSubsystem*)Obj)->bGlobalEvolutionEventTriggered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bGlobalEvolutionEventTriggered = { "bGlobalEvolutionEventTriggered", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicRealmSubsystem), &Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bGlobalEvolutionEventTriggered_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGlobalEvolutionEventTriggered_MetaData), NewProp_bGlobalEvolutionEventTriggered_MetaData) };
void Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bTranscendentEventTriggered_SetBit(void* Obj)
{
	((UAuracronDynamicRealmSubsystem*)Obj)->bTranscendentEventTriggered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bTranscendentEventTriggered = { "bTranscendentEventTriggered", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicRealmSubsystem), &Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bTranscendentEventTriggered_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTranscendentEventTriggered_MetaData), NewProp_bTranscendentEventTriggered_MetaData) };
void Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bMaxEvolutionEventTriggered_SetBit(void* Obj)
{
	((UAuracronDynamicRealmSubsystem*)Obj)->bMaxEvolutionEventTriggered = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bMaxEvolutionEventTriggered = { "bMaxEvolutionEventTriggered", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronDynamicRealmSubsystem), &Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bMaxEvolutionEventTriggered_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bMaxEvolutionEventTriggered_MetaData), NewProp_bMaxEvolutionEventTriggered_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_ValueProp = { "EvolutionTriggers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_Key_KeyProp = { "EvolutionTriggers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AuracronDynamicRealmBridge_EAuracronEvolutionTrigger, METADATA_PARAMS(0, nullptr) }; // 3530945112
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers = { "EvolutionTriggers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, EvolutionTriggers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EvolutionTriggers_MetaData), NewProp_EvolutionTriggers_MetaData) }; // 3530945112
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionData = { "GlobalEvolutionData", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, GlobalEvolutionData), Z_Construct_UScriptStruct_FAuracronGlobalEvolutionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalEvolutionData_MetaData), NewProp_GlobalEvolutionData_MetaData) }; // 2455143879
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionTimer = { "LayerEvolutionTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronDynamicRealmSubsystem, LayerEvolutionTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerEvolutionTimer_MetaData), NewProp_LayerEvolutionTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmLayerChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnLayerEvolutionStageChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnAdvancedRealmTransitionComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnGlobalEvolutionEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnTranscendentContentUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmEvolutionPhaseChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmTransitionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnRealmTransitionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnPrismalIslandActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_OnSystemFullyInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RealmLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_CurrentEvolutionPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_CurrentEvolutionPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PhaseStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TotalGameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalFlowActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalIslands_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PrismalIslands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveRails_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveRails,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastPerformanceUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerPerformanceMetrics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bLayerEvolutionActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastEvolutionUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveAdvancedTransitions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveAdvancedTransitions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActiveTransitionTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionVFXComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionAudioComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TransitionEffectHandles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorEvolutionEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_ActorLayerEntryTimes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bAdvancedRailSystemActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailSystemStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LastRailSystemUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailGenerationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerRailMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_RailMetricsMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailExperienceMap,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailBonuses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerRailMasteryEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerSigilRealmEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_PlayerMasterRailAbilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionVFXComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionBonusHandles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_TranscendentAbilityHandles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bGlobalEvolutionEventTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bTranscendentEventTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_bMaxEvolutionEventTriggered,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_EvolutionTriggers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_GlobalEvolutionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::NewProp_LayerEvolutionTimer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronDynamicRealmBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::ClassParams = {
	&UAuracronDynamicRealmSubsystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronDynamicRealmSubsystem()
{
	if (!Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.OuterSingleton, Z_Construct_UClass_UAuracronDynamicRealmSubsystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronDynamicRealmSubsystem);
UAuracronDynamicRealmSubsystem::~UAuracronDynamicRealmSubsystem() {}
// ********** End Class UAuracronDynamicRealmSubsystem *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESigilType_StaticEnum, TEXT("ESigilType"), &Z_Registration_Info_UEnum_ESigilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 959477739U) },
		{ EAuracronSigilType_StaticEnum, TEXT("EAuracronSigilType"), &Z_Registration_Info_UEnum_EAuracronSigilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3902901247U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronRealmLayerData::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmLayerData_Statics::NewStructOps, TEXT("AuracronRealmLayerData"), &Z_Registration_Info_UScriptStruct_FAuracronRealmLayerData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmLayerData), 1866550139U) },
		{ FPrismalIslandData::StaticStruct, Z_Construct_UScriptStruct_FPrismalIslandData_Statics::NewStructOps, TEXT("PrismalIslandData"), &Z_Registration_Info_UScriptStruct_FPrismalIslandData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalIslandData), 81172973U) },
		{ FAuracronRealmLayerEvolutionData::StaticStruct, Z_Construct_UScriptStruct_FAuracronRealmLayerEvolutionData_Statics::NewStructOps, TEXT("AuracronRealmLayerEvolutionData"), &Z_Registration_Info_UScriptStruct_FAuracronRealmLayerEvolutionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronRealmLayerEvolutionData), 2207293635U) },
		{ FAuracronAdvancedRealmTransition::StaticStruct, Z_Construct_UScriptStruct_FAuracronAdvancedRealmTransition_Statics::NewStructOps, TEXT("AuracronAdvancedRealmTransition"), &Z_Registration_Info_UScriptStruct_FAuracronAdvancedRealmTransition, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronAdvancedRealmTransition), 2082472291U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronDynamicRealmSubsystem, UAuracronDynamicRealmSubsystem::StaticClass, TEXT("UAuracronDynamicRealmSubsystem"), &Z_Registration_Info_UClass_UAuracronDynamicRealmSubsystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronDynamicRealmSubsystem), 1140753700U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_488383777(TEXT("/Script/AuracronDynamicRealmBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronDynamicRealmBridge_Public_AuracronDynamicRealmSubsystem_h__Script_AuracronDynamicRealmBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
