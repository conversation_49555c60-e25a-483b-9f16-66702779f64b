// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronAdvancedCombatExample.h"
#include "AuracronCombatBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronAdvancedCombatExample() {}

// ********** Begin Cross Module References ********************************************************
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample_NoRegister();
AURACRONCOMBATBRIDGE_API UClass* Z_Construct_UClass_UAuracronCombatBridge_NoRegister();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType();
AURACRONCOMBATBRIDGE_API UEnum* Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronAICombatConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronCombatAnalytics();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronComboConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronElementalDamageConfig();
AURACRONCOMBATBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronCombatBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateAdvancedDestruction ***
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics
{
	struct AuracronAdvancedCombatExample_eventDemonstrateAdvancedDestruction_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate advanced destruction */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate advanced destruction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventDemonstrateAdvancedDestruction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateAdvancedDestruction", Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::AuracronAdvancedCombatExample_eventDemonstrateAdvancedDestruction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::AuracronAdvancedCombatExample_eventDemonstrateAdvancedDestruction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateAdvancedDestruction)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateAdvancedDestruction(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateAdvancedDestruction *****

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateAIAdaptation **********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate AI behavior adaptation */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate AI behavior adaptation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateAIAdaptation", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateAIAdaptation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateAIAdaptation();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateAIAdaptation ************

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateCombatAnalytics *******
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate combat analytics */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate combat analytics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateCombatAnalytics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateCombatAnalytics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateCombatAnalytics();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateCombatAnalytics *********

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateElementalDamage *******
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics
{
	struct AuracronAdvancedCombatExample_eventDemonstrateElementalDamage_Parms
	{
		AActor* TargetActor;
		EAuracronElementalType ElementType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate elemental damage application */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate elemental damage application" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventDemonstrateElementalDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_ElementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_ElementType = { "ElementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventDemonstrateElementalDamage_Parms, ElementType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_ElementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::NewProp_ElementType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateElementalDamage", Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::AuracronAdvancedCombatExample_eventDemonstrateElementalDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::AuracronAdvancedCombatExample_eventDemonstrateElementalDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateElementalDamage)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_ENUM(EAuracronElementalType,Z_Param_ElementType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateElementalDamage(Z_Param_TargetActor,EAuracronElementalType(Z_Param_ElementType));
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateElementalDamage *********

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateElementalInteractions *
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics
{
	struct AuracronAdvancedCombatExample_eventDemonstrateElementalInteractions_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate elemental interactions */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate elemental interactions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventDemonstrateElementalInteractions_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateElementalInteractions", Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::AuracronAdvancedCombatExample_eventDemonstrateElementalInteractions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::AuracronAdvancedCombatExample_eventDemonstrateElementalInteractions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateElementalInteractions)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateElementalInteractions(Z_Param_Out_Location);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateElementalInteractions ***

// ********** Begin Class AAuracronAdvancedCombatExample Function DemonstrateProceduralDamage ******
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics
{
	struct AuracronAdvancedCombatExample_eventDemonstrateProceduralDamage_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Demonstrate procedural damage */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Demonstrate procedural damage" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventDemonstrateProceduralDamage_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "DemonstrateProceduralDamage", Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::AuracronAdvancedCombatExample_eventDemonstrateProceduralDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::AuracronAdvancedCombatExample_eventDemonstrateProceduralDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execDemonstrateProceduralDamage)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DemonstrateProceduralDamage(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function DemonstrateProceduralDamage ********

// ********** Begin Class AAuracronAdvancedCombatExample Function ExecuteExampleCombo **************
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics
{
	struct AuracronAdvancedCombatExample_eventExecuteExampleCombo_Parms
	{
		EAuracronComboType ComboType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Execute example combo */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Execute example combo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ComboType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ComboType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::NewProp_ComboType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::NewProp_ComboType = { "ComboType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventExecuteExampleCombo_Parms, ComboType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType, METADATA_PARAMS(0, nullptr) }; // 916783320
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::NewProp_ComboType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::NewProp_ComboType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "ExecuteExampleCombo", Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::AuracronAdvancedCombatExample_eventExecuteExampleCombo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::AuracronAdvancedCombatExample_eventExecuteExampleCombo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execExecuteExampleCombo)
{
	P_GET_ENUM(EAuracronComboType,Z_Param_ComboType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteExampleCombo(EAuracronComboType(Z_Param_ComboType));
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function ExecuteExampleCombo ****************

// ********** Begin Class AAuracronAdvancedCombatExample Function ExportExampleAnalytics ***********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Export example analytics data */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Export example analytics data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "ExportExampleAnalytics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execExportExampleAnalytics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportExampleAnalytics();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function ExportExampleAnalytics *************

// ********** Begin Class AAuracronAdvancedCombatExample Function InitializeAdvancedDestructionExample 
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize advanced destruction system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize advanced destruction system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "InitializeAdvancedDestructionExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execInitializeAdvancedDestructionExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAdvancedDestructionExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function InitializeAdvancedDestructionExample 

// ********** Begin Class AAuracronAdvancedCombatExample Function InitializeAICombatExample ********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize AI combat with example behavior */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize AI combat with example behavior" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "InitializeAICombatExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execInitializeAICombatExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAICombatExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function InitializeAICombatExample **********

// ********** Begin Class AAuracronAdvancedCombatExample Function InitializeElementalSystemExample *
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Elemental" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize elemental system with example configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize elemental system with example configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "InitializeElementalSystemExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execInitializeElementalSystemExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeElementalSystemExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function InitializeElementalSystemExample ***

// ********** Begin Class AAuracronAdvancedCombatExample Function InitializeEnhancedInputExample ***
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize enhanced input system with example configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize enhanced input system with example configuration" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "InitializeEnhancedInputExample", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execInitializeEnhancedInputExample)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeEnhancedInputExample();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function InitializeEnhancedInputExample *****

// ********** Begin Class AAuracronAdvancedCombatExample Function OnAdvancedDestructionExample *****
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics
{
	struct AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms
	{
		FVector Location;
		float DestructionForce;
		int32 AffectedObjects;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DestructionForce;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AffectedObjects;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_DestructionForce = { "DestructionForce", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms, DestructionForce), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_AffectedObjects = { "AffectedObjects", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms, AffectedObjects), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_DestructionForce,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::NewProp_AffectedObjects,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "OnAdvancedDestructionExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00880401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::AuracronAdvancedCombatExample_eventOnAdvancedDestructionExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execOnAdvancedDestructionExample)
{
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DestructionForce);
	P_GET_PROPERTY(FIntProperty,Z_Param_AffectedObjects);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAdvancedDestructionExample(Z_Param_Location,Z_Param_DestructionForce,Z_Param_AffectedObjects);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function OnAdvancedDestructionExample *******

// ********** Begin Class AAuracronAdvancedCombatExample Function OnAICombatDecisionExample ********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics
{
	struct AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms
	{
		EAuracronAICombatBehavior BehaviorType;
		FString DecisionType;
		float ConfidenceLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DecisionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConfidenceLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms, BehaviorType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronAICombatBehavior, METADATA_PARAMS(0, nullptr) }; // 2928806894
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_DecisionType = { "DecisionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms, DecisionType), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_ConfidenceLevel = { "ConfidenceLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms, ConfidenceLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_DecisionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::NewProp_ConfidenceLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "OnAICombatDecisionExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::AuracronAdvancedCombatExample_eventOnAICombatDecisionExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execOnAICombatDecisionExample)
{
	P_GET_ENUM(EAuracronAICombatBehavior,Z_Param_BehaviorType);
	P_GET_PROPERTY(FStrProperty,Z_Param_DecisionType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_ConfidenceLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAICombatDecisionExample(EAuracronAICombatBehavior(Z_Param_BehaviorType),Z_Param_DecisionType,Z_Param_ConfidenceLevel);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function OnAICombatDecisionExample **********

// ********** Begin Class AAuracronAdvancedCombatExample Function OnCombatAnalyticsUpdatedExample **
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics
{
	struct AuracronAdvancedCombatExample_eventOnCombatAnalyticsUpdatedExample_Parms
	{
		FAuracronCombatAnalytics Analytics;
		float EfficiencyScore;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Analytics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EfficiencyScore;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::NewProp_Analytics = { "Analytics", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnCombatAnalyticsUpdatedExample_Parms, Analytics), Z_Construct_UScriptStruct_FAuracronCombatAnalytics, METADATA_PARAMS(0, nullptr) }; // 2913893938
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::NewProp_EfficiencyScore = { "EfficiencyScore", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnCombatAnalyticsUpdatedExample_Parms, EfficiencyScore), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::NewProp_Analytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::NewProp_EfficiencyScore,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "OnCombatAnalyticsUpdatedExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::AuracronAdvancedCombatExample_eventOnCombatAnalyticsUpdatedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::AuracronAdvancedCombatExample_eventOnCombatAnalyticsUpdatedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execOnCombatAnalyticsUpdatedExample)
{
	P_GET_STRUCT(FAuracronCombatAnalytics,Z_Param_Analytics);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EfficiencyScore);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCombatAnalyticsUpdatedExample(Z_Param_Analytics,Z_Param_EfficiencyScore);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function OnCombatAnalyticsUpdatedExample ****

// ********** Begin Class AAuracronAdvancedCombatExample Function OnComboExecutedExample ***********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics
{
	struct AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms
	{
		EAuracronComboType ComboType;
		int32 ComboStep;
		float DamageMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// === Event Handlers ===\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "=== Event Handlers ===" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ComboType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ComboType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ComboStep;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboType = { "ComboType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms, ComboType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronComboType, METADATA_PARAMS(0, nullptr) }; // 916783320
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboStep = { "ComboStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms, ComboStep), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms, DamageMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_ComboStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::NewProp_DamageMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "OnComboExecutedExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::AuracronAdvancedCombatExample_eventOnComboExecutedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execOnComboExecutedExample)
{
	P_GET_ENUM(EAuracronComboType,Z_Param_ComboType);
	P_GET_PROPERTY(FIntProperty,Z_Param_ComboStep);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageMultiplier);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnComboExecutedExample(EAuracronComboType(Z_Param_ComboType),Z_Param_ComboStep,Z_Param_DamageMultiplier);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function OnComboExecutedExample *************

// ********** Begin Class AAuracronAdvancedCombatExample Function OnElementalDamageAppliedExample **
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics
{
	struct AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms
	{
		AActor* TargetActor;
		EAuracronElementalType ElementType;
		float Damage;
		bool bStatusEffectApplied;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ElementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ElementType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static void NewProp_bStatusEffectApplied_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStatusEffectApplied;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_ElementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_ElementType = { "ElementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms, ElementType), Z_Construct_UEnum_AuracronCombatBridge_EAuracronElementalType, METADATA_PARAMS(0, nullptr) }; // 2822706140
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms, Damage), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_bStatusEffectApplied_SetBit(void* Obj)
{
	((AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms*)Obj)->bStatusEffectApplied = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_bStatusEffectApplied = { "bStatusEffectApplied", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms), &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_bStatusEffectApplied_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_ElementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_ElementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::NewProp_bStatusEffectApplied,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "OnElementalDamageAppliedExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::AuracronAdvancedCombatExample_eventOnElementalDamageAppliedExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execOnElementalDamageAppliedExample)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_ENUM(EAuracronElementalType,Z_Param_ElementType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_GET_UBOOL(Z_Param_bStatusEffectApplied);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnElementalDamageAppliedExample(Z_Param_TargetActor,EAuracronElementalType(Z_Param_ElementType),Z_Param_Damage,Z_Param_bStatusEffectApplied);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function OnElementalDamageAppliedExample ****

// ********** Begin Class AAuracronAdvancedCombatExample Function RunAdvancedCombatScenario ********
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Complete" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Run complete advanced combat scenario */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Run complete advanced combat scenario" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "RunAdvancedCombatScenario", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execRunAdvancedCombatScenario)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunAdvancedCombatScenario();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function RunAdvancedCombatScenario **********

// ********** Begin Class AAuracronAdvancedCombatExample Function SetupExampleCombos ***************
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Setup example combo system */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup example combo system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "SetupExampleCombos", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execSetupExampleCombos)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupExampleCombos();
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function SetupExampleCombos *****************

// ********** Begin Class AAuracronAdvancedCombatExample Function UpdateAICombatExample ************
struct Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics
{
	struct AuracronAdvancedCombatExample_eventUpdateAICombatExample_Parms
	{
		AActor* TargetActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON Example|AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Update AI combat state with example target */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Update AI combat state with example target" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronAdvancedCombatExample_eventUpdateAICombatExample_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::NewProp_TargetActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAuracronAdvancedCombatExample, nullptr, "UpdateAICombatExample", Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::AuracronAdvancedCombatExample_eventUpdateAICombatExample_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::AuracronAdvancedCombatExample_eventUpdateAICombatExample_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAuracronAdvancedCombatExample::execUpdateAICombatExample)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAICombatExample(Z_Param_TargetActor);
	P_NATIVE_END;
}
// ********** End Class AAuracronAdvancedCombatExample Function UpdateAICombatExample **************

// ********** Begin Class AAuracronAdvancedCombatExample *******************************************
void AAuracronAdvancedCombatExample::StaticRegisterNativesAAuracronAdvancedCombatExample()
{
	UClass* Class = AAuracronAdvancedCombatExample::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DemonstrateAdvancedDestruction", &AAuracronAdvancedCombatExample::execDemonstrateAdvancedDestruction },
		{ "DemonstrateAIAdaptation", &AAuracronAdvancedCombatExample::execDemonstrateAIAdaptation },
		{ "DemonstrateCombatAnalytics", &AAuracronAdvancedCombatExample::execDemonstrateCombatAnalytics },
		{ "DemonstrateElementalDamage", &AAuracronAdvancedCombatExample::execDemonstrateElementalDamage },
		{ "DemonstrateElementalInteractions", &AAuracronAdvancedCombatExample::execDemonstrateElementalInteractions },
		{ "DemonstrateProceduralDamage", &AAuracronAdvancedCombatExample::execDemonstrateProceduralDamage },
		{ "ExecuteExampleCombo", &AAuracronAdvancedCombatExample::execExecuteExampleCombo },
		{ "ExportExampleAnalytics", &AAuracronAdvancedCombatExample::execExportExampleAnalytics },
		{ "InitializeAdvancedDestructionExample", &AAuracronAdvancedCombatExample::execInitializeAdvancedDestructionExample },
		{ "InitializeAICombatExample", &AAuracronAdvancedCombatExample::execInitializeAICombatExample },
		{ "InitializeElementalSystemExample", &AAuracronAdvancedCombatExample::execInitializeElementalSystemExample },
		{ "InitializeEnhancedInputExample", &AAuracronAdvancedCombatExample::execInitializeEnhancedInputExample },
		{ "OnAdvancedDestructionExample", &AAuracronAdvancedCombatExample::execOnAdvancedDestructionExample },
		{ "OnAICombatDecisionExample", &AAuracronAdvancedCombatExample::execOnAICombatDecisionExample },
		{ "OnCombatAnalyticsUpdatedExample", &AAuracronAdvancedCombatExample::execOnCombatAnalyticsUpdatedExample },
		{ "OnComboExecutedExample", &AAuracronAdvancedCombatExample::execOnComboExecutedExample },
		{ "OnElementalDamageAppliedExample", &AAuracronAdvancedCombatExample::execOnElementalDamageAppliedExample },
		{ "RunAdvancedCombatScenario", &AAuracronAdvancedCombatExample::execRunAdvancedCombatScenario },
		{ "SetupExampleCombos", &AAuracronAdvancedCombatExample::execSetupExampleCombos },
		{ "UpdateAICombatExample", &AAuracronAdvancedCombatExample::execUpdateAICombatExample },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAuracronAdvancedCombatExample;
UClass* AAuracronAdvancedCombatExample::GetPrivateStaticClass()
{
	using TClass = AAuracronAdvancedCombatExample;
	if (!Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronAdvancedCombatExample"),
			Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.InnerSingleton,
			StaticRegisterNativesAAuracronAdvancedCombatExample,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.InnerSingleton;
}
UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample_NoRegister()
{
	return AAuracronAdvancedCombatExample::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Example implementation showcasing advanced combat features\n * This demonstrates how to use the expanded AuracronCombatBridge with UE 5.6 features\n */" },
#endif
		{ "IncludePath", "AuracronAdvancedCombatExample.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example implementation showcasing advanced combat features\nThis demonstrates how to use the expanded AuracronCombatBridge with UE 5.6 features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CombatBridge_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combat Bridge Component */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat Bridge Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleInputConfig_MetaData[] = {
		{ "Category", "Enhanced Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Enhanced Input Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Enhanced Input Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleAIConfig_MetaData[] = {
		{ "Category", "AI Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example AI Combat Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example AI Combat Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleElementalConfig_MetaData[] = {
		{ "Category", "Elemental System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Elemental Damage Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Elemental Damage Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleCombos_MetaData[] = {
		{ "Category", "Combo System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Combo Configurations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Combo Configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleDestructionConfig_MetaData[] = {
		{ "Category", "Advanced Destruction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example Advanced Destruction Configuration */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Advanced Destruction Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExampleTarget_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Example target actor for demonstrations */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronAdvancedCombatExample.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example target actor for demonstrations" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CombatBridge;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleInputConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleAIConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleElementalConfig;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleCombos_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ExampleCombos;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ExampleDestructionConfig;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ExampleTarget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAdvancedDestruction, "DemonstrateAdvancedDestruction" }, // 1840756239
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateAIAdaptation, "DemonstrateAIAdaptation" }, // 3664591138
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateCombatAnalytics, "DemonstrateCombatAnalytics" }, // 79545462
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalDamage, "DemonstrateElementalDamage" }, // 3974963662
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateElementalInteractions, "DemonstrateElementalInteractions" }, // 470941830
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_DemonstrateProceduralDamage, "DemonstrateProceduralDamage" }, // 3826074399
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExecuteExampleCombo, "ExecuteExampleCombo" }, // 2764134621
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_ExportExampleAnalytics, "ExportExampleAnalytics" }, // 341757536
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAdvancedDestructionExample, "InitializeAdvancedDestructionExample" }, // 3146817709
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeAICombatExample, "InitializeAICombatExample" }, // 427276722
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeElementalSystemExample, "InitializeElementalSystemExample" }, // 230872285
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_InitializeEnhancedInputExample, "InitializeEnhancedInputExample" }, // 1882310861
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAdvancedDestructionExample, "OnAdvancedDestructionExample" }, // 3975323622
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnAICombatDecisionExample, "OnAICombatDecisionExample" }, // 2683554716
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnCombatAnalyticsUpdatedExample, "OnCombatAnalyticsUpdatedExample" }, // 754720938
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnComboExecutedExample, "OnComboExecutedExample" }, // 4045443812
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_OnElementalDamageAppliedExample, "OnElementalDamageAppliedExample" }, // 3925303914
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_RunAdvancedCombatScenario, "RunAdvancedCombatScenario" }, // 2819501365
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_SetupExampleCombos, "SetupExampleCombos" }, // 892297540
		{ &Z_Construct_UFunction_AAuracronAdvancedCombatExample_UpdateAICombatExample, "UpdateAICombatExample" }, // 1475409951
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAuracronAdvancedCombatExample>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_CombatBridge = { "CombatBridge", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, CombatBridge), Z_Construct_UClass_UAuracronCombatBridge_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CombatBridge_MetaData), NewProp_CombatBridge_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleInputConfig = { "ExampleInputConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleInputConfig), Z_Construct_UScriptStruct_FAuracronEnhancedInputConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleInputConfig_MetaData), NewProp_ExampleInputConfig_MetaData) }; // 2243282029
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleAIConfig = { "ExampleAIConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleAIConfig), Z_Construct_UScriptStruct_FAuracronAICombatConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleAIConfig_MetaData), NewProp_ExampleAIConfig_MetaData) }; // 517528943
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleElementalConfig = { "ExampleElementalConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleElementalConfig), Z_Construct_UScriptStruct_FAuracronElementalDamageConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleElementalConfig_MetaData), NewProp_ExampleElementalConfig_MetaData) }; // 1909653023
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleCombos_Inner = { "ExampleCombos", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronComboConfig, METADATA_PARAMS(0, nullptr) }; // 2639411205
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleCombos = { "ExampleCombos", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleCombos), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleCombos_MetaData), NewProp_ExampleCombos_MetaData) }; // 2639411205
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleDestructionConfig = { "ExampleDestructionConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleDestructionConfig), Z_Construct_UScriptStruct_FAuracronAdvancedDestructionConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleDestructionConfig_MetaData), NewProp_ExampleDestructionConfig_MetaData) }; // 886306391
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleTarget = { "ExampleTarget", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAuracronAdvancedCombatExample, ExampleTarget), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExampleTarget_MetaData), NewProp_ExampleTarget_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_CombatBridge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleInputConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleAIConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleElementalConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleCombos_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleCombos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleDestructionConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::NewProp_ExampleTarget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronCombatBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::ClassParams = {
	&AAuracronAdvancedCombatExample::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::Class_MetaDataParams), Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAuracronAdvancedCombatExample()
{
	if (!Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.OuterSingleton, Z_Construct_UClass_AAuracronAdvancedCombatExample_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAuracronAdvancedCombatExample.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAuracronAdvancedCombatExample);
AAuracronAdvancedCombatExample::~AAuracronAdvancedCombatExample() {}
// ********** End Class AAuracronAdvancedCombatExample *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h__Script_AuracronCombatBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAuracronAdvancedCombatExample, AAuracronAdvancedCombatExample::StaticClass, TEXT("AAuracronAdvancedCombatExample"), &Z_Registration_Info_UClass_AAuracronAdvancedCombatExample, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAuracronAdvancedCombatExample), 2259392469U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h__Script_AuracronCombatBridge_1248663236(TEXT("/Script/AuracronCombatBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h__Script_AuracronCombatBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronCombatBridge_Public_AuracronAdvancedCombatExample_h__Script_AuracronCombatBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
