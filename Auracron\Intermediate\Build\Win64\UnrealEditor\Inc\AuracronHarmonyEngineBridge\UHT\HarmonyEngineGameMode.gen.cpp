// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "HarmonyEngineGameMode.h"
#include "AuracronHarmonyEngineBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeHarmonyEngineGameMode() {}

// ********** Begin Cross Module References ********************************************************
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_AHarmonyEngineGameMode();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_AHarmonyEngineGameMode_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UClass* Z_Construct_UClass_UHarmonyEngineSubsystem_NoRegister();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FHarmonyInterventionData();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FKindnessReward();
AURACRONHARMONYENGINEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
ENGINE_API UClass* Z_Construct_UClass_APlayerController_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AHarmonyEngineGameMode Function OnCommunityHealingStarted ****************
struct HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms
{
	APlayerController* Victim;
	APlayerController* Healer;
};
static FName NAME_AHarmonyEngineGameMode_OnCommunityHealingStarted = FName(TEXT("OnCommunityHealingStarted"));
void AHarmonyEngineGameMode::OnCommunityHealingStarted(APlayerController* Victim, APlayerController* Healer)
{
	HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms Parms;
	Parms.Victim=Victim;
	Parms.Healer=Healer;
	UFunction* Func = FindFunctionChecked(NAME_AHarmonyEngineGameMode_OnCommunityHealingStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Victim;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Healer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::NewProp_Victim = { "Victim", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms, Victim), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::NewProp_Healer = { "Healer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms, Healer), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::NewProp_Victim,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::NewProp_Healer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnCommunityHealingStarted", Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::PropPointers), sizeof(HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEngineGameMode_eventOnCommunityHealingStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AHarmonyEngineGameMode Function OnCommunityHealingStarted ******************

// ********** Begin Class AHarmonyEngineGameMode Function OnHarmonyBehaviorDetected ****************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics
{
	struct HarmonyEngineGameMode_eventOnHarmonyBehaviorDetected_Parms
	{
		FString PlayerID;
		FPlayerBehaviorSnapshot BehaviorData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event handlers for Harmony Engine\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event handlers for Harmony Engine" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyBehaviorDetected_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::NewProp_BehaviorData = { "BehaviorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyBehaviorDetected_Parms, BehaviorData), Z_Construct_UScriptStruct_FPlayerBehaviorSnapshot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorData_MetaData), NewProp_BehaviorData_MetaData) }; // 3446946508
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::NewProp_BehaviorData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnHarmonyBehaviorDetected", Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::HarmonyEngineGameMode_eventOnHarmonyBehaviorDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::HarmonyEngineGameMode_eventOnHarmonyBehaviorDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnHarmonyBehaviorDetected)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FPlayerBehaviorSnapshot,Z_Param_Out_BehaviorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHarmonyBehaviorDetected(Z_Param_PlayerID,Z_Param_Out_BehaviorData);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnHarmonyBehaviorDetected ******************

// ********** Begin Class AHarmonyEngineGameMode Function OnHarmonyCommunityHealing ****************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics
{
	struct HarmonyEngineGameMode_eventOnHarmonyCommunityHealing_Parms
	{
		FString HealerID;
		FString VictimID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictimID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_HealerID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VictimID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::NewProp_HealerID = { "HealerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyCommunityHealing_Parms, HealerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealerID_MetaData), NewProp_HealerID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::NewProp_VictimID = { "VictimID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyCommunityHealing_Parms, VictimID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictimID_MetaData), NewProp_VictimID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::NewProp_HealerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::NewProp_VictimID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnHarmonyCommunityHealing", Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::HarmonyEngineGameMode_eventOnHarmonyCommunityHealing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::HarmonyEngineGameMode_eventOnHarmonyCommunityHealing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnHarmonyCommunityHealing)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_HealerID);
	P_GET_PROPERTY(FStrProperty,Z_Param_VictimID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHarmonyCommunityHealing(Z_Param_HealerID,Z_Param_VictimID);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnHarmonyCommunityHealing ******************

// ********** Begin Class AHarmonyEngineGameMode Function OnHarmonyInterventionTriggered ***********
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics
{
	struct HarmonyEngineGameMode_eventOnHarmonyInterventionTriggered_Parms
	{
		FString PlayerID;
		FHarmonyInterventionData InterventionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InterventionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyInterventionTriggered_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::NewProp_InterventionData = { "InterventionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyInterventionTriggered_Parms, InterventionData), Z_Construct_UScriptStruct_FHarmonyInterventionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionData_MetaData), NewProp_InterventionData_MetaData) }; // 1406652677
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::NewProp_InterventionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnHarmonyInterventionTriggered", Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::HarmonyEngineGameMode_eventOnHarmonyInterventionTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::HarmonyEngineGameMode_eventOnHarmonyInterventionTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnHarmonyInterventionTriggered)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FHarmonyInterventionData,Z_Param_Out_InterventionData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHarmonyInterventionTriggered(Z_Param_PlayerID,Z_Param_Out_InterventionData);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnHarmonyInterventionTriggered *************

// ********** Begin Class AHarmonyEngineGameMode Function OnHarmonyKindnessReward ******************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics
{
	struct HarmonyEngineGameMode_eventOnHarmonyKindnessReward_Parms
	{
		FString PlayerID;
		FKindnessReward Reward;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerID_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reward_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Reward;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::NewProp_PlayerID = { "PlayerID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyKindnessReward_Parms, PlayerID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerID_MetaData), NewProp_PlayerID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::NewProp_Reward = { "Reward", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyKindnessReward_Parms, Reward), Z_Construct_UScriptStruct_FKindnessReward, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reward_MetaData), NewProp_Reward_MetaData) }; // 4200945998
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::NewProp_PlayerID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::NewProp_Reward,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnHarmonyKindnessReward", Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::HarmonyEngineGameMode_eventOnHarmonyKindnessReward_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::HarmonyEngineGameMode_eventOnHarmonyKindnessReward_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnHarmonyKindnessReward)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerID);
	P_GET_STRUCT_REF(FKindnessReward,Z_Param_Out_Reward);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHarmonyKindnessReward(Z_Param_PlayerID,Z_Param_Out_Reward);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnHarmonyKindnessReward ********************

// ********** Begin Class AHarmonyEngineGameMode Function OnHarmonyLevelChanged ********************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics
{
	struct HarmonyEngineGameMode_eventOnHarmonyLevelChanged_Parms
	{
		int32 NewHarmonyLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewHarmonyLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::NewProp_NewHarmonyLevel = { "NewHarmonyLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnHarmonyLevelChanged_Parms, NewHarmonyLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::NewProp_NewHarmonyLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnHarmonyLevelChanged", Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::HarmonyEngineGameMode_eventOnHarmonyLevelChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::HarmonyEngineGameMode_eventOnHarmonyLevelChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnHarmonyLevelChanged)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewHarmonyLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHarmonyLevelChanged(Z_Param_NewHarmonyLevel);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnHarmonyLevelChanged **********************

// ********** Begin Class AHarmonyEngineGameMode Function OnInterventionTriggered ******************
struct HarmonyEngineGameMode_eventOnInterventionTriggered_Parms
{
	APlayerController* Player;
	FString InterventionMessage;
};
static FName NAME_AHarmonyEngineGameMode_OnInterventionTriggered = FName(TEXT("OnInterventionTriggered"));
void AHarmonyEngineGameMode::OnInterventionTriggered(APlayerController* Player, const FString& InterventionMessage)
{
	HarmonyEngineGameMode_eventOnInterventionTriggered_Parms Parms;
	Parms.Player=Player;
	Parms.InterventionMessage=InterventionMessage;
	UFunction* Func = FindFunctionChecked(NAME_AHarmonyEngineGameMode_OnInterventionTriggered);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InterventionMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InterventionMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnInterventionTriggered_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::NewProp_InterventionMessage = { "InterventionMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnInterventionTriggered_Parms, InterventionMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InterventionMessage_MetaData), NewProp_InterventionMessage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::NewProp_InterventionMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnInterventionTriggered", Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::PropPointers), sizeof(HarmonyEngineGameMode_eventOnInterventionTriggered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEngineGameMode_eventOnInterventionTriggered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AHarmonyEngineGameMode Function OnInterventionTriggered ********************

// ********** Begin Class AHarmonyEngineGameMode Function OnKindnessRewardAwarded ******************
struct HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms
{
	APlayerController* Player;
	int32 Points;
	FString Reason;
};
static FName NAME_AHarmonyEngineGameMode_OnKindnessRewardAwarded = FName(TEXT("OnKindnessRewardAwarded"));
void AHarmonyEngineGameMode::OnKindnessRewardAwarded(APlayerController* Player, int32 Points, const FString& Reason)
{
	HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms Parms;
	Parms.Player=Player;
	Parms.Points=Points;
	Parms.Reason=Reason;
	UFunction* Func = FindFunctionChecked(NAME_AHarmonyEngineGameMode_OnKindnessRewardAwarded);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Reason_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Points;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Reason;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms, Points), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Reason = { "Reason", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms, Reason), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Reason_MetaData), NewProp_Reason_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::NewProp_Reason,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnKindnessRewardAwarded", Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::PropPointers), sizeof(HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEngineGameMode_eventOnKindnessRewardAwarded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AHarmonyEngineGameMode Function OnKindnessRewardAwarded ********************

// ********** Begin Class AHarmonyEngineGameMode Function OnPlayerAction ***************************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics
{
	struct HarmonyEngineGameMode_eventOnPlayerAction_Parms
	{
		APlayerController* Player;
		FString ActionType;
		bool bIsPositive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static void NewProp_bIsPositive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPositive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerAction_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
void Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_bIsPositive_SetBit(void* Obj)
{
	((HarmonyEngineGameMode_eventOnPlayerAction_Parms*)Obj)->bIsPositive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_bIsPositive = { "bIsPositive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEngineGameMode_eventOnPlayerAction_Parms), &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_bIsPositive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::NewProp_bIsPositive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPlayerAction", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::HarmonyEngineGameMode_eventOnPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::HarmonyEngineGameMode_eventOnPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnPlayerAction)
{
	P_GET_OBJECT(APlayerController,Z_Param_Player);
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_UBOOL(Z_Param_bIsPositive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerAction(Z_Param_Player,Z_Param_ActionType,Z_Param_bIsPositive);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnPlayerAction *****************************

// ********** Begin Class AHarmonyEngineGameMode Function OnPlayerChatMessage **********************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics
{
	struct HarmonyEngineGameMode_eventOnPlayerChatMessage_Parms
	{
		APlayerController* Player;
		FString Message;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Harmony Engine Integration\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Harmony Engine Integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerChatMessage_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerChatMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPlayerChatMessage", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::HarmonyEngineGameMode_eventOnPlayerChatMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::HarmonyEngineGameMode_eventOnPlayerChatMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnPlayerChatMessage)
{
	P_GET_OBJECT(APlayerController,Z_Param_Player);
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerChatMessage(Z_Param_Player,Z_Param_Message);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnPlayerChatMessage ************************

// ********** Begin Class AHarmonyEngineGameMode Function OnPlayerDeath ****************************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics
{
	struct HarmonyEngineGameMode_eventOnPlayerDeath_Parms
	{
		APlayerController* Player;
		APlayerController* Killer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerDeath_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerDeath_Parms, Killer), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPlayerDeath", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::HarmonyEngineGameMode_eventOnPlayerDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::HarmonyEngineGameMode_eventOnPlayerDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnPlayerDeath)
{
	P_GET_OBJECT(APlayerController,Z_Param_Player);
	P_GET_OBJECT(APlayerController,Z_Param_Killer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerDeath(Z_Param_Player,Z_Param_Killer);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnPlayerDeath ******************************

// ********** Begin Class AHarmonyEngineGameMode Function OnPlayerDisconnect ***********************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics
{
	struct HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms
	{
		APlayerController* Player;
		bool bRageQuit;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_bRageQuit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRageQuit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_bRageQuit_SetBit(void* Obj)
{
	((HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms*)Obj)->bRageQuit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_bRageQuit = { "bRageQuit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms), &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_bRageQuit_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::NewProp_bRageQuit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPlayerDisconnect", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::HarmonyEngineGameMode_eventOnPlayerDisconnect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnPlayerDisconnect)
{
	P_GET_OBJECT(APlayerController,Z_Param_Player);
	P_GET_UBOOL(Z_Param_bRageQuit);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerDisconnect(Z_Param_Player,Z_Param_bRageQuit);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnPlayerDisconnect *************************

// ********** Begin Class AHarmonyEngineGameMode Function OnPlayerKill *****************************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics
{
	struct HarmonyEngineGameMode_eventOnPlayerKill_Parms
	{
		APlayerController* Killer;
		APlayerController* Victim;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Victim;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerKill_Parms, Killer), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::NewProp_Victim = { "Victim", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPlayerKill_Parms, Victim), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::NewProp_Killer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::NewProp_Victim,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPlayerKill", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::HarmonyEngineGameMode_eventOnPlayerKill_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::HarmonyEngineGameMode_eventOnPlayerKill_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnPlayerKill)
{
	P_GET_OBJECT(APlayerController,Z_Param_Killer);
	P_GET_OBJECT(APlayerController,Z_Param_Victim);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerKill(Z_Param_Killer,Z_Param_Victim);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnPlayerKill *******************************

// ********** Begin Class AHarmonyEngineGameMode Function OnPositiveBehaviorDetected ***************
struct HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms
{
	APlayerController* Player;
	float PositivityLevel;
};
static FName NAME_AHarmonyEngineGameMode_OnPositiveBehaviorDetected = FName(TEXT("OnPositiveBehaviorDetected"));
void AHarmonyEngineGameMode::OnPositiveBehaviorDetected(APlayerController* Player, float PositivityLevel)
{
	HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms Parms;
	Parms.Player=Player;
	Parms.PositivityLevel=PositivityLevel;
	UFunction* Func = FindFunctionChecked(NAME_AHarmonyEngineGameMode_OnPositiveBehaviorDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Events" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PositivityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::NewProp_PositivityLevel = { "PositivityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms, PositivityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::NewProp_PositivityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnPositiveBehaviorDetected", Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::PropPointers), sizeof(HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEngineGameMode_eventOnPositiveBehaviorDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AHarmonyEngineGameMode Function OnPositiveBehaviorDetected *****************

// ********** Begin Class AHarmonyEngineGameMode Function OnTeamworkAction *************************
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics
{
	struct HarmonyEngineGameMode_eventOnTeamworkAction_Parms
	{
		APlayerController* Player;
		FString TeamworkType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamworkType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TeamworkType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnTeamworkAction_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::NewProp_TeamworkType = { "TeamworkType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnTeamworkAction_Parms, TeamworkType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamworkType_MetaData), NewProp_TeamworkType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::NewProp_TeamworkType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnTeamworkAction", Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::HarmonyEngineGameMode_eventOnTeamworkAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::HarmonyEngineGameMode_eventOnTeamworkAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AHarmonyEngineGameMode::execOnTeamworkAction)
{
	P_GET_OBJECT(APlayerController,Z_Param_Player);
	P_GET_PROPERTY(FStrProperty,Z_Param_TeamworkType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnTeamworkAction(Z_Param_Player,Z_Param_TeamworkType);
	P_NATIVE_END;
}
// ********** End Class AHarmonyEngineGameMode Function OnTeamworkAction ***************************

// ********** Begin Class AHarmonyEngineGameMode Function OnToxicBehaviorDetected ******************
struct HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms
{
	APlayerController* Player;
	float ToxicityLevel;
};
static FName NAME_AHarmonyEngineGameMode_OnToxicBehaviorDetected = FName(TEXT("OnToxicBehaviorDetected"));
void AHarmonyEngineGameMode::OnToxicBehaviorDetected(APlayerController* Player, float ToxicityLevel)
{
	HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms Parms;
	Parms.Player=Player;
	Parms.ToxicityLevel=ToxicityLevel;
	UFunction* Func = FindFunctionChecked(NAME_AHarmonyEngineGameMode_OnToxicBehaviorDetected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Harmony Engine Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior Analysis Events\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior Analysis Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ToxicityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms, Player), Z_Construct_UClass_APlayerController_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::NewProp_ToxicityLevel = { "ToxicityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms, ToxicityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::NewProp_ToxicityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AHarmonyEngineGameMode, nullptr, "OnToxicBehaviorDetected", Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::PropPointers), sizeof(HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08020800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(HarmonyEngineGameMode_eventOnToxicBehaviorDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// ********** End Class AHarmonyEngineGameMode Function OnToxicBehaviorDetected ********************

// ********** Begin Class AHarmonyEngineGameMode ***************************************************
void AHarmonyEngineGameMode::StaticRegisterNativesAHarmonyEngineGameMode()
{
	UClass* Class = AHarmonyEngineGameMode::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "OnHarmonyBehaviorDetected", &AHarmonyEngineGameMode::execOnHarmonyBehaviorDetected },
		{ "OnHarmonyCommunityHealing", &AHarmonyEngineGameMode::execOnHarmonyCommunityHealing },
		{ "OnHarmonyInterventionTriggered", &AHarmonyEngineGameMode::execOnHarmonyInterventionTriggered },
		{ "OnHarmonyKindnessReward", &AHarmonyEngineGameMode::execOnHarmonyKindnessReward },
		{ "OnHarmonyLevelChanged", &AHarmonyEngineGameMode::execOnHarmonyLevelChanged },
		{ "OnPlayerAction", &AHarmonyEngineGameMode::execOnPlayerAction },
		{ "OnPlayerChatMessage", &AHarmonyEngineGameMode::execOnPlayerChatMessage },
		{ "OnPlayerDeath", &AHarmonyEngineGameMode::execOnPlayerDeath },
		{ "OnPlayerDisconnect", &AHarmonyEngineGameMode::execOnPlayerDisconnect },
		{ "OnPlayerKill", &AHarmonyEngineGameMode::execOnPlayerKill },
		{ "OnTeamworkAction", &AHarmonyEngineGameMode::execOnTeamworkAction },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AHarmonyEngineGameMode;
UClass* AHarmonyEngineGameMode::GetPrivateStaticClass()
{
	using TClass = AHarmonyEngineGameMode;
	if (!Z_Registration_Info_UClass_AHarmonyEngineGameMode.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("HarmonyEngineGameMode"),
			Z_Registration_Info_UClass_AHarmonyEngineGameMode.InnerSingleton,
			StaticRegisterNativesAHarmonyEngineGameMode,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AHarmonyEngineGameMode.InnerSingleton;
}
UClass* Z_Construct_UClass_AHarmonyEngineGameMode_NoRegister()
{
	return AHarmonyEngineGameMode::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AHarmonyEngineGameMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Example Game Mode that demonstrates Harmony Engine integration\n * Shows how to integrate anti-toxicity AI with your game's core systems\n */" },
#endif
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "HarmonyEngineGameMode.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Example Game Mode that demonstrates Harmony Engine integration\nShows how to integrate anti-toxicity AI with your game's core systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHarmonyEngine_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorAnalysisInterval_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToxicityDetectionThreshold_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticInterventions_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableCommunityHealing_MetaData[] = {
		{ "Category", "Harmony Engine Config" },
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HarmonyEngineSubsystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Harmony Engine Reference\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Harmony Engine Reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerJoinTimes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player Tracking\n" },
#endif
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerActionCounts_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerSessionDurations_MetaData[] = {
		{ "ModuleRelativePath", "Public/HarmonyEngineGameMode.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableHarmonyEngine_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHarmonyEngine;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BehaviorAnalysisInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ToxicityDetectionThreshold;
	static void NewProp_bEnableAutomaticInterventions_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticInterventions;
	static void NewProp_bEnableCommunityHealing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCommunityHealing;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HarmonyEngineSubsystem;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerJoinTimes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerJoinTimes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerJoinTimes;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerActionCounts_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerActionCounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerActionCounts;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerSessionDurations_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerSessionDurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerSessionDurations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnCommunityHealingStarted, "OnCommunityHealingStarted" }, // 1675764821
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyBehaviorDetected, "OnHarmonyBehaviorDetected" }, // 1325089400
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyCommunityHealing, "OnHarmonyCommunityHealing" }, // 2586992636
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyInterventionTriggered, "OnHarmonyInterventionTriggered" }, // 1757899741
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyKindnessReward, "OnHarmonyKindnessReward" }, // 476640138
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnHarmonyLevelChanged, "OnHarmonyLevelChanged" }, // 1009923655
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnInterventionTriggered, "OnInterventionTriggered" }, // 323217207
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnKindnessRewardAwarded, "OnKindnessRewardAwarded" }, // 2697667815
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerAction, "OnPlayerAction" }, // 1487336247
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerChatMessage, "OnPlayerChatMessage" }, // 3265479860
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDeath, "OnPlayerDeath" }, // 1794092370
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerDisconnect, "OnPlayerDisconnect" }, // 2641217766
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPlayerKill, "OnPlayerKill" }, // 309725767
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnPositiveBehaviorDetected, "OnPositiveBehaviorDetected" }, // 3061611041
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnTeamworkAction, "OnTeamworkAction" }, // 3474013163
		{ &Z_Construct_UFunction_AHarmonyEngineGameMode_OnToxicBehaviorDetected, "OnToxicBehaviorDetected" }, // 3286906613
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AHarmonyEngineGameMode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableHarmonyEngine_SetBit(void* Obj)
{
	((AHarmonyEngineGameMode*)Obj)->bEnableHarmonyEngine = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableHarmonyEngine = { "bEnableHarmonyEngine", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AHarmonyEngineGameMode), &Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableHarmonyEngine_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHarmonyEngine_MetaData), NewProp_bEnableHarmonyEngine_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_BehaviorAnalysisInterval = { "BehaviorAnalysisInterval", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, BehaviorAnalysisInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorAnalysisInterval_MetaData), NewProp_BehaviorAnalysisInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_ToxicityDetectionThreshold = { "ToxicityDetectionThreshold", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, ToxicityDetectionThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToxicityDetectionThreshold_MetaData), NewProp_ToxicityDetectionThreshold_MetaData) };
void Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableAutomaticInterventions_SetBit(void* Obj)
{
	((AHarmonyEngineGameMode*)Obj)->bEnableAutomaticInterventions = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableAutomaticInterventions = { "bEnableAutomaticInterventions", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AHarmonyEngineGameMode), &Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableAutomaticInterventions_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticInterventions_MetaData), NewProp_bEnableAutomaticInterventions_MetaData) };
void Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableCommunityHealing_SetBit(void* Obj)
{
	((AHarmonyEngineGameMode*)Obj)->bEnableCommunityHealing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableCommunityHealing = { "bEnableCommunityHealing", nullptr, (EPropertyFlags)0x0020080000010015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AHarmonyEngineGameMode), &Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableCommunityHealing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableCommunityHealing_MetaData), NewProp_bEnableCommunityHealing_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_HarmonyEngineSubsystem = { "HarmonyEngineSubsystem", nullptr, (EPropertyFlags)0x0124080000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, HarmonyEngineSubsystem), Z_Construct_UClass_UHarmonyEngineSubsystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HarmonyEngineSubsystem_MetaData), NewProp_HarmonyEngineSubsystem_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes_ValueProp = { "PlayerJoinTimes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes_Key_KeyProp = { "PlayerJoinTimes_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes = { "PlayerJoinTimes", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, PlayerJoinTimes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerJoinTimes_MetaData), NewProp_PlayerJoinTimes_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts_ValueProp = { "PlayerActionCounts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts_Key_KeyProp = { "PlayerActionCounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts = { "PlayerActionCounts", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, PlayerActionCounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerActionCounts_MetaData), NewProp_PlayerActionCounts_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations_ValueProp = { "PlayerSessionDurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations_Key_KeyProp = { "PlayerSessionDurations_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations = { "PlayerSessionDurations", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AHarmonyEngineGameMode, PlayerSessionDurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerSessionDurations_MetaData), NewProp_PlayerSessionDurations_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AHarmonyEngineGameMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableHarmonyEngine,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_BehaviorAnalysisInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_ToxicityDetectionThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableAutomaticInterventions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_bEnableCommunityHealing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_HarmonyEngineSubsystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerJoinTimes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerActionCounts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AHarmonyEngineGameMode_Statics::NewProp_PlayerSessionDurations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AHarmonyEngineGameMode_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AHarmonyEngineGameMode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronHarmonyEngineBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AHarmonyEngineGameMode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AHarmonyEngineGameMode_Statics::ClassParams = {
	&AHarmonyEngineGameMode::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AHarmonyEngineGameMode_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AHarmonyEngineGameMode_Statics::PropPointers),
	0,
	0x009003ACu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AHarmonyEngineGameMode_Statics::Class_MetaDataParams), Z_Construct_UClass_AHarmonyEngineGameMode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AHarmonyEngineGameMode()
{
	if (!Z_Registration_Info_UClass_AHarmonyEngineGameMode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AHarmonyEngineGameMode.OuterSingleton, Z_Construct_UClass_AHarmonyEngineGameMode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AHarmonyEngineGameMode.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AHarmonyEngineGameMode);
AHarmonyEngineGameMode::~AHarmonyEngineGameMode() {}
// ********** End Class AHarmonyEngineGameMode *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h__Script_AuracronHarmonyEngineBridge_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AHarmonyEngineGameMode, AHarmonyEngineGameMode::StaticClass, TEXT("AHarmonyEngineGameMode"), &Z_Registration_Info_UClass_AHarmonyEngineGameMode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AHarmonyEngineGameMode), 2010160943U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h__Script_AuracronHarmonyEngineBridge_1159879769(TEXT("/Script/AuracronHarmonyEngineBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronHarmonyEngineBridge_Public_HarmonyEngineGameMode_h__Script_AuracronHarmonyEngineBridge_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
