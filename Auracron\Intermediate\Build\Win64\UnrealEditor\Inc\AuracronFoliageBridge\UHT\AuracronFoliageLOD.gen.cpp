// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AuracronFoliageLOD.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAuracronFoliageLOD() {}

// ********** Begin Cross Module References ********************************************************
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageLODManager();
AURACRONFOLIAGEBRIDGE_API UClass* Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel();
AURACRONFOLIAGEBRIDGE_API UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronBillboardData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronImpostorData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODInstanceData();
AURACRONFOLIAGEBRIDGE_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODPerformanceData();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
UPackage* Z_Construct_UPackage__Script_AuracronFoliageBridge();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAuracronFoliageLODType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronFoliageLODType;
static UEnum* EAuracronFoliageLODType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLODType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronFoliageLODType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronFoliageLODType"));
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLODType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronFoliageLODType>()
{
	return EAuracronFoliageLODType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Billboard.DisplayName", "Billboard" },
		{ "Billboard.Name", "EAuracronFoliageLODType::Billboard" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronFoliageLODType::Custom" },
		{ "DistanceBased.DisplayName", "Distance Based" },
		{ "DistanceBased.Name", "EAuracronFoliageLODType::DistanceBased" },
		{ "Hybrid.DisplayName", "Hybrid" },
		{ "Hybrid.Name", "EAuracronFoliageLODType::Hybrid" },
		{ "Impostor.DisplayName", "Impostor" },
		{ "Impostor.Name", "EAuracronFoliageLODType::Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
		{ "Nanite.DisplayName", "Nanite" },
		{ "Nanite.Name", "EAuracronFoliageLODType::Nanite" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronFoliageLODType::DistanceBased", (int64)EAuracronFoliageLODType::DistanceBased },
		{ "EAuracronFoliageLODType::Impostor", (int64)EAuracronFoliageLODType::Impostor },
		{ "EAuracronFoliageLODType::Billboard", (int64)EAuracronFoliageLODType::Billboard },
		{ "EAuracronFoliageLODType::Nanite", (int64)EAuracronFoliageLODType::Nanite },
		{ "EAuracronFoliageLODType::Hybrid", (int64)EAuracronFoliageLODType::Hybrid },
		{ "EAuracronFoliageLODType::Custom", (int64)EAuracronFoliageLODType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronFoliageLODType",
	"EAuracronFoliageLODType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType()
{
	if (!Z_Registration_Info_UEnum_EAuracronFoliageLODType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronFoliageLODType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronFoliageLODType.InnerSingleton;
}
// ********** End Enum EAuracronFoliageLODType *****************************************************

// ********** Begin Enum EAuracronImpostorType *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronImpostorType;
static UEnum* EAuracronImpostorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronImpostorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronImpostorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronImpostorType"));
	}
	return Z_Registration_Info_UEnum_EAuracronImpostorType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronImpostorType>()
{
	return EAuracronImpostorType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Impostor types (based on UE5.6 Impostor Baker Plugin)\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronImpostorType::Custom" },
		{ "FullSphere.DisplayName", "Full Sphere" },
		{ "FullSphere.Name", "EAuracronImpostorType::FullSphere" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Impostor types (based on UE5.6 Impostor Baker Plugin)" },
#endif
		{ "TraditionalBillboard.DisplayName", "Traditional Billboard" },
		{ "TraditionalBillboard.Name", "EAuracronImpostorType::TraditionalBillboard" },
		{ "UpperHemisphere.DisplayName", "Upper Hemisphere" },
		{ "UpperHemisphere.Name", "EAuracronImpostorType::UpperHemisphere" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronImpostorType::FullSphere", (int64)EAuracronImpostorType::FullSphere },
		{ "EAuracronImpostorType::UpperHemisphere", (int64)EAuracronImpostorType::UpperHemisphere },
		{ "EAuracronImpostorType::TraditionalBillboard", (int64)EAuracronImpostorType::TraditionalBillboard },
		{ "EAuracronImpostorType::Custom", (int64)EAuracronImpostorType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronImpostorType",
	"EAuracronImpostorType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType()
{
	if (!Z_Registration_Info_UEnum_EAuracronImpostorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronImpostorType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronImpostorType.InnerSingleton;
}
// ********** End Enum EAuracronImpostorType *******************************************************

// ********** Begin Enum EAuracronLODTransitionType ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLODTransitionType;
static UEnum* EAuracronLODTransitionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODTransitionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLODTransitionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronLODTransitionType"));
	}
	return Z_Registration_Info_UEnum_EAuracronLODTransitionType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLODTransitionType>()
{
	return EAuracronLODTransitionType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// LOD transition types\n" },
#endif
		{ "Crossfade.DisplayName", "Crossfade" },
		{ "Crossfade.Name", "EAuracronLODTransitionType::Crossfade" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronLODTransitionType::Custom" },
		{ "Dither.DisplayName", "Dither" },
		{ "Dither.Name", "EAuracronLODTransitionType::Dither" },
		{ "Fade.DisplayName", "Fade" },
		{ "Fade.Name", "EAuracronLODTransitionType::Fade" },
		{ "Instant.DisplayName", "Instant" },
		{ "Instant.Name", "EAuracronLODTransitionType::Instant" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD transition types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLODTransitionType::Instant", (int64)EAuracronLODTransitionType::Instant },
		{ "EAuracronLODTransitionType::Fade", (int64)EAuracronLODTransitionType::Fade },
		{ "EAuracronLODTransitionType::Dither", (int64)EAuracronLODTransitionType::Dither },
		{ "EAuracronLODTransitionType::Crossfade", (int64)EAuracronLODTransitionType::Crossfade },
		{ "EAuracronLODTransitionType::Custom", (int64)EAuracronLODTransitionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronLODTransitionType",
	"EAuracronLODTransitionType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODTransitionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLODTransitionType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLODTransitionType.InnerSingleton;
}
// ********** End Enum EAuracronLODTransitionType **************************************************

// ********** Begin Enum EAuracronCullingType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronCullingType;
static UEnum* EAuracronCullingType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronCullingType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronCullingType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronCullingType"));
	}
	return Z_Registration_Info_UEnum_EAuracronCullingType.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronCullingType>()
{
	return EAuracronCullingType_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAuracronCullingType::Adaptive" },
		{ "BlueprintType", "true" },
		{ "Combined.DisplayName", "Combined" },
		{ "Combined.Name", "EAuracronCullingType::Combined" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling types\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronCullingType::Custom" },
		{ "Distance.DisplayName", "Distance" },
		{ "Distance.Name", "EAuracronCullingType::Distance" },
		{ "Frustum.DisplayName", "Frustum" },
		{ "Frustum.Name", "EAuracronCullingType::Frustum" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
		{ "Occlusion.DisplayName", "Occlusion" },
		{ "Occlusion.Name", "EAuracronCullingType::Occlusion" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling types" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronCullingType::Distance", (int64)EAuracronCullingType::Distance },
		{ "EAuracronCullingType::Frustum", (int64)EAuracronCullingType::Frustum },
		{ "EAuracronCullingType::Occlusion", (int64)EAuracronCullingType::Occlusion },
		{ "EAuracronCullingType::Combined", (int64)EAuracronCullingType::Combined },
		{ "EAuracronCullingType::Adaptive", (int64)EAuracronCullingType::Adaptive },
		{ "EAuracronCullingType::Custom", (int64)EAuracronCullingType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronCullingType",
	"EAuracronCullingType",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType()
{
	if (!Z_Registration_Info_UEnum_EAuracronCullingType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronCullingType.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronCullingType.InnerSingleton;
}
// ********** End Enum EAuracronCullingType ********************************************************

// ********** Begin Enum EAuracronLODQualityLevel **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAuracronLODQualityLevel;
static UEnum* EAuracronLODQualityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODQualityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAuracronLODQualityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("EAuracronLODQualityLevel"));
	}
	return Z_Registration_Info_UEnum_EAuracronLODQualityLevel.OuterSingleton;
}
template<> AURACRONFOLIAGEBRIDGE_API UEnum* StaticEnum<EAuracronLODQualityLevel>()
{
	return EAuracronLODQualityLevel_StaticEnum();
}
struct Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Cinematic.DisplayName", "Cinematic" },
		{ "Cinematic.Name", "EAuracronLODQualityLevel::Cinematic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance quality levels\n" },
#endif
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAuracronLODQualityLevel::Custom" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EAuracronLODQualityLevel::Epic" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAuracronLODQualityLevel::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAuracronLODQualityLevel::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAuracronLODQualityLevel::Medium" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance quality levels" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAuracronLODQualityLevel::Low", (int64)EAuracronLODQualityLevel::Low },
		{ "EAuracronLODQualityLevel::Medium", (int64)EAuracronLODQualityLevel::Medium },
		{ "EAuracronLODQualityLevel::High", (int64)EAuracronLODQualityLevel::High },
		{ "EAuracronLODQualityLevel::Epic", (int64)EAuracronLODQualityLevel::Epic },
		{ "EAuracronLODQualityLevel::Cinematic", (int64)EAuracronLODQualityLevel::Cinematic },
		{ "EAuracronLODQualityLevel::Custom", (int64)EAuracronLODQualityLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	"EAuracronLODQualityLevel",
	"EAuracronLODQualityLevel",
	Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel()
{
	if (!Z_Registration_Info_UEnum_EAuracronLODQualityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAuracronLODQualityLevel.InnerSingleton, Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAuracronLODQualityLevel.InnerSingleton;
}
// ********** End Enum EAuracronLODQualityLevel ****************************************************

// ********** Begin ScriptStruct FAuracronFoliageLODConfiguration **********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration;
class UScriptStruct* FAuracronFoliageLODConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronFoliageLODConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Configuration Data\n * Configuration for LOD system behavior\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Configuration Data\nConfiguration for LOD system behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableLODSystem_MetaData[] = {
		{ "Category", "LOD System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultLODType_MetaData[] = {
		{ "Category", "LOD System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityLevel_MetaData[] = {
		{ "Category", "LOD System" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD0Distance_MetaData[] = {
		{ "Category", "Distance LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD1Distance_MetaData[] = {
		{ "Category", "Distance LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD2Distance_MetaData[] = {
		{ "Category", "Distance LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD3Distance_MetaData[] = {
		{ "Category", "Distance LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartCullDistance_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndCullDistance_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingType_MetaData[] = {
		{ "Category", "Culling" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionType_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeMultiplier_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncLODUpdates_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLODUpdatesPerFrame_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODUpdateInterval_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceMonitoring_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableImpostorGeneration_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorType_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorResolution_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorFramesXY_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableBillboardGeneration_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardResolution_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCenterXYOnMeshPivot_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePlaneTop_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePlaneSides_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHISMOptimization_MetaData[] = {
		{ "Category", "HISM Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerCluster_MetaData[] = {
		{ "Category", "HISM Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClusterRadius_MetaData[] = {
		{ "Category", "HISM Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGPUCulling_MetaData[] = {
		{ "Category", "HISM Optimization" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableLODSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableLODSystem;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DefaultLODType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DefaultLODType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD0Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD1Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD2Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD3Distance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartCullDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EndCullDistance;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CullingType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CullingType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TransitionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TransitionType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeMultiplier;
	static void NewProp_bEnableAsyncLODUpdates_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncLODUpdates;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLODUpdatesPerFrame;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODUpdateInterval;
	static void NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceMonitoring;
	static void NewProp_bEnableImpostorGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableImpostorGeneration;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ImpostorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ImpostorType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ImpostorResolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ImpostorFramesXY;
	static void NewProp_bEnableBillboardGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableBillboardGeneration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BillboardResolution;
	static void NewProp_bCenterXYOnMeshPivot_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCenterXYOnMeshPivot;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReferencePlaneTop;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReferencePlaneSides;
	static void NewProp_bEnableHISMOptimization_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHISMOptimization;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerCluster;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ClusterRadius;
	static void NewProp_bEnableGPUCulling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGPUCulling;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronFoliageLODConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableLODSystem_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableLODSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableLODSystem = { "bEnableLODSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableLODSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableLODSystem_MetaData), NewProp_bEnableLODSystem_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_DefaultLODType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_DefaultLODType = { "DefaultLODType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, DefaultLODType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronFoliageLODType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultLODType_MetaData), NewProp_DefaultLODType_MetaData) }; // 1685479502
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, QualityLevel), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityLevel_MetaData), NewProp_QualityLevel_MetaData) }; // 683004800
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD0Distance = { "LOD0Distance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, LOD0Distance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD0Distance_MetaData), NewProp_LOD0Distance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD1Distance = { "LOD1Distance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, LOD1Distance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD1Distance_MetaData), NewProp_LOD1Distance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD2Distance = { "LOD2Distance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, LOD2Distance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD2Distance_MetaData), NewProp_LOD2Distance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD3Distance = { "LOD3Distance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, LOD3Distance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD3Distance_MetaData), NewProp_LOD3Distance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_StartCullDistance = { "StartCullDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, StartCullDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartCullDistance_MetaData), NewProp_StartCullDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_EndCullDistance = { "EndCullDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, EndCullDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndCullDistance_MetaData), NewProp_EndCullDistance_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_CullingType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_CullingType = { "CullingType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, CullingType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronCullingType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingType_MetaData), NewProp_CullingType_MetaData) }; // 3086061814
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionType = { "TransitionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, TransitionType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODTransitionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionType_MetaData), NewProp_TransitionType_MetaData) }; // 1867776590
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_FadeMultiplier = { "FadeMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, FadeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeMultiplier_MetaData), NewProp_FadeMultiplier_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableAsyncLODUpdates_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableAsyncLODUpdates = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableAsyncLODUpdates = { "bEnableAsyncLODUpdates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableAsyncLODUpdates_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncLODUpdates_MetaData), NewProp_bEnableAsyncLODUpdates_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_MaxLODUpdatesPerFrame = { "MaxLODUpdatesPerFrame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, MaxLODUpdatesPerFrame), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLODUpdatesPerFrame_MetaData), NewProp_MaxLODUpdatesPerFrame_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LODUpdateInterval = { "LODUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, LODUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODUpdateInterval_MetaData), NewProp_LODUpdateInterval_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnablePerformanceMonitoring = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnablePerformanceMonitoring = { "bEnablePerformanceMonitoring", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnablePerformanceMonitoring_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceMonitoring_MetaData), NewProp_bEnablePerformanceMonitoring_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableImpostorGeneration_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableImpostorGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableImpostorGeneration = { "bEnableImpostorGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableImpostorGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableImpostorGeneration_MetaData), NewProp_bEnableImpostorGeneration_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorType = { "ImpostorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ImpostorType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorType_MetaData), NewProp_ImpostorType_MetaData) }; // 2533920382
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorResolution = { "ImpostorResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ImpostorResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorResolution_MetaData), NewProp_ImpostorResolution_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorFramesXY = { "ImpostorFramesXY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ImpostorFramesXY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorFramesXY_MetaData), NewProp_ImpostorFramesXY_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableBillboardGeneration_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableBillboardGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableBillboardGeneration = { "bEnableBillboardGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableBillboardGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableBillboardGeneration_MetaData), NewProp_bEnableBillboardGeneration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_BillboardResolution = { "BillboardResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, BillboardResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardResolution_MetaData), NewProp_BillboardResolution_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bCenterXYOnMeshPivot_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bCenterXYOnMeshPivot = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bCenterXYOnMeshPivot = { "bCenterXYOnMeshPivot", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bCenterXYOnMeshPivot_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCenterXYOnMeshPivot_MetaData), NewProp_bCenterXYOnMeshPivot_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ReferencePlaneTop = { "ReferencePlaneTop", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ReferencePlaneTop), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePlaneTop_MetaData), NewProp_ReferencePlaneTop_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ReferencePlaneSides = { "ReferencePlaneSides", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ReferencePlaneSides), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePlaneSides_MetaData), NewProp_ReferencePlaneSides_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableHISMOptimization_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableHISMOptimization = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableHISMOptimization = { "bEnableHISMOptimization", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableHISMOptimization_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHISMOptimization_MetaData), NewProp_bEnableHISMOptimization_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_MaxInstancesPerCluster = { "MaxInstancesPerCluster", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, MaxInstancesPerCluster), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerCluster_MetaData), NewProp_MaxInstancesPerCluster_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ClusterRadius = { "ClusterRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronFoliageLODConfiguration, ClusterRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClusterRadius_MetaData), NewProp_ClusterRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableGPUCulling_SetBit(void* Obj)
{
	((FAuracronFoliageLODConfiguration*)Obj)->bEnableGPUCulling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableGPUCulling = { "bEnableGPUCulling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronFoliageLODConfiguration), &Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableGPUCulling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGPUCulling_MetaData), NewProp_bEnableGPUCulling_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableLODSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_DefaultLODType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_DefaultLODType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_QualityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD0Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD1Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD2Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LOD3Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_StartCullDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_EndCullDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_CullingType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_CullingType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_TransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_FadeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableAsyncLODUpdates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_MaxLODUpdatesPerFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_LODUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnablePerformanceMonitoring,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableImpostorGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ImpostorFramesXY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableBillboardGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_BillboardResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bCenterXYOnMeshPivot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ReferencePlaneTop,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ReferencePlaneSides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableHISMOptimization,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_MaxInstancesPerCluster,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_ClusterRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewProp_bEnableGPUCulling,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronFoliageLODConfiguration",
	Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::PropPointers),
	sizeof(FAuracronFoliageLODConfiguration),
	alignof(FAuracronFoliageLODConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FAuracronFoliageLODConfiguration ************************************

// ********** Begin ScriptStruct FAuracronImpostorData *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronImpostorData;
class UScriptStruct* FAuracronImpostorData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronImpostorData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronImpostorData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronImpostorData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronImpostorData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronImpostorData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronImpostorData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Impostor Data\n * Data for impostor generation and management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Impostor Data\nData for impostor generation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorId_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMesh_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorMesh_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorMaterial_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorType_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Resolution_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FramesXY_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCaptureUsingGBuffer_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOrthographic_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraDistance_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SceneCaptureResolution_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorMapsToRender_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChannelPackedMasks_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstantSpecular_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstantRoughness_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstantOpacity_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubsurfaceColor_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerated_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Impostor" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImpostorId;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpostorMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpostorMaterial;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ImpostorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ImpostorType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Resolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FramesXY;
	static void NewProp_bCaptureUsingGBuffer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCaptureUsingGBuffer;
	static void NewProp_bOrthographic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOrthographic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SceneCaptureResolution;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ColorMapsToRender_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ColorMapsToRender;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelPackedMasks_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ChannelPackedMasks_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ChannelPackedMasks;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstantSpecular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstantRoughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConstantOpacity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SubsurfaceColor;
	static void NewProp_bIsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerated;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronImpostorData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorId = { "ImpostorId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ImpostorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorId_MetaData), NewProp_ImpostorId_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMesh_MetaData), NewProp_SourceMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorMesh = { "ImpostorMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ImpostorMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorMesh_MetaData), NewProp_ImpostorMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorMaterial = { "ImpostorMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ImpostorMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorMaterial_MetaData), NewProp_ImpostorMaterial_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorType = { "ImpostorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ImpostorType), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronImpostorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorType_MetaData), NewProp_ImpostorType_MetaData) }; // 2533920382
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_Resolution = { "Resolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, Resolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Resolution_MetaData), NewProp_Resolution_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_FramesXY = { "FramesXY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, FramesXY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FramesXY_MetaData), NewProp_FramesXY_MetaData) };
void Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bCaptureUsingGBuffer_SetBit(void* Obj)
{
	((FAuracronImpostorData*)Obj)->bCaptureUsingGBuffer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bCaptureUsingGBuffer = { "bCaptureUsingGBuffer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronImpostorData), &Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bCaptureUsingGBuffer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCaptureUsingGBuffer_MetaData), NewProp_bCaptureUsingGBuffer_MetaData) };
void Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bOrthographic_SetBit(void* Obj)
{
	((FAuracronImpostorData*)Obj)->bOrthographic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bOrthographic = { "bOrthographic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronImpostorData), &Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bOrthographic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOrthographic_MetaData), NewProp_bOrthographic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_CameraDistance = { "CameraDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, CameraDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraDistance_MetaData), NewProp_CameraDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SceneCaptureResolution = { "SceneCaptureResolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, SceneCaptureResolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SceneCaptureResolution_MetaData), NewProp_SceneCaptureResolution_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ColorMapsToRender_Inner = { "ColorMapsToRender", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ColorMapsToRender = { "ColorMapsToRender", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ColorMapsToRender), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorMapsToRender_MetaData), NewProp_ColorMapsToRender_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks_ValueProp = { "ChannelPackedMasks", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks_Key_KeyProp = { "ChannelPackedMasks_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks = { "ChannelPackedMasks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ChannelPackedMasks), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChannelPackedMasks_MetaData), NewProp_ChannelPackedMasks_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantSpecular = { "ConstantSpecular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ConstantSpecular), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstantSpecular_MetaData), NewProp_ConstantSpecular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantRoughness = { "ConstantRoughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ConstantRoughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstantRoughness_MetaData), NewProp_ConstantRoughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantOpacity = { "ConstantOpacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, ConstantOpacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstantOpacity_MetaData), NewProp_ConstantOpacity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SubsurfaceColor = { "SubsurfaceColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, SubsurfaceColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubsurfaceColor_MetaData), NewProp_SubsurfaceColor_MetaData) };
void Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bIsGenerated_SetBit(void* Obj)
{
	((FAuracronImpostorData*)Obj)->bIsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bIsGenerated = { "bIsGenerated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronImpostorData), &Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bIsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerated_MetaData), NewProp_bIsGenerated_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronImpostorData, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ImpostorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_Resolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_FramesXY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bCaptureUsingGBuffer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bOrthographic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_CameraDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SceneCaptureResolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ColorMapsToRender_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ColorMapsToRender,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ChannelPackedMasks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantSpecular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantRoughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_ConstantOpacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_SubsurfaceColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_bIsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewProp_GenerationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronImpostorData",
	Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::PropPointers),
	sizeof(FAuracronImpostorData),
	alignof(FAuracronImpostorData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronImpostorData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronImpostorData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronImpostorData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronImpostorData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronImpostorData ***********************************************

// ********** Begin ScriptStruct FAuracronBillboardData ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronBillboardData;
class UScriptStruct* FAuracronBillboardData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBillboardData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronBillboardData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronBillboardData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronBillboardData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBillboardData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronBillboardData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Billboard Data\n * Data for billboard generation and management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Billboard Data\nData for billboard generation and management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardId_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMesh_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardMesh_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardMaterial_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Resolution_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HorizontalFrames_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCenterXYOnMeshPivot_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePlaneTop_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReferencePlaneSides_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSpriteVertexShader_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DitherTransition_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VertexShaderThreshold_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsGenerated_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationTime_MetaData[] = {
		{ "Category", "Billboard" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BillboardId;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BillboardMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BillboardMaterial;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Resolution;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HorizontalFrames;
	static void NewProp_bCenterXYOnMeshPivot_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCenterXYOnMeshPivot;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReferencePlaneTop;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReferencePlaneSides;
	static void NewProp_bUseSpriteVertexShader_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSpriteVertexShader;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DitherTransition;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VertexShaderThreshold;
	static void NewProp_bIsGenerated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsGenerated;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronBillboardData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardId = { "BillboardId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, BillboardId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardId_MetaData), NewProp_BillboardId_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMesh_MetaData), NewProp_SourceMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardMesh = { "BillboardMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, BillboardMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardMesh_MetaData), NewProp_BillboardMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardMaterial = { "BillboardMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, BillboardMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardMaterial_MetaData), NewProp_BillboardMaterial_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_Resolution = { "Resolution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, Resolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Resolution_MetaData), NewProp_Resolution_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_HorizontalFrames = { "HorizontalFrames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, HorizontalFrames), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HorizontalFrames_MetaData), NewProp_HorizontalFrames_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bCenterXYOnMeshPivot_SetBit(void* Obj)
{
	((FAuracronBillboardData*)Obj)->bCenterXYOnMeshPivot = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bCenterXYOnMeshPivot = { "bCenterXYOnMeshPivot", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBillboardData), &Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bCenterXYOnMeshPivot_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCenterXYOnMeshPivot_MetaData), NewProp_bCenterXYOnMeshPivot_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_ReferencePlaneTop = { "ReferencePlaneTop", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, ReferencePlaneTop), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePlaneTop_MetaData), NewProp_ReferencePlaneTop_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_ReferencePlaneSides = { "ReferencePlaneSides", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, ReferencePlaneSides), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReferencePlaneSides_MetaData), NewProp_ReferencePlaneSides_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bUseSpriteVertexShader_SetBit(void* Obj)
{
	((FAuracronBillboardData*)Obj)->bUseSpriteVertexShader = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bUseSpriteVertexShader = { "bUseSpriteVertexShader", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBillboardData), &Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bUseSpriteVertexShader_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSpriteVertexShader_MetaData), NewProp_bUseSpriteVertexShader_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_DitherTransition = { "DitherTransition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, DitherTransition), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DitherTransition_MetaData), NewProp_DitherTransition_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_VertexShaderThreshold = { "VertexShaderThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, VertexShaderThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VertexShaderThreshold_MetaData), NewProp_VertexShaderThreshold_MetaData) };
void Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bIsGenerated_SetBit(void* Obj)
{
	((FAuracronBillboardData*)Obj)->bIsGenerated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bIsGenerated = { "bIsGenerated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronBillboardData), &Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bIsGenerated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsGenerated_MetaData), NewProp_bIsGenerated_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_GenerationTime = { "GenerationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronBillboardData, GenerationTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationTime_MetaData), NewProp_GenerationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_BillboardMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_Resolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_HorizontalFrames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bCenterXYOnMeshPivot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_ReferencePlaneTop,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_ReferencePlaneSides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bUseSpriteVertexShader,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_DitherTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_VertexShaderThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_bIsGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewProp_GenerationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronBillboardData",
	Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::PropPointers),
	sizeof(FAuracronBillboardData),
	alignof(FAuracronBillboardData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronBillboardData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronBillboardData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronBillboardData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronBillboardData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronBillboardData **********************************************

// ********** Begin ScriptStruct FAuracronLODPerformanceData ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData;
class UScriptStruct* FAuracronLODPerformanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLODPerformanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronLODPerformanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Performance Data\n * Performance metrics for LOD system\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Performance Data\nPerformance metrics for LOD system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisibleInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CulledInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD0Instances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD1Instances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD2Instances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LOD3Instances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardInstances_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LODUpdateTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CullingTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GPUMemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_VisibleInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CulledInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LOD0Instances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LOD1Instances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LOD2Instances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LOD3Instances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ImpostorInstances;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BillboardInstances;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LODUpdateTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CullingTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GPUMemoryUsageMB;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLODPerformanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_TotalInstances = { "TotalInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, TotalInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalInstances_MetaData), NewProp_TotalInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_VisibleInstances = { "VisibleInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, VisibleInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisibleInstances_MetaData), NewProp_VisibleInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_CulledInstances = { "CulledInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, CulledInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CulledInstances_MetaData), NewProp_CulledInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD0Instances = { "LOD0Instances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, LOD0Instances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD0Instances_MetaData), NewProp_LOD0Instances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD1Instances = { "LOD1Instances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, LOD1Instances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD1Instances_MetaData), NewProp_LOD1Instances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD2Instances = { "LOD2Instances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, LOD2Instances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD2Instances_MetaData), NewProp_LOD2Instances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD3Instances = { "LOD3Instances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, LOD3Instances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LOD3Instances_MetaData), NewProp_LOD3Instances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_ImpostorInstances = { "ImpostorInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, ImpostorInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorInstances_MetaData), NewProp_ImpostorInstances_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_BillboardInstances = { "BillboardInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, BillboardInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardInstances_MetaData), NewProp_BillboardInstances_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LODUpdateTime = { "LODUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, LODUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LODUpdateTime_MetaData), NewProp_LODUpdateTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_CullingTime = { "CullingTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, CullingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CullingTime_MetaData), NewProp_CullingTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_RenderTime = { "RenderTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, RenderTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderTime_MetaData), NewProp_RenderTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, Triangles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_GPUMemoryUsageMB = { "GPUMemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODPerformanceData, GPUMemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GPUMemoryUsageMB_MetaData), NewProp_GPUMemoryUsageMB_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_TotalInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_VisibleInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_CulledInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD0Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD1Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD2Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LOD3Instances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_ImpostorInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_BillboardInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_LODUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_CullingTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_RenderTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewProp_GPUMemoryUsageMB,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronLODPerformanceData",
	Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::PropPointers),
	sizeof(FAuracronLODPerformanceData),
	alignof(FAuracronLODPerformanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODPerformanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLODPerformanceData *****************************************

// ********** Begin ScriptStruct FAuracronLODInstanceData ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData;
class UScriptStruct* FAuracronLODInstanceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLODInstanceData, (UObject*)Z_Construct_UPackage__Script_AuracronFoliageBridge(), TEXT("AuracronLODInstanceData"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * LOD Instance Data\n * Data for individual foliage instance LOD management\n */" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "LOD Instance Data\nData for individual foliage instance LOD management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FoliageTypeId_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceTransform_MetaData[] = {
		{ "Category", "Instance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetLODLevel_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DistanceToCamera_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeAmount_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCulled_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTransitioning_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionProgress_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "LOD" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriangleCount_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RenderCost_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FoliageTypeId;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceTransform;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLODLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetLODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToCamera;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeAmount;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_bIsCulled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCulled;
	static void NewProp_bIsTransitioning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTransitioning;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TriangleCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RenderCost;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLODInstanceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_FoliageTypeId = { "FoliageTypeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, FoliageTypeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FoliageTypeId_MetaData), NewProp_FoliageTypeId_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_InstanceTransform = { "InstanceTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, InstanceTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceTransform_MetaData), NewProp_InstanceTransform_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_CurrentLODLevel = { "CurrentLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, CurrentLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLODLevel_MetaData), NewProp_CurrentLODLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TargetLODLevel = { "TargetLODLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, TargetLODLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetLODLevel_MetaData), NewProp_TargetLODLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_DistanceToCamera = { "DistanceToCamera", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, DistanceToCamera), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DistanceToCamera_MetaData), NewProp_DistanceToCamera_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_FadeAmount = { "FadeAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, FadeAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeAmount_MetaData), NewProp_FadeAmount_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAuracronLODInstanceData*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODInstanceData), &Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsCulled_SetBit(void* Obj)
{
	((FAuracronLODInstanceData*)Obj)->bIsCulled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsCulled = { "bIsCulled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODInstanceData), &Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsCulled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCulled_MetaData), NewProp_bIsCulled_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsTransitioning_SetBit(void* Obj)
{
	((FAuracronLODInstanceData*)Obj)->bIsTransitioning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsTransitioning = { "bIsTransitioning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLODInstanceData), &Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsTransitioning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTransitioning_MetaData), NewProp_bIsTransitioning_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TransitionProgress = { "TransitionProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, TransitionProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionProgress_MetaData), NewProp_TransitionProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TriangleCount = { "TriangleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, TriangleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriangleCount_MetaData), NewProp_TriangleCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_RenderCost = { "RenderCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLODInstanceData, RenderCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RenderCost_MetaData), NewProp_RenderCost_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_FoliageTypeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_InstanceTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_CurrentLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TargetLODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_DistanceToCamera,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_FadeAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsCulled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_bIsTransitioning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TransitionProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_TriangleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewProp_RenderCost,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
	nullptr,
	&NewStructOps,
	"AuracronLODInstanceData",
	Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::PropPointers),
	sizeof(FAuracronLODInstanceData),
	alignof(FAuracronLODInstanceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLODInstanceData()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLODInstanceData ********************************************

// ********** Begin Delegate FOnLODChanged *********************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics
{
	struct AuracronFoliageLODManager_eventOnLODChanged_Parms
	{
		FString InstanceId;
		int32 OldLOD;
		int32 NewLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldLOD;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnLODChanged_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_OldLOD = { "OldLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnLODChanged_Parms, OldLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_NewLOD = { "NewLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnLODChanged_Parms, NewLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_OldLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::NewProp_NewLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "OnLODChanged__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnLODChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnLODChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageLODManager::FOnLODChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLODChanged, const FString& InstanceId, int32 OldLOD, int32 NewLOD)
{
	struct AuracronFoliageLODManager_eventOnLODChanged_Parms
	{
		FString InstanceId;
		int32 OldLOD;
		int32 NewLOD;
	};
	AuracronFoliageLODManager_eventOnLODChanged_Parms Parms;
	Parms.InstanceId=InstanceId;
	Parms.OldLOD=OldLOD;
	Parms.NewLOD=NewLOD;
	OnLODChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnLODChanged ***********************************************************

// ********** Begin Delegate FOnInstanceCulled *****************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics
{
	struct AuracronFoliageLODManager_eventOnInstanceCulled_Parms
	{
		FString InstanceId;
		bool bCulled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_bCulled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCulled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnInstanceCulled_Parms, InstanceId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_bCulled_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventOnInstanceCulled_Parms*)Obj)->bCulled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_bCulled = { "bCulled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventOnInstanceCulled_Parms), &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_bCulled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::NewProp_bCulled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "OnInstanceCulled__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnInstanceCulled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnInstanceCulled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageLODManager::FOnInstanceCulled_DelegateWrapper(const FMulticastScriptDelegate& OnInstanceCulled, const FString& InstanceId, bool bCulled)
{
	struct AuracronFoliageLODManager_eventOnInstanceCulled_Parms
	{
		FString InstanceId;
		bool bCulled;
	};
	AuracronFoliageLODManager_eventOnInstanceCulled_Parms Parms;
	Parms.InstanceId=InstanceId;
	Parms.bCulled=bCulled ? true : false;
	OnInstanceCulled.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnInstanceCulled *******************************************************

// ********** Begin Delegate FOnImpostorGenerated **************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics
{
	struct AuracronFoliageLODManager_eventOnImpostorGenerated_Parms
	{
		FString ImpostorId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImpostorId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_ImpostorId = { "ImpostorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnImpostorGenerated_Parms, ImpostorId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventOnImpostorGenerated_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventOnImpostorGenerated_Parms), &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_ImpostorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "OnImpostorGenerated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnImpostorGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnImpostorGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageLODManager::FOnImpostorGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnImpostorGenerated, const FString& ImpostorId, bool bSuccess)
{
	struct AuracronFoliageLODManager_eventOnImpostorGenerated_Parms
	{
		FString ImpostorId;
		bool bSuccess;
	};
	AuracronFoliageLODManager_eventOnImpostorGenerated_Parms Parms;
	Parms.ImpostorId=ImpostorId;
	Parms.bSuccess=bSuccess ? true : false;
	OnImpostorGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnImpostorGenerated ****************************************************

// ********** Begin Delegate FOnBillboardGenerated *************************************************
struct Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics
{
	struct AuracronFoliageLODManager_eventOnBillboardGenerated_Parms
	{
		FString BillboardId;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BillboardId;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_BillboardId = { "BillboardId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventOnBillboardGenerated_Parms, BillboardId), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventOnBillboardGenerated_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventOnBillboardGenerated_Parms), &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_BillboardId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "OnBillboardGenerated__DelegateSignature", Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnBillboardGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::AuracronFoliageLODManager_eventOnBillboardGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UAuracronFoliageLODManager::FOnBillboardGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnBillboardGenerated, const FString& BillboardId, bool bSuccess)
{
	struct AuracronFoliageLODManager_eventOnBillboardGenerated_Parms
	{
		FString BillboardId;
		bool bSuccess;
	};
	AuracronFoliageLODManager_eventOnBillboardGenerated_Parms Parms;
	Parms.BillboardId=BillboardId;
	Parms.bSuccess=bSuccess ? true : false;
	OnBillboardGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnBillboardGenerated ***************************************************

// ********** Begin Class UAuracronFoliageLODManager Function ApplyBiomeLODToInstances *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics
{
	struct AuracronFoliageLODManager_eventApplyBiomeLODToInstances_Parms
	{
		FString BiomeId;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventApplyBiomeLODToInstances_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::NewProp_BiomeId,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "ApplyBiomeLODToInstances", Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::AuracronFoliageLODManager_eventApplyBiomeLODToInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::AuracronFoliageLODManager_eventApplyBiomeLODToInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execApplyBiomeLODToInstances)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyBiomeLODToInstances(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function ApplyBiomeLODToInstances ***************

// ********** Begin Class UAuracronFoliageLODManager Function BatchGenerateImpostors ***************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics
{
	struct AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms
	{
		TArray<UStaticMesh*> SourceMeshes;
		FAuracronImpostorData Settings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceMeshes_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SourceMeshes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_SourceMeshes_Inner = { "SourceMeshes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_SourceMeshes = { "SourceMeshes", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms, SourceMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceMeshes_MetaData), NewProp_SourceMeshes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms, Settings), Z_Construct_UScriptStruct_FAuracronImpostorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 1298454982
void Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_SourceMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_SourceMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_Settings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "BatchGenerateImpostors", Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::AuracronFoliageLODManager_eventBatchGenerateImpostors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execBatchGenerateImpostors)
{
	P_GET_TARRAY_REF(UStaticMesh*,Z_Param_Out_SourceMeshes);
	P_GET_STRUCT_REF(FAuracronImpostorData,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->BatchGenerateImpostors(Z_Param_Out_SourceMeshes,Z_Param_Out_Settings);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function BatchGenerateImpostors *****************

// ********** Begin Class UAuracronFoliageLODManager Function CalculateFadeAmount ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics
{
	struct AuracronFoliageLODManager_eventCalculateFadeAmount_Parms
	{
		float Distance;
		int32 LODLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventCalculateFadeAmount_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventCalculateFadeAmount_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventCalculateFadeAmount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "CalculateFadeAmount", Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::AuracronFoliageLODManager_eventCalculateFadeAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::AuracronFoliageLODManager_eventCalculateFadeAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execCalculateFadeAmount)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateFadeAmount(Z_Param_Distance,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function CalculateFadeAmount ********************

// ********** Begin Class UAuracronFoliageLODManager Function CalculateLODLevel ********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics
{
	struct AuracronFoliageLODManager_eventCalculateLODLevel_Parms
	{
		float Distance;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Distance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::NewProp_Distance = { "Distance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventCalculateLODLevel_Parms, Distance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventCalculateLODLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::NewProp_Distance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "CalculateLODLevel", Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::AuracronFoliageLODManager_eventCalculateLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::AuracronFoliageLODManager_eventCalculateLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execCalculateLODLevel)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Distance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->CalculateLODLevel(Z_Param_Distance);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function CalculateLODLevel **********************

// ********** Begin Class UAuracronFoliageLODManager Function DrawDebugLODInfo *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics
{
	struct AuracronFoliageLODManager_eventDrawDebugLODInfo_Parms
	{
		UWorld* World;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventDrawDebugLODInfo_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::NewProp_World,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "DrawDebugLODInfo", Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::AuracronFoliageLODManager_eventDrawDebugLODInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::AuracronFoliageLODManager_eventDrawDebugLODInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execDrawDebugLODInfo)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DrawDebugLODInfo(Z_Param_World);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function DrawDebugLODInfo ***********************

// ********** Begin Class UAuracronFoliageLODManager Function EnableDebugVisualization *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics
{
	struct AuracronFoliageLODManager_eventEnableDebugVisualization_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug and visualization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug and visualization" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventEnableDebugVisualization_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventEnableDebugVisualization_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "EnableDebugVisualization", Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::AuracronFoliageLODManager_eventEnableDebugVisualization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::AuracronFoliageLODManager_eventEnableDebugVisualization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execEnableDebugVisualization)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableDebugVisualization(Z_Param_bEnabled);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function EnableDebugVisualization ***************

// ********** Begin Class UAuracronFoliageLODManager Function GenerateBillboard ********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics
{
	struct AuracronFoliageLODManager_eventGenerateBillboard_Parms
	{
		UStaticMesh* SourceMesh;
		FAuracronBillboardData BillboardSettings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Billboard management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Billboard management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BillboardSettings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGenerateBillboard_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_BillboardSettings = { "BillboardSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGenerateBillboard_Parms, BillboardSettings), Z_Construct_UScriptStruct_FAuracronBillboardData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardSettings_MetaData), NewProp_BillboardSettings_MetaData) }; // 344031264
void Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventGenerateBillboard_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventGenerateBillboard_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_BillboardSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GenerateBillboard", Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::AuracronFoliageLODManager_eventGenerateBillboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::AuracronFoliageLODManager_eventGenerateBillboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGenerateBillboard)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_GET_STRUCT_REF(FAuracronBillboardData,Z_Param_Out_BillboardSettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateBillboard(Z_Param_SourceMesh,Z_Param_Out_BillboardSettings);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GenerateBillboard **********************

// ********** Begin Class UAuracronFoliageLODManager Function GenerateImpostor *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics
{
	struct AuracronFoliageLODManager_eventGenerateImpostor_Parms
	{
		UStaticMesh* SourceMesh;
		FAuracronImpostorData ImpostorSettings;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Impostor management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Impostor management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpostorSettings;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_SourceMesh = { "SourceMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGenerateImpostor_Parms, SourceMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ImpostorSettings = { "ImpostorSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGenerateImpostor_Parms, ImpostorSettings), Z_Construct_UScriptStruct_FAuracronImpostorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorSettings_MetaData), NewProp_ImpostorSettings_MetaData) }; // 1298454982
void Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventGenerateImpostor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventGenerateImpostor_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_SourceMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ImpostorSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GenerateImpostor", Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::AuracronFoliageLODManager_eventGenerateImpostor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::AuracronFoliageLODManager_eventGenerateImpostor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGenerateImpostor)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_SourceMesh);
	P_GET_STRUCT_REF(FAuracronImpostorData,Z_Param_Out_ImpostorSettings);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateImpostor(Z_Param_SourceMesh,Z_Param_Out_ImpostorSettings);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GenerateImpostor ***********************

// ********** Begin Class UAuracronFoliageLODManager Function GetAllBillboards *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics
{
	struct AuracronFoliageLODManager_eventGetAllBillboards_Parms
	{
		TArray<FAuracronBillboardData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronBillboardData, METADATA_PARAMS(0, nullptr) }; // 344031264
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetAllBillboards_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 344031264
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetAllBillboards", Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::AuracronFoliageLODManager_eventGetAllBillboards_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::AuracronFoliageLODManager_eventGetAllBillboards_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetAllBillboards)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronBillboardData>*)Z_Param__Result=P_THIS->GetAllBillboards();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetAllBillboards ***********************

// ********** Begin Class UAuracronFoliageLODManager Function GetAllImpostors **********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics
{
	struct AuracronFoliageLODManager_eventGetAllImpostors_Parms
	{
		TArray<FAuracronImpostorData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronImpostorData, METADATA_PARAMS(0, nullptr) }; // 1298454982
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetAllImpostors_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1298454982
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetAllImpostors", Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::AuracronFoliageLODManager_eventGetAllImpostors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::AuracronFoliageLODManager_eventGetAllImpostors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetAllImpostors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronImpostorData>*)Z_Param__Result=P_THIS->GetAllImpostors();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetAllImpostors ************************

// ********** Begin Class UAuracronFoliageLODManager Function GetAverageFrameTime ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics
{
	struct AuracronFoliageLODManager_eventGetAverageFrameTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetAverageFrameTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetAverageFrameTime", Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::AuracronFoliageLODManager_eventGetAverageFrameTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::AuracronFoliageLODManager_eventGetAverageFrameTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetAverageFrameTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAverageFrameTime();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetAverageFrameTime ********************

// ********** Begin Class UAuracronFoliageLODManager Function GetBillboard *************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics
{
	struct AuracronFoliageLODManager_eventGetBillboard_Parms
	{
		FString BillboardId;
		FAuracronBillboardData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BillboardId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::NewProp_BillboardId = { "BillboardId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetBillboard_Parms, BillboardId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardId_MetaData), NewProp_BillboardId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetBillboard_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronBillboardData, METADATA_PARAMS(0, nullptr) }; // 344031264
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::NewProp_BillboardId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetBillboard", Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::AuracronFoliageLODManager_eventGetBillboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::AuracronFoliageLODManager_eventGetBillboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetBillboard)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BillboardId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronBillboardData*)Z_Param__Result=P_THIS->GetBillboard(Z_Param_BillboardId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetBillboard ***************************

// ********** Begin Class UAuracronFoliageLODManager Function GetBiomeLODSettings ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics
{
	struct AuracronFoliageLODManager_eventGetBiomeLODSettings_Parms
	{
		FString BiomeId;
		FAuracronFoliageLODConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetBiomeLODSettings_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetBiomeLODSettings_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(0, nullptr) }; // 3403220216
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetBiomeLODSettings", Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::AuracronFoliageLODManager_eventGetBiomeLODSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::AuracronFoliageLODManager_eventGetBiomeLODSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetBiomeLODSettings)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageLODConfiguration*)Z_Param__Result=P_THIS->GetBiomeLODSettings(Z_Param_BiomeId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetBiomeLODSettings ********************

// ********** Begin Class UAuracronFoliageLODManager Function GetConfiguration *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics
{
	struct AuracronFoliageLODManager_eventGetConfiguration_Parms
	{
		FAuracronFoliageLODConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(0, nullptr) }; // 3403220216
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetConfiguration", Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::AuracronFoliageLODManager_eventGetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::AuracronFoliageLODManager_eventGetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronFoliageLODConfiguration*)Z_Param__Result=P_THIS->GetConfiguration();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetConfiguration ***********************

// ********** Begin Class UAuracronFoliageLODManager Function GetCullDistances *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics
{
	struct AuracronFoliageLODManager_eventGetCullDistances_Parms
	{
		float StartDistance;
		float EndDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EndDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::NewProp_StartDistance = { "StartDistance", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetCullDistances_Parms, StartDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::NewProp_EndDistance = { "EndDistance", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetCullDistances_Parms, EndDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::NewProp_StartDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::NewProp_EndDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetCullDistances", Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::AuracronFoliageLODManager_eventGetCullDistances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::AuracronFoliageLODManager_eventGetCullDistances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetCullDistances)
{
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_StartDistance);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_EndDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetCullDistances(Z_Param_Out_StartDistance,Z_Param_Out_EndDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetCullDistances ***********************

// ********** Begin Class UAuracronFoliageLODManager Function GetImpostor **************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics
{
	struct AuracronFoliageLODManager_eventGetImpostor_Parms
	{
		FString ImpostorId;
		FAuracronImpostorData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImpostorId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::NewProp_ImpostorId = { "ImpostorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetImpostor_Parms, ImpostorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorId_MetaData), NewProp_ImpostorId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetImpostor_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronImpostorData, METADATA_PARAMS(0, nullptr) }; // 1298454982
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::NewProp_ImpostorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetImpostor", Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::AuracronFoliageLODManager_eventGetImpostor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::AuracronFoliageLODManager_eventGetImpostor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetImpostor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ImpostorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronImpostorData*)Z_Param__Result=P_THIS->GetImpostor(Z_Param_ImpostorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetImpostor ****************************

// ********** Begin Class UAuracronFoliageLODManager Function GetInstance **************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics
{
	struct AuracronFoliageLODManager_eventGetInstance_Parms
	{
		UAuracronFoliageLODManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Singleton access\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Singleton access" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetInstance_Parms, ReturnValue), Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetInstance", Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::AuracronFoliageLODManager_eventGetInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::AuracronFoliageLODManager_eventGetInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetInstance)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAuracronFoliageLODManager**)Z_Param__Result=UAuracronFoliageLODManager::GetInstance();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetInstance ****************************

// ********** Begin Class UAuracronFoliageLODManager Function GetInstancesInRadius *****************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics
{
	struct AuracronFoliageLODManager_eventGetInstancesInRadius_Parms
	{
		FVector Location;
		float Radius;
		TArray<FAuracronLODInstanceData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetInstancesInRadius_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetInstancesInRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLODInstanceData, METADATA_PARAMS(0, nullptr) }; // 1651973650
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetInstancesInRadius_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1651973650
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetInstancesInRadius", Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::AuracronFoliageLODManager_eventGetInstancesInRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::AuracronFoliageLODManager_eventGetInstancesInRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetInstancesInRadius)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAuracronLODInstanceData>*)Z_Param__Result=P_THIS->GetInstancesInRadius(Z_Param_Out_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetInstancesInRadius *******************

// ********** Begin Class UAuracronFoliageLODManager Function GetLODDistances **********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics
{
	struct AuracronFoliageLODManager_eventGetLODDistances_Parms
	{
		TArray<float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetLODDistances_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetLODDistances", Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::AuracronFoliageLODManager_eventGetLODDistances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::AuracronFoliageLODManager_eventGetLODDistances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetLODDistances)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<float>*)Z_Param__Result=P_THIS->GetLODDistances();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetLODDistances ************************

// ********** Begin Class UAuracronFoliageLODManager Function GetLODDistribution *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics
{
	struct AuracronFoliageLODManager_eventGetLODDistribution_Parms
	{
		TMap<int32,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetLODDistribution_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetLODDistribution", Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::AuracronFoliageLODManager_eventGetLODDistribution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::AuracronFoliageLODManager_eventGetLODDistribution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetLODDistribution)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<int32,int32>*)Z_Param__Result=P_THIS->GetLODDistribution();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetLODDistribution *********************

// ********** Begin Class UAuracronFoliageLODManager Function GetLODInstance ***********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics
{
	struct AuracronFoliageLODManager_eventGetLODInstance_Parms
	{
		FString InstanceId;
		FAuracronLODInstanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetLODInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetLODInstance_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLODInstanceData, METADATA_PARAMS(0, nullptr) }; // 1651973650
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetLODInstance", Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::AuracronFoliageLODManager_eventGetLODInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::AuracronFoliageLODManager_eventGetLODInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetLODInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLODInstanceData*)Z_Param__Result=P_THIS->GetLODInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetLODInstance *************************

// ********** Begin Class UAuracronFoliageLODManager Function GetOrCreateHISMComponent *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics
{
	struct AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms
	{
		UStaticMesh* Mesh;
		int32 LODLevel;
		UHierarchicalInstancedStaticMeshComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms, Mesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms, ReturnValue), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_LODLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetOrCreateHISMComponent", Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::AuracronFoliageLODManager_eventGetOrCreateHISMComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetOrCreateHISMComponent)
{
	P_GET_OBJECT(UStaticMesh,Z_Param_Mesh);
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UHierarchicalInstancedStaticMeshComponent**)Z_Param__Result=P_THIS->GetOrCreateHISMComponent(Z_Param_Mesh,Z_Param_LODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetOrCreateHISMComponent ***************

// ********** Begin Class UAuracronFoliageLODManager Function GetPerformanceData *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics
{
	struct AuracronFoliageLODManager_eventGetPerformanceData_Parms
	{
		FAuracronLODPerformanceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Performance monitoring\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance monitoring" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetPerformanceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FAuracronLODPerformanceData, METADATA_PARAMS(0, nullptr) }; // 2784752201
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetPerformanceData", Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::AuracronFoliageLODManager_eventGetPerformanceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::AuracronFoliageLODManager_eventGetPerformanceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetPerformanceData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAuracronLODPerformanceData*)Z_Param__Result=P_THIS->GetPerformanceData();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetPerformanceData *********************

// ********** Begin Class UAuracronFoliageLODManager Function GetQualityLevel **********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics
{
	struct AuracronFoliageLODManager_eventGetQualityLevel_Parms
	{
		EAuracronLODQualityLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetQualityLevel_Parms, ReturnValue), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel, METADATA_PARAMS(0, nullptr) }; // 683004800
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetQualityLevel", Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::AuracronFoliageLODManager_eventGetQualityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::AuracronFoliageLODManager_eventGetQualityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetQualityLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAuracronLODQualityLevel*)Z_Param__Result=P_THIS->GetQualityLevel();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetQualityLevel ************************

// ********** Begin Class UAuracronFoliageLODManager Function GetTotalInstanceCount ****************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics
{
	struct AuracronFoliageLODManager_eventGetTotalInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetTotalInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetTotalInstanceCount", Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::AuracronFoliageLODManager_eventGetTotalInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::AuracronFoliageLODManager_eventGetTotalInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetTotalInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetTotalInstanceCount ******************

// ********** Begin Class UAuracronFoliageLODManager Function GetVisibleInstanceCount **************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics
{
	struct AuracronFoliageLODManager_eventGetVisibleInstanceCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventGetVisibleInstanceCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "GetVisibleInstanceCount", Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::AuracronFoliageLODManager_eventGetVisibleInstanceCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::AuracronFoliageLODManager_eventGetVisibleInstanceCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execGetVisibleInstanceCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetVisibleInstanceCount();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function GetVisibleInstanceCount ****************

// ********** Begin Class UAuracronFoliageLODManager Function Initialize ***************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics
{
	struct AuracronFoliageLODManager_eventInitialize_Parms
	{
		FAuracronFoliageLODConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Manager lifecycle\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manager lifecycle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventInitialize_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3403220216
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "Initialize", Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::AuracronFoliageLODManager_eventInitialize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::AuracronFoliageLODManager_eventInitialize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execInitialize)
{
	P_GET_STRUCT_REF(FAuracronFoliageLODConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Initialize(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function Initialize *****************************

// ********** Begin Class UAuracronFoliageLODManager Function IsDebugVisualizationEnabled **********
struct Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics
{
	struct AuracronFoliageLODManager_eventIsDebugVisualizationEnabled_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventIsDebugVisualizationEnabled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventIsDebugVisualizationEnabled_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "IsDebugVisualizationEnabled", Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageLODManager_eventIsDebugVisualizationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::AuracronFoliageLODManager_eventIsDebugVisualizationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execIsDebugVisualizationEnabled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDebugVisualizationEnabled();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function IsDebugVisualizationEnabled ************

// ********** Begin Class UAuracronFoliageLODManager Function IsInitialized ************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics
{
	struct AuracronFoliageLODManager_eventIsInitialized_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventIsInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventIsInitialized_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "IsInitialized", Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::AuracronFoliageLODManager_eventIsInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::AuracronFoliageLODManager_eventIsInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execIsInitialized)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInitialized();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function IsInitialized **************************

// ********** Begin Class UAuracronFoliageLODManager Function IsInstanceTransitioning **************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics
{
	struct AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms
	{
		FString InstanceId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "IsInstanceTransitioning", Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::AuracronFoliageLODManager_eventIsInstanceTransitioning_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execIsInstanceTransitioning)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInstanceTransitioning(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function IsInstanceTransitioning ****************

// ********** Begin Class UAuracronFoliageLODManager Function LogPerformanceStatistics *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "LogPerformanceStatistics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execLogPerformanceStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogPerformanceStatistics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function LogPerformanceStatistics ***************

// ********** Begin Class UAuracronFoliageLODManager Function OptimizeHISMComponents ***************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// HISM optimization\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HISM optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "OptimizeHISMComponents", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execOptimizeHISMComponents)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeHISMComponents();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function OptimizeHISMComponents *****************

// ********** Begin Class UAuracronFoliageLODManager Function RegisterBillboard ********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics
{
	struct AuracronFoliageLODManager_eventRegisterBillboard_Parms
	{
		FAuracronBillboardData BillboardData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_BillboardData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_BillboardData = { "BillboardData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventRegisterBillboard_Parms, BillboardData), Z_Construct_UScriptStruct_FAuracronBillboardData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardData_MetaData), NewProp_BillboardData_MetaData) }; // 344031264
void Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventRegisterBillboard_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventRegisterBillboard_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_BillboardData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "RegisterBillboard", Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::AuracronFoliageLODManager_eventRegisterBillboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::AuracronFoliageLODManager_eventRegisterBillboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execRegisterBillboard)
{
	P_GET_STRUCT_REF(FAuracronBillboardData,Z_Param_Out_BillboardData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterBillboard(Z_Param_Out_BillboardData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function RegisterBillboard **********************

// ********** Begin Class UAuracronFoliageLODManager Function RegisterImpostor *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics
{
	struct AuracronFoliageLODManager_eventRegisterImpostor_Parms
	{
		FAuracronImpostorData ImpostorData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ImpostorData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ImpostorData = { "ImpostorData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventRegisterImpostor_Parms, ImpostorData), Z_Construct_UScriptStruct_FAuracronImpostorData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorData_MetaData), NewProp_ImpostorData_MetaData) }; // 1298454982
void Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventRegisterImpostor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventRegisterImpostor_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ImpostorData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "RegisterImpostor", Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::AuracronFoliageLODManager_eventRegisterImpostor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::AuracronFoliageLODManager_eventRegisterImpostor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execRegisterImpostor)
{
	P_GET_STRUCT_REF(FAuracronImpostorData,Z_Param_Out_ImpostorData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterImpostor(Z_Param_Out_ImpostorData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function RegisterImpostor ***********************

// ********** Begin Class UAuracronFoliageLODManager Function RegisterInstance *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics
{
	struct AuracronFoliageLODManager_eventRegisterInstance_Parms
	{
		FAuracronLODInstanceData InstanceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Instance management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Instance management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_InstanceData = { "InstanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventRegisterInstance_Parms, InstanceData), Z_Construct_UScriptStruct_FAuracronLODInstanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceData_MetaData), NewProp_InstanceData_MetaData) }; // 1651973650
void Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventRegisterInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventRegisterInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_InstanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "RegisterInstance", Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::AuracronFoliageLODManager_eventRegisterInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::AuracronFoliageLODManager_eventRegisterInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execRegisterInstance)
{
	P_GET_STRUCT_REF(FAuracronLODInstanceData,Z_Param_Out_InstanceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RegisterInstance(Z_Param_Out_InstanceData);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function RegisterInstance ***********************

// ********** Begin Class UAuracronFoliageLODManager Function SetBiomeLODSettings ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics
{
	struct AuracronFoliageLODManager_eventSetBiomeLODSettings_Parms
	{
		FString BiomeId;
		FAuracronFoliageLODConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome integration\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BiomeId_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BiomeId;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::NewProp_BiomeId = { "BiomeId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetBiomeLODSettings_Parms, BiomeId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BiomeId_MetaData), NewProp_BiomeId_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetBiomeLODSettings_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3403220216
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::NewProp_BiomeId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetBiomeLODSettings", Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::AuracronFoliageLODManager_eventSetBiomeLODSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::AuracronFoliageLODManager_eventSetBiomeLODSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetBiomeLODSettings)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BiomeId);
	P_GET_STRUCT_REF(FAuracronFoliageLODConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBiomeLODSettings(Z_Param_BiomeId,Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetBiomeLODSettings ********************

// ********** Begin Class UAuracronFoliageLODManager Function SetConfiguration *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics
{
	struct AuracronFoliageLODManager_eventSetConfiguration_Parms
	{
		FAuracronFoliageLODConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configuration management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetConfiguration_Parms, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3403220216
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetConfiguration", Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::AuracronFoliageLODManager_eventSetConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::AuracronFoliageLODManager_eventSetConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetConfiguration)
{
	P_GET_STRUCT_REF(FAuracronFoliageLODConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetConfiguration(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetConfiguration ***********************

// ********** Begin Class UAuracronFoliageLODManager Function SetCullDistances *********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics
{
	struct AuracronFoliageLODManager_eventSetCullDistances_Parms
	{
		float StartDistance;
		float EndDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EndDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::NewProp_StartDistance = { "StartDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetCullDistances_Parms, StartDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::NewProp_EndDistance = { "EndDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetCullDistances_Parms, EndDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::NewProp_StartDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::NewProp_EndDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetCullDistances", Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::AuracronFoliageLODManager_eventSetCullDistances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::AuracronFoliageLODManager_eventSetCullDistances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetCullDistances)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_StartDistance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EndDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCullDistances(Z_Param_StartDistance,Z_Param_EndDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetCullDistances ***********************

// ********** Begin Class UAuracronFoliageLODManager Function SetHISMCullingDistance ***************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics
{
	struct AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms
	{
		UHierarchicalInstancedStaticMeshComponent* Component;
		float StartDistance;
		float EndDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EndDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms, Component), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_StartDistance = { "StartDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms, StartDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_EndDistance = { "EndDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms, EndDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_StartDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::NewProp_EndDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetHISMCullingDistance", Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::AuracronFoliageLODManager_eventSetHISMCullingDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetHISMCullingDistance)
{
	P_GET_OBJECT(UHierarchicalInstancedStaticMeshComponent,Z_Param_Component);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StartDistance);
	P_GET_PROPERTY(FFloatProperty,Z_Param_EndDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetHISMCullingDistance(Z_Param_Component,Z_Param_StartDistance,Z_Param_EndDistance);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetHISMCullingDistance *****************

// ********** Begin Class UAuracronFoliageLODManager Function SetLODDistances **********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics
{
	struct AuracronFoliageLODManager_eventSetLODDistances_Parms
	{
		float LOD0;
		float LOD1;
		float LOD2;
		float LOD3;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD0;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD1;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD2;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LOD3;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD0 = { "LOD0", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetLODDistances_Parms, LOD0), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD1 = { "LOD1", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetLODDistances_Parms, LOD1), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD2 = { "LOD2", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetLODDistances_Parms, LOD2), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD3 = { "LOD3", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetLODDistances_Parms, LOD3), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD0,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD1,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD2,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::NewProp_LOD3,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetLODDistances", Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::AuracronFoliageLODManager_eventSetLODDistances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::AuracronFoliageLODManager_eventSetLODDistances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetLODDistances)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD0);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD1);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD2);
	P_GET_PROPERTY(FFloatProperty,Z_Param_LOD3);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLODDistances(Z_Param_LOD0,Z_Param_LOD1,Z_Param_LOD2,Z_Param_LOD3);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetLODDistances ************************

// ********** Begin Class UAuracronFoliageLODManager Function SetQualityLevel **********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics
{
	struct AuracronFoliageLODManager_eventSetQualityLevel_Parms
	{
		EAuracronLODQualityLevel QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_QualityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::NewProp_QualityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventSetQualityLevel_Parms, QualityLevel), Z_Construct_UEnum_AuracronFoliageBridge_EAuracronLODQualityLevel, METADATA_PARAMS(0, nullptr) }; // 683004800
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::NewProp_QualityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "SetQualityLevel", Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::AuracronFoliageLODManager_eventSetQualityLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::AuracronFoliageLODManager_eventSetQualityLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execSetQualityLevel)
{
	P_GET_ENUM(EAuracronLODQualityLevel,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetQualityLevel(EAuracronLODQualityLevel(Z_Param_QualityLevel));
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function SetQualityLevel ************************

// ********** Begin Class UAuracronFoliageLODManager Function ShouldCullInstance *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics
{
	struct AuracronFoliageLODManager_eventShouldCullInstance_Parms
	{
		FAuracronLODInstanceData InstanceData;
		FVector CameraLocation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceData_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InstanceData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_InstanceData = { "InstanceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventShouldCullInstance_Parms, InstanceData), Z_Construct_UScriptStruct_FAuracronLODInstanceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceData_MetaData), NewProp_InstanceData_MetaData) }; // 1651973650
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_CameraLocation = { "CameraLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventShouldCullInstance_Parms, CameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLocation_MetaData), NewProp_CameraLocation_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventShouldCullInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventShouldCullInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_InstanceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_CameraLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "ShouldCullInstance", Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::AuracronFoliageLODManager_eventShouldCullInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::AuracronFoliageLODManager_eventShouldCullInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execShouldCullInstance)
{
	P_GET_STRUCT_REF(FAuracronLODInstanceData,Z_Param_Out_InstanceData);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ShouldCullInstance(Z_Param_Out_InstanceData,Z_Param_Out_CameraLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function ShouldCullInstance *********************

// ********** Begin Class UAuracronFoliageLODManager Function Shutdown *****************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "Shutdown", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execShutdown)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Shutdown();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function Shutdown *******************************

// ********** Begin Class UAuracronFoliageLODManager Function StartLODTransition *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics
{
	struct AuracronFoliageLODManager_eventStartLODTransition_Parms
	{
		FString InstanceId;
		int32 FromLOD;
		int32 ToLOD;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Transition management\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_FromLOD;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ToLOD;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventStartLODTransition_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_FromLOD = { "FromLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventStartLODTransition_Parms, FromLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_ToLOD = { "ToLOD", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventStartLODTransition_Parms, ToLOD), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_FromLOD,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::NewProp_ToLOD,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "StartLODTransition", Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::AuracronFoliageLODManager_eventStartLODTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::AuracronFoliageLODManager_eventStartLODTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execStartLODTransition)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_PROPERTY(FIntProperty,Z_Param_FromLOD);
	P_GET_PROPERTY(FIntProperty,Z_Param_ToLOD);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartLODTransition(Z_Param_InstanceId,Z_Param_FromLOD,Z_Param_ToLOD);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function StartLODTransition *********************

// ********** Begin Class UAuracronFoliageLODManager Function Tick *********************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics
{
	struct AuracronFoliageLODManager_eventTick_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventTick_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "Tick", Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::AuracronFoliageLODManager_eventTick_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::AuracronFoliageLODManager_eventTick_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_Tick()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_Tick_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execTick)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Tick(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function Tick ***********************************

// ********** Begin Class UAuracronFoliageLODManager Function UnregisterBillboard ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics
{
	struct AuracronFoliageLODManager_eventUnregisterBillboard_Parms
	{
		FString BillboardId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BillboardId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BillboardId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_BillboardId = { "BillboardId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUnregisterBillboard_Parms, BillboardId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BillboardId_MetaData), NewProp_BillboardId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventUnregisterBillboard_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventUnregisterBillboard_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_BillboardId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UnregisterBillboard", Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::AuracronFoliageLODManager_eventUnregisterBillboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::AuracronFoliageLODManager_eventUnregisterBillboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUnregisterBillboard)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BillboardId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterBillboard(Z_Param_BillboardId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UnregisterBillboard ********************

// ********** Begin Class UAuracronFoliageLODManager Function UnregisterImpostor *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics
{
	struct AuracronFoliageLODManager_eventUnregisterImpostor_Parms
	{
		FString ImpostorId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpostorId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ImpostorId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ImpostorId = { "ImpostorId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUnregisterImpostor_Parms, ImpostorId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpostorId_MetaData), NewProp_ImpostorId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventUnregisterImpostor_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventUnregisterImpostor_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ImpostorId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UnregisterImpostor", Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::AuracronFoliageLODManager_eventUnregisterImpostor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::AuracronFoliageLODManager_eventUnregisterImpostor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUnregisterImpostor)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ImpostorId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterImpostor(Z_Param_ImpostorId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UnregisterImpostor *********************

// ********** Begin Class UAuracronFoliageLODManager Function UnregisterInstance *******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics
{
	struct AuracronFoliageLODManager_eventUnregisterInstance_Parms
	{
		FString InstanceId;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUnregisterInstance_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventUnregisterInstance_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventUnregisterInstance_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UnregisterInstance", Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::AuracronFoliageLODManager_eventUnregisterInstance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::AuracronFoliageLODManager_eventUnregisterInstance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUnregisterInstance)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnregisterInstance(Z_Param_InstanceId);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UnregisterInstance *********************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateCulling ************************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics
{
	struct AuracronFoliageLODManager_eventUpdateCulling_Parms
	{
		FVector CameraLocation;
		FVector CameraDirection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Culling system\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Culling system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraDirection_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraDirection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::NewProp_CameraLocation = { "CameraLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateCulling_Parms, CameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLocation_MetaData), NewProp_CameraLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::NewProp_CameraDirection = { "CameraDirection", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateCulling_Parms, CameraDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraDirection_MetaData), NewProp_CameraDirection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::NewProp_CameraLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::NewProp_CameraDirection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateCulling", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::AuracronFoliageLODManager_eventUpdateCulling_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::AuracronFoliageLODManager_eventUpdateCulling_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateCulling)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraDirection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateCulling(Z_Param_Out_CameraLocation,Z_Param_Out_CameraDirection);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateCulling **************************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateDistanceBasedLOD ***************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics
{
	struct AuracronFoliageLODManager_eventUpdateDistanceBasedLOD_Parms
	{
		FVector CameraLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Distance-based LOD\n" },
#endif
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Distance-based LOD" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CameraLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::NewProp_CameraLocation = { "CameraLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateDistanceBasedLOD_Parms, CameraLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLocation_MetaData), NewProp_CameraLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::NewProp_CameraLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateDistanceBasedLOD", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::AuracronFoliageLODManager_eventUpdateDistanceBasedLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::AuracronFoliageLODManager_eventUpdateDistanceBasedLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateDistanceBasedLOD)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CameraLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDistanceBasedLOD(Z_Param_Out_CameraLocation);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateDistanceBasedLOD *****************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateHISMInstances ******************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics
{
	struct AuracronFoliageLODManager_eventUpdateHISMInstances_Parms
	{
		UHierarchicalInstancedStaticMeshComponent* Component;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateHISMInstances_Parms, Component), Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::NewProp_Component,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateHISMInstances", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::AuracronFoliageLODManager_eventUpdateHISMInstances_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::AuracronFoliageLODManager_eventUpdateHISMInstances_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateHISMInstances)
{
	P_GET_OBJECT(UHierarchicalInstancedStaticMeshComponent,Z_Param_Component);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateHISMInstances(Z_Param_Component);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateHISMInstances ********************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateInstanceLOD ********************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics
{
	struct AuracronFoliageLODManager_eventUpdateInstanceLOD_Parms
	{
		FString InstanceId;
		int32 NewLODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateInstanceLOD_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::NewProp_NewLODLevel = { "NewLODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateInstanceLOD_Parms, NewLODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::NewProp_NewLODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateInstanceLOD", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::AuracronFoliageLODManager_eventUpdateInstanceLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::AuracronFoliageLODManager_eventUpdateInstanceLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateInstanceLOD)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_PROPERTY(FIntProperty,Z_Param_NewLODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateInstanceLOD(Z_Param_InstanceId,Z_Param_NewLODLevel);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateInstanceLOD **********************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateInstanceVisibility *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics
{
	struct AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms
	{
		FString InstanceId;
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InstanceId_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InstanceId;
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_InstanceId = { "InstanceId", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms, InstanceId), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InstanceId_MetaData), NewProp_InstanceId_MetaData) };
void Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms), &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_InstanceId,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateInstanceVisibility", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::AuracronFoliageLODManager_eventUpdateInstanceVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateInstanceVisibility)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InstanceId);
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateInstanceVisibility(Z_Param_InstanceId,Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateInstanceVisibility ***************

// ********** Begin Class UAuracronFoliageLODManager Function UpdateLODTransitions *****************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics
{
	struct AuracronFoliageLODManager_eventUpdateLODTransitions_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AuracronFoliageLODManager_eventUpdateLODTransitions_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdateLODTransitions", Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::AuracronFoliageLODManager_eventUpdateLODTransitions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::AuracronFoliageLODManager_eventUpdateLODTransitions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdateLODTransitions)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateLODTransitions(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdateLODTransitions *******************

// ********** Begin Class UAuracronFoliageLODManager Function UpdatePerformanceMetrics *************
struct Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "LOD Manager" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAuracronFoliageLODManager, nullptr, "UpdatePerformanceMetrics", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAuracronFoliageLODManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// ********** End Class UAuracronFoliageLODManager Function UpdatePerformanceMetrics ***************

// ********** Begin Class UAuracronFoliageLODManager ***********************************************
void UAuracronFoliageLODManager::StaticRegisterNativesUAuracronFoliageLODManager()
{
	UClass* Class = UAuracronFoliageLODManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyBiomeLODToInstances", &UAuracronFoliageLODManager::execApplyBiomeLODToInstances },
		{ "BatchGenerateImpostors", &UAuracronFoliageLODManager::execBatchGenerateImpostors },
		{ "CalculateFadeAmount", &UAuracronFoliageLODManager::execCalculateFadeAmount },
		{ "CalculateLODLevel", &UAuracronFoliageLODManager::execCalculateLODLevel },
		{ "DrawDebugLODInfo", &UAuracronFoliageLODManager::execDrawDebugLODInfo },
		{ "EnableDebugVisualization", &UAuracronFoliageLODManager::execEnableDebugVisualization },
		{ "GenerateBillboard", &UAuracronFoliageLODManager::execGenerateBillboard },
		{ "GenerateImpostor", &UAuracronFoliageLODManager::execGenerateImpostor },
		{ "GetAllBillboards", &UAuracronFoliageLODManager::execGetAllBillboards },
		{ "GetAllImpostors", &UAuracronFoliageLODManager::execGetAllImpostors },
		{ "GetAverageFrameTime", &UAuracronFoliageLODManager::execGetAverageFrameTime },
		{ "GetBillboard", &UAuracronFoliageLODManager::execGetBillboard },
		{ "GetBiomeLODSettings", &UAuracronFoliageLODManager::execGetBiomeLODSettings },
		{ "GetConfiguration", &UAuracronFoliageLODManager::execGetConfiguration },
		{ "GetCullDistances", &UAuracronFoliageLODManager::execGetCullDistances },
		{ "GetImpostor", &UAuracronFoliageLODManager::execGetImpostor },
		{ "GetInstance", &UAuracronFoliageLODManager::execGetInstance },
		{ "GetInstancesInRadius", &UAuracronFoliageLODManager::execGetInstancesInRadius },
		{ "GetLODDistances", &UAuracronFoliageLODManager::execGetLODDistances },
		{ "GetLODDistribution", &UAuracronFoliageLODManager::execGetLODDistribution },
		{ "GetLODInstance", &UAuracronFoliageLODManager::execGetLODInstance },
		{ "GetOrCreateHISMComponent", &UAuracronFoliageLODManager::execGetOrCreateHISMComponent },
		{ "GetPerformanceData", &UAuracronFoliageLODManager::execGetPerformanceData },
		{ "GetQualityLevel", &UAuracronFoliageLODManager::execGetQualityLevel },
		{ "GetTotalInstanceCount", &UAuracronFoliageLODManager::execGetTotalInstanceCount },
		{ "GetVisibleInstanceCount", &UAuracronFoliageLODManager::execGetVisibleInstanceCount },
		{ "Initialize", &UAuracronFoliageLODManager::execInitialize },
		{ "IsDebugVisualizationEnabled", &UAuracronFoliageLODManager::execIsDebugVisualizationEnabled },
		{ "IsInitialized", &UAuracronFoliageLODManager::execIsInitialized },
		{ "IsInstanceTransitioning", &UAuracronFoliageLODManager::execIsInstanceTransitioning },
		{ "LogPerformanceStatistics", &UAuracronFoliageLODManager::execLogPerformanceStatistics },
		{ "OptimizeHISMComponents", &UAuracronFoliageLODManager::execOptimizeHISMComponents },
		{ "RegisterBillboard", &UAuracronFoliageLODManager::execRegisterBillboard },
		{ "RegisterImpostor", &UAuracronFoliageLODManager::execRegisterImpostor },
		{ "RegisterInstance", &UAuracronFoliageLODManager::execRegisterInstance },
		{ "SetBiomeLODSettings", &UAuracronFoliageLODManager::execSetBiomeLODSettings },
		{ "SetConfiguration", &UAuracronFoliageLODManager::execSetConfiguration },
		{ "SetCullDistances", &UAuracronFoliageLODManager::execSetCullDistances },
		{ "SetHISMCullingDistance", &UAuracronFoliageLODManager::execSetHISMCullingDistance },
		{ "SetLODDistances", &UAuracronFoliageLODManager::execSetLODDistances },
		{ "SetQualityLevel", &UAuracronFoliageLODManager::execSetQualityLevel },
		{ "ShouldCullInstance", &UAuracronFoliageLODManager::execShouldCullInstance },
		{ "Shutdown", &UAuracronFoliageLODManager::execShutdown },
		{ "StartLODTransition", &UAuracronFoliageLODManager::execStartLODTransition },
		{ "Tick", &UAuracronFoliageLODManager::execTick },
		{ "UnregisterBillboard", &UAuracronFoliageLODManager::execUnregisterBillboard },
		{ "UnregisterImpostor", &UAuracronFoliageLODManager::execUnregisterImpostor },
		{ "UnregisterInstance", &UAuracronFoliageLODManager::execUnregisterInstance },
		{ "UpdateCulling", &UAuracronFoliageLODManager::execUpdateCulling },
		{ "UpdateDistanceBasedLOD", &UAuracronFoliageLODManager::execUpdateDistanceBasedLOD },
		{ "UpdateHISMInstances", &UAuracronFoliageLODManager::execUpdateHISMInstances },
		{ "UpdateInstanceLOD", &UAuracronFoliageLODManager::execUpdateInstanceLOD },
		{ "UpdateInstanceVisibility", &UAuracronFoliageLODManager::execUpdateInstanceVisibility },
		{ "UpdateLODTransitions", &UAuracronFoliageLODManager::execUpdateLODTransitions },
		{ "UpdatePerformanceMetrics", &UAuracronFoliageLODManager::execUpdatePerformanceMetrics },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAuracronFoliageLODManager;
UClass* UAuracronFoliageLODManager::GetPrivateStaticClass()
{
	using TClass = UAuracronFoliageLODManager;
	if (!Z_Registration_Info_UClass_UAuracronFoliageLODManager.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AuracronFoliageLODManager"),
			Z_Registration_Info_UClass_UAuracronFoliageLODManager.InnerSingleton,
			StaticRegisterNativesUAuracronFoliageLODManager,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageLODManager.InnerSingleton;
}
UClass* Z_Construct_UClass_UAuracronFoliageLODManager_NoRegister()
{
	return UAuracronFoliageLODManager::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAuracronFoliageLODManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Foliage LOD Manager\n * Manager for the foliage LOD system including distance-based LOD, impostors, and billboards\n */" },
#endif
		{ "IncludePath", "AuracronFoliageLOD.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Foliage LOD Manager\nManager for the foliage LOD system including distance-based LOD, impostors, and billboards" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLODChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInstanceCulled_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnImpostorGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBillboardGenerated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInitialized_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ManagedWorld_MetaData[] = {
		{ "ModuleRelativePath", "Public/AuracronFoliageLOD.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLODChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInstanceCulled;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnImpostorGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBillboardGenerated;
	static void NewProp_bIsInitialized_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInitialized;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_ManagedWorld;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_ApplyBiomeLODToInstances, "ApplyBiomeLODToInstances" }, // 3669789138
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_BatchGenerateImpostors, "BatchGenerateImpostors" }, // 4170624958
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateFadeAmount, "CalculateFadeAmount" }, // 587439527
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_CalculateLODLevel, "CalculateLODLevel" }, // 124427576
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_DrawDebugLODInfo, "DrawDebugLODInfo" }, // 1142721639
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_EnableDebugVisualization, "EnableDebugVisualization" }, // 185025216
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateBillboard, "GenerateBillboard" }, // 1607821334
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GenerateImpostor, "GenerateImpostor" }, // 2834276608
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllBillboards, "GetAllBillboards" }, // 3914914565
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetAllImpostors, "GetAllImpostors" }, // 2186527449
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetAverageFrameTime, "GetAverageFrameTime" }, // 211651554
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetBillboard, "GetBillboard" }, // 1205773347
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetBiomeLODSettings, "GetBiomeLODSettings" }, // 1317144223
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetConfiguration, "GetConfiguration" }, // 12979903
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetCullDistances, "GetCullDistances" }, // 3282008319
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetImpostor, "GetImpostor" }, // 487694392
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstance, "GetInstance" }, // 3147915718
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetInstancesInRadius, "GetInstancesInRadius" }, // 79137169
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistances, "GetLODDistances" }, // 3889670204
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODDistribution, "GetLODDistribution" }, // 3988339304
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetLODInstance, "GetLODInstance" }, // 1197929971
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetOrCreateHISMComponent, "GetOrCreateHISMComponent" }, // 2186976655
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetPerformanceData, "GetPerformanceData" }, // 2167087180
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetQualityLevel, "GetQualityLevel" }, // 1723029097
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetTotalInstanceCount, "GetTotalInstanceCount" }, // 3476062752
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_GetVisibleInstanceCount, "GetVisibleInstanceCount" }, // 386140447
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_Initialize, "Initialize" }, // 3579370396
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_IsDebugVisualizationEnabled, "IsDebugVisualizationEnabled" }, // 3728260149
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_IsInitialized, "IsInitialized" }, // 2600134991
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_IsInstanceTransitioning, "IsInstanceTransitioning" }, // 271822145
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_LogPerformanceStatistics, "LogPerformanceStatistics" }, // 3857337471
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature, "OnBillboardGenerated__DelegateSignature" }, // 318180809
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature, "OnImpostorGenerated__DelegateSignature" }, // 511245349
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature, "OnInstanceCulled__DelegateSignature" }, // 2054728570
		{ &Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature, "OnLODChanged__DelegateSignature" }, // 1405089673
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_OptimizeHISMComponents, "OptimizeHISMComponents" }, // 4206006390
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterBillboard, "RegisterBillboard" }, // 2779758910
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterImpostor, "RegisterImpostor" }, // 2454847539
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_RegisterInstance, "RegisterInstance" }, // 848490178
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetBiomeLODSettings, "SetBiomeLODSettings" }, // 910904188
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetConfiguration, "SetConfiguration" }, // 1468147328
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetCullDistances, "SetCullDistances" }, // 2180789982
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetHISMCullingDistance, "SetHISMCullingDistance" }, // 131466966
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetLODDistances, "SetLODDistances" }, // 231845479
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_SetQualityLevel, "SetQualityLevel" }, // 2678581017
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_ShouldCullInstance, "ShouldCullInstance" }, // 3851695361
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_Shutdown, "Shutdown" }, // 1805695823
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_StartLODTransition, "StartLODTransition" }, // 3983945280
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_Tick, "Tick" }, // 14648583
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterBillboard, "UnregisterBillboard" }, // 370289459
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterImpostor, "UnregisterImpostor" }, // 74746727
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UnregisterInstance, "UnregisterInstance" }, // 1408587837
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateCulling, "UpdateCulling" }, // 2808432775
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateDistanceBasedLOD, "UpdateDistanceBasedLOD" }, // 3047797666
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateHISMInstances, "UpdateHISMInstances" }, // 717754087
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceLOD, "UpdateInstanceLOD" }, // 1740435183
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateInstanceVisibility, "UpdateInstanceVisibility" }, // 265993391
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdateLODTransitions, "UpdateLODTransitions" }, // 2412313077
		{ &Z_Construct_UFunction_UAuracronFoliageLODManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1812419406
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAuracronFoliageLODManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnLODChanged = { "OnLODChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, OnLODChanged), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnLODChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLODChanged_MetaData), NewProp_OnLODChanged_MetaData) }; // 1405089673
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnInstanceCulled = { "OnInstanceCulled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, OnInstanceCulled), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnInstanceCulled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInstanceCulled_MetaData), NewProp_OnInstanceCulled_MetaData) }; // 2054728570
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnImpostorGenerated = { "OnImpostorGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, OnImpostorGenerated), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnImpostorGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnImpostorGenerated_MetaData), NewProp_OnImpostorGenerated_MetaData) }; // 511245349
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnBillboardGenerated = { "OnBillboardGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, OnBillboardGenerated), Z_Construct_UDelegateFunction_UAuracronFoliageLODManager_OnBillboardGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBillboardGenerated_MetaData), NewProp_OnBillboardGenerated_MetaData) }; // 318180809
void Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_bIsInitialized_SetBit(void* Obj)
{
	((UAuracronFoliageLODManager*)Obj)->bIsInitialized = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_bIsInitialized = { "bIsInitialized", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAuracronFoliageLODManager), &Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_bIsInitialized_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInitialized_MetaData), NewProp_bIsInitialized_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, Configuration), Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3403220216
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_ManagedWorld = { "ManagedWorld", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAuracronFoliageLODManager, ManagedWorld), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ManagedWorld_MetaData), NewProp_ManagedWorld_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAuracronFoliageLODManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnLODChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnInstanceCulled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnImpostorGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_OnBillboardGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_bIsInitialized,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_Configuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAuracronFoliageLODManager_Statics::NewProp_ManagedWorld,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageLODManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAuracronFoliageLODManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_AuracronFoliageBridge,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageLODManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAuracronFoliageLODManager_Statics::ClassParams = {
	&UAuracronFoliageLODManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAuracronFoliageLODManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageLODManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAuracronFoliageLODManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UAuracronFoliageLODManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAuracronFoliageLODManager()
{
	if (!Z_Registration_Info_UClass_UAuracronFoliageLODManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAuracronFoliageLODManager.OuterSingleton, Z_Construct_UClass_UAuracronFoliageLODManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAuracronFoliageLODManager.OuterSingleton;
}
UAuracronFoliageLODManager::UAuracronFoliageLODManager(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAuracronFoliageLODManager);
UAuracronFoliageLODManager::~UAuracronFoliageLODManager() {}
// ********** End Class UAuracronFoliageLODManager *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAuracronFoliageLODType_StaticEnum, TEXT("EAuracronFoliageLODType"), &Z_Registration_Info_UEnum_EAuracronFoliageLODType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1685479502U) },
		{ EAuracronImpostorType_StaticEnum, TEXT("EAuracronImpostorType"), &Z_Registration_Info_UEnum_EAuracronImpostorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2533920382U) },
		{ EAuracronLODTransitionType_StaticEnum, TEXT("EAuracronLODTransitionType"), &Z_Registration_Info_UEnum_EAuracronLODTransitionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1867776590U) },
		{ EAuracronCullingType_StaticEnum, TEXT("EAuracronCullingType"), &Z_Registration_Info_UEnum_EAuracronCullingType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3086061814U) },
		{ EAuracronLODQualityLevel_StaticEnum, TEXT("EAuracronLODQualityLevel"), &Z_Registration_Info_UEnum_EAuracronLODQualityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 683004800U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronFoliageLODConfiguration::StaticStruct, Z_Construct_UScriptStruct_FAuracronFoliageLODConfiguration_Statics::NewStructOps, TEXT("AuracronFoliageLODConfiguration"), &Z_Registration_Info_UScriptStruct_FAuracronFoliageLODConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronFoliageLODConfiguration), 3403220216U) },
		{ FAuracronImpostorData::StaticStruct, Z_Construct_UScriptStruct_FAuracronImpostorData_Statics::NewStructOps, TEXT("AuracronImpostorData"), &Z_Registration_Info_UScriptStruct_FAuracronImpostorData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronImpostorData), 1298454982U) },
		{ FAuracronBillboardData::StaticStruct, Z_Construct_UScriptStruct_FAuracronBillboardData_Statics::NewStructOps, TEXT("AuracronBillboardData"), &Z_Registration_Info_UScriptStruct_FAuracronBillboardData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronBillboardData), 344031264U) },
		{ FAuracronLODPerformanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronLODPerformanceData_Statics::NewStructOps, TEXT("AuracronLODPerformanceData"), &Z_Registration_Info_UScriptStruct_FAuracronLODPerformanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLODPerformanceData), 2784752201U) },
		{ FAuracronLODInstanceData::StaticStruct, Z_Construct_UScriptStruct_FAuracronLODInstanceData_Statics::NewStructOps, TEXT("AuracronLODInstanceData"), &Z_Registration_Info_UScriptStruct_FAuracronLODInstanceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLODInstanceData), 1651973650U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAuracronFoliageLODManager, UAuracronFoliageLODManager::StaticClass, TEXT("UAuracronFoliageLODManager"), &Z_Registration_Info_UClass_UAuracronFoliageLODManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAuracronFoliageLODManager), 3693133977U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_1813426843(TEXT("/Script/AuracronFoliageBridge"),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Aura_projeto_Auracron_Source_AuracronFoliageBridge_Public_AuracronFoliageLOD_h__Script_AuracronFoliageBridge_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
